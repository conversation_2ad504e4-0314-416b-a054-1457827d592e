/*
 * Powered By fast.up
 */
package com.fast.controller.subscribe;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.subscribe.FastMemberSubscribePO;
import com.fast.service.subscribe.FastMemberSubscribeService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberSubscribe")
public class FastMemberSubscribeController extends BaseController {

    @Autowired
    private FastMemberSubscribeService fastMemberSubscribeService;

    @ApiName(value = "订阅消息-查询列表", folder = {"订阅消息"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberSubscribePO params, PageVO pageVO) {
        return fastMemberSubscribeService.queryPageList(params, pageVO);
    }

    @ApiName(value = "订阅消息-查询单个详情", folder = {"订阅消息"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberSubscribePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberSubscribePO fastMemberSubscribe = fastMemberSubscribeService.queryById(params);
        return ResultVO.success(fastMemberSubscribe);
    }

    @ApiName(value = "订阅消息-添加", folder = {"订阅消息"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastMemberSubscribePO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getDramaId() == null) {
            return ResultVO.error(StaticStr.DRAMAID_CANNOT_BE_EMPTY);
        }
        params.setMemberId(sessionVO.getMemberId());
        params.setMiniId(sessionVO.getMiniId());
        params.setOpenid(sessionVO.getOpenid());
        params.setRetailId(sessionVO.getRetailId());
        MethodVO methodVO = fastMemberSubscribeService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "订阅消息-更新", folder = {"订阅消息"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastMemberSubscribePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberSubscribeService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
