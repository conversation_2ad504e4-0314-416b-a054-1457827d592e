/*
 * Powered By fast.up
 */
package com.fast.controller.subscribe;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.subscribe.FastMiniSubscribeTemplatePO;
import com.fast.service.mini.FastMiniService;
import com.fast.service.subscribe.FastMiniSubscribeTemplateService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniSubscribeTemplate")
public class FastMiniSubscribeTemplateController extends BaseController {

    @Autowired
    private FastMiniSubscribeTemplateService fastMiniSubscribeTemplateService;
    @Autowired
    private FastMiniService fastMiniService;

    @ApiName(value = "订阅消息模板-查询列表", folder = {"订阅消息"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMiniSubscribeTemplatePO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setMiniId(sessionVO.getMiniId());

        if (params.getType() != null && params.getType() == 99) {
            // 如果是抖音就改成4，因为已经发版
            FastMiniVO miniVO = fastMiniService.queryInfoByRedis(sessionVO.getMiniId());
            if (miniVO != null && miniVO.getType() == 2) {
                params.setType(4);
            } else {
                params.setType(null);
            }
        } else if (params.getType() != null && params.getType() > 0) {
            // 保持不变
        } else {
            params.setType(1);
        }
        return fastMiniSubscribeTemplateService.queryPageList(params, pageVO);
    }

    @ApiName(value = "订阅消息-查询单个详情", folder = {"订阅消息"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniSubscribeTemplatePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMiniSubscribeTemplatePO fastMiniSubscribeTemplate = fastMiniSubscribeTemplateService.queryById(params);
        return ResultVO.success(fastMiniSubscribeTemplate);
    }

    @ApiName(value = "订阅消息-添加", folder = {"订阅消息"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastMiniSubscribeTemplatePO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMiniSubscribeTemplateService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "订阅消息-更新", folder = {"订阅消息"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastMiniSubscribeTemplatePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMiniSubscribeTemplateService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
