package com.fast.controller.app;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.LoginTypeEnum;
import com.fast.service.mini.UserMiniService;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.mini.MiniLoginVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/app")
public class AppController extends BaseController {

    @Autowired
    private UserMiniService userMiniService;

    @ApiName(value = "静默登录接口", folder = {"APP"})
    @ApiParamsIn(value = {
            "code:1:str:设备ID",
            "appId:1:str:应用ID",
            "linkId:0:int:推广链接id",
            "refer:0:str:2、运营链接进入 3、分享进入",
            "phoneBrand:0:str:手机品牌信息（如：HONOR_AQM-AL10_ANDROID，两个下滑线分开，品牌_型号_系统）"
    })
    @ApiParamsOut(value = {
            "openid:唯一ID",
            "encryptionMemberId:用户加密id"
    })
    @RequestMapping("/nologin/getOpenidByCode")
    public ResultVO<?> loginByCode(HttpServletRequest request, MiniLoginVO loginVO) {
        String deviceId = loginVO.getCode();
        String appId = loginVO.getAppId();

        if (StrUtil.isEmpty(deviceId)) {
            return ResultVO.error(StaticStr.CONTENT_CANNOT_BE_EMPTY);
        }
        if (StrUtil.isEmpty(appId)) {
            return ResultVO.error(StaticStr.APPID_CANNOT_BE_EMPTY);
        }

        String md5 = Md5Util.getMD5(deviceId);
        log.info("app login -> appId: {}, deviceId: {}, md5: {}", appId, deviceId, md5);

        loginVO.setDeviceId(deviceId);
        loginVO.setCode(md5);
        loginVO.setLoginType(LoginTypeEnum.DEVICE_ID.getCode());
        return userMiniService.loginByAppCodeQuick(request, loginVO);
    }

    @ApiName(value = "手机号登录接口", folder = {"APP"})
    @ApiParamsIn(value = {
            "phone:1:int:手机号",
            "verifyCode:1:str:验证码"
    })
    @ApiParamsOut(value = {
            "openid:唯一ID",
            "encryptionMemberId:用户加密id"
    })
    @RequestMapping("/loginByPhone")
    public ResultVO<?> loginByPhone(HttpServletRequest request, MiniLoginVO loginVO) {
        if (StrUtil.isEmpty(loginVO.getPhone())) {
            return ResultVO.error(StaticStr.PHONE_CANNOT_BE_EMPTY);
        }
        if (StrUtil.isEmpty(loginVO.getVerifyCode())) {
            return ResultVO.error(StaticStr.VERIFYCODE_CANNOT_BE_EMPTY);
        }
        loginVO.setLoginType(LoginTypeEnum.MOBILE.getCode());
        return userMiniService.loginByPhoneApp(request, loginVO);
    }

    @ApiName(value = "邮箱登录接口", folder = {"APP"})
    @ApiParamsIn(value = {
            "email:1:str:邮箱",
            "verifyCode:1:str:验证码"
    })
    @ApiParamsOut(value = {
            "openid:唯一ID",
            "encryptionMemberId:用户加密id"
    })
    @RequestMapping("/loginByEmail")
    public ResultVO<?> loginByEmail(HttpServletRequest request, MiniLoginVO loginVO) {
        if (StrUtil.isEmpty(loginVO.getEmail())) {
            return ResultVO.error(StaticStr.EMAIL_CANNOT_BE_EMPTY);
        }
        if (StrUtil.isEmpty(loginVO.getVerifyCode())) {
            return ResultVO.error(StaticStr.VERIFYCODE_CANNOT_BE_EMPTY);
        }
        if (!loginVO.getEmail().matches(StaticVar.EMAIL_REGEX)) {
            return ResultVO.error(StaticStr.EMAIL_INCORRECTLY_FORMATTED);
        }
        loginVO.setLoginType(LoginTypeEnum.EMAIL.getCode());
        return userMiniService.loginByEmailApp(request, loginVO);
    }

    @ApiName(value = "三方授权登录接口", folder = {"APP"})
    @ApiParamsIn(value = {
            "uid:1:str:唯一ID",
            "email:0:str:邮箱",
            "name:0:str:姓名",
            "photoUrl:0:str:头像地址",
            "providerId:0:str:提供者ID",
            "loginType:1:int:登录方式（4、google登录；5、Facebook登录）",
    })
    @ApiParamsOut(value = {
            "openid:唯一ID",
            "encryptionMemberId:用户加密id"
    })
    @RequestMapping("/loginByThird")
    public ResultVO<?> loginByThird(HttpServletRequest request, MiniLoginVO loginVO) {
        if (loginVO.getLoginType() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isEmpty(loginVO.getUid())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (loginVO.getEmail() != null && !loginVO.getEmail().matches(StaticVar.EMAIL_REGEX)) {
            return ResultVO.error(StaticStr.EMAIL_INCORRECTLY_FORMATTED);
        }
        return userMiniService.loginByThirdApp(request, loginVO);
    }

    @ApiName(value = "退出登录接口", folder = {"APP"})
    @ApiParamsIn(value = {
            "code:1:str:设备ID",
            "appId:1:str:应用ID"
    })
    @RequestMapping("/loginOut")
    public ResultVO<?> loginOut(HttpServletRequest request, MiniLoginVO loginVO) {
        if (StrUtil.isNotEmpty(loginVO.getCode())) {
            loginVO.setCode(Md5Util.getMD5(loginVO.getCode()));
        }
        return userMiniService.loginOutApp(request, loginVO);
    }

    @ApiName(value = "注销接口", folder = {"APP"})
    @ApiParamsIn(value = {
            "code:1:str:设备ID",
            "appId:1:str:应用ID",
            "linkId:0:int:推广链接id",
            "refer:0:str:2、运营链接进入 3、分享进入"
    })
    @ApiParamsOut(value = {
            "openid:唯一ID",
            "encryptionMemberId:用户加密id"
    })
    @RequestMapping("/loginDestory")
    public ResultVO<?> loginDestory(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        return userMiniService.loginDestoryApp(sessionVO);
    }

}
