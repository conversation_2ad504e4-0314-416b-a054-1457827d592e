package com.fast.controller.task;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.MiniTypeEnum;
import com.fast.po.task.FastMemberTaskActionPO;
import com.fast.po.task.FastMemberTaskRechargePO;
import com.fast.service.task.FastMemberTaskActionService;
import com.fast.service.task.FastMemberTaskRechargeService;
import com.fast.utils.redis.JedisLock;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 用户
 */
@RestController
@RequestMapping("/task")
public class FastMemberTaskController extends BaseController {

    @Autowired
    private FastMemberTaskActionService fastMemberTaskActionService;
    @Autowired
    private FastMemberTaskRechargeService fastMemberTaskRechargeService;


    @ApiName(value = "福利任务-查询完成情况", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn({"null:1:int:null",})
    @ApiParamsOut({
            "taskSetting:小程序福利任务配置",
            "signDay:已签到第几天",
            "advCount:已经观看广告数量",
            "coinRemain:剩余可用币",
            "newList:新手任务完成列表",
            "newList>>state:0未完成1已完成",
            "newList>>awardState:0未领取1已领取",
            "newList>>targetNum:目标值（如邀请5好友，追剧5部剧）",
            "newList>>processNum:进度值（当前已完成数量）",
            "usualList:日常任务完成列表",
            "usualList>>state:0未完成1已完成",
            "usualList>>awardState:0未领取1已领取",
            "usualList>>targetNum:目标值（如邀请5好友，追剧5部剧）",
            "usualList>>processNum:进度值（当前已完成数量）",
            "prideList:成就任务完成列表",
            "prideList>>state:0未完成1已完成",
            "prideList>>awardState:0未领取1已领取",
            "prideList>>targetNum:目标值（如邀请5好友，追剧5部剧）",
            "prideList>>processNum:进度值（当前已完成数量）",
    })
    @RequestMapping("/getMemberTaskInfo")
    public ResultVO<?> getMemberTaskInfo(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        JedisLock lock = new JedisLock(StaticVar.GET_MEMBER_TASK_INFO_LOCK + sessionVO.getMemberId(), 3_000, 5_000);
        ResultVO<?> resultVO = null;
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.REQUEST_FREQUENCY_TOO_HIGH);
            }
            resultVO = fastMemberTaskActionService.getMemberTaskInfo(sessionVO);
        } catch (Exception e) {
            log.error("getMemberTaskInfo lock fail: {}", ExceptionUtils.getStackTrace(e));
        } finally {
            lock.release();
        }
        return resultVO;
    }

    @ApiName(value = "福利任务-去完成福利任务", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn({
            "taskType:1:int:1签到2看广告3新手4日常5成就任务",
            "markIdx:1:int:标记顺序1,2,3,4,5,6;taskType=1或2时-第xxx天/次;taskType=3时-[1保存桌面,2桌面启动,3安全登录];taskType=4时-[1去剧场,2添加5部剧,3邀请5好友,4看一部新剧;taskType=5时-[1看完一整部,2累计看5部]",
            "备注:1:str:新手、日常、成就，调用后开始记录任务完成情况"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @RequestMapping("/goTaskAction")
    public ResultVO<?> goTaskAction(HttpServletRequest request, FastMemberTaskActionPO actionPO) {
        SessionVO sessionVO = getSessionVO(request);
        actionPO.setState(0);
        return fastMemberTaskActionService.addTaskAction(sessionVO, actionPO);
    }

    @ApiName(value = "福利任务-已完成福利任务", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn({
            "taskType:1:int:1签到2看广告3新手4日常5成就任务",
            "markIdx:1:int:标记顺序1,2,3,4,5,6;taskType=1或2时-第xxx天/次;taskType=3时-[1保存桌面,2桌面启动,3安全登录];taskType=4时-[1去剧场,2添加5部剧,3邀请5好友,4看一部新剧;taskType=5时-[1看完一整部,2累计看5部]",
            "moreTimesFlag:1:int:翻倍标志=1不变，=2翻倍，3翻三倍...",
            "备注:1:str:签到、看广告，调用后直接添加K币",
    })
    @ApiParamsOut({
            "status:ok"
    })
    @RequestMapping("/finishTaskAction")
    public ResultVO<?> addTaskAction(HttpServletRequest request, FastMemberTaskActionPO actionPO) {
        SessionVO sessionVO = getSessionVO(request);
        actionPO.setState(1);
        if (actionPO.getTaskType() == null) {
            return ResultVO.error(StaticStr.TASK_CANNOT_BE_EMPTY);
        }
        return fastMemberTaskActionService.addTaskAction(sessionVO, actionPO);
    }

    @ApiName(value = "邀请好友记录", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn({"无需参数:1:int:无需参数"})
    @ApiParamsOut({"status:ok"})
    @RequestMapping("/addInviteLog")
    public ResultVO<?> addInviteLog(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        return fastMemberTaskActionService.addInviteLog(sessionVO);
    }


    @ApiName(value = "福利任务K币记录", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn({
            "无参数:0:int:无参数"
    })
    @ApiParamsOut({
            "list>>remark:免费记录",
            "list>>createTime:操作时间",
            "list>>coinAll:获得K币",
            "list>>miniName:小程序名称",
            "list>>miniType:1：微信小程序;2抖音小程序;3H54快手",
            "list>>retailName: 分销商名称",
            "list>>expireHandle: 已做过期处理（0、否；1、是）"
    })
    @RequestMapping("/getFastMemberTaskRechargeList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberTaskRechargePO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setMemberId(sessionVO.getMemberId());
        // app应用特殊处理
        if (Objects.equals(sessionVO.getMiniType(), MiniTypeEnum.APP.index)) {
            params.setDataType(0);
        }
        return fastMemberTaskRechargeService.queryPageList(params, pageVO);
    }

    @ApiName(value = "福利任务领取接口", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn({
            "id:1:int:任务操作id"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @RequestMapping("/getPrize")
    public ResultVO<?> getPrize(HttpServletRequest request, FastMemberTaskActionPO params) {
        if (params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setMemberId(sessionVO.getMemberId());
        MethodVO methodVO = fastMemberTaskRechargeService.getPrize(sessionVO, params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
