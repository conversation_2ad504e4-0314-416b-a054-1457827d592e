/*
 * Powered By fast.up
 */
package com.fast.controller.drama;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.member.FastMemberRecentPO;
import com.fast.po.mini.FastMiniSettingPO;
import com.fast.po.monitor.FastMonitorInterfaceTimePO;
import com.fast.service.drama.FastDramaService;
import com.fast.service.member.FastMemberRecentService;
import com.fast.service.member.FastMemberService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.mini.FastMiniSettingService;
import com.fast.service.monitor.FastMonitorInterfaceTimeService;
import com.fast.utils.CalTime;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.drama.FastDramaVO;
import com.fast.vo.member.MemberDramaVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastDrama")
public class FastDramaController extends BaseController {

    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastMemberService memberService;
    @Autowired
    private FastMemberRecentService memberRecentService;
    @Autowired
    private FastMiniSettingService miniSettingService;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMonitorInterfaceTimeService fastMonitorInterfaceTimeService;

    @RequestMapping(value = "/getTags", method = {RequestMethod.POST})
    @ApiName(value = "查询短剧标签", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({"no:0:int:无需参数"})
    @ApiParamsOut({"status:ok"})
    public ResultVO<?> getTags(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        return ResultVO.success(dramaService.getDramaTags(sessionVO.getContentType(), null));
    }

    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    @ApiName(value = "查询短剧列表", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({"feeFlag:0:int:付费标识1付费2免费"})
    @ApiParamsOut({
            "dramaName:短剧名称",
            "encryptionId:加密id",
            "contentType:1=短剧;2=漫画;3=小说",
            "sex:性别 0=无;1=男;2=女",
            "dramaCover:封面",
            "dramaCoverHor:横版封面",
            "retailCount:已授权分销商数量",
            "recDramaId:推荐剧id",
            "recDramaName:推荐剧名称",
            "seriesNumUpdate:已更新的集数",
            "releaseDate:上线日期",
            "releaseDate:上线日期",
    })
    public ResultVO<?> getList(HttpServletRequest request, FastDramaPO params, PageVO pageVO) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getTagIds(), params.getCornerIds());
        params.setDelFlag(StaticVar.NO);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setContentType(sessionVO.getContentType());
        // 只查有效数据
        params.setOpenState(1);
        params.setShelfState(1);
        return dramaService.queryPageList(params, pageVO, sessionVO);
    }

    @ApiName(value = "查询剧介绍", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({"no:0:int:无"})
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/getRecList", method = {RequestMethod.POST})
    public ResultVO<?> getRecList(HttpServletRequest request, FastDramaPO params) {
        List<FastDramaPO> dramaList = dramaService.queryRecList(params);
        return ResultVO.success("ok", dramaList);
    }

    /**
     * 查询用户播放剧接口
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "查询用户播放剧接口，播放列表", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "id:1:int:剧id-数字",
            "tagRelation:0:int:加载tag相关推荐（1、是）"
    })
    @ApiParamsOut({
            "dramaObj:剧信息",
            "dramaObj 》updateState:更新状态: 1=连载;0=完结",
            "dramaObj 》openState:显示状态: 1=显示;0=隐藏",
            "dramaObj 》shelfState:上架状态: 1=上架;0=下架",
            "dramaObj 》dramaName:短剧名称",
            "dramaObj 》dramaCover:短剧封面",
            "dramaObj 》seriesNumAll:剧集总数",
            "dramaObj 》seriesNumUpdate:更新集数",
            "dramaObj 》dramaIntroduce:剧集描述",
            "dramaObj 》corner:角标",
            "dramaObj 》tagNameList:标签列表",
            "dramaObj 》addictionState:1-已关注;0-未关注",
            "dramaObj 》recentSeriesNum:最近观看剧集号",
            "dramaObj 》recentSeriesNum:最近观看剧集号",
            "dramaObj 》addictionCount:在追数量",
            "seriesList:剧集列表",
            "seriesList 》state:显示状态，1=显示;0=隐藏",
            "seriesList 》seriesNum:集序号",
            "seriesList 》follow:关注集号，拉起关注，0否1是",
            "seriesList 》seriesTime:视频长度",
            "seriesList 》url:视频地址",
            "seriesList 》lock:0解锁，1未解锁",
            "seriesList 》coinPer:单集金币",
            "seriesList 》seriesCover:单集封面",
            "seriesList 》title:简介",
    })
    @RequestMapping(value = "/getDrama", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastDramaPO params) {
        FastMonitorInterfaceTimePO mifPO = fastMonitorInterfaceTimeService.monitorStart("getDrama2");
        CalTime calTime = new CalTime();
        if (params.getId() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        // params.setContentType(sessionVO.getContentType());
        ResultVO<?> resultVO = dramaService.queryDramaAgent(params, sessionVO);
        if (mifPO != null) {
            mifPO.setCostTime(calTime.getCostTimeLong().intValue());
            fastMonitorInterfaceTimeService.monitorEnd(mifPO, sessionVO.getMemberId());
        }
        actionLogService.log("mini_getdrama", "clientType=" + sessionVO.getClientType() + "，dramaId=" + params.getId() + "，costTime=" + calTime.getCostTime());
        return resultVO;
    }

    /**
     * 查询推荐剧详细信息
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "查询推荐剧详细信息", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({"id:1:int:当前在看的剧id-数字"})
    @ApiParamsOut({
            "dramaObj:剧信息",
            "dramaObj 》updateState:更新状态: 1=连载;0=完结",
            "dramaObj 》openState:显示状态: 1=显示;0=隐藏",
            "dramaObj 》shelfState:上架状态: 1=上架;0=下架",
            "dramaObj 》dramaName:短剧名称",
            "dramaObj 》dramaCover:短剧封面",
            "dramaObj 》seriesNumAll:剧集总数",
            "dramaObj 》seriesNumUpdate:更新集数",
            "dramaObj 》dramaIntroduce:剧集描述",
            "dramaObj 》corner:角标",
            "dramaObj 》tagNameList:标签列表",
            "dramaObj 》addictionState:1-已关注;0-未关注",
            "dramaObj 》recentSeriesNum:最近观看剧集号",
            "seriesList:剧集列表",
            "seriesList 》state:显示状态，1=显示;0=隐藏",
            "seriesList 》seriesNum:集序号",
            "seriesList 》follow:关注集号，拉起关注，0否1是",
            "seriesList 》seriesTime:视频长度",
            "seriesList 》url:视频地址",
            "seriesList 》lock:0解锁，1未解锁",
            "seriesList 》coinPer:单集金币"
    })
    @RequestMapping(value = "/getRecDrama", method = {RequestMethod.POST})
    public ResultVO<?> getRecDrama(HttpServletRequest request, FastDramaPO params) {
        if (params.getId() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        // 查询当前剧
        FastDramaVO dramaObj = dramaService.queryInfoByRedis(params);
        if (dramaObj == null) {
            return ResultVO.error100("剧不存在");
        }
        if (dramaObj.getShelfState() == 0) {
            return ResultVO.error100("剧已下架");
        }
        boolean hasRecDrama = true;
        Integer recDramaId = dramaObj.getRecDramaId();
        // 查询推荐剧
        if (recDramaId != null) {
            params.setId(recDramaId);
            FastDramaVO recDrama = dramaService.queryInfoByRedis(params);
            if (recDrama == null) {
                hasRecDrama = false;
            } else if (recDrama.getShelfState() == 0) {
                hasRecDrama = false;
            } else if (recDrama.getOpenState() == 0) {
                hasRecDrama = false;
            } else {
                boolean hasSettingMini = false;
                // 判断推荐短剧是否配置到小程序
                List<Integer> dramaIds = miniSettingService.getMiniSettingDramaIds(sessionVO.getMiniId(), params.getContVersionId());
                if (CollUtil.hasContent(dramaIds) && dramaIds.contains(recDramaId)) {
                    hasSettingMini = true;
                }
                // 如果推荐短剧没有配置到小程序, 判断是否在我的观看历史
                if (!hasSettingMini) {
                    List<MemberDramaVO> recentList = memberService.getMemberRecentListRedis(sessionVO.getMemberId());
                    if (CollUtil.hasContent(recentList)) {
                        for (MemberDramaVO cur : recentList) {
                            if (cur.getDramaId().equals(recDramaId)) {
                                hasSettingMini = true;
                                break;
                            }
                        }
                    }
                }
                // 如果推荐短剧已配置到小程序 或 没有配置到小程序且是在我的观看历史
                if (hasSettingMini) {
                    // 查询我最近观看, 判断该推荐剧是否看完
                    List<MemberDramaVO> recentList = memberService.getMemberRecentListRedis(sessionVO.getMemberId());
                    if (CollUtil.hasContent(recentList)) {
                        for (MemberDramaVO cur : recentList) {
                            if (cur.getDramaId().equals(recDramaId)) {
                                recDrama.setRecentSeriesNum(cur.getSeriesNum());
                                break;
                            }
                        }
                        // 如果看到最后一集, 则跳过
                        if (recDrama.getRecentSeriesNum() != null && Objects.equals(recDrama.getRecentSeriesNum(), recDrama.getSeriesNumUpdate())) {
                            hasRecDrama = false;
                        }
                    }
                } else {
                    hasRecDrama = false;
                }
            }
        } else {
            hasRecDrama = false;
        }
        // 无推荐剧, 查询观看历史中未看到最后一集的剧, 随机一个
        if (!hasRecDrama) {
            FastMemberRecentPO recent = new FastMemberRecentPO();
            recent.setMemberId(sessionVO.getMemberId());
            Integer dramaId = memberRecentService.queryRecentDramaNotFinish(recent);
            if (dramaId != null) {
                params.setId(dramaId);
            } else {
                return ResultVO.error100("无推荐剧");
            }
        } else {
            // 推荐剧
            params.setId(recDramaId);
        }
        return dramaService.queryDramaAgent(params, sessionVO);
    }

    /**
     * 查询短剧精选
     *
     * @param request
     * @param params
     * @return
     */
    @RequestMapping(value = "/getBoutiqueList", method = {RequestMethod.POST})
    @ApiName(value = "查询短剧精选", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "appId:1:str:小程序appId",
            "boutiqueType:0:int:精选类型（0/null、剧场推荐；1、精选TAB）",
    })
    @ApiParamsOut({
            "dramaName:短剧名称",
            "encryptionId:加密id",
            "contentType:1=短剧;2=漫画;3=小说",
            "sex:性别 0=无;1=男;2=女",
            "dramaCover:封面",
            "dramaCoverHor:横版封面",
            "retailCount:已授权分销商数量",
            "recDramaId:推荐剧id",
            "recDramaName:推荐剧名称",
            "seriesNumUpdate:已更新的集数",
            "releaseDate:上线日期",
            "seriesNum:我看到的集号",
            "url:剧集播放地址",
            "url:剧集播放地址",
    })
    public ResultVO<?> getBoutiqueList(HttpServletRequest request, FastMiniSettingPO params) {
        // 判断是否被sql注入
        // 微信小程序
        FastMiniVO miniPO = fastMiniService.queryByAppIdRedis(params.getAppId());
        if (miniPO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }
        params.setMiniId(miniPO.getId());
        params.setCreatorId(0);
        params.setUpdatorId(0);
        params.setType(miniPO.getType());

        SessionVO sessionVO = getSessionVO(request);
        if (sessionVO != null) {
            params.setRetailId(sessionVO.getRetailId());
        }
        return dramaService.getBoutiqueList(params, sessionVO);
    }

    /**
     * 查询短剧精选
     *
     * @param request
     * @param params
     * @return
     */
    @RequestMapping(value = "/getRecommendBottomList", method = {RequestMethod.POST})
    @ApiName(value = "查询短剧推荐列表", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({"appId:1:str:小程序appId"})
    @ApiParamsOut({
            "dramaName:短剧名称",
            "encryptionId:加密id",
            "contentType:1=短剧;2=漫画;3=小说",
            "sex:性别 0=无;1=男;2=女",
            "dramaCover:封面",
            "dramaCoverHor:横版封面",
            "retailCount:已授权分销商数量",
            "recDramaId:推荐剧id",
            "recDramaName:推荐剧名称",
            "seriesNumUpdate:已更新的集数",
            "releaseDate:上线日期",
            "seriesNum:我看到的集号",
            "url:剧集播放地址",
            "url:剧集播放地址",
    })
    public ResultVO<?> getRecommendBottomList(HttpServletRequest request, FastMiniSettingPO params, PageVO pageVO) {
        // 判断是否被sql注入
        // 微信小程序
        FastMiniVO miniPO = fastMiniService.queryByAppIdRedis(params.getAppId());
        params.setMiniId(miniPO.getId());
        params.setCreatorId(0);
        params.setUpdatorId(0);
        params.setType(miniPO.getType());
        SessionVO sessionVO = getSessionVO(request);
        if (sessionVO != null) {
            params.setRetailId(sessionVO.getRetailId());
        }
        return dramaService.getRecommendBottomList(params, sessionVO, pageVO);
    }

    /**
     * 查询短剧列表(分销商全量剧)=性能优化
     */
    @RequestMapping(value = "/getRetailDramaAllList", method = {RequestMethod.POST})
    @ApiName(value = "查询分销商短剧全列表", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({"000"})
    @ApiParamsOut({
            "dramaName:短剧名称",
            "dramaCover:剧封面",
            "dramaCoverHor:剧封面横版",
            "seriesNumUpdate:已更新集数"
    })
    public ResultVO<?> getRetailDramaAllList(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        return dramaService.getRetailDramaAllList(sessionVO);
    }

    /**
     * 查询排行榜
     */
    @RequestMapping(value = "/getDramaRankings", method = {RequestMethod.POST})
    @ApiName(value = "查询排行榜", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "type:1:int:榜单类型（1、热播榜）"
    })
    @ApiParamsOut({
            "dramaName:短剧名称",
            "dramaCover:剧封面",
            "dramaCoverHor:剧封面横版",
            "seriesNumUpdate:已更新集数"
    })
    public ResultVO<?> getDramaRankings(HttpServletRequest request, FastDramaPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setContentType(sessionVO.getContentType());
        return dramaService.getDramaRankings(params, pageVO);
    }

}
