/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.mini.FastMiniBottomPO;
import com.fast.po.mini.FastMiniMemberVersionPO;
import com.fast.po.mini.FastMiniSettingPO;
import com.fast.service.common.FastActionLogService;
import com.fast.service.mini.FastMiniMemberVersionService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.mini.FastMiniSettingService;
import com.fast.service.monitor.FastMonitorInterfaceTimeService;
import com.fast.service.upay.UpayTtMerchantService;
import com.fast.utils.CalTime;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 小程序各项配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniSetting")
public class FastMiniSettingController extends BaseController {

    @Autowired
    private FastMiniSettingService miniSettingService;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMiniMemberVersionService memberVersionService;
    @Autowired
    private FastActionLogService actionLogService;
    @Autowired
    private UpayTtMerchantService upayTtMerchantService;
    @Autowired
    private FastMonitorInterfaceTimeService fastMonitorInterfaceTimeService;

    @ApiName(value = "获取小程序配置详细信息", folder = {"小程序配置"})
    @ApiParamsIn({"appId:1:str:小程序appId"})
    @ApiParamsOut({"同PC接口返回结果"})
    @RequestMapping(value = {"/getDetail", "/nologin/getDetail"}, method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniSettingPO params) {
        CalTime calTime = new CalTime();
        if (StrUtil.isEmpty(params.getAppId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        FastMiniSettingPO settingPO;
        // 微信小程序
        FastMiniVO miniPO = fastMiniService.queryByAppIdRedis(params.getAppId());
        if (miniPO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }
        params.setMiniId(miniPO.getId());
        params.setCreatorId(0);
        params.setUpdatorId(0);
        params.setType(miniPO.getType());
        FastMiniMemberVersionPO versionPO = null;
        long version = 0;
        if (sessionVO != null) {
            params.setRetailId(sessionVO.getRetailId());
            versionPO = memberVersionService.queryInfoByRedis(sessionVO.getMemberId());
            if (versionPO != null && versionPO.getVersion() != null) {
                version = versionPO.getVersion();
            }
        }
        settingPO = miniSettingService.queryInfoByRedisMember(params, versionPO, sessionVO);
        settingPO.setDesktopFlag(miniPO.getDesktopFlag());
        // 获取数据版本号
        Integer value = toInteger(RedisUtil.get(StaticVar.MINI_CONTENT_SETTING_VER + miniPO.getId() + ":" + params.getContVersionId()), 0);
        settingPO.setVersion(value + "-" + version);
        actionLogService.log("method_cost", "/fastMiniSetting/getDetail:" + calTime.getCostTime());
        actionLogService.log("get_minisetting", "获取成功");
        settingPO.setAccelerateFlag(miniPO.getAccelerateFlag());
        if (settingPO.getType() == 1) {
            // 微小过滤state为0的
            if (CollUtil.isNotEmpty(settingPO.getBottomList())) {
                List<FastMiniBottomPO> result = settingPO.getBottomList()
                        .stream().filter(item -> {
                            return item.getOpenState() == 1;
                        }).collect(Collectors.toList());
                settingPO.setBottomList(result);
            }
        }
        return ResultVO.success(settingPO);
    }

}
