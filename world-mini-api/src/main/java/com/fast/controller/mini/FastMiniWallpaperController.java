/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.po.mini.FastMiniWallpaperPO;
import com.fast.service.mini.FastMiniWallpaperService;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniWallpaper")
public class FastMiniWallpaperController extends BaseController {

    @Autowired
    private FastMiniWallpaperService fastMiniWallpaperService;

    @ApiName(value = "壁纸-查询列表", folder = {"壁纸"})
    @RequestMapping(value = "/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastMiniWallpaperPO params) {
        return fastMiniWallpaperService.queryList(params);
    }

}
