/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.mini.FastMiniPO;
import com.fast.service.mini.FastMiniSettingOperationPopService;
import com.fast.service.mini.FastMiniSettingOperationService;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mini")
public class FastMiniSettingOperationController extends BaseController {

    @Autowired
    private FastMiniSettingOperationService fastMiniSettingOperationService;
    @Autowired
    private FastMiniSettingOperationPopService fastMiniSettingOperationPopService;

    @ApiName(value = "运营能力-获得加桌设置", folder = {"mini"})
    @ApiParamsIn({
            "appId:1:str:小程序appId"
    })
    @ApiParamsOut({
            "data.desktopType:加桌方式1=强制加桌;2=引导加桌;3=不展示加桌",
            "data.startSeriesNum:加桌剧集",
            "data.popStep:弹窗弹出频率",
            "data.popTitle:弹窗标题文案",
            "data.popDesc:弹窗描述文案",
            "data.popBtnTitle:弹窗按钮文案",
    })
    @PostMapping("/operation/getMiniDeskConfig")
    public ResultVO<?> getMiniDeskConfig(FastMiniPO params) {
        if (StrUtil.isEmpty(params.getAppId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        return fastMiniSettingOperationService.getMiniDeskConfig(params);
    }

    @ApiName(value = "运营能力-获得浮窗配置", folder = {"mini"})
    @ApiParamsIn({
            "appId:1:str:小程序appId"
    })
    @ApiParamsOut({
            "popName:浮窗名称",
            "crowdId:目标人群id 0=所有人",
            "popType:浮窗类型 1=加桌按钮;2=福利活动",
            "popPage:浮窗位置 1=剧场;2=追剧;3=我的;4=观看历史;5=播放页，多个逗号隔开",
            "popPosition:播放浮窗样式 1=位于底部;2=位于侧边",
            "popDesc:浮窗文案",
            "jumpPage:跳转内容 1=福利中心",
            "state:状态 0=禁用;1=启用",
    })
    @PostMapping("/operation/getMiniPopConfig")
    public ResultVO<?> getMiniPopConfig(FastMiniPO params) {
        if (StrUtil.isEmpty(params.getAppId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        return fastMiniSettingOperationPopService.getMiniPopConfig(params);
    }
}
