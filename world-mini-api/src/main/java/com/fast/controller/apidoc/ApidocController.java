package com.fast.controller.apidoc;

import com.fast.constant.StaticStr;
import com.fast.framework.config.ApiConfig;
import com.fast.vo.ApiDocVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/apidoc")
public class ApidocController {
    @Value("${javadoc}")
    private Boolean enable;
    @Autowired
    private ApiConfig apiConfig;

    @RequestMapping("/getAllMethod")
    public ResultVO getAllMethod() {
        if (!enable) {
            return ResultVO.error(StaticStr.NOT_SUPPORTED);
        }
        List<ApiDocVO> list = apiConfig.getAllMethod();
        Map<String, Object> results = ResultVO.getMap();
        results.put("list", list);
        return ResultVO.success(results);
    }

    @RequestMapping("/index")
    public ResultVO index(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.reset();//（清空缓冲区）(1.设置此处刷新)
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html; charset=utf-8");
        PrintWriter out = response.getWriter();//(2.使用getOutputStream(),防止和Springboot中Tomcat不一致)
        out.write(ApiConfig.getHtml());
        out.close();
        return null;
    }


}
