/*
 * Powered By fast.up
 */
package com.fast.controller.monitor;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.monitor.FastMonitorMiniPO;
import com.fast.service.monitor.FastMonitorMiniService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor")
public class FastMonitorMiniController extends BaseController {

    @Autowired
    private FastMonitorMiniService fastMonitorMiniService;


    @ApiName(value = "monitor-添加小程序视频播放异常", folder = {"monitor"})
    @ApiParamsIn({
            "type:1:int:类型1视频播放",
            "appId:1:str:小程序appid",
            "dramaId:1:int:剧id",
            "lastSeriesNum:0:int:剧集",
            "remark:0:str:备注信息"
    })
    @ApiParamsOut({
            "success:ok"
    })
    @PostMapping("/insertFastMonitorMini")
    public ResultVO<?> insert(HttpServletRequest request, FastMonitorMiniPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (!StrUtil.isNum(params.getLastSeriesNum())) {
            params.setLastSeriesNum("1");
        }
        MethodVO methodVO = fastMonitorMiniService.insert(sessionVO, params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
