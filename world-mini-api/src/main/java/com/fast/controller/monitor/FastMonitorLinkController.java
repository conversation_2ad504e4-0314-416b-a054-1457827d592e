/*
 * Powered By fast.up
 */
package com.fast.controller.monitor;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.po.monitor.FastMonitorLinkPO;
import com.fast.service.monitor.FastMonitorLinkService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMonitorLink")
public class FastMonitorLinkController extends BaseController {

    @Autowired
    private FastMonitorLinkService fastMonitorLinkService;

    @ApiName(value = "监控-添加", folder = {"监控"})
    @RequestMapping(value = "/nologin/insert")
    public ResultVO<?> insert(HttpServletRequest request, FastMonitorLinkPO params) {
        if (params.getStep() == null) {
            log.error("step不能为空");
        }
        if (StrUtil.isEmpty(params.getUuid())) {
            log.error("uuid不能为空");
        }
        params.setCreateTime(DateUtil.getNowDate());
        MethodVO methodVO = fastMonitorLinkService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
