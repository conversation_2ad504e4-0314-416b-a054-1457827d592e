/*
 * Powered By fast.up
 */
package com.fast.controller.monitor;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.monitor.FastMonitorPointTimePO;
import com.fast.service.monitor.FastMonitorPointTimeService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor")
public class FastMonitorPointTimeController extends BaseController {

    @Autowired
    private FastMonitorPointTimeService fastMonitorPointTimeService;

    @ApiName(value = "monitor-添加", folder = {"monitor"})
    @ApiParamsIn({
            "typeCode:1:str:编码",
            "pointTime:1:int:13位毫秒时间戳",
            "randomCode:1:str:随机串",
            "seriesNum:0:int:剧集"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/nologin/monitorPointTime")
    public ResultVO<?> insert(HttpServletRequest request, FastMonitorPointTimePO params) {
        if (StrUtil.isEmpty(params.getRandomCode())) {
            return ResultVO.success();
        }
        SessionVO sessionVO = getSessionVO(request);
        if (sessionVO != null) {
            params.setLinkId(sessionVO.getLinkId());
            params.setMiniId(sessionVO.getMiniId());
            params.setMemberId(sessionVO.getMemberId());
            if (params.getDramaId() == null) {
                params.setDramaId(sessionVO.getDramaId());
            }
        }
        MethodVO methodVO = fastMonitorPointTimeService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }


}
