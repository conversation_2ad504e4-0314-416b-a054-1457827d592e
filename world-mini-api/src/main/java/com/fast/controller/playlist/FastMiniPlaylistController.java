/*
 * Powered By fast.up
 */
package com.fast.controller.playlist;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.mini.FastMiniContVersionPO;
import com.fast.po.playlist.FastMiniPlaylistPO;
import com.fast.service.mini.FastMiniContVersionService;
import com.fast.service.playlist.FastMiniPlaylistService;
import com.fast.utils.StrUtil;
import com.fast.utils.thread.LanguageContext;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniPlaylist")
public class FastMiniPlaylistController extends BaseController {

    @Autowired
    private FastMiniPlaylistService fastMiniPlaylistService;

    @Autowired
    private FastMiniContVersionService fastMiniContVersionService;

    @ApiName(value = "剧单-查询列表", folder = {"剧单"})
    @ApiParamsIn({
            "page:1:int:页码",
            "limit:1:int:每页条数",
            "miniId:1:int:应用ID",
    })
    @ApiParamsOut({
            "list 》 type:应用类型",
            "list 》 miniId:应用ID",
            "list 》 title:标题",
            "list 》 subTitle:副标题",
            "list 》 remark:描述信息",
            "list 》 cover:封面图",
            "list 》 seq:排序",
            "list 》 show:是否显示（0、否；1、是）",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastMiniPlaylistPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setMiniId(sessionVO.getMiniId());

        // 拿到用户配置的语言，找到对应的版本
        String lang = LanguageContext.getLanguageType();
        FastMiniContVersionPO contVersion = fastMiniContVersionService.queryByLangCodeNotDefaulted(params.getMiniId(), lang);
        if (contVersion == null || contVersion.getDefaulted() == 1) {
            return ResultVO.success(getPageListData(new ArrayList<>(), pageVO));
        }
        params.setContVersionId(contVersion.getId());
        log.info("playlist, 当前用户 {} 手机配置的语言: {}，查询到的APP内容版本: {} - {}", sessionVO.getMemberId(), lang, contVersion.getId(), contVersion.getName());

        return fastMiniPlaylistService.queryPageList(params, pageVO);
    }

    @ApiName(value = "剧单-查询单个详情", folder = {"剧单"})
    @ApiParamsIn({
            "id:1:int:剧单ID",
    })
    @ApiParamsOut({
            "type:应用类型",
            "miniId:应用ID",
            "title:标题",
            "subTitle:副标题",
            "remark:描述信息",
            "cover:封面图",
            "seq:排序",
            "show:是否显示（0、否；1、是）",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniPlaylistPO params) {
        if (params.getId() == null && !StrUtil.isEmpty(params.getEncryptionId())) {
            Integer id = decodeInt(params.getEncryptionId());
            if (id != null) {
                params.setId(id);
            }
        }
        if (params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        return ResultVO.success(fastMiniPlaylistService.getDetail(params.getId()));
    }

}
