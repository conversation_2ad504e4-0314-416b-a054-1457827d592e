/*
 * Powered By fast.up
 */
package com.fast.controller.help;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.help.FastHelpComplainPO;
import com.fast.service.help.FastHelpComplainService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/help")
public class FastHelpComplainController extends BaseController {

    @Autowired
    private FastHelpComplainService fastHelpComplainService;

    @ApiName(value = "问题投诉-查询列表", folder = {"help"})
    @ApiParamsIn({
            "null:0:int:无需参数",
    })
    @ApiParamsOut({
            "encryptionId:加密ID",
            "id:ID",
            "phone:反馈手机号",
            "type:问题类型0=其他;1=更新慢;2=不流畅;3=耗流量;4=剧集少;5=价格高;6=界面少;7=提示少",
            "feedbackText:反馈内容",
            "state:处理状态0=待处理1=处理中=2已处理",
    })
    @PostMapping("/getFastHelpComplainList")
    public ResultVO<?> getList(HttpServletRequest request, FastHelpComplainPO params, PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeStart(date.get(0));
            params.setCreateTimeEnd(date.get(1));
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setMemberId(sessionVO.getMemberId());
        params.setLinkId(sessionVO.getLinkId());
        return fastHelpComplainService.queryMiniPageList(params, pageVO);
    }

    @ApiName(value = "问题投诉-查询单个详情", folder = {"help"})
    @ApiParamsIn({
            "encryptionId:加密ID",
    })
    @ApiParamsOut({
            "encryptionId:加密ID",
            "id:ID",
            "phone:反馈手机号",
            "type:问题类型0=其他;1=更新慢;2=不流畅;3=耗流量;4=剧集少;5=价格高;6=界面少;7=提示少",
            "feedbackText:反馈内容",
            "state:处理状态0=待处理1=处理中=2已处理",
    })
    @PostMapping("/getFastHelpComplainDetail")
    public ResultVO<?> getDetail(FastHelpComplainPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        FastHelpComplainPO fastHelpComplain = fastHelpComplainService.queryById(params);
        return ResultVO.success(fastHelpComplain);
    }

    @ApiName(value = "问题投诉-提交反馈", folder = {"help"})
    @ApiParamsIn({
            "phone:0:str:馈手机号",
            "type:0:int:问题类型0=其他;1=更新慢;2=不流畅;3=耗流量;4=剧集少;5=价格高;6=界面少;7=提示少",
            "feedbackText:0:str:反馈内容",
    })
    @ApiParamsOut({
            "state:状态0",
    })
    @PostMapping("/insertFastHelpComplain")
    public ResultVO<?> insertFastHelpComplain(HttpServletRequest request, FastHelpComplainPO params) {
        // 参数校验
        if (StrUtil.isEmpty(params.getPhone())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.PHONE_CANNOT_BE_EMPTY);
        }
        if (StrUtil.isEmpty(params.getFeedbackText())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.CONTENT_CANNOT_BE_EMPTY);
        }

        SessionVO sessionVO = getSessionVO(request);
        params.setMemberId(sessionVO.getMemberId());
        params.setLinkId(sessionVO.getLinkId());
        MethodVO methodVO = fastHelpComplainService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
