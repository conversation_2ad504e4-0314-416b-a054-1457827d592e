/*
 * Powered By fast.up
 */
package com.fast.controller.help;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.help.FastHelpQuestionPO;
import com.fast.service.help.FastHelpQuestionService;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/help")
public class FastHelpQuestionController extends BaseController {

    @Autowired
    private FastHelpQuestionService fastHelpQuestionService;

    @ApiName(value = "帮助中心-查询列表", folder = {"help"})
    @ApiParamsIn({
            "null:0:int:无需参数",
    })
    @ApiParamsOut({
            "encryptionId:问题类型加密ID",
            "id:问题类型ID",
            "name:问题类型名称",
            "sequence:排序正序",
            "questionList>>encryptionId:问题加密ID",
            "questionList>>id:问题id",
            "questionList>>typeId:问题类型id",
            "questionList>>name:问题名称",
            "questionList>>solution:问题解答",
            "questionList>>sequence:排序正序",
    })
    @PostMapping("/getFastHelpQuestionList")
    public ResultVO<?> getList() {
        return fastHelpQuestionService.queryTreeList();
    }

    @ApiName(value = "帮助中心-查询问题解答详情", folder = {"help"})
    @ApiParamsIn({
            "null:0:int:无需参数",
    })
    @ApiParamsOut({
            "encryptionId:问题类型加密ID",
            "id:问题id",
            "typeId:问题类型id",
            "name:问题名称",
            "solution:问题解答",
            "sequence:排序正序",
    })
    @PostMapping("/getAnswer")
    public ResultVO<?> getAnswer(FastHelpQuestionPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        return fastHelpQuestionService.getAnswer(params);
    }

    @ApiName(value = "帮助中心-是否解决", folder = {"help"})
    @ApiParamsIn({
            "encryptionId:0:str:加密ID",
            "clickType:0:int:类型1已解决0未解决",
    })
    @ApiParamsOut({
            "state:状态0",
    })
    @PostMapping("/clickValid")
    public ResultVO<?> clickValid(FastHelpQuestionPO params, HttpServletRequest request) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        if (!StrUtil.equalsAny(params.getClickType(), 0, 1)) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.CLICK_RESOLVED_OR_UNRESOLVED);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        return fastHelpQuestionService.clickValid(params, sessionVO);
    }


}
