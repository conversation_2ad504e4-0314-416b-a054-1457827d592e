package com.fast.controller.upay;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.IoUtils;
import com.alibaba.nacos.common.utils.MapUtil;
import com.fast.base.BaseController;
import com.fast.service.upay.UpayPayService;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

/**
 * 聚合支付
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("upay/wap/asyncNotice")
public class UpayAsyncNoticeController extends BaseController {

    @Autowired
    private UpayPayService upayPayService;

    /**
     * 支付宝支付回调(支付宝小程序)
     *
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/nologin/aliPay/callbackSaas")
    public Object aliPayCallback(HttpServletRequest request) throws Exception {
        JSONObject map = getParams(request);
        log.info("接收到支付宝支付回调map:{}", map.toJSONString());

        // boolean sign = PayFactory.Payment.Common(StaticVar.ALI_SAAS_APP_ID, StaticVar.ALI_SAAS_PRIVATE_KEY, StaticVar.ALI_SAAS_PUBLIC_KEY).verifyNotify(JsonUtil.jsonToMap(map));
        // if (!sign) {
        //     log.error("支付宝回调签名验证失败请求参数:{}", map.toJSONString());
        //     return "fail";
        // }

        upayPayService.aliPayCallback(map);
        return "success";
    }

    /**
     * 获取请求参数
     *
     * @param request 请求信息
     * @return JSONObject
     * @throws IOException
     */
    public JSONObject getParams(HttpServletRequest request) throws IOException {
        JSONObject parameterMap = new JSONObject();
        if (MapUtil.isNotEmpty(request.getParameterMap())) {
            for (Map.Entry<String, String[]> map : request.getParameterMap().entrySet()) {
                parameterMap.put(map.getKey(), map.getValue()[0]);
            }
        }
        if ("POST".equalsIgnoreCase(request.getMethod())) {
            String readStr = StrUtil.join(IoUtils.readLines(request.getReader()), "");
            if (StringUtils.isNotBlank(readStr)) {
                parameterMap.putAll(JsonUtil.toJSONObject(readStr));
            }
        }
        return parameterMap;
    }

}
