package com.fast.controller.upay;

import com.fast.base.BaseController;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.framework.exception.MyException;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.upay.UpayOrderLog;
import com.fast.service.member.FastMemberOrderRechargeService;
import com.fast.service.upay.UpayCloseTransService;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 聚合支付
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("upay/wap")
public class UpayCloseController extends BaseController {

    @Autowired
    private UpayCloseTransService upayCloseTransService;
    @Autowired
    private FastMemberOrderRechargeService fastMemberOrderRechargeService;

    /**
     * 发起关单-接口
     *
     * @param request
     * @param param
     * @return
     */
    @RequestMapping("closeTrans")
    public synchronized ResultVO<?> closeTrans(HttpServletRequest request, UpayOrderLog param) {
        ResultVO<?> resultData = new ResultVO<>();
        try {
            SessionVO sessionVO = getSessionVO(request);
            if (sessionVO == null) {
                return ResultVO.error(StaticStr.USER_LOGIN_HAS_EXPIRED);
            }
            if (isBlank(param.getEncryptionId()) && isBlank(param.getEncryptionId())) {
                return ResultVO.error(501, StaticMsg.MSG_PAY_ERROR_104);
            }

            Long rechargeOrderId = decodeLong(param.getEncryptionId());
            if (rechargeOrderId == null || rechargeOrderId <= 0) {
                return ResultVO.error(501, StaticMsg.MSG_PAY_ERROR_104);
            }
            FastMemberOrderRechargePO query = new FastMemberOrderRechargePO();
            query.setId(rechargeOrderId);
            FastMemberOrderRechargePO rechargeOrder = fastMemberOrderRechargeService.queryOne(query);
            if (rechargeOrder == null) {
                return ResultVO.error(512, StaticMsg.MSG_PAY_ERROR_117);
            }
            if (rechargeOrder.getState() == 2) {
                return ResultVO.error(512, StaticMsg.MSG_PAY_ERROR_120);
            }
            param.setRechargeOrderId(rechargeOrderId);
            param.setPayLogId(rechargeOrder.getPayLogId());
            param.setId(rechargeOrder.getPayLogId());
            param.setRetailId(rechargeOrder.getRetailId());
            param.setMiniId(rechargeOrder.getMiniId());
            // 发起关单
            upayCloseTransService.close(resultData, param);
        } catch (MyException e) {
            return ResultVO.error(501, e.getMessage());
        } catch (Exception e) {
            MyException.print(log, e);
            resultData.setCode(501);
            resultData.setMessage(e.getMessage());
        }
        return resultData;
    }
}
