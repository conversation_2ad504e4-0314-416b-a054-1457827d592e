package com.fast.controller.upay;

import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.framework.exception.MyException;
import com.fast.po.upay.UpayOrderLog;
import com.fast.service.upay.UpayRefundService;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 退款
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("upay/wap")
public class UpayRefundController extends BaseController {

    @Autowired
    private UpayRefundService upayRefundService;

    /**
     * 发起退款-接口
     *
     * @param request
     * @param param
     * @return
     */
    @RequestMapping("e1106")
    public ResultVO<Map<String, Object>> refund(HttpServletRequest request, UpayOrderLog param) {
        ResultVO<Map<String, Object>> resultData = new ResultVO<>();
        try {
            if (isBlank(param.getMiniId())) {
                resultData.setCode(500);
                resultData.setMessage("机构id不能为空");
                return resultData;
            }
            if (isBlank(param.getId())) {
                resultData.setCode(500);
                resultData.setMessage("订单id不能为空");
                return resultData;
            }
            if (isBlank(param.getOrdAmt())) {
                return ResultVO.error(501, StaticStr.REFUND_AMOUNT_CANNOT_BE_EMPTY);
            } else if (param.getOrdAmt().compareTo(BigDecimal.ZERO) <= 0) {
                return ResultVO.error(501, StaticStr.REFUND_AMOUNT_IS_ILLEGAL);
            }
            // 组装退款参数
            UpayOrderLog entity = new UpayOrderLog();
            entity.setMiniId(param.getMiniId());
            entity.setId(param.getId());
            entity.setOrdAmt(param.getOrdAmt());
            // 发起退款
            upayRefundService.doRefund(resultData, entity);
        } catch (MyException e) {
            return ResultVO.error(501, e.getMessage());
        } catch (Exception e) {
            MyException.print(log, e);
            resultData.setCode(501);
            resultData.setMessage(e.getMessage());
        }
        return resultData;
    }

}
