package com.fast.controller.upay.google;

import com.alibaba.fastjson.JSONObject;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.service.upay.google.GooglePayService;
import com.fast.utils.GooglePlayUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.upay.google.GooglePayVerifyDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Google Pay 支付控制器，处理与 Google Pay 相关的请求
 * Created by <PERSON> on 2025/05/09.
 */
@RestController
@RequestMapping("/upay/googlePay")
public class GooglePayController extends BaseController {

    @Autowired
    private GooglePayService googlePayService;

    @Autowired
    private GooglePlayUtil googlePlayUtil;

    @ApiName(value = "GP-验证一次性商品购买", folder = {StaticFolder.FOLDER_PAY_ONLINE})
    @ApiParamsIn({
            "purchaseToken:1:str:购买令牌",
            "orderId:1:str:谷歌订单ID",
            "termOrdId:1:str:平台订单ID",
            "purchaseTime:1:int:购买时间（时间戳）",
            "productId:1:str:商品ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/verifyProductPurchase")
    public ResultVO<JSONObject> verifyProductPurchase(@Validated GooglePayVerifyDTO dto) {
        String orderId = decode(dto.getTermOrdId());
        if (StrUtil.isBlank(orderId)) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        dto.setTermOrdId(orderId);
        googlePayService.verifyProductPurchase(dto);
        return ResultVO.success();
    }

    @ApiName(value = "GP-验证订阅商品购买", folder = {StaticFolder.FOLDER_PAY_ONLINE})
    @ApiParamsIn({
            "purchaseToken:1:str:购买令牌",
            "orderId:1:str:谷歌订单ID",
            "termOrdId:1:str:平台订单ID",
            "purchaseTime:1:int:购买时间（时间戳）",
            "subscriptionId:1:str:订阅ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/verifySubscriptionPurchase")
    public ResultVO<JSONObject> verifySubscriptionPurchase(@Validated GooglePayVerifyDTO dto) {
        Long orderId = decodeLong(dto.getTermOrdId());
        if (orderId == null) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        dto.setTermOrdId(String.valueOf(orderId));
        googlePayService.verifySubscriptionPurchase(dto);
        return ResultVO.success();
    }

    // /**
    //  * 处理Google Play实时开发者通知，此端点需要配置在Google Play Console中
    //  *
    //  * @param request HTTP请求
    //  * @return 处理结果
    //  */
    // @PostMapping("/notifications")
    // public String handleRealTimeNotification(HttpServletRequest request) {
    //     try {
    //         // 读取请求体
    //         StringBuilder buffer = new StringBuilder();
    //         BufferedReader reader = request.getReader();
    //         String line;
    //         while ((line = reader.readLine()) != null) {
    //             buffer.append(line);
    //         }
    //         String notificationData = buffer.toString();
    //         log.info("Google Play通知数据: {}", notificationData);
    //         // 处理通知
    //         return googlePayService.handleRealTimeNotification(notificationData);
    //     } catch (IOException e) {
    //         log.error("读取Google Play通知数据失败: {}", ExceptionUtils.getStackTrace(e));
    //         return "ERROR";
    //     }
    // }

    @PostMapping("/getProductPurchase")
    public ResultVO getProductPurchase(String pid, String token) {
        if (StrUtil.isBlank(pid) || StrUtil.isBlank(token)) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        return ResultVO.success(googlePlayUtil.getProductPurchase(pid, token));
    }

    @PostMapping("/getInAppProduct")
    public ResultVO getInAppProduct(String pid) {
        if (StrUtil.isBlank(pid)) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        return ResultVO.success(googlePlayUtil.getInAppProduct(pid));
    }

    @PostMapping("/getOrder")
    public ResultVO getOrder(String orderId) {
        if (StrUtil.isBlank(orderId)) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        return ResultVO.success(googlePlayUtil.getOrder(orderId));
    }

}