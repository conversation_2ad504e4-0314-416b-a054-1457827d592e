package com.fast.controller.upay.apple;

import com.alibaba.fastjson.JSONObject;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.service.upay.apple.ApplePayService;
import com.fast.utils.ApplePayUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.upay.apple.ApplePayVerifyDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Apple Pay 支付控制器，处理与 Apple Pay 相关的请求
 * Created by Song on 2025/05/09.
 */
@RestController
@RequestMapping("/upay/applePay")
public class ApplePayController extends BaseController {

    @Autowired
    private ApplePayService applePayService;

    @Autowired
    private ApplePayUtil applePayUtil;

    @ApiName(value = "Apple-验证一次性商品购买", folder = {StaticFolder.FOLDER_PAY_ONLINE})
    @ApiParamsIn({
            "receiptData:1:str:购买凭证",
            "orderId:1:str:苹果订单ID",
            "encryptionId:1:str:平台订单ID（加密）",
            "productId:1:str:商品ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/verifyProductPurchase")
    public ResultVO<JSONObject> verifyProductPurchase(@Validated ApplePayVerifyDTO dto) {
        String orderId = decode(dto.getEncryptionId());
        if (StrUtil.isBlank(orderId)) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        dto.setTermOrdId(orderId);
        applePayService.verifyProductPurchase(dto);
        return ResultVO.success();
    }

    // @ApiName(value = "Apple-验证订阅商品购买", folder = {StaticFolder.FOLDER_PAY_ONLINE})
    // @ApiParamsIn({
    //         "purchaseToken:1:str:购买令牌",
    //         "orderId:1:str:苹果订单ID",
    //         "encryptionId:1:str:平台订单ID",
    //         "purchaseTime:1:int:购买时间（时间戳）",
    //         "subscriptionId:1:str:订阅ID",
    // })
    // @ApiParamsOut({
    //         "status:ok",
    // })
    // @PostMapping("/verifySubscriptionPurchase")
    // public ResultVO<JSONObject> verifySubscriptionPurchase(@Validated ApplePayVerifyDTO dto) {
    //     Long orderId = decodeLong(dto.getencryptionId());
    //     if (orderId == null) {
    //         return ResultVO.error(StaticStr.ILLEGAL_PARAM);
    //     }
    //     dto.setencryptionId(String.valueOf(orderId));
    //     applePayService.verifySubscriptionPurchase(dto);
    //     return ResultVO.success();
    // }

    // /**
    //  * 处理Apple Play实时开发者通知，此端点需要配置在Apple Play Console中
    //  *
    //  * @param request HTTP请求
    //  * @return 处理结果
    //  */
    // @PostMapping("/notifications")
    // public String handleRealTimeNotification(HttpServletRequest request) {
    //     try {
    //         // 读取请求体
    //         StringBuilder buffer = new StringBuilder();
    //         BufferedReader reader = request.getReader();
    //         String line;
    //         while ((line = reader.readLine()) != null) {
    //             buffer.append(line);
    //         }
    //         String notificationData = buffer.toString();
    //         log.info("Apple Play通知数据: {}", notificationData);
    //         // 处理通知
    //         return applePayService.handleRealTimeNotification(notificationData);
    //     } catch (IOException e) {
    //         log.error("读取Apple Play通知数据失败: {}", ExceptionUtils.getStackTrace(e));
    //         return "ERROR";
    //     }
    // }

    // @PostMapping("/getProductPurchase")
    // public ResultVO getProductPurchase(String pid, String token) {
    //     if (StrUtil.isBlank(pid) || StrUtil.isBlank(token)) {
    //         return ResultVO.error(StaticStr.ILLEGAL_PARAM);
    //     }
    //     return ResultVO.success(applePlayUtil.getProductPurchase(pid, token));
    // }
    //
    // @PostMapping("/getInAppProduct")
    // public ResultVO getInAppProduct(String pid) {
    //     if (StrUtil.isBlank(pid)) {
    //         return ResultVO.error(StaticStr.ILLEGAL_PARAM);
    //     }
    //     return ResultVO.success(applePlayUtil.getInAppProduct(pid));
    // }
    //
    // @PostMapping("/getOrder")
    // public ResultVO getOrder(String orderId) {
    //     if (StrUtil.isBlank(orderId)) {
    //         return ResultVO.error(StaticStr.ILLEGAL_PARAM);
    //     }
    //     return ResultVO.success(applePlayUtil.getOrder(orderId));
    // }

}