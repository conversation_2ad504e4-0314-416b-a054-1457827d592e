/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberPO;
import com.fast.po.promote.FastMemberLinkPO;
import com.fast.service.member.FastMemberService;
import com.fast.service.promote.FastMemberLinkService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberLink")
public class FastMemberLinkController extends BaseController {

    @Autowired
    private FastMemberLinkService memberLinkService;
    @Autowired
    private FastMemberService memberService;

    @ApiName(value = "小程序-用户链接-查询列表", folder = {"小程序/用户链接"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberLinkPO params, PageVO pageVO) {
        return memberLinkService.queryPageList(params, pageVO);
    }

    @ApiName(value = "小程序-用户链接-查询单个详情", folder = {"小程序/用户链接"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberLinkPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberLinkPO fastMemberLink = memberLinkService.queryById(params);
        return ResultVO.success(fastMemberLink);
    }

    @ApiName(value = "小程序-用户链接-添加", folder = {"小程序/用户链接"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastMemberLinkPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = memberLinkService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "小程序-用户链接-更新", folder = {"小程序/用户链接"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastMemberLinkPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = memberLinkService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "小程序-注册用户-刷新用户链接", folder = {"小程序用户"})
    @ApiParamsIn({
            "注-用户通过点击推广链接打开小程序，成功注册登录后，调用该方法刷新注册链接。:1:str:调用 FastMemberLinkService fastMemberLinkService.updateLinkV1(FastMemberLinkPO params) params 设置以下属性信息",
            "memberId:1:int:用户Id",
            "ip:1:str:用户ip",
            "ua:1:str:用户user-agent",
            "firstLinkId:1:int:首次推广链接id",
            "firstLinkTime:1:date:首次推广链接时间",
            "lastLinkId:1:int:最近推广链接id =首次推广链接id",
            "lastLinkTime:1:date:最近推广链接时间 =首次推广链接时间",
            "creatorId:1:int:用户Id"
    })
    @ApiParamsOut({
            ""
    })
    @RequestMapping(value = "/updateLinkV1", method = {RequestMethod.POST})
    public ResultVO<?> updateLinkV1(HttpServletRequest request, FastMemberLinkPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberPO memberPO = memberService.queryById(params.getMemberId());
        return memberLinkService.updateLinkV1(params, memberPO, 1, "", "");
    }

}
