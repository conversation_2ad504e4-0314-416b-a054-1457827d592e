/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.promote.FastActivityClickPO;
import com.fast.po.promote.FastActivityPO;
import com.fast.service.promote.FastActivityClickService;
import com.fast.service.promote.FastActivityService;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 促销活动
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastPromote")
public class FastActivityController extends BaseController {

    @Autowired
    private FastActivityService activityService;
    @Autowired
    private FastActivityClickService activityClickService;

    /**
     * 促销活动-个详情
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "促销活动-查询单个详情", folder = {"促销活动"})
    @ApiParamsIn(value = {
            "encryptionId:1:str:活动加密id(优先)",
            "id:1:int:活动id",
    })
    @ApiParamsOut(value = {
            "encryptionId:活动加密id",
            "encryptionModelDetailId:模板detail加密id",
            "title:标题",
            "content:内容",
            "startTime:开始时间(yyyy-MM-dd HH:mm:ss)",
            "endTime:结束时间(yyyy-MM-dd HH:mm:ss)",
            "rechargeType:充值档位::1=充值金币;2=充值VIP",
            "activityState:活动状态::1=未开始;2=进行中;3=已结束",
            "moneyRecharge:充值金额",
            "modelGearId:充值档位id",
            "limitType:限购类型::1=按活动;2=按天",
            "limitNum:限购次数(0=不限制)",
            "templateId:模板id-(1,2,3)",
            "state:是否开启::1=是;0=否",
    })
    @RequestMapping(value = "/getPromoteActivityDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastActivityPO params) {
        Integer id = null;
        if (notBlank(params.getEncryptionId())) {
            id = decodeInt(params.getEncryptionId());
        }
        if (id == null) {
            id = params.getId();
        }
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastActivityPO fastPromoteActivity = activityService.queryInfoByRedis(params);
        return ResultVO.success(fastPromoteActivity);
    }

    /**
     * 促销活动-点击
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "促销活动-点击", folder = {"促销活动"})
    @ApiParamsIn(value = {
            "encryptionId:1:str:活动加密id",
    })
    @ApiParamsOut(value = {"success"})
    @RequestMapping(value = "/click", method = {RequestMethod.POST})
    public ResultVO<?> click(HttpServletRequest request, FastActivityClickPO params) {
        Integer activityId = null;
        if (notBlank(params.getEncryptionId())) {
            activityId = decodeInt(params.getEncryptionId());
        }
        if (activityId == null) {
            activityId = params.getId();
        }
        if (activityId == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setMemberId(sessionVO.getMemberId());
        params.setActivityId(activityId);
        MethodVO methodVO = activityClickService.click(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
