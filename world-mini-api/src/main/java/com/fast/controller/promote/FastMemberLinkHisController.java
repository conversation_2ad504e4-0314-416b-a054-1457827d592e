/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.promote.FastMemberLinkHisPO;
import com.fast.service.promote.FastMemberLinkHisService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberLinkHis")
public class FastMemberLinkHisController extends BaseController {

    @Autowired
    private FastMemberLinkHisService fastMemberLinkHisService;

    @ApiName(value = "小程序-用户链接历史-查询列表", folder = {"小程序/用户链接历史"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberLinkHisPO params, PageVO pageVO) {
        return fastMemberLinkHisService.queryPageList(params, pageVO);
    }

    @ApiName(value = "小程序-用户链接历史-查询单个详情", folder = {"小程序/用户链接历史"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberLinkHisPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberLinkHisPO fastMemberLinkHis = fastMemberLinkHisService.queryById(params);
        return ResultVO.success(fastMemberLinkHis);
    }

    @ApiName(value = "小程序-用户链接历史-添加", folder = {"小程序/用户链接历史"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastMemberLinkHisPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberLinkHisService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "小程序-用户链接历史-更新", folder = {"小程序/用户链接历史"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastMemberLinkHisPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberLinkHisService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
