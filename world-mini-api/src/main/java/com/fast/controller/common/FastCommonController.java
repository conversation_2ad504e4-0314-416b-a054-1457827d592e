/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberMapper;
import com.fast.po.member.FastMemberPO;
import com.fast.po.setting.FastSettingSystemPO;
import com.fast.service.common.MailService;
import com.fast.service.setting.FastSettingSystemService;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.sms.SmsSendVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/fastCommon")
public class FastCommonController extends BaseController {
    @Autowired
    private FastMemberMapper fastMemberMapper;
    @Autowired
    private FastSettingSystemService fastSettingSystemService;
    @Autowired
    private MailService mailService;

    @RequestMapping(value = "/nologin/testMember", method = {RequestMethod.POST})
    public ResultVO<?> testMember(HttpServletRequest request, Long memberId) {
        if (memberId == null) {
            memberId = 1L;
        }
        FastMemberPO fmParam = new FastMemberPO();
        fmParam.setId(memberId);
        FastMemberPO fmPO = fastMemberMapper.queryOne(fmParam);
        return ResultVO.success(fmPO);
    }

    @RequestMapping(value = "/nologin/getEnvDomain")
    public ResultVO<?> getEnvDomain(HttpServletRequest request) {
        FastSettingSystemPO setting = fastSettingSystemService.queryByCode("app_api_domain");
        if (setting == null) {
            return ResultVO.error(StaticStr.SYSTEM_CONFIG_MISSING);
        }
        ResultVO<String> success = ResultVO.success();
        success.setResults(setting.getContent());
        return success;
    }

    @ApiName(value = "发送验证码", folder = {"common"})
    @ApiParamsIn({
            "type:1:int:发送类型：1、短信；2、邮件",
            "emails:1:str:邮箱",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @RequestMapping(value = "/sendVerifyCode")
    public ResultVO<?> sendVerifyCode(HttpServletRequest request, SmsSendVO smsSendVO) {
        if (smsSendVO.getType() == null) {
            return ResultVO.error(StaticStr.SEND_TYPE_CANNOT_BE_EMPTY);
        }
        if (smsSendVO.getType() == 1) {
            if (StrUtil.isEmpty(smsSendVO.getPhoneNumbers())) {
                return ResultVO.error(StaticStr.PHONE_CANNOT_BE_EMPTY);
            }
        }
        if (smsSendVO.getType() == 2) {
            if (StrUtil.isEmpty(smsSendVO.getEmails())) {
                return ResultVO.error(StaticStr.EMAIL_CANNOT_BE_EMPTY);
            }
            if (!smsSendVO.getEmails().matches(StaticVar.EMAIL_REGEX)) {
                return ResultVO.error(StaticStr.EMAIL_INCORRECTLY_FORMATTED);
            }
            mailService.sendVerifyCodeMail(smsSendVO.getEmails());
        }
        return ResultVO.success();
    }

}
