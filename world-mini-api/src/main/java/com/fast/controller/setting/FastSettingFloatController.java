/*
 * Powered By fast.up
 */
package com.fast.controller.setting;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticVar;
import com.fast.po.setting.FastSettingFloatPO;
import com.fast.service.setting.FastSettingFloatService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 浮窗配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastSetting")
public class FastSettingFloatController extends BaseController {

    @Autowired
    private FastSettingFloatService settingFloatService;

    /**
     * 浮窗配置-查询列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "浮窗配置-查询列表", folder = {"运营配置"})
    @ApiParamsIn(value = {
            "state:1:int:1=是;0=否",
    })
    @ApiParamsOut(value = {
            "encryptionId:浮窗加密id",
            "state:是否启用::1=是;0=否",
            "floatName:浮窗名称",
            "floatLocation:浮窗位置::1=追剧页（关注类）;2=页面顶部（送K币）;3=小剧场（送福利）",
            "floatText:浮窗文案",
            "jumpType:跳转形式::1=微信链接;2=图文;3=小程序页面;4=活动;5=H5链接",
            "jumpContent:跳转内容(跳转形式为1,3,4的时候配置)",
            "encryptionActivityId:活动加密id",
            "jumpArticle:跳转图文ID-专属",
            "articleTitle:浮窗图文标题",
            "articleTag:1:浮窗图文标签",
            "articleContent:浮窗图文内容说明",
            "articleCode:浮窗图文二维码url",
            "articleRemark:浮窗图文说明",
            "titleBottom:浮窗图文底部标题",
    })
    @RequestMapping(value = "/getFloatSettingList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastSettingFloatPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setDelFlag(StaticVar.NO);
        params.setState(1);
        return settingFloatService.queryList4Member(params, pageVO);
    }
}
