/*
 * Powered By fast.up
 */
package com.fast.controller.setting;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.po.member.FastMemberUnlockRewardLogPO;
import com.fast.service.setting.FastMiniRewardAdSettingService;
import com.fast.service.unlock.FastMemberUnlockRewardLogService;
import com.fast.utils.redis.JedisLock;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 抖音小程序 广告弹出配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniRewardAdSetting")
public class FastMiniRewardAdSettingController extends BaseController {

    @Autowired
    private FastMiniRewardAdSettingService miniRewardAdSettingService;
    @Autowired
    private FastMemberUnlockRewardLogService memberUnlockRewardLogService;

    @ApiName(value = "广告解锁-是否弹出广告", folder = {StaticFolder.FOLDER_TIKTOK_MINI})
    @PostMapping("/whetherShowRewardAd")
    @ApiParamsIn(value = {
            "header传token"
    })
    @ApiParamsOut(value = {
            "whetherShowRewardAd:是否弹出 true弹出 false不弹",
            "adSequeue:弹出顺序 1优先于充值面板弹出 2支付挽留弹窗弹出完毕后",
            "perUnlockAmount:每集解锁次数",
            "msg:提示信息"
    })
    public ResultVO<?> whetherShowRewardAd(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        return miniRewardAdSettingService.whetherShowRewardAd(sessionVO);
    }

    @ApiName(value = "广告解锁-写入广告观看记录-废弃", folder = {StaticFolder.FOLDER_TIKTOK_MINI})
    @PostMapping("/insertRewardAdLog")
    @ApiParamsIn(value = {
            "dramaId:1:int:短剧id",
            "miniId:1:int:小程序id",
            "adUnitId:1:str:广告位Id",
            "enterType:1:int:抖音小程序用户进入类型 1、兜底;2、投放;3、运营;4、分享;5、挂载",
            "startSeriesNum:1:int:若该剧支持跳级解锁，解锁改集；若不支持，按序解锁后一集",
            "watchAdStartId:1:int:开始观看的id",
    })
    @ApiParamsOut(value = {"success"})
    public ResultVO<?> insertRewardAdLog(HttpServletRequest request, FastMemberUnlockRewardLogPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getUnlockType() == null) {
            params.setUnlockType(1);
        }
        params.setLinkId(sessionVO.getLinkId());
        params.setMemberId(sessionVO.getMemberId());
        if (params.getDramaId() == null) {
            return ResultVO.error(StaticStr.DRAMAID_CANNOT_BE_EMPTY);
        }
        if (params.getMiniId() == null) {
            return ResultVO.error(StaticStr.APPID_CANNOT_BE_EMPTY);
        }
        if (isEmpty(params.getAdUnitId())) {
            return ResultVO.error(StaticStr.ADVID_CANNOT_BE_EMPTY);
        }
        if (params.getEnterType() == null) {
            return ResultVO.error(StaticStr.ENTERTYPE_CANNOT_BE_EMPTY);
        }
        if (params.getStartSeriesNum() == null) {
            return ResultVO.error(StaticStr.DRAMASERIES_NUM_CANNOT_BE_EMPTY);
        }
        final JedisLock lock = new JedisLock(StaticVar.LOCK_INSERT_WATCH_AD_END + params.getMiniId() + ":" + params.getMemberId() + ":" + params.getDramaId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            return memberUnlockRewardLogService.insertRewardAdLog(sessionVO, params);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

}
