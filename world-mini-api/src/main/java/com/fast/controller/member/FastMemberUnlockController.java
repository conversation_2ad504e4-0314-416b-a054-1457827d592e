/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.alibaba.fastjson.JSONObject;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.po.member.FastMemberAdmobLogPO;
import com.fast.po.member.FastMemberOrderDramaPO;
import com.fast.po.member.FastMemberUnlockDramaPO;
import com.fast.po.member.FastMemberUnlockRewardLogPO;
import com.fast.po.unlock.FastMemberUnlockStartLogPO;
import com.fast.service.drama.FastDramaService;
import com.fast.service.member.FastMemberAccountService;
import com.fast.service.member.FastMemberAdmobLogService;
import com.fast.service.member.FastMemberOrderDramaService;
import com.fast.service.member.FastMemberVipOnlineService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.unlock.FastMemberUnlockDramaService;
import com.fast.service.unlock.FastMemberUnlockRewardLogService;
import com.fast.service.unlock.FastMemberUnlockStartLogService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.drama.FastDramaVO;
import com.fast.vo.fee.FastFeeRuleVO;
import com.fast.vo.member.MemberAccountVO;
import com.fast.vo.promote.FastLinkVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberUnlockDrama")
public class FastMemberUnlockController extends BaseController {

    @Autowired
    private FastMemberUnlockDramaService memberUnlockDramaService;
    @Autowired
    private FastMemberAccountService memberAccountService;
    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastDramaService fastDramaService;
    @Autowired
    private FastMemberOrderDramaService fastMemberOrderDramaService;
    @Autowired
    private FastMemberUnlockRewardLogService memberUnlockRewardLogService;
    @Autowired
    private FastMemberUnlockStartLogService unlockStartLogService;
    @Autowired
    private FastMemberVipOnlineService fastMemberVipOnlineService;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastMemberAdmobLogService fastMemberAdmobLogService;

    @ApiName(value = "解锁剧集-查询列表", folder = {"用户"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberUnlockDramaPO params, PageVO pageVO) {
        return memberUnlockDramaService.queryPageList(params, pageVO);
    }

    @ApiName(value = "解锁剧集-查询单个详情", folder = {"用户"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberUnlockDramaPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        // SessionVO sessionVO = getSessionVO(request);
        FastMemberUnlockDramaPO fastMemberUnlockDrama = memberUnlockDramaService.queryById(params);
        return ResultVO.success(fastMemberUnlockDrama);
    }

    /**
     * 解锁剧集-解锁
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "解锁剧集-解锁", folder = {"用户"})
    @ApiParamsIn({
            "encryptionId:1:str:短剧加密id",
            "seriesNum:1:int:短剧剧集号",
            "type:1:int:解锁方式（0、IAP；1、IAA）",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @RequestMapping(value = "/unlockDrama", method = {RequestMethod.POST})
    public ResultVO<?> unlockDrama(HttpServletRequest request, FastMemberUnlockDramaPO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 解锁剧集的一把锁(同一用户不能同时操作解锁)
        final JedisLock lock = new JedisLock(StaticVar.UNLOCK_DRAMA_LOCK + sessionVO.getMemberId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            // 校验剧集
            if (params.getSeriesNum() == null || params.getSeriesNum() <= 0 || params.getSeriesNum() >= 65535) {
                return ResultVO.error(StaticStr.DRAMASERIES_IS_ILLEGAL);
            }
            // 校验短剧ID
            if (isEmpty(params.getEncryptionId())) {
                return ResultVO.error(StaticStr.DRAMAID_CANNOT_BE_EMPTY);
            }
            Integer dramaId = decodeInt(params.getEncryptionId());
            if (dramaId == null) {
                return ResultVO.error(StaticStr.DRAMAID_CANNOT_BE_EMPTY);
            }
            // 校验短剧是否存在
            FastDramaVO drama = dramaService.queryInfoByRedis(dramaId);
            if (drama == null) {
                return ResultVO.error(StaticStr.DRAMA_IS_ILLEGAL);
            }
            // 校验短剧规则
            FastFeeRuleVO feeRule = fastDramaService.getFeeRule(sessionVO.getLinkId(), sessionVO.getOfficialId(), dramaId);
            if (feeRule == null) {
                return ResultVO.error(StaticStr.RULE_NOT_CONFIGURED);
            }
            if (params.getSeriesNum() < feeRule.getStartNum()) {
                log.info("用户 {} 解锁短剧 {} 的第 {} 集时，检测到当前剧集早于规则配置的起始收费剧集{}", sessionVO.getMemberId(), dramaId, params.getSeriesNum(), feeRule.getStartNum());
                return ResultVO.error(StaticStr.DRAMASERIES_NOT_NEED_UNLOCK);
            }
            // 校验是否重复解锁
            List<Integer> ids = memberUnlockDramaService.queryInfoByRedis(sessionVO.getMemberId(), dramaId);
            if (CollUtil.hasContent(ids) && ids.contains(params.getSeriesNum())) {
                log.info("用户 {} 解锁短剧 {} 的第 {} 集时，检测到当前剧集已经解锁", sessionVO.getMemberId(), dramaId, params.getSeriesNum());
                return ResultVO.error(StaticStr.DRAMASERIES_CANNOT_REPEAT_UNLOCK);
            }
            // 校验跳集解锁
            int linkSkipSeries = 0;
            FastLinkVO linkVO = fastLinkService.queryInfoByRedis(sessionVO.getLinkId());
            if (linkVO != null && linkVO.getSkipSeries() == 1) {
                linkSkipSeries = 1;
            }
            if (feeRule.getSkipSeries() == 0 && linkSkipSeries == 0) {
                if (CollUtil.isEmpty(ids) && params.getSeriesNum() != feeRule.getStartNum().intValue()) {
                    log.info("用户 {} 解锁短剧 {} 的第 {} 集时，与规则配置的起始收费剧集{}不一致", sessionVO.getMemberId(), dramaId, params.getSeriesNum(), feeRule.getStartNum());
                    return ResultVO.error(StaticStr.DRAMASERIES_NOT_SUPPORT_SKIP);
                }
                if (CollUtil.hasContent(ids)) {
                    // 当前解锁的前一集必须是已解锁或免费剧集
                    int seriesNum = params.getSeriesNum() - 1;
                    if (seriesNum >= feeRule.getStartNum() && !ids.contains(seriesNum)) {
                        log.info("用户 {} 解锁短剧 {} 的第 {} 集时，前一集还未解锁", sessionVO.getMemberId(), dramaId, params.getSeriesNum());
                        return ResultVO.error(StaticStr.DRAMASERIES_NOT_SUPPORT_SKIP);
                    }
                }
            }

            log.info("用户 {} 解锁短剧 {} 的第 {} 集，解锁方式: {}", sessionVO.getMemberId(), dramaId, params.getSeriesNum(), params.getType());

            params.setMemberId(sessionVO.getMemberId());
            params.setDramaId(dramaId);

            // IAA
            if (Objects.equals(params.getType(), 1)) {
                // 广告观看记录
                FastMemberAdmobLogPO logQuery = new FastMemberAdmobLogPO();
                logQuery.setMemberId(sessionVO.getMemberId());
                logQuery.setDramaId(dramaId);
                logQuery.setSeriesNum(params.getSeriesNum());
                logQuery.setState(0);
                FastMemberAdmobLogPO admobLog = fastMemberAdmobLogService.queryOne(logQuery);
                if (admobLog == null) {
                    this.log.info("用户 {} 解锁短剧 {} 的第 {} 集时，未找到有效的admob记录，不予解锁", sessionVO.getMemberId(), dramaId, params.getSeriesNum());
                    return ResultVO.error(StaticStr.UNLOCK_FAILED);
                }
                // 解锁
                MethodVO methodVO = memberUnlockDramaService.unlockDrama(sessionVO, params, null, admobLog);
                return ResultVO.fromMethodVO1(methodVO);
            }
            // IAP
            else {
                // 判断是否是VIP用户
                MemberAccountVO account = memberAccountService.queryInfoByRedis(sessionVO.getMemberId());
                if (account == null) {
                    return ResultVO.error(StaticStr.USER_ACCOUNT_NOT_EXIST);
                }
                if (account.getDeadTime().compareTo(DateUtil.getNowDate()) > 0) {
                    // 判断vip类型，上线vip
                    if (fastMemberVipOnlineService.checkOnline(sessionVO, dramaId)) {
                        log.info("用户 {} 解锁短剧 {} 的第 {} 集时，判断为VIP，无需校验金币校验", sessionVO.getMemberId(), dramaId, params.getSeriesNum());
                        return ResultVO.success(StaticStr.VIP_NOT_NEED_UNLOCK);
                    }
                }
                // 判断剧卡
                FastMemberOrderDramaPO odParam = new FastMemberOrderDramaPO();
                odParam.setDramaId(dramaId);
                odParam.setMemberId(sessionVO.getMemberId());
                String buyDramaStr = fastMemberOrderDramaService.queryOrderDramaRedis(odParam);
                if (StaticVar.YES_STR.equals(buyDramaStr)) {
                    log.info("用户 {} 解锁短剧 {} 的第 {} 集时，判断有剧卡，无需校验金币校验", sessionVO.getMemberId(), dramaId, params.getSeriesNum());
                    return ResultVO.success(StaticStr.CARD_OPEN_NOT_NEED_UNLOCK);
                }
                // 解锁
                MethodVO methodVO = memberUnlockDramaService.unlockDrama(sessionVO, params, feeRule, null);
                return ResultVO.fromMethodVO1(methodVO);
            }
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    @ApiName(value = "广告解锁-开始观看广告记录日志", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn(value = {
            "dramaId:1:int:短剧id",
            "miniId:1:int:小程序id",
            "adUnitId:1:str:广告位Id",
            "state:1:int:0=准备看(广告未加载);1=开始看(广告已加载成功);",
    })
    @ApiParamsOut(value = {"watchAdStartId:开始观看的id"})
    @PostMapping("/insertWatchAdStart")
    public ResultVO<?> insertWatchAdStart(HttpServletRequest request, FastMemberUnlockStartLogPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setLinkId(sessionVO.getLinkId());
        params.setMemberId(sessionVO.getMemberId());
        if (params.getState() == null || params.getState() == 0) {
            if (params.getDramaId() == null) {
                return ResultVO.error(StaticStr.DRAMASERIES_ID_CANNOT_BE_EMPTY);
            }
            if (params.getMiniId() == null) {
                return ResultVO.error(StaticStr.APPID_CANNOT_BE_EMPTY);
            }
            if (isEmpty(params.getAdUnitId())) {
                return ResultVO.error(StaticStr.ADVID_CANNOT_BE_EMPTY);
            }
        }
        boolean isFirst = false;
        if (params.getState() == null) {
            params.setState(1);
            isFirst = true;
        } else if (params.getState() == 1) {
            if (params.getWatchAdStartId() == null || params.getWatchAdStartId() <= 0) {
                return ResultVO.error(StaticStr.ILLEGAL_PARAM);
            }
        } else if (params.getState() == 0) {
            isFirst = true;
        }
        if (!StrUtil.equalsAny(params.getState(), 0, 1)) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        if (isFirst) {
            // 投放
            params.setEnterType(2);
            // 此处的锁不需要释放,限制用户3秒内不能重复提交
            String lockKey = StaticVar.LOCK_INSERT_WATCH_AD_START + params.getMiniId() + "_" + params.getMemberId() + "_" + params.getDramaId();
            final JedisLock lock = new JedisLock(lockKey, 100, 3500);
            try {
                String key = StaticVar.INSERT_WATCH_AD_START + params.getMiniId() + "_" + params.getMemberId() + "_" + params.getDramaId();
                if (!lock.lock()) {
                    String val = RedisUtil.get(key);
                    if (notEmpty(val)) {
                        return ResultVO.success(new JSONObject().fluentPut("watchAdStartId", val));
                    }
                    return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
                }
                ResultVO<?> vo = unlockStartLogService.insertWatchAdStart(params);
                if (biggerZero(params.getLinkId())) {
                    RedisUtil.set(key, params.getId().toString(), 5);
                }
                if (vo == null) {
                    return ResultVO.success(new JSONObject().fluentPut("watchAdStartId", 0));
                }
                return vo;
            } catch (Exception e) {
                log.error("error:", e);
                throw new MyException(e);
            }
        } else {
            // 更新state状态
            return unlockStartLogService.updateWatchAdState(params);
        }
    }

    @ApiName(value = "广告解锁-结束观看广告记录日志", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn(value = {
            "watchAdStartId:1:int:开始观看的id",
            "playSeconds:1:int:广告播放秒数(整数)",
            "state:1:int:2=已看完全部广告;3=看完部分广告",
    })
    @ApiParamsOut(value = {"success"})
    @PostMapping("/closeWatchAd")
    public ResultVO<?> closeWatchAd(HttpServletRequest request, FastMemberUnlockStartLogPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setLinkId(sessionVO.getLinkId());
        params.setMemberId(sessionVO.getMemberId());
        if (params.getWatchAdStartId() == null || params.getWatchAdStartId() <= 0) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        if (params.getPlaySeconds() == null || params.getPlaySeconds() < 0) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        if (params.getPlaySeconds() > 32760) {
            params.setPlaySeconds(32760);
        }
        if (params.getState() == null) {
            params.setState(2);
        }
        final JedisLock lock = new JedisLock(StaticVar.LOCK_CLOSE_WATCH_AD + params.getWatchAdStartId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            return unlockStartLogService.closeWatchAd(params);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    @ApiName(value = "广告解锁-完成观看广告记录日志", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn(value = {
            "unlockType:1:int:1.下滑解锁 2.挽留解锁 3.渠道链接广告挽留",
            "dramaId:1:int:短剧id",
            "miniId:1:int:小程序id",
            "adUnitId:1:str:广告位Id",
            "enterType:1:int:抖音小程序用户进入类型 1、兜底;2、投放;3、运营;4、分享;5、挂载",
            "startSeriesNum:1:int:若该剧支持跳级解锁，解锁改集；若不支持，按序解锁后一集",
            "watchAdStartId:1:int:开始观看的id",
    })
    @ApiParamsOut(value = {"success"})
    @PostMapping("/insertRewardAdLog")
    public ResultVO<?> insertRewardAdLog(HttpServletRequest request, FastMemberUnlockRewardLogPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setLinkId(sessionVO.getLinkId());
        params.setMemberId(sessionVO.getMemberId());
        if (params.getUnlockType() == null) {
            params.setUnlockType(1);// 默认下滑解锁
        }
        if (params.getDramaId() == null) {
            return ResultVO.error(StaticStr.DRAMAID_CANNOT_BE_EMPTY);
        }
        if (params.getMiniId() == null) {
            return ResultVO.error(StaticStr.APPID_CANNOT_BE_EMPTY);
        }
        if (isEmpty(params.getAdUnitId())) {
            return ResultVO.error(StaticStr.ADVID_CANNOT_BE_EMPTY);
        }
        if (params.getEnterType() == null) {
            return ResultVO.error(StaticStr.ENTERTYPE_CANNOT_BE_EMPTY);
        }
        if (params.getStartSeriesNum() == null) {
            return ResultVO.error(StaticStr.DRAMASERIES_NUM_CANNOT_BE_EMPTY);
        }
        final JedisLock lock = new JedisLock(StaticVar.LOCK_INSERT_WATCH_AD_END + params.getMiniId() + ":" + params.getMemberId() + ":" + params.getDramaId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            return memberUnlockRewardLogService.insertRewardAdLog(sessionVO, params);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }
}
