/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.po.member.FastMemberOrderRechargeSdkBackPO;
import com.fast.service.member.FastMemberOrderRechargeSdkBackService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/member")
public class FastMemberOrderRechargeSdkBackController extends BaseController {

    @Autowired
    private FastMemberOrderRechargeSdkBackService fastMemberOrderRechargeSdkBackService;

    @ApiName(value = "member-查询列表", folder = {"member"})
    @PostMapping("/getFastMemberOrderRechargeSdkBackList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberOrderRechargeSdkBackPO params, PageVO pageVO) {
        return fastMemberOrderRechargeSdkBackService.queryPageList(params, pageVO);
    }

}
