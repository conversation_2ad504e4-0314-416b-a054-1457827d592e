/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.alibaba.fastjson.JSON;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberMessagePushPO;
import com.fast.po.member.FastMemberPO;
import com.fast.service.member.FastMemberMessagePushService;
import com.fast.service.member.FastMemberService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.google.firebase.messaging.AndroidConfig;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
public class FastMemberMessagePushController extends BaseController {

    @Autowired
    private FastMemberMessagePushService fastMemberMessagePushService;

    @Autowired
    private FastMemberService fastMemberService;

    @PostMapping("/fastMemberMessagePush/insert")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberMessagePushPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setPushTime(DateUtil.addDays(DateUtil.getNowDate(), 1));
        params.setMemberId(sessionVO.getMemberId());
        params.setState(0);
        if (params.getType() == null) {
            return ResultVO.error(StaticStr.SUBSCRIPTION_TYPE_CANNOT_BE_EMPTY);
        }
        MethodVO methodVO = fastMemberMessagePushService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @PostMapping("/fastMemberMessagePush/list")
    public ResultVO<?> list(HttpServletRequest request, FastMemberMessagePushPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getType() == null) {
            return ResultVO.error(StaticStr.SUBSCRIPTION_TYPE_CANNOT_BE_EMPTY);
        }
        params.setMemberId(sessionVO.getMemberId());
        List<FastMemberMessagePushPO> fastMemberMessagePushPOS = fastMemberMessagePushService.queryList(params);
        return ResultVO.success(fastMemberMessagePushPOS);
    }

    @ApiName(value = "推送数据消息", folder = {"member"})
    @ApiParamsIn({
            "Map格式入参",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/fastMemberMessagePush/sendData")
    public ResultVO<?> sendData(HttpServletRequest request, @RequestParam Map<String, String> params) {
        SessionVO sessionVO = getSessionVO(request);
        try {
            String pushToken = params.get("pushToken");
            if (StrUtil.isBlank(pushToken)) {
                FastMemberPO fastMemberPO = fastMemberService.queryById(sessionVO.getMemberId());
                if (fastMemberPO == null || StrUtil.isBlank(fastMemberPO.getPushToken())) {
                    return ResultVO.error(StaticStr.INVALID_PARAM);
                }
                pushToken = fastMemberPO.getPushToken();
            }
            log.info("推送移动端消息令牌: {}", pushToken);
            Message message = Message.builder()
                    .setToken(pushToken)
                    .setAndroidConfig(AndroidConfig.builder().setPriority(AndroidConfig.Priority.HIGH).build())
                    .putAllData(params)
                    .build();
            log.info("推送移动端消息入参: {}", JSON.toJSONString(params));
            String response = FirebaseMessaging.getInstance().send(message);
            log.info("推送移动端消息出参: {}", response);
            return ResultVO.success("success", response);
        } catch (Exception e) {
            log.error("推送移动端消息失败: {}", ExceptionUtils.getStackTrace(e));
            return ResultVO.error("推送移动端消息失败");
        }
    }

    @ApiName(value = "推送通知消息", folder = {"member"})
    @ApiParamsIn({
            "pushToken:0:str:令牌",
            "title:1:str:标题",
            "des:1:str:内容",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/fastMemberMessagePush/sendNotification")
    public ResultVO<?> sendNotification(HttpServletRequest request, @RequestParam Map<String, String> params) {
        SessionVO sessionVO = getSessionVO(request);
        try {
            String pushToken = params.get("pushToken");
            if (StrUtil.isBlank(pushToken)) {
                FastMemberPO fastMemberPO = fastMemberService.queryById(sessionVO.getMemberId());
                if (fastMemberPO == null || StrUtil.isBlank(fastMemberPO.getPushToken())) {
                    return ResultVO.error(StaticStr.INVALID_PARAM);
                }
                pushToken = fastMemberPO.getPushToken();
            }
            log.info("推送移动端消息令牌: {}", pushToken);
            Message message = Message.builder()
                    .setToken(pushToken)
                    .setNotification(Notification.builder()
                            .setTitle(params.get("title"))
                            .setBody(params.get("des"))
                            .build())
                    .build();
            log.info("推送移动端消息入参: {}", JSON.toJSONString(params));
            String response = FirebaseMessaging.getInstance().send(message);
            log.info("推送移动端消息出参: {}", response);
            return ResultVO.success("success", response);
        } catch (Exception e) {
            log.error("推送移动端消息失败: {}", ExceptionUtils.getStackTrace(e));
            return ResultVO.error("推送移动端消息失败");
        }
    }

}
