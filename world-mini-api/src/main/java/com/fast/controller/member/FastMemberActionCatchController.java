/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberActionCatchPO;
import com.fast.service.member.FastMemberActionCatchService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberActionCatch")
public class FastMemberActionCatchController extends BaseController {

    @Autowired
    private FastMemberActionCatchService fastMemberActionCatchService;

    @ApiName(value = "ios防封-查询列表", folder = {"ios防封"})
    @PostMapping("/getFastMemberActionCatchList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberActionCatchPO params, PageVO pageVO) {
        return fastMemberActionCatchService.queryPageList(params, pageVO);
    }

    @ApiName(value = "ios防封-查询单个详情", folder = {"ios防封"})
    @PostMapping("/getFastMemberActionCatchDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberActionCatchPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberActionCatchPO fastMemberActionCatch = fastMemberActionCatchService.queryById(params);
        return ResultVO.success(fastMemberActionCatch);
    }

    @ApiName(value = "ios防封-添加潜在风险行为", folder = {"ios防封"})
    @ApiParamsIn({
            "type:1:int:1截图，2非正常跳转到收费剧集"
    })
    @ApiParamsOut({"state:ok"})
    @PostMapping("/insertFastMemberActionCatch")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberActionCatchPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setMemberId(sessionVO.getMemberId());
        MethodVO methodVO = fastMemberActionCatchService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "ios防封-更新", folder = {"ios防封"})
    @PostMapping("/updateFastMemberActionCatch")
    public ResultVO<?> update(HttpServletRequest request, FastMemberActionCatchPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberActionCatchService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
