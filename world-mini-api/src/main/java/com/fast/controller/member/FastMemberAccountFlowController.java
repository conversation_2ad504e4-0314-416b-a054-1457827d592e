/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberAccountFlowPO;
import com.fast.service.member.FastMemberAccountFlowService;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberAccountFlow")
public class FastMemberAccountFlowController extends BaseController {

    @Autowired
    private FastMemberAccountFlowService fastMemberAccountFlowService;

    @ApiName(value = "账户流水-查询列表", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({
            "type:1:int:类型（1、入；2、出）"
    })
    @ApiParamsOut({
            "coin:金币",
            "remark:备注",
            "createTime:时间",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberAccountFlowPO params, PageVO pageVO) {
        if (params.getType() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.ILLEGAL_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setMemberId(sessionVO.getMemberId());
        return fastMemberAccountFlowService.queryPageList(params, pageVO);
    }

    @ApiName(value = "账户流水-查询单个详情", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberAccountFlowPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.ILLEGAL_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.ILLEGAL_PARAM);
        }
        params.setId(id);
        FastMemberAccountFlowPO fastMemberAccountFlow = fastMemberAccountFlowService.queryById(params);
        return ResultVO.success(fastMemberAccountFlow);
    }

}
