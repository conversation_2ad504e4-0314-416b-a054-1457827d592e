package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.po.member.*;
import com.fast.service.fee.FastFeeKeepBatchService;
import com.fast.service.fee.FastFeeModelService;
import com.fast.service.flock.FastFlockService;
import com.fast.service.member.*;
import com.fast.service.promote.FastAdGroundService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.fee.FeeKeepVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户
 */
@RestController
@RequestMapping("/member")
public class FastMemberController extends BaseController {

    @Autowired
    private FastMemberService memberService;
    @Autowired
    private FastMemberRecentService recentService;
    @Autowired
    private FastFeeModelService feeModelService;
    @Autowired
    private FastFlockService fastFlockService;
    @Autowired
    private FastAdGroundService adGroundService;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastFeeKeepBatchService feeKeepBatchService;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private FastMemberSettingService fastMemberSettingService;
    @Autowired
    private FastMemberYoungService fastMemberYoungService;
    @Autowired
    private FastMemberActiveService fastMemberActiveService;

    /**
     * 获取是否可以充值ios用户
     *
     * @param request
     * @return
     */
    @RequestMapping("/getCanRecharge")
    @ApiName(value = "ios防封-获取ios是否可以充值", folder = "ios防封")
    @ApiParamsIn({"dramaId:1:int:剧id"})
    @ApiParamsOut({"recharge:0否1是"})
    public ResultVO<?> getCanRecharge(HttpServletRequest request, Integer dramaId) {
        SessionVO sessionVO = getSessionVO(request);
        dramaId = dramaId == null ? 0 : dramaId;
        return memberService.getCanRecharge(sessionVO, dramaId);
    }


    /**
     * 查询用户信息
     *
     * @param request
     * @return
     */
    @ApiName(value = "查询用户信息", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({"no:0:int:只要登录就可以"})
    @ApiParamsOut({
            "miniId",
            "openid",
            "officialOpenid",
            "officialId",
            "memberName",
            "memberImg",
            "phone",
            "linkId:链接id",
            "coinRecharge:充值金币",
            "coinGive:赠送金币",
            "coinSign:签到金币",
            "coinRechargeRemain:金币充值剩余",
            "coinGiveRemain:赠送剩余总金币",
            "coinSignRemain:签到剩余总金币",
            "moneyRecharge:充值总金额",
            "moneyRefund:退款金额",
            "deadTime:用户到期时间",
            "coinDeadTime:K币到期时间",
            "officialName:公众号名称",
            "contractState;// 签约状态0否1是",
            "contractProduct:签约产品",
            "contractMoneyRecharge:签约金额",
            "contractSignWay:是否签约0否1是",
            "contractVipDate:续费日期"
    })
    @RequestMapping("/getUserInfo")
    public ResultVO<?> getUserInfo(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        return memberService.getUserInfoRedis(sessionVO);
    }

    /**
     * 查看-我的追剧/最近观看
     *
     * @param request
     * @return
     */
    @ApiName(value = "我的追剧/最近观看", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({"no:0:int:只要登录就可以"})
    @ApiParamsOut({
            "addictionList:追剧列表",
            "addictionList-dramaId:剧集id",
            "addictionList-updateState:连载状态,1=连载;0=完结",
            "addictionList-openState:显示状态: 1=显示;0=隐藏",
            "addictionList-dramaName:剧名",
            "addictionList-dramaCover:剧照",
            "addictionList-dramaIntroduce:剧集描述",
            "addictionList-seriesNumAll:总节数",
            "addictionList-seriesNumUpdate:更新节数",
            "addictionList-subscribe:是否订阅0否1是",
            "addictionList-addiction:是否追剧0否1是"
    })
    @RequestMapping("/getUserRelate")
    public ResultVO<?> getUserRelate(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        return memberService.getUserRelate(sessionVO);
    }

    /**
     * 添加追剧
     *
     * @param request
     * @param addiction
     * @return
     */
    @ApiName(value = "添加我的追剧", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({"dramaId:1:int:剧id"})
    @ApiParamsOut({"status:ok"})
    @RequestMapping("/addAddiction")
    public ResultVO<?> addAddiction(HttpServletRequest request, FastMemberAddictionPO addiction) {
        SessionVO sessionVO = getSessionVO(request);
        if (addiction.getDramaId() == null) {
            return ResultVO.error(StaticStr.DRAMAID_CANNOT_BE_EMPTY);
        }
        addiction.setMemberId(sessionVO.getMemberId());
        return memberService.addAddiction(addiction);
    }

    /**
     * 停止我的追剧
     *
     * @param request
     * @param addiction
     * @return
     */
    @ApiName(value = "停止我的追剧", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({"dramaId:1:int:剧id"})
    @ApiParamsOut({"status:ok"})
    @RequestMapping("/stopAddiction")
    public ResultVO<?> stopAddiction(HttpServletRequest request, FastMemberAddictionPO addiction) {
        SessionVO sessionVO = getSessionVO(request);
        if (addiction.getDramaId() == null) {
            return ResultVO.error(StaticStr.DRAMAID_CANNOT_BE_EMPTY);
        }
        addiction.setMemberId(sessionVO.getMemberId());
        return memberService.stopAddiction(addiction);
    }

    /**
     * 添加观看记录
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "添加观看记录", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({"dramaId:1:int:剧id", "seriesId:1:int:剧集id", "seriesNum:1:int:剧集号", "playSecondAccurate:1:int:播放秒数"})
    @ApiParamsOut({"status:ok"})
    @RequestMapping(value = "/addRecent", method = {RequestMethod.POST})
    public ResultVO<?> addRecent(HttpServletRequest request, FastMemberRecentPO params) {
        if (params.getDramaId() == null) {
            return ResultVO.error(StaticStr.DRAMAID_CANNOT_BE_EMPTY);
        }
        if (params.getSeriesId() == null) {
            return ResultVO.error(StaticStr.DRAMASERIES_ID_CANNOT_BE_EMPTY);
        }
        if (params.getSeriesNum() == null) {
            return ResultVO.error(StaticStr.DRAMASERIES_NUM_CANNOT_BE_EMPTY);
        }
        SessionVO sessionVO = getSessionVO(request);
        if (sessionVO.getContentType() == null) {
            sessionVO.setContentType(1);
        }
        params.setMemberId(sessionVO.getMemberId());
        if (params.getPlaySecondAccurate() != null) {
            if (sessionVO.getContentType() == 1 || sessionVO.getContentType() == 4) {
                if (params.getPlaySecondAccurate().compareTo(BigDecimal.ZERO) <= 0 || params.getPlaySecondAccurate().compareTo(BigDecimalVar.BD_999_99) > 0) {
                    log.error("播放秒数不合法:{};ID:{}", params.getPlaySecondAccurate(), sessionVO.getMemberId());
                    return ResultVO.success();
                }
            }
            if (sessionVO.getContentType() >= 2) {
                if (params.getPlaySecondAccurate().compareTo(BigDecimal.ZERO) <= 0 || params.getPlaySecondAccurate().compareTo(BigDecimalVar.BD_9999_99) > 0) {
                    log.error("播放秒数不合法:{};ID:{}", params.getPlaySecondAccurate(), sessionVO.getMemberId());
                    return ResultVO.success();
                }
            }
            if (params.getPlaySecondAccurate().compareTo(BigDecimal.ZERO) >= 0 && params.getPlaySecondAccurate().compareTo(BigDecimalVar.BD_0_1) < 0) {
                log.error("播放秒数不合法.:{};ID:{}", params.getPlaySecondAccurate(), sessionVO.getMemberId());
                return ResultVO.success();
            }
            // 播放秒数取整(取附近较大的整数)
            int playSecond = params.getPlaySecondAccurate().setScale(0, RoundingMode.CEILING).intValue();
            params.setPlaySecond(playSecond);
        }

        params.setContentType(sessionVO.getContentType());
        final JedisLock lock = new JedisLock(StaticVar.ADD_RECENT_LOG_LOCK + params.getMemberId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            params.setMemberId(sessionVO.getMemberId());
            params.setMiniId(sessionVO.getMiniId());
            params.setOfficialId(sessionVO.getOfficialId());
            params.setRetailId(sessionVO.getRetailId());
            params.setLinkId(sessionVO.getLinkId());
            params.setMiniType(sessionVO.getMiniType());
            MethodVO methodVO = recentService.addRecent(params);

            // 清除缓存
            String key = StaticVar.MEMBER_RECENT + params.getMemberId();
            RedisUtil.del(key);
            key = StaticVar.MEMBER_ADDICTION + params.getMemberId();
            RedisUtil.del(key);
            return ResultVO.fromMethodVO(methodVO);
        } catch (Exception e) {
            log.error("error:", e);
            throw new RuntimeException(e);
        } finally {
            lock.release();
        }
    }

    /**
     * 获取充值模板
     *
     * @param request
     * @param type
     * @return
     */
    @ApiName(value = "获取充值模板", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({
            "type:1:int:类型1=充值金币;2=充值VIP",
            "phoneOs:0:int:手机系统：1安卓，2爱疯",
    })
    @ApiParamsOut({
            "type:类型:1=充值金币;2=充值VIP",
            "title:卡名称",
            "moneyRecharge:充值金额",
            "coinRecharge:充值币",
            "coinGive:赠送币",
            "corner:角标内容",
            "timesLimit:用户充值次数限制",
            "validDate:有效期n月",
            "validUnit:单位:1=周;2=月;3=年;4=日",
            "moneyUnit:单价x元/天",
            "remark:描述",
            "productId:商品ID",
            "description:商品描述",
            "defaultFlag:默认标志0否1是"
    })
    @RequestMapping(value = "/getMemberFeeModel", method = {RequestMethod.POST})
    public ResultVO<?> getMemberFeeModel(HttpServletRequest request, Integer type, @RequestParam(value = "phoneOs", required = false, defaultValue = "1") Integer phoneOs) {
        SessionVO sessionVO = getSessionVO(request);
        return ResultVO.success(feeModelService.getMemberFeeModel(type, phoneOs, sessionVO));
    }

    /**
     * 获取充值模板中的Google商品id
     *
     * @param request
     * @return
     */
    @ApiName(value = "获取充值模板Google商品id", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({
            "phoneOs:0:int:手机系统：1安卓，2爱疯",
    })
    @ApiParamsOut({
            "productId:商品ID",
    })
    @RequestMapping(value = "/getMemberFeeModelPids", method = {RequestMethod.POST})
    public ResultVO<?> getMemberFeeModelPids(HttpServletRequest request, @RequestParam(value = "phoneOs", required = false, defaultValue = "1") Integer phoneOs) {
        SessionVO sessionVO = getSessionVO(request);
        List<FastFeeModelDetailPO> list1 = feeModelService.getMemberFeeModel(1, phoneOs, sessionVO);
        if (CollUtil.isEmpty(list1)) {
            return ResultVO.error(StaticStr.RECHARGE_TEMPLATE_NOT_EXIST);
        }
        // log.info("list1: {}", JSON.toJSONString(list1));
        List<FastFeeModelDetailPO> list2 = feeModelService.getMemberFeeModel(2, phoneOs, sessionVO);
        if (CollUtil.isEmpty(list2)) {
            return ResultVO.error(StaticStr.RECHARGE_TEMPLATE_NOT_EXIST);
        }
        // log.info("list2: {}", JSON.toJSONString(list2));
        List<String> ids1 = list1.stream().map(FastFeeModelDetailPO::getProductId).filter(productId -> !StrUtil.isBlank(productId)).collect(Collectors.toList());
        List<String> ids2 = list2.stream().map(FastFeeModelDetailPO::getProductId).filter(productId -> !StrUtil.isBlank(productId)).collect(Collectors.toList());
        ids1.addAll(ids2);
        return ResultVO.success(ids1);
    }

    /**
     * 获取支付挽留规则
     *
     * @param request
     * @param keepVO
     * @return
     */
    @ApiName(value = "获取支付挽留", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({
            "defaultDetailId:1:int:加密默认档位加密id",
            "selectDetailId:1:int:用户选择取消支付的档位加密id",
            "dramaId:1:int:短剧id"
    })
    @ApiParamsOut({
            "type:类型:1=充值金币;2=充值VIP",
            "title:卡名称",
            "moneyRecharge:充值金额",
            "coinRecharge:充值币",
            "coinGive:赠送币",
            "corner:角标内容",
            "timesLimit:用户充值次数限制",
            "validDate:有效期n月",
            "validUnit:单位:1=周;2=月;3=年;4=日",
            "moneyUnit:单价x元/天",
            "remark:描述",
            "defaultFlag:默认标志0否1是",
            "unlockSeriesNum:解锁集数",
            "keepTimes:挽留次数"
    })
    @RequestMapping(value = "/getMemberFeeKeep", method = {RequestMethod.POST})
    public ResultVO<?> getMemberFeeKeep(HttpServletRequest request, FeeKeepVO keepVO) {
        SessionVO sessionVO = getSessionVO(request);
        if ("undefined".equals(keepVO.getDramaId())) {
            Map<String, Object> results = ResultVO.getMap();
            results.put("message", "剧id不能为空");
            return ResultVO.success(results);
        }
        return feeKeepBatchService.getMemberFeeKeep(keepVO, sessionVO);
    }

    @ApiName(value = "连续播放设置接口", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn({
            "continueFlag:1:int:连续播放设置0不连续,1连续"
    })
    @ApiParamsOut({
            "status:ok",
    })
    @RequestMapping("/updateContinueFlag")
    public ResultVO<?> updateContinueFlag(HttpServletRequest request, Integer continueFlag) {
        SessionVO sessionVO = getSessionVO(request);
        FastMemberSettingPO msPO = new FastMemberSettingPO();
        msPO.setId(sessionVO.getMemberId());
        msPO.setContinueFlag(continueFlag);
        MethodVO methodVO = fastMemberSettingService.updateContinueFlag(msPO);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "查询连续播放设置", folder = {"vip升级"})
    @ApiParamsIn({
            "no:1:int:无需参数"
    })
    @ApiParamsOut({
            "continueFlag:0不连续播放，1连续自动播放",
    })
    @RequestMapping("/getContinueFlag")
    public ResultVO<?> getContinueFlag(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        FastMemberSettingPO msPO = fastMemberSettingService.queryByIdRedis(sessionVO);
        Map<String, Object> results = new HashMap<>();
        results.put("continueFlag", msPO.getContinueFlag());
        return ResultVO.success(results);
    }

    @ApiName(value = "vip查询可看剧列表", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn({
            "no:1:int:无需参数"
    })
    @ApiParamsOut({
            "dramaList:剧列表",
            "dramaCount:剧数量"
    })
    @RequestMapping("/getMyDramaList")
    public ResultVO<?> getMyDramaList(HttpServletRequest request) {
        return memberService.getMyDramaList(getSessionVO(request));
    }

    @ApiName(value = "青少年模式-设置接口", folder = {StaticFolder.FOLDER_FREE_DRAMA})
    @ApiParamsIn({
            "youngFlag:1:int:青少年模式设置0关闭,1开启",
            "youngPassword:1:str:密码",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @RequestMapping("/updateYoungFlag")
    public ResultVO<?> updateTeenagerFlag(HttpServletRequest request, FastMemberYoungPO params) {
        SessionVO sessionVO = getSessionVO(request);

        // 参数校验
        if (params.getYoungFlag() == null || !StrUtil.equalsAny(params.getYoungFlag(), 0, 1)) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isBlank(params.getYoungPassword())) {
            return ResultVO.error(StaticStr.PASSWORD_CANNOT_BE_EMPTY);
        }

        // 密码md5加密
        params.setYoungPassword(Md5Util.getMD5BySalt(params.getYoungPassword()));
        params.setId(sessionVO.getMemberId());
        ResultVO<?> r = fastMemberYoungService.updateYoungFlag(params);
        sessionVO.setYoungFlag(params.getYoungFlag());
        r.setSummary(sessionVO);
        return r;
    }

    @ApiName(value = "会员自动续费管理查询", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({
            "params:1:int:无需参数"
    })
    @ApiParamsOut({
            "memberId:用户id",
            "contractState:服务状态0未签约1签约",
            "product:续费产品",
            "moneyRecharge:续费金额",
            "vipDate:续费日期",
            "signWay:支付方式2-支付宝，3-抖音支付",
    })
    @RequestMapping("/getMemberAutoVip")
    public ResultVO<?> getMemberAutoVip(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        return memberService.getMemberAutoVip(sessionVO);
    }

    @ApiName(value = "绑定推送token", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({
            "pushToken:1:str:推送token",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @RequestMapping("/bindPushToken")
    public ResultVO<?> bindPushToken(HttpServletRequest request, FastMemberPO params) {
        if (StrUtil.isBlank(params.getPushToken())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setId(sessionVO.getMemberId());
        params.setPushToken(params.getPushToken());
        return ResultVO.fromMethodVO(memberService.update(params));
    }

}
