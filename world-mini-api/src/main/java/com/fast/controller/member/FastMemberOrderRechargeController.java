/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.po.fee.FastFeeKeepDetailPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.member.FastMemberPO;
import com.fast.service.fee.FastFeeKeepDetailService;
import com.fast.service.fee.FastFeeModelDetailService;
import com.fast.service.member.FastMemberOrderRechargeService;
import com.fast.service.member.FastMemberService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.promote.FastActivityPayService;
import com.fast.service.promote.FastActivityService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.JedisLock;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.fee.FastFeeModelDetailVO;
import com.fast.vo.mini.FastMiniVO;
import com.fast.vo.promote.FastLinkVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 订单充值记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberRecharge")
public class FastMemberOrderRechargeController extends BaseController {

    @Autowired
    private FastMemberOrderRechargeService rechargeService;
    @Autowired
    private FastFeeModelDetailService modelDetailService;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastMemberService fastMemberService;
    @Autowired
    private FastActivityService activityService;
    @Autowired
    private FastActivityPayService activityPayService;
    @Autowired
    private FastFeeKeepDetailService fastFeeKeepDetailService;
    @Autowired
    private FastMiniService fastMiniService;

    /**
     * 小程序-我的充值记录
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "小程序-我的充值记录", folder = {"用户"})
    @ApiParamsIn({
            "orderType:0:str:订单类型:1=充值金币;2=充值VIP",
            "given:0:str:是否查询赠币（0、否；1、是）"
    })
    @ApiParamsOut({
            "encryptionId:充值订单id",
            "coinRecharge:金币充值数",
            "coinGive:金币赠送数",
            "moneyRecharge:充值金额",
            "state:支付状态 0=待支付;1=已支付;2=已关闭",
            "orderType:订单类型:1=充值金币;2=充值VIP",
            "validDate:有效期n单位",
            "validUnit:单位:1=周;2=月;3=年;4=日",
            "outTransId:微信的订单号",
            "payTime:支付时间",
            "coinChangeId:金币变更id-如果大于0-以下changeType,coinChange字段才有效",
            "changeType:1=赠送;2=扣除;3=过期",
            "coinChange:金币变更数",
            "remark:备注",
    })
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberOrderRechargePO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setStates("0,1,2");
        if (sessionVO.getClientType() != null && sessionVO.getClientType() == 2) {
            params.setStates("1");
        }
        params.setMemberId(sessionVO.getMemberId());
        if (sessionVO.getContentType() != null && sessionVO.getContentType() == 2) {
            params.setOrderTypes("1,3");
        } else {
            params.setOrderType(null);
        }
        return rechargeService.queryPageListSimpleVO(params, pageVO);
    }

    /**
     * 用户充值-查询单个详情
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "用户充值-查询单个详情", folder = {"用户"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberOrderRechargePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberOrderRechargePO fastMemberRecharge = rechargeService.queryById(params);
        return ResultVO.success(fastMemberRecharge);
    }

    @ApiName(value = "用户充值-sdk查询订单的回传状态", folder = {"用户"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id"
    })
    @ApiParamsOut({
            "backState:1待回传，2回传成功，3回传失败"
    })
    @RequestMapping(value = "/getOrderBack", method = {RequestMethod.POST})
    public ResultVO<?> getOrderBack(HttpServletRequest request, FastMemberOrderRechargePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        return rechargeService.getOrderBack(id);
    }

    /**
     * 用户充值-充值
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "用户充值-充值", folder = {"用户"})
    @ApiParamsIn({
            "encryptionId:1:str:模板加密detailId",
            "dramaId:0:int:短剧数字id",
            "encryptionActivityId:0:str:活动加密id",
            "seriesNum:0:int:剧集号"
    })
    @ApiParamsOut({
            "encryptionId:订单加密id",
            "moneyRecharge:支付金额",
    })
    @RequestMapping(value = "/rechargeAdd", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastMemberOrderRechargePO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.RECHARGE_ADD_LOCK + sessionVO.getMemberId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            FastFeeKeepDetailPO keepDetailPO = null;
            boolean needModelFlag = true;
            if (StrUtil.isNotEmpty(params.getEncryptionDetailId())) {
                Integer keepDetailId = decodeInt(params.getEncryptionDetailId());
                keepDetailPO = fastFeeKeepDetailService.queryById(keepDetailId);
                if (keepDetailPO.getRecGearType() == 3) {
                    // 自定义档位的时候，可以没有模板id
                    needModelFlag = false;
                }
            }
            if (needModelFlag && isBlank(params.getEncryptionId())) {
                return ResultVO.error(StaticStr.TEMPLATEID_CANNOT_BE_EMPTY);
            }
            Integer modelDetailId = decodeInt(params.getEncryptionId());
            if (needModelFlag && modelDetailId == null) {
                return ResultVO.error(StaticStr.TEMPLATEID_CANNOT_BE_EMPTY);
            }
            params.setModelDetailId(modelDetailId);
            FastFeeModelDetailVO modelDetail = null;
            if (needModelFlag) {
                modelDetail = modelDetailService.queryInfoByRedis(modelDetailId);
                if (modelDetail == null) {
                    return ResultVO.error(StaticCode.ERROR, StaticStr.RECHARGE_TEMPLATE_IS_ILLEGAL);
                }
                if (modelDetail.getType() == 3 && params.getDramaId() == null) {
                    return ResultVO.error(StaticStr.GEAR_NOT_SUPPORT_RECHARGE_AT_THIS_LOCATION);
                }
                if (modelDetail.getTimesLimit() != null && modelDetail.getTimesLimit() >= 0) {
                    // 校验充值模板充值的次数
                    int count = rechargeService.queryRechargeCount(sessionVO.getMemberId(), sessionVO.getLinkId(), modelDetailId);
                    if (count >= modelDetail.getTimesLimit()) {
                        return ResultVO.error(StaticCode.ERROR, StaticStr.RECHARGE_TIMES_HAVE_BEEN_USEDUP);
                    }
                }
            }
            FastMiniVO miniVO = fastMiniService.queryInfoByRedis(sessionVO.getMiniId());
            Integer type = miniVO.getType();
            params.setMiniType(type);
            params.setMiniId(sessionVO.getMiniId());
            params.setOfficialId(sessionVO.getOfficialId());
            params.setLinkId(sessionVO.getLinkId());
            params.setContentType(sessionVO.getContentType());
            if (sessionVO.getLinkId() != null && sessionVO.getLinkId() > 0) {
                FastLinkVO link = fastLinkService.queryInfoByRedis(sessionVO.getLinkId());
                if (link == null) {
                    return ResultVO.error(StaticCode.ERROR, StaticStr.LINK_IS_ILLEGAL);
                }
                params.setAdvUserId(link.getAdvUserId());
                params.setRetailId(link.getRetailId());
                params.setLinkType(link.getLinkType());
                // 链接有content取链接的contentType
                params.setContentType(link.getContentType());
            } else {
                params.setAdvUserId(0);
                params.setRetailId(sessionVO.getRetailId());
            }
            // 处理手机系统
            if (params.getPhoneOs() == null) {
                if (sessionVO.getPhoneOs() != null) {
                    params.setPhoneOs(sessionVO.getPhoneOs());
                } else {
                    FastMemberPO query = new FastMemberPO();
                    query.setId(sessionVO.getMemberId());
                    FastMemberPO member = fastMemberService.querySimpleOne(query);
                    params.setPhoneOs(member.getPhoneOs());
                }
            }
            return rechargeService.rechargeAdd(sessionVO, params, modelDetail, keepDetailPO);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }
}
