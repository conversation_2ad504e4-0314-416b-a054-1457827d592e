/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberDesktopPO;
import com.fast.service.member.FastMemberDesktopService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberDesktop")
public class FastMemberDesktopController extends BaseController {

    @Autowired
    private FastMemberDesktopService fastMemberDesktopService;

    @ApiName(value = "member-查询列表", folder = {"member"})
    @PostMapping("/getFastMemberDesktopList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberDesktopPO params, PageVO pageVO) {
        return fastMemberDesktopService.queryPageList(params, pageVO);
    }

    @ApiName(value = "member-查询单个详情", folder = {"member"})
    @PostMapping("/getFastMemberDesktopDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberDesktopPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberDesktopPO fastMemberDesktop = fastMemberDesktopService.queryById(params);
        return ResultVO.success(fastMemberDesktop);
    }

    @ApiName(value = "member-加桌", folder = {"member"})
    @ApiParamsIn({
            "state:0:int:加桌状态0失败1成功（加桌回调时参数）"
    })
    @ApiParamsOut({
            "state:ok"
    })
    @PostMapping("/insertFastMemberDesktop")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberDesktopPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberDesktopService.insert(sessionVO, params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
