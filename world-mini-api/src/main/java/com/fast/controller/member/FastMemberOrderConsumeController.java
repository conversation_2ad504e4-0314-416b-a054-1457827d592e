/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberOrderConsumePO;
import com.fast.service.member.FastMemberOrderConsumeService;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 订单消费记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/member")
public class FastMemberOrderConsumeController extends BaseController {

    @Autowired
    private FastMemberOrderConsumeService fastMemberOrderConsumeService;

    /**
     * 用户消费列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "用户消费列表", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @ApiParamsIn({"no:0:int:只要登录就可以"})
    @ApiParamsOut({
            "dramaName:短剧名称",
            "dramaCover:短剧封面",
            "seriesNum:短剧剧集号",
            "createTime:消费时间",
            "coinConsume:消费金币",
            "coinChangeId:金币变更id-如果大于0-以下changeType,coinChange字段才有效",
            "changeType:1=赠送;2=扣除;3=过期",
            "coinChange:金币变更数",
            "remark:备注",
    })
    @RequestMapping(value = "/getMemberConsumeList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberOrderConsumePO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setMemberId(sessionVO.getMemberId());
        return fastMemberOrderConsumeService.queryPageList4Member(params, pageVO);
    }

    @ApiName(value = "用户消费-查询单个详情", folder = {StaticFolder.FOLDER_MINI_MEMBER})
    @RequestMapping(value = "/getMemberConsumeDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberOrderConsumePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberOrderConsumePO fastMemberConsume = fastMemberOrderConsumeService.queryById(params);
        return ResultVO.success(fastMemberConsume);
    }
}
