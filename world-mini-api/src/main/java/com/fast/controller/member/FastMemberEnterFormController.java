package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticVar;
import com.fast.po.member.FastMemberEnterFromPO;
import com.fast.service.member.FastMemberEnterFromService;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@RestController
public class FastMemberEnterFormController extends BaseController {

    @Autowired
    private FastMemberEnterFromService fastMemberEnterFromService;

    @ApiName(value = "member-查询列表", folder = {"member"})
    @PostMapping("/fastMemberEnterFrom/insert")
    public ResultVO<?> getList(HttpServletRequest request, String scene) {
        SessionVO sessionVO = getSessionVO(request);
        int enterFromType = 1;
        // 判断是否桌面进入
        if (StrUtil.isNotBlank(scene) && StaticVar.WE_CHAT_DESKTOP_SCENE.contains(scene)) {
            enterFromType = 2;
        }
        log.info("用户桌面进入判断enterFromType:{}", enterFromType);
        FastMemberEnterFromPO fastMemberEnterFromPO = new FastMemberEnterFromPO();
        fastMemberEnterFromPO.setType(enterFromType);
        fastMemberEnterFromPO.setMemberId(sessionVO.getMemberId());
        fastMemberEnterFromPO.setCreateTime(new Date());
        fastMemberEnterFromPO.setUpdateTime(new Date());
        // 增加用户桌面进入记录
        fastMemberEnterFromService.insertAsync(fastMemberEnterFromPO);
        return ResultVO.success();
    }
}
