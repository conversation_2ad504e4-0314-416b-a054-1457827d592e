package com.fast.controller.google;

import com.fast.base.BaseController;
import com.fast.po.member.FastMemberAdmobLogPO;
import com.fast.service.member.FastMemberAdmobLogService;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.google.crypto.tink.apps.rewardedads.RewardedAdsVerifier;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
public class GoogleAdsController extends BaseController {

    @Autowired
    private RewardedAdsVerifier rewardedAdsVerifier;

    @Autowired
    private FastMemberAdmobLogService fastMemberAdmobLogService;

    @RequestMapping("/nologin/admob/notify")
    public ResultVO<?> admobNotify(HttpServletRequest request, HttpServletResponse response,
                                   @RequestParam("ad_network") String adNetwork,
                                   @RequestParam("ad_unit") String adUnit,
                                   @RequestParam("reward_amount") String rewardAmount,
                                   @RequestParam("reward_item") String rewardItem,
                                   @RequestParam("timestamp") String timestamp,
                                   @RequestParam("transaction_id") String transactionId,
                                   @RequestParam(value = "user_id", required = false) String userId,
                                   @RequestParam(value = "custom_data", required = false) String customData,
                                   @RequestParam("signature") String signature,
                                   @RequestParam("key_id") String keyId) {

        log.info("adNetwork: {}", adNetwork);
        log.info("adUnit: {}", adUnit);
        log.info("rewardAmount: {}", rewardAmount);
        log.info("rewardItem: {}", rewardItem);
        log.info("timestamp: {}", timestamp);
        log.info("transactionId: {}", transactionId);
        log.info("userId: {}", userId);
        log.info("customData: {}", customData);
        log.info("signature: {}", signature);
        log.info("keyId: {}", keyId);

        StringBuilder urlBuilder = new StringBuilder("https://www.myserver.com/?");
        urlBuilder.append("ad_network=").append(adNetwork);
        urlBuilder.append("&ad_unit=").append(adUnit);

        if (StrUtil.hasText(customData)) {
            urlBuilder.append("&custom_data=").append(customData);
        }

        urlBuilder.append("&reward_amount=").append(rewardAmount);
        urlBuilder.append("&reward_item=").append(rewardItem);
        urlBuilder.append("&timestamp=").append(timestamp);
        urlBuilder.append("&transaction_id=").append(transactionId);

        if (StrUtil.hasText(userId)) {
            urlBuilder.append("&user_id=").append(userId);
        }

        urlBuilder.append("&signature=").append(signature);
        urlBuilder.append("&key_id=").append(keyId);

        String rewardUrl = urlBuilder.toString();
        log.info("google admob rewardUrl: {}", rewardUrl);

        try {
            rewardedAdsVerifier.verify(rewardUrl);
            log.error("google admob notify -> 验证成功");

            FastMemberAdmobLogPO logPO = new FastMemberAdmobLogPO();
            logPO.setMemberId(Long.valueOf(userId));
            logPO.setAdNetwork(adNetwork);
            logPO.setAdUnit(adUnit);
            logPO.setKeyId(keyId);
            logPO.setRewardAmount(Integer.valueOf(rewardAmount));
            logPO.setRewardItem(rewardItem);
            logPO.setSignature(signature);
            logPO.setTimestamp(Long.valueOf(timestamp));
            logPO.setTransactionId(transactionId);

            String[] split = StrUtil.split(customData, "_");
            logPO.setDramaId(Integer.valueOf(split[0]));
            logPO.setSeriesNum(Integer.valueOf(split[1]));
            if (split.length > 2) {
                logPO.setType(Integer.valueOf(split[2]));
            }
            fastMemberAdmobLogService.insert(logPO);
            log.error("google admob notify -> 观看记录已落库，ID: {}", logPO.getId());
        } catch (Exception e) {
            log.error("google admob notify -> 验证失败: {}", ExceptionUtils.getStackTrace(e));
            // response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            // throw new MyException("signature verification failed.");
            return ResultVO.error("signature verification failed.");
        }
        return ResultVO.success();
    }

}
