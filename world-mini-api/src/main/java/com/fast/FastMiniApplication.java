package com.fast;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

import com.fast.framework.config.ResourcesConfig;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableAsync// 开启异步调用
public class FastMiniApplication {

    public static void main(String[] args) {
    	ResourcesConfig.interceptorType = 4; // 加载小程序专用拦截器
        SpringApplication.run(FastMiniApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  系统[短剧mini-api]启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}
