package com.fast;

import com.fast.service.member.FastMemberAccountFlowService;
import com.fast.service.member.FastMemberAccountService;
import com.fast.service.member.FastMemberOrderConsumeService;
import com.fast.service.member.FastMemberOrderRechargeService;
import com.fast.service.task.FastMemberTaskRechargeService;
import com.fast.service.upay.UpayOrderLogService;
import com.google.crypto.tink.apps.rewardedads.RewardedAdsVerifier;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = FastMiniApplication.class)
public class CommonTest {

    @Autowired
    private FastMemberTaskRechargeService fastMemberTaskRechargeService;

    @Autowired
    private FastMemberOrderRechargeService fastMemberOrderRechargeService;

    @Autowired
    private FastMemberOrderConsumeService fastMemberOrderConsumeService;

    @Autowired
    private FastMemberAccountService fastMemberAccountService;

    @Autowired
    private FastMemberAccountFlowService fastMemberAccountFlowService;

    @Autowired
    private UpayOrderLogService upayOrderLogService;

    @Autowired
    private RewardedAdsVerifier rewardedAdsVerifier;

    @Test
    public void execute() {
        // List<FastMemberTaskRechargePO> list = fastMemberTaskRechargeService.queryList(new FastMemberTaskRechargePO());
        // for (FastMemberTaskRechargePO item : list) {
        //     if (item.getCoinCost() != 0) {
        //         continue;
        //     }
        //     FastMemberAccountFlowPO flow = new FastMemberAccountFlowPO();
        //     flow.setMemberId(item.getMemberId());
        //     flow.setType(FlowTypeEnum.IN.getCode());
        //     flow.setCoin(item.getCoinAll());
        //     flow.setCoinTask(item.getCoinAll());
        //     flow.setRemark(item.getRemark());
        //     flow.setBatchNo(StrUtil.getRandomInt(10));
        //     flow.setCreateTime(item.getCreateTime());
        //     fastMemberAccountFlowService.insert(flow);
        //     System.out.println(item.getMemberId() + "补流水: " + item.getCoinAll() + " -> " + item.getRemark());
        // }

        // FastMemberOrderRechargePO params = new FastMemberOrderRechargePO();
        // params.setState(1);
        // List<FastMemberOrderRechargePO> list = fastMemberOrderRechargeService.queryList(params);
        // for (FastMemberOrderRechargePO item : list) {
        //     if (item.getOrderType() != 1) {
        //         continue;
        //     }
        //     if (item.getCoinChangeId() != 0) {
        //         continue;
        //     }
        //     String batchNo = StrUtil.getRandomInt(10);
        //
        //     FastMemberAccountFlowPO flow = new FastMemberAccountFlowPO();
        //     flow.setMemberId(item.getMemberId());
        //     flow.setType(FlowTypeEnum.IN.getCode());
        //     flow.setCoin(item.getCoinRecharge());
        //     flow.setCoinRecharge(item.getCoinRecharge());
        //     flow.setRemark(StaticStr.RECHARGE_COINS);
        //     flow.setBatchNo(batchNo);
        //     flow.setCreateTime(item.getCreateTime());
        //     fastMemberAccountFlowService.insert(flow);
        //     System.out.println(item.getMemberId() + "补流水: " + item.getCoinRecharge() + " -> " + flow.getRemark());
        //
        //     if (item.getCoinGive() > 0) {
        //         flow.setCoin(item.getCoinGive());
        //         flow.setCoinGive(item.getCoinGive());
        //         flow.setRemark(StaticStr.RECHARGE_GIFT);
        //         flow.setBatchNo(batchNo);
        //         flow.setCreateTime(item.getCreateTime());
        //         fastMemberAccountFlowService.insert(flow);
        //         System.out.println(item.getMemberId() + "补流水: " + item.getCoinGive() + " -> " + flow.getRemark());
        //     }
        // }

        // List<FastMemberOrderConsumePO> list = fastMemberOrderConsumeService.queryList(new FastMemberOrderConsumePO());
        // for (FastMemberOrderConsumePO item : list) {
        //     if (item.getCoinChangeId() != 0) {
        //         continue;
        //     }
        //     FastMemberAccountFlowPO flow = new FastMemberAccountFlowPO();
        //     flow.setMemberId(item.getMemberId());
        //     flow.setType(FlowTypeEnum.OUT.getCode());
        //     flow.setCoin(item.getCoinRechargeConsume() + item.getCoinGiveConsume() + item.getCoinTaskConsume());
        //     flow.setCoinRecharge(item.getCoinRechargeConsume());
        //     flow.setCoinGive(item.getCoinGiveConsume());
        //     flow.setCoinTask(item.getCoinTaskConsume());
        //     flow.setRemark(StaticStr.UNLOCK_DRAMASERIES);
        //     flow.setBatchNo(StrUtil.getRandomInt(10));
        //     flow.setCreateTime(item.getCreateTime());
        //     fastMemberAccountFlowService.insert(flow);
        //     System.out.println(item.getMemberId() + "补流水: " + flow.getCoin() + " -> " + flow.getRemark());
        // }

        // Map<Long, Integer> map = new HashMap<>();
        // List<FastMemberTaskRechargePO> list1 = fastMemberTaskRechargeService.queryList(new FastMemberTaskRechargePO());
        // for (FastMemberTaskRechargePO item : list1) {
        //     if (item.getCoinCost() != 0) {
        //         continue;
        //     }
        //     Integer num = map.getOrDefault(item.getMemberId(), 0);
        //     map.put(item.getMemberId(), num + item.getCoinRemain());
        // }
        // for (Map.Entry<Long, Integer> entry : map.entrySet()) {
        //     System.out.println(entry.getKey() + ":" + entry.getValue());
        //     FastMemberAccountPO po = new FastMemberAccountPO();
        //     po.setMemberId(entry.getKey());
        //     po.setCoinTaskRemain(entry.getValue());
        //     po.setCoinTaskAll(entry.getValue());
        //     fastMemberAccountService.update(po);
        // }

        // Map<Long, Integer> map1 = new HashMap<>();
        // Map<Long, Integer> map2 = new HashMap<>();
        // FastMemberOrderRechargePO params = new FastMemberOrderRechargePO();
        // params.setState(1);
        // List<FastMemberOrderRechargePO> list = fastMemberOrderRechargeService.queryList(params);
        // for (FastMemberOrderRechargePO item : list) {
        //     if (item.getOrderType() != 1) {
        //         continue;
        //     }
        //     if (item.getCoinChangeId() != 0) {
        //         continue;
        //     }
        //     Integer num1 = map1.getOrDefault(item.getMemberId(), 0);
        //     map1.put(item.getMemberId(), num1 + item.getCoinRechargeRemain());
        //    
        //     Integer num2 = map2.getOrDefault(item.getMemberId(), 0);
        //     map2.put(item.getMemberId(), num2 + item.getCoinGiveRemain());
        // }
        // for (Map.Entry<Long, Integer> entry : map1.entrySet()) {
        //     System.out.println(entry.getKey() + ":" + entry.getValue());
        //     FastMemberAccountPO po = new FastMemberAccountPO();
        //     po.setMemberId(entry.getKey());
        //     po.setCoinRechargeRemain(entry.getValue());
        //     po.setCoinRechargeAll(entry.getValue());
        //     fastMemberAccountService.update(po);
        // }
        // for (Map.Entry<Long, Integer> entry : map2.entrySet()) {
        //     System.out.println(entry.getKey() + ":" + entry.getValue());
        //     FastMemberAccountPO po = new FastMemberAccountPO();
        //     po.setMemberId(entry.getKey());
        //     po.setCoinGiveRemain(entry.getValue());
        //     po.setCoinGiveAll(entry.getValue());
        //     fastMemberAccountService.update(po);
        // }

        // Map<Long, Integer> map = new HashMap<>();
        // List<FastMemberAccountFlowPO> list = fastMemberAccountFlowService.queryList(new FastMemberAccountFlowPO());
        // for (FastMemberAccountFlowPO item : list) {
        //     if (item.getType() != 1) {
        //         continue;
        //     }
        //     Integer num = map.getOrDefault(item.getMemberId(), 0);
        //     map.put(item.getMemberId(), num + item.getCoinGive());
        // }
        // for (Map.Entry<Long, Integer> entry : map.entrySet()) {
        //     System.out.println(entry.getKey() + ":" + entry.getValue());
        //     FastMemberAccountPO po = new FastMemberAccountPO();
        //     po.setMemberId(entry.getKey());
        //     po.setCoinGiveAll(entry.getValue());
        //     fastMemberAccountService.update(po);
        // }

        // UpayOrderLog upayOrderLog = new UpayOrderLog();
        // upayOrderLog.setState(2);
        // List<UpayOrderLog> list = upayOrderLogService.queryList(upayOrderLog);
        // for (UpayOrderLog orderLog : list) {
        //     upayOrderLogService.fillGoogleProductInfo(orderLog.getId());
        // }
    }

    public static void main(String[] args) throws Exception {

    }


}
