<#include "/macro.include"/>
<#assign className = table.className>
<#assign classNameFirstLower = table.classNameFirstLower>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<#macro namespace>${className}.</#macro>

<mapper namespace="${basePackage}.mapper.${module}.${className}Mapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="${className}_columns">
		select <#list table.columns as column>t.`${column.sqlName}`<#if column_has_next>,</#if></#list>
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="${className}PO">
		<include refid="${className}_columns" />
	    from ${table.sqlName} t
        <where>
		<#list table.compositeIdColumns as column>
	        t.`${column.sqlName}` = <@mapperEl 'id'/><#if column_has_next> and </#if>
	    </#list>
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="${className}PO" resultType="${className}PO">
		<include refid="${className}_columns" />
	    from ${table.sqlName} t
        <where>
        	<include refid="whereSQL" />
        </where>
        <#noparse>limit 1</#noparse>
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="${className}PO" resultType="${className}PO">
		<include refid="${className}_columns" />
	    from ${table.sqlName} t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="${className}PO" resultType="java.lang.Integer">
		select count(*)
	    from ${table.sqlName} t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<#list table.columns as column>
		<if test="${column.columnNameLower} != null">
			and t.`${column.sqlName}` = <@mapperEl column.columnNameLower/>
		</if>
        </#list>
		<if test="createTimeS != null">
			and t.`create_time` &gt;= <@mapperEl 'createTimeS'/>
		</if>
		<if test="createTimeE != null">
			and t.`create_time` &lt;= <@mapperEl 'createTimeE'/>
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="${table.idColumn.columnNameFirstLower}" parameterType="${className}PO">
        insert into ${table.sqlName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <#list table.notPkColumns as column>
            <if test="${column.columnNameLower} != null">`${column.sqlName}`<#if column_has_next>,</#if></if>
			</#list>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <#list table.notPkColumns as column>
	        <if test="${column.columnNameLower} != null"><@mapperEl column.columnNameFirstLower/><#if column_has_next>,</#if></if>
	        </#list>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="${table.idColumn.columnNameFirstLower}" parameterType="${className}PO">
        insert into ${table.sqlName} (
        <#list table.notPkColumns as column> `${column.sqlName}`<#if column_has_next>,</#if></#list>
        ) values
        <foreach collection="list" item="item" separator=",">
        (
        <#list table.notPkColumns as column> <@mapperBatchEl column.columnNameLower/><#if column_has_next>,</#if></#list>
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="${className}PO">
        update ${table.sqlName}
        <set>
	        <#list table.notPkColumns as column>
         	<if test="${column.columnNameFirstLower} != null">
                `${column.sqlName}` = <@mapperEl column.columnNameFirstLower/>,
            </if>
	        </#list>
	    </set>
        where <#list table.compositeIdColumns as column>`${column.sqlName}` = <@mapperEl column.columnNameLower/> <#if column_has_next> AND </#if> </#list>
	</update>

	<!-- 删除 -->
	<delete id="deleteById">
		delete from `${table.sqlName}` where `id` = <@mapperEl 'id'/>
	</delete>

</mapper>
