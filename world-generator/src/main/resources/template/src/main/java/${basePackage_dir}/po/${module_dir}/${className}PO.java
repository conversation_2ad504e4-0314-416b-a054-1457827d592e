<#include "/java_copyright.include">
<#assign className = table.className>   
<#assign classNameLower = className?uncap_first> 
package ${basePackage}.po.${module};

import java.io.Serializable;
import com.fast.utils.DateUtil;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import ${basePackage}.po.BasePO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * ${table.remarks}
 */
@Setter
@Getter
@JsonInclude(Include.NON_NULL)
@TableName("${table.sqlName}")
public class ${className}PO extends BasePO implements Serializable {  

    private static final long serialVersionUID = 1L;

    <#list table.columns as column>
    <#if column.columnNameLower=="id">
    @TableId
    private ${column.simpleJavaType} ${column.columnNameLower}; // ${column.remarks}
    <#elseif column.simpleJavaType=="Date">
    <#if !column.nullable && column.columnNameLower!="createTime" && column.columnNameLower!="updateTime">
    @NotNull(message="${column.remarks}不能为空")
    </#if>
    @JsonFormat(pattern = DateUtil.sdf07)
    private ${column.simpleJavaType} ${column.columnNameLower}; // ${column.remarks}
    <#else>
    <#if column.simpleJavaType=="String">
    <#if !column.nullable>
    @NotBlank(message = "${column.remarks}不能为空")
    </#if>
    @Size(max = ${column.size}, message = "${column.remarks}最多输入${column.size}位")
    <#else>
    <#if !column.nullable && column.columnNameLower!="delFlag" && column.columnNameLower!="creatorId" && column.columnNameLower!="updatorId">
    @NotNull(message="${column.remarks}不能为空")
    </#if>
    </#if>
    <#if column.columnNameLower=="delFlag">
    @JsonIgnore
    </#if>
    private ${column.simpleJavaType} ${column.columnNameLower}; // ${column.remarks}
    </#if>
    </#list>

    // 非数据库字段
    private String createTimeStr; // 创建时间区间（yyyy-MM-dd - yyyy-MM-dd）
    private Date createTimeS; // 创建时间开始
    private Date createTimeE; // 创建时间结束
}
