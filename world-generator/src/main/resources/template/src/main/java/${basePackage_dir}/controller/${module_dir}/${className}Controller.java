<#include "/java_copyright.include">
<#assign className = table.className>
<#assign classNameLower = className?uncap_first>
package ${basePackage}.controller.${module};

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;

import ${basePackage}.vo.PageVO;
import ${basePackage}.vo.MethodVO;
import ${basePackage}.vo.ResultVO;
import ${basePackage}.vo.SessionVO;
import ${basePackage}.utils.DateUtil;
import ${basePackage}.utils.StrUtil;
import ${basePackage}.constant.StaticStr;
import ${basePackage}.po.${module}.${className}PO;
import ${basePackage}.service.${module}.${className}Service;

/**
<#include "/java_description.include">
 */
@RestController
@RequestMapping("/${module}")
public class ${className}Controller extends BaseController {

    @Autowired
    private ${className}Service ${classNameLower}Service;

    @ApiName(value = "${moduleName}-查询列表",folder = {"${folder}"})
    @ApiParamsIn({
            <#list table.columns as column>
            <#if column.columnNameLower != "id" && column.columnNameLower != "delFlag" && column.columnNameLower != "creatorId" && column.columnNameLower != "createTime" && column.columnNameLower != "updatorId" && column.columnNameLower != "updateTime">
            "${column.columnNameLower}:0:str:${column.columnAlias}",
            </#if>
            </#list>
            "createTimeStr:0:str:创建时间区间（yyyy-MM-dd - yyyy-MM-dd）",
            "page:1:int:页码",
            "limit:1:int:每页条数",
    })
    @ApiParamsOut({
            <#list table.columns as column>
            <#if column.columnNameLower != "delFlag" && column.columnNameLower != "creatorId" && column.columnNameLower != "updatorId" && column.columnNameLower != "updateTime">
            "list 》${column.columnNameLower}: ${column.columnAlias}",
            </#if>
            </#list>
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, @RequestBody ${className}PO params, @RequestBody PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        return ${classNameLower}Service.queryPageList(params, pageVO);
    }

    @ApiName(value = "${moduleName}-查询单个详情", folder = {"${folder}"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            <#list table.columns as column>
            <#if column.columnNameLower != "delFlag" && column.columnNameLower != "creatorId" && column.columnNameLower != "updatorId" && column.columnNameLower != "updateTime">
            "${column.columnNameLower}: ${column.columnAlias}",
            </#if>
            </#list>
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, @RequestBody ${className}PO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        ${className}PO ${classNameLower} = ${classNameLower}Service.queryById(params);
        return ResultVO.success(${classNameLower});
    }

    @ApiName(value = "${moduleName}-添加", folder = {"${folder}"})
    @ApiParamsIn({
            <#list table.columns as column>
            <#if column.columnNameLower != "id" && column.columnNameLower != "delFlag" && column.columnNameLower != "creatorId" && column.columnNameLower != "createTime" && column.columnNameLower != "updatorId" && column.columnNameLower != "updateTime">
            "${column.columnNameLower}:${column.nullable?string('0','1')}:${(column.simpleJavaType=='String')?string('str','int')}:${column.columnAlias}",
            </#if>
            </#list>
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated ${className}PO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = ${classNameLower}Service.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "${moduleName}-更新",folder = {"${folder}"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            <#list table.columns as column>
            <#if column.columnNameLower != "id" && column.columnNameLower != "delFlag" && column.columnNameLower != "creatorId" && column.columnNameLower != "createTime" && column.columnNameLower != "updatorId" && column.columnNameLower != "updateTime">
            "${column.columnNameLower}:${column.nullable?string(0,1)}:${(column.simpleJavaType=='String')?string('str','int')}:${column.columnAlias}",
            </#if>
            </#list>
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody @Validated ${className}PO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = ${classNameLower}Service.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "${moduleName}-删除", folder = {"${folder}"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, @RequestBody ${className}PO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = ${classNameLower}Service.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }
    
}
