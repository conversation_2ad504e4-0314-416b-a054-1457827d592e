<#include "/java_copyright.include">
<#assign className = table.className>
<#assign classNameLower = className?uncap_first>
package ${basePackage}.service.${module};

import com.fast.utils.DateUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import ${basePackage}.mapper.${module}.${className}Mapper;
import ${basePackage}.po.${module}.${className}PO;
import ${basePackage}.vo.ResultVO;
import ${basePackage}.vo.MethodVO;
import ${basePackage}.vo.PageVO;
import ${basePackage}.service.${module}.${className}Service;
import ${basePackage}.service.base.BaseService;
import ${basePackage}.utils.DateUtil;
import com.fast.constant.StaticStr;

/**
<#include "/java_description.include">
 */
@Service
public class ${className}Service extends BaseService {

    @Autowired
    private ${className}Mapper ${classNameLower}Mapper;

    /**
     * 通过id查询单个对象
     */
    public ${className}PO queryById(${className}PO params) {
        return ${classNameLower}Mapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public ${className}PO queryById(Integer id) {
        return ${classNameLower}Mapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public ${className}PO queryOne(${className}PO params) {
        return ${classNameLower}Mapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<${className}PO> queryList(${className}PO params) {
        return ${classNameLower}Mapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(${className}PO params, PageVO pageVO) {
        startPage(pageVO);
        List<${className}PO> list = ${classNameLower}Mapper.queryList(params);
        for (${className}PO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
		return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(${className}PO params){
        Integer count = ${classNameLower}Mapper.queryCount(params);
        return count == null ? 0 : count;
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(${className}PO params)  {
    	Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (${classNameLower}Mapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<${className}PO> list) {
        if(${classNameLower}Mapper.insertBatch(list) > 0) {
            return MethodVO.success();
        }else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(${className}PO params)  {
		Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (${classNameLower}Mapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(${className}PO params)  {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        ${className}PO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        ${classNameLower}Mapper.deleteById(po.getId());
        return MethodVO.success();
    }
    
}
