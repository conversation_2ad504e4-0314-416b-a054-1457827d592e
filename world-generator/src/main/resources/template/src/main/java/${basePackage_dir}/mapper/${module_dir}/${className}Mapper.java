<#include "/java_copyright.include">
<#assign className = table.className>
<#assign classNameLower = className?uncap_first>
package ${basePackage}.mapper.${module};

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import ${basePackage}.po.${module}.${className}PO;

/**
<#include "/java_description.include">
 */
@Mapper
public interface ${className}Mapper {

	// 通过id查询单个对象
    ${className}PO queryById(${className}PO entity);

	// 通过id查询单个对象
	${className}PO queryById(@Param("id") Integer id);

	// 通过条件查询单个对象
    ${className}PO queryOne(${className}PO entity);

    // 查询全部
    List<${className}PO> queryList(${className}PO entity);

    // 查询总数
    Integer queryCount(${className}PO entity);

    // 可选新增
    int insertSelective(${className}PO entity);

    // 批量新增
    int insertBatch(List<${className}PO> list);

    // 更新
    int updateById(${className}PO entity);

    // 删除
    int deleteById(@Param("id") Integer id);

}
