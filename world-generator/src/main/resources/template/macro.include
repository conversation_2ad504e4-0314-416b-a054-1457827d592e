<#-- 本文件包含一些公共的函数,本文件会被其它模板自动include -->

<#assign dollar = '$'> 

<#-- 将value变成jsp el表达式,主要由于FreeMarker生成表达式不方便 -->
<#macro jspEl value>${r"${"}${value}}</#macro>

<#-- 生成java构造函数 -->
<#macro generateConstructor constructor>
	public ${constructor}(){
	}

	public ${constructor}(
	<#list table.compositeIdColumns as column>
		${column.javaType} ${column.columnNameLower}<#if column_has_next>,</#if>
	</#list>		
	){
	<#list table.compositeIdColumns as column>
		<#if column.pk>
		this.${column.columnNameLower} = ${column.columnNameLower};
		</#if>
	</#list>	
	}
</#macro>

<#macro mapperEl value>${r"#{"}${value}}</#macro>
<#macro mapperPageEl value>${r"#{paramMap."}${value}}</#macro>
<#macro mapperBatchEl value>${r"#{item."}${value}}</#macro>
