<?xml version="1.0" encoding="GBK"?>
<!DOCTYPE properties SYSTEM "http://java.sun.com/dtd/properties.dtd">
<properties>
    <comment>
        代码生成器配置文件:
        1.会为所有的property生成property_dir属性,如pkg=com.company => pkg_dir=com/company
        2.可以引用环境变量: ${env.JAVA_HOME} or System.properties: ${user.home},property之间也可以相互引用
    </comment>

    <!-- jsp namespace: web/${namespace}/${className}/list.jsp -->
    <entry key="namespace">pages</entry>

    <!-- oracle需要指定jdbc.schema,其它数据库忽略此项配置 -->

    <!-- 数据库类型映射 -->
    <entry key="java_typemapping.java.sql.Timestamp">java.util.Date</entry>
    <entry key="java_typemapping.java.sql.DateTime">java.util.Date</entry>
    <entry key="java_typemapping.java.sql.Date">java.util.Date</entry>
    <entry key="java_typemapping.java.sql.Time">java.util.Time</entry>
    <entry key="java_typemapping.java.lang.Byte">Integer</entry>
    <entry key="java_typemapping.java.lang.Short">Integer</entry>
    <entry key="java_typemapping.java.math.BigDecimal">java.math.BigDecimal</entry>

    <!-- H2 -->
    <!-- <entry key="jdbc.url">jdbc:h2:tcp://localhost/test</entry> <entry key="jdbc.driver">org.h2.Driver</entry> -->

    <entry key="jdbc_url">*****************************************</entry>
    <entry key="jdbc_schema">test_world</entry>
    <entry key="jdbc_username">fast_insert</entry>
    <entry key="jdbc_password">KK5k09IiPi6tLp07t6OpjKlqM</entry>
    <entry key="jdbc_driver">com.mysql.cj.jdbc.Driver</entry>

    <!-- Oracle ****************************** <entry key="jdbc.url">************************************:[sid]</entry>
        <entry key="jdbc.driver">oracle.jdbc.driver.OracleDriver</entry> -->

    <!-- SQLServer2000 <entry key="jdbc.url">******************************************************=[database]</entry>
        <entry key="jdbc.driver">com.microsoft.jdbc.sqlserver.SQLServerDriver</entry> -->

    <!-- SQLServer2005 <entry key="jdbc.url">***********************************************=[database]</entry>
        <entry key="jdbc.driver">com.microsoft.sqlserver.jdbc.SQLServerDriver</entry> -->

    <!-- JTDs for SQLServer <entry key="jdbc.url">****************************************/[database];tds=8.0;lastupdatecount=true</entry>
        <entry key="jdbc.driver">net.sourceforge.jtds.jdbc.Driver</entry> -->

    <!-- PostgreSql <entry key="jdbc.url">***************************/[database]</entry>
        <entry key="jdbc.driver">org.postgresql.Driver</entry> -->

    <!-- Sybase <entry key="jdbc.url">******************************/[database]</entry>
        <entry key="jdbc.driver">com.sybase.jdbc.SybDriver</entry> -->

    <!-- DB2 <entry key="jdbc.url">*************************/[database]</entry>
        <entry key="jdbc.driver">com.ibm.db2.jdbc.app.DB2Driver</entry> -->

    <!-- HsqlDB <entry key="jdbc.url">***************************</entry> <entry
        key="jdbc.driver">org.hsqldb.jdbcDriver</entry> -->

    <!-- Derby <entry key="jdbc.url">***********************************</entry>
        <entry key="jdbc.driver">org.apache.derby.jdbc.ClientDriver</entry> -->

</properties>
