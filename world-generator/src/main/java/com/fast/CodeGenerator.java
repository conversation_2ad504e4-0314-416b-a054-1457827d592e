package com.fast;

import cn.org.rapid_framework.generator.GeneratorFacade;
import cn.org.rapid_framework.generator.GeneratorProperties;

public class CodeGenerator {

    public static void main(String[] args) {
        try {
            // 模板地址
            String templatePath = "classpath:template";
            // 获取系统用户名
            GeneratorProperties.setProperty("userName", System.getProperty("user.name"));
            // 输出目录
            GeneratorProperties.setProperty("outRoot", "generatorOutput");
            // 移除表名前缀
            GeneratorProperties.setProperty("tableRemovePrefixes", "");
            // Package
            GeneratorProperties.setProperty("basePackage", "com.fast");
            // 模块描述
            GeneratorProperties.setProperty("moduleName", "影片");
            // apidoc的文件夹
            GeneratorProperties.setProperty("folder", "影片库");
            // 所属模块-包
            GeneratorProperties.setProperty("module", "make");
            // 需要生成的表名
            String[] tables = {
                    "make_subtitle_extract_task",
                    "make_subtitle_remove_task",
                    "make_subtitle_translate_task",
                    "make_video_compress_task",
            };
            GeneratorFacade generator = new GeneratorFacade();
            generator.getGenerator().addTemplateRootDir(templatePath);
            // 删除历史生成的文件
            generator.deleteOutRootDir();
            System.out.println("===开始处理===");
            // 通过数据库表生成文件
            generator.generateByTable(tables);
            // 自动搜索数据库中的所有表并生成文件
            // generator.generateByAllTable();
            // 按table名字删除文件
            // generator.deleteByTable("table_name", "template");
            System.out.println("===结束处理===");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
