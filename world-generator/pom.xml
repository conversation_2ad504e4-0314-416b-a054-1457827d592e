<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fast</groupId>
    <artifactId>world</artifactId>
    <version>1.0.0</version>
  </parent>
  <artifactId>world-generator</artifactId>
  
  <dependencies>
        <!-- 代码生成工具 -->
        <dependency>
            <groupId>com.googlecode.rapid-framework</groupId>
            <artifactId>rapid-generator</artifactId>
            <version>4.0.6</version>
        </dependency>

        <!-- mysql数据库 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- freemarker -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.31</version>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>2.1.214</version>
        </dependency>

        <!-- 代码生成器扩展包 -->
        <dependency>
            <groupId>com.googlecode.rapid-framework</groupId>
            <artifactId>rapid-generator-ext</artifactId>
            <version>4.0.6</version>
        </dependency>

        <!-- 代码生成器模板,模板根目录通过 classpath:generator/template/rapid 可以引用 -->
        <dependency>
            <groupId>com.googlecode.rapid-framework</groupId>
            <artifactId>rapid-generator-template</artifactId>
            <version>4.0.6</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>