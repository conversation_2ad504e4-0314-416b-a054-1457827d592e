<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.fast</groupId>
    <artifactId>world</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>world</name>
    <description>world</description>

    <modules>
        <module>world-api</module>
        <module>world-mini-api</module>
        <module>world-gateway</module>
        <module>world-service</module>
        <module>world-generator</module>
        <module>world-job</module>
    </modules>

    <properties>
        <fast.up.version>${project.version}</fast.up.version>
        <project.sourceEncoding>UTF-8</project.sourceEncoding>
        <project.outputEncoding>UTF-8</project.outputEncoding>
        <maven-clean-plugin.version>3.4.1</maven-clean-plugin.version>
        <maven-compiler-plugin.version>3.14.0</maven-compiler-plugin.version>
        <maven-jar-plugin.version>3.4.2</maven-jar-plugin.version>
        <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
        <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
        <maven-surefire-plugin.version>3.5.2</maven-surefire-plugin.version>
        <java.version>21</java.version>

        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-framework.version>5.3.39</spring-framework.version>
        <spring-boot-admin.version>2.7.13</spring-boot-admin.version>
        <alipay-easysdk.version>2.2.3</alipay-easysdk.version>
        <bcprov-jdk16.version>1.46</bcprov-jdk16.version>
        <alipay-easysdk.version>2.2.3</alipay-easysdk.version>
        <aliyun-oss.version>3.18.1</aliyun-oss.version>
        <aliyun-cdn.version>3.1.1</aliyun-cdn.version>
        <commons-io.version>2.18.0</commons-io.version>
        <commons-lang3.version>3.17.0</commons-lang3.version>
        <commons-pool2.version>2.12.0</commons-pool2.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <commons-validator.version>1.7</commons-validator.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>33.4.8-jre</guava.version>
        <hikaricp.version>6.2.1</hikaricp.version>
        <jedis.version>6.0.0</jedis.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <mybatis.version>3.5.19</mybatis.version>
        <mybatis-boot.version>2.3.2</mybatis-boot.version>
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <nacos.version>2.4.3</nacos.version>
        <pagehelper-boot.version>2.1.1</pagehelper-boot.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <poi.version>5.2.5</poi.version>
        <tos.version>2.8.7</tos.version>
        <tomcat.version>9.0.107</tomcat.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- SpringFramework的依赖配置-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- SpringBoot的依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <!-- SpringBoot集成mybatis框架 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <!-- mybatis-plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- Dynamic DataSource -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <!-- page-helper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-boot.version}</version>
            </dependency>

            <!--io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <!--文件上传工具类 -->
            <!--<dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
            </dependency>-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-validator</groupId>
                <artifactId>commons-validator</artifactId>
                <version>${commons-validator.version}</version>
            </dependency>
            <!-- 汉字转拼音 -->
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- 条码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- Google的核心Java库 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikaricp.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <!-- 阿里云oss上传 -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-oss.version}</version>
            </dependency>
            <!-- 阿里云cdn查询 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>cdn20180510</artifactId>
                <version>${aliyun-cdn.version}</version>
            </dependency>

            <!-- 加密解密 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk16</artifactId>
                <version>${bcprov-jdk16.version}</version>
            </dependency>

            <!-- SpringBoot 监控服务端 -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!--  SpringBoot 监控客户端 -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- Alibaba Nacos 配置 -->
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.version}</version>
            </dependency>

            <!-- SpringCloud Alibaba Nacos Config -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>2021.0.5.0</version>
            </dependency>

            <!--连山云TOS-->
            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>ve-tos-java-sdk</artifactId>
                <version>${tos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.rst</groupId>
                <artifactId>service</artifactId>
                <version>${fast.up.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fast</groupId>
                <artifactId>world-service</artifactId>
                <version>${fast.up.version}</version>
            </dependency>

            <!--<dependency>
                <groupId>com.aliyun.openservices</groupId>
                <artifactId>tablestore</artifactId>
                <version>5.16.3</version>
            </dependency>-->

            <dependency>
                <groupId>cfca.sadk</groupId>
                <artifactId>sadk-unlimit</artifactId>
                <version>3.2.0.8</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.14.9</version>
            </dependency>

            <dependency>
                <groupId>org.brotli</groupId>
                <artifactId>dec</artifactId>
                <version>0.1.2</version>
            </dependency>

            <dependency>
                <groupId>com.google.apis</groupId>
                <artifactId>google-api-services-androidpublisher</artifactId>
                <version>v3-rev20250519-2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.auth</groupId>
                <artifactId>google-auth-library-oauth2-http</artifactId>
                <version>1.23.0</version>
            </dependency>

            <dependency>
                <groupId>com.google.firebase</groupId>
                <artifactId>firebase-admin</artifactId>
                <version>9.1.1</version>
            </dependency>

            <dependency>
                <groupId>com.google.crypto.tink</groupId>
                <artifactId>apps-rewardedads</artifactId>
                <version>1.12.2</version>
            </dependency>

            <dependency>
                <groupId>org.mp4parser</groupId>
                <artifactId>isoparser</artifactId>
                <version>1.9.41</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibabacloud-ice20201109</artifactId>
                <version>6.0.7</version>
            </dependency>

            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>volc-sdk-java</artifactId>
                <version>1.0.231</version>
            </dependency>

            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>volcengine-java-sdk-vod20250101</artifactId>
                <version>0.2.24</version>
            </dependency>
            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>volcengine-java-sdk-core</artifactId>
                <version>0.2.24</version>
            </dependency>

            <dependency>
                <groupId>com.github.vladimir-bukhtoyarov</groupId>
                <artifactId>bucket4j-core</artifactId>
                <version>7.0.0</version>
            </dependency>

            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-ratelimiter</artifactId>
                <version>2.0.2</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.40.2</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
                <configuration>
                    <encoding>${project.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope> <!--打包本地jar-->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>${maven-clean-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <skipTests>true</skipTests> <!-- 打包跳过测试 -->
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>ali</id>
            <name>ali public</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2078099-release-7ZuOxS/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>maven-central</id>
            <url>https://repo.maven.apache.org/maven2</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
