# 多文件下载打包功能使用说明

## 功能概述

基于现有的 `FileDownloader` 工具类，实现了多文件下载并打包成ZIP的功能。该功能支持：

- 并发下载多个URL文件
- 自动打包成ZIP压缩文件
- 支持自定义文件名
- 提供Web API接口
- 自动清理临时文件
- 错误处理和日志记录

## 核心组件

### 1. MultiFileDownloader（工具类）
位置：`com.fast.service.make.video.util.MultiFileDownloader`

核心功能：
- 并发下载多个文件
- 打包成ZIP文件
- 临时文件管理

### 2. MultiFileDownloadService（服务类）
位置：`com.fast.service.make.video.MultiFileDownloadService`

业务功能：
- 封装下载逻辑
- 提供便利方法
- 文件管理功能

### 3. MultiFileDownloadController（控制器）
位置：`com.fast.controller.MultiFileDownloadController`

Web API接口：
- RESTful API
- 文件下载接口
- 文件管理接口

## 使用方式

### 1. 直接使用工具类

```java
// 方式1：使用URL列表
List<String> urls = Arrays.asList(
    "https://example.com/file1.txt",
    "https://example.com/file2.jpg"
);
String zipPath = MultiFileDownloader.downloadUrlsAndZip(urls, "/path/to/output.zip");

// 方式2：使用URL和文件名映射
Map<String, String> urlFileMap = new HashMap<>();
urlFileMap.put("https://example.com/file1.txt", "document.txt");
urlFileMap.put("https://example.com/file2.jpg", "image.jpg");
String zipPath = MultiFileDownloader.downloadUrlsAndZip(urlFileMap, "/path/to/output.zip");

// 方式3：使用DownloadTask（最灵活）
List<DownloadTask> tasks = new ArrayList<>();
tasks.add(new DownloadTask("https://example.com/file1.txt", "custom_name.txt"));
String zipPath = MultiFileDownloader.downloadAndZip(tasks, "/path/to/output.zip", null, 5);
```

### 2. 使用服务类

```java
@Autowired
private MultiFileDownloadService multiFileDownloadService;

// 下载文件并打包
List<String> urls = Arrays.asList("url1", "url2");
String zipPath = multiFileDownloadService.downloadFilesAndZip(urls, "my_files.zip", "/save/dir");

// 下载视频包
String zipPath = multiFileDownloadService.downloadVideoPackage(
    "video_url", "subtitle_url", "thumbnail_url", 
    "video_package.zip", "/save/dir"
);

// 下载字幕包
List<String> subtitleUrls = Arrays.asList("url1", "url2");
List<String> languages = Arrays.asList("en", "zh");
String zipPath = multiFileDownloadService.downloadSubtitlePackage(
    subtitleUrls, languages, "subtitles.zip", "/save/dir"
);
```

### 3. 使用Web API

#### 下载多个URL文件
```bash
POST /api/multi-download/download-urls
Content-Type: application/json

{
    "urls": [
        "https://example.com/file1.txt",
        "https://example.com/file2.jpg"
    ],
    "zipFileName": "my_download.zip",
    "saveDir": "/path/to/save"
}
```

#### 下载并指定文件名
```bash
POST /api/multi-download/download-with-names
Content-Type: application/json

{
    "urlFileNameMap": {
        "https://example.com/file1.txt": "document.txt",
        "https://example.com/file2.jpg": "image.jpg"
    },
    "zipFileName": "custom_files.zip",
    "saveDir": "/path/to/save"
}
```

#### 下载视频包
```bash
POST /api/multi-download/download-video-package
Content-Type: application/json

{
    "videoUrl": "https://example.com/video.mp4",
    "subtitleUrl": "https://example.com/subtitle.srt",
    "thumbnailUrl": "https://example.com/thumb.jpg",
    "zipFileName": "video_package.zip",
    "saveDir": "/path/to/save"
}
```

#### 下载生成的ZIP文件
```bash
GET /api/multi-download/download-file/{fileName}?filePath=/optional/path
```

## 配置参数

### 并发下载配置
- 默认最大线程数：5
- 下载超时时间：300秒（5分钟）
- 缓冲区大小：8192字节

### 文件路径配置
- 默认临时目录：系统临时目录
- 默认保存目录：系统临时目录
- ZIP文件编码：UTF-8

## 错误处理

### 常见错误情况
1. URL无效或无法访问
2. 网络连接超时
3. 磁盘空间不足
4. 文件权限问题

### 错误处理策略
- 部分文件下载失败时，继续打包成功的文件
- 详细的错误日志记录
- 自动清理临时文件
- 返回详细的错误信息

## 性能优化

### 并发下载
- 使用线程池进行并发下载
- 可配置最大并发数
- 避免过多线程导致系统负载过高

### 内存管理
- 流式处理，避免大文件占用过多内存
- 及时关闭IO流
- 自动清理临时文件

### 网络优化
- 使用缓冲流提高IO效率
- 支持断点续传（可扩展）
- 连接池复用（可扩展）

## 扩展功能

### 可扩展的功能点
1. 支持断点续传
2. 支持下载进度回调
3. 支持更多压缩格式（RAR、7Z等）
4. 支持加密ZIP文件
5. 支持云存储直传

### 自定义扩展示例
```java
// 自定义下载任务处理
public class CustomDownloadTask extends DownloadTask {
    private String customProperty;
    
    // 自定义逻辑
}

// 自定义下载处理器
public class CustomDownloadHandler {
    public void handleDownloadProgress(String url, long downloaded, long total) {
        // 处理下载进度
    }
}
```

## 注意事项

1. **文件大小限制**：注意单个文件和总文件大小限制
2. **网络稳定性**：确保网络连接稳定，避免下载中断
3. **磁盘空间**：确保有足够的磁盘空间存储文件
4. **并发控制**：合理设置并发线程数，避免过载
5. **临时文件清理**：定期清理临时文件，避免磁盘空间不足
6. **安全性**：验证URL的安全性，避免下载恶意文件

## 依赖说明

该功能依赖以下组件：
- `commons-compress`：ZIP文件处理
- `FileDownloader`：单文件下载
- `Spring Boot`：Web框架
- `Lombok`：代码简化
- `SLF4J`：日志记录

确保项目中已包含这些依赖。
