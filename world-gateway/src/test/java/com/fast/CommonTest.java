package com.fast;

import com.fast.service.member.FastMemberAccountFlowService;
import com.fast.service.member.FastMemberAccountService;
import com.fast.service.member.FastMemberOrderConsumeService;
import com.fast.service.member.FastMemberOrderRechargeService;
import com.fast.service.task.FastMemberTaskRechargeService;
import com.fast.service.upay.UpayOrderLogService;
import com.google.crypto.tink.apps.rewardedads.RewardedAdsVerifier;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = FastGatewaylication.class)
public class CommonTest {

    public static void main(String[] args) throws Exception {

    }


}
