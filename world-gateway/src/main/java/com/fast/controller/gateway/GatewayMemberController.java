/*
 * Powered By fast.up
 */
package com.fast.controller.gateway;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;

import com.fast.vo.PageVO;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.constant.StaticStr;
import com.fast.po.gateway.GatewayMemberPO;
import com.fast.service.gateway.GatewayMemberService;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/gatewayMember")
public class GatewayMemberController extends BaseController {

    @Autowired
    private GatewayMemberService gatewayMemberService;

    @ApiName(value = "gatewayMember-网关用户，查询列表",folder = {"gateway"})
    @ApiParamsIn({
            "appName:0:str:包简称",
            "memberId:0:str:用户id",
            "socialUserId:0:str:第三方用户ID",
            "email:0:str:email",
            "deviceId:0:str:设备唯一信息",
            "externalId:0:str:设备唯一信息",
            "deeplink:0:str:deeplink",
            "channelId:0:str:渠道id",
            "novelId:0:str:作品id",
            "originInfo:0:str:从第三方平台获取的原始信息",
            "adId:0:str:广告id",
            "pixelId:0:str:pixel_id",
            "ip:0:str:ip地址",
            "ua:0:str:ua信息",
            "os:0:str:系统",
            "lastRechargeId:0:str:最后充值id",
            "lastRechargeAt:0:str:最后充值时间",
            "updateTime:0:str:更新时间",
            "createTime:0:str:添加时间",
            "createTimeStr:0:str:创建时间区间（yyyy-MM-dd - yyyy-MM-dd）",
            "page:1:int:页码",
            "limit:1:int:每页条数",
    })
    @ApiParamsOut({
            "list 》id: id",
            "list 》appName: 包简称",
            "list 》memberId: 用户id",
            "list 》socialUserId: 第三方用户ID",
            "list 》email: email",
            "list 》deviceId: 设备唯一信息",
            "list 》externalId: 设备唯一信息",
            "list 》deeplink: deeplink",
            "list 》channelId: 渠道id",
            "list 》novelId: 作品id",
            "list 》originInfo: 从第三方平台获取的原始信息",
            "list 》adId: 广告id",
            "list 》pixelId: pixel_id",
            "list 》ip: ip地址",
            "list 》ua: ua信息",
            "list 》os: 系统",
            "list 》lastRechargeId: 最后充值id",
            "list 》lastRechargeAt: 最后充值时间",
            "list 》updateTime: 更新时间",
            "list 》createTime: 添加时间",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, @RequestBody GatewayMemberPO params, @RequestBody PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        return gatewayMemberService.queryPageList(params, pageVO);
    }

    @ApiName(value = "gatewayMember-网关用户，查询单个详情", folder = {"gateway"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "id: id",
            "appName: 包简称",
            "memberId: 用户id",
            "socialUserId: 第三方用户ID",
            "email: email",
            "deviceId: 设备唯一信息",
            "externalId: 设备唯一信息",
            "deeplink: deeplink",
            "channelId: 渠道id",
            "novelId: 作品id",
            "originInfo: 从第三方平台获取的原始信息",
            "adId: 广告id",
            "pixelId: pixel_id",
            "ip: ip地址",
            "ua: ua信息",
            "os: 系统",
            "lastRechargeId: 最后充值id",
            "lastRechargeAt: 最后充值时间",
            "updateTime: 更新时间",
            "createTime: 添加时间",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, @RequestBody GatewayMemberPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Long id = Long.valueOf(params.getEncryptionId());
        params.setId(id);
        GatewayMemberPO gatewayMember = gatewayMemberService.queryById(params);
        return ResultVO.success(gatewayMember);
    }

    @ApiName(value = "gatewayMember-网关用户，添加", folder = {"gateway"})
    @ApiParamsIn({
            "appName:1:str:包简称",
            "memberId:1:int:用户id",
            "socialUserId:1:str:第三方用户ID",
            "email:1:str:email",
            "deviceId:1:str:设备唯一信息",
            "externalId:1:str:设备唯一信息",
            "deeplink:1:str:deeplink",
            "channelId:1:int:渠道id",
            "novelId:1:int:作品id",
            "originInfo:1:str:从第三方平台获取的原始信息",
            "adId:1:str:广告id",
            "pixelId:1:str:pixel_id",
            "ip:1:str:ip地址",
            "ua:1:str:ua信息",
            "os:1:str:系统",
            "lastRechargeId:1:int:最后充值id",
            "lastRechargeAt:1:int:最后充值时间",
            "updateTime:1:int:更新时间",
            "createTime:1:int:添加时间",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated GatewayMemberPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = gatewayMemberService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "gatewayMember-网关用户，更新",folder = {"gateway"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "appName:1:str:包简称",
            "memberId:1:int:用户id",
            "socialUserId:1:str:第三方用户ID",
            "email:1:str:email",
            "deviceId:1:str:设备唯一信息",
            "externalId:1:str:设备唯一信息",
            "deeplink:1:str:deeplink",
            "channelId:1:int:渠道id",
            "novelId:1:int:作品id",
            "originInfo:1:str:从第三方平台获取的原始信息",
            "adId:1:str:广告id",
            "pixelId:1:str:pixel_id",
            "ip:1:str:ip地址",
            "ua:1:str:ua信息",
            "os:1:str:系统",
            "lastRechargeId:1:int:最后充值id",
            "lastRechargeAt:1:int:最后充值时间",
            "updateTime:1:int:更新时间",
            "createTime:1:int:添加时间",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody @Validated GatewayMemberPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Long id = Long.valueOf(params.getEncryptionId());
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = gatewayMemberService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "gatewayMember-网关用户，删除", folder = {"gateway"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, @RequestBody GatewayMemberPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Long id = Long.valueOf(params.getEncryptionId());
        params.setId(id);
        MethodVO methodVO = gatewayMemberService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }
    
}
