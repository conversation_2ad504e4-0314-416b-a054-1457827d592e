/*
 * Powered By fast.up
 */
package com.fast.controller.gateway;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.fast.constant.StaticYml;
import com.fast.po.gateway.GatewayMemberDataPO;
import com.fast.po.gateway.GatewayMemberPO;
import com.fast.po.member.FastMemberPO;
import com.fast.service.gateway.GatewayMemberService;
import com.fast.service.member.FastMemberService;
import com.fast.utils.IPUtil;
import com.fast.vo.gateway.EventPostEntity;
import com.fast.vo.gateway.LoadingPageEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;

import com.fast.vo.PageVO;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.constant.StaticStr;
import com.fast.po.gateway.GatewayEventPO;
import com.fast.service.gateway.GatewayEventService;


import com.facebook.ads.sdk.APIContext;
import com.facebook.ads.sdk.APIException;
import com.facebook.ads.sdk.serverside.ActionSource;
import com.facebook.ads.sdk.serverside.Content;
import com.facebook.ads.sdk.serverside.CustomData;
import com.facebook.ads.sdk.serverside.DeliveryCategory;
import com.facebook.ads.sdk.serverside.Event;
import com.facebook.ads.sdk.serverside.EventRequest;
import com.facebook.ads.sdk.serverside.EventResponse;
import com.facebook.ads.sdk.serverside.UserData;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/nologin/gatewayEvent")
public class GatewayEventController extends BaseController {

    @Autowired
    private GatewayEventService gatewayEventService;
    @Autowired
    private GatewayMemberService gatewayMemberService;
    @Autowired
    private FastMemberService fastMemberService;

    @ApiName(value = "gatewayEvent-网关埋点，查询列表",folder = {"gateway"})
    @ApiParamsIn({
            "gatewayMemberId:0:str:网关用户id",
            "deviceId:0:str:设备唯一信息",
            "ip:0:str:ip地址",
            "ua:0:str:ua信息",
            "adId:0:str:广告id",
            "adName:0:str:广告名称",
            "appName:0:str:包简称",
            "eventId:0:str:event_id",
            "eventName:0:str:event_name",
            "data:0:str:w2a_data",
            "customData:0:str:custom_data",
            "updateTime:0:str:更新时间",
            "createTime:0:str:添加时间",
            "createTimeStr:0:str:创建时间区间（yyyy-MM-dd - yyyy-MM-dd）",
            "page:1:int:页码",
            "limit:1:int:每页条数",
    })
    @ApiParamsOut({
            "list 》id: id",
            "list 》gatewayMemberId: 网关用户id",
            "list 》deviceId: 设备唯一信息",
            "list 》ip: ip地址",
            "list 》ua: ua信息",
            "list 》adId: 广告id",
            "list 》adName: 广告名称",
            "list 》appName: 包简称",
            "list 》eventId: event_id",
            "list 》eventName: event_name",
            "list 》data: w2a_data",
            "list 》customData: custom_data",
            "list 》updateTime: 更新时间",
            "list 》createTime: 添加时间",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, @RequestBody GatewayEventPO params, @RequestBody PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        return gatewayEventService.queryPageList(params, pageVO);
    }

    @ApiName(value = "gatewayEvent-网关埋点，查询单个详情", folder = {"gateway"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "id: id",
            "gatewayMemberId: 网关用户id",
            "deviceId: 设备唯一信息",
            "ip: ip地址",
            "ua: ua信息",
            "adId: 广告id",
            "adName: 广告名称",
            "appName: 包简称",
            "eventId: event_id",
            "eventName: event_name",
            "data: w2a_data",
            "customData: custom_data",
            "updateTime: 更新时间",
            "createTime: 添加时间",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, @RequestBody GatewayEventPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        GatewayEventPO gatewayEvent = gatewayEventService.queryById(params);
        return ResultVO.success(gatewayEvent);
    }

    @ApiName(value = "gatewayEvent-网关埋点，添加", folder = {"gateway"})
    @ApiParamsIn({
            "gatewayMemberId:1:int:网关用户id",
            "deviceId:1:str:设备唯一信息",
            "ip:1:str:ip地址",
            "ua:1:str:ua信息",
            "adId:1:str:广告id",
            "adName:1:str:广告名称",
            "appName:1:str:包简称",
            "eventId:1:str:event_id",
            "eventName:1:str:event_name",
            "data:1:str:w2a_data",
            "customData:1:str:custom_data",
            "updateTime:1:int:更新时间",
            "createTime:1:int:添加时间",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated GatewayEventPO params) {
        SessionVO sessionVO = getSessionVO(request);

        MethodVO methodVO = gatewayEventService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "gatewayEvent-网关埋点，更新",folder = {"gateway"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "gatewayMemberId:1:int:网关用户id",
            "deviceId:1:str:设备唯一信息",
            "ip:1:str:ip地址",
            "ua:1:str:ua信息",
            "adId:1:str:广告id",
            "adName:1:str:广告名称",
            "appName:1:str:包简称",
            "eventId:1:str:event_id",
            "eventName:1:str:event_name",
            "data:1:str:w2a_data",
            "customData:1:str:custom_data",
            "updateTime:1:int:更新时间",
            "createTime:1:int:添加时间",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody @Validated GatewayEventPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);

        MethodVO methodVO = gatewayEventService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "gatewayEvent-网关埋点，删除", folder = {"gateway"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, @RequestBody GatewayEventPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = gatewayEventService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 自埋点数据上报
     * @return
     */
    @ApiName(value = "eventPost-埋点提交", folder = {"gateway"})
    @ApiParamsIn({
            "fbTest:0:str:测试id",
            "event>>eventId:1:str:埋点id（获取fb app端埋点的eventId）",
            "event>>eventName:1:str:埋点名称",
            "customData:0:str:自定义数据，不同埋点上报不同参数，以下为添加购物车与购物事件参数",
            "customData>>currency:1:str:例:充值币种，美元为USD",
            "customData>>value:1:str:充值金额",
            "customData>>content_type:1:str:内容类型，固定值product",
            "customData>>content_ids:1:str:内容id",
            "userData>>memberId:1:str:App端用户id",
            "userData>>deviceId:1:str:App端设备id",
            "userData>>clientIpAddress:1:str:ip地址",
            "userData>>clientUserAgent:1:str:取手机默认浏览器UserAgent",
            "w2aDataEncrypt:1:str:web2app信息，剪切板存在则直接获取，否则根据ip ua接口获取"
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/eventPost")
    public ResultVO eventPost(HttpServletRequest request, @RequestBody EventPostEntity params) {
        if (StaticYml.FB_PIXEL_ID == null || StaticYml.FB_PIXEL_ID.equals("0")) {
            return ResultVO.error("FB_PIXEL_ID error");
        }
        if (StaticYml.FB_TOKEN == null || StaticYml.FB_TOKEN.equals("token")) {
            return ResultVO.error("FB_TOKEN error");
        }
        if (params.getEvent() == null) {
            return ResultVO.error("event error");
        }
        if (StrUtil.isEmpty(params.getEvent().getEventId())) {
            return ResultVO.error("eventId error");
        }
        if (StrUtil.isEmpty(params.getEvent().getEventName())) {
            return ResultVO.error("eventName error");
        }
        if (StrUtil.isEmpty(params.getW2aDataEncrypt()) || params.getW2aDataEncrypt().length() < 4) {
            return ResultVO.error("w2a_data_encrypt error");
        }
        if (!StrUtil.substring(params.getW2aDataEncrypt(), 0,4).equals("w2a:")) {
            return ResultVO.error("w2a_data_encrypt error!");
        }

        String w2aData = StrUtil.fromCharCode(params.getW2aDataEncrypt().replace("w2a:", ""));
        LoadingPageEntity loadingPageEntity = JSON.parseObject(w2aData, LoadingPageEntity.class);

        APIContext context = new APIContext(StaticYml.FB_TOKEN).enableDebug(true);
        context.setLogger(System.out);

        UserData userData = new UserData()
                .clientIpAddress(IPUtil.getIpAddr(request))
                .clientUserAgent(loadingPageEntity.getUserData().getClientUserAgent())
                .fbc(loadingPageEntity.getUserData().getFbc())
                .fbp(loadingPageEntity.getUserData().getFbp());
        // 根据用户id获取用户邮箱与手机号
        if (params.getUserData() != null && params.getUserData().getMemberId() != null) {
            FastMemberPO member = fastMemberService.queryById(params.getUserData().getMemberId());
            if (member != null) {
                if (member.getEmail() != null) {
                    userData.email(member.getEmail());
                }
                if (member.getPhone() != null) {
                    userData.phone(member.getPhone());
                }
                if (member.getLoginType() != null && member.getLoginType() == 5) {
                    userData.fbLoginId(member.getUid());
                }

                /**
                 * 更新网关，用户id
                 */
                if (loadingPageEntity.getGatewayMemberId() != null) {
                    GatewayMemberPO gatewayMemberPO = new GatewayMemberPO();
                    gatewayMemberPO.setId(loadingPageEntity.getGatewayMemberId());
                    gatewayMemberPO.setMemberId(member.getId());
                    gatewayMemberPO.setSocialUserId(member.getOpenid());
                    gatewayMemberService.update(gatewayMemberPO);
                }
            }
        }

        CustomData customData = new CustomData();
        if (params.getCustomData() != null) {
            customData.customProperties(params.getCustomData());
        }

        Event event = new Event();
        event.eventName(params.getEvent().getEventName())
            .eventTime(DateUtil.getTimeNowUnix())
            .userData(userData)
            .customData(customData)
            .eventSourceUrl(loadingPageEntity.getEventSourceUrl())
            .actionSource(ActionSource.website);

        EventRequest eventRequest = new EventRequest(StaticYml.FB_PIXEL_ID, context);
        if (!StrUtil.isEmpty(params.getFbTest())) {
            eventRequest.testEventCode(params.getFbTest());
        } else {
            if (!StrUtil.isEmpty(loadingPageEntity.getFbTest())) {
                eventRequest.testEventCode(loadingPageEntity.getFbTest());
            }
        }
        eventRequest.addDataItem(event);

        String fbTraceId = "";
        try {
            EventResponse response = eventRequest.execute();
            fbTraceId = response.getFbTraceId();
            System.out.println(String.format("Standard API response : %s ", response));
        } catch (APIException e) {
            e.printStackTrace();
            return ResultVO.error("facebook report error");
        }

        /**
         * 埋点入库
         * http://loc-fast-world.601book.com/index.html?linkId=8888&_fbc=1&_fbq=2&ad_id=123456&ad_name=223456&fb_test=TEST82437
         */
        String ua = "";
        GatewayEventPO gatewayEventPO = new GatewayEventPO();
        gatewayEventPO.setGatewayMemberId(loadingPageEntity.getGatewayMemberId());
        if (params.getUserData() != null) {
            if (StrUtil.isNotEmpty(params.getUserData().getDeviceId())) {
                gatewayEventPO.setDeviceId(params.getUserData().getDeviceId());
            }
            if (StrUtil.isNotEmpty(params.getUserData().getClientUserAgent())) {
                ua = params.getUserData().getClientUserAgent();
            }
        } else {
            if (StrUtil.isNotEmpty(loadingPageEntity.getUserData().getClientUserAgent())) {
                ua = loadingPageEntity.getUserData().getClientUserAgent();
            }
        }

        gatewayEventPO.setIp(IPUtil.getIpAddr(request));
        gatewayEventPO.setUa(StrUtil.getGatewayUa(ua));
        gatewayEventPO.setAdId(loadingPageEntity.getAdSource().getAdId());
        gatewayEventPO.setAdName(loadingPageEntity.getAdSource().getAdName());
        gatewayEventPO.setAppName(loadingPageEntity.getUserData().getAppName());
        gatewayEventPO.setEventId(params.getEvent().getEventId());
        gatewayEventPO.setEventName(params.getEvent().getEventName());
        gatewayEventPO.setData(toJSONString(params));
        gatewayEventPO.setCustomData(toJSONString(params.getCustomData()));
        gatewayEventPO.setFbTraceId(fbTraceId);
        gatewayEventService.insert(gatewayEventPO);

        return ResultVO.success();
    }
}
