/*
 * Powered By fast.up
 */
package com.fast.controller.gateway;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;

import com.fast.vo.PageVO;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.constant.StaticStr;
import com.fast.po.gateway.GatewayMemberDataPO;
import com.fast.service.gateway.GatewayMemberDataService;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/gatewayMemberData")
public class GatewayMemberDataController extends BaseController {

    @Autowired
    private GatewayMemberDataService gatewayMemberDataService;

    @ApiName(value = "gatewayMemberData-埋点原始数据，查询列表",folder = {"gateway"})
    @ApiParamsIn({
            "gatewayMemberId:0:str:网关用户信息id",
            "data:0:str:w2a_data",
            "updatedAt:0:str:更新时间",
            "createdAt:0:str:添加时间",
            "createTimeStr:0:str:创建时间区间（yyyy-MM-dd - yyyy-MM-dd）",
            "page:1:int:页码",
            "limit:1:int:每页条数",
    })
    @ApiParamsOut({
            "list 》id: id",
            "list 》gatewayMemberId: 网关用户信息id",
            "list 》data: w2a_data",
            "list 》updatedAt: 更新时间",
            "list 》createdAt: 添加时间",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, @RequestBody GatewayMemberDataPO params, @RequestBody PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        return gatewayMemberDataService.queryPageList(params, pageVO);
    }

    @ApiName(value = "gatewayMemberData-埋点原始数据，查询单个详情", folder = {"gateway"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "id: id",
            "gatewayMemberId: 网关用户信息id",
            "data: w2a_data",
            "updatedAt: 更新时间",
            "createdAt: 添加时间",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, @RequestBody GatewayMemberDataPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        GatewayMemberDataPO gatewayMemberData = gatewayMemberDataService.queryById(params);
        return ResultVO.success(gatewayMemberData);
    }

    @ApiName(value = "gatewayMemberData-埋点原始数据，添加", folder = {"gateway"})
    @ApiParamsIn({
            "gatewayMemberId:1:int:网关用户信息id",
            "data:1:str:w2a_data",
            "updatedAt:1:int:更新时间",
            "createdAt:1:int:添加时间",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated GatewayMemberDataPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = gatewayMemberDataService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "gatewayMemberData-埋点上报原始数据，更新",folder = {"gateway"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "gatewayMemberId:1:int:网关用户信息id",
            "data:1:str:w2a_data",
            "updatedAt:1:int:更新时间",
            "createdAt:1:int:添加时间",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody @Validated GatewayMemberDataPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = gatewayMemberDataService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "gatewayMemberData-埋点原始数据，删除", folder = {"gateway"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, @RequestBody GatewayMemberDataPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = gatewayMemberDataService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }
    
}
