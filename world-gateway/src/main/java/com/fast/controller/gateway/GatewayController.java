package com.fast.controller.gateway;

import com.fast.annotation.ApiDesc;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.mapper.gateway.GatewayMemberMapper;
import com.fast.po.gateway.GatewayLogPO;
import com.fast.po.gateway.GatewayMemberDataPO;
import com.fast.po.gateway.GatewayMemberPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.gateway.GatewayLogService;
import com.fast.service.gateway.GatewayMemberDataService;
import com.fast.service.gateway.GatewayMemberService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.DateUtil;
import com.fast.utils.IPUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.gateway.LoadingPageEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Map;

@RestController
@RequestMapping("/nologin/gateway")
public class GatewayController extends BaseController {

    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private GatewayMemberService gatewayMemberService;
    @Autowired
    private GatewayMemberDataService gatewayMemberDataService;
    @Autowired
    private GatewayLogService gatewayLogService;

    /**
     * 获取渠道信息
     * @param linkId
     * @return
     */
    @ApiName(value = "gateway-获取渠道信息", folder = {"gateway"})
    @ApiParamsIn({
            "linkId:1:str:渠道ID",
    })
    @ApiParamsOut({
            "results>>id:渠道id",
            "results>>linkName:渠道名",
            "results>>promoteLink:广告link（deeplink）",
            "results>>dramaId:剧Id",
            "results>>linkTitle:剧名"
    })
    @PostMapping("/channel")
    public ResultVO getLink(Integer linkId) {
        if (StrUtil.isBlank(linkId)) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        FastLinkPO fastLinkPO = new FastLinkPO();
        fastLinkPO.setId(linkId);
        FastLinkPO fastLink = fastLinkService.queryById(fastLinkPO);
        return ResultVO.success(fastLink);
    }

    /**
     * 用户打开落地页，数据上报
     * @return
     */
    @ApiName(value = "gateway-落地页初始化数据", folder = {"gateway"})
    @ApiParamsIn({
            "actionSource:1:str:来源 固定值，web、app",
            "eventSourceUrl:1:str:埋点来源url",
            "adSource>>media:1:str:媒体名meta（fb）、google、tiktok",
            "adSource>>adId:1:str:广告id",
            "adSource>>adName:1:str:广告名称",
            "adSource>>pixelId:1:str:像素埋点id",
            "userData>>fbc:1:str:fbc facebook自动携带",
            "userData>>fbp:1:str:fbp facebook自动携带",
            "userData>>clientIpAddress:1:str:ip",
            "userData>>clientUserAgent:1:str:ua",
            "userData>>externalId:1:str:",
            "userData>>em:1:str:email",
            "userData>>fbLoginId:1:str:fb登录id",
            "userData>>country:1:str:国家",
            "userData>>linkId:1:str:渠道id",
            "userData>>novelId:1:str:dramaId 剧id",
            "userData>>deeplink:1:str:深链"
    })
    @ApiParamsOut({
            "results>>client_ip_address:ip 地址",
            "results>>w2a_data_encrypt:web 端页面初始化数据"
    })
    @PostMapping("/loadingPageSave")
    public ResultVO loadingPageSave(HttpServletRequest request, @RequestBody LoadingPageEntity params) {
        if (StrUtil.isBlank(params)) {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        if(params.getUserData() == null || params.getAdSource() == null) {
            return ResultVO.error("userData error");
        }
        if(params.getUserData().getFbc() == null) {
            return ResultVO.error("fbc error");
        }
        if(params.getUserData().getFbp() == null) {
            return ResultVO.error("fbp error");
        }
        if(params.getAdSource().getPixelId() == null) {
            return ResultVO.error("pixel_id error");
        }
        if(params.getUserData().getClientUserAgent() == null) {
            return ResultVO.error("client_user_agent error");
        }
        String userAgent = params.getUserData().getClientUserAgent();

        String os = "";
        String osInfo = StrUtil.getGatewayUa(userAgent);
        // 3. 判断是否为 Android 或 Adr
        if (userAgent.contains("Android") || userAgent.contains("Adr")) {
            os = "android";
        }
        if (userAgent.contains("iPhone") || userAgent.contains("Mac")) {
            os = "ios";
        }

        String ip = IPUtil.getIpAddr(request);
        GatewayMemberPO gatewayMemberPO = new GatewayMemberPO();
        gatewayMemberPO.setAppName(params.getUserData().getAppName());
        gatewayMemberPO.setEmail(params.getUserData().getEm());
        gatewayMemberPO.setExternalId(params.getUserData().getExternalId());
        gatewayMemberPO.setDeeplink(params.getUserData().getDeepLink());
        if (params.getUserData().getLinkId() != null) {
            gatewayMemberPO.setLinkId(Integer.valueOf(params.getUserData().getLinkId()));
        }
        if (params.getUserData().getDramaId() != null) {
            gatewayMemberPO.setDramaId(Integer.valueOf(params.getUserData().getDramaId()));
        }
        gatewayMemberPO.setAdId(params.getAdSource().getAdId());
        gatewayMemberPO.setPixelId(params.getAdSource().getPixelId());
        gatewayMemberPO.setIp(ip);
        gatewayMemberPO.setUa(osInfo);
        gatewayMemberPO.setOs(os);
        gatewayMemberPO.setLastRechargeId(0);
        gatewayMemberPO.setLastRechargeAt(0);
        gatewayMemberPO.setCreateTime(DateUtil.getNowDate());
        gatewayMemberPO.setUpdateTime(DateUtil.getNowDate());
        MethodVO methodVO = gatewayMemberService.setGatewayMember(gatewayMemberPO, params);

        if (StrUtil.isNotEmpty(params.getUserData().getFbclid())) {
            try {
                // 记录log
                GatewayLogPO gatewayLogPO = new GatewayLogPO();
                gatewayLogPO.setFbclid(params.getUserData().getFbclid());
                GatewayLogPO gatewayLog = gatewayLogService.queryOne(gatewayLogPO);

                if (params.getUserData().getDramaId() != null) {
                    gatewayLogPO.setDramaId(Integer.valueOf(params.getUserData().getDramaId()));
                }
                gatewayLogPO.setPixelId(params.getAdSource().getPixelId());
                gatewayLogPO.setAdId(params.getAdSource().getAdId());
                gatewayLogPO.setAdName(params.getAdSource().getAdName());
                gatewayLogPO.setIp(ip);
                gatewayLogPO.setUa(osInfo);
                gatewayLogPO.setOs(os);
                if (params.getUserData().getLinkId() != null) {
                    gatewayLogPO.setLinkId(Integer.valueOf(params.getUserData().getLinkId()));
                }
                gatewayLogPO.setDeeplink(params.getUserData().getDeepLink());
                gatewayLogPO.setExternalId(params.getUserData().getExternalId());
                gatewayLogPO.setFbc(params.getUserData().getFbc());
                gatewayLogPO.setFbp(params.getUserData().getFbp());
                gatewayLogPO.setFbclid(params.getUserData().getFbclid());
                gatewayLogPO.setUpdateTime(DateUtil.getNowDate());
                if (gatewayLog != null) {
                    gatewayLogPO.setId(gatewayLog.getId());
                    gatewayLogService.update(gatewayLogPO);
                } else {
                    gatewayLogPO.setCreateTime(DateUtil.getNowDate());
                    gatewayLogService.insert(gatewayLogPO);
                }
            } catch (Exception e) {
                log.error("记录log失败", e);
            }
        }

        return ResultVO.fromMethodVO1(methodVO);
    }

    /**
     * 用户打开落地页，数据上报
     * @return
     */
    @ApiName(value = "gateway-获取投放初始化信息", folder = {"gateway"})
    @ApiParamsIn({
            "****:1:str:head头需包含User-Agent，取手机默认浏览器ua头",
            "memberId:1:str:用户id",
            "deviceId:1:str:设备id"
    })
    @ApiParamsOut({
            "results>>client_ip_address:ip 地址",
            "results>>w2a_data_encrypt:web 端页面初始化数据"
    })
    @PostMapping("/getLoadingPage")
    public ResultVO getLoadingPage(HttpServletRequest request, @RequestBody Map<String, String> params) {
        String memberId = params.getOrDefault("memberId", "");
        String deviceId = params.getOrDefault("deviceId", "");
        String referrer = params.getOrDefault("referrer", "");

        String ip = IPUtil.getIpAddr(request);
        String userAgent = request.getHeader("User-Agent");
        String ua = StrUtil.getGatewayUa(userAgent);

        GatewayMemberPO gatewayMember = new GatewayMemberPO();;
        if (StrUtil.isNotEmpty(referrer)) {
            GatewayMemberPO gatewayMemberPO = new GatewayMemberPO();
            gatewayMemberPO.setExternalId(referrer);
            gatewayMember = gatewayMemberService.queryOne(gatewayMemberPO);
        }

        if (gatewayMember == null || gatewayMember.getId() == null){
            GatewayMemberPO gatewayMemberPO = new GatewayMemberPO();
            gatewayMemberPO.setIp(ip);
            gatewayMemberPO.setUa(ua);
            gatewayMemberPO.setCreateTimeS(DateUtil.addDays(DateUtil.getNowDate(), -7));
            gatewayMember = gatewayMemberService.queryOne(gatewayMemberPO);
        }

        if (gatewayMember != null && gatewayMember.getId() != null) {
            // 更新网关用户 memberId
            if (StrUtil.isNotEmpty(memberId)) {
                GatewayMemberPO gatewayMemberPO1 = new GatewayMemberPO();
                gatewayMemberPO1.setId(gatewayMember.getId());
                gatewayMemberPO1.setMemberId(Long.valueOf(memberId));
                gatewayMemberPO1.setDeviceId(deviceId);
                gatewayMemberService.update(gatewayMemberPO1);
            }

            // 获取网关用户初始化数据
            GatewayMemberDataPO gatewayMemberDataPO = new GatewayMemberDataPO();
            gatewayMemberDataPO.setGatewayMemberId(gatewayMember.getId());
            GatewayMemberDataPO gatewayMemberData = gatewayMemberDataService.queryOne(gatewayMemberDataPO);
            if (gatewayMemberData != null && gatewayMemberData.getData() != null) {
                Map<String, String> data = new HashMap<>();
                data.put("client_ip_address", ip);
                data.put("w2a_data_encrypt", "w2a:" + StrUtil.charCodeAt(gatewayMemberData.getData()));
                return ResultVO.success(data);
            } else {
                return ResultVO.error("No data available!!");
            }
        } else {
            return ResultVO.error("No data available!");
        }
    }

    @ApiName(value = "gateway-w2a解密", folder = {"gateway"})
    @ApiParamsIn({
            "w2a_data_encrypt:1:str:加密数据"
    })
    @ApiParamsOut({
            "message:解密后的数据"
    })
    @PostMapping("/w2aDecode")
    public ResultVO w2aDecode(HttpServletRequest request, @RequestBody Map<String, String> params) {
        String w2a = params.getOrDefault("w2a_data_encrypt", "");
        if (StrUtil.isBlank(w2a)) {
            return ResultVO.error("No data available!!");
        }
        return ResultVO.error(StrUtil.fromCharCode(w2a));
    }
}
