package com.fast;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

import com.fast.framework.config.ResourcesConfig;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableAsync// 开启异步调用
public class FastGatewaylication {

    public static void main(String[] args) {
        SpringApplication.run(FastGatewaylication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  系统[web2app-gateway]启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}
