package com.fast.quartz;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;

import java.util.UUID;
import java.util.function.Consumer;

/**
 * Created by <PERSON> on 2025/04/30.
 */
@Slf4j
public class TaskExecutor {

    public static void execute(String taskName, Consumer<String> consumer) {
        if (MDC.get("traceId") == null) {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            String traceId = uuid.substring(0, 16) + "." + uuid.substring(16);
            MDC.put("traceId", traceId);
        }
        log.info("{}定时任务-开始执行", taskName);
        try {
            long start = System.currentTimeMillis();
            consumer.accept(taskName);
            long end = System.currentTimeMillis();
            log.info("{}定时任务-执行完毕，耗时: {}s", taskName, (end - start) / 1000f);
        } catch (Exception e) {
            log.error("{}定时任务-执行失败: {}", taskName, ExceptionUtils.getStackTrace(e));
        } finally {
            MDC.clear();
        }
    }

}
