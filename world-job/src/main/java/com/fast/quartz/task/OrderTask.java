package com.fast.quartz.task;

import com.fast.mapper.upay.UpayOrderLogMapper;
import com.fast.po.upay.UpayOrderLog;
import com.fast.quartz.TaskExecutor;
import com.fast.service.upay.UpayOrderLogService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 定时任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderTask {

    @Autowired
    private UpayOrderLogService upayOrderLogService;

    @Autowired
    private UpayOrderLogMapper upayOrderLogMapper;

    /**
     * 分页大小常量
     */
    private static final int PAGE_SIZE = 50;

    /**
     * 成功状态常量
     */
    private static final int SUCCESS_STATE = 2;

    /**
     * 填充Google商品信息定时任务
     * 每5分钟执行一次，处理状态为成功的订单
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void job_1() {
        TaskExecutor.execute("填充Google商品信息", taskName -> fillGoogleProductInfoTask());
    }

    /**
     * 填充Google商品信息的具体业务逻辑
     */
    private void fillGoogleProductInfoTask() {
        // 查询状态为成功的订单
        UpayOrderLog queryCondition = new UpayOrderLog();
        queryCondition.setState(SUCCESS_STATE);
        queryCondition.setRealAmt(BigDecimal.ZERO);

        // 查询总数
        int totalCount = upayOrderLogService.queryCount(queryCondition);
        if (totalCount == 0) {
            log.info("没有需要填充Google商品信息的订单");
            return;
        }

        // 计算总页数
        int totalPages = (totalCount + PAGE_SIZE - 1) / PAGE_SIZE;
        log.info("开始填充Google商品信息，总共{}条订单需要处理，每页{}条记录，总共需要处理{}页", totalCount, PAGE_SIZE, totalPages);

        // 分页处理
        for (int currentPage = 1; currentPage <= totalPages; currentPage++) {
            log.info("正在处理第{}/{}页，每页{}条记录", currentPage, totalPages, PAGE_SIZE);

            // 设置分页参数
            PageHelper.startPage(currentPage, PAGE_SIZE);
            List<UpayOrderLog> orderList = upayOrderLogMapper.queryList(queryCondition);

            if (CollUtil.isEmpty(orderList)) {
                log.warn("第{}页查询结果为空，跳过处理", currentPage);
                continue;
            }

            // 处理当前页的所有订单
            orderList.forEach(order -> {
                if (StrUtil.isBlank(order.getRegion())) {
                    upayOrderLogService.fillGoogleProductInfo(order);
                }
            });

            log.info("第{}页处理完成，处理{}条记录", currentPage, orderList.size());
        }
    }

}
