// package com.fast.quartz.task;
//
// import com.fast.quartz.TaskExecutor;
// import com.fast.service.make.video.VideoProcessServiceV2;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.scheduling.annotation.Scheduled;
// import org.springframework.stereotype.Component;
//
// /**
//  * 定时任务
//  *
//  * <AUTHOR>
//  */
// @Component
// @Slf4j
// public class VideoProcessTask {
//
//     @Autowired
//     private VideoProcessServiceV2 videoProcessServiceV2;
//
//     /**
//      * 分页大小常量
//      */
//     private static final int PAGE_SIZE = 50;
//
//     /**
//      * 成功状态常量
//      */
//     private static final int SUCCESS_STATE = 2;
//
//     @Scheduled(cron = "0 0/5 * * * ?")
//     public void job_1() {
//         TaskExecutor.execute("补齐视频处理任务数据", taskName -> videoProcessServiceV2.fillForTask(null));
//     }
//
// }
