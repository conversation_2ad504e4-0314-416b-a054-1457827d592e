package com.fast;

import com.fast.utils.DateUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableAsync// 开启异步调用
@EnableScheduling
public class FastJobApplication {

    public static void main(String[] args) {
        SpringApplication.run(FastJobApplication.class, args);
        System.out.println(DateUtil.getNowTime07Str());
        System.out.println("(♥◠‿◠)ﾉﾞ  系统[fast-job]启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }

}
