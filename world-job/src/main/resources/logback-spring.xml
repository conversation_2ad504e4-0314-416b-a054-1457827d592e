<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <!-- 日志存放路径 -->
    <springProperty scop="context" name="spring.application.name" source="spring.application.name" defaultValue=""/>
    <property name="PROJECT_NAME" value="${spring.application.name}"/>
    <property name="LOG_PATCH" value="logs/${PROJECT_NAME}"/>
    <!-- 日志输出格式 -->
    <property name="LOG_PATTERN" value="%d{HH:mm:ss.SSS} [%t] %-5level [%X{traceId}] %c{80}:%L - %m%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%red(%d{HH:mm:ss.SSS}) %green([%t]) %highlight(%-5level) [%X{traceId}] %boldMagenta(%c{80}:%L) - %cyan(%m%n)
            </pattern>
        </encoder>
    </appender>

    <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATCH}/info.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG_PATCH}/zip_info/%d{yyyy-MM, aux}/info-${PROJECT_NAME}-%d{yyyy-MM-dd}.%i.log.zip
            </fileNamePattern>
            <MaxFileSize>200MB</MaxFileSize>
            <totalSizeCap>25GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATCH}/error.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG_PATCH}/zip_error/%d{yyyy-MM, aux}/error-${PROJECT_NAME}-%d{yyyy-MM-dd}.%i.log.zip
            </fileNamePattern>
            <MaxFileSize>200MB</MaxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level> <!-- 只打印错误日志 -->
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <logger name="com.fast.mapper" level="error"/>
    <logger name="org.springframework" level="warn"/>

    <springProfile name="prod">
        <logger name="com.fast.mapper" level="info"/>
        <root level="info">
            <appender-ref ref="info"/>
            <appender-ref ref="error"/>
        </root>
    </springProfile>

    <springProfile name="test">
        <root level="info">
            <appender-ref ref="info"/>
            <appender-ref ref="error"/>
        </root>
    </springProfile>

    <springProfile name="dev">
        <root level="info">
            <appender-ref ref="console"/>
        </root>
    </springProfile>

</configuration>
