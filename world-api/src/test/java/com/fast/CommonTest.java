package com.fast;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fast.enums.LanguageEnum;
import com.fast.po.common.FastCornerI18nPO;
import com.fast.po.common.FastTagI18nPO;
import com.fast.po.drama.FastDramaI18nPO;
import com.fast.service.common.*;
import com.fast.service.drama.FastDramaI18nService;
import com.fast.service.drama.FastDramaService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = FastApiApplication.class)
public class CommonTest {
    @Autowired
    private FastDramaService fastDramaService;
    @Autowired
    private FastDramaI18nService fastDramaI18nService;
    @Autowired
    private FastTagService fastTagService;
    @Autowired
    private FastTagI18nService fastTagI18nService;
    @Autowired
    private FastCornerService fastCornerService;
    @Autowired
    private FastCornerI18nService fastCornerI18nService;
    @Autowired
    private MailService mailService;

    @Test
    public void test() throws Exception {
        // List<FastDramaPO> dramas = fastDramaService.queryList(new FastDramaPO());
        // for (FastDramaPO fastDramaPO : dramas) {
        //     insertDramaI18n(fastDramaPO.getId(),
        //             fastDramaPO.getDramaName(),
        //             fastDramaPO.getDramaIntroduce(),
        //             fastDramaPO.getDramaCover(),
        //             fastDramaPO.getDramaCover(),
        //             LanguageEnum.SIMPLIFIED_CHINESE);
        //
        //     String name = translate(fastDramaPO.getDramaName(), "zh-CHS", "en");
        //     System.err.println(name);
        //     Thread.sleep(1000);
        //
        //     String introduce = translate(fastDramaPO.getDramaIntroduce(), "zh-CHS", "en");
        //     System.err.println(introduce);
        //     Thread.sleep(1000);
        //
        //     insertDramaI18n(fastDramaPO.getId(),
        //             name,
        //             introduce,
        //             fastDramaPO.getDramaCover(),
        //             fastDramaPO.getDramaCover(),
        //             LanguageEnum.ENGLISH);
        //
        //     name = translate(fastDramaPO.getDramaName(), "zh-CHS", "es");
        //     System.err.println(name);
        //     Thread.sleep(1000);
        //
        //     introduce = translate(fastDramaPO.getDramaIntroduce(), "zh-CHS", "es");
        //     System.err.println(introduce);
        //     Thread.sleep(1000);
        //
        //     insertDramaI18n(fastDramaPO.getId(),
        //             name,
        //             introduce,
        //             fastDramaPO.getDramaCover(),
        //             fastDramaPO.getDramaCover(),
        //             LanguageEnum.SPANISH);
        // }
        //
        // List<FastTagPO> tags = fastTagService.queryList(new FastTagPO());
        // for (FastTagPO fastTagPO : tags) {
        //     insertTagI18n(fastTagPO.getId(), fastTagPO.getTagName(), LanguageEnum.SIMPLIFIED_CHINESE);
        //
        //     String name = translate(fastTagPO.getTagName(), "zh-CHS", "en");
        //     System.err.println(name);
        //     Thread.sleep(1000);
        //     insertTagI18n(fastTagPO.getId(), name, LanguageEnum.ENGLISH);
        //
        //     name = translate(fastTagPO.getTagName(), "zh-CHS", "es");
        //     System.err.println(name);
        //     Thread.sleep(1000);
        //     insertTagI18n(fastTagPO.getId(), name, LanguageEnum.SPANISH);
        // }
        //
        // List<FastCornerPO> corners = fastCornerService.queryList(new FastCornerPO());
        // for (FastCornerPO fastCornerPO : corners) {
        //     insertCornerI18n(fastCornerPO.getId(), fastCornerPO.getCornerName(), LanguageEnum.SIMPLIFIED_CHINESE);
        //
        //     String name = translate(fastCornerPO.getCornerName(), "zh-CHS", "en");
        //     System.err.println(name);
        //     Thread.sleep(1000);
        //     insertCornerI18n(fastCornerPO.getId(), name, LanguageEnum.ENGLISH);
        //
        //     name = translate(fastCornerPO.getCornerName(), "zh-CHS", "es");
        //     System.err.println(name);
        //     Thread.sleep(1000);
        //     insertCornerI18n(fastCornerPO.getId(), name, LanguageEnum.SPANISH);
        // }

        mailService.sendTextMail("<EMAIL>", "test", "验证码：123456");
    }

    private void insertDramaI18n(Integer dramaId, String name, String introduce, String cover, String coverHor, LanguageEnum language) {
        FastDramaI18nPO i18nPO3 = new FastDramaI18nPO();
        i18nPO3.setDramaId(dramaId);
        i18nPO3.setLanguageCode(language.getCode());
        i18nPO3.setName(name);
        i18nPO3.setIntroduce(introduce);
        i18nPO3.setCover(cover);
        i18nPO3.setCoverHor(coverHor);
        fastDramaI18nService.insert(i18nPO3);
    }

    private void insertTagI18n(Integer tagId, String name, LanguageEnum language) {
        FastTagI18nPO i18nPO3 = new FastTagI18nPO();
        i18nPO3.setTagId(tagId);
        i18nPO3.setLanguageCode(language.getCode());
        i18nPO3.setName(name);
        fastTagI18nService.insert(i18nPO3);
    }

    private void insertCornerI18n(Integer cornerId, String name, LanguageEnum language) {
        FastCornerI18nPO i18nPO3 = new FastCornerI18nPO();
        i18nPO3.setCornerId(cornerId);
        i18nPO3.setLanguageCode(language.getCode());
        i18nPO3.setName(name);
        fastCornerI18nService.insert(i18nPO3);
    }

    // {
    //     "tSpeakUrl": "https://openapi.youdao.com/ttsapi?q=%E5%BE%85%E7%BF%BB%E8%AD%AF%E6%96%87%E6%9C%AC&langType=zh-CHT&sign=7AADD6423160F4CF2076D1B8D799625E&salt=1747201724012&voice=4&format=mp3&appKey=3a5ce6cdcced0339&ttsVoiceStrict=false&osType=api",
    //     "requestId": "cd5e38ad-e603-412b-8997-3bc23c858fec",
    //     "query": "待翻译文本",
    //     "isDomainSupport": false,
    //     "translation": ["待翻譯文本"],
    //     "mTerminalDict": {"url": "https://m.youdao.com/m/result?lang=zh-CHS&word=%E5%BE%85%E7%BF%BB%E8%AF%91%E6%96%87%E6%9C%AC"},
    //     "errorCode": "0",
    //     "dict": {"url": "yddict://m.youdao.com/dict?le=eng&q=%E5%BE%85%E7%BF%BB%E8%AF%91%E6%96%87%E6%9C%AC"},
    //     "webdict": {"url": "http://mobile.youdao.com/dict?le=eng&q=%E5%BE%85%E7%BF%BB%E8%AF%91%E6%96%87%E6%9C%AC"},
    //     "l": "zh-CHS2zh-CHT",
    //     "isWord": false,
    //     "speakUrl": "https://openapi.youdao.com/ttsapi?q=%E5%BE%85%E7%BF%BB%E8%AF%91%E6%96%87%E6%9C%AC&langType=zh-CHS&sign=E3C3545AD1174D35954FE79AC1202263&salt=1747201724012&voice=4&format=mp3&appKey=3a5ce6cdcced0339&ttsVoiceStrict=false&osType=api"
    // }
    private String translate(String q, String from, String to) throws NoSuchAlgorithmException {
        // 添加请求参数
        Map<String, String[]> params = new HashMap<>();
        params.put("q", new String[]{q});
        params.put("from", new String[]{from});
        params.put("to", new String[]{to});
        // 添加鉴权相关参数
        AuthV3Util.addAuthParams("xxx", "xxx", params);
        // 请求api服务
        byte[] result = HttpUtil.doPost("https://openapi.youdao.com/api", null, params, "application/json");
        // 打印返回结果
        String s = new String(result, StandardCharsets.UTF_8);
        System.out.println(s);
        JSONObject jsonObject = JSON.parseObject(s);
        JSONArray translation = jsonObject.getJSONArray("translation");
        return translation.get(0).toString();
    }

}
