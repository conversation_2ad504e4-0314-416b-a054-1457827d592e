package com.fast;

import com.fast.utils.VideoUtil;

import java.nio.charset.StandardCharsets;

/**
 * VideoUtilFinal测试类
 *
 * <AUTHOR>
 */
public class VideoUtilTest {

    public static void main(String[] args) {
        System.out.println("=== MP4文件结构分析 ===");

        String testUrl = "https://world-test.tos-ap-southeast-1.volces.com/drama/testvideo.mp4";
        System.out.println("测试URL: " + testUrl);

        // 1. 分析文件结构
        System.out.println("\n1. 分析MP4文件结构...");
        analyzeMP4Structure(testUrl);

        // 2. 测试解析
        System.out.println("\n2. 测试时长解析...");
        long startTime = System.currentTimeMillis();
        double duration = VideoUtil.getMp4Duration(testUrl);
        long endTime = System.currentTimeMillis();

        if (duration > 0) {
            System.out.println("✓ 解析成功!");
            System.out.println("  时长: " + duration + "秒");
            System.out.println("  格式化: " + VideoUtil.formatDuration(duration));
            System.out.println("  耗时: " + (endTime - startTime) + "ms");
        } else {
            System.out.println("✗ 解析失败");
            System.out.println("  耗时: " + (endTime - startTime) + "ms");

            System.out.println("\n可能的原因:");
            System.out.println("1. moov box位置在文件末尾，需要下载更多数据");
            System.out.println("2. 文件使用了特殊的编码格式");
            System.out.println("3. 缺少FFmpeg等解析工具");
        }

        System.out.println("\n=== 分析完成 ===");
    }

    /**
     * 分析MP4文件结构
     */
    private static void analyzeMP4Structure(String videoUrl) {
        try {
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) new java.net.URL(videoUrl).openConnection();
            connection.setRequestMethod("HEAD");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

            System.out.println("文件信息:");
            System.out.println("  Content-Type: " + connection.getContentType());
            System.out.println("  Content-Length: " + formatFileSize(connection.getContentLength()));
            System.out.println("  Accept-Ranges: " + connection.getHeaderField("Accept-Ranges"));

            // 分析文件头部结构
            System.out.println("\nMP4 Box结构分析:");
            analyzeBoxStructure(videoUrl, 0, 64 * 1024); // 前64KB

            // 如果前面没找到moov，检查更大范围
            if (!foundMoovInRange(videoUrl, 0, 64 * 1024)) {
                System.out.println("\n前64KB未发现moov box，检查更大范围...");
                analyzeBoxStructure(videoUrl, 0, 10 * 1024 * 1024); // 前10MB

                if (!foundMoovInRange(videoUrl, 0, 10 * 1024 * 1024)) {
                    System.out.println("\n前10MB仍未发现moov box，可能在文件末尾");
                    // 检查文件末尾
                    long fileSize = connection.getContentLength();
                    if (fileSize > 0) {
                        long startPos = Math.max(0, fileSize - 10 * 1024 * 1024);
                        System.out.println("检查文件末尾10MB...");
                        analyzeBoxStructure(videoUrl, startPos, fileSize);
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("分析失败: " + e.getMessage());
        }
    }

    /**
     * 分析Box结构
     */
    private static void analyzeBoxStructure(String videoUrl, long startPos, long endPos) {
        try {
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) new java.net.URL(videoUrl).openConnection();

            if (startPos > 0) {
                connection.setRequestProperty("Range", "bytes=" + startPos + "-" + (endPos - 1));
            } else {
                connection.setRequestProperty("Range", "bytes=0-" + (endPos - 1));
            }
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

            if (connection.getResponseCode() == 206 || connection.getResponseCode() == 200) {
                try (java.io.InputStream is = connection.getInputStream()) {
                    byte[] data = new byte[(int) (endPos - startPos)];
                    int bytesRead = is.read(data);

                    System.out.println("  读取范围: " + startPos + "-" + endPos + " (" + formatFileSize(bytesRead) + ")");

                    // 解析Box结构
                    parseBoxes(data, startPos);
                }
            }

        } catch (Exception e) {
            System.out.println("  范围分析失败: " + e.getMessage());
        }
    }

    /**
     * 解析MP4 Boxes
     */
    private static void parseBoxes(byte[] data, long baseOffset) {
        int offset = 0;
        int boxCount = 0;

        while (offset < data.length - 8 && boxCount < 20) { // 限制显示前20个box
            try {
                // 读取box大小（前4字节）
                long boxSize = readBigEndianInt32(data, offset);

                // 读取box类型（接下来4字节）
                String boxType = new String(data, offset + 4, 4, StandardCharsets.US_ASCII);

                System.out.println("  Box[" + boxCount + "]: " + boxType +
                        " (size=" + formatFileSize((int) boxSize) +
                        ", offset=" + (baseOffset + offset) + ")");

                // 特殊处理重要的box
                if ("mvhd".equals(boxType)) {
                    System.out.println("    ★ 发现mvhd box - 包含时长信息!");
                    analyzeMvhdBox(data, offset);
                } else if ("moov".equals(boxType)) {
                    System.out.println("    ★ 发现moov box - 元数据容器!");
                } else if ("mdat".equals(boxType)) {
                    System.out.println("    ★ 发现mdat box - 媒体数据!");
                }

                // 移动到下一个box
                if (boxSize <= 0 || boxSize > data.length) {
                    break;
                }
                offset += (int) boxSize;
                boxCount++;

            } catch (Exception e) {
                break;
            }
        }
    }

    /**
     * 分析mvhd box
     */
    private static void analyzeMvhdBox(byte[] data, int mvhdOffset) {
        try {
            int offset = mvhdOffset + 12; // 跳过box header

            if (offset + 16 < data.length) {
                long timescale = readBigEndianInt32(data, offset);
                long duration = readBigEndianInt32(data, offset + 4);

                System.out.println("      timescale: " + timescale);
                System.out.println("      duration: " + duration);

                if (timescale > 0 && duration > 0) {
                    double seconds = (double) duration / timescale;
                    System.out.println("      计算时长: " + seconds + "秒 (" +
                            VideoUtil.formatDuration(seconds) + ")");
                }
            }
        } catch (Exception e) {
            System.out.println("      mvhd解析失败: " + e.getMessage());
        }
    }

    /**
     * 检查指定范围是否包含moov box
     */
    private static boolean foundMoovInRange(String videoUrl, long startPos, long endPos) {
        try {
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) new java.net.URL(videoUrl).openConnection();
            connection.setRequestProperty("Range", "bytes=" + startPos + "-" + (endPos - 1));
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

            if (connection.getResponseCode() == 206 || connection.getResponseCode() == 200) {
                try (java.io.InputStream is = connection.getInputStream()) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;

                    while ((bytesRead = is.read(buffer)) != -1) {
                        String content = new String(buffer, 0, bytesRead, StandardCharsets.ISO_8859_1);
                        if (content.contains("moov") || content.contains("mvhd")) {
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 忽略错误
        }

        return false;
    }

    /**
     * 读取大端序32位整数
     */
    private static long readBigEndianInt32(byte[] data, int offset) {
        if (offset + 4 > data.length) return 0;

        return ((long) (data[offset] & 0xFF) << 24) |
                ((long) (data[offset + 1] & 0xFF) << 16) |
                ((long) (data[offset + 2] & 0xFF) << 8) |
                ((long) (data[offset + 3] & 0xFF));
    }

    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + "B";
        if (bytes < 1024 * 1024) return (bytes / 1024) + "KB";
        return (bytes / (1024 * 1024)) + "MB";
    }
}
