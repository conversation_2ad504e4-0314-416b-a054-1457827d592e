/*
 * Powered By fast.up
 */
package com.fast.controller.playlist;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.playlist.FastMiniPlaylistPO;
import com.fast.service.playlist.FastMiniPlaylistService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniPlaylist")
public class FastMiniPlaylistController extends BaseController {

    @Autowired
    private FastMiniPlaylistService fastMiniPlaylistService;

    @ApiName(value = "剧单-查询列表", folder = {"剧单"})
    @ApiParamsIn({
            "page:1:int:页码",
            "limit:1:int:每页条数",
            "miniId:1:int:应用ID",
            "contVersionId:1:int:APP内容版本ID",
    })
    @ApiParamsOut({
            "list 》 type:应用类型",
            "list 》 miniId:应用ID",
            "list 》 contVersionId:APP内容版本ID",
            "list 》 title:标题",
            "list 》 subTitle:副标题",
            "list 》 remark:描述信息",
            "list 》 cover:封面图",
            "list 》 seq:排序",
            "list 》 show:是否显示（0、否；1、是）",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastMiniPlaylistPO params, PageVO pageVO) {
        return fastMiniPlaylistService.queryPageList(params, pageVO);
    }

    @ApiName(value = "剧单-查询单个详情", folder = {"剧单"})
    @ApiParamsIn({
            "id:1:int:剧单ID",
    })
    @ApiParamsOut({
            "type:应用类型",
            "miniId:应用ID",
            "contVersionId:APP内容版本ID",
            "title:标题",
            "subTitle:副标题",
            "remark:描述信息",
            "cover:封面图",
            "seq:排序",
            "show:是否显示（0、否；1、是）",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniPlaylistPO params) {
        if (params.getId() == null && !StrUtil.isEmpty(params.getEncryptionId())) {
            Integer id = decodeInt(params.getEncryptionId());
            if (id != null) {
                params.setId(id);
            }
        }
        if (params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        return ResultVO.success(fastMiniPlaylistService.getDetail(params.getId()));
    }

    @ApiName(value = "剧单-添加", folder = {"剧单"})
    @ApiParamsIn({
            "miniId:1:int:应用ID",
            "contVersionId:1:int:APP内容版本ID",
            "title:1:str:标题",
            "cover:0:str:封面图",
            "seq:0:int:排序",
            "dramaIds:1:str:漫剧ID列表",
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated FastMiniPlaylistPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniPlaylistService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "剧单-更新", folder = {"剧单"})
    @ApiParamsIn({
            "id:1:int:剧单ID",
            "miniId:1:int:应用ID",
            "contVersionId:1:int:APP内容版本ID",
            "title:1:str:标题",
            "cover:0:str:封面图",
            "seq:0:int:排序",
            "dramaIds:1:str:漫剧ID列表",
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody @Validated FastMiniPlaylistPO params) {
        if (params.getId() == null && !isEmpty(params.getEncryptionId())) {
            Integer id = decodeInt(params.getEncryptionId());
            if (id != null) {
                params.setId(id);
            }
        }
        if (params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniPlaylistService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "剧单-删除", folder = {"剧单"})
    @ApiParamsIn({
            "id:1:int:剧单ID",
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, FastMiniPlaylistPO params) {
        if (params.getId() == null && !StrUtil.isEmpty(params.getEncryptionId())) {
            Integer id = decodeInt(params.getEncryptionId());
            if (id != null) {
                params.setId(id);
            }
        }
        if (params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        return ResultVO.success(fastMiniPlaylistService.delete(params.getId()));
    }

}
