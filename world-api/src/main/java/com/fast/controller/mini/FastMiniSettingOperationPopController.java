/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.mini.FastMiniSettingOperationPopPO;
import com.fast.service.mini.FastMiniSettingOperationPopService;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mini")
public class FastMiniSettingOperationPopController extends BaseController {

    @Autowired
    private FastMiniSettingOperationPopService fastMiniSettingOperationPopService;

    @ApiName(value = "运营能力-获得浮窗配置", folder = {"mini"})
    @ApiParamsIn({
            "encryptionId:1:str:miniId加密id"
    })
    @ApiParamsOut({
            "popName:浮窗名称",
            "crowdId:目标人群id 0=所有人",
            "popType:浮窗类型 1=加桌按钮;2=福利活动",
            "popPage:浮窗位置 1=剧场;2=追剧;3=我的;4=观看历史;5=播放页，多个逗号隔开",
            "popPosition:播放浮窗样式 1=位于底部;2=位于侧边",
            "popDesc:浮窗文案",
            "jumpPage:跳转内容 1=福利中心",
            "state:状态 0=禁用;1=启用",
    })
    @PostMapping("/operation/pop/getList")
    public ResultVO<?> getList(FastMiniSettingOperationPopPO params, PageVO pageVO) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        return fastMiniSettingOperationPopService.getList(params, pageVO);
    }

    @ApiName(value = "运营能力-新增或更新浮窗", folder = {"mini"})
    @ApiParamsIn({
            "encryptionId:1:str:miniId加密id",
            "id:0:int:浮窗id(编辑时必传)",
            "popName:1:str:浮窗名称",
            "crowdId:0:int:目标人群id 0=所有人",
            "popType:1:int:浮窗类型 1=加桌按钮;2=福利活动",
            "popPage:1:str:浮窗位置 1=剧场;2=追剧;3=我的;4=观看历史;5=播放页，多个逗号隔开",
            "popPosition:1:int:播放浮窗样式 1=位于底部;2=位于侧边",
            "popDesc:1:str:浮窗文案",
            "jumpPage:1:int:跳转内容 1=福利中心",
            "jumpUrl:1:str:跳转链接,前端定义跳转页面或参数",
            "state:1:int:状态 0=禁用;1=启用",
    })
    @ApiParamsOut({
            "status:0成功"
    })
    @PostMapping("/operation/pop/addOrEdit")
    public ResultVO<?> addOrEdit(HttpServletRequest request, FastMiniSettingOperationPopPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);

        // 参数校验
        if (isEmpty(params.getPopName())) {
            return ResultVO.error("浮窗名称不能为空");
        }
        if (params.getCrowdId() == null) {
            params.setCreatorId(0);
        }
        if (params.getPopType() == null || !StrUtil.equalsAny(params.getPopType(), 1, 2)) {
            return ResultVO.error("浮窗类型参数错误");
        }
        if (isEmpty(params.getPopPage()) || params.getPopPage().matches(".*[^1-5,].*")) {
            return ResultVO.error("浮窗位置参数错误");
        }
        // 防止sql注入
        StrUtil.checkMysqlInData(params.getPopPage());
        if (params.getPopPosition() == null || !StrUtil.equalsAny(params.getPopPosition(), 1, 2)) {
            return ResultVO.error("播放浮窗样式参数错误");
        }
        if (isEmpty(params.getPopDesc())) {
            return ResultVO.error("浮窗文案不能为空");
        }
        if (params.getJumpPage() != null && !StrUtil.equalsAny(params.getJumpPage(), 1)) {
            return ResultVO.error("跳转内容参数错误");
        }
        if (params.getState() == null || !StrUtil.equalsAny(params.getState(), 0, 1)) {
            return ResultVO.error("状态参数错误");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        return fastMiniSettingOperationPopService.addOrEdit(params);
    }

    @ApiName(value = "运营能力-复制浮窗配置", folder = {"mini"})
    @ApiParamsIn({
            "encryptionId:1:str:当前正在配置的小程序加密id",
            "encryptionId2:1:str:被复制的小程序加密id",
    })
    @ApiParamsOut({
            "status:0成功"
    })
    @PostMapping("/operation/pop/copy")
    public ResultVO<?> copy(HttpServletRequest request, FastMiniSettingOperationPopPO params) {
        if (isEmpty(params.getEncryptionId()) || isEmpty(params.getEncryptionId2())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        Integer copyMiniId = decodeInt(params.getEncryptionId2());
        if (miniId == null || copyMiniId == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        params.setCopyMiniId(copyMiniId);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        return fastMiniSettingOperationPopService.copy(params);
    }

    @ApiName(value = "运营能力-删除浮窗配置", folder = {"mini"})
    @ApiParamsIn({
            "encryptionId:1:str:miniId加密id",
            "id:1:int:浮窗id"
    })
    @ApiParamsOut({
            "status:0成功"
    })
    @PostMapping("/operation/pop/delete")
    public ResultVO<?> delete(HttpServletRequest request, FastMiniSettingOperationPopPO params) {
        if (isEmpty(params.getEncryptionId()) || params.getId() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        return fastMiniSettingOperationPopService.delete(params);
    }


}
