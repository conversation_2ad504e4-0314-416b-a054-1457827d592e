/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.LogVisit;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.mini.FastMiniSettingPO;
import com.fast.po.task.FastSettingTaskPO;
import com.fast.service.common.FastActionLogService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.mini.FastMiniSettingService;
import com.fast.utils.CalTime;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 小程序各项配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniSetting")
public class FastMiniSettingController extends BaseController {

    @Autowired
    private FastMiniSettingService miniSettingService;
    @Autowired
    private FastActionLogService actionLogService;
    @Autowired
    private FastMiniService fastMiniService;

    /**
     * 清理小程序缓存信息
     *
     * @param request
     * @param ids     指定清理的小程序id(多个逗号分隔)
     * @return
     */
    @RequestMapping(value = "/nologin/cleanSettingRedis")
    public ResultVO<?> cleanSettingRedis(HttpServletRequest request, String ids, Integer cvid) {
        Set<Integer> miniIds = fastMiniService.queryMiniIds(ids);
        for (Integer miniId : miniIds) {
            RedisUtil.incr(StaticVar.MINI_CONTENT_SETTING_VER + miniId + ":" + cvid);
            RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniId + ":" + cvid);
            RedisUtil.del(StaticVar.MINI_SETTING_DRAMA_IDS + miniId + ":" + cvid);
        }
        return ResultVO.success();
    }

    /**
     * 获取小程序配置详细信息
     *
     * @param request
     * @param params
     * @return
     */
    @RequestMapping(value = "/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniSettingPO params) {
        CalTime calTime = new CalTime();
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId());
        params.setContentType(sessionVO.getContentType());
        params.setType(sessionVO.getMiniType());
        FastMiniSettingPO settingPO = miniSettingService.queryInfoByRedis(params);
        // 获取数据版本号
        Integer value = toInteger(RedisUtil.get(StaticVar.MINI_CONTENT_SETTING_VER + miniId + ":" + params.getContVersionId()));
        settingPO.setVersion(value + "");
        actionLogService.log("method_cost", "/fastMiniSetting/getDetail:" + calTime.getCostTime());
        return ResultVO.success(settingPO);
    }

    /**
     * 更新小程序配置信息
     *
     * @param request
     * @param params
     * @return
     */
    @LogVisit(name = "更新小程序配置信息")
    @ApiName(value = "更新小程序配置信息", folder = {StaticFolder.FOLDER_MINI_SET})
    @ApiParamsIn({
            "------:1:str:本接口采取json格式上传参数",
            "encryptionId:1:str:小程序加密id",
            "coinName:1:str:币名称",
            "openRecModularTitle:1:int:推荐模块标题开启状态 1=开启;0=关闭",
            "openFindModularTitle:1:int:发现模块标题开启状态 1=开启;0=关闭",
            "openRecState:1:int:推荐开启状态 1=开启;0=关闭",
            "openAccState:1:int:入口开启状态 1=开启;0=关闭",
            "openTypeSex:1:int:展示分类性别 1=开启;0=关闭",
            "openTypeTag:1:int:展示分类标签 1=开启;0=关闭",
            "openTypeUpdate:1:int:展示分类更新状态 1=开启;0=关闭",
            "openIosCharge:1:int:IOS充值开启状态 1=开启;0=关闭",
            "openIosTime:1:int:IOS充值开启 HH:mm-HH:mm",
            "openIosCharge1:1:int:601-IOS充值开启状态 1=开启;0=关闭",
            "openIosTime1:1:int:601-IOS充值开启 HH:mm-HH:mm",
            "openIosCharge2:1:int:广告推广-IOS充值开启状态 1=开启;0=关闭",
            "openIosTime2:1:int:广告推广-IOS充值开启 HH:mm-HH:mm",
            "openIosCharge3:1:int:公众号-IOS充值开启状态 1=开启;0=关闭",
            "openIosTime3:1:int: 公众号-IOS充值开启 HH:mm-HH:mm",
            "shareWithOfficial:1:int:分享携带公众号信息 1=开启;0=关闭",
            "videoPlayVersion:1:int:视频播放器版本1=1.0版本，2=2.0版本",
            "payTypeSet:1:str:快应用支付方式设置",
            "payToStay:1:int:支付挽留开启 1=是;0=否",
            "payToStayType:1:int:支付挽留模式 1=引导;2=强制",
            "openVirtualPay:1:int:开启虚拟支付 1=是;0=否",
            "bannerList:1:array:小程序banner",
            "bannerListGroup:1:array:小程序banner Group",
            "bannerFindList:1:array:发现banner",
            "accessList:1:array:小程序功能入口",
            "recommend:1:object:小程序推荐区-全局配置，推荐第一种",
            "recommendGroup:1:object:小程序推荐区-用户分组配置，推荐第二种",
            "recommendBottom:1:object:小程序推荐区-推荐区底导航",
            "recommendSuper:1:object:小程序推荐区-精品配置，推荐第三种",
            "recommendYoung:1:object:小程序推荐区-青少年模式，推荐第三种",
            "recommendTheaterList:0:object:快应用多个推荐集合",
            "recommendSuperList:0:object:快应用多个推荐集合",
            "recommendSuperList>>tabOne:0:1:1=联盟2华为",
            "recommendSuperList>>tabTwo:0:1:1=渠道2自然",
            "recommendModularList:1:array:小程序模块推荐区-全局配置",
            "recommendModularList>>showType:1:int:展示方式 1=横屏;2=竖屏",
            "recommendModularList>>modularName:1:str:模块标题",
            "recommendModularList>>showDrama:1:int:展示剧目 1=全部;2=标签;3=角标;0=自定义选择",
            "recommendModularListGroup:1:array:小程序模块推荐区-用户组",
            "recommendModularListGroup>>showType:1:int:展示方式 1=横屏;2=竖屏",
            "recommendModularListGroup>>modularName:1:str:模块标题",
            "recommendModularListGroup>>showDrama:1:int:展示剧目 1=全部;2=标签;3=角标;0=自定义选择",
            "videoModularList:1:array:视频模块推荐区",
            "videoModularList>>showType:1:int:展示方式 1=横屏;2=竖屏",
            "videoModularList>>modularName:1:str:模块标题",
            "videoModularList>>videoIds:1:str:视频id(数字)",
            "bottomList:1:array:小程序底部导航",
            "wallpaperList:1:array:壁纸数组",
            "wallpaperList>>url:1:str:壁纸地址",
            "wallpaperList>>sequence:1:int:排序",
            "introductionList:1:array:剧集介绍数组",
            "introductionList>>dramaId:1:int:剧id",
            "introductionList>>sequence:1:int:剧集介绍排序",
            "settingGroup:1:object:用户组配置信息",
            "settingGroup>>openState:1:int:开启用户群 1=开启;0=关闭",
            "settingGroup>>openMemberGroup:1:int:可见用户群 3,2,1",
            "settingGroup>>rechargeCount:1:int:累计充值次数",
            "settingGroup>>vipOrderDay:1:int:VIP卡购买时长",
            "settingGroup>>finishDramaNum:1:int:已完播剧目",
            "settingGroupBanner:1:object:用户组配置信息",
            "settingGroupBanner>>openState:1:int:开启用户群 1=开启;0=关闭",
            "settingGroupBanner>>openMemberGroup:1:int:可见用户群 3,2,1",
            "settingGroupBanner>>rechargeCount:1:int:累计充值次数",
            "settingGroupBanner>>vipOrderDay:1:int:VIP卡购买时长",
            "settingGroupBanner>>finishDramaNum:1:int:已完播剧目",
            "taskPO>>switchSign:1:int:-签到任务开关0关1开",
            "taskPO>>switchAdv:1:int:-看广告任务开关0关1开",
            "taskPO>>switchNew:1:int:-新手任务开关0关1开",
            "taskPO>>switchUsual:1:int:-日常任务开关0关1开",
            "taskPO>>switchPride:1:int:-成就任务开关0关1开",
            "taskPO>>circleAdv:1:int:-看广告周期1天2周3月",
            "taskPO>>circleUsual:1:int:-日常任务周期1天2周3月",
            "taskPO>>coinList>>taskType:1:int:-任务类型 1:签到 2:广告 3:新手 4:日常 5:成就",
            "taskPO>>coinList>>markIdx:1:int:-标记顺序1,2,3,4,5,6;taskType=1或2时-第xxx天/次;taskType=3时-[1保存桌面,2桌面启动,3安全登录];taskType=4时-[1去剧场,2添加5部剧,3邀请5好友,4看一部新剧;taskType=5时-[1看完一整部,2累计看5部]]",
            "taskPO>>coinList>>coin:1:int:-金币数量",
            "contVersionId:1:int:APP内容版本ID",
    })
    @ApiParamsOut({"status:ok"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, @RequestBody FastMiniSettingPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("小程序加密id不合法");
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        FastSettingTaskPO taskPO = params.getTaskPO();
        if (taskPO != null) {
            taskPO.setMiniId(miniId);
            params.setTaskPO(taskPO);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = miniSettingService.update(sessionVO, params, null);

        RedisUtil.incr(StaticVar.MINI_CONTENT_SETTING_VER + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.MINI_SETTING_DRAMA_IDS + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.MINI_TASK_DETAIL + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.MINI_SETTING_SIMPLE + miniId + ":" + params.getContVersionId());

        FastMiniPO miniPO = fastMiniService.queryById(miniId);
        RedisUtil.del(StaticVar.MINI_CONTENT_SETTING_SIMPLE + miniPO.getAppId() + ":" + params.getContVersionId());

        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 更新小程序虚拟支付
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "更新小程序虚拟支付", folder = {StaticFolder.FOLDER_MINI_SET})
    @ApiParamsIn({
            "encryptionId:1:str:小程序加密id(多个逗号分隔)",
            "openVirtualPay:1:int:开启虚拟支付 1=是;0=否",
    })
    @ApiParamsOut({"status:ok"})
    @RequestMapping(value = "/updateVirtualPay", method = {RequestMethod.POST})
    public ResultVO<?> updateVirtualPay(HttpServletRequest request, FastMiniSettingPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("小程序加密id不合法");
        }
        List<String> ids = CollUtil.parseStr2List(params.getEncryptionId());
        for (String idStr : ids) {
            Integer miniId = decodeInt(idStr);
            if (miniId == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setMiniId(miniId);
            SessionVO sessionVO = getSessionVO(request);
            params.setCreatorId(sessionVO.getUserId());
            params.setUpdatorId(sessionVO.getUserId());
            miniSettingService.updateVirtualPay(params);

            RedisUtil.incr(StaticVar.MINI_CONTENT_SETTING_VER + miniId + ":" + params.getContVersionId());
            RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniId + ":" + params.getContVersionId());
            RedisUtil.del(StaticVar.MINI_SETTING_DRAMA_IDS + miniId + ":" + params.getContVersionId());
        }
        return ResultVO.success();
    }

    /**
     * 复制小程序配置信息
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "复制小程序配置信息", folder = {StaticFolder.FOLDER_MINI_SET})
    @ApiParamsIn({
            "encryptionId:1:str:当前正在配置的小程序加密id",
            "encryptionId2:1:str:被复制的小程序加密id",
            "contVersionId:1:int:APP内容版本ID",
    })
    @ApiParamsOut({"status:ok"})
    @RequestMapping(value = "/copyMiniSetting", method = {RequestMethod.POST})
    public ResultVO<?> copyMiniSetting(HttpServletRequest request, FastMiniSettingPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("当前正在配置的小程序加密id不合法");
        }
        if (isBlank(params.getEncryptionId2())) {
            return ResultVO.error("被复制的小程序加密id不合法");
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer miniId2 = decodeInt(params.getEncryptionId2());
        if (miniId2 == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());

        // 查询被复制的小程序配置信息
        FastMiniSettingPO query = new FastMiniSettingPO();
        query.setMiniId(miniId2);
        query.setCreatorId(sessionVO.getUserId());
        query.setUpdatorId(sessionVO.getUserId());
        query.setRetailId(sessionVO.getRetailId());

        FastMiniSettingPO settingPO = miniSettingService.queryInfoByRedis(query);
        settingPO.setMiniId(miniId);
        settingPO.setVideoPlayVersion(null);// 禁止复制播放器版本
        // 福利任务复制
        if (Objects.nonNull(settingPO.getTaskPO())) {
            settingPO.getTaskPO().setMiniId(miniId);
        }
        MethodVO methodVO = miniSettingService.update(sessionVO, settingPO, 1);

        RedisUtil.incr(StaticVar.MINI_CONTENT_SETTING_VER + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.MINI_SETTING_DRAMA_IDS + miniId + ":" + params.getContVersionId());
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 兑换K币名称设置
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "兑换K币名称设置-更新", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({
            "coinName:1:str:币名称",
            "encryptionId:1:str:小程序加密id",
            "contVersionId:1:int:APP内容版本ID",
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateCoinName", method = {RequestMethod.POST})
    public ResultVO<?> updateCoinName(HttpServletRequest request, FastMiniSettingPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("小程序加密id不合法");
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        if (isBlank(params.getCoinName())) {
            return ResultVO.error("名称不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = miniSettingService.updateCoinName(params);

        RedisUtil.incr(StaticVar.MINI_CONTENT_SETTING_VER + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.MINI_SETTING_DRAMA_IDS + miniId + ":" + params.getContVersionId());
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 兑换K币名称查询
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "兑换名称查询", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({"encryptionId:1:str:小程序加密id"})
    @ApiParamsOut({"coinName:1:str:币名称"})
    @RequestMapping(value = "/getCoinName", method = {RequestMethod.POST})
    public ResultVO<?> getCoinName(HttpServletRequest request, FastMiniSettingPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("小程序加密id不合法");
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        FastMiniSettingPO vo = miniSettingService.getMiniSetting(params);
        return ResultVO.success(vo);
    }

    /**
     * ios充值开关设置
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "ios充值开关设置-更新", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({"encryptionId:1:str:小程序加密id",
            "openIosCharge1:1:int:601-ios充值开启状态 1=开启;0=关闭",
            "openIosTime1:1:str:601-ios充值开启时间",
            "openIosCharge2:1:int:广告推广-ios充值开启状态 1=开启;0=关闭",
            "openIosTime2:1:str:广告推广-ios充值开启时间",
            "openIosCharge3:1:int:公众号-ios充值开启状态 1=开启;0=关闭",
            "openIosTime3:1:str:公众号-ios充值开启时间",
            "iosRechargeType:1:int:1=钻石支付，2=钻石+IM支付",
            "contVersionId:1:int:APP内容版本ID",
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateIosChargeOpen", method = {RequestMethod.POST})
    public ResultVO<?> updateIosChargeOpen(HttpServletRequest request, FastMiniSettingPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("小程序加密id不合法");
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        if (params.getOpenIosCharge1() == null || !StrUtil.equalsAny(params.getOpenIosCharge1(), 1, 0)) {
            return ResultVO.error("ios充值开启状态不合法");
        }
        if (isBlank(params.getOpenIosTime1())) {
            return ResultVO.error("ios充值开启时间不能为空");
        }
        if (params.getOpenIosTime1().length() != 11) {
            return ResultVO.error("ios充值开启时间不合法");
        }
        if (params.getOpenIosCharge2() == null || !StrUtil.equalsAny(params.getOpenIosCharge2(), 1, 0)) {
            return ResultVO.error("ios充值开启状态不合法");
        }
        if (isBlank(params.getOpenIosTime2())) {
            return ResultVO.error("ios充值开启时间不能为空");
        }
        if (params.getOpenIosTime2().length() != 11) {
            return ResultVO.error("ios充值开启时间不合法");
        }
        if (params.getOpenIosCharge3() == null || !StrUtil.equalsAny(params.getOpenIosCharge3(), 1, 0)) {
            return ResultVO.error("ios充值开启状态不合法");
        }
        if (isBlank(params.getOpenIosTime3())) {
            return ResultVO.error("ios充值开启时间不能为空");
        }
        if (params.getOpenIosTime3().length() != 11) {
            return ResultVO.error("ios充值开启时间不合法");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = miniSettingService.updateIosChargeOpen(params);

        RedisUtil.incr(StaticVar.MINI_CONTENT_SETTING_VER + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.MINI_SETTING_DRAMA_IDS + miniId + ":" + params.getContVersionId());
        RedisUtil.del(StaticVar.TIKTOK_IOS_PAY + miniId);
        RedisUtil.del(StaticVar.MINI_SETTING_SIMPLE + miniId + ":" + params.getContVersionId());
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * ios充值开关查询
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "ios充值开关查询", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({"encryptionId:1:str:小程序加密id"})
    @ApiParamsOut({
            "openIosCharge1:601-ios充值开启状态 1=开启;0=关闭",
            "openIosTime1:601-ios充值开启时间",
            "openIosCharge2:广告推广-ios充值开启状态 1=开启;0=关闭",
            "openIosTime2:广告推广-ios充值开启时间",
            "openIosCharge3:公众号-ios充值开启状态 1=开启;0=关闭",
            "openIosTime3:公众号-ios充值开启时间",
    })
    @RequestMapping(value = "/getIosChargeOpen", method = {RequestMethod.POST})
    public ResultVO<?> getIosChargeOpen(HttpServletRequest request, FastMiniSettingPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("小程序加密id不合法");
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        FastMiniSettingPO vo = miniSettingService.getMiniSetting(params);
        return ResultVO.success(vo);
    }

    @ApiName(value = "设置群聊开关", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({"clipboardOn:1:int:0关闭1开启"})
    @ApiParamsOut({"status:success"})
    @RequestMapping(value = "/updateClipboard", method = {RequestMethod.POST})
    public ResultVO<?> updateClipboard(HttpServletRequest request, Integer clipboardOn) {
        if (clipboardOn == null) {
            return ResultVO.error("开关状态不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        FastMiniSettingPO setting = new FastMiniSettingPO();
        setting.setClipboardOn(clipboardOn);
        setting.setMiniId(sessionVO.getMiniId());
        setting.setUpdatorId(sessionVO.getUserId());
        setting.setUpdateTime(DateUtil.getNowDate());
        MethodVO methodVO = miniSettingService.updateClipboard(setting);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "查群聊开关", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({"noparam"})
    @ApiParamsOut({"clipboardOn:0关1开"})
    @RequestMapping(value = "/getClipboard", method = {RequestMethod.POST})
    public ResultVO<?> getClipboard(HttpServletRequest request, Integer clipboardOn) {
        SessionVO sessionVO = getSessionVO(request);
        FastMiniSettingPO miniSet = new FastMiniSettingPO();
        miniSet.setMiniId(sessionVO.getMiniId());
        miniSet.setCreatorId(sessionVO.getUserId());
        miniSet.setCreateTime(DateUtil.getNowDate());
        FastMiniSettingPO vo = miniSettingService.getMiniSetting(miniSet);
        return ResultVO.success(vo);
    }

    @ApiName(value = "查询继续观剧提醒", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({"noparam"})
    @ApiParamsOut({"continuePushFlag:0关1开"})
    @RequestMapping(value = "/getContinuePushFlag", method = {RequestMethod.POST})
    public ResultVO<?> getContinuePushFlag(HttpServletRequest request, Integer clipboardOn) {
        SessionVO sessionVO = getSessionVO(request);
        FastMiniSettingPO miniSet = new FastMiniSettingPO();
        miniSet.setMiniId(sessionVO.getMiniId());
        miniSet.setCreatorId(sessionVO.getUserId());
        miniSet.setCreateTime(DateUtil.getNowDate());
        FastMiniSettingPO vo = miniSettingService.getMiniSetting(miniSet);
        return ResultVO.success(vo);
    }

    @ApiName(value = "设置继续观剧提醒开关", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({"continuePushFlag:1:int:0关闭1开启"})
    @ApiParamsOut({"status:success"})
    @RequestMapping(value = "/updateContinuePushFlag", method = {RequestMethod.POST})
    public ResultVO<?> updateContinuePushFlag(HttpServletRequest request, Integer continuePushFlag) {
        if (continuePushFlag == null) {
            return ResultVO.error("继续观剧提醒不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        FastMiniSettingPO setting = new FastMiniSettingPO();
        setting.setContinuePushFlag(continuePushFlag);
        setting.setMiniId(sessionVO.getMiniId());
        setting.setUpdatorId(sessionVO.getUserId());
        setting.setUpdateTime(DateUtil.getNowDate());
        MethodVO methodVO = miniSettingService.updateClipboard(setting);
        return ResultVO.fromMethodVO(methodVO);
    }


}
