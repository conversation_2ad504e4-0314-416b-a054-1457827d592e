/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.MiniTypeEnum;
import com.fast.po.mini.FastMiniPO;
import com.fast.service.mini.FastMiniService;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMini")
public class FastMiniController extends BaseController {

    @Autowired
    private FastMiniService fastMiniService;

    @ApiName(value = "小程序管理-查询列表", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({
            "state:1:int:状态0无效1有效",
            "type:1:int:1：微信小程序;2抖音小程序;3H5",
            "contentType:1=短剧;2=漫画;3=小说",
            "hadSetting:1:int:是否已配置: 1是;0否",
            "hadFee:1:int:是否已配置: 1是;0否",
            "principalName:0:str:主体名称",
            "openVirtualPay:1:int:开启虚拟支付 1=是;0=否",
            "feeFlag:0:int:投放目标1付费2免费9混合投放",
            "auditFlag:0:int:是否用于送审0否1是",
            "isConfigTemplate:0:int:是否配置消息模板 1是0否",
            "isConfigAdUnit:0:int:是否配置广告位ID 1是0否",
            "wechatAdqFlag:0:int:0未配置1已配置"
    })
    @ApiParamsOut({"miniName:小程序名称", "encryptionId:加密id", "id:id"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMiniPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getContentType() == null && StrUtil.isEmpty(params.getContentTypes())) {
            if (sessionVO.getContentType() != null && sessionVO.getContentType() != 99) {
                params.setContentType(sessionVO.getContentType());
            }
        }
        if (Objects.nonNull(params.getFilterContentTypeFlag()) && params.getFilterContentTypeFlag() == 1) {
            params.setContentType(null);
        }
        if (StrUtil.isNotEmpty(params.getContentTypes())) {
            params.setContentTypes("1,2,3,4");
        }
        // 若是分销商只能看到自己的小程序，且开启数据权限的应用
        if (biggerZero(sessionVO.getRetailId())) {
            params.setRetailId(sessionVO.getRetailId());
        }
        return fastMiniService.queryPageList(params, pageVO);
    }

    @ApiName(value = "抖小商户号配置-查询列表", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({
            "ids:0:str:小程序id们",
            "hasSet:0:int:是否配置商户号0否1是"
    })
    @ApiParamsOut({
            "miniName:小程序名称",
            "encryptionId:加密id",
            "merchantNum:商户号"
    })
    @RequestMapping(value = "/getMerchantNumList", method = {RequestMethod.POST})
    public ResultVO<?> getMerchantNumList(HttpServletRequest request, FastMiniPO params, PageVO pageVO) {
        if (StrUtil.isNotEmpty(params.getIds())) {
            params.setIds(StrUtil.resetInt(params.getIds()));
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setType(2);// 抖音小程序
        return fastMiniService.queryMerchantPageList(params, pageVO);
    }

    @ApiName(value = "抖小商户号配置-更新商户号", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "merchantNum:1:str:商户号"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @RequestMapping(value = "/updateMiniMerchant", method = {RequestMethod.POST})
    public ResultVO<?> updateMiniMerchant(HttpServletRequest request, FastMiniPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniService.updateMiniMerchant(params);
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiName(value = "小程序管理-查询单个详情", folder = {"小程序"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMiniPO fastMini = fastMiniService.queryById(params);
        return ResultVO.success(fastMini);
    }

    @ApiName(value = "小程序管理-添加", folder = {"小程序"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastMiniPO params) {
        SessionVO sessionUser = getSessionVO(request);
        params.setCreatorId(sessionUser.getUserId());
        MethodVO methodVO = fastMiniService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "小程序管理-添加H5", folder = {"小程序"})
    @ApiParamsIn({
            "appId:1:str:一级域名(不是id)",
            "miniName:1:str:应用名称",
    })
    @ApiParamsOut({"s"})
    @RequestMapping(value = "/insertMiniH5", method = {RequestMethod.POST})
    public ResultVO<?> insertMiniH5(HttpServletRequest request, @Validated FastMiniPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setContentType(sessionVO.getContentType());
        params.setType(3);
        MethodVO methodVO = fastMiniService.insertMiniH5(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "小程序管理-更新H5", folder = {"小程序"})
    @ApiParamsIn({
            "miniName:1:str:应用名称",
    })
    @ApiParamsOut({"s"})
    @RequestMapping(value = "/updateMiniH5", method = {RequestMethod.POST})
    public ResultVO<?> updateMiniH5(HttpServletRequest request, FastMiniPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniService.updateMiniH5(params);

        String key = StaticVar.MINI_INFO_ID + id;
        RedisUtil.del(key);
        return ResultVO.fromMethodVO(methodVO);
    }


    /**
     * 小程序管理-更新
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "小程序管理-更新", folder = {"小程序"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "roadId:1:int:链路id",
            "ghId:1:str:小程序原始id",
            "offerId:1:str:虚拟支付offerId",
            "appKey:1:str:虚拟支付appKey",
    })
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastMiniPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniService.update(params);
        FastMiniPO miniPO = fastMiniService.queryById(id);

        RedisUtil.del(StaticVar.MINI_INFO_ID + id);
        RedisUtil.del(StaticVar.MINI_INFO_APPID + miniPO.getAppId());
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "小程序管理-配置兜底公众号", folder = {"小程序"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "defOfficialId:1:int:公众号id"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @RequestMapping(value = "/setDefaultOfficial", method = {RequestMethod.POST})
    public ResultVO<?> setDefaultOfficial(HttpServletRequest request, FastMiniPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getDefOfficialId() == null) {
            return ResultVO.error("兜底公众号的id不能为空");
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniService.updateDefaultOfficialId(params);
        String key = StaticVar.MINI_INFO_ID + id;
        RedisUtil.del(key);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "小程序管理-配置付费免费", folder = {"小程序"})
    @ApiParamsIn({
            "encryptionIds:1:str:加密id",
            "feeFlag:1:int:1付费2免费9混合投放"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @RequestMapping(value = "/setFeeFlag", method = {RequestMethod.POST})
    public ResultVO<?> setFeeFlag(HttpServletRequest request, FastMiniPO params) {
        if (isBlank(params.getEncryptionIds()) && StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        List<Integer> idList = new ArrayList<>();
        if (StrUtil.isNotEmpty(params.getEncryptionIds())) {
            List<String> encryptionIdsList = StrUtil.getStrListFromStr(params.getEncryptionIds());
            for (String encryptionId : encryptionIdsList) {
                Integer id = decodeInt(encryptionId);
                if (id == null) {
                    return ResultVO.error(StaticStr.INVALID_PARAM);
                }
                idList.add(id);
            }
        } else if (StrUtil.isNotEmpty(params.getEncryptionId())) {
            Integer id = decodeInt(params.getEncryptionId());
            if (id == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            idList.add(id);
        }
        if (idList.size() == 0) {
            return ResultVO.error("修改对象不能为空");
        }
        if (params.getFeeFlag() == null) {
            return ResultVO.error("投放目标feeFlag不能为空");
        }
        if (!Arrays.asList(1, 2, 9).contains(params.getFeeFlag())) {
            return ResultVO.error("投放目标feeFlag错误");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniService.updateFeeFlag(idList, params);
        for (Integer id : idList) {
            String key = StaticVar.MINI_INFO_ID + id;
            RedisUtil.del(key);
        }
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiName(value = "小程序管理-修改小程序名称", folder = {"小程序"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "miniName:1:int:小程序名称"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @RequestMapping(value = "/updateMiniName", method = {RequestMethod.POST})
    public ResultVO<?> updateMiniName(HttpServletRequest request, FastMiniPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId()) || StrUtil.isEmpty(params.getMiniName())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        return fastMiniService.updateMiniName(params);
    }

    @ApiName(value = "小程序管理-获得应用功能配置", folder = {"小程序"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
    })
    @ApiParamsOut({
            "hotDramaTemplateId:热播新剧提醒",
            "dramaUpdateTemplateId:剧集更新提醒",
            "rewardVideoAdId:激励广告位ID",
            "bannerAdId:banner广告位ID",
            "interstitialAdId:插屏广告位ID",
            "feeFlag:投放目标1=付费;2=免费;9=混合投放",
            "accelerateFlag:倍速播放 倍速标志0否1是",
            "desktopFlag:加桌配置 0不支持加桌1支持加桌",
            "authStatus:抖小剧授权开关 授权状态0否1是，是否可以抖音授权",
            "forbidBackFlag:是否回传付费 是否禁止付费回传0否1是",
    })
    @RequestMapping(value = "/getAppConf", method = {RequestMethod.POST})
    public ResultVO<?> getAppConf(FastMiniPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        return fastMiniService.getAppConf(params);
    }

    @ApiName(value = "小程序管理-应用功能配置", folder = {"小程序"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "hotDramaTemplateId:1:str:热播新剧提醒",
            "dramaUpdateTemplateId:1:str:剧集更新提醒",
            "rewardVideoAdId:0:str:激励广告位ID",
            "bannerAdId:0:str:banner广告位ID",
            "interstitialAdId:0:str:插屏广告位ID",
            "feeFlag:1:int:投放目标1付费2免费9混合投放",
            "accelerateFlag:1:int:倍速播放 倍速标志0否1是",
            "desktopFlag:1:int:加桌配置 0不支持加桌1支持加桌",
            "authStatus:1:int:抖小剧授权开关 授权状态0否1是，是否可以抖音授权",
            "forbidBackFlag:1:int:是否禁止付费回传0否1是",
            "wechatAdqDataId:0:str:adq数据源id",
            "wechatAdqSecret:0:str:adq数据源秘钥"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @RequestMapping(value = "/appConf", method = {RequestMethod.POST})
    public ResultVO<?> appConf(HttpServletRequest request, FastMiniPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);

        // 参数校验
        if (params.getHotDramaTemplateId() == null || params.getDramaUpdateTemplateId() == null ||
                params.getFeeFlag() == null || params.getAccelerateFlag() == null || params.getDesktopFlag() == null ||
                params.getAuthStatus() == null || params.getForbidBackFlag() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        // 防止错误数据
        if (!Arrays.asList(1, 2, 9).contains(params.getFeeFlag())) {
            return ResultVO.error("投放目标feeFlag错误");
        }
        if (params.getAccelerateFlag() != 0 && params.getAccelerateFlag() != 1) {
            return ResultVO.error("倍速播放accelerateFlag错误");
        }
        if (params.getDesktopFlag() != 0 && params.getDesktopFlag() != 1) {
            return ResultVO.error("加桌配置desktopFlag错误");
        }
        if (params.getAuthStatus() != 0 && params.getAuthStatus() != 1) {
            return ResultVO.error("抖小剧授权开关authStatus错误");
        }
        if (params.getForbidBackFlag() != 0 && params.getForbidBackFlag() != 1) {
            return ResultVO.error("是否回传付费forbidBackFlag错误");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        return fastMiniService.appConf(params);
    }

    @ApiName(value = "小程序管理-清空应用缓存", folder = {"小程序"})
    @ApiParamsIn({
            "无"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @RequestMapping(value = "/clearAppCache", method = {RequestMethod.POST})
    public ResultVO<?> clearAppCache() {
        fastMiniService.clearAppCache();
        return ResultVO.success();
    }

    @ApiName(value = "修改小程序tag", folder = {"小程序"})
    @ApiParamsIn({
            "tag:1:str:小程序标签",
            "encryptionId:1:str:加密id"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @RequestMapping(value = "/updateTag", method = {RequestMethod.POST})
    public ResultVO<?> updateTag(HttpServletRequest request, FastMiniPO params) {
        if (StrUtil.isEmpty(params.getAppId()) || StrUtil.isEmpty(params.getTag())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        return fastMiniService.updateTag(params);
    }

    @ApiName(value = "小程序管理-添加APP", folder = {"小程序"})
    @ApiParamsIn({
            "miniName:1:str:应用名称",
            "appId:1:str:使用包名",
            "principalName:1:str:主体信息",
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/insertApp", method = {RequestMethod.POST})
    public ResultVO<?> insertApp(HttpServletRequest request, FastMiniPO params) {
        // 对包名格式进行校验，使用正则表达式，例如com.example.demo
        if (StrUtil.isEmpty(params.getAppId())) {
            return ResultVO.error("包名不能为空");
        }
        if (!params.getAppId().matches("^[a-zA-Z]+[a-zA-Z0-9_]*\\.[a-zA-Z]+[a-zA-Z0-9_]*\\.[a-zA-Z]+[a-zA-Z0-9_]*$")) {
            return ResultVO.error("包名格式不正确");
        }
        if (StrUtil.isEmpty(params.getPrincipalName())) {
            return ResultVO.error("主体信息不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setContentType(sessionVO.getContentType());
        params.setType(MiniTypeEnum.APP.index);
        // 设置appid
        MethodVO methodVO = fastMiniService.insertApp(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
