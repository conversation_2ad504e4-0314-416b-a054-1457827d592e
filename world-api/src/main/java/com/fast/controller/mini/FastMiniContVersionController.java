/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.po.mini.FastMiniContVersionPO;
import com.fast.service.mini.FastMiniContVersionService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniContVersion")
public class FastMiniContVersionController extends BaseController {

    @Autowired
    private FastMiniContVersionService fastMiniContVersionService;

    @ApiName(value = "APP内容版本-查询列表", folder = {StaticFolder.FOLDER_MINI_SET})
    @ApiParamsIn({
            "page:1:int:页码",
            "limit:1:int:每页条数",
    })
    @ApiParamsOut({
            "miniId:程序ID",
            "name:版本名称",
            "languageCodes:关联语种",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastMiniContVersionPO params, PageVO pageVO) {
        return fastMiniContVersionService.queryPageList(params, pageVO);
    }

    @ApiName(value = "APP内容版本-查询单个详情", folder = {StaticFolder.FOLDER_MINI_SET})
    @ApiParamsIn({
            "encryptionId:1:str:版本ID",
    })
    @ApiParamsOut({
            "miniId:程序ID",
            "name:版本名称",
            "languageCodes:关联语种",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniContVersionPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        FastMiniContVersionPO fastMiniContVersion = fastMiniContVersionService.queryById(params);
        return ResultVO.success(fastMiniContVersion);
    }

    @ApiName(value = "APP内容版本-添加", folder = {StaticFolder.FOLDER_MINI_SET})
    @ApiParamsIn({
            "miniId:1:int:程序ID",
            "name1:str:版本名称",
            "languageCodes:1:str:关联语种code，多个以英文逗号分隔",
    })
    @ApiParamsOut({
            "state:ok"
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody FastMiniContVersionPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniContVersionService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "APP内容版本-更新", folder = {StaticFolder.FOLDER_MINI_SET})
    @ApiParamsIn({
            "encryptionId:1:str:版本ID",
            "miniId:1:int:程序ID",
            "name1:str:版本名称",
            "languageCodes:1:str:关联语种code，多个以英文逗号分隔",
    })
    @ApiParamsOut({
            "state:ok"
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody FastMiniContVersionPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniContVersionService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "APP内容版本-删除", folder = {StaticFolder.FOLDER_MINI_SET})
    @ApiParamsIn({
            "encryptionId:1:str:版本ID",
    })
    @ApiParamsOut({
            "state:ok"
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, FastMiniContVersionPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = fastMiniContVersionService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
