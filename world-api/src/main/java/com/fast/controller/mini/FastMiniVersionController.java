/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.mini.FastMiniVersionPO;
import com.fast.service.mini.FastMiniVersionService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniVersion")
public class FastMiniVersionController extends BaseController {

    @Autowired
    private FastMiniVersionService fastMiniVersionService;

    @ApiName(value = "应用版本-查询列表", folder = {"miniVersion"})
    @ApiParamsIn({
            "page:1:int:页码",
            "limit:1:int:每页条数",
            "version:0:str:版本号",
            "createTimeStr:0:str:发布时间区间",
    })
    @ApiParamsOut({
            "list 》 type:应用类型",
            "list 》 miniId:应用ID",
            "list 》 miniName:应用名称",
            "list 》 version:版本号",
            "list 》 whatsNew:更新内容",
            "list 》 remark:备注",
            "list 》 memberCnt:在用人数",
            "list 》 createTime:发布时间",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastMiniVersionPO params, PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeStart(date.get(0));
            params.setCreateTimeEnd(date.get(1));
        }
        return fastMiniVersionService.queryPageList(params, pageVO);
    }

    @ApiName(value = "应用版本-查询单个详情", folder = {"miniVersion"})
    @ApiParamsIn({
            "id:1:int:主键ID",
    })
    @ApiParamsOut({
            "id:主键ID",
            "type:应用类型",
            "miniId:应用ID",
            "miniName:应用名称",
            "version:版本号",
            "whatsNew:更新内容",
            "remark:备注",
            "memberCnt:在用人数",
            "createTime:发布时间",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniVersionPO params) {
        if (params.getId() == null && !StrUtil.isEmpty(params.getEncryptionId())) {
            Integer id = decodeInt(params.getEncryptionId());
            if (id != null) {
                params.setId(id);
            }
        }
        if (params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        FastMiniVersionPO fastMiniVersion = fastMiniVersionService.queryById(params);
        return ResultVO.success(fastMiniVersion);
    }

    @ApiName(value = "应用版本-添加", folder = {"miniVersion"})
    @ApiParamsIn({
            "miniId:1:int:应用ID",
            "version:1:str:版本号",
            "url:0:str:包文件地址",
            "codeUrl:0:str:代码地址",
            "whatsNew:0:str:更新内容",
            "remark:0:str:备注",
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated FastMiniVersionPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniVersionService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "应用版本-更新", folder = {"miniVersion"})
    @ApiParamsIn({
            "id:1:int:主键ID",
            "miniId:1:int:应用ID",
            "version:1:str:版本号",
            "url:0:str:包文件地址",
            "codeUrl:0:str:代码地址",
            "whatsNew:0:str:更新内容",
            "remark:0:str:备注",
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody @Validated FastMiniVersionPO params) {
        if (params.getId() == null && !isEmpty(params.getEncryptionId())) {
            Integer id = decodeInt(params.getEncryptionId());
            if (id != null) {
                params.setId(id);
            }
        }
        if (params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniVersionService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "应用版本-删除", folder = {"miniVersion"})
    @ApiParamsIn({
            "id:1:int:主键ID",
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, FastMiniVersionPO params) {
        if (params.getId() == null && !isEmpty(params.getEncryptionId())) {
            Integer id = decodeInt(params.getEncryptionId());
            if (id != null) {
                params.setId(id);
            }
        }
        if (params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        return ResultVO.success(fastMiniVersionService.delete(params.getId()));
    }
}
