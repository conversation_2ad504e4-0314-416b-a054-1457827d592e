/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.mini.FastMiniPublishAuditPO;
import com.fast.service.mini.FastMiniPublishAuditService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniPublishAudit")
public class FastMiniPublishAuditController extends BaseController {

    @Autowired
    private FastMiniPublishAuditService fastMiniPublishAuditService;

    /*
     * 小程序发布-查询列表
     */
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMiniPublishAuditPO params, PageVO pageVO) {
        return fastMiniPublishAuditService.queryPageList(params, pageVO);
    }

    /*
     * 小程序发布-查询单个详情
     */
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniPublishAuditPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMiniPublishAuditPO fastMiniPublishAudit = fastMiniPublishAuditService.queryById(params);
        Map<String, Object> results = ResultVO.getMap();
        results.put("item", fastMiniPublishAudit);
        return ResultVO.success(results);
    }

    /*
     * 小程序发布-添加
     */
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastMiniPublishAuditPO params) {
        SessionVO sessionUser = getSessionVO(request);
        MethodVO methodVO = fastMiniPublishAuditService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    /*
     * 小程序发布-更新
     */
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastMiniPublishAuditPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMiniPublishAuditService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
