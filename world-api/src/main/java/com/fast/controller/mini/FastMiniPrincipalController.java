/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.mini.FastMiniPrincipalPO;
import com.fast.service.mini.FastMiniPrincipalService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mini")
public class FastMiniPrincipalController extends BaseController {

    @Autowired
    private FastMiniPrincipalService fastMiniPrincipalService;

    @ApiName(value = "mini-查询主体列表", folder = {"mini"})
    @ApiParamsIn({
            "principalName:0:str:主体名称",
            "hasSet:0:int:是否配置客服",
            "state:0:int:状态 1启用 0禁用",
            "signFlag:0:int:0不支持签约1支持"
    })
    @ApiParamsOut({
            "principalName:主体名称",
            "wechatCorpId:企业id",
            "wechatServiceUrl:客服链接",
            "encryptionId:加密id",
            "principalSimpleName:主体简称",
            "invoiceTaxNo:税号",
            "invoiceBankNo:开户号",
            "invoiceBank:开户行",
            "invoiceAddress:开票地址",
            "invoiceTel:开户电话",
            "invoiceContentType:开票内容 1信息服务费",
            "principalName:1:str:主体名称",
    })
    @PostMapping("/getFastMiniPrincipalList")
    public ResultVO<?> getList(HttpServletRequest request, FastMiniPrincipalPO params, PageVO pageVO) {
        return fastMiniPrincipalService.queryPageList(params, pageVO);
    }

    @ApiName(value = "mini-查询单个详情", folder = {"mini"})
    @PostMapping("/getFastMiniPrincipalDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniPrincipalPO params) {
        if (Objects.nonNull(params.getId())) {
            FastMiniPrincipalPO fastMiniPrincipal = fastMiniPrincipalService.queryById(params);
            return ResultVO.success(fastMiniPrincipal);
        }
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMiniPrincipalPO fastMiniPrincipal = fastMiniPrincipalService.queryById(params);
        return ResultVO.success(fastMiniPrincipal);
    }

    @ApiName(value = "mini-主体配置更新", folder = {"mini"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "wechatCorpId:企业id",
            "wechatServiceUrl:客服链接"
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/updateFastMiniPrincipal")
    public ResultVO<?> update(HttpServletRequest request, FastMiniPrincipalPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = fastMiniPrincipalService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiName(value = "mini-主体配置更新", folder = {"mini"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/updateFastMiniPrincipalCp")
    public ResultVO<?> updateCp(HttpServletRequest request, FastMiniPrincipalPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = fastMiniPrincipalService.updateCp(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "mini-主体配置新增", folder = {"mini"})
    @ApiParamsIn({
            "signFlag:1:str:签约0否1是",
            "principalSimpleName:1:str:主体简称",
            "invoiceTaxNo:1:str:税号",
            "invoiceBankNo:1:str:开户号",
            "invoiceBank:1:str:开户行",
            "invoiceAddress:1:str:开票地址",
            "invoiceTel:1:str:开户电话",
            "invoiceContentType:1:str:开票内容 1信息服务费",
            "principalName:1:str:主体名称",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/insertFastMiniPrincipal")
    public ResultVO<?> insert(HttpServletRequest request, FastMiniPrincipalPO params) {
        MethodVO methodVO = fastMiniPrincipalService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
