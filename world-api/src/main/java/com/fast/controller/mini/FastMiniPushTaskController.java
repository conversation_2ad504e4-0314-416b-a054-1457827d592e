/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.mini.FastMiniPushTaskPO;
import com.fast.service.mini.FastMiniPushTaskService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniPushTask")
public class FastMiniPushTaskController extends BaseController {

    @Autowired
    private FastMiniPushTaskService fastMiniPushTaskService;

    @ApiParamsIn({"page:1:int:页", "limit:1:int:每页条数"})
    @ApiParamsOut({
            "state:消息状态1：待发送2：已发送3：取消",
            "retailId:分销商id",
            "officialId:公众号id",
            "taskName:任务名称",
            "dramaId:剧id",
            "pushMiniIds:推送关联应用",
            "pushMemberType:1全部用户2未观看本剧用户",
            "pushTime:推送时间",
            "receiveCounts:触达人数",
            "clickCounts:点击人数",
            "clickRate:点击率"
    })
    @ApiName(value = "mini-抖音订阅消息列表", folder = {"mini"})
    @PostMapping("/getFastMiniPushTaskList")
    public ResultVO<?> getList(HttpServletRequest request, FastMiniPushTaskPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setDelFlag(0);
        params.setContentType(sessionVO.getContentType());
        return fastMiniPushTaskService.queryPageList(params, pageVO);
    }

    @ApiName(value = "mini-抖音订阅消息查询单个详情", folder = {"mini"})
    @ApiParamsIn({"encryptionId:1:str:加密id"})
    @ApiParamsOut({
            "state:消息状态1：待发送2：已发送3：取消",
            "retailId:分销商id",
            "officialId:公众号id",
            "taskName:任务名称",
            "dramaId:剧id",
            "pushMiniIds:推送关联应用",
            "pushMemberType:1全部用户2未观看本剧用户",
            "pushTime:推送时间",
            "receiveCounts:触达人数",
            "clickCounts:点击人数",
            "clickRate:点击率"
    })
    @PostMapping("/getFastMiniPushTaskDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniPushTaskPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMiniPushTaskPO fastMiniPushTask = fastMiniPushTaskService.queryById(params);
        return ResultVO.success(fastMiniPushTask);
    }

    @ApiName(value = "mini-抖音订阅消息添加", folder = {"mini"})
    @ApiParamsIn({
            "taskName:1:str:任务名称",
            "dramaId:1:int:剧id",
            "pushMiniIds:1:str:推送关联应用",
            "pushMemberType:1:int:1全部用户2未观看本剧用户",
            "pushTimeStr:1:str:推送时间yyyy-MM-DD hh:mm:ss"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insertFastMiniPushTask")
    public ResultVO<?> insert(HttpServletRequest request, FastMiniPushTaskPO params) {
        if (StrUtil.isEmpty(params.getTaskName())) {
            return ResultVO.error("任务名称不能为空");
        }
        if (StrUtil.isEmpty(params.getDramaId())) {
            return ResultVO.error("剧不能为空");
        }
        if (StrUtil.isEmpty(params.getPushMiniIds())) {
            return ResultVO.error("推送应用不能为空");
        }
        if (StrUtil.isEmpty(params.getPushMemberType())) {
            return ResultVO.error("推送用户类型不能为空");
        }
        if (StrUtil.isEmpty(params.getPushTimeStr())) {
            return ResultVO.error("推送时间不能为空");
        }
        params.setPushTime(DateUtil.format07(params.getPushTimeStr()));
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setState(1);
        MethodVO methodVO = fastMiniPushTaskService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "mini-抖音订阅消息更新", folder = {"mini"})
    @ApiParamsIn({
            "encryptonId:1:str:加密id",
            "taskName:1:str:任务名称",
            "dramaId:1:int:剧id",
            "pushMiniIds:1:str:推送关联应用",
            "pushMemberType:1:int:1全部用户2未观看本剧用户",
            "pushTimeStr:1:str:推送时间yyyy-MM-DD hh:mm:ss"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateFastMiniPushTask")
    public ResultVO<?> update(HttpServletRequest request, FastMiniPushTaskPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMiniPushTaskService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
