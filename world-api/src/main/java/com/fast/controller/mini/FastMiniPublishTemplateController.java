/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.po.mini.FastMiniPublishTemplatePO;
import com.fast.service.mini.FastMiniPublishTemplateService;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniPublishTemplate")
public class FastMiniPublishTemplateController extends BaseController {

    @Autowired
    private FastMiniPublishTemplateService fastMiniPublishTemplateService;

    @ApiName(value = "mini-查询列表", folder = {"mini"})
    @PostMapping("/getFastMiniPublishTemplateList")
    public ResultVO<?> getList(HttpServletRequest request, FastMiniPublishTemplatePO params) {
        return fastMiniPublishTemplateService.queryPageList(params);
    }

}
