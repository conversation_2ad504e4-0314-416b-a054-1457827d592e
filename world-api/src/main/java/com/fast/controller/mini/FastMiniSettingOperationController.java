/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.mini.FastMiniSettingOperationPO;
import com.fast.service.mini.FastMiniSettingOperationService;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mini")
public class FastMiniSettingOperationController extends BaseController {

    @Autowired
    private FastMiniSettingOperationService fastMiniSettingOperationService;

    @ApiName(value = "运营能力-获得加桌设置", folder = {"mini"})
    @ApiParamsIn({
            "encryptionId:1:str:miniId加密id"
    })
    @ApiParamsOut({
            "desktopType:加桌方式1=强制加桌;2=引导加桌;3=不展示加桌",
            "startSeriesNum:加桌剧集",
            "popStep:弹窗弹出频率",
            "popTitle:弹窗标题文案",
            "popDesc:弹窗描述文案",
            "popBtnTitle:弹窗按钮文案",
    })
    @PostMapping("/operation/getDeskConfig")
    public ResultVO<?> getDeskConfig(FastMiniVO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        return fastMiniSettingOperationService.getDeskConfig(params);
    }

    @ApiName(value = "运营能力-保存加桌设置", folder = {"mini"})
    @ApiParamsIn({
            "encryptionId:1:str:miniId加密id",
            "desktopType:1:int:加桌方式1=强制加桌;2=引导加桌;3=不展示加桌",
            "startSeriesNum:1:int:加桌剧集",
            "popStep:1:int:弹窗弹出频率",
            "popTitle:1:str:弹窗标题文案",
            "popDesc:1:str:弹窗描述文案",
            "popBtnTitle:1:str:弹窗按钮文案",
    })
    @ApiParamsOut({
            "status:0成功"
    })
    @PostMapping("/operation/saveDeskConfig")
    public ResultVO<?> saveDeskConfig(HttpServletRequest request, FastMiniSettingOperationPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }

        // 判断加桌方式
        if (params.getDesktopType() == null || !StrUtil.equalsAny(params.getDesktopType(), 1, 2, 3)) {
            return ResultVO.error(StaticCode.ERROR, "加桌方式错误");
        }
        // 判断加桌剧集
        if (params.getStartSeriesNum() == null || params.getStartSeriesNum() < 1) {
            return ResultVO.error(StaticCode.ERROR, "加桌剧集错误");
        }

        params.setMiniId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        return fastMiniSettingOperationService.saveDeskConfig(params);
    }

}
