/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.po.mini.FastMiniWallpaperPO;
import com.fast.service.mini.FastMiniWallpaperService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniWallpaper")
public class FastMiniWallpaperController extends BaseController {

    @Autowired
    private FastMiniWallpaperService fastMiniWallpaperService;

    @ApiName(value = "小程序壁纸-查询列表", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({"dramaName:0:str:来源名称", "page:0:1:页码", "limit:0:int:条数"})
    @ApiParamsOut({"url:图片地址", "dramaName:图片来源", "id:图片id"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMiniWallpaperPO params, PageVO pageVO) {
        params.setMiniId(0);
        return fastMiniWallpaperService.queryPageList(params, pageVO);
    }

    @ApiName(value = "小程序壁纸-查询单个详情", folder = {"小程序"})
    @ApiParamsIn({"encryptionId:1:str:加密id"})
    @ApiParamsOut({"url:图片地址", "dramaName:图片来源", "id:图片id"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniWallpaperPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        FastMiniWallpaperPO fastMiniWallpaper = fastMiniWallpaperService.queryById(params);
        return ResultVO.success(fastMiniWallpaper);
    }

    @ApiName(value = "小程序壁纸-批量添加", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({
            "paramsList 》url:1:str:壁纸地址",
            "paramsList 》dramaId:1:int:剧集id",
            "paramsList 》dramaName:1:str:剧名",
            "paramsList 》miniId:0:int:小程序id"})
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/insertBatch", method = {RequestMethod.POST})
    public ResultVO<?> insertBatch(HttpServletRequest request, @RequestBody List<FastMiniWallpaperPO> paramsList) {
        SessionVO sessionVO = getSessionVO(request);
        if (paramsList == null || paramsList.size() < 1) {
            return ResultVO.error("壁纸不能为空");
        }
        for (FastMiniWallpaperPO wall : paramsList) {
            if (wall.getDramaId() == null) {
                return ResultVO.error("剧id不能为空");
            }
            if (StrUtil.isEmpty(wall.getDramaName())) {
                return ResultVO.error("剧名不能为空");
            }
            if (StrUtil.isEmpty(wall.getUrl())) {
                return ResultVO.error("图片地址不能为空");
            }
            if (wall.getSequence() == null) {
                wall.setSequence(999);
            }
            wall.setMiniId(0);// 平台壁纸设置为0
            wall.setCreatorId(sessionVO.getUserId());
        }
        MethodVO methodVO = fastMiniWallpaperService.insertBatch(paramsList);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "小程序壁纸-更新", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "url:1:str:壁纸地址",
            "dramaId:1:int:剧集id",
            "dramaName:1:str:剧名",
            "miniId:0:int:小程序id"})
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastMiniWallpaperPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = fastMiniWallpaperService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "小程序壁纸-批量删除", folder = {StaticFolder.FOLDER_MINI})
    @ApiParamsIn({"encryptionIds:1:str:加密ids"})
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/deleteBatch", method = {RequestMethod.POST})
    public ResultVO<?> deleteBatch(HttpServletRequest request, FastMiniWallpaperPO params) {
        if (isBlank(params.getEncryptionIds())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        List<Integer> idList = decodeIntList(params.getEncryptionIds());
        MethodVO methodVO = fastMiniWallpaperService.deleteBatch(idList);
        return ResultVO.fromMethodVO(methodVO);
    }
}
