/*
 * Powered By fast.up
 */
package com.fast.controller.mini;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.po.mini.FastMiniGiveCouponLogPO;
import com.fast.service.mini.FastMiniGiveCouponLogService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/couponLog")
public class FastMiniGiveCouponLogController extends BaseController {

    @Autowired
    private FastMiniGiveCouponLogService fastMiniGiveCouponLogService;

    @ApiName(value = "抖音小程序券领取记录-查询列表", folder = {StaticFolder.FOLDER_MINI_SET})
    @ApiParamsIn({
            "memberId:0:int:用户id",
            "linkId:0:str:渠道id",
            "miniIds:0:str:小程序Id",
            "retailIds:0:str:分销商Id",
            "giveTimeBegin:0:str:赠送开始时间",
            "giveTimeEnd:0:str:赠送结束时间",
            "consumeTimeBegin:0:str:核销开始时间",
            "consumeTimeEnd:0:str:核销结束时间"
    })
    @ApiParamsOut({
            "miniId:小程序id",
            "memberId:用户id",
            "ttCouponId:抖音小程序劵id",
            "giveCoin:赠送的K币",
            "giveTime:赠送时间",
            "validBeginTime:生效开始时间",
            "validEndTime:生效结束时间",
            "retailId:分销商id",
            "linkId:推广链接id",
            "consumeStatus:核销状态 1 已核销 0 未核销",
            "createTime:创建时间",
            "consumeTime:核销时间",
            "updateTime:更新时间",
            "activityName:活动名称",
            "linkName:推广链接名称",
            "miniName:小程序名称",
            "retailName:分销商名称"
    })
    @PostMapping("/getFastMiniGiveCouponLogList")
    public ResultVO<?> getList(HttpServletRequest request, FastMiniGiveCouponLogPO params, PageVO pageVO) {
        return fastMiniGiveCouponLogService.queryPageList(params, pageVO);
    }
}
