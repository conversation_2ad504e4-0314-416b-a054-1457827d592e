/*
 * Powered By fast.up
 */
package com.fast.controller.help;


import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.help.FastHelpComplainPO;
import com.fast.service.help.FastHelpComplainService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/help")
public class FastHelpComplainController extends BaseController {

    @Autowired
    private FastHelpComplainService fastHelpComplainService;

    @ApiName(value = "问题投诉-查询列表", folder = {"help"})
    @ApiParamsIn({
            "memberIdStr:0:int:请输入用户id/登录账号",
            "typeId:0:int:请选择问题类型",
            "linkName:0:str:请输入链接名称",
            "linkId:0:str:请输入链接ID",
            "miniIds:0:str:请选择应用",
            "retailIds:0:int:请选择分销商",
            "createTimeStr:0:str:提交时间",
    })
    @ApiParamsOut({
            "encryptionId:加密ID",
            "id:ID",
            "linkId:链接id",
            "memberId:用户id",
            "phone:反馈手机号",
            "type:问题类型0=其他;1=更新慢;2=不流畅;3=耗流量;4=剧集少;5=价格高;6=界面少;7=提示少",
            "feedbackText:反馈内容",
            "state:处理状态0=待处理1=处理中=2已处理",
            "miniName:应用名称",
            "memberPhone:用户登录名",
            "retailName:分销商名称",
    })
    @PostMapping("/getFastHelpComplainList")
    public ResultVO<?> getList(FastHelpComplainPO params, PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeStart(date.get(0));
            params.setCreateTimeEnd(date.get(1));
        }
        if (params.getTypeId() != null) {
            params.setType(params.getTypeId());
        }
        // 防止sql注入
        StrUtil.checkMysqlInData(params.getMiniIds());
        StrUtil.checkMysqlInData(params.getRetailIds());
        return fastHelpComplainService.queryPageList(params, pageVO);
    }
}
