/*
 * Powered By fast.up
 */
package com.fast.controller.help;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.help.FastHelpQuestionPO;
import com.fast.po.help.FastHelpQuestionTypePO;
import com.fast.service.help.FastHelpQuestionService;
import com.fast.service.help.FastHelpQuestionTypeService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/help")
public class FastHelpQuestionController extends BaseController {

    @Autowired
    private FastHelpQuestionService fastHelpQuestionService;
    @Autowired
    private FastHelpQuestionTypeService fastHelpQuestionTypeService;

    @ApiName(value = "帮助中心-问题类型查询列表", folder = {"help"})
    @ApiParamsIn({
            "typeId:0:int:请选择问题类型",
    })
    @ApiParamsOut({
            "encryptionId:加密ID",
            "id:ID",
            "name:问题名称",
            "sequence:排序正序",
    })
    @PostMapping("/getFastHelpQuestionTypeList")
    public ResultVO<?> getListType(FastHelpQuestionTypePO params) {
        return ResultVO.success(fastHelpQuestionTypeService.queryList(params));
    }

    @ApiName(value = "帮助中心-查询列表", folder = {"help"})
    @ApiParamsIn({
            "typeId:0:int:请选择问题类型",
    })
    @ApiParamsOut({
            "encryptionId:加密ID",
            "id:ID",
            "typeId:问题类型ID",
            "name:问题名称",
            "solution:问题解答",
            "validClickNum:问题解答有效点击数",
            "invalidClickNum:问题解答无效点击数",
            "sequence:排序正序",
    })
    @PostMapping("/getFastHelpQuestionList")
    public ResultVO<?> getList(FastHelpQuestionPO params, PageVO pageVO) {
        return fastHelpQuestionService.queryPageList(params, pageVO);
    }

    @ApiName(value = "帮助中心-查询单个详情", folder = {"help"})
    @ApiParamsIn({
            "encryptionId:0:str:加密ID",
    })
    @ApiParamsOut({
            "encryptionId:加密ID",
            "id:ID",
            "typeId:问题类型ID",
            "name:问题名称",
            "solution:问题解答",
            "validClickNum:问题解答有效点击数",
            "invalidClickNum:问题解答无效点击数",
            "sequence:排序正序",
    })
    @PostMapping("/getFastHelpQuestionDetail")
    public ResultVO<?> getDetail(FastHelpQuestionPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        FastHelpQuestionPO fastHelpQuestion = fastHelpQuestionService.queryById(params);
        return ResultVO.success(fastHelpQuestion);
    }

    @ApiName(value = "帮助中心-添加", folder = {"help"})
    @ApiParamsIn({
            "typeId:0:int:请选择问题类型",
            "name:0:int:问题名称",
            "solution:0:int:问题解答",
    })
    @ApiParamsOut({
            "status:ok状态",
    })
    @PostMapping("/insertFastHelpQuestion")
    public ResultVO<?> insert(HttpServletRequest request, FastHelpQuestionPO params) {
        // 参数校验
        if (!biggerZero(params.getTypeId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (isEmpty(params.getName())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (isEmpty(params.getSolution())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastHelpQuestionService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "帮助中心-更新", folder = {"help"})
    @ApiParamsIn({
            "encryptionId:0:str:加密ID",
            "typeId:0:int:请选择问题类型",
            "name:0:int:问题名称",
            "solution:0:int:问题解答",
    })
    @ApiParamsOut({
            "status:ok状态",
    })
    @PostMapping("/updateFastHelpQuestion")
    public ResultVO<?> update(HttpServletRequest request, FastHelpQuestionPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        // 参数校验
        if (!biggerZero(params.getTypeId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (isEmpty(params.getName())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (isEmpty(params.getSolution())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastHelpQuestionService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "帮助中心-删除", folder = {"help"})
    @ApiParamsIn({
            "encryptionId:0:str:加密ID",
    })
    @ApiParamsOut({
            "status:ok状态",
    })
    @PostMapping("/deleteFastHelpQuestion")
    public ResultVO<?> delete(HttpServletRequest request, FastHelpQuestionPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastHelpQuestionService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "帮助中心-问题类型新增", folder = {"help"})
    @ApiParamsIn({
            "name:0:str:问题类型名称",
    })
    @ApiParamsOut({
            "status:ok状态",
    })
    @PostMapping("/insertFastHelpQuestionType")
    public ResultVO<?> insertType(HttpServletRequest request, FastHelpQuestionTypePO params) {
        // 参数校验
        if (isEmpty(params.getName())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastHelpQuestionTypeService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "帮助中心-问题类型更新", folder = {"help"})
    @ApiParamsIn({
            "name:0:str:问题类型名称",
    })
    @ApiParamsOut({
            "status:ok状态",
    })
    @PostMapping("/updateFastHelpQuestionType")
    public ResultVO<?> updateType(HttpServletRequest request, FastHelpQuestionTypePO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        // 参数校验
        if (isEmpty(params.getName())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastHelpQuestionTypeService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "帮助中心-问题类型删除", folder = {"help"})
    @ApiParamsIn({
            "encryptionId:0:str:问题类型加密ID",
    })
    @ApiParamsOut({
            "status:ok状态",
    })
    @PostMapping("/deleteFastHelpQuestionType")
    public ResultVO<?> deleteType(HttpServletRequest request, FastHelpQuestionTypePO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastHelpQuestionTypeService.deleteById(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "帮助中心-问题类型排序", folder = {"help"})
    @ApiParamsIn({
            "encryptionId:0:str:问题类型加密ID",
            "sequence:0:int:排序正序",
    })
    @ApiParamsOut({
            "status:ok状态",
    })
    @PostMapping("/updateFastHelpQuestionTypeSequence")
    public ResultVO<?> updateTypeSequence(HttpServletRequest request, FastHelpQuestionTypePO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        // 参数校验
        if (params.getSequence() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getSequence() < 0) {
            params.setSequence(0);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastHelpQuestionTypeService.updateSequence(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "帮助中心-问题排序", folder = {"help"})
    @ApiParamsIn({
            "encryptionId:0:str:加密ID",
            "sequence:0:int:排序正序",
    })
    @ApiParamsOut({
            "status:ok状态",
    })
    @PostMapping("/updateFastHelpQuestionSequence")
    public ResultVO<?> updateSequence(HttpServletRequest request, FastHelpQuestionPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        // 参数校验
        if (params.getSequence() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getSequence() < 0) {
            params.setSequence(0);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastHelpQuestionService.updateSequence(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
