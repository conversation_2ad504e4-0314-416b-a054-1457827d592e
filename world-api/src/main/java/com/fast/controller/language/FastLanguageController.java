/*
 * Powered By fast.up
 */
package com.fast.controller.language;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.enums.YesOrNoEnum;
import com.fast.po.language.FastLanguagePO;
import com.fast.service.language.FastLanguageService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastLanguage")
public class FastLanguageController extends BaseController {

    @Autowired
    private FastLanguageService fastLanguageService;

    @ApiName(value = "语言种类-查询列表", folder = {"语言种类"})
    @ApiParamsIn({
            "page:0:int:页码",
            "limit:0:int:条数",
    })
    @ApiParamsOut({
            "code:唯一编码",
            "name:名称",
            "seq:顺序",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastLanguagePO params, PageVO pageVO) {
        params.setShow(YesOrNoEnum.YES.getCode()); // 默认只查询显示的语言种类
        return fastLanguageService.queryPageList(params, pageVO);
    }

    @ApiName(value = "语言种类-查询单个详情", folder = {"语言种类"})
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastLanguagePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastLanguagePO fastLanguage = fastLanguageService.queryById(params);
        return ResultVO.success(fastLanguage);
    }

    @ApiName(value = "语言种类-添加", folder = {"语言种类"})
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody FastLanguagePO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastLanguageService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "语言种类-更新", folder = {"语言种类"})
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody FastLanguagePO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastLanguageService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "语言种类-删除", folder = {"语言种类"})
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, FastLanguagePO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = fastLanguageService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
