/*
 * Powered By fast.up
 */
package com.fast.controller.i18n;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.i18n.FastApptextPO;
import com.fast.service.i18n.FastApptextI18nService;
import com.fast.service.i18n.FastApptextService;
import com.fast.service.language.FastLanguageService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastApptext")
public class FastApptextController extends BaseController {

    @Autowired
    private FastApptextService fastApptextService;

    @Autowired
    private FastApptextI18nService fastApptextI18NService;

    @Autowired
    private FastLanguageService fastLanguageService;

    @ApiName(value = "APP文案-查询列表", folder = {"APP文案"})
    @ApiParamsIn({
            "page:0:int:页码",
            "limit:0:int:条数",
            "keyword:模糊查询关键字",
    })
    @ApiParamsOut({
            "code:字段名（唯一编码）",
            "text:文案",
            "texts:文案（多语种）",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastApptextPO params, PageVO pageVO) {
        return fastApptextService.queryPageList(params, pageVO);
    }

    @ApiName(value = "APP文案-查询单个详情", folder = {"APP文案"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "code:字段名（唯一编码）",
            "text:文案",
            "texts:文案（多语种）",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastApptextPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        FastApptextPO item = fastApptextService.queryById(params);
        if (item != null) {
            item.setTexts(fastApptextI18NService.getMapByCodeForLang(item.getCode()));
        }
        return ResultVO.summary(item, fastLanguageService.allCodeMapList());
    }

    @ApiName(value = "APP文案-添加", folder = {"APP文案"})
    @ApiParamsIn({
            "code:1:str:字段名（唯一编码）",
            "i18ns:1:array:角标数组，格式 {'languageCode'：'SC','name'：'推荐111'}",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody FastApptextPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastApptextService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "APP文案-更新", folder = {"APP文案"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "code:1:str:字段名（唯一编码）",
            "i18ns:1:array:角标数组，格式 {'languageCode'：'SC','name'：'推荐111'}",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody FastApptextPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastApptextService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "APP文案-删除", folder = {"APP文案"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, FastApptextPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = fastApptextService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
