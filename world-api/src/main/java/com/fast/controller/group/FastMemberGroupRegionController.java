/*
 * Powered By fast.up
 */
package com.fast.controller.group;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.group.FastMemberGroupRegionPO;
import com.fast.service.group.FastMemberGroupRegionService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/group")
public class FastMemberGroupRegionController extends BaseController {

    @Autowired
    private FastMemberGroupRegionService fastMemberGroupRegionService;

    @ApiName(value = "group-查询列表", folder = {"group"})
    @PostMapping("/getFastMemberGroupRegionList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberGroupRegionPO params, PageVO pageVO) {
        return fastMemberGroupRegionService.queryPageList(params, pageVO);
    }

    @ApiName(value = "group-查询单个详情", folder = {"group"})
    @PostMapping("/getFastMemberGroupRegionDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberGroupRegionPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberGroupRegionPO fastMemberGroupRegion = fastMemberGroupRegionService.queryById(params);
        return ResultVO.success(fastMemberGroupRegion);
    }

    @ApiName(value = "group-添加", folder = {"group"})
    @PostMapping("/insertFastMemberGroupRegion")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberGroupRegionPO params) {
        MethodVO methodVO = fastMemberGroupRegionService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "group-更新", folder = {"group"})
    @PostMapping("/updateFastMemberGroupRegion")
    public ResultVO<?> update(HttpServletRequest request, FastMemberGroupRegionPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = fastMemberGroupRegionService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
