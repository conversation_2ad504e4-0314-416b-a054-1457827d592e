/*
 * Powered By fast.up
 */
package com.fast.controller.group;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.group.FastMemberGroupPO;
import com.fast.service.group.FastMemberGroupService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/group")
public class FastMemberGroupController extends BaseController {

    @Autowired
    private FastMemberGroupService fastMemberGroupService;

    @ApiName(value = "group-人群分组查询列表", folder = {"group"})
    @ApiParamsIn({
            "groupName:0:str:群名称，模糊匹配",
            "state:0:int:状态0禁用1启用",
            "miniType:1:int:1微信小程序;2抖音小程序;3H5;4快手;5快应用"
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "groupName:群名称",
            "sequence:排序",
            "state:状态",
            "remark:备注",
            "createTime:创建时间",
            "memberCount:人数"
    })
    @PostMapping("/getFastMemberGroupList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberGroupPO params, PageVO pageVO) {
        return fastMemberGroupService.queryPageList(params, pageVO);
    }

    @ApiName(value = "group-人群分组查询单个详情", folder = {"group"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id"
    })
    @ApiParamsOut({
            "groupName: 人群名称",
            "sequence: 排序越大优先级越高",
            "state: 状态0禁用1启用",
            "miniType: 1微信小程序;2抖音小程序;3H5;4快手;5快应用",
            "remark: 备注信息",
            "linkIds: 渠道",
            "linkIdsRemove: 排除渠道",
            "regionIds: 地区id们",
            "areaIds: 区域id们",
            "cityLevels: 城市类型们",
            "phoneBrands: 手机类型们",
            "rechargeTypes: 充值类型们",
            "appIds: 小程序appid们",
            "startTypes: 启动类型",
            "todayLoginFlag: 今日是否登录-1不限制0否1是",
            "addDeskFlag: 是否加桌-1不限制0否1是",
            "todaySignFlag: 今日签到-1不限制0否1是",
            "registDaysMin: 注册天数，下限",
            "registDaysMax: 注册天数，上限",
            "activeDaysMin: 激活天数，下限",
            "activeDaysMax: 激活天数，上限",
            "regActiveDaysMin: 注册至今活跃天数下限",
            "regActiveDaysMax: 注册至今活跃天数上限",
            "lastActiveDaysMin: 最后活跃天数下限",
            "lastActiveDaysMax: 最后活跃天数上限",
            "bindLinkHoursMin: 渠道绑定小时下限",
            "bindLinkHoursMax: 渠道绑定小时上限",
            "outBackDaysMin: 回流天数下限",
            "outBackDaysMax: 回流天数上限",
            "lastVisitDaysMin: 上次访问下限",
            "lastVisitDaysMax: 上次访问上限",
            "rechargeFlag: 是否付费-1不限0否1是2广告",
            "rechargeCountMin: 付费次数-下限",
            "rechargeCountMax: 付费次数-上限",
            "orderCountMin: 下单次数-下限",
            "orderCountMax: 下单次数-上限",
            "rechargeMoneyMin: 充值金额-下限",
            "rechargeMoneyMax: 充值金额-上限",
            "firstMoneyMin: 首充金额-下限",
            "firstMoneyMax: 首充金额-上限",
            "lastMoneyMin: 最后充值金额-下限",
            "lastMoneyMax: 最后充值金额-上限",
            "avgMoneyMin: 平均充值金额-下限",
            "avgMoneyMax: 平均充值金额-上限",
            "accountCoinMin: 账户余额k币-下限",
            "accountCoinMax: 账户余额k币-上限",
            "consumeCoinMin: 消费币-下限",
            "consumeCoinMax: 消费币-上限",
            "lastRechargeDaysMin: 最后充值距今天数下限",
            "lastRechargeDaysMax: 最后充值距今天数上限",
            "lastRechargeEnterCountMin: 最后充值，进入充值页面次数下限",
            "lastRechargeEnterCountMax: 最后充值，进入充值页面次数上限",
            "adReceiveCountMin: 广告接受次数下限",
            "adReceiveCountMax: 广告接受次数上限",
            "adFinishCountMin: 广告看完次数下限",
            "adFinishCountMax: 广告看完次数上限",
            "avgEcpmMin: 平均ecpm下限",
            "avgEcpmMax: 平均ecpm上限",
            "adMoneyDayMin: 当日广告累计收入下限",
            "adMoneyDayMax: 当日广告累计收入上限",
            "adMoneyAllMin: 累计广告收入下限",
            "adMoneyAllMax: 累计广告收入上限",
            "dramaIds: 剧",
            "unlockTypes: 解锁类型1=k币，2=vip，3=剧卡 12=k币+vip，13=k币+剧卡，23=vip+剧卡，123=k币+vip+剧卡",
            "watchCountDayMin: 当日观看集数下限",
            "watchCountDayMax: 当日观看集数上限",
            "watchCountDayFeeMin: 当日观看付费集数下限",
            "watchCountDayFeeMax: 当日观看付费集数上限",
            "watchCountDayFreeMin: 当日观看免费集数下限",
            "watchCountDayFreeMax: 当日观看免费集数上限",
            "watchCountAllMin: 累计观看集数下限",
            "watchCountAllMax: 累计观看集数下限",
            "watchCountAllFeeMin: 累计观看付费集数下限",
            "watchCountAllFeeMax: 累计观看付费集数上限",
            "watchCountAllFreeMin: 累计观看免费集数下限",
            "watchCountAllFreeMax: 累计观看免费集数上限"
    })
    @PostMapping("/getFastMemberGroupDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberGroupPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberGroupPO fastMemberGroup = fastMemberGroupService.queryById(params);
        return ResultVO.success(fastMemberGroup);
    }

    @ApiName(value = "group-人群分组添加", folder = {"group"})
    @ApiParamsIn({
            "groupName:1:str:人群名称",
            "sequence:1:str:排序越大优先级越高",
            "state:1:str:状态0禁用1启用",
            "miniType:1:str:1微信小程序;2抖音小程序;3H5;4快手;5快应用",
            "remark:1:str:备注信息",
            "linkIds:1:str:渠道",
            "linkIdsRemove:1:str:排除渠道",
            "regionIds:1:str:地区id们",
            "areaIds:1:str:区域id们",
            "cityLevels:1:str:城市类型们",
            "phoneBrands:1:str:手机类型们",
            "rechargeTypes:1:str:充值类型们",
            "appIds:1:str:小程序appid们",
            "startTypes:1:str:启动类型",
            "todayLoginFlag:1:str:今日是否登录-1不限制0否1是",
            "addDeskFlag:1:str:是否加桌-1不限制0否1是",
            "todaySignFlag:1:str:今日签到-1不限制0否1是",
            "registDaysMin:1:str:注册天数，下限",
            "registDaysMax:1:str:注册天数，上限",
            "activeDaysMin:1:str:激活天数，下限",
            "activeDaysMax:1:str:激活天数，上限",
            "regActiveDaysMin:1:str:注册至今活跃天数下限",
            "regActiveDaysMax:1:str:注册至今活跃天数上限",
            "lastActiveDaysMin:1:str:最后活跃天数下限",
            "lastActiveDaysMax:1:str:最后活跃天数上限",
            "bindLinkHoursMin:1:str:渠道绑定小时下限",
            "bindLinkHoursMax:1:str:渠道绑定小时上限",
            "outBackDaysMin:1:str:回流天数下限",
            "outBackDaysMax:1:str:回流天数上限",
            "lastVisitDaysMin:1:str:上次访问下限",
            "lastVisitDaysMax:1:str:上次访问上限",
            "rechargeFlag:1:str:是否付费-1不限0否1是2广告",
            "rechargeCountMin:1:str:付费次数-下限",
            "rechargeCountMax:1:str:付费次数-上限",
            "orderCountMin:1:str:下单次数-下限",
            "orderCountMax:1:str:下单次数-上限",
            "rechargeMoneyMin:1:str:充值金额-下限",
            "rechargeMoneyMax:1:str:充值金额-上限",
            "firstMoneyMin:1:str:首充金额-下限",
            "firstMoneyMax:1:str:首充金额-上限",
            "lastMoneyMin:1:str:最后充值金额-下限",
            "lastMoneyMax:1:str:最后充值金额-上限",
            "avgMoneyMin:1:str:平均充值金额-下限",
            "avgMoneyMax:1:str:平均充值金额-上限",
            "accountCoinMin:1:str:账户余额k币-下限",
            "accountCoinMax:1:str:账户余额k币-上限",
            "consumeCoinMin:1:str:消费币-下限",
            "consumeCoinMax:1:str:消费币-上限",
            "lastRechargeDaysMin:1:str:最后充值距今天数下限",
            "lastRechargeDaysMax:1:str:最后充值距今天数上限",
            "lastRechargeEnterCountMin:1:str:最后充值，进入充值页面次数下限",
            "lastRechargeEnterCountMax:1:str:最后充值，进入充值页面次数上限",
            "adReceiveCountMin:1:str:广告接受次数下限",
            "adReceiveCountMax:1:str:广告接受次数上限",
            "adFinishCountMin:1:str:广告看完次数下限",
            "adFinishCountMax:1:str:广告看完次数上限",
            "avgEcpmMin:1:str:平均ecpm下限",
            "avgEcpmMax:1:str:平均ecpm上限",
            "adMoneyDayMin:1:str:当日广告累计收入下限",
            "adMoneyDayMax:1:str:当日广告累计收入上限",
            "adMoneyAllMin:1:str:累计广告收入下限",
            "adMoneyAllMax:1:str:累计广告收入上限",
            "dramaIds:1:str:剧",
            "unlockTypes:1:str:解锁类型1=k币，2=vip，3=剧卡 12=k币+vip，13=k币+剧卡，23=vip+剧卡，123=k币+vip+剧卡",
            "watchCountDayMin:1:str:当日观看集数下限",
            "watchCountDayMax:1:str:当日观看集数上限",
            "watchCountDayFeeMin:1:str:当日观看付费集数下限",
            "watchCountDayFeeMax:1:str:当日观看付费集数上限",
            "watchCountDayFreeMin:1:str:当日观看免费集数下限",
            "watchCountDayFreeMax:1:str:当日观看免费集数上限",
            "watchCountAllMin:1:str:累计观看集数下限",
            "watchCountAllMax:1:str:累计观看集数下限",
            "watchCountAllFeeMin:1:str:累计观看付费集数下限",
            "watchCountAllFeeMax:1:str:累计观看付费集数上限",
            "watchCountAllFreeMin:1:str:累计观看免费集数下限",
            "watchCountAllFreeMax:1:str:累计观看免费集数上限"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insertFastMemberGroup")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberGroupPO params) {
        String verifyRes = verifyParam(params);
        if (StrUtil.isNotEmpty(verifyRes)) {
            return ResultVO.error(verifyRes);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMemberGroupService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "group-人群分组更新", folder = {"group"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "groupName:1:str:人群名称",
            "sequence:1:str:排序越大优先级越高",
            "state:1:str:状态0禁用1启用",
            "miniType:1:str:1微信小程序;2抖音小程序;3H5;4快手;5快应用",
            "remark:1:str:备注信息",
            "linkIds:1:str:渠道",
            "linkIdsRemove:1:str:排除渠道",
            "regionIds:1:str:地区id们",
            "areaIds:1:str:区域id们",
            "cityLevels:1:str:城市类型们",
            "phoneBrands:1:str:手机类型们",
            "rechargeTypes:1:str:充值类型们",
            "appIds:1:str:小程序appid们",
            "startTypes:1:str:启动类型",
            "todayLoginFlag:1:str:今日是否登录-1不限制0否1是",
            "addDeskFlag:1:str:是否加桌-1不限制0否1是",
            "todaySignFlag:1:str:今日签到-1不限制0否1是",
            "registDaysMin:1:str:注册天数，下限",
            "registDaysMax:1:str:注册天数，上限",
            "activeDaysMin:1:str:激活天数，下限",
            "activeDaysMax:1:str:激活天数，上限",
            "regActiveDaysMin:1:str:注册至今活跃天数下限",
            "regActiveDaysMax:1:str:注册至今活跃天数上限",
            "lastActiveDaysMin:1:str:最后活跃天数下限",
            "lastActiveDaysMax:1:str:最后活跃天数上限",
            "bindLinkHoursMin:1:str:渠道绑定小时下限",
            "bindLinkHoursMax:1:str:渠道绑定小时上限",
            "outBackDaysMin:1:str:回流天数下限",
            "outBackDaysMax:1:str:回流天数上限",
            "lastVisitDaysMin:1:str:上次访问下限",
            "lastVisitDaysMax:1:str:上次访问上限",
            "rechargeFlag:1:str:是否付费-1不限0否1是2广告",
            "rechargeCountMin:1:str:付费次数-下限",
            "rechargeCountMax:1:str:付费次数-上限",
            "orderCountMin:1:str:下单次数-下限",
            "orderCountMax:1:str:下单次数-上限",
            "rechargeMoneyMin:1:str:充值金额-下限",
            "rechargeMoneyMax:1:str:充值金额-上限",
            "firstMoneyMin:1:str:首充金额-下限",
            "firstMoneyMax:1:str:首充金额-上限",
            "lastMoneyMin:1:str:最后充值金额-下限",
            "lastMoneyMax:1:str:最后充值金额-上限",
            "avgMoneyMin:1:str:平均充值金额-下限",
            "avgMoneyMax:1:str:平均充值金额-上限",
            "accountCoinMin:1:str:账户余额k币-下限",
            "accountCoinMax:1:str:账户余额k币-上限",
            "consumeCoinMin:1:str:消费币-下限",
            "consumeCoinMax:1:str:消费币-上限",
            "lastRechargeDaysMin:1:str:最后充值距今天数下限",
            "lastRechargeDaysMax:1:str:最后充值距今天数上限",
            "lastRechargeEnterCountMin:1:str:最后充值，进入充值页面次数下限",
            "lastRechargeEnterCountMax:1:str:最后充值，进入充值页面次数上限",
            "adReceiveCountMin:1:str:广告接受次数下限",
            "adReceiveCountMax:1:str:广告接受次数上限",
            "adFinishCountMin:1:str:广告看完次数下限",
            "adFinishCountMax:1:str:广告看完次数上限",
            "avgEcpmMin:1:str:平均ecpm下限",
            "avgEcpmMax:1:str:平均ecpm上限",
            "adMoneyDayMin:1:str:当日广告累计收入下限",
            "adMoneyDayMax:1:str:当日广告累计收入上限",
            "adMoneyAllMin:1:str:累计广告收入下限",
            "adMoneyAllMax:1:str:累计广告收入上限",
            "dramaIds:1:str:剧",
            "unlockTypes:1:str:解锁类型1=k币，2=vip，3=剧卡 12=k币+vip，13=k币+剧卡，23=vip+剧卡，123=k币+vip+剧卡",
            "watchCountDayMin:1:str:当日观看集数下限",
            "watchCountDayMax:1:str:当日观看集数上限",
            "watchCountDayFeeMin:1:str:当日观看付费集数下限",
            "watchCountDayFeeMax:1:str:当日观看付费集数上限",
            "watchCountDayFreeMin:1:str:当日观看免费集数下限",
            "watchCountDayFreeMax:1:str:当日观看免费集数上限",
            "watchCountAllMin:1:str:累计观看集数下限",
            "watchCountAllMax:1:str:累计观看集数下限",
            "watchCountAllFeeMin:1:str:累计观看付费集数下限",
            "watchCountAllFeeMax:1:str:累计观看付费集数上限",
            "watchCountAllFreeMin:1:str:累计观看免费集数下限",
            "watchCountAllFreeMax:1:str:累计观看免费集数上限"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateFastMemberGroup")
    public ResultVO<?> update(HttpServletRequest request, FastMemberGroupPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        String verifyRes = verifyParam(params);
        if (StrUtil.isNotEmpty(verifyRes)) {
            return ResultVO.error(verifyRes);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMemberGroupService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    private String verifyParam(FastMemberGroupPO params) {
        if (StrUtil.isEmpty(params.getGroupName())) {
            return "人群名称不能为空";
        }
        return "";
    }
}
