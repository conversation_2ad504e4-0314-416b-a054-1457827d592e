/*
 * Powered By fast.up
 */
package com.fast.controller.group;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.group.FastMemberGroupAreaPO;
import com.fast.service.group.FastMemberGroupAreaService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/group")
public class FastMemberGroupAreaController extends BaseController {

    @Autowired
    private FastMemberGroupAreaService fastMemberGroupAreaService;

    @ApiName(value = "group-查询列表", folder = {"group"})
    @PostMapping("/getFastMemberGroupAreaList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberGroupAreaPO params, PageVO pageVO) {
        return fastMemberGroupAreaService.queryPageList(params, pageVO);
    }

    @ApiName(value = "group-查询单个详情", folder = {"group"})
    @PostMapping("/getFastMemberGroupAreaDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberGroupAreaPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberGroupAreaPO fastMemberGroupArea = fastMemberGroupAreaService.queryById(params);
        return ResultVO.success(fastMemberGroupArea);
    }

    @ApiName(value = "group-添加", folder = {"group"})
    @PostMapping("/insertFastMemberGroupArea")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberGroupAreaPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberGroupAreaService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "group-更新", folder = {"group"})
    @PostMapping("/updateFastMemberGroupArea")
    public ResultVO<?> update(HttpServletRequest request, FastMemberGroupAreaPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = fastMemberGroupAreaService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
