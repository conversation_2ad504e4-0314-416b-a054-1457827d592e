/*
 * Powered By fast.up
 */
package com.fast.controller.group;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.group.FastMemberGroupDetailPO;
import com.fast.service.group.FastMemberGroupDetailService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/group")
public class FastMemberGroupDetailController extends BaseController {

    @Autowired
    private FastMemberGroupDetailService fastMemberGroupDetailService;

    @ApiName(value = "group-查询列表", folder = {"group"})
    @PostMapping("/getFastMemberGroupDetailList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberGroupDetailPO params, PageVO pageVO) {
        return fastMemberGroupDetailService.queryPageList(params, pageVO);
    }

    @ApiName(value = "group-查询单个详情", folder = {"group"})
    @PostMapping("/getFastMemberGroupDetailDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberGroupDetailPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberGroupDetailPO fastMemberGroupDetail = fastMemberGroupDetailService.queryById(params);
        return ResultVO.success(fastMemberGroupDetail);
    }

    @ApiName(value = "group-添加", folder = {"group"})
    @PostMapping("/insertFastMemberGroupDetail")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberGroupDetailPO params) {
        MethodVO methodVO = fastMemberGroupDetailService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "group-更新", folder = {"group"})
    @PostMapping("/updateFastMemberGroupDetail")
    public ResultVO<?> update(HttpServletRequest request, FastMemberGroupDetailPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = fastMemberGroupDetailService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
