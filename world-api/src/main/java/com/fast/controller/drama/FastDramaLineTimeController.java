/*
 * Powered By fast.up
 */
package com.fast.controller.drama;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.drama.FastDramaLineTimePO;
import com.fast.service.drama.FastDramaLineTimeService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/drama")
public class FastDramaLineTimeController extends BaseController {

    @Autowired
    private FastDramaLineTimeService fastDramaLineTimeService;

    @ApiParamsIn({
            "dramaId:0:int:短剧id",
            "type:0:str:用途 3三方分发",
    })
    @ApiParamsOut({
            "ok:ok",
    })
    @ApiName(value = "drama-查询列表", folder = {"drama"})
    @PostMapping("/getFastDramaLineTimeList")
    public ResultVO<?> getList(HttpServletRequest request, FastDramaLineTimePO params, PageVO pageVO) {
        return fastDramaLineTimeService.queryPageList(params, pageVO);
    }

    @ApiName(value = "drama-查询单个详情", folder = {"drama"})
    @PostMapping("/getFastDramaLineTimeDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastDramaLineTimePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastDramaLineTimePO fastDramaLineTime = fastDramaLineTimeService.queryById(params);
        return ResultVO.success(fastDramaLineTime);
    }

    @ApiParamsIn({
            "encryptionId:0:str:加密id",
            "offlineTime:0:str:下线时间",
    })
    @ApiParamsOut({
            "ok:ok",
    })
    @ApiName(value = "drama-添加", folder = {"drama"})
    @PostMapping("/insertFastDramaLineTime")
    public ResultVO<?> insert(HttpServletRequest request, FastDramaLineTimePO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastDramaLineTimeService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiParamsIn({
            "dramaLineTimeList->type:1:int:用途 2端原生 3三方分发",
            "dramaLineTimeList->subType:1:int:子类型 字典code的id",
            "dramaLineTimeList->onlineTime:1:str:上线时间",
            "dramaId:1:str:短剧id",
            "type:1:str:2端原生 3三方分发",
    })
    @ApiParamsOut({
            "ok:ok",
    })
    @ApiName(value = "drama-批量新增", folder = {"drama"})
    @PostMapping("/batchInsertFastDramaLineTime")
    public ResultVO<?> batchInsertFastDramaLineTime(HttpServletRequest request, @RequestBody FastDramaLineTimePO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastDramaLineTimeService.batchInsert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiParamsIn({
            "encryptionId:0:str:加密id",
            "offlineTime:0:str:下线时间",
    })
    @ApiParamsOut({
            "ok:ok",
    })
    @ApiName(value = "drama-更新", folder = {"drama"})
    @PostMapping("/updateFastDramaLineTime")
    public ResultVO<?> update(HttpServletRequest request, FastDramaLineTimePO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastDramaLineTimeService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
