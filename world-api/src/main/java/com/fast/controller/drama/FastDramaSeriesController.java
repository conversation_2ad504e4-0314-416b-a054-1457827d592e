/*
 * Powered By fast.up
 */
package com.fast.controller.drama;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.po.drama.FastDramaSeriesPO;
import com.fast.po.fee.FastFeeRulePO;
import com.fast.service.aliyun.AliCdnService;
import com.fast.service.cache.CacheResetService;
import com.fast.service.drama.FastDramaI18nService;
import com.fast.service.drama.FastDramaSeriesService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.fee.FastFeeRuleService;
import com.fast.service.oss.OssService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.VideoUtil;
import com.fast.utils.file.FileUtil;
import com.fast.utils.poi.ExcelImportUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.drama.FastDramaSeriesExcel;
import com.fast.vo.drama.FastDramaVO;
import com.fast.vo.drama.SeriesBatchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 短剧剧集
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastDramaSeries")
public class FastDramaSeriesController extends BaseController {

    @Autowired
    private FastDramaSeriesService fastDramaSeriesService;
    @Autowired
    private FastDramaService fastDramaService;
    @Autowired
    private FastFeeRuleService fastFeeRuleService;
    @Autowired
    private CacheResetService cacheResetService;
    @Autowired
    private OssService ossService;
    @Autowired
    private AliCdnService aliCdnService;
    @Autowired
    private FastDramaI18nService fastDramaI18nService;

    /**
     * 智能重新排序
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "智能重新排序", folder = {StaticFolder.FOLDER_RETAIL_SYSTEM})
    @ApiParamsIn({
            "encryptionId:1:str:剧加密id",
            "playType:播放源0通用1腾讯2抖音3快手"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/resortByMind", method = {RequestMethod.POST})
    public ResultVO<?> resortByMind(HttpServletRequest request, FastDramaSeriesPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("剧加密id不能为空");
        }
        if (isBlank(params.getLanguageCode())) {
            return ResultVO.error("语言编码不能为空");
        }
        Integer dramaId = decodeInt(params.getEncryptionId());
        params.setDramaId(dramaId);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastDramaSeriesService.resortByMind(params);
        if (methodVO.getCode() == 0) {
            // 清除缓存
            String key = StaticVar.FEE_RULE_ID + sessionVO.getOfficialId() + StaticVar.UNDERLINE + dramaId;
            RedisUtil.del(key);
            key = StaticVar.DRAMA_SERIES + dramaId + ":" + params.getLanguageCode() + ":" + params.getCaptionType();
            RedisUtil.del(key);
            RedisUtil.del(StaticVar.DRAMA_INFO_ID + dramaId);
            cacheResetService.resetCacheFeeRule(null, dramaId);
            cacheResetService.resetCacheMiniSet(null, dramaId);
            cacheResetService.resetCacheDramaNumSet(dramaId);
        }
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 剧集设置-设置关注剧集
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "剧集设置-设置关注剧集", folder = {StaticFolder.FOLDER_RETAIL_SYSTEM})
    @ApiParamsIn({"encryptionId:1:str:剧加密id", "followNum:1:int:集号"})
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateFollowNum", method = {RequestMethod.POST})
    public ResultVO<?> updateFollowNum(HttpServletRequest request, FastFeeRulePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("剧加密id不能为空");
        }
        if (params.getFollowNum() == null || params.getFollowNum() <= 0) {
            return ResultVO.error("集号不合法");
        }
        Integer dramaId = decodeInt(params.getEncryptionId());
        params.setDramaId(dramaId);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        params.setCreatorId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        MethodVO methodVO = fastFeeRuleService.updateFollowNum(params);
        if (methodVO.getCode() == 0) {
            String key = StaticVar.DRAMA_INFO_ID + dramaId;
            RedisUtil.del(key);
        }
        cacheResetService.resetCacheFeeRule(sessionVO.getOfficialId(), dramaId);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 剧集列表查询
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "剧集列表查询", folder = StaticFolder.FOLDER_RETAIL_SYSTEM)
    @ApiParamsIn({
            "encryptionId:1:str:剧加密id",
            "languageCode:1:str:语言编码",
            "captionType:1:int:字幕类型（1、内嵌字幕；2、外挂字幕）",
    })
    @ApiParamsOut({
            "encryptionId:集加密id",
            "playType:1:int:0默认播放终端0通用1微信小程序2抖音小程序3快手小程序",
            "seriesNum:集号",
            "follow:关注剧集，0否1是",
            "sortOrder:排序顺序 1=倒序;2=正序",
            "seriesTime:集长（秒）",
            "title:漫画简介",
            "url:视频播放地址",
            "mediaId:腾讯媒资id",
            "albumId:抖音剧目id",
            "episodeId:抖音剧集id",
            "fromDramaId:源剧id",
            "playRatio:剧集观看进度",
            "urlOrg:原始素材url",
            "captionLangs:字幕语种",
    })
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastDramaSeriesPO params, PageVO pageVO) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        if (params.getSortOrder() == null) {
            params.setSortOrder(2);
        }
        Integer dramaId = decodeInt(params.getEncryptionId());
        if (dramaId == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        if (params.getPlayType() == null) {
            params.setPlayType(0);
        }
        params.setDramaId(dramaId);
        params.setDelFlag(StaticVar.NO);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setUserId(sessionVO.getUserId());
        return fastDramaSeriesService.queryPageList(sessionVO, params, pageVO);
    }

    /**
     * 查询短剧剧集
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "查询短剧剧集", folder = StaticFolder.FOLDER_RETAIL_SYSTEM)
    @ApiParamsIn({"encryptionId:1:str:剧加密id"})
    @ApiParamsOut({
            "encryptionId:集加密id",
            "seriesNum:集号",
            "seriesTime:集长（秒）",
            "title:漫画简介",
            "url:视频播放地址",
            "urlOrg:原始素材url"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastDramaSeriesPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastDramaSeriesPO fastDramaSeries = fastDramaSeriesService.queryById(sessionVO, params);
        return ResultVO.success(fastDramaSeries);
    }

    /**
     * 新增短剧剧集
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "新增短剧剧集", folder = StaticFolder.FOLDER_RETAIL_SYSTEM)
    @ApiParamsIn({
            "encryptionId:1:str:短剧加密id",
            "title:0:str:漫画简介",
            "seriesCover:0:str:章节封面",
            "url:1:str:视频/图片地址/小说章节内容",
    })
    @ApiParamsOut({"success"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastDramaSeriesPO params) {
        // 短剧id
        Integer dramaId = decodeInt(params.getEncryptionId());
        if (dramaId == null) {
            return ResultVO.error("id不能为空");
        }
        if (isBlank(params.getLanguageCode())) {
            return ResultVO.error("语言编码不能为空");
        }
        params.setDramaId(dramaId);
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        switch (sessionVO.getContentType()) {
            case 1: {
                // 计算视频时长
                params.setSeriesTime(VideoUtil.getM3u8Duration(params.getUrl()));
                if (params.getSeriesTime() < 0) {
                    return ResultVO.error("获取视频失败,请检查视频链接是否正确:" + params.getUrl());
                }
                params.setUrl(aliCdnService.replaceHost(params.getUrl()));
                if (params.getSeriesTime() != null && params.getSeriesTime() > 65535) {
                    return ResultVO.error("时长过大,不能超65535");
                }
            }
            break;
            case 2: {
                params.setUrl(aliCdnService.replaceHost(params.getUrl()).replace(ossService.getOssHost(false), StaticVar.EMPTY));
                if (notBlank(params.getSeriesCover())) {
                    params.setSeriesCover(aliCdnService.replaceHost(params.getSeriesCover()).replace(ossService.getOssHost(false), StaticVar.EMPTY));
                }
                params.setSeriesTime(params.getUrl().split(StaticVar.COMMA).length);

                // 计算第N章节
                FastDramaSeriesPO count = new FastDramaSeriesPO();
                count.setDramaId(params.getDramaId());
                count.setDelFlag(StaticVar.NO);
                params.setSeriesNum(fastDramaSeriesService.queryMaxSeriesNum(count) + 1);
            }
            break;
            case 3: {
                // 计算章节字数
                params.setSeriesTime(params.getUrl().length());
            }
            break;
        }
        MethodVO methodVO = fastDramaSeriesService.insert(params);

        // 清除缓存信息
        RedisUtil.del(StaticVar.DRAMA_INFO_ID + params.getDramaId());
        String key = StaticVar.FEE_RULE_ID + sessionVO.getOfficialId() + StaticVar.UNDERLINE + params.getDramaId();
        RedisUtil.del(key);
        key = StaticVar.DRAMA_SERIES + params.getDramaId() + ":" + params.getLanguageCode() + ":" + params.getCaptionType();
        RedisUtil.del(key);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 新增小说章节-批量
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "新增小说章节-批量", folder = StaticFolder.FOLDER_RETAIL_SYSTEM)
    @ApiParamsIn({
            "body:1:str:{encryptionId:123,seriesList:[{title:'标题1',seriesNum:9,url:'小说内容'},{...}]}",
            "字段解释:1:str:encryptionId=短剧加密id",
            "字段解释:1:str:title=章节标题",
            "字段解释:1:str:url=章节内容"
    })
    @ApiParamsOut({"success"})
    @RequestMapping(value = "/insertBatch", method = {RequestMethod.POST})
    public ResultVO<?> insertBatch(HttpServletRequest request, @RequestBody SeriesBatchVO params) {
        // 短剧id
        Integer dramaId = decodeInt(params.getEncryptionId());
        if (dramaId == null) {
            return ResultVO.error("id不能为空");
        }
        if (isBlank(params.getLanguageCode())) {
            return ResultVO.error("语言编码不能为空");
        }

        params.setDramaId(dramaId);
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());

        MethodVO methodVO = fastDramaSeriesService.insertBatch(params);

        // 清除缓存信息
        RedisUtil.del(StaticVar.DRAMA_INFO_ID + params.getDramaId());
        String key = StaticVar.FEE_RULE_ID + sessionVO.getOfficialId() + StaticVar.UNDERLINE + params.getDramaId();
        RedisUtil.del(key);
        key = StaticVar.DRAMA_SERIES + params.getDramaId() + ":" + params.getLanguageCode() + ":" + params.getCaptionType();
        RedisUtil.del(key);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "导入短剧剧集", folder = StaticFolder.FOLDER_RETAIL_SYSTEM)
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "languageCode:1:str:语言编码",
            "file:1:file:Excel文件流",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @RequestMapping(value = "/importBatch", method = {RequestMethod.POST})
    public ResultVO<?> importBatch(HttpServletRequest request, MultipartFile file, FastDramaSeriesPO params) {
        SessionVO sessionUser = getSessionVO(request);
        params.setCreatorId(sessionUser.getUserId());
        params.setUpdatorId(sessionUser.getUserId());
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (isBlank(params.getLanguageCode())) {
            return ResultVO.error("语言编码不能为空");
        }
        if (params.getCaptionType() == null) {
            return ResultVO.error("字幕类型不能为空");
        }
        // 短剧id
        Integer dramaId = decodeInt(params.getEncryptionId());
        if (dramaId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setDramaId(dramaId);
        String filePath = FileUtil.uploadFileNamePre(file, StaticVar.IMPORT_TEMP_PATH, "ds-" + sessionUser.getRetailId() + "-");
        try {
            if (file.getSize() > StaticVar.IMPORT_SIZE) {
                return ResultVO.error("上传文件大小不能超过1MB,您可以选择分批导入");
            }
            boolean result = ExcelImportUtil.isExcel(filePath);
            if (!result) {
                return ResultVO.error("导入文件格式不匹配，请检查格式是否正确");
            }
            List<FastDramaSeriesExcel> list = ExcelImportUtil.readExcel(FastDramaSeriesExcel.class, filePath);
            if (CollUtil.isEmpty(list)) {
                return ResultVO.error("导入文件没有数据");
            }
            FastDramaVO dramaVO = fastDramaService.queryInfoByRedis(dramaId);
            Set<String> names = new HashSet<>();
            List<String> errorNames = new ArrayList<>();
            for (FastDramaSeriesExcel excel : list) {
                if (!names.contains(excel.getDramaName())) {
                    if (!dramaVO.getDramaName().equals(excel.getDramaName())) {
                        errorNames.add(excel.getDramaName());
                        continue;
                    }
                    names.add(excel.getDramaName());
                }
            }
            for (FastDramaSeriesExcel excel : list) {
                if (StrUtil.isEmpty(excel.getSeriesNumStr())) {
                    return ResultVO.error("剧集数不能为空");
                }
                Double seriesNumPart = Double.valueOf(excel.getSeriesNumStr());// 获取集号
                int seriesNum = seriesNumPart.intValue();// toInteger(excel.getSeriesNumStr());
                if (seriesNum > 65535) {
                    return ResultVO.error("剧集数过大,不能超65535");
                }
                excel.setSeriesNum(seriesNum);
                excel.setSeriesNumPart(seriesNumPart);
                if (isBlank(excel.getUrl())) {
                    return ResultVO.error("oss编码不能为空");
                }
                if (excel.getUrl().length() > 150) {
                    return ResultVO.error("oss编码过长");
                }

                log.info("短剧 {} 导入的剧集 {}，语言: {}，字幕类型: {}", dramaId, excel.getUrl(), params.getLanguageCode(), params.getCaptionType());

                if ((!excel.getUrl().contains("/") || !excel.getUrl().endsWith(".m3u8")) && !excel.getUrl().endsWith(".mp4")) {
                    excel.setUrl(excel.getUrl() + "/" + seriesNum + "/" + seriesNum + ".m3u8");
                }

                switch (sessionUser.getContentType()) {
                    case 1:
                    case 4:
                        String videoUrl = ossService.getOssHost(false) + excel.getUrl();
                        int seriesTime;
                        if (excel.getUrl().endsWith(".mp4")) {
                            seriesTime = VideoUtil.getMp4Duration(videoUrl);
                        } else {
                            seriesTime = VideoUtil.getM3u8Duration(videoUrl);
                        }
                        log.info("videoUrl: {}, seriesTime: {}", videoUrl, seriesTime);
                        if (seriesTime < 0) {
                            return ResultVO.error("获取视频失败,请检查视频链接是否正确:" + ossService.getOssHost(false) + excel.getUrl());
                        }
                        excel.setSeriesTime(seriesTime);
                        break;
                    case 2:
                        excel.setSeriesTime(toInteger(excel.getSeriesTimeStr()));
                        if (excel.getSeriesTime() == null) {
                            return ResultVO.error("图片数不能为空");
                        }
                        if (excel.getSeriesTime() > 65535) {
                            return ResultVO.error("图片数不能超65535");
                        }
                        String u = excel.getUrl();
                        if (!u.endsWith("/")) {
                            u = u + "/";
                        }
                        List<String> url = new ArrayList<>();
                        for (int i = 1; i <= excel.getSeriesTime(); i++) {
                            url.add(u + i + ".JPEG");
                        }
                        excel.setUrl(StrUtil.join(url));
                        break;
                }
            }
            CollUtil.removeRepeat(errorNames);
            // 短剧名称不存在-错误
            if (CollUtil.hasContent(errorNames)) {
                return ResultVO.error(500, "导入的剧名要和当前打开的剧名相同:" + StrUtil.join(errorNames));
            }
            MethodVO methodVO = fastDramaSeriesService.importBatch(params, list);

            SessionVO sessionVO = getSessionVO(request);

            // 清除缓存信息
            String key = StaticVar.FEE_RULE_ID + sessionVO.getOfficialId() + StaticVar.UNDERLINE + dramaId;
            RedisUtil.del(key);
            key = StaticVar.DRAMA_SERIES + dramaId + ":" + params.getLanguageCode() + ":" + params.getCaptionType();
            RedisUtil.del(key);

            RedisUtil.del(StaticVar.DRAMA_INFO_ID + dramaId);

            cacheResetService.resetCacheFeeRule(null, dramaId);
            cacheResetService.resetCacheMiniSet(null, dramaId);
            cacheResetService.resetCacheDramaNumSet(dramaId);
            return ResultVO.fromMethodVO(methodVO);
        } catch (Exception e) {
            MyException.print(log, e);
        } finally {
            if (notBlank(filePath)) {
                FileUtil.delFile(filePath);
            }
        }
        return null;
    }

    /**
     * 替换短剧剧集
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "替换短剧剧集", folder = StaticFolder.FOLDER_RETAIL_SYSTEM)
    @ApiParamsIn({
            "encryptionId:1:str:剧集加密id",
            "title:0:str:漫画简介",
            "seriesCover:0:str:章节封面",
            "url:1:str:视频/图片地址",
    })
    @ApiParamsOut({"success"})
    @RequestMapping(value = "/updateReplace", method = {RequestMethod.POST})
    public ResultVO<?> updateReplace(HttpServletRequest request, FastDramaSeriesPO params) {
        SessionVO sessionUser = getSessionVO(request);
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer seriesId = decodeInt(params.getEncryptionId());
        if (seriesId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(seriesId);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        switch (sessionUser.getContentType()) {
            case 1: {
                // 计算视频时长
                params.setSeriesTime(VideoUtil.getM3u8Duration(params.getUrl()));
                if (params.getSeriesTime() < 0) {
                    return ResultVO.error("获取视频失败,请检查视频链接是否正确:" + params.getUrl());
                }
                params.setUrl(aliCdnService.replaceHost(params.getUrl()));
                if (params.getSeriesTime() != null && params.getSeriesTime() > 65535) {
                    return ResultVO.error("时长过大,不能超65535");
                }
            }
            break;
            case 2: {
                params.setUrl(aliCdnService.replaceHost(params.getUrl()));
                if (notBlank(params.getSeriesCover())) {
                    params.setSeriesCover(aliCdnService.replaceHost(params.getSeriesCover()));
                }
                params.setSeriesTime(params.getUrl().split(StaticVar.COMMA).length);
            }
            break;
            case 3: {
                params.setSeriesTime(params.getUrl().length());
            }
        }
        MethodVO methodVO = fastDramaSeriesService.update(params);
        FastDramaSeriesPO seriesParam = new FastDramaSeriesPO();
        seriesParam.setId(seriesId);
        FastDramaSeriesPO series = fastDramaSeriesService.queryById(sessionVO, seriesParam);

        RedisUtil.del(StaticVar.DRAMA_INFO_ID + series.getDramaId());
        // 清除计费规则缓存信息
        cacheResetService.resetCacheFeeRule(null, series.getDramaId());
        // 清除小程序配置缓存
        cacheResetService.resetCacheMiniSet(null, series.getDramaId());
        cacheResetService.resetCacheDramaNumSet(series.getDramaId());
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 删除短剧剧集
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "删除短剧剧集", folder = StaticFolder.FOLDER_RETAIL_SYSTEM)
    @ApiParamsIn({
            "encryptionId:1:str:剧集加密id",
    })
    @ApiParamsOut({"success"})
    @RequestMapping(value = "/updateDel", method = {RequestMethod.POST})
    public ResultVO<?> updateDel(HttpServletRequest request, FastDramaSeriesPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (isBlank(params.getLanguageCode())) {
            return ResultVO.error("语言编码不能为空");
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastDramaSeriesService.updateDel(params);

        // 清除缓存信息
        String key = StaticVar.FEE_RULE_ID + sessionVO.getOfficialId() + StaticVar.UNDERLINE + id;
        RedisUtil.del(key);
        key = StaticVar.DRAMA_SERIES + id + ":" + params.getLanguageCode() + ":" + params.getCaptionType();
        RedisUtil.del(key);
        cacheResetService.resetCacheMiniSet(null, id);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 删除短剧剧集
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "内容库-删除短剧剧集", folder = StaticFolder.FOLDER_RETAIL_SYSTEM)
    @ApiParamsIn({
            "encryptionId:1:str:剧集加密id",
    })
    @ApiParamsOut({"success"})
    @RequestMapping(value = "/updateInvestDel", method = {RequestMethod.POST})
    public ResultVO<?> updateInvestDel(HttpServletRequest request, FastDramaSeriesPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (isBlank(params.getLanguageCode())) {
            return ResultVO.error("语言编码不能为空");
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastDramaSeriesService.updateDel(params);

        // 清除缓存信息
        String key = StaticVar.FEE_RULE_ID + sessionVO.getOfficialId() + StaticVar.UNDERLINE + id;
        RedisUtil.del(key);
        key = StaticVar.DRAMA_SERIES + id + ":" + params.getLanguageCode() + ":" + params.getCaptionType();
        RedisUtil.del(key);
        cacheResetService.resetCacheMiniSet(null, id);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "audit-内容库~修改单集播放源", folder = {StaticFolder.FOLDER_AUDIT})
    @ApiParamsIn({
            "encryptionId:1:int:剧集id",
            "playType:1:int:播放源类型：1腾讯2抖音3快手",
            "mediaId:1:str:腾讯媒资id",
            "albumId:1:str:抖音媒资剧目id",
            "episodeId:1:str:抖音媒资剧集id"

    })
    @ApiParamsOut({"success"})
    @RequestMapping(value = "/updatePlayOne", method = {RequestMethod.POST})
    public ResultVO<?> updatePlayOne(HttpServletRequest request, FastDramaSeriesPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (isBlank(params.getLanguageCode())) {
            return ResultVO.error("语言编码不能为空");
        }
        params.setId(id);
        if (params.getPlayType() == null) {
            return ResultVO.error("播放源类型不能为空");
        }
        if (params.getPlayType() == 1 && params.getMediaId() == null) {
            return ResultVO.error("腾讯媒资id不能为空");
        }
        if (params.getPlayType() == 2) {
            if (StrUtil.isEmpty(params.getAlbumId())) {
                return ResultVO.error("抖音媒资剧目id不能为空");
            }
            if (StrUtil.isEmpty(params.getEpisodeId())) {
                return ResultVO.error("抖音媒资剧集id不能为空");
            }
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastDramaSeriesService.update(params);

        // 清除缓存信息
        String key = StaticVar.FEE_RULE_ID + sessionVO.getOfficialId() + StaticVar.UNDERLINE + id;
        RedisUtil.del(key);
        key = StaticVar.DRAMA_SERIES + id + ":" + params.getLanguageCode() + ":" + params.getCaptionType();
        RedisUtil.del(key);
        cacheResetService.resetCacheMiniSet(null, id);
        return ResultVO.fromMethodVO(methodVO);
    }


}
