/*
 * Powered By fast.up
 */
package com.fast.controller.drama;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.drama.FastDramaRetailPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.service.cache.CacheResetService;
import com.fast.service.drama.FastDramaRetailService;
import com.fast.service.drama.XmanService;
import com.fast.service.retail.FastRetailService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fast")
public class FastDramaRetailController extends BaseController {

    @Autowired
    private FastDramaRetailService fastDramaRetailService;
    @Autowired
    private FastRetailService fastRetailService;
    @Autowired
    private CacheResetService cacheResetService;
    @Autowired
    private XmanService xmanService;

    /**
     * 短剧授权分销商-查询短剧已授权的分销商
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "短剧授权分销商-查询短剧已授权的分销商", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({"encryptionId:1:str:短剧加密id", "retailNameLike:1:str:分销商名称搜索",})
    @ApiParamsOut({
            "retailName:分销商名称",
            "retailId:分销商id",
            "dramaId:短剧id",
            "miniList》miniName:授权小程序名称",
            "state:状态1=启用;0=禁用",
    })
    @RequestMapping(value = "/getDramaRetailDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastDramaRetailPO params, PageVO pageVO) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer dramaId = decodeInt(params.getEncryptionId());
        if (dramaId == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setDramaId(dramaId);
        if (notBlank(params.getRetailNameLike())) {
            FastRetailPO retail = new FastRetailPO();
            retail.setRetailNameLike(params.getRetailNameLike());
            Set<Integer> retailIds = fastRetailService.queryRetailIds(retail);
            if (CollUtil.isEmpty(retailIds)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setRetailIds(StrUtil.join(retailIds));
        }
        SessionVO sessionVO = getSessionVO(request);
        return fastDramaRetailService.getDramaRetailDetail(params, pageVO);
    }

    /**
     * 短剧授权分销商-添加
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "短剧授权分销商-添加", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "dramaIds:1:str:短剧数字id,多个逗号分隔",
            "retailIds:1:str:分销商数字id,多个逗号分隔",
    })
    @ApiParamsOut({"success"})
    @RequestMapping(value = "/insertDramaRetail", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastDramaRetailPO params) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getDramaIds());
        if (isBlank(params.getDramaIds())) {
            return ResultVO.error("短剧id不能为空");
        }
        if (isBlank(params.getRetailIds())) {
            return ResultVO.error("分销商id不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastDramaRetailService.insert(params);

        // 清除缓存
        List<Integer> retailIds = CollUtil.parseIntStr2List(params.getRetailIds());
        for (Integer retailId : retailIds) {
            RedisUtil.del(StaticVar.RETAIL_DRAMA_LIST + retailId);
            RedisUtil.del(StaticVar.RETAIL_MINI_DRAMA_LIST + retailId);
        }
        Set<Integer> retailIdsSet = CollUtil.parseIntStr2Set(params.getRetailIds());
        // 查询挂载分销商
        FastRetailPO retailParam = new FastRetailPO();
        List<FastRetailPO> retailList = fastRetailService.queryList(retailParam);
        for (FastRetailPO retail : retailList) {
            if (retailIdsSet.contains(retail.getId())) {
                // 同步剧
                xmanService.sendAllDramaToRetail(retail.getId());
            }
        }
        cacheResetService.incrVersion(null, null);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 短剧授权分销商-取消
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "短剧授权分销商-取消", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "dramaId:1:int:短剧数字id",
            "dramaIds:0:str:短剧数字id,多个逗号分割",
            "retailIds:1:str:分销商数字id,多个逗号分隔",
    })
    @ApiParamsOut({"success"})
    @RequestMapping(value = "/cancelDramaRetail", method = {RequestMethod.POST})
    public ResultVO<?> cancel(HttpServletRequest request, FastDramaRetailPO params) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getDramaIds());
        if (params.getDramaId() == null && StrUtil.isEmpty(params.getDramaIds())) {
            return ResultVO.error("短剧id不能为空");
        }
        if (isBlank(params.getRetailIds())) {
            return ResultVO.error("分销商id不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastDramaRetailService.cancel(params);

        // 清除缓存
        List<Integer> retailIds = CollUtil.parseIntStr2List(params.getRetailIds());
        for (Integer retailId : retailIds) {
            RedisUtil.del(StaticVar.RETAIL_DRAMA_LIST + retailId);
            RedisUtil.del(StaticVar.RETAIL_MINI_DRAMA_LIST + retailId);
        }
        cacheResetService.incrVersion(null, null);
        return ResultVO.fromMethodVO(methodVO);
    }
}
