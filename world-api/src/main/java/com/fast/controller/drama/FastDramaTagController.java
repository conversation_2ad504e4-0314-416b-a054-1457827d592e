/*
 * Powered By fast.up
 */
package com.fast.controller.drama;

import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.drama.FastDramaTagPO;
import com.fast.service.drama.FastDramaTagService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 短剧标签
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastDramaTag")
public class FastDramaTagController extends BaseController {

    @Autowired
    private FastDramaTagService fastDramaTagService;

    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastDramaTagPO params, PageVO pageVO) {
        return fastDramaTagService.queryPageList(params, pageVO);
    }

    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastDramaTagPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastDramaTagPO fastDramaTag = fastDramaTagService.queryById(params);
        return ResultVO.success(fastDramaTag);
    }

    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastDramaTagPO params) {
        SessionVO sessionUser = getSessionVO(request);
        MethodVO methodVO = fastDramaTagService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastDramaTagPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastDramaTagService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
