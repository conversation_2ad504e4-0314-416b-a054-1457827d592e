/*
 * Powered By fast.up
 */
package com.fast.controller.drama;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.enums.LanguageEnum;
import com.fast.framework.exception.MyException;
import com.fast.po.drama.FastDramaCaptionsPO;
import com.fast.po.language.FastLanguagePO;
import com.fast.service.drama.FastDramaCaptionsService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastDramaCaptions")
public class FastDramaCaptionsController extends BaseController {

    @Autowired
    private FastDramaCaptionsService fastDramaCaptionsService;

    @ApiName(value = "字幕-查询列表", folder = {StaticFolder.FOLDER_RETAIL_SYSTEM})
    @ApiParamsIn(value = {
            "dramaId:1:int:短剧ID",
            "languageCode:1:str:短剧语种",
            "subLang:1:str:字幕语种",
            "seriesNum:0:int:剧集数",
    })
    @ApiParamsOut(value = {
            "id:主键ID",
            "dramaId:短剧ID",
            "languageCode:短剧语种",
            "seriesNum:剧集数",
            "subLang:字幕语种",
            "url:字幕文件地址",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastDramaCaptionsPO params, PageVO pageVO) {
        return fastDramaCaptionsService.queryPageList(params, pageVO);
    }

    @ApiName(value = "字幕-查询已配置的语种", folder = {StaticFolder.FOLDER_RETAIL_SYSTEM})
    @ApiParamsIn(value = {
            "dramaId:1:int:短剧ID",
            "languageCode:1:str:短剧语种",
            "seriesNum:1:int:剧集数",
    })
    @ApiParamsOut(value = {
            "name:名称",
            "code:编码",
    })
    @PostMapping("/getLanguages")
    public ResultVO<?> getLanguages(HttpServletRequest request, FastDramaCaptionsPO params) {
        Set<String> codes = fastDramaCaptionsService.getSubLangCodes(params.getDramaId(), params.getLanguageCode(), params.getSeriesNum());
        List<FastLanguagePO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(codes)) {
            for (String code : codes) {
                FastLanguagePO po = new FastLanguagePO();
                po.setCode(code);
                po.setName(LanguageEnum.ofCode(code).getName());
                list.add(po);
            }
        } else {
            FastLanguagePO po = new FastLanguagePO();
            po.setCode(LanguageEnum.ENGLISH.getCode());
            po.setName(LanguageEnum.ENGLISH.getName());
            list.add(po);
        }
        return ResultVO.success(list);
    }

    @ApiName(value = "字幕-导入", folder = {StaticFolder.FOLDER_RETAIL_SYSTEM})
    @ApiParamsIn(value = {
            "dramaId:1:int:短剧ID",
            "languageCode:1:str:短剧语种",
            "subLang:1:str:字幕语种",
            "urls:1:array:字幕文件地址集合",
    })
    @ApiParamsOut(value = {
            "status:ok",
    })
    @PostMapping("/insertBatch")
    public ResultVO<?> insertBatch(HttpServletRequest request, @RequestBody FastDramaCaptionsPO params) {
        if (params.getDramaId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isBlank(params.getLanguageCode())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isBlank(params.getSubLang())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (CollUtil.isEmpty(params.getUrls())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        MethodVO methodVO = fastDramaCaptionsService.deleteBy(params);
        if (methodVO.getCode() != 0) {
            return ResultVO.error(StaticStr.SYS_ERROR);
        }
        SessionVO sessionVO = getSessionVO(request);
        Date nowDate = DateUtil.getNowDate();

        List<FastDramaCaptionsPO> items = new ArrayList<>();
        for (String url : params.getUrls()) {
            FastDramaCaptionsPO item = new FastDramaCaptionsPO();
            item.setDramaId(params.getDramaId());
            item.setLanguageCode(params.getLanguageCode());
            item.setSeriesNum(getNum(url));
            item.setSubLang(params.getSubLang());
            item.setUrl(url);
            item.setCreatorId(sessionVO.getUserId());
            item.setCreateTime(nowDate);
            items.add(item);
        }
        methodVO = fastDramaCaptionsService.insertBatch(items);

        return ResultVO.fromMethodVO(methodVO);
    }

    private Integer getNum(String url) {
        try {
            String filename = StrUtil.substringAfterLast(url, "/");
            String num = StrUtil.substringBefore(filename, ".");
            return Integer.valueOf(num);
        } catch (Exception e) {
            throw new MyException("字幕文件地址不合法:" + url);
        }
    }

}
