/*
 * Powered By fast.up
 */
package com.fast.controller.drama;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.LanguageEnum;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.po.drama.FastDramaI18nPO;
import com.fast.po.drama.FastDramaLineTimePO;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.fee.FastFeeRulePO;
import com.fast.service.cache.CacheResetService;
import com.fast.service.drama.FastDramaI18nService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.fee.FastFeeRuleService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.drama.FastDramaVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 短剧
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastDrama")
public class FastDramaController extends BaseController {

    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastFeeRuleService feeRuleService;
    @Autowired
    private CacheResetService cacheResetService;
    @Autowired
    private FastDramaMapper fastDramaMapper;
    @Autowired
    private FastDramaI18nService fastDramaI18nService;

    /**
     * 查询短剧列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "查询短剧列表", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "feeFlag:0:int:0都不选1付费2免费9都选",
            "nativeAccountId:0:int:端原生账号id",
            "nativePrincipalId:0:int:端原生主体id",
            "nativeDramaSource:0:int:端原生内容来源 1新建 2三方同步",
            "thirdPlatformId:0:int:三方分发平台id",
            "principalId:0:int:三方分发主体id",
            "accountId:0:int:三方分发账号id",
            "thirdDramaSource:0:int:三方分发短剧来源 1创建 2同步",
    })
    @ApiParamsOut({
            "dramaName:短剧名称",
            "encryptionId:加密id",
            "contentType:1=短剧;2=漫画;3=小说",
            "sex:性别 0=无;1=男;2=女",
            "dramaCover:封面",
            "dramaCoverHor:横版封面",
            "retailCount:已授权分销商数量",
            "recDramaId:推荐剧id",
            "recDramaName:推荐剧名称",
            "seriesNumUpdate:已更新的集数",
            "releaseDate:上线日期",
            "productionCpName:制作方名称",
            "scriptCpName:编剧方名称",
            "playTencent:腾讯播放源id",
            "playTiktok:抖音播放源id",
            "playKuaishou:快手播放源id",
            "tiktokAuditStatus:审核状态0无记录99-未审核 98-审核中 1-不可播放 2-可播放",
            "kuaishouAuditStatus:快手审核状态0.待提审 1.已提审 2.审核通过 3.审核拒绝 4.审核中",
            "thirdPlatformId:三方分发平台id",
            "principalId:三方分发主体id",
            "accountId:三方分发账号id",
            "thirdDramaSource:三方分发短剧来源 1创建 2同步",
            "languages:语言种类",
    })
    @RequestMapping(value = "/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastDramaPO params, PageVO pageVO) {
//    	if(params.getDramaType() == null){
//    		params.setDramaType(1);
//    	}
        // 默认查询投流剧
        if (Objects.isNull(params.getLaunchFlag()) && Objects.isNull(params.getNativeFlag()) && Objects.isNull(params.getThirdFlag())) {
            params.setLaunchFlag(1);
        }
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getTagIds(), params.getCornerIds());
        params.setDelFlag(StaticVar.NO);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        // 先不区分业务类型
        // if (params.getContentType() == null) {
        //     params.setContentType(sessionVO.getContentType());
        // }

        params.setContentType(null);
        if (params.getContentType() == null) {
            if (sessionVO.getContentType() == 1) {
                params.setDramaPerformType(1);
            } else if (sessionVO.getContentType() == 4) {
                params.setDramaPerformType(2);
            }
        }
        try {
            Integer dramaId = null;
            if (StrUtil.isNotEmpty(params.getDramaNameLike())) {
                dramaId = Integer.valueOf(params.getDramaNameLike());
            }
            if (dramaId != null) {
                params.setDramaNameLike(null);
                params.setId(dramaId);
            }
        } catch (Exception ignored) {
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = dramaService.exportDramaList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        } else {
            return dramaService.queryPageList(params, pageVO, sessionVO);
        }
    }

    @ApiName(value = "查询短剧列表-不区分用途", folder = {StaticFolder.FOLDER_DRAMA})
    @RequestMapping(value = "/getDramaList")
    public ResultVO<?> getDramaList(HttpServletRequest request, FastDramaPO params, PageVO pageVO) {
        params.setDelFlag(0);
        return dramaService.getDramaList(params, pageVO);
    }

    /**
     * 查询短剧列表
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "查询短剧列表-下拉框", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "feeFlag:0:int:0都不选1付费2免费9都选",
            "notWxFinderIds:0:str:需要排除的微信视频号ID,多个逗号分隔",
            "dramaNameId:0:str:短剧名称或者ID模糊搜索",
    })
    @ApiParamsOut({
            "dramaName:短剧名称",
            "encryptionId:加密id",
            "contentType:1=短剧;2=漫画;3=小说",
            "sex:性别 0=无;1=男;2=女",
            "dramaCover:封面",
            "dramaCoverHor:横版封面",
    })
    @RequestMapping(value = "/getSimpleList")
    public ResultVO<?> getSimpleList(HttpServletRequest request, FastDramaPO params) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getTagIds(), params.getCornerIds());
        // 默认查询投流剧
        if (Objects.isNull(params.getLaunchFlag()) && Objects.isNull(params.getNativeFlag()) && Objects.isNull(params.getThirdFlag())) {
            params.setLaunchFlag(1);
        }
        params.setDelFlag(StaticVar.NO);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        if (params.getContentType() == null) {
            params.setContentType(sessionVO.getContentType());
        }
        params.setContentType(null);
        if (params.getContentType() == null) {
            if (sessionVO.getContentType() == 1) {
                params.setDramaPerformType(1);
            } else if (sessionVO.getContentType() == 4) {
                params.setDramaPerformType(2);
            }
        }
        if (notEmpty(params.getNotWxFinderIds())) {
            params.setNotWxFinderIds(StrUtil.joinStr(params.getNotWxFinderIds()));
        }
        params.setSortField("id");
        try {
            Integer dramaId = null;
            if (StrUtil.isNotEmpty(params.getDramaNameLike())) {
                dramaId = Integer.valueOf(params.getDramaNameLike());
            }
            if (dramaId != null) {
                params.setDramaNameLike(null);
                params.setId(dramaId);
            }
        } catch (Exception ignored) {
        }
        return ResultVO.success(dramaService.querySimpleList(params, sessionVO));
    }

    /**
     * 查询短剧列表
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "查询短剧列表-下拉框树状", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "dramaNameId:0:str:短剧名称或者ID模糊搜索",
    })
    @ApiParamsOut({
            "dramaName:短剧名称",
            "encryptionId:加密id",
            "contentType:1=短剧;2=漫画;3=小说",
            "dramaCode:短剧编码",
    })
    @RequestMapping(value = "/getSimpleTreeList")
    public ResultVO<?> getSimpleTreeList(HttpServletRequest request, FastDramaPO params) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getTagIds(), params.getCornerIds());
        params.setDelFlag(StaticVar.NO);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        if (params.getContentType() == null) {
            params.setContentType(sessionVO.getContentType());
        }
        if (notEmpty(params.getNotWxFinderIds())) {
            params.setNotWxFinderIds(StrUtil.joinStr(params.getNotWxFinderIds()));
        }
        params.setSortField("id");
        try {
            Integer dramaId = null;
            if (StrUtil.isNotEmpty(params.getDramaNameLike())) {
                dramaId = Integer.valueOf(params.getDramaNameLike());
            }
            if (dramaId != null) {
                params.setDramaNameLike(null);
                params.setId(dramaId);
            }
        } catch (Exception ignored) {
        }

        List<FastDramaPO> fastDramaPOS = dramaService.querySimpleList(params, sessionVO);
        fastDramaPOS.forEach(item -> item.setEncryptionId(encode(item.getId())));
        List<FastDramaPO> launch = fastDramaPOS.stream().filter(item -> {
            return item.getLaunchFlag() == 1;
        }).collect(Collectors.toList());
        List<FastDramaPO> nativeList = fastDramaPOS.stream().filter(item -> {
            return item.getNativeFlag() == 1;
        }).collect(Collectors.toList());
        List<FastDramaPO> thirdList = fastDramaPOS.stream().filter(item -> {
            return item.getThirdFlag() == 1;
        }).collect(Collectors.toList());
        Map<String, List<FastDramaPO>> result = new HashMap<>();
        result.put("launch", launch);
        result.put("native", nativeList);
        result.put("third", thirdList);
        return ResultVO.success(result);
    }

    /**
     * 查询单个短剧
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "短剧-查询单个短剧", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({"encryptionId:1:str:短剧加密id"})
    @ApiParamsOut({
            "dramaName:短剧名称",
            "encryptionId:加密id",
            "contentType:1=短剧;2=漫画;3=小说",
            "sex:1:int:性别 0=无;1=男;2=女",
            "dramaCover:封面",
            "dramaCoverHor:横版封面",
            "recDramaId:推荐剧id",
            "recDramaName:推荐剧名称",
            "releaseDate:上线日期",
            "nextVersion:下一个版本",
            "thirdShelfState:三方分发和端原生上下架状态",
            "tagNameList:标签列表",
            "tagNameList>>tagName:标签名称",
            "tagNameList>>fontColor:标签字体颜色",
            "tagNameList>>backColor:标签背景颜色",
            "tagNameList>>remark:标签备注",
    })
    @RequestMapping(value = "/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastDramaPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setContentType(sessionVO.getContentType());
        FastDramaPO fastDrama = dramaService.queryById(params, true);
        // 查询起始付费集
        FastDramaPO dramaParam = new FastDramaPO();
        dramaParam.setId(id);
        List<FastDramaPO> dramaList = fastDramaMapper.queryList(dramaParam);
        if (dramaList != null && dramaList.size() > 0) {
            Integer startNum = dramaList.get(0).getStartNum();
            if (startNum == null) {
                startNum = dramaList.get(0).getStartNumDef();
            }
            fastDrama.setStartNum(startNum);
        }
        return ResultVO.success(fastDrama);
    }

    /**
     * 查询短剧配置的国际化
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "查询短剧配置的国际化-下拉框", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "encryptionId:1:str:短剧ID",
    })
    @ApiParamsOut({
            "code:国际化编码",
            "name:国际化名称",
    })
    @RequestMapping(value = "/getLanguages")
    public ResultVO<?> getLanguages(HttpServletRequest request, FastDramaPO params) {
        List<Map<String, String>> langList = new ArrayList<>();

        if (StrUtil.isEmpty(params.getEncryptionId())) {
            Map<String, String> defaultLang = new HashMap<>();
            defaultLang.put("code", LanguageEnum.ENGLISH.getCode());
            defaultLang.put("name", LanguageEnum.ENGLISH.getName());
            langList.add(defaultLang);
            return ResultVO.success(langList);
        }

        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Map<String, FastDramaI18nPO> map = fastDramaI18nService.getMapByIdForLang(id);
        if (CollUtil.isNotEmpty(map)) {
            map.keySet().forEach(code -> {
                Map<String, String> lang = new HashMap<>();
                lang.put("code", code);
                lang.put("name", LanguageEnum.ofCode(code).getName());
                langList.add(lang);
            });
        }
        return ResultVO.success(langList);
    }

    /**
     * 新增短剧
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "短剧-新增短剧/克隆短剧", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "projectId:1:int:项目id",
            "tagIds:1:str:标签IDs",
            "openState:1:int:显示状态: 1=显示;0=隐藏",
            "updateState:1:int:更新状态: 1=连载;0=完结",
            "shelfState:1:int:上架状态: 1=上架;0=下架",
            "sex:1:int:0=无;1=男;2=女",
            "seriesNumAll:1:int:剧集总数",
            "dramaCover:1:str:短剧封面",
            "dramaCoverHor:1:str:短剧封面-横版",
            "dramaName:1:str:短剧名称",
            "startNum:1:int:付费起始集",
            "coinPer:1:int:单集K币",
            "skipSeries:1:int: 跳级解锁:1=支持;0=不支持",
            "dramaIntroduce:1:str:剧集描述",
            "sequence:1:int:排序",
            "cornerId:1:int:角标id",
            "sourceFrom:1:int:短剧来源",
            "contentType:1:int:1=短剧;2=漫画;3=小说",
            "releaseDateStr:1:str:上线日期",
            "cloneFromId:1:int:克隆剧的id",
            "bookType:0:int:0无1长篇2短篇",
            "bookSex:0:int:0无1男2女3未知",
            "feeFlag:0:int:0都不选1付费2免费9都选",
            "versionType:0:int:1初版 2剪辑版",
            "sourceDramaId:0:int:剪辑来源剧id",
            "launchFlag:0:int:是否投放剧",
            "nativeFlag:0:int:是否端原生剧",
            "thirdFlag:0:int:是否三方分发剧",
            "teamId:0:int:端原生团队id 字典表 native_team",
            "versionSource:0:int:版本来源 1新增 2克隆",
            "dramaPerformType:0:int:1短剧2漫剧",
            "registrationNumber:0:str:备案号",
            "i18ns:1:array:标签数组，格式 {'languageCode'：'SC','name'：'国际化测试短剧1','introduce'：'国际化测试短剧1','cover'：'xxx'}",
    })
    @RequestMapping(value = "/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody FastDramaPO params) {

        ResultVO<?> resultVO = checkParams(params);
        if (resultVO != null) {
            return resultVO;
        }
        // 默认已完结
        params.setUpdateState(0);
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setContentType(sessionVO.getContentType());
        MethodVO methodVO = dramaService.insert(params, sessionVO);
        String key = StaticVar.DRAMA_INFO_ID + params.getId();
        RedisUtil.del(key);
        RedisUtil.del("all_drama_redis");
        return ResultVO.fromMethodVO1(methodVO);
    }

    @ApiName(value = "短剧-更新短剧所属项目和版本类型", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "encryptionId:1:str:短剧加密id",
            "projectId:1:str:项目id",
            "versionType:1:str:1初版 2剪辑版",
            "sourceDramaId:1:str:剪辑来源",
            "contentLevel:0:int:1新剧2老剧"
    })
    @ApiParamsOut({
            "ok:ok",
    })
    @RequestMapping(value = "/updateDramaProject")
    public ResultVO<?> syncNativeDramaUpdate(HttpServletRequest request, FastDramaPO params) {

        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        if (Objects.isNull(params.getProjectId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isNotBlank(params.getEncryptionIds())) {
            List<Integer> ids = decodeIntList(params.getEncryptionIds());
            params.setIds(StrUtil.join(ids));
            MethodVO result = dramaService.batchUpdateDramaProject(params);
            return ResultVO.fromMethodVO(result);
        }

        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);

        MethodVO result = dramaService.updateDramaProject(params);
        return ResultVO.fromMethodVO(result);
    }

    /**
     * 更新短剧
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "短剧-编辑短剧", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "encryptionId:1:str:短剧加密id",
            "projectId:1:int:项目id",
            "tagIds:1:str:标签IDs",
            "openState:1:int:显示状态: 1=显示;0=隐藏",
            "updateState:1:int:更新状态: 1=连载;0=完结",
            "shelfState:1:int:上架状态: 1=上架;0=下架",
            "sex:1:int:0=无;1=男;2=女",
            "seriesNumAll:1:int:剧集总数",
            "dramaCover:1:str:短剧封面",
            "dramaCoverHor:1:str:短剧封面-横版",
            "dramaName:1:str:短剧名称",
            "startNum:1:int:付费起始集",
            "coinPer:1:int:单集K币",
            "skipSeries:1:int: 跳级解锁:1=支持;0=不支持",
            "dramaIntroduce:1:str:剧集描述",
            "sequence:1:int:排序",
            "cornerId:1:int:角标id",
            "sourceFrom:1:int:短剧来源",
            "contentType:1:int:1=短剧;2=漫画;3=小说",
            "releaseDateStr:1:str:上线日期",
            "bookType:0:int:0无1长篇2短篇",
            "bookSex:0:int:0无1男2女3未知",
            "feeFlag:0:int:0都不选1付费2免费9都选",
            "i18ns:1:array:标签数组，格式 {'languageCode'：'SC','name'：'国际化测试短剧1','introduce'：'国际化测试短剧1','cover'：'xxx'}",
    })
    @RequestMapping(value = "/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody FastDramaPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer dramaId = decodeInt(params.getEncryptionId());
        if (dramaId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(dramaId);
        ResultVO<?> resultVO = checkParams(params);
        if (resultVO != null) {
            return resultVO;
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        // 不可更新的参数
        params.setVersionNum(null);
        params.setVersionType(null);
        params.setSourceDramaId(null);
        params.setDramaCode(null);
        params.setLaunchFlag(Objects.nonNull(params.getLaunchFlag()) && params.getLaunchFlag() == 1 ? 1 : null);
        params.setNativeFlag(Objects.nonNull(params.getNativeFlag()) && params.getNativeFlag() == 1 ? 1 : null);
        params.setThirdFlag(Objects.nonNull(params.getThirdFlag()) && params.getThirdFlag() == 1 ? 1 : null);
        MethodVO methodVO = dramaService.update(params, sessionVO);

        // 清除缓存
        String key = StaticVar.DRAMA_INFO_ID + dramaId;
        RedisUtil.del(key);
        RedisUtil.del("all_drama_redis");

        // 非投流剧不删除缓存
        if (Objects.nonNull(params.getLaunchFlag()) && params.getLaunchFlag() == 1) {
            log.info("短剧 {} DB数据更新完毕，开始清理缓存数据", dramaId);
            cacheResetService.resetCacheFeeRule(0, dramaId);
            cacheResetService.resetCacheMiniSet(null, dramaId);
            cacheResetService.resetCacheRetailDramaList();
        }
        // 清除关联标签缓存
        String keyTag = StaticVar.DRAMA_TAG + dramaId;
        RedisUtil.del(keyTag);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 校验字段合法性
     *
     * @param params
     * @return
     */
    private ResultVO<?> checkParams(FastDramaPO params) {
        boolean launch = Objects.nonNull(params.getLaunchFlag()) && params.getLaunchFlag() == 1;
        if (StrUtil.isNotBlank(params.getDramaLineTimeListStr())) {
            params.setDramaLineTimeList(JsonUtil.toList(params.getDramaLineTimeListStr(), FastDramaLineTimePO.class));
        }
        // 端原生和三方分发默认值
        if (!launch) {
            params.setStartNum(ObjectUtils.defaultIfNull(params.getStartNum(), 1));
            params.setCoinPer(ObjectUtils.defaultIfNull(params.getCoinPer(), 100));
            params.setFeeFlag(2);
            // 时间处理
            if (CollUtil.isNotEmpty(params.getDramaLineTimeList())) {
                params.setReleaseTime(params.getDramaLineTimeList().get(0).getOnlineTime());
            }
        }
        // 端原生校验
        if (Objects.nonNull(params.getNativeFlag()) && params.getNativeFlag() == 1 && (Objects.isNull(params.getNativeAccountId()) || Objects.isNull(params.getNativePrincipalId()))) {
            return ResultVO.error("端原生挂载主体和挂载账号不能为空");
        }
        // if (isBlank(params.getDramaName())) {
        //     return ResultVO.error("名称不能为空");
        // }
        // if (params.getDramaName().length() > 50) {
        //     return ResultVO.error("名称不能超过50字符");
        // }
        // if (launch && isBlank(params.getDramaCover())) {
        //     return ResultVO.error("封面不能为空");
        // }
        if (CollUtil.isEmpty(params.getI18ns())) {
            return ResultVO.error("国际化内容不能为空");
        }
        if (launch) {
            // 端原生和三方分发短剧不校验标签
            if (isBlank(params.getTagIds())) {
                return ResultVO.error("标签不能为空");
            }
        }
        if (params.getCornerId() == null && launch) {
            return ResultVO.error("角标不能为空");
        }
        if (params.getSeriesNumAll() != null && params.getSeriesNumAll() > 65535) {
            return ResultVO.error("总集数过大,不能超65535");
        }
        if (params.getSeriesNumAll() != null && params.getSeriesNumAll() <= 0) {
            return ResultVO.error("总集数不能小于等于0");
        }
        if (params.getUpdateState() == null) {
            return ResultVO.error("更新状态不能为空");
        }
        if (!StrUtil.equalsAny(params.getUpdateState(), 0, 1)) {
            return ResultVO.error("更新状态不合法");
        }
        if (params.getStartNum() == null || params.getStartNum() <= 0) {
            return ResultVO.error("付费起始集不合法");
        }
        if (params.getStartNum() > 65535) {
            return ResultVO.error("付费起始集过大,不能超65535");
        }
        if (params.getCoinPer() == null || params.getCoinPer() < 0) {
            return ResultVO.error("单集收费K币不合法");
        }
        if (params.getCoinPer() > 999) {
            return ResultVO.error("单集K币过大,不能超999");
        }
        if (params.getSequence() == null) {
            params.setSequence(0);
        }
        if (params.getSequence() < 0) {
            return ResultVO.error("排序不能小于0");
        }
        if (params.getSequence() > 65535) {
            return ResultVO.error("排序过大,不能超65535");
        }
        if (params.getFeeFlag() == null || !Arrays.asList(0, 1, 2, 9).contains(params.getFeeFlag())) {
            return ResultVO.error("feeFlag付费标识错误");
        }
        if (params.getFeeFlag() == 1 || params.getFeeFlag() == 9) {
            if (isBlank(params.getReleaseDateStr())) {
                return ResultVO.error("上线时间不能为空");
            }
        }
        return null;
    }

    /**
     * 删除短剧
     *
     * @param request
     * @param params
     * @return
     */
    @RequestMapping(value = "/updateDel")
    public ResultVO<?> updateDel(HttpServletRequest request, FastDramaPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        List<String> ids = CollUtil.parseStr2List(params.getEncryptionId());
        int success = 0;
        for (String idStr : ids) {
            Integer dramaId = decodeInt(idStr);
            if (dramaId == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setId(dramaId);
            FastDramaVO dramaPO = dramaService.queryInfoByRedis(params);
            if (dramaPO == null) {
                success++;
            } else if (dramaPO.getShelfState() == 0) {
                params.setId(dramaId);
                SessionVO sessionVO = getSessionVO(request);
                params.setUpdatorId(sessionVO.getUserId());
                MethodVO methodVO = dramaService.updateDel(params);
                if (methodVO.getCode() == 0) {
                    String key = StaticVar.DRAMA_INFO_ID + dramaId;
                    RedisUtil.del(key);

                    cacheResetService.resetCacheFeeRule(null, dramaId);
                    cacheResetService.resetCacheMiniSet(null, dramaId);
                    success++;
                }
            }
        }
        if (success != ids.size()) {
            return ResultVO.error("请先下架已上架的短剧");
        }
        RedisUtil.del("all_drama_redis");
        return ResultVO.success();
    }

    /**
     * 删除短剧 端原生和三方分发
     *
     * @param request
     * @param params
     * @return
     */
    @RequestMapping(value = "/updateDelThird")
    public ResultVO<?> updateDelThird(HttpServletRequest request, FastDramaPO params) {
        if (isBlank(params.getEncryptionIds())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        List<String> ids = CollUtil.parseStr2List(params.getEncryptionIds());
        int success = 0;
        for (String idStr : ids) {
            Integer dramaId = decodeInt(idStr);
            if (dramaId == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setId(dramaId);
            SessionVO sessionVO = getSessionVO(request);
            params.setUpdatorId(sessionVO.getUserId());
            // 存在编码和删除来源需要进行判断是否在此列表下创建 否则为直接引用删除
            MethodVO methodVO = dramaService.updateDelPlus(params);
            if (methodVO.getCode() == 0) {
                String key = StaticVar.DRAMA_INFO_ID + dramaId;
                RedisUtil.del(key);
                // 不是投流剧不需删除缓存
                // cacheResetService.resetCacheFeeRule(null, dramaId);
                // cacheResetService.resetCacheMiniSet(null, dramaId);
                success++;
            }
        }
        if (success != ids.size()) {
            return ResultVO.error("请先下架已上架的短剧");
        }
        RedisUtil.del("all_drama_redis");
        return ResultVO.success();
    }

    /**
     * 上下架短剧
     *
     * @param request
     * @param params
     * @return
     */
    @RequestMapping(value = "/updateShelf")
    public ResultVO<?> updateShelf(HttpServletRequest request, FastDramaPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getShelfState() == null || !StrUtil.equalsAny(params.getShelfState(), 0, 1)) {
            return ResultVO.error(StaticStr.INVALID_PARAM + ":shelfState");
        }

        List<String> ids = CollUtil.parseStr2List(params.getEncryptionId());
        int success = 0;
        for (String idStr : ids) {
            Integer dramaId = decodeInt(idStr);
            if (dramaId == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setId(dramaId);
            SessionVO sessionVO = getSessionVO(request);
            params.setUpdatorId(sessionVO.getUserId());
            MethodVO methodVO = dramaService.updateShelf(params);
            if (methodVO.getCode() == 0) {
                String key = StaticVar.DRAMA_INFO_ID + dramaId;
                RedisUtil.del(key);

                cacheResetService.resetCacheFeeRule(null, dramaId);
                cacheResetService.resetCacheMiniSet(null, dramaId);
                success++;
            }
        }
        if (success == 0) {
            return ResultVO.error("操作失败");
        }
        if (success != ids.size()) {
            return ResultVO.error("部分失败");
        }
        RedisUtil.del("all_drama_redis");
        return ResultVO.success();
    }

    /**
     * 上下架短剧 端原生和三方分发
     *
     * @param request
     * @param params
     * @return
     */
    @RequestMapping(value = "/updateShelfThird")
    public ResultVO<?> updateShelfThird(HttpServletRequest request, FastDramaPO params) {
        if (params.getShelfState() == null || !StrUtil.equalsAny(params.getShelfState(), 0, 1)) {
            return ResultVO.error(StaticStr.INVALID_PARAM + ":shelfState");
        }
        params.setThirdShelfState(params.getShelfState());
        List<String> ids = CollUtil.parseStr2List(params.getEncryptionIds());
        int success = 0;
        for (String idStr : ids) {
            Integer dramaId = decodeInt(idStr);
            if (dramaId == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setId(dramaId);
            SessionVO sessionVO = getSessionVO(request);
            params.setUpdatorId(sessionVO.getUserId());
            MethodVO methodVO = dramaService.updateShelfThird(params);
            if (methodVO.getCode() == 0) {
                String key = StaticVar.DRAMA_INFO_ID + dramaId;
                RedisUtil.del(key);
                // cacheResetService.resetCacheFeeRule(null, dramaId);
                // cacheResetService.resetCacheMiniSet(null, dramaId);
                success++;
            }
        }
        if (success == 0) {
            return ResultVO.error("操作失败");
        }
        if (success != ids.size()) {
            return ResultVO.error("部分失败");
        }
        RedisUtil.del("all_drama_redis");
        return ResultVO.success();
    }

    /**
     * 批量更新-显示隐藏
     *
     * @param request
     * @param params
     * @return
     */
    @RequestMapping(value = "/updateOpenState")
    public ResultVO<?> updateOpenState(HttpServletRequest request, FastDramaPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getOpenState() == null || !StrUtil.equalsAny(params.getOpenState(), 0, 1)) {
            return ResultVO.error(StaticStr.INVALID_PARAM + ":openState");
        }

        List<String> ids = CollUtil.parseStr2List(params.getEncryptionId());
        int success = 0;
        for (String idStr : ids) {
            Integer dramaId = decodeInt(idStr);
            if (dramaId == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setId(dramaId);
            SessionVO sessionVO = getSessionVO(request);
            params.setUpdatorId(sessionVO.getUserId());
            MethodVO methodVO = dramaService.updateOpenState(params);
            if (methodVO.getCode() == 0) {
                String key = StaticVar.DRAMA_INFO_ID + dramaId;
                RedisUtil.del(key);

                cacheResetService.resetCacheFeeRule(null, dramaId);
                cacheResetService.resetCacheMiniSet(null, dramaId);
                success++;
            }
        }
        if (success == 0) {
            return ResultVO.error("操作失败");
        }
        if (success != ids.size()) {
            return ResultVO.error("部分失败");
        }
        RedisUtil.del("all_drama_redis");
        return ResultVO.success();
    }

    /**
     * 批量更新-付费起始集
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "付费规则-批量更新付费起始集", folder = {"费用相关"})
    @ApiParamsIn({"encryptionId:1:str:剧集加密id，多个逗号分开", "startNum:1:int:起始集"})
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateStartNum")
    public ResultVO<?> updateStartNum(HttpServletRequest request, FastFeeRulePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getStartNum() == null || params.getStartNum() <= 0) {
            return ResultVO.error("付费起始集不合法");
        }
        if (params.getStartNum() > 65535) {
            return ResultVO.error("付费起始集过大,不能超65535");
        }

        List<String> ids = CollUtil.parseStr2List(params.getEncryptionId());
        int success = 0;
        for (String idStr : ids) {
            Integer dramaId = decodeInt(idStr);
            if (dramaId == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setDramaId(dramaId);
            SessionVO sessionVO = getSessionVO(request);
            params.setCreatorId(sessionVO.getUserId());
            params.setUpdatorId(sessionVO.getUserId());
            params.setRetailId(sessionVO.getRetailId());
            params.setOfficialId(sessionVO.getOfficialId());
            MethodVO methodVO = feeRuleService.updateStartNum(params);
            if (methodVO.getCode() == 0) {
                String key = StaticVar.DRAMA_INFO_ID + dramaId;
                RedisUtil.del(key);

                cacheResetService.resetCacheFeeRule(null, dramaId);
                cacheResetService.resetCacheMiniSet(null, dramaId);
                success++;
            }
        }
        if (success == 0) {
            return ResultVO.error("操作失败");
        }
        if (success != ids.size()) {
            return ResultVO.error("部分失败");
        }
        RedisUtil.del("all_drama_redis");
        return ResultVO.success();
    }

    /**
     * 批量更新-单集币
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "付费规则-批量更新单集币", folder = {"费用相关"})
    @ApiParamsIn({"encryptionId:1:str:剧集加密id，多个逗号分开", "coinPer:1:int:单集K币数量"})
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateCoinPer")
    public ResultVO<?> updateCoinPer(HttpServletRequest request, FastFeeRulePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getCoinPer() == null || params.getCoinPer() <= 0) {
            return ResultVO.error("单集K币不合法");
        }
        if (params.getCoinPer() > 999) {
            return ResultVO.error("单集K币过大,不能超999");
        }

        List<String> ids = CollUtil.parseStr2List(params.getEncryptionId());
        int success = 0;
        for (String idStr : ids) {
            Integer dramaId = decodeInt(idStr);
            if (dramaId == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setDramaId(dramaId);
            SessionVO sessionVO = getSessionVO(request);
            params.setCreatorId(sessionVO.getUserId());
            params.setUpdatorId(sessionVO.getUserId());
            params.setRetailId(sessionVO.getRetailId());
            params.setOfficialId(sessionVO.getOfficialId());
            MethodVO methodVO = feeRuleService.updateCoinPer(params);
            if (methodVO.getCode() == 0) {
                String key = StaticVar.DRAMA_INFO_ID + dramaId;
                RedisUtil.del(key);

                cacheResetService.resetCacheFeeRule(null, dramaId);
                cacheResetService.resetCacheMiniSet(null, dramaId);
                success++;
            }
        }
        if (success == 0) {
            return ResultVO.error("操作失败");
        }
        if (success != ids.size()) {
            return ResultVO.error("部分失败");
        }
        RedisUtil.del("all_drama_redis");
        return ResultVO.success();
    }

    /**
     * 更新推荐剧
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "更新推荐剧", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({"encryptionId:1:str:原始剧集加密id", "recDramaId:1:int:推荐内容数字id"})
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateRecDrama")
    public ResultVO<?> updateRecDrama(HttpServletRequest request, FastDramaPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getRecDramaId() == null || params.getRecDramaId() <= 0) {
            return ResultVO.error("推荐内容id不合法");
        }
        Integer dramaId = decodeInt(params.getEncryptionId());
        if (dramaId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(dramaId);

        if (dramaId.equals(params.getRecDramaId())) {
            return ResultVO.error("推荐内容不能是自己");
        }

        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = dramaService.update(params);

        // 清除缓存
        String key = StaticVar.DRAMA_INFO_ID + dramaId;
        RedisUtil.del(key);
        RedisUtil.del("all_drama_redis");

        cacheResetService.resetCacheFeeRule(null, dramaId);
        cacheResetService.resetCacheMiniSet(null, dramaId);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 更新cdn开关
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "剧的cdn开关", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "encryptionId:1:str:原始剧集加密id",
            "cdnWechat:1:int:腾讯cdn开关0关闭1开启",
            "cdnTiktok:1:int:抖音cdn开关0关闭1开启"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateCdn")
    public ResultVO<?> updateCdn(HttpServletRequest request, FastDramaPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer dramaId = decodeInt(params.getEncryptionId());
        if (dramaId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(dramaId);
        if (params.getCdnWechat() == null && params.getCdnTiktok() == null) {
            return ResultVO.error("开关不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = dramaService.update(params);

        // 清除缓存
        String key = StaticVar.DRAMA_INFO_ID + dramaId;
        RedisUtil.del(key);
        RedisUtil.del("all_drama_redis");
        RedisUtil.del(StaticVar.AUDIT_TT_PLAN + dramaId);
        cacheResetService.resetCacheFeeRule(null, dramaId);
        cacheResetService.resetCacheMiniSet(null, dramaId);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 更新基建开关
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "剧的基建开关", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "encryptionId:1:str:原始剧集加密id",
            "foundationState与freeFoundationState二选一:0:int:基建开关",
            "foundationState:1:int:基建开关0关闭1开启",
            "freeFoundationState:1:int:转免基建开关0关闭1开启",
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateFoundationState")
    public ResultVO<?> updateFoundationState(HttpServletRequest request, FastDramaPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer dramaId = decodeInt(params.getEncryptionId());
        if (dramaId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(dramaId);
        boolean updateFoundationFlag = params.getFoundationState() != null; // 是否更新基建开关
        // 基建开关-付费基建
        if (params.getFreeFoundationState() != null) {
            updateFoundationFlag = true; // 基建开关-免费巨量
        }
        if (params.getFreeOtherFoundationState() != null) {
            updateFoundationFlag = true; // 基建开关-免费其他
        }
        if (params.getNatureFoundationState() != null) {
            updateFoundationFlag = true; // 基建开关-自然流量
        }
        if (!updateFoundationFlag) {
            return ResultVO.error("开关不能为空");
        }

        if (params.getFoundationState() != null && !Arrays.asList(0, 1).contains(params.getFoundationState())) {
            return ResultVO.error("基建开关不合法");
        }
        if (params.getFreeFoundationState() != null && !Arrays.asList(0, 1).contains(params.getFreeFoundationState())) {
            return ResultVO.error("转免基建开关不合法");
        }
        if (params.getFreeOtherFoundationState() != null && !Arrays.asList(0, 1).contains(params.getFreeOtherFoundationState())) {
            return ResultVO.error("转免基建开关不合法");
        }
        if (params.getNatureFoundationState() != null && !Arrays.asList(0, 1).contains(params.getNatureFoundationState())) {
            return ResultVO.error("自然流量基建开关不合法");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = dramaService.update(params);

        // 清除缓存
        String key = StaticVar.DRAMA_INFO_ID + dramaId;
        RedisUtil.del(key);
        RedisUtil.del("all_drama_redis");
        RedisUtil.del(StaticVar.AUDIT_TT_PLAN + dramaId);
        cacheResetService.resetCacheFeeRule(null, dramaId);
        cacheResetService.resetCacheMiniSet(null, dramaId);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 更新上线时间
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "更新上线时间", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({"id:1:str:剧集id-数字", "releaseDateStr:1:str:上线日期yyyy-MM-dd"})
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateReleaseDate")
    public ResultVO<?> updateReleaseDate(HttpServletRequest request, FastDramaPO params) {
        if (params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer dramaId = params.getId();

        if (isBlank(params.getReleaseDateStr())) {
            return ResultVO.error("上线日期不能为空");
        }
        try {
            DateUtil.format09(params.getReleaseDateStr());
        } catch (Exception e) {
            return ResultVO.error("上线日期不合法");
        }

        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = dramaService.update(params);

        // 清除缓存
        String key = StaticVar.DRAMA_INFO_ID + dramaId;
        RedisUtil.del(key);
        RedisUtil.del("all_drama_redis");

        cacheResetService.resetCacheFeeRule(null, dramaId);
        cacheResetService.resetCacheMiniSet(null, dramaId);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 更新上线时间
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "批量设置剧的投放类型", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "encryptionIds:1:str:加密的剧集id们，逗号分割",
            "feeFlag:1:int:0不投放1投放付费2投放免费9全部投放",
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateFeeFlag")
    public ResultVO<?> updateFeeFlag(HttpServletRequest request, FastDramaPO params) {
        if (StrUtil.isEmpty(params.getEncryptionIds())) {
            return ResultVO.error("剧加密id不能为空");
        }
        if (params.getFeeFlag() == null || !Arrays.asList(1, 2, 9).contains(params.getFeeFlag())) {
            return ResultVO.error("投放类型错误");
        }
        List<String> encryptionList = CollUtil.parseStr2List(params.getEncryptionIds());
        List<Integer> idList = new ArrayList<>();
        for (String encryptionId : encryptionList) {
            Integer id = decodeInt(encryptionId);
            if (id != null) {
                idList.add(id);
            }
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = dramaService.updateFeeFlag(params, idList);
        for (Integer id : idList) {
            // 清除缓存
            String key = StaticVar.DRAMA_INFO_ID + id;
            RedisUtil.del(key);
            RedisUtil.del("all_drama_redis");
            cacheResetService.resetCacheFeeRule(null, id);
            cacheResetService.resetCacheMiniSet(null, id);
        }
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 更新上线时间
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "修改剧快应用备案号", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "encryptionId:1:str:加密的剧集id",
            "auditNumber:1:str:快应用需要的备案号"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateAuditNumber")
    public ResultVO<?> updateAuditNumber(HttpServletRequest request, FastDramaPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error("剧加密id不能为空");
        }
        if (StrUtil.isEmpty(params.getAuditNumber())) {
            return ResultVO.error("备案号不能为空");
        }
        Integer id = decodeInt(params.getEncryptionId());
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = dramaService.updateAuditNumber(params);
        // 清除缓存
        String key = StaticVar.DRAMA_INFO_ID + id;
        RedisUtil.del(key);
        RedisUtil.del("all_drama_redis");
        cacheResetService.resetCacheFeeRule(null, id);
        cacheResetService.resetCacheMiniSet(null, id);
        return ResultVO.fromMethodVO(methodVO);
    }


    /**
     * 更改支付宝短剧备案号
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "修改剧支付宝小程序备案号", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "encryptionId:1:str:加密的剧集id",
            "aliAuditNumber:1:str:支付宝需要的备案号"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateAliAuditNumber")
    public ResultVO<?> updateAliAuditNumber(HttpServletRequest request, FastDramaPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error("剧加密id不能为空");
        }
        if (StrUtil.isEmpty(params.getAliAuditNumber())) {
            return ResultVO.error("备案号不能为空");
        }
        Integer id = decodeInt(params.getEncryptionId());
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = dramaService.updateAliAuditNumber(params);
        // 清除缓存
        String key = StaticVar.DRAMA_INFO_ID + id;
        RedisUtil.del(key);
        RedisUtil.del("all_drama_redis");
        cacheResetService.resetCacheFeeRule(null, id);
        cacheResetService.resetCacheMiniSet(null, id);
        return ResultVO.fromMethodVO(methodVO);
    }


    /**
     * 更改默认语言
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "更改默认语言", folder = {StaticFolder.FOLDER_DRAMA})
    @ApiParamsIn({
            "encryptionId:1:str:加密的剧集id",
            "defaultLang:1:str:默认语言编码"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateDefaultLang")
    public ResultVO<?> updateDefaultLang(HttpServletRequest request, FastDramaPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error("剧加密id不能为空");
        }
        if (StrUtil.isEmpty(params.getDefaultLang())) {
            return ResultVO.error("默认语言编码不能为空");
        }
        Integer id = decodeInt(params.getEncryptionId());
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = dramaService.updateDefaultLang(params);
        // 清除缓存
        String key = StaticVar.DRAMA_INFO_ID + id;
        RedisUtil.del(key);
        RedisUtil.del("all_drama_redis");
        cacheResetService.resetCacheFeeRule(null, id);
        cacheResetService.resetCacheMiniSet(null, id);
        return ResultVO.fromMethodVO(methodVO);
    }

}
