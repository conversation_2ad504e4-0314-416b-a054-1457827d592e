package com.fast.controller.xman;

import com.fast.base.BaseController;
import com.fast.mapper.retail.FastRetailMapper;
import com.fast.po.retail.FastRetailPO;
import com.fast.service.drama.XmanService;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("/xman")
public class XmanController extends BaseController {
    @Autowired
    private XmanService xmanService;
    @Autowired
    private FastRetailMapper fastRetailMapper;

    /**
     * 启动推送票据，运行一次即可
     *
     * @param request
     * @return
     */
    @GetMapping("/nologin/pushInit")
    public ResultVO pushInit(HttpServletRequest request, Integer retailId, Integer dramaId) {
        if (retailId == null) {
            return ResultVO.error("分销商id不能为空");
        }
        if (dramaId != null && dramaId > 0) {
            FastRetailPO retailPO = fastRetailMapper.queryById(retailId);
            if (xmanService.sendToXiangYueHui(retailPO, dramaId) == 1) {
                return ResultVO.success("同步成功");
            }
            return ResultVO.error("同步失败");
        } else {
            xmanService.sendAllDramaToRetail(retailId);
            return ResultVO.success("同步成功");
        }
    }

}
