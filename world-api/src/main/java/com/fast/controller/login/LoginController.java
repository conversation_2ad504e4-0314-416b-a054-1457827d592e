package com.fast.controller.login;


import com.alibaba.fastjson.JSONObject;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.LogVisit;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticVar;
import com.fast.service.system.LoginService;
import com.fast.utils.IPUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.uuid.IdUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/login")
public class LoginController extends BaseController {

    @Autowired
    private LoginService loginService;

    @ApiName(value = "账号密码登录(前置登录)", folder = {StaticFolder.FOLDER_LOGIN})
    @ApiParamsIn(value = {"loginName:1:str:登录账户", "password:1:str:密码", "from:1:int:1平台2分销商3内容生产平台"})
    @ApiParamsOut(value = {""})
    @RequestMapping("/loginPre")
    @LogVisit(name = "管理后台登录-step1")
    public ResultVO<?> loginPre(HttpServletRequest request, String loginName, String password, Integer from) {

        String ip = IPUtil.getIpAddr(request);
        String key = StaticVar.LOGIN_VERIFY + ip + "_" + loginName;
        String res = RedisUtil.get(key);
        Integer count = 0;
        if (StrUtil.isNotEmpty(res)) {
            count = Integer.valueOf(res);
        }
        count++;
        if (count > 50) {
            return ResultVO.error("休息会，好好想想密码，然后再尝试");
        }
        RedisUtil.set(key, count.toString(), 60 * 5);// 5分钟

        if (isBlank(loginName)) {
            return ResultVO.error("账号不能为空");
        }
        if (isBlank(password)) {
            return ResultVO.error("密码不能为空");
        }
        if (from == null) {
            return ResultVO.error("平台来源不能为空");
        }
        return loginService.loginStepOne(loginName, password, from);
    }

    @ApiName(value = "账号密码登录(前置登录)", folder = {StaticFolder.FOLDER_LOGIN})
    @ApiParamsIn(value = {"contentType:1:int:内容类型1短剧2漫画3小说99总部"})
    @ApiParamsOut(value = {""})
    @RequestMapping("/loginStepTwo")
    @LogVisit(name = "管理后台登录-step2")
    public ResultVO<?> loginIn(HttpServletRequest request, Integer contentType) {
        SessionVO sessionVO = getSessionVO(request);
        if (contentType == null) {
            return ResultVO.error("进入内容类型不能为空");
        }
        if (sessionVO == null) {
            return ResultVO.error(StaticCode.LOGIN_EXPIRE, "未登录，请先登录");
        }
        return loginService.loginBySessionVO(sessionVO, contentType);
    }

    @ApiName(value = "账号密码登录", folder = {StaticFolder.FOLDER_LOGIN})
    @ApiParamsIn(value = {"loginName:1:str:登录账户", "password:1:str:密码", "from:1:int:1平台2分销商"})
    @ApiParamsOut(value = {"menuList:菜单树", "menuIds:全部权限菜单id"})
    @RequestMapping("/login")
    public ResultVO<?> loginByPass(String loginName, String password, Integer from) {
        if (isBlank(loginName)) {
            return ResultVO.error("账号不能为空");
        }
        if (isBlank(password)) {
            return ResultVO.error("密码不能为空");
        }
        if (from == null) {
            return ResultVO.error("平台来源不能为空");
        }
        return loginService.loginByPass(loginName, password, from);
    }


    /**
     * 测试
     *
     * @return
     */
    @RequestMapping("/nologin/test")
    public ResultVO test(String type) {
//    	cpRuleService.synCpConsumeDay(47,DateUtil.beginOfDay(DateUtil.getNowDate()) ,1);
        if ("cdn".equals(type)) {
            // aliCdnService.synCdnBpsData();
            return ResultVO.success();
        }
//    	cpReportConsumeService.genSettlementLog(1);

        return ResultVO.success("ok");
    }


    /**
     * 获取滑块验证码配置
     *
     * @return
     * @throws Exception
     */
    @RequestMapping("/nologin/captchaConfig")
    public ResultVO verifyCaptcha() throws Exception {
        // 获取滑块验证码配置
        String s = IdUtil.simpleUUID();
        RedisUtil.set(StaticVar.CAPTCHA_VERIFY_TOKEN_KEY + s, "1", StaticVar.CAPTCHA_VERIFY_TOKEN_EXPIRE_TIME);
        // StaticVar.ADMIN_PASSWORD_UPDATE_DAT
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("prefix", StaticVar.ALI_CAPTCHA_PREFIX);
        jsonObject.put("SceneId", StaticVar.ALI_CAPTCHA_SCENE_ID);
        jsonObject.put("randomVerifyToken", s);
        return ResultVO.success(jsonObject);
    }

    /**
     * 根据用户名获取脱敏手机号
     *
     * @param loginName
     * @return
     */
    @ApiName(value = "系统用户-根据用户名获取脱敏手机号", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {
            "loginName:1:str:用户名",
    })
    @ApiParamsOut(value = {"data:手机号"})
    @RequestMapping("/nologin/getPhoneByLoginName")
    public ResultVO getPhoneByLoginName(String loginName) {
        String phone = loginService.getPhoneByLoginName(loginName);
        String maskedPhoneNumber = phone.replaceAll("(.{3})(.{4})(.{4})", "$1****$3");
        return ResultVO.success("成功", maskedPhoneNumber);
    }


    /**
     * 发送短信验证码
     *
     * @return
     */
    @ApiName(value = "系统用户-找回密码发送短信验证码", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {
            "captchaToken:1:str:滑块验证成功token",
            "loginName:1:str:用户名",
    })
    @ApiParamsOut(value = {"data:token"})
    @RequestMapping("/nologin/forgetPassword/sendSms")
    public ResultVO sendSmsForgetPassword(HttpServletRequest request, String captchaToken, String loginName) throws Exception {
        String ip = IPUtil.getIpAddr(request);
        String ua = request.getHeader("User-Agent");
        String token = loginService.sendSmsForgetPassword(captchaToken, loginName, ip, ua);
        return ResultVO.success("ok", token);
    }

    @ApiName(value = "系统用户-短信验证码修改密码", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {
            "newPassword:1:str:密码",
            "loginName:1:str:用户名",
            "smsCode:1:str:短信验证码",
            "smsToken:1:str:短信token",

    })
    @ApiParamsOut(value = {"state:ok成功"})
    @RequestMapping(value = "/nologin/updatePassword", method = {RequestMethod.POST})
    public ResultVO<?> updatePasswordNologin(HttpServletRequest request, String newPassword, String loginName, String smsCode, String smsToken) {
        if (StrUtil.isBlank(newPassword) || StrUtil.isBlank(loginName) || StrUtil.isBlank(smsCode) || StrUtil.isBlank(smsToken)) {
            return ResultVO.error("参数错误!");
        }
        MethodVO methodVO = loginService.updatePasswordBySms(newPassword, loginName, smsCode, smsToken);
        return ResultVO.fromMethodVO(methodVO);
    }

}
