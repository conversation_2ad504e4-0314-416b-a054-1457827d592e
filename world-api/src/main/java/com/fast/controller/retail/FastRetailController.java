/*
 * Powered By fast.up
 */
package com.fast.controller.retail;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.*;
import com.fast.mapper.user.FastUserMapper;
import com.fast.po.retail.FastRetailMiniPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.retail.FastRetailMiniService;
import com.fast.service.retail.FastRetailService;
import com.fast.service.user.FastUserService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.retail.RetailMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 分销商账号管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastRetail")
public class FastRetailController extends BaseController {

    @Autowired
    private FastRetailService fastRetailService;
    @Autowired
    private FastUserService fastUserService;
    @Autowired
    private FastRetailMiniService fastRetailMiniService;
    @Autowired
    private FastUserMapper fastUserMapper;

    @ApiName(value = "账号管理-查询列表", folder = {"分销商管理"})
    @ApiParamsIn({
            "retailFlag:0:int: 标志1：分销商，2：cp 3:查询所有",
            "contentType:0:int:1短剧分销商2漫画分销商3小说分销商",
            "searchKey:0:str: 关键字查询 ",
            "cpTypes:0:str: cp类型 1：制片团队，2：编剧团队，3：制作-外包公司 4: 制作-导演 5:制作-演员 6：制作-后期"
    })
    @ApiParamsOut({
            "results 》 list 》id : id",
            "results 》 list 》retailName : cp名称",
            "results 》 list 》cpType : 1：制片团队，2：编剧团队，3：制作-外包公司 4: 制作-导演 5:制作-演员 6：制作-后期',",
            "results 》 list 》contentType : 团队归属1：公司内部，2：外部合作",
            "results 》 list 》username : 管理员姓名",
            "results 》 list 》phone : 管理员手机",
            "results 》 list 》loginName : 登陆账号",
            "results 》 list 》contentTypes : 内容类型1短剧2漫画3小说，逗号分割",
            "results 》 list 》state : 状态:1=启用;0=禁用",
            "results 》 list 》level : cp账户等级",
            "results 》 list 》levelDesc : cp账户等级描述",
            "results 》 list 》auditStatus : cp账户审核状态",
            "results 》 list 》settlementType : 收款方式:1=银行卡对公",
            "results 》 list 》settlementUserName : 收款人姓名",
            "results 》 list 》settlementAccount : 收款账户",
            "results 》 list 》settlementBankName : 收款账户开户银行",
            "results 》 list 》invoiceName : 发票名称",
            "results 》 list 》invoiceDutyNum : 发票税号",
            "results 》 list 》invoiceAddress :发票地址",
            "results 》 list 》invoicePhone : 发票手机号",
            "results 》 list 》invoiceAccount : 发票收款账户",
            "results 》 list 》invoiceBankName : 发票收款账户开户银行",
            "results 》 list 》cpOrgList : 所属组织",
            "results 》 list 》businessType:1=投放;2=挂载",
            "results 》retail 》retailName:分销商名称",
            "results 》retail 》divideRate:分成比例",
            "results 》miniList 》miniName:授权小程序名称",
            "results 》miniList 》adDataFlag:是否可看广告数据:0=否;1=是",
    })
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastRetailPO params, PageVO pageVO) {
        params.setDelFlag(StaticVar.NO);
        if (notBlank(params.getMiniIds())) {
            List<Integer> miniIds = decodeIntList(params.getMiniIds());
            params.setMiniIds(StrUtil.join(miniIds));
        }
        if (params.getRetailFlag() == null) {
            params.setRetailFlag(1);// 默认分销商
        }
        if (params.getRetailFlag() == 3) {
            params.setRetailFlag(null);// 默认分销商
        }
        SessionVO sessionVO = getSessionVO(request);
        if (params.getContentType() == null && sessionVO.getContentType() != 99) {
            params.setContentType(sessionVO.getContentType());
        }
        return fastRetailService.queryPageList(params, pageVO);
    }

    @ApiName(value = "账号管理-修改是否可看广告数据", folder = {"分销商管理"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "miniId:1:int:应用id",
            "adDataFlag:1:int:是否可看广告数据:0=否;1=是",
    })
    @ApiParamsOut({"states:状态"})
    @RequestMapping(value = "/updateAdDataFlag", method = {RequestMethod.POST})
    public ResultVO<?> updateAdDataFlag(FastRetailMiniPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        if (params.getAdDataFlag() == null || !StrUtil.equalsAny(params.getAdDataFlag(), 0, 1)) {
            return ResultVO.error(StaticStr.INVALID_PARAM + ":adDataFlag");
        }
        if (params.getMiniId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM + ":miniId");
        }
        MethodVO methodVO = fastRetailMiniService.updateAdDataFlag(params);
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiName(value = "账号管理-查询单个详情", folder = {StaticFolder.FOLDER_RETAIL_SYSTEM})
    @ApiParamsIn({"encryptionId:0:str:分销商加密id，为空取当前登录的分销商"})
    @ApiParamsOut({
            "results 》retail 》retailName:分销商名称",
            "results 》retail 》retailType:类别 1=自投、2=代投、3=独立分销商",
            "results 》retail 》divideRate:分成比例",
            "results 》 retail 》level : cp账户等级",
            "results 》 retail 》levelDesc : cp账户等级描述",
            "results 》 retail 》auditStatus : cp账户审核状态",
            "results 》userPO 》loginName:登录账号",
            "results 》userPO 》encryptionId:登录用户加密id，用作修改密码",
            "results 》retailMiniList 》miniName:授权小程序名称",
            "results 》invoiceList 》name:发票名称",
            "results 》invoiceList 》dutyNum:税号",
            "results 》invoiceList 》address:地址",
            "results 》invoiceList 》phone:手机",
            "results 》invoiceList 》account:收款账户",
            "results 》invoiceList 》bankName:收款账户开户银行",
            "results 》settlementList 》type:结算，收款方式:1=银行卡对公",
            "results 》settlementList 》userName:结算，收款人姓名",
            "results 》settlementList 》account:结算，收款账户",
            "results 》settlementList 》bankName:结算，收款账户开户银行"
    })
    @RequestMapping(value = "/getRetailDetail", method = {RequestMethod.POST})
    public ResultVO<?> getRetailDetail(HttpServletRequest request, FastRetailPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            params.setId(sessionVO.getRetailId());
        } else {
            params.setId(decodeInt(params.getEncryptionId()));
        }
        return fastRetailService.queryDetail(params);
    }

    @ApiName(value = "账号管理-查询单个详情", folder = {"分销商管理"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastRetailPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastRetailPO fastRetail = fastRetailService.queryById(params);
        return ResultVO.success(fastRetail);
    }

    @ApiName(value = "账号管理-添加", folder = {"分销商管理", StaticFolder.FOLDER_CP})
    @ApiParamsIn({
            "state:1:int:1=启用;0=禁用",
            "retailName:1:str: 分销商名称",
            "retailType:1:int:1",
            "divideRate:1:str:分成比例",
            "userName:1:str: 管理员名称",
            "loginName:1:str:账号 ***********",
            "phone:1:str:联系电话 ***********",
            "miniIds:1:str:小程序id I9jK_T4Blz49roGBLCKYdA,jtUGc63WjKZ4fKCKN-QAIQ,,ijpgn8puoukr8U4w-qGt1g,VVed7wmYzUp_8hKH_Wvz1Q",
            "settlementType:1:int:收款方式",
            "settlementUserName:1:str: 收款人",
            "settlementAccount:1:str: 收款账号",
            "settlementBankName:1:str: 开户行",
            "invoiceName:1:str: 发票名称",
            "invoiceDutyNum:1:str: 税号",
            "invoiceAddress:1:str: 单位地址",
            "invoicePhone:1:str: 电话号码",
            "invoiceAccount:1:str: 收款账号",
            "invoiceBankName:1:str: 收款开户行",
            "contentTypes:1:str:'1,2'",
            "retailFlag:0:int:标志1：分销商，2：cp",
            "cpType:0:int:1：制片团队，2：编剧团队，3：制作-外包公司 4: 制作-导演 5:制作-演员 6：制作-后期 7: 策划团队",
            "belongType:0:int:cp团队归属1：公司内部，2：外部合作",
            "cpOrgIds:0:str:归属组织ids,逗号隔开,",
            "level:0:int:cp账号等级",
            "levelDesc:0:str:cp账号等级描述",
            "menuIds:0:str:菜单ids",
            "businessType:1:int:业务类型:1=投放;2=挂载",
            "mountType:1:int:挂载平台:1=微信视频号"
    })
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastRetailPO params) {
        ResultVO<?> resultVO = checkParams(params);
        if (resultVO != null) {
            return resultVO;
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        if (params.getRetailFlag() == null) {
            params.setRetailFlag(1);// 默认分销商
        }
        FastUserPO fastUserPO = new FastUserPO();
        fastUserPO.setLoginName(params.getLoginName());
        Integer count = fastUserMapper.queryCountByLoginname(fastUserPO);
        if (count > 0) {
            return ResultVO.error("登录账号不能重复！");
        }
        if (Objects.nonNull(params.getBusinessType()) && !List.of(1, 2).contains(params.getBusinessType())) {
            return ResultVO.error("业务类型不合法");
        }
        if (StrUtil.isNotBlank(params.getMountType())) {
            if (params.getBusinessType() != 2) {
                params.setMountType(null);
            }
        }
        MethodVO methodVO = fastRetailService.insert(sessionVO, params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "账号管理-更新", folder = {"分销商管理"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "state:1:int:1=启用;0=禁用",
            "retailName:1:str: 分销商名称",
            "retailType:1:int:1",
            "divideRate:1:str:分成比例",
            "userName:1:str: 管理员名称",
            "loginName:1:str:账号 ***********",
            "phone:1:str:联系电话 ***********",
            "miniIds:1:str:小程序id I9jK_T4Blz49roGBLCKYdA,jtUGc63WjKZ4fKCKN-QAIQ,,ijpgn8puoukr8U4w-qGt1g,VVed7wmYzUp_8hKH_Wvz1Q",
            "settlementType:1:int:收款方式",
            "settlementUserName:1:str: 收款人",
            "settlementAccount:1:str: 收款账号",
            "settlementBankName:1:str: 开户行",
            "invoiceName:1:str: 发票名称",
            "invoiceDutyNum:1:str: 税号",
            "invoiceAddress:1:str: 单位地址",
            "invoicePhone:1:str: 电话号码",
            "invoiceAccount:1:str: 收款账号",
            "invoiceBankName:1:str: 收款开户行",
            "contentTypes:1:str:'1,2'",
            "retailFlag:0:int:标志1：分销商，2：cp",
            "cpType:0:int:1：制片团队，2：编剧团队，3：制作-外包公司 4: 制作-导演 5:制作-演员 6：制作-后期 7: 策划团队",
            "belongType:0:int:cp团队归属1：公司内部，2：外部合作",
            "cpOrgIds:0:str:归属组织ids,逗号隔开,",
            "level:0:int:cp账号等级",
            "levelDesc:0:str:cp账号等级描述",
            "menuIds:0:str:菜单ids",
            "businessType:1:int:业务类型:1=投放;2=挂载",
            "mountType:1:int:挂载平台:1=微信视频号"
    })
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastRetailPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        ResultVO<?> resultVO = checkParams(params);
        if (resultVO != null) {
            return resultVO;
        }
        if (Objects.nonNull(params.getBusinessType()) && !List.of(1, 2).contains(params.getBusinessType())) {
            return ResultVO.error("业务类型不合法");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastRetailService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 校验字段合法性
     *
     * @param params
     * @return
     */
    private ResultVO<?> checkParams(FastRetailPO params) {
        if (params.getRetailFlag() == null) {
            params.setRetailFlag(1);// 默认分销商
        }
        if (params.getRetailFlag() == 1) {
            // 分销商必填
            if (params.getDivideRate() == null) {
                return ResultVO.error("抽成比例不合法");
            }
            if (params.getDivideRate().compareTo(BigDecimal.ZERO) < 0) {
                return ResultVO.error("抽成比例不能小于0");
            }
            if (params.getDivideRate().compareTo(BigDecimalVar.BD_100) > 0) {
                return ResultVO.error("抽成比例不能大于100");
            }
            if (isBlank(params.getMiniIds())) {
                return ResultVO.error("授权小程序不能为空");
            }
        }
        if (isBlank(params.getRetailName())) {
            return ResultVO.error("名称不能为空");
        }
        if (params.getRetailName().length() > 20) {
            return ResultVO.error("名称不能超过20字符");
        }
        if (fastRetailService.queryNameIsRepeat(params) > 0) {
            return ResultVO.error("名称不能重复");
        }
        if (isBlank(params.getUserName())) {
            return ResultVO.error("管理员姓名不能为空");
        }
        if (params.getUserName().length() > 20) {
            return ResultVO.error("管理员姓名不能超过20字符");
        }
        if (isBlank(params.getLoginName())) {
            return ResultVO.error("登录账号不能为空");
        }
        if (params.getLoginName().length() > 20) {
            return ResultVO.error("登录账号不能超过20字符");
        }
        if (isBlank(params.getPhone())) {
            return ResultVO.error("手机号不能为空");
        }
        if (!StrUtil.isMobilePhone(params.getPhone())) {
            return ResultVO.error("手机号不合法");
        }

        List<Integer> miniIds = decodeIntList(params.getMiniIds());
        params.setMiniIds(StrUtil.join(miniIds));
        if (isBlank(params.getSettlementUserName())) {
            return ResultVO.error("收款人不能为空");
        }
        if (params.getSettlementUserName().length() > 20) {
            return ResultVO.error("收款人不能超过20字符");
        }
        if (isBlank(params.getSettlementAccount())) {
            return ResultVO.error("收款账户不能为空");
        }
        if (params.getSettlementAccount().length() > 50) {
            return ResultVO.error("收款账户不能超过50字符");
        }
        if (isBlank(params.getSettlementBankName())) {
            return ResultVO.error("收款银行不能为空");
        }
        if (params.getSettlementBankName().length() > 50) {
            return ResultVO.error("收款银行不能超过50字符");
        }
        if (notBlank(params.getInvoiceName()) && params.getInvoiceName().length() > 50) {
            return ResultVO.error("发票名称不能超过50字符");
        }
        if (notBlank(params.getInvoiceDutyNum()) && params.getInvoiceDutyNum().length() > 50) {
            return ResultVO.error("发票税号不能超过50字符");
        }
        if (notBlank(params.getInvoiceAddress()) && params.getInvoiceAddress().length() > 200) {
            return ResultVO.error("发票地址不能超过200字符");
        }
        if (notBlank(params.getInvoiceAccount()) && params.getInvoiceAccount().length() > 50) {
            return ResultVO.error("发票银行账户不能超过50字符");
        }
        if (notBlank(params.getInvoiceBankName()) && params.getInvoiceBankName().length() > 50) {
            return ResultVO.error("发票银行不能超过50字符");
        }
        if (StrUtil.isEmpty(params.getContentTypes())) {
            return ResultVO.error("投放类型不能为空");
        }
        return null;
    }

    @ApiName(value = "账号管理-更新", folder = {"分销商管理"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ids,用,隔开",
            "state:1:int:1=启用;0=禁用"})
    @RequestMapping(value = "/updateState", method = {RequestMethod.POST})
    public ResultVO<?> updateState(HttpServletRequest request, FastRetailPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getState() == null || !StrUtil.equalsAny(params.getState(), 0, 1)) {
            return ResultVO.error(StaticStr.INVALID_PARAM + ":state");
        }

        List<String> ids = CollUtil.parseStr2List(params.getEncryptionId());
        int success = 0;
        for (String idStr : ids) {
            Integer id = decodeInt(idStr);
            if (id == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setId(id);
            SessionVO sessionVO = getSessionVO(request);
            params.setUpdatorId(sessionVO.getUserId());
            MethodVO methodVO = fastRetailService.updateState(params);
            if (methodVO.getCode() == 0) {
                success++;
                fastUserService.retailAccountChanged(DateUtil.getNowTimeStamp(), id);
            }
        }
        if (success == 0) {
            return ResultVO.error("操作失败");
        }
        if (success != ids.size()) {
            return ResultVO.error("部分失败");
        }
        return ResultVO.success();
    }

    @ApiName(value = "分销商-小程序全部列表", folder = {StaticFolder.FOLDER_RETAIL_SYSTEM})
    @ApiParamsIn({"type:1:int:1：微信小程序;2抖音小程序;3H5"})
    @ApiParamsOut({"id:小程序id", "appId:小程序appId", "miniName:小程序名称"})
    @RequestMapping(value = "/queryRetailMiniList", method = {RequestMethod.POST})
    public ResultVO<?> queryList(HttpServletRequest request, FastRetailMiniPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setContentType(sessionVO.getContentType());
        List<RetailMiniVO> miniList = fastRetailMiniService.queryList(params);
        return ResultVO.success(miniList);
    }

}
