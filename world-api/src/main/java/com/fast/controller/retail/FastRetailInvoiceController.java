/*
 * Powered By fast.up
 */
package com.fast.controller.retail;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.retail.FastRetailInvoicePO;
import com.fast.service.retail.FastRetailInvoiceService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 发票信息管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fast")
public class FastRetailInvoiceController extends BaseController {

    @Autowired
    private FastRetailInvoiceService retailInvoiceService;

    @ApiName(value = "发票管理-查询列表", folder = {"发票信息管理"})
    @RequestMapping(value = "/getRetailInvoiceList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastRetailInvoicePO params, PageVO pageVO) {
        return retailInvoiceService.queryPageList(params, pageVO);
    }

    @ApiName(value = "发票管理-查询单个详情", folder = {"发票信息管理"})
    @ApiParamsIn({
            "retailId:1:int:分销商数字id",
    })
    @ApiParamsOut({
            "name:名称",
            "dutyNum:税号",
            "address:地址",
            "phone:手机号",
            "account:收款账户",
            "bankName:收款账户开户银行",
    })
    @RequestMapping(value = "/getRetailInvoiceDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastRetailInvoicePO params) {
        if (StrUtil.isEmpty(params.getRetailId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        return ResultVO.success(retailInvoiceService.queryOne(params));
    }

    @ApiName(value = "发票管理-添加", folder = {"发票信息管理"})
    @RequestMapping(value = "/insertRetailInvoice", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastRetailInvoicePO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = retailInvoiceService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "发票管理-更新", folder = {"发票信息管理"})
    @RequestMapping(value = "/updateRetailInvoice", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastRetailInvoicePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = retailInvoiceService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
