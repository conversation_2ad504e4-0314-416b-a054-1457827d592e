/*
 * Powered By fast.up
 */
package com.fast.controller.user;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.user.FastRolePO;
import com.fast.service.user.FastRoleService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastRole")
public class FastRoleController extends BaseController {

    @Autowired
    private FastRoleService fastRoleService;

    @ApiName(value = "系统角色-查询列表", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"state:1:int:状态0无效1有效"})
    @ApiParamsOut(value = {
            "roleName:角色名称",
            "indexViewPermission:1:int:首页查看权限 1:仅看自己的项目 2:看全部项目",
            "contentTypeStr:系统进入权限",
            "contentTypeStr>>name:权限",
            "contentTypeStr>>contentType:权限类型",
    })
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastRolePO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        Integer cpUserType = sessionVO.getCpUserType();
        if (cpUserType == 1) {
            params.setOrgId(sessionVO.getOrgId());
        }
        params.setRetailId(sessionVO.getRetailId());
        return fastRoleService.queryPageList(params, pageVO);
    }

    @ApiName(value = "系统角色-查询单个详情", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"encryptionId:1:str:加密的角色id"})
    @ApiParamsOut(value = {"rolePO:角色信息", "roleMenuList:角色关联的菜单", "allMenuList:全部菜单列表"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastRolePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        if (params.getContentType() == null) {
            return ResultVO.error("内容类型不能为空");
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        return fastRoleService.queryById(sessionVO, params);
    }

    @ApiName(value = "系统角色-添加", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {
            "roleName:1:str:角色名称",
            "indexViewPermission:1:int:首页查看权限 1:仅看自己的项目 2:看全部项目",
            "remark:0:str:备注"})
    @ApiParamsOut(value = {"state:ok"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastRolePO params) {
        if (StrUtil.isEmpty(params.getRoleName())) {
            return ResultVO.error("角色名称不能为空");
        }
        if (params.getRoleName().length() > 30) {
            return ResultVO.error("角色名称不能超过30字符");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        if (fastRoleService.queryNameIsRepeat(params) > 0) {
            return ResultVO.error("角色名称不能重复");
        }
        if (StaticVar.FINDER_ROLE_NAME.equals(params.getRoleName())) {
            return ResultVO.error(String.format("%s,不可添加", StaticVar.FINDER_ROLE_NAME));
        }
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastRoleService.insert(sessionVO, params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "系统角色-更新角色名称", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"roleName:1:str:角色名称",
            "indexViewPermission:1:int:首页查看权限 1:仅看自己的项目 2:看全部项目",
            "encryptionId:1:str:加密的角色id"})
    @ApiParamsOut(value = {"state:ok"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastRolePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isEmpty(params.getRoleName())) {
            return ResultVO.error("角色名称不能为空");
        }
        if (params.getRoleName().length() > 30) {
            return ResultVO.error("角色名称不能超过30字符");
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        if (fastRoleService.queryNameIsRepeat(params) > 0) {
            return ResultVO.error("角色名称不能重复");
        }
        if (StaticVar.FINDER_ROLE_NAME.equals(params.getRoleName())) {
            return ResultVO.error(String.format("%s,不可修改", StaticVar.FINDER_ROLE_NAME));
        }
        params.setRetailId(null);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastRoleService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "系统角色-更新角色权限", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"menuIds:1:str:菜单id，逗号分割", "encryptionId:1:str:加密的角色id", "contentType:1:int:内容类型"})
    @ApiParamsOut(value = {"state:ok"})
    @RequestMapping(value = "/updateRoleMenu", method = {RequestMethod.POST})
    public ResultVO<?> updateRoleMenu(HttpServletRequest request, FastRolePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isEmpty(params.getMenuIds())) {
            return ResultVO.error("菜单不能为空");
        }
        if (params.getContentType() == null) {
            return ResultVO.error("内容类型不能为空");
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastRoleService.updateRoleMenu(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "系统角色-更新角色权限-批量", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {
            "list>>json数据",
            "list>>menuIds:1:str:菜单id，逗号分割",
            "list>>encryptionId:1:str:加密的角色id",
            "list>>contentType:1:int:内容类型"
    })
    @ApiParamsOut(value = {"state:ok"})
    @RequestMapping(value = "/updateBatchRoleMenu", method = {RequestMethod.POST})
    public ResultVO<?> updateBatchRoleMenu(HttpServletRequest request, @RequestBody FastRolePO params) {
        if (params.getList() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastRoleService.updateBatchRoleMenu(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "系统角色-删除角色", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"encryptionId:1:str:加密的角色id"})
    @ApiParamsOut(value = {"state:ok"})
    @RequestMapping(value = "/deleteRole", method = {RequestMethod.POST})
    public ResultVO<?> deleteRole(HttpServletRequest request, FastRolePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastRoleService.deleteRole(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
