/*
 * Powered By fast.up
 */
package com.fast.controller.user;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.user.FastUserRecentLogPO;
import com.fast.service.user.FastUserRecentLogService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
public class FastUserRecentLogController extends BaseController {

    @Autowired
    private FastUserRecentLogService fastUserRecentLogService;

    @ApiParamsIn(value = {
            "dramaIds:1:int:剧ids 短剧名称选择框",
            "dramaId:1:int:剧id",
            "retailIds:1:str:分销商ids",
            "userIds:1:str:用户ids 用户姓名输入框",
            "userId:1:int:用户id",
            "beginUpdateTime:1:str:开始观看时间",
            "endUpdateTime:1:str:结束观看时间",
    })
    @ApiParamsOut(value = {
            "retailName:分销商名称",
            "userName:用户名",
            "userId:用户id",
            "dramaName:短剧名称",
            "seriesNum:剧集号",
            "playRatio:观看进度 0.55",
            "playSecond:观看时长 秒",
            "updateTime:最后观看时间",
    })
    @ApiName(value = "user-规则校验-观看记录列表", folder = {"user"})
    @PostMapping("/user/fastUserRecentLogList/list")
    public ResultVO<?> getList(HttpServletRequest request, FastUserRecentLogPO params, PageVO pageVO) {
        return fastUserRecentLogService.queryPageList(params, pageVO);
    }

    @ApiParamsIn(value = {
            "dramaId:1:int:剧id",
            "seriesNum:1:int:剧集号",
            "playSecond:1:int:播放到的秒数",
    })
    @ApiParamsOut(value = {
            "seriesCount:ok",
    })
    @ApiName(value = "user-规则校验-观看记录插入", folder = {"user"})
    @PostMapping("/user/fastUserRecentLogList/insertOrUpdate")
    public ResultVO<?> insertOrUpdate(HttpServletRequest request, FastUserRecentLogPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUserId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        fastUserRecentLogService.insertOrUpdate(params);
        return ResultVO.success();
    }
}
