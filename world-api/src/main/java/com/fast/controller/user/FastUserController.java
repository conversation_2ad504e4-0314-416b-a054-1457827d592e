/*
 * Powered By fast.up
 */
package com.fast.controller.user;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.user.FastUserMapper;
import com.fast.po.retail.FastRetailPO;
import com.fast.po.user.FastUserCardPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.retail.FastRetailService;
import com.fast.service.system.LoginService;
import com.fast.service.user.FastUserCardService;
import com.fast.service.user.FastUserService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastUser")
public class FastUserController extends BaseController {

    private final List<Integer> childUserIdList = Lists.newArrayList();

    @Autowired
    private FastUserService fastUserService;
    @Autowired
    private LoginService loginService;
    @Autowired
    private FastUserMapper fastUserMapper;
    @Autowired
    private FastRetailService fastRetailService;
    @Autowired
    private FastUserCardService fastUserCardService;

    /**
     * 切换公众号
     *
     * @param request
     * @param officialId
     * @return
     */
    @ApiName(value = "切换公众号", folder = {StaticFolder.FOLDER_LOGIN})
    @ApiParamsIn(value = {"officialId:1:int:公众号id"})
    @ApiParamsOut(value = {"menuList:菜单树", "menuIds:全部权限菜单id"})
    @RequestMapping("/changeOfficial")
    public ResultVO<?> changeOfficial(HttpServletRequest request, Integer officialId) {
        SessionVO sessionVO = getSessionVO(request);
        return loginService.changeOfficial(officialId, sessionVO);
    }

    /**
     * 系统用户-查询列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "系统用户-查询列表", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {
            "userName:0:str:用户名称",
            "state:0:int:1启用，2禁用",
            "parentId:0:int:直属上级id",
            "retaiId:0:int:分销商id"
    })
    @ApiParamsOut(value = {
            "list 》userName:用户名称",
            "list 》loginName:登录账户",
            "list 》headImg:用户头像",
            "list 》retailId:分销商id",
            "list 》roleIds:角色id",
            "list 》roleNames:角色名称，逗号分割",
            "list 》officialIds:关联的公众号id，逗号分割",
            "list 》relateOfficial:0=未关联公众号，1=已关联公众号",
            "list 》phone:用户号码",
            "list 》parentName:直属上级",
            "list 》realName:归属人",
            "page 》total:总个数",
            "page 》totalPage:总页数",
            "page 》limit:每页条数",
            "page 》page:页码"
    })
    @Slave
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastUserPO params, PageVO pageVO) {
        if (BooleanUtils.isTrue(params.getFilterAllRetail())) {
            // 单独处理查询全部分销商用户
            FastRetailPO fastRetailPO = new FastRetailPO();
            fastRetailPO.setRetailFlag(1);
            List<FastRetailPO> fastRetailPOS = fastRetailService.querySimpleList(fastRetailPO);
            if (CollUtil.isEmpty(fastRetailPOS)) {
                params.setRetailIds("-1");
            } else {
                Set<Integer> collect = fastRetailPOS.stream().map(FastRetailPO::getId).collect(Collectors.toSet());
                params.setRetailIds(StrUtil.join(collect));
            }
            return fastUserService.queryPageList(params, pageVO);
        }

        if (params.getRetailId() != null) {
            params.setRetailId(params.getRetailId());
        } else {
            SessionVO sessionVO = getSessionVO(request);
            Integer cpUserType = sessionVO.getCpUserType();
            if (cpUserType == 1) {
                // 组织用户
                params.setOrgId(sessionVO.getOrgId());
            } else if (cpUserType == 2) {
                // 普通cp用户
                params.setRetailId(sessionVO.getRetailId());
            } else if (cpUserType == 3) {
                // 分销商
                params.setRetailId(sessionVO.getRetailId());
            } else if (cpUserType == 4) {
                // 平台用户
                params.setRetailId(0);
                params.setOrgId(0);
            }
        }
        return fastUserService.queryPageList(params, pageVO);
    }

    @ApiName(value = "系统用户-全部用户列表", folder = {StaticFolder.FOLDER_USER, StaticFolder.FOLDER_CP})
    @ApiParamsIn(value = {"state:0:int:1启用，2禁用"})
    @ApiParamsOut(value = {
            "list 》userName:用户名称",
            "list 》headImg:用户头像",
            "list 》retailId:分销商id",
            "list 》roleIds:角色id"
    })
    @Slave
    @RequestMapping(value = "/getAllList", method = {RequestMethod.POST})
    public ResultVO<?> getAllList(HttpServletRequest request, FastUserPO params) {
        params.setState(1);
        List<FastUserPO> userList = fastUserService.queryList(params);
        for (FastUserPO user : userList) {
            user.setLoginName("");
            user.setPassword("");
        }
        return ResultVO.success("ok", userList);
    }

    /**
     * 系统用户-查询优化师列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "系统用户-查询优化师列表", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {
            "retailId:0:int:分销商id 不传获取所有，传则取该分销商下优化师",
            "retailIds:0:int:分销商id ,号分割",
            "dramaId:1:int:短剧id-数字",
    })
    @ApiParamsOut(value = {
            "list 》userName:用户名称",
            "list 》loginName:登录账户",
            "list 》headImg:用户头像",
            "list 》retailId:分销商id",
            "list 》roleIds:角色id",
            "list 》roleNames:角色名称，逗号分割",
            "list 》officialIds:关联的公众号id，逗号分割",
            "list 》relateOfficial:0=未关联公众号，1=已关联公众号",
            "list 》phone:用户号码",
            "page 》total:总个数",
            "page 》totalPage:总页数",
            "page 》limit:每页条数",
            "page 》page:页码"
    })
    @Slave
    @RequestMapping(value = "/getAdvUserList", method = {RequestMethod.POST})
    public ResultVO<?> getAdvUserList(HttpServletRequest request, FastUserPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        params.setContentType(sessionVO.getContentType());
        return fastUserService.queryPageAdvUserList(params, pageVO);
    }

    /**
     * 系统用户-查询自代投、分销优化师列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "系统用户-查询自代投分销用户列表", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {
            "retailId:0:int:分销商id 不传获取所有，传则取该分销商下优化师",
            "retailIds:0:int:分销商id ,号分割",
            "retailTypes:1:int:1自投、2代投、3分销，多个逗号分割",
    })
    @ApiParamsOut(value = {
            "list 》userName:用户名称",
            "list 》loginName:登录账户",
            "list 》headImg:用户头像",
            "list 》retailId:分销商id",
            "list 》roleIds:角色id",
            "list 》roleNames:角色名称，逗号分割",
            "list 》officialIds:关联的公众号id，逗号分割",
            "list 》relateOfficial:0=未关联公众号，1=已关联公众号",
            "list 》phone:用户号码",
            "page 》total:总个数",
            "page 》totalPage:总页数",
            "page 》limit:每页条数",
            "page 》page:页码"
    })
    @Slave
    @RequestMapping(value = "/getRetailUserList", method = {RequestMethod.POST})
    public ResultVO<?> getRetailUserList(HttpServletRequest request, FastUserPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (StrUtil.isEmpty(params.getRetailTypes())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setContentType(null);
        return fastUserService.queryPageAdvUserList(params, pageVO);
    }

    @ApiName(value = "系统用户-查询单个详情", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"encryptionId:1:str:加密id"})
    @ApiParamsOut({
            "item>userName:用户名称",
            "item>loginName:登录账户",
            "item>roleIds:角色id",
            "item>parentId:直属上级id",
            "item>parentName:直属上级username",
            "item>idCard:身份证号",
            "item>idFigureUrl:身份证号人像图片url",
            "item>idBgUrl:身份证号背景图片url",
            "item>contractState:签约状态(仅达人) 0：未签约 1：已签约 2：未检索到个体工商业者信息 3：签约中 4：签约失败 5：已解约。",
            "cardList>phone:手机号",
            "cardList>realName:真实姓名",
            "cardList>cardNo:银行卡号",
            "cardList>cardName:开户行名称",
    })
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastUserPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        FastUserPO fastUser = fastUserService.queryById(params);
        Map<String, Object> results = ResultVO.getMap();
        results.put("item", fastUser);
        List<FastUserCardPO> list = fastUserCardService.queryUserId(fastUser.getId());
        results.put("cardList", list);
        return ResultVO.success(results);
    }

    @ApiName(value = "系统用户-删除单个用户", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"encryptionId:1:str:加密id"})
    @ApiParamsOut(value = {"state:ok成功"})
    @RequestMapping(value = "/deleteUser", method = {RequestMethod.POST})
    public ResultVO<?> deleteUser(HttpServletRequest request, FastUserPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        MethodVO methodVO = fastUserService.deleteById(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "系统用户-添加", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {
            "userName:1:str:用户名称",
            "headImg:1:str:头像",
            "phone:1:str:手机号",
            "loginName:1:str:登录账户",
            "roleIds:1:str:角色id，逗号分割",
            "officialIds:0:str:公众号id，逗号分割",
            "parentId:0:int:直属上级",
            "shortName:1:int:简称"
    })
    @ApiParamsOut(value = {"state:ok成功"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastUserPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
//        if(isBlank(params.getShortName())){
//            return ResultVO.error("简称不能为空");
//        }
        if (isBlank(params.getUserName())) {
            return ResultVO.error("用户名称不能为空");
        }
        if (params.getUserName().contains(" ")) {
            return ResultVO.error("名称不能包含空格");
        }
        if (isBlank(params.getLoginName())) {
            return ResultVO.error("登录账户不能为空");
        }
        if (isBlank(params.getRoleIds())) {
            return ResultVO.error("角色不能为空");
        }
        if (isBlank(params.getHeadImg())) {
            return ResultVO.error("头像不能为空");
        }
        if (isBlank(params.getPhone())) {
            return ResultVO.error("手机号不能为空");
        }
        List<Integer> roleIds = CollUtil.parseIntStr2List(params.getRoleIds());
        if (CollUtil.isEmpty(roleIds)) {
            return ResultVO.error("角色不能为空");
        }
        if (CollUtil.containsAny(roleIds, 1, 2)) {
            return ResultVO.error("角色ID不合法");
        }
        if (fastUserService.queryNameIsRepeat(params) > 0) {
            return ResultVO.error("登录账户不能重复");
        }
        if (Objects.nonNull(sessionVO.getOrgId())) {
            params.setOrgId(sessionVO.getOrgId());
        }
        params.setPassword(Md5Util.getMD5BySalt(StaticVar.DEFAULT_PASSWORD));
        params.setRetailId(sessionVO.getRetailId());
        MethodVO methodVO = fastUserService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiName(value = "系统用户-更新", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {
            "encryptionId:1:int:加密id",
            "userName:0:str:用户名称",
            "roleIds:0:str:用户角色，逗号分开",
            "shortName:1:int:简称"
    })
    @ApiParamsOut(value = {"state:ok成功"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastUserPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isNotEmpty(params.getUserName())) {
            if (params.getUserName().contains(" ")) {
                return ResultVO.error("名称不能包含空格");
            }
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        if (fastUserService.queryNameIsRepeat(params) > 0) {
            return ResultVO.error("登录账户不能重复");
        }
        // 校验直属上级
        if (Objects.nonNull(params.getParentId())) {
            ResultVO resultVO = checkParentId(sessionVO, params.getParentId(), params.getId());
            if (Objects.nonNull(resultVO)) {
                return resultVO;
            }
        }
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastUserService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "系统用户-重置密码", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"encryptionIds:1:int:加密id"})
    @ApiParamsOut(value = {"state:ok成功"})
    @RequestMapping(value = "/resetPassword", method = {RequestMethod.POST})
    public ResultVO<?> resetPassword(HttpServletRequest request, String encryptionIds) {
        if (isBlank(encryptionIds)) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        List<Integer> idList = new ArrayList<Integer>();
        String[] encryptionIdsList = encryptionIds.split(",");
        for (String encryptionIdStr : encryptionIdsList) {
            Integer id = decodeInt(encryptionIdStr);
            if (id != null && id > 0) {
                idList.add(id);
            }
        }
        if (idList.size() == 0) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastUserService.resetPasswords(idList, sessionVO.getUserId());
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "系统用户-启用/禁用", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"encryptionId:1:int:加密id", "state:1:int:1启用2禁用"})
    @ApiParamsOut(value = {"state:ok成功"})
    @RequestMapping(value = "/modifyState", method = {RequestMethod.POST})
    public ResultVO<?> modifyState(HttpServletRequest request, String encryptionId, Integer state) {
        if (isBlank(encryptionId)) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (state == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(encryptionId);
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

        SessionVO sessionVO = getSessionVO(request);
        FastUserPO userPO = new FastUserPO();
        userPO.setId(id);
        userPO.setState(state);
        MethodVO methodVO = fastUserService.update(userPO);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "系统用户-修改密码", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"password:1:str:密码"})
    @ApiParamsOut(value = {"state:ok成功"})
    @RequestMapping(value = "/updatePassword", method = {RequestMethod.POST})
    public ResultVO<?> updatePassword(HttpServletRequest request, String password) {
        if (isBlank(password)) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (password.length() < 8) {
            return ResultVO.error("密码必须大于8位!");
        }
        // 校验密码
        if (!Pattern.matches(StaticVar.ADMIN_PASSWORD_REG, password)) {
            return ResultVO.error("密码必须包含大小写字母和数字!");
        }
        SessionVO sessionVO = getSessionVO(request);
        FastUserPO item = new FastUserPO();
        item.setId(sessionVO.getUserId());
        item.setPassword(Md5Util.getMD5BySalt(password));
        item.setPasswordUpdateTime(DateUtil.getNowDate());
        MethodVO methodVO = fastUserService.update(item);
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiName(value = "系统用户-修改密码无需登录", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"password:1:str:密码"})
    @ApiParamsOut(value = {"state:ok成功"})
    @RequestMapping(value = "/nologin/updatePassword", method = {RequestMethod.POST})
    public ResultVO<?> updatePasswordNologin(HttpServletRequest request, String newPassword, String loginName, String password) {
        if (isBlank(password)) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (newPassword.length() < 8) {
            return ResultVO.error("密码必须大于8位!");
        }
        // 校验密码
        if (!Pattern.matches(StaticVar.ADMIN_PASSWORD_REG, newPassword)) {
            return ResultVO.error("密码必须包含大小写字母和数字!");
        }

        FastUserPO query = new FastUserPO();
        query.setLoginName(loginName);
        FastUserPO existsUser = fastUserMapper.queryOne(query);
        if (Objects.isNull(existsUser)) {
            return ResultVO.error("用户不存在!");
        }
        if (!existsUser.getPassword().equals(Md5Util.getMD5BySalt(password))) {
            return ResultVO.error("原始密码错误!");
        }
        FastUserPO item = new FastUserPO();
        item.setId(existsUser.getId());
        item.setPassword(Md5Util.getMD5BySalt(newPassword));
        item.setPasswordUpdateTime(DateUtil.getNowDate());
        MethodVO methodVO = fastUserService.update(item);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "系统用户-成员架构", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsOut(value = {"state:ok"})
    @RequestMapping(value = "/treeUserList", method = {RequestMethod.POST})
    public ResultVO<?> treeUserList(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        Integer cpUserType = sessionVO.getCpUserType();
        if (cpUserType == 1) {
            FastUserPO orgIdParam = new FastUserPO();
            orgIdParam.setOrgId(sessionVO.getOrgId());
            List<FastUserPO> orgUserList = fastUserService.queryList(orgIdParam);
            FastUserPO parentUser = orgUserList.stream().filter(e -> Objects.nonNull(e.getParentId()) && e.getParentId().equals(0)).findFirst().get();
            parentUser.setChildList(getChildUserList(parentUser.getId(), orgUserList));
            return ResultVO.success(parentUser);
        }
        if (cpUserType == 2) {
            FastUserPO retailIdParam = new FastUserPO();
            retailIdParam.setRetailId(sessionVO.getRetailId());
            List<FastUserPO> cpUserList = fastUserService.queryList(retailIdParam);
            FastUserPO parentUser = cpUserList.stream().filter(e -> Objects.nonNull(e.getParentId()) && e.getParentId().equals(0)).findFirst().get();
            parentUser.setChildList(getChildUserList(parentUser.getId(), cpUserList));
            return ResultVO.success(parentUser);
        }
        return ResultVO.error("非cp团队用户！");
    }

    @ApiName(value = "系统用户-查询列表", folder = {StaticFolder.FOLDER_USER})
    @ApiParamsIn(value = {"userName:0:str:用户名称", "state:0:int:1启用，2禁用", "parentId:0:int:直属上级id"})
    @ApiParamsOut(value = {
            "list 》userName:用户名称",
            "list 》loginName:登录账户",
            "list 》headImg:用户头像",
            "list 》retailId:分销商id",
            "list 》roleIds:角色id",
            "list 》roleNames:角色名称，逗号分割",
            "list 》officialIds:关联的公众号id，逗号分割",
            "list 》relateOfficial:0=未关联公众号，1=已关联公众号",
            "list 》phone:用户号码",
            "list 》parentName:直属上级",
            "page 》total:总个数",
            "page 》totalPage:总页数",
            "page 》limit:每页条数",
            "page 》page:页码"
    })
    @Slave
    @RequestMapping(value = "/getAuditUserList", method = {RequestMethod.POST})
    public ResultVO<?> getAuditUserList(HttpServletRequest request, FastUserPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getRetailId() == null || params.getRetailId() == 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getIgnoreRetailFlag() != null && params.getIgnoreRetailFlag() == 1) {
            params.setRetailId(null);
        }
        if (Objects.nonNull(sessionVO.getOrgId())) {
            params.setOrgId(sessionVO.getOrgId());
        }
        return fastUserService.getAuditUserList(params, pageVO);
    }

    private ResultVO<FastUserPO> checkParentId(SessionVO sessionVO, Integer parentId, Integer updateId) {
        if (Objects.nonNull(updateId) && parentId.equals(updateId)) {
            return ResultVO.error("直属上级不能为本人");
        }
        Integer cpUserType = sessionVO.getCpUserType();
        List<FastUserPO> userList = Lists.newArrayList();
        if (cpUserType == 1) {
            FastUserPO orgIdParam = new FastUserPO();
            orgIdParam.setOrgId(sessionVO.getOrgId());
            userList = fastUserService.queryList(orgIdParam);
        }
        if (cpUserType == 2) {
            FastUserPO retailIdParam = new FastUserPO();
            retailIdParam.setRetailId(sessionVO.getRetailId());
            userList = fastUserService.queryList(retailIdParam);
        }
        FastUserPO project = userList.stream().filter(e -> e.getId().equals(parentId)).findFirst().get();
        if (Objects.isNull(project)) {
            return ResultVO.error("直属上级非当前团队成员");
        }
        childUserIdList.clear();
        FastUserPO curUser = userList.stream().filter(e -> e.getId().equals(updateId)).findFirst().get();
        getChildUserList(curUser.getId(), userList);
        if (childUserIdList.contains(parentId)) {
            return ResultVO.error("直属上级不能是当前用户的下级");
        }
        return null;
    }


    public List<FastUserPO> getChildUserList(Integer parentId, List<FastUserPO> userList) {
        List<FastUserPO> childList = Lists.newArrayList();
        for (FastUserPO item : userList) {
            if (parentId.equals(item.getParentId())) {
                childList.add(item);
                childUserIdList.add(item.getId());
            }
        }
        for (FastUserPO child : childList) {
            List<FastUserPO> childUserList = getChildUserList(child.getId(), userList);
            child.setChildList(childUserList);
        }
        if (CollUtil.isEmpty(childList)) {
            return Lists.newArrayList();
        }
        return childList;
    }

}
