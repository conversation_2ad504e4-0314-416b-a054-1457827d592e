/*
 * Powered By fast.up
 */
package com.fast.controller.user;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.user.FastRoleContentPO;
import com.fast.service.user.FastRoleContentService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastRoleContent")
public class FastRoleContentController extends BaseController {

    @Autowired
    private FastRoleContentService fastRoleContentService;

    @ApiName(value = "【漫画版本】系统进入权限编辑", folder = {"user"})
    @ApiParamsIn({
            "encryptionId:1:str:角色id",
            "contentTypeStr:1:str:内容权限多个逗号分割"
    })
    @ApiParamsOut({""})
    @PostMapping("/updateFastRoleContent")
    public ResultVO<?> update(HttpServletRequest request, FastRoleContentPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error("加密角色id不能为空");
        }
        if (StrUtil.isEmpty(params.getContentTypeStr())) {
            return ResultVO.error("内容权限不能为空");
        }
        Integer roleId = decodeInt(params.getEncryptionId());
        if (roleId == null) {
            return ResultVO.error("角色id不能为空");
        }
        params.setRoleId(roleId);
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastRoleContentService.updateRoleContent(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
