/*
 * Powered By fast.up
 */
package com.fast.controller.user;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.user.FastLinkCreateRuleRetailPO;
import com.fast.service.user.FastLinkCreateRuleRetailService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
public class FastLinkCreateRuleRetailController extends BaseController {

    @Autowired
    private FastLinkCreateRuleRetailService fastLinkCreateRuleRetailService;


    @ApiParamsIn(value = {
            "retailId:1:str:分销商id",
            "retailIds:1:str:分销商ids",
            "retailType:1:int:分销商类型",
    })
    @ApiParamsOut(value = {"ruleName:规则名称",
            "retailId:分销商id",
            "retailName:分销商名称",
            "retailType:分销商类型",

    })
    @ApiName(value = "链接规则-应用分销商列表查询", folder = {"user"})
    @PostMapping("/user/fastLinkCreateRuleRetail/list")
    public ResultVO<?> getList(HttpServletRequest request, FastLinkCreateRuleRetailPO params, PageVO pageVO) {
        StrUtil.checkMysqlInData(params.getRetailIds());
        return fastLinkCreateRuleRetailService.queryPageList(params, pageVO);
    }


    @ApiParamsIn(value = {
            "retailIds:1:str:分销商id ,分割 1,2,3",
            "ruleId:1:int:规则id",

    })
    @ApiParamsOut(value = {
            "ok",

    })
    @ApiName(value = "链接规则-应用分销商添加", folder = {"user"})
    @PostMapping("/user/fastLinkCreateRuleRetail/insert")
    public ResultVO<?> insert(HttpServletRequest request, FastLinkCreateRuleRetailPO params) {

        StrUtil.checkMysqlInData(params.getRetailIds());
        if (StrUtil.isBlank(params.getRetailIds())) {
            return ResultVO.error("请选择分销商!");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastLinkCreateRuleRetailService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiParamsIn(value = {
            "encryptionId:1:str:加密id",

    })
    @ApiParamsOut(value = {
            "ok",

    })
    @ApiName(value = "链接规则-应用分销商删除", folder = {"user"})
    @PostMapping("/user/fastLinkCreateRuleRetail/delete")
    public ResultVO<?> delete(HttpServletRequest request, FastLinkCreateRuleRetailPO params) {
        SessionVO sessionVO = getSessionVO(request);
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        fastLinkCreateRuleRetailService.deleteById(id);
        return ResultVO.success();
    }

}
