/*
 * Powered By fast.up
 */
package com.fast.controller.user;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.user.FastLinkCreateRulePO;
import com.fast.service.user.FastLinkCreateRuleService;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
public class FastLinkCreateRuleController extends BaseController {

    @Autowired
    private FastLinkCreateRuleService fastLinkCreateRuleService;

    @ApiParamsIn(value = {"roleName:1:str:不需要参数",})
    @ApiParamsOut(value = {"ruleName:规则名称",
            "seriesRatio:单集比例",
            "seriesPlayTimeRatio:剧集数占比",

    })
    @ApiName(value = "user-链接规则", folder = {"user"})
    @PostMapping("/user/fastLinkCreateRule/detail")
    public ResultVO<?> getList(HttpServletRequest request) {
        return ResultVO.success(fastLinkCreateRuleService.getFastLinkCreateRule());
    }


    @ApiParamsIn(value = {
            "seriesRatio:1:double:单集比例",
            "seriesPlayTimeRatio:1:double:剧集数占比",
            "encryptionId:1:str:加密id",

    })
    @ApiName(value = "user-链接规则更新", folder = {"user"})
    @PostMapping("/user/fastLinkCreateRule/update")
    public ResultVO<?> update(HttpServletRequest request, FastLinkCreateRulePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        if (Objects.isNull(params.getSeriesPlayTimeRatio()) || Objects.isNull(params.getSeriesRatio())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        FastLinkCreateRulePO update = new FastLinkCreateRulePO();
        update.setId(id);
        update.setUpdatorId(getSessionVO(request).getUserId());
        update.setSeriesPlayTimeRatio(params.getSeriesPlayTimeRatio());
        update.setSeriesRatio(params.getSeriesRatio());

        return ResultVO.success(fastLinkCreateRuleService.update(update));
    }


    @ApiParamsIn(value = {
            "dramaId:1:int:剧id",

    })
    @ApiParamsOut(value = {
            "seriesCount:最少播放集数",
            "seriesPlayTimeRatio:每集播放比例 0.10",
    })
    @ApiName(value = "user-链接规则校验", folder = {"user"})
    @PostMapping("/user/fastLinkCreateRule/verify")
    public ResultVO<?> verify(HttpServletRequest request, FastLinkCreateRulePO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setUserId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId());
        List<FastLinkCreateRulePO> fastLinkCreateRulePOS = fastLinkCreateRuleService.verifyUser(params);
        return ResultVO.success(fastLinkCreateRulePOS);
    }
}
