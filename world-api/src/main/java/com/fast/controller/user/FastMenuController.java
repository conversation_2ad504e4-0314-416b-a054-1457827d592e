/*
 * Powered By fast.up
 */
package com.fast.controller.user;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.po.user.FastMenuPO;
import com.fast.service.user.FastMenuService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMenu")
public class FastMenuController extends BaseController {

    @Autowired
    private FastMenuService fastMenuService;

    @ApiName(value = "系统菜单-查询列表", folder = {"2208"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMenuPO params, PageVO pageVO) {
        return fastMenuService.queryPageList(params, pageVO);
    }

}
