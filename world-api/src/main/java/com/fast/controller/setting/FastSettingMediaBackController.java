/*
 * Powered By fast.up
 */
package com.fast.controller.setting;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.po.setting.FastSettingMediaBackLogPO;
import com.fast.po.setting.FastSettingMediaBackPO;
import com.fast.service.setting.FastSettingMediaBackLogService;
import com.fast.service.setting.FastSettingMediaBackService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 媒体返点设置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/settingMediaBack")
public class FastSettingMediaBackController extends BaseController {

    @Autowired
    private FastSettingMediaBackService settingMediaBackService;
    @Autowired
    private FastSettingMediaBackLogService settingMediaBackLogService;

    @ApiName(value = "媒体返点设置-查询列表", folder = {StaticFolder.FOLDER_MEDIA_BACK})
    @ApiParamsIn(value = {"null"})
    @ApiParamsOut(value = {
            "encryptionId:加密id",
            "mediaType:媒体 1=巨量;2=ADQ;3=快手;4=快手native;5=百度;6=抖音挂载;7=快手挂载",
            "backRatio:媒体返点(不含%)",
            "effectDate:生效日期",
            "effectDateMax:生效日期-最大",
    })
    @PostMapping("/getSettingMediaBackList")
    public ResultVO<?> getList(HttpServletRequest request, FastSettingMediaBackPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        return settingMediaBackService.queryPageList(params, pageVO);
    }

    @ApiName(value = "媒体返点设置-查询列表-历史", folder = {StaticFolder.FOLDER_MEDIA_BACK})
    @ApiParamsIn(value = {"encryptionId:1:str:加密id"})
    @ApiParamsOut(value = {
            "encryptionId:加密id",
            "mediaType:媒体 1=巨量;2=ADQ;3=快手;4=快手native;5=百度;6=抖音挂载;7=快手挂载",
            "backRatio:媒体返点(不含%)",
            "effectDate:生效日期",
            "createTime:创建时间",
            "creatorName:创建人",
    })
    @PostMapping("/getSettingMediaBackHisList")
    public ResultVO<?> getHisList(HttpServletRequest request, FastSettingMediaBackLogPO params, PageVO pageVO) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setPid(id);
        params.setDelFlag(0);
        return settingMediaBackLogService.queryPageList(params, pageVO);
    }

    @ApiName(value = "媒体返点设置-更新", folder = {StaticFolder.FOLDER_MEDIA_BACK})
    @ApiParamsIn(value = {
            "encryptionId:1:str:加密id",
            "backRatio:1:str:媒体返点(不含%)",
            "effectDateStr:1:str:生效日期 yyyy-MM-dd",
    })
    @ApiParamsOut(value = {"ok"})
    @PostMapping("/updateSettingMediaBack")
    public ResultVO<?> update(HttpServletRequest request, FastSettingMediaBackLogPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setPid(id);
        if (isEmpty(params.getEffectDateStr())) {
            return ResultVO.error("生效日期非法");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setEffectDate(DateUtil.format09(params.getEffectDateStr()));
        MethodVO methodVO = settingMediaBackLogService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "媒体返点设置-作废", folder = {StaticFolder.FOLDER_MEDIA_BACK})
    @ApiParamsIn(value = {"encryptionId:1:str:加密id"})
    @ApiParamsOut(value = {"ok"})
    @PostMapping("/delSettingMediaBack")
    public ResultVO<?> del(HttpServletRequest request, FastSettingMediaBackLogPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

        FastSettingMediaBackLogPO settingPO = settingMediaBackLogService.queryById(id);
        if (settingPO == null) {
            return ResultVO.error("非法数据ID");
        }
        if (settingPO.getEffectDate().compareTo(DateUtil.beginOfDay()) <= 0) {
            return ResultVO.error("正在生效的规则不能删除");
        }

        SessionVO sessionVO = getSessionVO(request);
        params.setId(id);
        params.setUpdatorId(sessionVO.getUserId());
        params.setUpdateTime(DateUtil.getNowDate());
        MethodVO methodVO = settingMediaBackLogService.updateDel(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
