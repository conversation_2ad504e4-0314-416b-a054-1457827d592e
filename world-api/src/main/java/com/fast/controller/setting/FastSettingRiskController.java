/*
 * Powered By fast.up
 */
package com.fast.controller.setting;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.setting.FastSettingRiskPO;
import com.fast.service.setting.FastSettingRiskService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastSettingRisk")
public class FastSettingRiskController extends BaseController {

    @Autowired
    private FastSettingRiskService fastSettingRiskService;

    @ApiName(value = "ios防封-小程序防封设置设置列表", folder = {"ios防封"})
    @ApiParamsIn({"miniId:1:int:小程序id"})
    @ApiParamsOut({
            "type:1、兜底2、投放3、运营4、分享",
            "rechargeFlag:充值开关0关1开",
            "rechargeTime:充值时间范围",
            "rechargeTotalFlag:全部剧充值条件开关",
            "rechargeOneFlag:单剧充值条件开关",
            "totalBaseCount:满足全量的剧数量，大于等于",
            "totalRechargeCount:满足全量的充值次数，大于等于",
            "totalSelect:全量的条件1满足任意一个，2同时满足两个",
            "freeAllCount:免费播放总时长占比",
            "freeSeriesCount:免费播放集数，大于等于",
            "freeSeriesRatio:免费单集播放时长比例",
            "oneSelect:单集的条件1满足任意一个，2同时满足两个",
            "imActiveFlag: 活跃用户是否开启0否1是 ",
            "imHisFlag:历史调用im支付是否开启0否1是 ",
            "imRechargeDownFlag:im充值金额下限是否开启0否1是",
            "imRechargeDown:下限金额",
            "imRechargeTimesFlag:充值次数是否开启0否1是",
            "imRechargeTimes:充值次数",
            "imRecentFlag:播放记录验证是否开启0否1是",
            "imRecentTimeAll:累计播放秒",
            "imRecentCount:每剧播放集数",
            "imRecentTimeOne:单集播放秒数",
            "imRecentSelect:播放记录1是两个任意满足，2两个都要满足"
    })
    @PostMapping("/getFastSettingRiskList")
    public ResultVO<?> getList(HttpServletRequest request, FastSettingRiskPO params, PageVO pageVO) {
        return fastSettingRiskService.queryPageList(params, pageVO);
    }

    @ApiName(value = "ios防封-小程序防封设置添加", folder = {"ios防封"})
    @ApiParamsIn({
            "body的格式:1:str:[{type:1,rechargeFlag:1,rechargeTime:'07:00-20:00',rechargeTotalFlag:1,rechargeOneFlag:1,totalBaseCount:3,totalRechargeCount:3,totalSelect:1,freeAllCount:0.5,freeSeriesCount:5,freeSeriesRatio:0.4,oneSelect:2},{type:2,...},{type:3,...},{type:4,...}]",
            "字段解释如下:1:str:字段注释如下",
            "miniId:1:int:小程序id",
            "type:1:int:1、兜底2、投放3、运营4、分享",
            "rechargeFlag:1:int:充值开关0关1开",
            "rechargeTime:1:int:充值时间范围",
            "rechargeTotalFlag:1:int:全部剧充值条件开关",
            "rechargeOneFlag:1:int:单剧充值条件开关",
            "totalBaseCount:1:int:满足全量的剧数量，大于等于",
            "totalRechargeCount:1:int:满足全量的充值次数，大于等于",
            "totalSelect:1:int:全量的条件1满足任意一个，2同时满足两个",
            "freeAllCount:1:int:免费播放总时长占比",
            "freeSeriesCount:1:int:免费播放集数，大于等于",
            "freeSeriesRatio:1:int:免费单集播放时长比例",
            "oneSelect:1:int:单集的条件1满足任意一个，2同时满足两个",
            "imActiveFlag:1:int: 活跃用户是否开启0否1是 ",
            "imHisFlag:1:int:历史调用im支付是否开启0否1是 ",
            "imRechargeDownFlag:1:int:im充值金额下限是否开启0否1是",
            "imRechargeDown:1:int:下限金额",
            "imRechargeTimesFlag:1:int:充值次数是否开启0否1是",
            "imRechargeTimes:1:int:充值次数",
            "imRecentFlag:1:int:播放记录验证是否开启0否1是",
            "imRecentTimeAll:1:int:累计播放秒",
            "imRecentCount:1:int:每剧播放集数",
            "imRecentTimeOne:1:int:单集播放秒数",
            "imRecentSelect:1:int:播放记录1是两个任意满足，2两个都要满足"
    })
    @ApiParamsOut({"state:ok"})
    @PostMapping("/insertFastSettingRisk")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody List<FastSettingRiskPO> riskList) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastSettingRiskService.insertOrUpdate(sessionVO, riskList);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "ios防封-小程序防封设置更新", folder = {"ios防封"})
    @ApiParamsIn({
            "body的格式:1:str:[{id:1,type:1,rechargeFlag:1,rechargeTime:'07:00-20:00',rechargeTotalFlag:1,rechargeOneFlag:1,totalBaseCount:3,totalRechargeCount:3,totalSelect:1,freeAllCount:0.5,freeSeriesCount:5,freeSeriesRatio:0.4,oneSelect:2},{type:2,...},{type:3,...},{type:4,...}]",
            "字段解释如下:1:str:字段注释如下",
            "miniId:1:int:小程序id",
            "encryptionId:1:str:加密id",
            "type:1:int:1、兜底2、投放3、运营4、分享",
            "rechargeFlag:1:int:充值开关0关1开",
            "rechargeTime:1:int:充值时间范围",
            "rechargeTotalFlag:1:int:全部剧充值条件开关",
            "rechargeOneFlag:1:int:单剧充值条件开关",
            "totalBaseCount:1:int:满足全量的剧数量，大于等于",
            "totalRechargeCount:1:int:满足全量的充值次数，大于等于",
            "totalSelect:1:int:全量的条件1满足任意一个，2同时满足两个",
            "freeAllCount:1:int:免费播放总时长占比",
            "freeSeriesCount:1:int:免费播放集数，大于等于",
            "freeSeriesRatio:1:int:免费单集播放时长比例",
            "oneSelect:1:int:单集的条件1满足任意一个，2同时满足两个",
            "imActiveFlag:1:int: 活跃用户是否开启0否1是 ",
            "imHisFlag:1:int:历史调用im支付是否开启0否1是 ",
            "imRechargeDownFlag:1:int:im充值金额下限是否开启0否1是",
            "imRechargeDown:1:int:下限金额",
            "imRechargeTimesFlag:1:int:充值次数是否开启0否1是",
            "imRechargeTimes:1:int:充值次数",
            "imRecentFlag:1:int:播放记录验证是否开启0否1是",
            "imRecentTimeAll:1:int:累计播放秒",
            "imRecentCount:1:int:每剧播放集数",
            "imRecentTimeOne:1:int:单集播放秒数",
            "imRecentSelect:1:int:播放记录1是两个任意满足，2两个都要满足"
    })
    @PostMapping("/updateFastSettingRisk")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody List<FastSettingRiskPO> riskList) {
        SessionVO sessionVO = getSessionVO(request);
        for (FastSettingRiskPO risk : riskList) {
            if (StrUtil.isEmpty(risk.getEncryptionId())) {
                return ResultVO.error("加密id不能为空");
            }
        }
        MethodVO methodVO = fastSettingRiskService.insertOrUpdate(sessionVO, riskList);
        return ResultVO.fromMethodVO(methodVO);
    }
}
