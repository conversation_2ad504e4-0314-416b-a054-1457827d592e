/*
 * Powered By fast.up
 */
package com.fast.controller.setting;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.setting.FastSettingFloatPO;
import com.fast.service.setting.FastSettingFloatService;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 浮窗配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastSetting")
public class FastSettingFloatController extends BaseController {

    @Autowired
    private FastSettingFloatService settingFloatService;

    /**
     * 浮窗配置-查询列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "浮窗配置-查询列表", folder = {"运营配置"})
    @ApiParamsIn(value = {
            "分页参数"
    })
    @ApiParamsOut(value = {
            "encryptionId:浮窗加密id",
            "state:是否启用::1=是;0=否",
            "floatName:浮窗名称",
            "floatLocation:浮窗位置::1=追剧页（关注类）;2=页面顶部（送K币）;3=小剧场（送福利）",
            "floatText:浮窗文案",
            "jumpType:跳转形式::1=微信链接;2=图文;3=小程序页面;4=活动;5=H5链接",
            "jumpContent:跳转内容(跳转形式为1,3,4的时候配置)",
            "jumpArticle:跳转图文ID-专属",
            "articleTitle:浮窗图文标题",
            "articleTag:1:浮窗图文标签",
            "articleContent:浮窗图文内容说明",
            "articleCode:浮窗图文二维码url",
            "articleRemark:浮窗图文说明",
            "titleBottom:浮窗图文底部标题",
    })
    @RequestMapping(value = "/getFloatSettingList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastSettingFloatPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setDelFlag(StaticVar.NO);
        return settingFloatService.queryPageList(params, pageVO);
    }

    @ApiName(value = "浮窗配置-查询单个详情", folder = {"运营配置"})
    @ApiParamsIn(value = {
            "encryptionId:浮窗加密id",
    })
    @ApiParamsOut(value = {
            "encryptionId:浮窗加密id",
            "state:是否开启::1=是;0=否",
            "floatName:浮窗名称",
            "floatLocation:浮窗位置::1=追剧页（关注类）;2=页面顶部（送K币）;3=小剧场（送福利）",
            "floatText:浮窗文案",
            "jumpType:跳转形式::1=微信链接;2=图文;3=小程序页面;4=活动;5=H5链接",
            "jumpContent:跳转内容(跳转形式为1,3,4的时候配置)",
            "jumpArticle:跳转图文ID-专属",
            "articleHeadImg:浮窗图文头像",
            "articleTitle:浮窗图文标题",
            "articleTag:1:浮窗图文标签",
            "articleContent:浮窗图文内容说明",
            "articleCode:浮窗图文二维码url",
            "articleRemark:浮窗图文说明",
            "titleBottom:浮窗图文底部标题",
    })
    @RequestMapping(value = "/getFloatSettingDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastSettingFloatPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        FastSettingFloatPO fastFloatSetting = settingFloatService.queryById(params);
        return ResultVO.success(fastFloatSetting);
    }

    /**
     * 浮窗配置-添加
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "浮窗配置-添加", folder = {"运营配置"})
    @ApiParamsIn(value = {
            "floatName:1:str:浮窗名称",
            "floatLocation:1:int:浮窗位置::1=追剧页（关注类）;2=页面顶部（送K币）;3=小剧场（送福利）",
            "floatText:1:str:浮窗文案",
            "jumpType:1:str:跳转形式::1=微信链接;2=图文;3=小程序页面;4=活动;5=H5链接",
            "jumpContent:1:str:跳转内容(跳转形式为1,3,4的时候配置)",
            "articleHeadImg:1:str:浮窗图文头像",
            "articleTitle:1:str:浮窗图文标题",
            "articleTag:1:str:浮窗图文标签",
            "articleContent:1:str:浮窗图文内容说明",
            "articleCode:1:str:浮窗图文二维码url",
            "articleRemark:1:str:浮窗图文说明",
            "titleBottom:1:str:浮窗图文底部标题",
    })
    @ApiParamsOut(value = {"success"})
    @RequestMapping(value = "/insertFloatSetting", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastSettingFloatPO params) {
        SessionVO sessionVO = getSessionVO(request);
        ResultVO<?> resultVO = checkParams(params);
        if (resultVO != null) {
            return resultVO;
        }
        params.setCreatorId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        MethodVO methodVO = settingFloatService.insert(params);

        String key = StaticVar.SETTING_FLOAT_LIST + sessionVO.getOfficialId();
        RedisUtil.del(key);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 浮窗配置-更新
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "浮窗配置-更新", folder = {"运营配置"})
    @ApiParamsIn(value = {
            "encryptionId:1:str:浮窗加密id",
            "floatName:1:str:浮窗名称",
            "floatLocation:1:int:浮窗位置::1=追剧页（关注类）;2=页面顶部（送K币）;3=小剧场（送福利）",
            "floatText:1:str:浮窗文案",
            "jumpType:1:str:跳转形式::1=微信链接;2=图文;3=小程序页面;4=活动;5=H5链接",
            "jumpContent:1:str:跳转内容(跳转形式为1,3,4的时候配置)",
            "jumpArticle:1:int:跳转图文ID-专属",
            "articleHeadImg:1:str:浮窗图文头像",
            "articleTitle:1:str:浮窗图文标题",
            "articleTag:1:str:浮窗图文标签",
            "articleContent:1:str:浮窗图文内容说明",
            "articleCode:1:str:浮窗图文二维码url",
            "articleRemark:1:str:浮窗图文说明",
            "titleBottom:1:str:浮窗图文底部标题",
    })
    @ApiParamsOut(value = {"success"})
    @RequestMapping(value = "/updateFloatSetting", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastSettingFloatPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        ResultVO<?> resultVO = checkParams(params);
        if (resultVO != null) {
            return resultVO;
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        MethodVO methodVO = settingFloatService.update(params);

        String key = StaticVar.SETTING_FLOAT_LIST + sessionVO.getOfficialId();
        RedisUtil.del(key);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 浮窗配置-启用/禁用
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "浮窗配置-启用/禁用", folder = {"运营配置"})
    @ApiParamsIn(value = {"encryptionId:1:str:浮窗加密id", "state:1:int:是否启用:1=是;0=否",})
    @ApiParamsOut(value = {"success"})
    @RequestMapping(value = "/updateFloatSettingState", method = {RequestMethod.POST})
    public ResultVO<?> updateState(HttpServletRequest request, FastSettingFloatPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        if (params.getState() == null || !StrUtil.equalsAny(params.getState(), 0, 1)) {
            return ResultVO.error("启用/禁用状态不合法");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        MethodVO methodVO = settingFloatService.updateState(params);

        String key = StaticVar.SETTING_FLOAT_LIST + sessionVO.getOfficialId();
        RedisUtil.del(key);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 浮窗配置-删除
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "浮窗配置-删除", folder = {"运营配置"})
    @ApiParamsIn(value = {"encryptionId:1:str:浮窗加密id",})
    @ApiParamsOut(value = {"success"})
    @RequestMapping(value = "/updateFloatSettingDel", method = {RequestMethod.POST})
    public ResultVO<?> updateDel(HttpServletRequest request, FastSettingFloatPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        MethodVO methodVO = settingFloatService.updateDel(params);

        String key = StaticVar.SETTING_FLOAT_LIST + sessionVO.getOfficialId();
        RedisUtil.del(key);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 校验字段合法性
     *
     * @param params
     * @return
     */
    private ResultVO<?> checkParams(FastSettingFloatPO params) {
        if (isBlank(params.getFloatName())) {
            return ResultVO.error("浮窗名称不能为空");
        }
        if (params.getFloatName().length() > 20) {
            return ResultVO.error("浮窗名称不能超过20字符");
        }
        if (params.getFloatLocation() == null) {
            return ResultVO.error("浮窗位置不能为空");
        }
        if (!StrUtil.equalsAny(params.getFloatLocation(), 1, 2, 3)) {
            return ResultVO.error("浮窗位置不合法");
        }
        if (isBlank(params.getFloatText())) {
            return ResultVO.error("浮窗文案不能为空");
        }
        if (params.getFloatText().length() > 15) {
            return ResultVO.error("浮窗文案不能超过15字符");
        }
        if (params.getJumpType() == null) {
            return ResultVO.error("跳转形式不能为空");
        }
        if (!StrUtil.equalsAny(params.getJumpType(), 1, 2, 3, 4)) {
            return ResultVO.error("跳转形式不合法");
        }
        if (params.getJumpType() == 2) {
            if (isBlank(params.getArticleHeadImg())) {
                return ResultVO.error("图文头像不能为空");
            }
            if (isBlank(params.getArticleTitle())) {
                return ResultVO.error("图文标题不能为空");
            }
            if (params.getArticleTitle().length() > 6) {
                return ResultVO.error("图文标题不能超过6字符");
            }
            if (notBlank(params.getArticleTag()) && params.getArticleTag().length() > 10) {
                return ResultVO.error("图文标签不能超过10字符");
            }
            if (isBlank(params.getArticleContent())) {
                return ResultVO.error("内容说明不能为空");
            }
            if (params.getArticleContent().length() > 100) {
                return ResultVO.error("内容说明不能超过100字符");
            }
            if (isBlank(params.getArticleCode())) {
                return ResultVO.error("二维码不能为空");
            }
            if (isBlank(params.getArticleRemark())) {
                return ResultVO.error("说明不能为空");
            }
            if (params.getArticleRemark().length() > 40) {
                return ResultVO.error("说明不能超过40字符");
            }
            if (notBlank(params.getTitleBottom()) && params.getTitleBottom().length() > 6) {
                return ResultVO.error("底部标题不能超过6字符");
            }
        } else if (isBlank(params.getJumpContent())) {
            return ResultVO.error("跳转内容不能为空");
        }
        return null;
    }
}
