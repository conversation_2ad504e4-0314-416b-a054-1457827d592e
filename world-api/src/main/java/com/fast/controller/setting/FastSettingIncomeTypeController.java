/*
 * Powered By fast.up
 */
package com.fast.controller.setting;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.setting.FastSettingIncomeTypePO;
import com.fast.service.setting.FastSettingIncomeTypeService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastSettingIncomeType")
public class FastSettingIncomeTypeController extends BaseController {

    @Autowired
    private FastSettingIncomeTypeService fastSettingIncomeTypeService;

    @ApiName(value = "fastSettingIncomeType-查询列表", folder = {"setting"})
    @ApiParamsIn({
            "timeType:0:int:入账周期，1日 2周 3月",
            "limit:0:int:分页条数，默认15条",
            "page:0:int:页数，默认1",
    })
    @ApiParamsOut(value = {
            "id:数据id",
            "title:标题",
            "type:类型，1收入",
            "timeType:入账周期，1日 2周 3月",
            "remark:备注",
            "createTime:创建时间",
            "cpDevideFlag:是否参与cp分成 1是 0否",
            "cpIncomeFrom:归属cp收入来源 1=自平台付费投放;2=自平台免费投放;3=快手自建站付费投放;4=快手自建站免费投放;5=端原生;6=三方分发",
    })
    @PostMapping("/getFastSettingIncomeTypeList")
    public ResultVO<?> getList(HttpServletRequest request, FastSettingIncomeTypePO params, PageVO pageVO) {
        return fastSettingIncomeTypeService.queryPageList(params, pageVO);
    }

    @ApiName(value = "fastSettingIncomeType-查询单个详情", folder = {"setting"})
    @ApiParamsIn({"id:1:int:数据id"})
    @ApiParamsOut(value = {
            "id:数据id",
            "title:标题",
            "type:类型，1收入",
            "timeType:入账周期，1日 2周 3月",
            "remark:备注",
            "createTime:创建时间",
            "cpDevideFlag:是否参与cp分成 1是 0否",
            "cpIncomeFrom:归属cp收入来源 1=自平台付费投放;2=自平台免费投放;3=快手自建站付费投放;4=快手自建站免费投放;5=端原生;6=三方分发",
    })
    @PostMapping("/getFastSettingIncomeTypeDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastSettingIncomeTypePO params) {
        if (params.getId() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }

        return ResultVO.success(fastSettingIncomeTypeService.queryById(params));
    }

    @ApiName(value = "fastSettingIncomeType-添加", folder = {"setting"})
    @ApiParamsIn({
            "title:1:int:测试4",
            "type:1:int:类型，1收入",
            "timeType:1:int:入账周期，1日 2周 3月",
            "remark:1:string:备注",
            "cpDevideFlag:1:int:是否参与cp分成 1是 0否",
            "cpIncomeFrom:归属cp收入来源 1=自平台付费投放;2=自平台免费投放;3=快手自建站付费投放;4=快手自建站免费投放;5=端原生;6=三方分发",
    })
    @ApiParamsOut(value = {
            "code:0",
            "state:ok",
            "success:true"
    })
    @PostMapping("/insertFastSettingIncomeType")
    public ResultVO<?> insert(HttpServletRequest request, FastSettingIncomeTypePO params) {
        // Date nowTime = DateUtil.getNowDate();

        FastSettingIncomeTypePO po = new FastSettingIncomeTypePO();
        po.setTitle(params.getTitle());
        if (fastSettingIncomeTypeService.queryOne(po) != null) {
            return ResultVO.error("该类型名称已存在");
        }

        return ResultVO.fromMethodVO(fastSettingIncomeTypeService.insert(params));
    }

    @ApiName(value = "fastSettingIncomeType-更新", folder = {"setting"})
    @ApiParamsIn({
            "id:1:int:数据id",
            "title:0:int:测试4",
            "type:0:int:类型，1收入",
            "timeType:0:int:入账周期，1日 2周 3月",
            "remark:0:string:备注",
            "cpDevideFlag:1:int:是否参与cp分成 1是 0否",
            "cpIncomeFrom:归属cp收入来源 1=自平台付费投放;2=自平台免费投放;3=快手自建站付费投放;4=快手自建站免费投放;5=端原生;6=三方分发",
    })
    @ApiParamsOut(value = {
            "code:0",
            "state:ok",
            "success:true"
    })
    @PostMapping("/updateFastSettingIncomeType")
    public ResultVO<?> update(HttpServletRequest request, FastSettingIncomeTypePO params) {
        if (params.getId() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isNotEmpty(params.getTitle())) {
            FastSettingIncomeTypePO po = new FastSettingIncomeTypePO();
            po.setTitle(params.getTitle());
            po.setNeqId(params.getId());
            if (fastSettingIncomeTypeService.queryOne(po) != null) {
                return ResultVO.error("该类型名称已存在");
            }
        }

        MethodVO methodVO = fastSettingIncomeTypeService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fastSettingIncomeType-删除", folder = {"setting"})
    @ApiParamsIn({
            "id:1:int:数据id",
    })
    @ApiParamsOut(value = {
            "code:0",
            "state:ok",
            "success:true"
    })
    @PostMapping("/deleteFastSettingIncomeType")
    public ResultVO<?> delete(HttpServletRequest request, FastSettingIncomeTypePO params) {
        if (params.getId() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        MethodVO methodVO = fastSettingIncomeTypeService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
