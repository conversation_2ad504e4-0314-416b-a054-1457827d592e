/*
 * Powered By fast.up
 */
package com.fast.controller.setting;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.setting.FastSettingSystemPO;
import com.fast.service.setting.FastSettingSystemService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastSettingSystem")
public class FastSettingSystemController extends BaseController {

    @Autowired
    private FastSettingSystemService fastSettingSystemService;

    @ApiName(value = "setting-查询列表", folder = {"setting"})
    @PostMapping("/getFastSettingSystemList")
    public ResultVO<?> getList(HttpServletRequest request, FastSettingSystemPO params, PageVO pageVO) {
        return fastSettingSystemService.queryPageList(params, pageVO);
    }

    @ApiName(value = "setting-查询指定vip限制配置", folder = {"setting"})
    @ApiParamsIn({
            "code:1:str:配置key"
    })
    @ApiParamsOut({
            "code:配置key",
            "content:配置内容"
    })
    @PostMapping("/getFastSettingSystem")
    public ResultVO<?> getSettingSystem(HttpServletRequest request, FastSettingSystemPO params) {
        if (StrUtil.isEmpty(params.getCode())) {
            return ResultVO.error("code不能为空");
        }
        FastSettingSystemPO fastSettingSystem = fastSettingSystemService.queryOne(params);
        return ResultVO.success(fastSettingSystem);
    }

    @ApiName(value = "setting-查询单个详情", folder = {"setting"})
    @PostMapping("/getFastSettingSystemDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastSettingSystemPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastSettingSystemPO fastSettingSystem = fastSettingSystemService.queryById(params);
        return ResultVO.success(fastSettingSystem);
    }

    @ApiName(value = "setting-添加", folder = {"setting"})
    @PostMapping("/insertFastSettingSystem")
    public ResultVO<?> insert(HttpServletRequest request, FastSettingSystemPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastSettingSystemService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "setting-更新全局配置", folder = {"setting"})
    @ApiParamsIn({
            "code:1:str:配置key",
            "content:1:str:配置内容"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateFastSettingSystem")
    public ResultVO<?> update(HttpServletRequest request, FastSettingSystemPO params) {
        if (StrUtil.isEmpty(params.getCode())) {
            return ResultVO.error("配置不能为空");
        }
        if (StrUtil.isEmpty(params.getContent())) {
            return ResultVO.error("内容不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastSettingSystemService.updateByCode(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
