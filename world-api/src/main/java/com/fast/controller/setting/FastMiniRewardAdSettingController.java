/*
 * Powered By fast.up
 */
package com.fast.controller.setting;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.setting.FastMiniRewardAdSettingPO;
import com.fast.service.setting.FastMiniRewardAdSettingService;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 抖音小程序广告弹出设置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniRewardAdSetting")
public class FastMiniRewardAdSettingController extends BaseController {

    @Autowired
    private FastMiniRewardAdSettingService fastMiniRewardAdSettingService;

    @ApiName(value = "抖音小程序广告弹出设置-查询列表", folder = {"setting"})
    @PostMapping("/getFastMiniRewardAdSettingList")
    @ApiParamsIn(value = {
            "miniId:1:str:抖音小程序id"
    })
    @ApiParamsOut(value = {
            "encryptionId:加密id",
            "miniId:抖音小程序id",
            "enterType:类型 1、兜底2、投放3、运营4、分享5、挂载",
            "androidAllowFlag:安卓系统是否允许弹出广告 1、允许 0、不允许",
            "iosAllowFlag:IOS系统是否允许弹出广告 1、允许 0、不允许",
            "androidRegisterDays:安卓用户注册N天内未充值",
            "androidNotChargeDays:安卓用户N天内未充值",
            "androidSequeue:安卓用户弹出顺序 1、优先于充值面板弹出 2、支付挽留弹出完毕后",
            "androidPerUnlockAmount:安卓用户每看1次广告解锁短剧集数",
            "androidUnlockMax:安卓用户每日看广告解锁上限",
            "iosRegisterDays:IOS用户注册N天内未充值",
            "iosNotChargeDays:IOS用户N天内未充值",
            "iosSequeue:IOS用户弹出顺序 1、优先于充值面板弹出 2、支付挽留弹出完毕后",
            "iosPerUnlockAmount:IOS用户每看1次广告解锁短剧集数",
            "iosUnlockMax:IOS用户每日看广告解锁上限"
    })
    public ResultVO<?> getList(HttpServletRequest request, FastMiniRewardAdSettingPO params, PageVO pageVO) {
        return fastMiniRewardAdSettingService.queryPageList(params, pageVO);
    }

    @ApiName(value = "抖音小程序广告弹出设置-添加", folder = {"setting"})
    @PostMapping("/insertFastMiniRewardAdSetting")
    @ApiParamsIn(value = {
            "body的格式:1:str:[{type:1,...},{type:2,...},{type:3,...},{type:4,...},{type:5,...}]",
            "miniId:抖音小程序id",
            "enterType:类型 1、兜底2、投放3、运营4、分享5、挂载",
            "androidAllowFlag:安卓系统是否允许弹出广告 1、允许 0、不允许",
            "iosAllowFlag:IOS系统是否允许弹出广告 1、允许 0、不允许",
            "androidRegisterDays:安卓用户注册N天内未充值",
            "androidNotChargeDays:安卓用户N天内未充值",
            "androidSequeue:安卓用户弹出顺序 1、优先于充值面板弹出 2、支付挽留弹出完毕后",
            "androidPerUnlockAmount:安卓用户每看1次广告解锁短剧集数",
            "androidUnlockMax:1:安卓用户每日看广告解锁上限",
            "iosRegisterDays:IOS用户注册N天内未充值",
            "iosNotChargeDays:IOS用户N天内未充值",
            "iosSequeue:IOS用户弹出顺序 1、优先于充值面板弹出 2、支付挽留弹出完毕后",
            "iosPerUnlockAmount:IOS用户每看1次广告解锁短剧集数",
            "iosUnlockMax:IOS用户每日看广告解锁上限"
    })
    @ApiParamsOut(value = {"success"})
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody List<FastMiniRewardAdSettingPO> params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMiniRewardAdSettingService.insertOrUpdate(sessionVO, params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "抖音小程序广告弹出设置-更新", folder = {"setting"})
    @PostMapping("/updateFastMiniRewardAdSetting")
    @ApiParamsIn(value = {
            "body的格式:1:str:[{type:1,...},{type:2,...},{type:3,...},{type:4,...},{type:5,...}]",
            "miniId:抖音小程序id",
            "enterType:类型 1、兜底2、投放3、运营4、分享5、挂载",
            "androidAllowFlag:安卓系统是否允许弹出广告 1、允许 0、不允许",
            "iosAllowFlag:IOS系统是否允许弹出广告 1、允许 0、不允许",
            "androidRegisterDays:安卓用户注册N天内未充值",
            "androidNotChargeDays:安卓用户N天内未充值",
            "androidSequeue:安卓用户弹出顺序 1、优先于充值面板弹出 2、支付挽留弹出完毕后",
            "androidPerUnlockAmount:安卓用户每看1次广告解锁短剧集数",
            "androidUnlockMax:1:安卓用户每日看广告解锁上限",
            "iosRegisterDays:IOS用户注册N天内未充值",
            "iosNotChargeDays:IOS用户N天内未充值",
            "iosSequeue:IOS用户弹出顺序 1、优先于充值面板弹出 2、支付挽留弹出完毕后",
            "iosPerUnlockAmount:IOS用户每看1次广告解锁短剧集数",
            "iosUnlockMax:IOS用户每日看广告解锁上限"
    })
    public ResultVO<?> update(HttpServletRequest request, @RequestBody List<FastMiniRewardAdSettingPO> params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMiniRewardAdSettingService.insertOrUpdate(sessionVO, params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
