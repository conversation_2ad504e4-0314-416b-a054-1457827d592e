/*
 * Powered By fast.up
 */
package com.fast.controller.setting;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.setting.FastSettingMediaProfitPO;
import com.fast.service.setting.FastSettingMediaProfitService;
import com.fast.utils.DoubleUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastSettingMediaProfit")
public class FastSettingMediaProfitController extends BaseController {

    @Autowired
    private FastSettingMediaProfitService fastSettingMediaProfitService;

    @ApiName(value = "setting-利润分配比例设置列表", folder = {"setting"})
    @ApiParamsIn(value = {
            "state:0:int:默认1,1生效，2历史修改记录",
            "mediaType:0:int:媒体 1=巨量;2=ADQ;3=快手;4=快手native;5=百度;6=抖音挂载;7=快手挂载"
    })
    @ApiParamsOut(value = {
            "encryptionId:加密id",
            "mediaType:媒体 1=巨量;2=ADQ;3=快手;4=快手native;5=百度;6=抖音挂载;7=快手挂载",
            "effectMonth:生效月",
            "effectMonthStr:生效月",
            "companyRatio:公司利润",
            "staffRatio:员工利润",
            "createTime:创建时间",
            "creatorName:创建人",
    })
    @PostMapping("/getFastSettingMediaProfitList")
    public ResultVO<?> getList(HttpServletRequest request, FastSettingMediaProfitPO params, PageVO pageVO) {
        pageVO.setLimit(100);
        if (params.getState() == null) {
            params.setState(1);
        }
        return fastSettingMediaProfitService.queryPageList(params, pageVO);
    }

    @ApiName(value = "setting-查询单个详情", folder = {"setting"})
    @PostMapping("/getFastSettingMediaProfitDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastSettingMediaProfitPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastSettingMediaProfitPO fastSettingMediaProfit = fastSettingMediaProfitService.queryById(params);
        return ResultVO.success(fastSettingMediaProfit);
    }

    @ApiName(value = "setting-添加", folder = {"setting"})
    @PostMapping("/insertFastSettingMediaProfit")
    public ResultVO<?> insert(HttpServletRequest request, FastSettingMediaProfitPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setCompanyRatio(DoubleUtil.divB4Zero(params.getCompanyRatio(), BigDecimal.valueOf(100L), 4));
        params.setStaffRatio(DoubleUtil.divB4Zero(params.getStaffRatio(), BigDecimal.valueOf(100L), 4));
        MethodVO methodVO = fastSettingMediaProfitService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "setting-利润分配比例更新", folder = {"setting"})
    @ApiParamsIn(value = {
            "encryptionId:加密id",
            "mediaType:媒体 1=巨量;102=抖音挂载;2=ADQ;4=百度;5=快手;501=快手native;502=快手挂载",
            "effectMonth:开始生效月份",
            "companyRatio:公司获得比例",
            "staffRatio:个人获得比例"
    })
    @ApiParamsOut(value = {
            "ok"
    })
    @PostMapping("/updateFastSettingMediaProfit")
    public ResultVO<?> update(HttpServletRequest request, FastSettingMediaProfitPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);

        FastSettingMediaProfitPO fastSettingMediaProfit = fastSettingMediaProfitService.queryById(params);
        if (fastSettingMediaProfit == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

        SessionVO sessionVO = getSessionVO(request);
        fastSettingMediaProfit.setId(null);
        fastSettingMediaProfit.setState(2); // 记录历史更新记录
        fastSettingMediaProfit.setUpdatorId(sessionVO.getUserId()); // 记录历史更新记录
        fastSettingMediaProfitService.insert(fastSettingMediaProfit);

        params.setCompanyRatio(DoubleUtil.divB4Zero(params.getCompanyRatio(), BigDecimal.valueOf(100L), 4));
        params.setStaffRatio(DoubleUtil.divB4Zero(params.getStaffRatio(), BigDecimal.valueOf(100L), 4));
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastSettingMediaProfitService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
