/*
 * Powered By fast.up
 */
package com.fast.controller.setting;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.po.setting.FastMemberOrderSettingPO;
import com.fast.po.setting.FastSettingCommonPO;
import com.fast.service.setting.FastMemberOrderSettingService;
import com.fast.service.setting.FastSettingCommonService;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastSettingCommon")
public class FastSettingCommonController extends BaseController {

    @Autowired
    private FastSettingCommonService settingCommonService;
    @Autowired
    private FastMemberOrderSettingService orderSettingService;

    @ApiName(value = "ios防封-查询其他设置列表", folder = {"ios防封"})
    @ApiParamsIn({"appType:1:int:1小程序2抖音小程序3h5"})
    @PostMapping("/getFastSettingCommonList")
    public ResultVO<?> getList(HttpServletRequest request, FastSettingCommonPO params, PageVO pageVO) {
        return settingCommonService.queryPageList(params, pageVO);
    }

    @ApiName(value = "通道费设置-查询列表", folder = {StaticFolder.FOLDER_FEE_RATE})
    @ApiParamsIn({"null:null"})
    @ApiParamsOut({
            "results>>type:1=微信安卓普通;2=微信IOS普通;3=微信安卓虚拟;11=抖音安卓;12=抖音IOS;13=抖音支付宝安卓;14=抖音支付宝IOS;15=抖音微信安卓;16=抖音微信IOS;21=快手安卓;22=快手IOS",
            "results>>payFeeRate:支付费率(不含%)",
            "results>>startTime:开始时间",
            "results>>endTime:结束时间",
    })
    @PostMapping("/getFeeRateList")
    public ResultVO<?> getFeeRateList(HttpServletRequest request, FastMemberOrderSettingPO params, PageVO pageVO) {
        return orderSettingService.queryPageList(params, pageVO);
    }
//
//    @ApiName(value="ios防封-查询单个详情",folder= {"ios防封"})
//    @PostMapping("/getFastSettingCommonDetail")
//    public ResultVO<?> getDetail(HttpServletRequest request, FastSettingCommonPO params) {
//        if (StrUtil.isEmpty(params.getEncryptionId())) {
//            return ResultVO.error(StaticCode.ERROR, StaticStr.ERROR_PARAM);
//        }
//        Integer id = decodeInt(params.getEncryptionId());
//        if (id == null) {
//            return ResultVO.error(StaticCode.ERROR, StaticStr.ERROR_PARAM);
//        }
//        params.setId(id);
//        SessionVO sessionVO = getSessionVO(request);
//        FastSettingCommonPO fastSettingCommon = fastSettingCommonService.queryById(params);
//        return ResultVO.success(fastSettingCommon);
//    }

    @ApiName(value = "ios防封-其他配置添加", folder = {"ios防封"})
    @ApiParamsIn({
            "appType:1:int:1微信小程序2抖音小程序3h5",
            "costPopFlag:1:int:消耗提醒0不提示1提示",
            "rechargeCheckFlag:1:int:1用户充值协议2剧集消耗提醒"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insertFastSettingCommon")
    public ResultVO<?> insert(HttpServletRequest request, FastSettingCommonPO params) {
        if (params.getAppType() == null) {
            ResultVO.error("app类型不能为空");
        }
        if (params.getCostPopFlag() == null) {
            ResultVO.error("消耗提醒设置不能为空");
        }
        if (params.getRechargeCheckFlag() == null) {
            ResultVO.error("用户充值协议设置不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = settingCommonService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "ios防封-其他配置更新", folder = {"ios防封"})
    @ApiParamsIn({
            "appType:1:int:1微信小程序2抖音小程序3h5",
            "costPopFlag:1:int:消耗提醒0不提示1提示",
            "rechargeCheckFlag:1:int:1用户充值协议2剧集消耗提醒"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateFastSettingCommon")
    public ResultVO<?> update(HttpServletRequest request, FastSettingCommonPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        if (params.getAppType() == null) {
            ResultVO.error("app类型不能为空");
        }
        if (params.getCostPopFlag() == null) {
            ResultVO.error("消耗提醒设置不能为空");
        }
        if (params.getRechargeCheckFlag() == null) {
            ResultVO.error("用户充值协议设置不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = settingCommonService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
