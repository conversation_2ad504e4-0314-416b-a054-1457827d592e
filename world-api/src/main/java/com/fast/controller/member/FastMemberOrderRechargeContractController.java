/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.member.FastMemberOrderRechargeContractPO;
import com.fast.service.member.FastMemberOrderRechargeContractService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
public class FastMemberOrderRechargeContractController extends BaseController {

    @Autowired
    private FastMemberOrderRechargeContractService fastMemberOrderRechargeContractService;

    @ApiParamsIn({
            "encryptionId:0:str:加密签约单id",
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "id:平台订单号",
            "moneyRecharge:充值金额",
            "moneyProfit:实到金额",
            "createTime:交易时间",
            "state:支付状态:0=待支付;1=已支付;2=已关闭;3=已退款",
            "payFeeRate:通道费",
    })
    @ApiName(value = "member-签约订单记录", folder = {"member"})
    @PostMapping("/fastMemberOrderRechargeContract/list")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberOrderRechargeContractPO params, PageVO pageVO) {
        Long id = decodeLong(params.getEncryptionId());
        params.setContractId(id);
        return fastMemberOrderRechargeContractService.queryPageList(params, pageVO);
    }
}
