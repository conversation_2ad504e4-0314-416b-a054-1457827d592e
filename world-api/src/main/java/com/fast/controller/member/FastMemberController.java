/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.member.FastMemberPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.member.FastMemberExportService;
import com.fast.service.member.FastMemberService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMember")
public class FastMemberController extends BaseController {

    @Autowired
    private FastMemberService memberService;
    @Autowired
    private FastMemberExportService exportService;
    @Autowired
    private FastLinkService linkService;

    /**
     * 用户管理-查询列表
     */
    @ApiName(value = "用户管理-查询列表", folder = {StaticFolder.FOLDER_MEMBER_MANGER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "officialIds:0:str:公众号id-数字(多个逗号分隔)",
            "miniIds:0:str:小程序id-数字(多个逗号分隔)",
            "memberId:0:int:用户id-数字",
            "memberNameLike:0:str:昵称",
            "linkId:0:str:链接ID",
            "linkNameLike:0:str:链接名称",
            "openid:0:str:小程序openid",
            "vipState:0:int:VIP状态：0无效，1有效",
            "officialFollowState:0:int:关注状态：0无效，1有效",
            "phoneOs:0:int:手机系统：1安卓，2爱疯",
            "wxDramaIdLike:微信视频ID(内容ID)",
            "wxPromotionId:微信视频号加热订单ID",
            "wxFinderId:微信视频号ID",
            "wxFinderName:微信视频号名称",
            "registerTimeStr:0:str:注册时间(第一次)：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "activeFlag:0:int:活跃状态0非活跃1活跃"
    })
    @ApiParamsOut({
            "id:用户id",
            "encryptionId:加密id",
            "memberImg:用户头像",
            "memberName:昵称",
            "openid:小程序openid",
            "sex:性别：0未知，1男2女",
            "vipState:VIP状态：0无效，1有效",
            "rechargeMoney:累计充值金额",
            "rechargeCount:累计充值次数",
            "coinRemain:金币剩余",
            "playCount:播放次数",
            "phoneOs:手机系统：1安卓，2爱疯",
            "officialFollowState:关注状态：0无效，1有效",
            "awemeFollowState:抖音号关注状态：0否1是",
            "createTime:注册时间(第一次)",
            "lastLoginTime:上次登录时间（时间戳）",
            "activeDays:总活跃天数",
            "downloadChannel:下载渠道",
            "region:地区",
            "lang:语言",
            "pushOpen:是否开启推送（0、否；1、是）",
            "officialName:公众号名称",
            "miniName:小程序名称",
            "retailName:分销商名称",
            "phone:用户手机号",
            "wxDramaId:微信视频ID(内容ID)",
            "wxPromotionId:微信视频号加热订单ID",
            "wxFinderId:微信视频号ID",
            "wxFinderName:微信视频号名称",
            "linkSubType:1=星图推广;2=小程序推广;3=作品挂载;4=聚星推广;5=微信视频号",
            "addDesktop:是否加桌0否，1是"
    })
    @Slave
    @RequestMapping(value = "/getMemberList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberPO params, PageVO pageVO) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getOfficialIds(), params.getOfficialIds2(), params.getMiniIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        // 处理时间
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));
        }
        if (notEmpty(params.getLinkNameLike())) {
            FastLinkPO query = new FastLinkPO();
            query.setLinkNameLike(params.getLinkNameLike());
            query.setRetailId(params.getRetailId());
            Set<Integer> ids = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setLinkIds(StrUtil.join(ids));
        }
        if (notEmpty(params.getLinkTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
            params.setLinkTimeS(date.get(0));
            params.setLinkTimeE(date.get(1));
        }

        // 微信视频号相关搜索
        params.setWxFinderIds(StrUtil.joinStr(params.getWxFinderIds()));
        if (notEmpty(params.getWxDramaIdLike()) || notEmpty(params.getWxFinderId()) || notEmpty(params.getWxFinderIds()) || notEmpty(params.getWxPromotionId())) {
            params.setWxFinderFlag(1);
        }

        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = exportService.exportMemberList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        } else {
            return memberService.queryPageList(params, pageVO);
        }
    }

    /**
     * 用户管理-查询单个详情
     */
    @RequestMapping(value = "/getMemberDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberPO fastMember = memberService.queryById(params);
        Map<String, Object> results = ResultVO.getMap();
        results.put("item", fastMember);
        return ResultVO.success(results);
    }

    /**
     * 用户管理-查询单个详情
     */
    @RequestMapping(value = "/nologin/queryMemberIdByOutTransId")
    public ResultVO<?> queryMemberIdByOutTransId(HttpServletRequest request, String orderId) {
        if (StrUtil.isEmpty(orderId)) {
            return ResultVO.error("订单id不能为空");
        }
        Long memberId = memberService.queryMemberIdByOutTransId(orderId);
        Map<String, Object> results = ResultVO.getMap();
        results.put("orderId", orderId);
        results.put("memberId", memberId);
        return ResultVO.success(results);
    }

}
