package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.member.FastMemberBackHandPO;
import com.fast.service.member.FastMemberBackHandService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Objects;

@RestController
public class FastMemberHandbackController extends BaseController {

    @Autowired
    private FastMemberBackHandService fastMemberBackHandService;


    @ApiName(value = "member-手动回传列表", folder = {"member"})
    @ApiParamsIn({
            "backState:1:int:回传状态 回传状态:1-未回传 2-回传成功 3-回传失败",
            "linkId:1:int:链接id",
            "promotionId:1:int:广告id",
            "backAuto:1:int:回传方式 1=自动回传;2=手动回传",
            "orderCountType:1:int:订单数量类型 1大于0 2=0",
    })
    @ApiParamsOut({
            "memberId:用户id",
            "linkId:链接id",
            "linkName:推广链接名称",
            "feeFlag:链接类型:1=付费;2=免费",
            "moneyRecharge:回传金额",
            "backState:回传状态:1-未回传 2-回传成功 3-回传失败",
            "appType:推广应用类型",
            "appName:推广应用名称小程序名称",
            "promotionId:广告id",
            "orderCount:产生订单数",
            "backAuto:回传方式 1=自动回传;2=手动回传",
    })
    @PostMapping("/fastMemberLinkManualCallback/queryList")
    public ResultVO<?> queryList(HttpServletRequest request, FastMemberBackHandPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        // 回传状态 订单数 回传方式推广链接id 广告id
        FastMemberBackHandPO queryParams = new FastMemberBackHandPO();
        queryParams.setBackState(params.getBackState());
        queryParams.setOrderCountType(params.getOrderCountType());
        queryParams.setLinkId(params.getLinkId());
        queryParams.setPromotionId(params.getPromotionId());
        queryParams.setBackAuto(params.getBackAuto());
        queryParams.setRetailId(sessionVO.getRetailId());
        queryParams.setContentType(sessionVO.getContentType());
        return fastMemberBackHandService.queryPageList(queryParams, pageVO);
    }

    @ApiName(value = "member-手动回传", folder = {"member"})
    @ApiParamsIn({
            "moneyRecharge:1:double:回传金额 元",
            "encryptionId:1:str:加密id",
    })
    @PostMapping("/fastMemberLinkManualCallback/manualCallback")
    public ResultVO<?> manualCallback(HttpServletRequest request, FastMemberBackHandPO params) {

        Integer id = decodeInt(params.getEncryptionId());
        SessionVO sessionVO = getSessionVO(request);
        if (Objects.isNull(params.getMoneyRecharge()) || params.getMoneyRecharge().compareTo(BigDecimal.valueOf(0.1)) < 0) {
            return ResultVO.error("金额错误!");
        }
        fastMemberBackHandService.manualCallback(sessionVO.getUserId(), id, params.getMoneyRecharge());
        return ResultVO.success();
    }

}
