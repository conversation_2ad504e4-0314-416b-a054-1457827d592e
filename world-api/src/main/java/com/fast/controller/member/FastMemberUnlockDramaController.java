/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberUnlockDramaPO;
import com.fast.service.unlock.FastMemberUnlockDramaService;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户解锁剧集
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMember")
public class FastMemberUnlockDramaController extends BaseController {

    @Autowired
    private FastMemberUnlockDramaService fastMemberUnlockDramaService;

    @ApiName(value = "解锁剧集-查询列表", folder = {"用户"})
    @RequestMapping(value = "/getMemberUnlockDramaList", method = {RequestMethod.POST})
    @Slave
    public ResultVO<?> getList(HttpServletRequest request, FastMemberUnlockDramaPO params, PageVO pageVO) {
        return fastMemberUnlockDramaService.queryPageList(params, pageVO);
    }

    @ApiName(value = "解锁剧集-查询单个详情", folder = {"用户"})
    @RequestMapping(value = "/getMemberUnlockDramaDetail", method = {RequestMethod.POST})
    @Slave
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberUnlockDramaPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberUnlockDramaPO fastMemberUnlockDrama = fastMemberUnlockDramaService.queryById(params);
        return ResultVO.success(fastMemberUnlockDrama);
    }
}
