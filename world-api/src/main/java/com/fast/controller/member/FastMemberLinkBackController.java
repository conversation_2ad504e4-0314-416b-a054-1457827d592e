/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.po.member.FastMemberLinkBackPO;
import com.fast.service.member.FastMemberLinkBackService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/member")
public class FastMemberLinkBackController extends BaseController {

    @Autowired
    private FastMemberLinkBackService fastMemberLinkBackService;

    @ApiName(value = "member-查询列表", folder = {"member"})
    @PostMapping("/getFastMemberLinkBackList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberLinkBackPO params, PageVO pageVO) {
        return fastMemberLinkBackService.queryPageList(params, pageVO);
    }


}
