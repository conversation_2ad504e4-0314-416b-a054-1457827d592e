/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberSubLinkPO;
import com.fast.service.member.FastMemberSubLinkService;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberSubLink")
public class FastMemberSubLinkController extends BaseController {

    @Autowired
    private FastMemberSubLinkService fastMemberSubLinkService;

    @ApiName(value = "member-查询列表", folder = {"member"})
    @PostMapping("/getFastMemberSubLinkList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberSubLinkPO params, PageVO pageVO) {
        return fastMemberSubLinkService.queryPageList(params, pageVO);
    }

    @ApiName(value = "member-查询单个详情", folder = {"member"})
    @PostMapping("/getFastMemberSubLinkDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberSubLinkPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberSubLinkPO fastMemberSubLink = fastMemberSubLinkService.queryById(params);
        return ResultVO.success(fastMemberSubLink);
    }


}
