/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberBlackPO;
import com.fast.service.member.FastMemberBlackService;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberBlack")
public class FastMemberBlackController extends BaseController {

    @Autowired
    private FastMemberBlackService fastMemberBlackService;

    @ApiName(value = "ios防封-黑名单查询列表", folder = {"ios防封"})
    @ApiParamsIn({
            "id:0:int:用户id",
            "openid:0:str:openid",
            "retailIds:0:str:分销商id逗号分割",
            "officialIds:0:str:公众号id逗号分割",
            "miniIds:0:str:小程序id逗号分割"
    })
    @ApiParamsOut({
            "id:用户id",
            "remark:拉黑原因",
            "openid:openid",
            "officialName:公众号",
            "retailName:分销商",
            "miniName:小程序名称",
            "phoneOs:操作系统1安卓2ios",
            "registTime:注册时间",
            "userName:操作人"
    })
    @PostMapping("/getFastMemberBlackList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberBlackPO params, PageVO pageVO) {
        return fastMemberBlackService.queryPageList(params, pageVO);
    }

//    @ApiName(value="member-查询单个详情",folder= {"member"})
//    @PostMapping("/getFastMemberBlackDetail")
//    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberBlackPO params) {
//        if (StrUtil.isEmpty(params.getEncryptionId())) {
//            return ResultVO.error(StaticCode.ERROR, StaticStr.ERROR_PARAM);
//        }
//        Integer id = decodeInt(params.getEncryptionId());
//        if (id == null) {
//            return ResultVO.error(StaticCode.ERROR, StaticStr.ERROR_PARAM);
//        }
//        params.setId(id);
//        SessionVO sessionVO = getSessionVO(request);
//        FastMemberBlackPO fastMemberBlack = fastMemberBlackService.queryById(params);
//        return ResultVO.success(fastMemberBlack);
//    }

    @ApiName(value = "ios防封-黑名单添加", folder = {"ios防封"})
    @ApiParamsIn({"memberId:1:int:用户id", "remark:1:str:拉黑原因"})
    @ApiParamsOut({"state:ok"})
    @PostMapping("/insertFastMemberBlack")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberBlackPO params) {
        if (params.getMemberId() == null) {
            return ResultVO.error("用户id不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMemberBlackService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "ios防封-黑名单移除", folder = {"ios防封"})
    @ApiParamsIn({"encryptionId:1:str:加密的id"})
    @ApiParamsOut({"state:ok"})
    @PostMapping("/updateFastMemberBlack")
    public ResultVO<?> update(HttpServletRequest request, FastMemberBlackPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        params.setDelFlag(1);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMemberBlackService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
