/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.po.member.FastMemberCoinChangePO;
import com.fast.po.member.FastMemberPO;
import com.fast.service.member.FastMemberAccountService;
import com.fast.service.member.FastMemberCoinChangeExportService;
import com.fast.service.member.FastMemberCoinChangeService;
import com.fast.service.member.FastMemberService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.member.MemberAccountVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 增加减少K币/VIP卡
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMember")
public class FastMemberCoinChangeController extends BaseController {

    @Autowired
    private FastMemberCoinChangeService coinChangeService;
    @Autowired
    private FastMemberService memberService;
    @Autowired
    private FastMemberAccountService memberAccountService;
    @Autowired
    private FastMemberCoinChangeExportService exportService;

    /**
     * 用户调币/VIP-查询列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "用户调币/VIP-查询列表", folder = {StaticFolder.FOLDER_MEMBER_MANGER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "officialIds:0:str:公众号id-数字(多个逗号分隔)",
            "miniIds:0:str:小程序id-数字(多个逗号分隔)",
            "memberId:0:int:用户id-数字",
            "memberNameLike:0:str:昵称",
            "openid:0:str:小程序openid",
            "changeType:0:int:调整类型1=赠送;2=扣除",
            "type:0:int:应用类型 1=微信;2=抖音;3=H5;4=快手",
            "coinType:0:int:1=K币;2=VIP卡",
            "deadTimeStr:0:str:到期时间：yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "results>>memberId:用户id-数字",
            "results>>openid:用户openid",
            "results>>memberName:用户昵称",
            "results>>memberImg:用户图像",
            "results>>coinChange:金币变更数(或VIP卡天数)",
            "results>>changeType:1=赠送;2=扣除;3=过期",
            "results>>remark:备注",
            "results>>creatorName:操作者",
            "results>>officialName:公众号名称",
            "results>>miniName:应用名称",
            "results>>type:应用类型 1=微信;2=抖音;3=H5;4=快手",
            "results>>retailName:分销商名称",
            "results>>deadTime:有效期",
            "results>>coinRemain:当前K币余额",
            "results>>deadTime:vip会员到期时间",
            "results>>createTime:操作时间",
            "summary>>coinChangeMemberCount:调币总用户数",
            "summary>>coinChangeGive:人工赠送总K币(或VIP卡天数)",
            "summary>>coinChangeReduce:扣除总K币(或VIP卡天数)",
    })
    @Slave
    @RequestMapping(value = "/getCoinChangeList", method = {RequestMethod.POST})
    public ResultVO<?> getCoinChangeList(HttpServletRequest request, FastMemberCoinChangePO params, PageVO pageVO) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds(), params.getMemberIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        // 处理时间
        if (notBlank(params.getDeadTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getDeadTimeStr());
            params.setDeadTimeS(date.get(0));
            params.setDeadTimeE(date.get(1));
        }
        if (notBlank(params.getMemberNameLike())) {
            FastMemberPO query = new FastMemberPO();
            query.setMemberNameLike(params.getMemberNameLike());
            query.setRetailId(params.getRetailId());
            List<Long> ids = memberService.queryMemberIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setMemberIds(StrUtil.joinNoRepeat(ids));
        }
        if (params.getCoinType() == null || params.getCoinType() <= 0) {
            params.setCoinType(1);
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = exportService.exportMemberCoinChangeList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        } else {
            return coinChangeService.queryPageList(params, pageVO);
        }
    }

    /**
     * 用户调币-平台端汇总
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "用户调币-平台端汇总", folder = {StaticFolder.FOLDER_MEMBER_MANGER})
    @ApiParamsIn({"token"})
    @ApiParamsOut({
            "retailId:分销商id",
            "retailName:分销商名称",
            "coinType:0:int:1=K币;2=VIP卡",
            "coinChangeCount:当月调币总次数",
            "coinChangeMemberCount:当月调币总用户数",
            "coinChangeGive:当月人工赠送总K币",
            "coinChangeReduce:当月扣除总K币",
    })
    @Slave
    @RequestMapping(value = "/getCoinChangeSummaryList", method = {RequestMethod.POST})
    public ResultVO<?> getCoinChangeSummaryList(HttpServletRequest request, FastMemberCoinChangePO params, PageVO pageVO) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds(), params.getMemberIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getCoinType() == null || params.getCoinType() <= 0) {
            params.setCoinType(1);
        }
        // 处理时间(当月)
        params.setCreateTimeS(DateUtil.beginOfMonth());
        params.setCreateTimeE(DateUtil.endOfMonth());

        return coinChangeService.getCoinChangeSummaryList(params, pageVO);
    }

    @ApiName(value = "用户调币-查询单个详情", folder = {StaticFolder.FOLDER_MEMBER_MANGER})
    @RequestMapping(value = "/getCoinChangeDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberCoinChangePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberCoinChangePO fastMemberCoinChange = coinChangeService.queryById(params);
        return ResultVO.success(fastMemberCoinChange);
    }

    @ApiName(value = "用户调币-查询用户余额", folder = {StaticFolder.FOLDER_MEMBER_MANGER})
    @ApiParamsIn({
            "memberId:0:int:用户id-数字",
    })
    @ApiParamsOut({
            "deadTime:vip会员到期时间",
            "leftDay:vip剩余天数",
    })
    @RequestMapping(value = "/getMemberAccountDetail", method = {RequestMethod.POST})
    public ResultVO<?> getMemberAccountDetail(HttpServletRequest request, Long memberId) {
        MemberAccountVO accountVO = memberAccountService.queryInfoByRedis(memberId);
        if (accountVO != null) {
            int leftDay = DateUtil.daysBetweenUp(DateUtil.getNowDate(), accountVO.getDeadTime());
            if (leftDay <= 0) {
                leftDay = 0;
            }
            accountVO.setLeftDay(leftDay);
        }
        return ResultVO.success(accountVO);
    }

    @ApiName(value = "用户调币-调币/VIP", folder = {StaticFolder.FOLDER_MEMBER_MANGER})
    @ApiParamsIn({
            "memberId:0:int:用户id-数字",
            "coinType:0:int:1=K币;2=VIP卡",
            "coinChange:0:int:金币变更数(或VIP卡天数)",
            "changeType:0:int:1=赠送;2=扣除",
            "remark:0:str:备注",
    })
    @ApiParamsOut({"success"})
    @RequestMapping(value = "/addCoinChange", method = {RequestMethod.POST})
    public ResultVO<?> addCoinChange(HttpServletRequest request, @Validated FastMemberCoinChangePO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setCreatorId(sessionVO.getUserId());
        final JedisLock lock = new JedisLock(StaticVar.ADD_COIN_CHANGE_LOCK + params.getMemberId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            if (params.getCoinType() == null || params.getCoinType() <= 0) {
                params.setCoinType(1);
            }
            if (params.getCoinType() == 2) {
                if (params.getCoinChange() > 730) {
                    return ResultVO.error("VIP调整天数不能超过730天");
                }
            }
            MethodVO methodVO = coinChangeService.addCoinChange(params, 1);
            String key = StaticVar.MEMBER_ACCOUNT_ID + params.getMemberId();
            RedisUtil.del(key);
            return ResultVO.fromMethodVO(methodVO);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }
}
