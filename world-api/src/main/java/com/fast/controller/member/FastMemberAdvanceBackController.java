/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberAdvanceBackPO;
import com.fast.service.member.FastMemberAdvanceBackService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/member")
public class FastMemberAdvanceBackController extends BaseController {

    @Autowired
    private FastMemberAdvanceBackService fastMemberAdvanceBackService;


    @ApiName(value = "member-批量预先回传", folder = {"member"})
    @ApiParamsIn({
            "memberId:1:int:用户id",
            "moneyRecharge:1:int:金额",
            "rechargeRateValue:1:str:充值概率",
            "likeRateValue:1:str:偏好匹配率"
    })
    @ApiParamsOut({
            "state:ok"
    })
    @PostMapping("/nologin/doAdvanceBackBatch")
    public ResultVO<?> doAdvanceBackBatch(@RequestBody List<FastMemberAdvanceBackPO> advanceList) {
        return fastMemberAdvanceBackService.doAdvanceBackBatch(advanceList);
    }

    @ApiName(value = "member-查询列表", folder = {"member"})
    @PostMapping("/getFastMemberAdvanceBackList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberAdvanceBackPO params, PageVO pageVO) {
        return fastMemberAdvanceBackService.queryPageList(params, pageVO);
    }

    @ApiName(value = "member-查询单个详情", folder = {"member"})
    @PostMapping("/getFastMemberAdvanceBackDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberAdvanceBackPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberAdvanceBackPO fastMemberAdvanceBack = fastMemberAdvanceBackService.queryById(params);
        return ResultVO.success(fastMemberAdvanceBack);
    }

    @ApiName(value = "member-添加", folder = {"member"})
    @PostMapping("/insertFastMemberAdvanceBack")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberAdvanceBackPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberAdvanceBackService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
