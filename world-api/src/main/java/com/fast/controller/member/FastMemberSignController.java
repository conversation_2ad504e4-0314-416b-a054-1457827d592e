/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberSignPO;
import com.fast.service.member.FastMemberSignService;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户签到记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMember")
public class FastMemberSignController extends BaseController {

    @Autowired
    private FastMemberSignService fastMemberSignService;

    @ApiName(value = "用户签到-查询列表", folder = {"用户"})
    @RequestMapping(value = "/getMemberSignList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberSignPO params, PageVO pageVO) {
        return fastMemberSignService.queryPageList(params, pageVO);
    }

    @ApiName(value = "用户签到-查询单个详情", folder = {"用户"})
    @RequestMapping(value = "/getMemberSignDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberSignPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberSignPO fastMemberSign = fastMemberSignService.queryById(params);
        return ResultVO.success(fastMemberSign);
    }
}
