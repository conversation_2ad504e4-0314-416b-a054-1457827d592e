/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberPushDetailPO;
import com.fast.service.member.FastMemberPushDetailService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/member")
public class FastMemberPushDetailController extends BaseController {

    @Autowired
    private FastMemberPushDetailService fastMemberPushDetailService;

    @ApiName(value = "member-查询列表", folder = {"member"})
    @PostMapping("/getFastMemberPushDetailList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberPushDetailPO params, PageVO pageVO) {
        return fastMemberPushDetailService.queryPageList(params, pageVO);
    }

    @ApiName(value = "member-查询单个详情", folder = {"member"})
    @PostMapping("/getFastMemberPushDetailDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberPushDetailPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        FastMemberPushDetailPO fastMemberPushDetail = fastMemberPushDetailService.queryById(params);
        return ResultVO.success(fastMemberPushDetail);
    }

    @ApiName(value = "member-添加", folder = {"member"})
    @PostMapping("/insertFastMemberPushDetail")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberPushDetailPO params) {
        MethodVO methodVO = fastMemberPushDetailService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
