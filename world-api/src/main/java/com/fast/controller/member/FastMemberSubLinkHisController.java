/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberSubLinkHisPO;
import com.fast.service.member.FastMemberSubLinkHisService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberSubLinkHis")
public class FastMemberSubLinkHisController extends BaseController {

    @Autowired
    private FastMemberSubLinkHisService fastMemberSubLinkHisService;

    @ApiName(value = "member-次归属历史查询列表", folder = {"member"})
    @ApiParamsIn({
            "memberId:0:int:用户id",
            "retailIds:0:str:分销商id们",
            "linkName:0:str:链接名称",
            "linkId:0:int:链接id",
            "subLinkName:0:str:链接名称",
            "subLinkId:0:int:链接id",
            "linkSubTypes:0:int:1=星图推广;2=小程序推广;3=作品挂载;4=聚星推广;5=微信视频号",
            "subLinkTimeStartStr:0:挂载归属开始日期str:yyyy-mm-dd",
            "subLinkTimeEndStr:0:str:挂载归属结束日期yyyy-mm-dd",
            "registTimeStartStr:0:str:注册开始日期yyyy-mm-dd",
            "registTimeEndStr:0:str:注册结束日期yyyy-mm-dd"
    })
    @ApiParamsOut({
            "memberId:用户id",
            "retailName:分销商名称",
            "miniName:小程序名称",
            "linkId:推广链接id",
            "linkName:推广链接名称",
            "subLinkId:挂载id",
            "subLinkName:挂载名称",
            "subLinkTime:挂载时间",
            "registTime:注册时间",
            "linkTime:归属推广链接时间"
    })
    @PostMapping("/getFastMemberSubLinkHisList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberSubLinkHisPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (biggerZero(params.getLinkSubType())) {
            params.setLinkType(3);// 挂载链接
        }
        params.setContentType(sessionVO.getContentType());
        params.setLinkSubTypes(StrUtil.resetInt(params.getLinkSubTypes()));
        params.setRetailIds(StrUtil.resetInt(params.getRetailIds()));
        if (StrUtil.isNotEmpty(params.getSubLinkTimeStartStr())) {
            params.setSubLinkTimeStart(DateUtil.beginOfDay(DateUtil.format09(params.getSubLinkTimeStartStr())));
        }
        if (StrUtil.isNotEmpty(params.getSubLinkTimeEndStr())) {
            params.setSubLinkTimeEnd(DateUtil.endOfDay(DateUtil.format09(params.getSubLinkTimeEndStr())));
        }
        if (StrUtil.isNotEmpty(params.getRegistTimeStartStr())) {
            params.setRegistTimeStart(DateUtil.beginOfDay(DateUtil.format09(params.getRegistTimeStartStr())));
        }
        if (StrUtil.isNotEmpty(params.getRegistTimeEndStr())) {
            params.setRegistTimeEnd(DateUtil.endOfDay(DateUtil.format09(params.getRegistTimeEndStr())));
        }
        return fastMemberSubLinkHisService.queryPageList(params, pageVO);
    }

    @ApiName(value = "member-查询单个详情", folder = {"member"})
    @PostMapping("/getFastMemberSubLinkHisDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberSubLinkHisPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        FastMemberSubLinkHisPO fastMemberSubLinkHis = fastMemberSubLinkHisService.queryById(params);
        return ResultVO.success(fastMemberSubLinkHis);
    }


}
