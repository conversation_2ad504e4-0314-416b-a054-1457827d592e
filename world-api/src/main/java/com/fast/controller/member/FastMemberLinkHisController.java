/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticVar;
import com.fast.po.promote.FastMemberLinkHisPO;
import com.fast.service.promote.FastMemberLinkHisExportService;
import com.fast.service.promote.FastMemberLinkHisService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberLinkHis")
public class FastMemberLinkHisController extends BaseController {

    @Autowired
    private FastMemberLinkHisService fastMemberLinkHisService;
    @Autowired
    private FastMemberLinkHisExportService fastMemberLinkHisExportService;

    @ApiName(value = "member-查询染色记录列表", folder = {"member"})
    @ApiParamsIn({
            "memberId:0:int:会员id",
            "miniId:0:int:小程序id",
            "colorTimeStartStr:0:str:染色开始时间：yyyy-MM-DD",
            "colorTimeEndStr:0:str:染色结束时间：yyyy-MM-DD"
    })
    @ApiParamsOut({
            "miniType:1：微信小程序;2抖音小程序;3H5;4快手",
            "miniName:小程序名称",
            "officialName:公众号名称",
            "retailName:分销商名称",
            "linkName:链接名称",
            "memberId:会员id",
            "colorIn:注册时间"
    })

    @PostMapping("/getFastMemberLinkHisList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberLinkHisPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        if (StrUtil.isNotEmpty(params.getColorTimeStartStr())) {
            params.setColorTimeStart(DateUtil.format09(params.getColorTimeStartStr()));
        }
        if (StrUtil.isNotEmpty(params.getColorTimeEndStr())) {
            params.setColorTimeEnd(DateUtil.format09(params.getColorTimeEndStr()));
        }
        if (Objects.isNull(params.getContentType())) {
            params.setContentType(sessionVO.getContentType());
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastMemberLinkHisExportService.exportMemberLinkHisList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        } else {
            return fastMemberLinkHisService.queryPageList(params, pageVO);
        }
    }

}









