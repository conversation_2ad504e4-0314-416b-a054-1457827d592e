/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticVar;
import com.fast.po.member.FastMemberYoungPO;
import com.fast.po.member.FastMemberYoungResetPO;
import com.fast.service.member.FastMemberYoungResetService;
import com.fast.service.member.FastMemberYoungService;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberYoung")
public class FastMemberYoungController extends BaseController {

    @Autowired
    private FastMemberYoungService fastMemberYoungService;
    @Autowired
    private FastMemberYoungResetService fastMemberYoungResetService;

    @ApiName(value = "青少年模式-查询列表", folder = {StaticFolder.FOLDER_MEMBER_MANGER})
    @ApiParamsIn({
            "id:1:int:用户id",
            "retailIds:1:str:分销商们",
            "officialIds:1:str:公众号们",
            "miniIds:1:str:小程序们"
    })
    @ApiParamsOut({
            "userName:操作员",
            "miniName:应用名称",
            "officialName:公众号名称",
            "officialType:公众号类型1微信公众号;2抖音虚拟公众号;3：快手虚拟公众号",
            "miniType:应用类型1：微信小程序;2抖音小程序;3H5;4快手",
            "retailName:分销商名称",
            "remark:备注"
    })
    @PostMapping("/getYoungList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberYoungResetPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            // 导出
            return fastMemberYoungResetService.exportList(sessionVO, params);
        } else {
            return fastMemberYoungResetService.queryPageList(params, pageVO);
        }
    }

    @ApiName(value = "青少年模式-重置密码", folder = {StaticFolder.FOLDER_MEMBER_MANGER})
    @ApiParamsIn({
            "id:1:int:用户id",
            "remark:0:str:备注"
    })
    @ApiParamsOut({
            "success:是否成功"
    })
    @RequestMapping(value = "/resetYoungPassword")
    public ResultVO<?> resetYoungPassword(HttpServletRequest request, FastMemberYoungPO params) {
        if (!biggerZero(params.getId())) {
            return ResultVO.error("用户id不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        return fastMemberYoungService.resetYoungPassword(params, sessionVO);
    }

}
