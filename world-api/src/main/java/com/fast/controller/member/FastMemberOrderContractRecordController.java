/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.member.FastMemberOrderContractRecordPO;
import com.fast.service.member.FastMemberOrderContractRecordService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
public class FastMemberOrderContractRecordController extends BaseController {

    @Autowired
    private FastMemberOrderContractRecordService fastMemberOrderContractRecordService;

    @ApiParamsIn({
            "contractOrderId:0:str:签约单号",
            "dramaIds:0:str:短剧数字id(多个逗号分隔)",
            "linkIds:0:str:推广链接ids",
            "memberId:0:int:用户id-数字",
            "modelDetailName:0:str:签约模版名称",
            "contractTemplateIds:0:str:签约模板ids",
            "validUnit:0:str:扣款周期",
            "contractStates:0:str:签约状态",
            "advMediaIds:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "linkTypes:0:str:链接类型s",
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "miniIds:0:str:小程序id-数字(多个逗号分隔)",
            "officialIds:0:str:公众号id-数字(多个逗号分隔)",
            "advUserIds:0:str:优化师",
            "modelDetailName:0:str:签约模版名称",
            "linkSubTypes:0:str:挂载链接类型 挂载链接名称",
            "subLinkId:0:str:挂载链接id",
            "linkTimeS:0:str:染色开始时间",
            "linkTimeE:0:str:染色结束时间",
            "payTimeS:0:str:首次支付 开始",
            "payTimeS:0:str:首次支付 结束",
            "lastPayTimeS:0:str:最近一次支付 开始",
            "lastPayTimeE:0:str:最近一次支付 结束",
            "seriesNum:0:str:充值剧集数",
            "projectIds:0:str:项目ids",
            "feeFlag:0:str:付费标志:1=付费;2=免费",
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "contractOrderId:签约单号",
            "modelDetailName:模版名称",
            "contractTemplateId:签约模板id",
            "validUnit:扣款周期单位:1=周;2=月;3=年;4=日;5季",
            "moneyRecharge:首次支付金额",
            "contractMoneyRecharge:次月金额",
            "sumMoneyRecharge:总支付金额",
            "payTime:首次支付时间",
            "execCount:扣款执行期数",
            "successCount:扣款成功次数",
            "lastPayTime:最近一次扣款时间",
            "nextPayTime:下一次扣款时间",
            "contractState:签约状态0待签约 1签约中 2已解约",
            "remark:签约备注（失败原因）",
            "memberId:用户id-数字",
            "linkTime:染色时间",
            "miniName:小程序名称",
            "dramaName:短剧名称",
            "seriesNum:付费剧集",
            "payType:支付方式",
            "subLinkId:次归属链接",
            "linkType:链接类型",
            "feeFlag:链接类型 付费免费",
            "miniType:小程序类型",
    })
    @ApiName(value = "member-签约记录", folder = {"member"})
    @PostMapping("fastMemberOrderContractRecord/list")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberOrderContractRecordPO params, PageVO pageVO) {

        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (params.getExportData() == 1) {
            return fastMemberOrderContractRecordService.exportList(sessionVO, params);
        }
        return fastMemberOrderContractRecordService.queryPageList(params, pageVO);
    }
}
