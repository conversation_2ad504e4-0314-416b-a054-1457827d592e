/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.member.FastMemberDeletePO;
import com.fast.service.member.FastMemberDeleteService;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberDelete")
public class FastMemberDeleteController extends BaseController {

    @Autowired
    private FastMemberDeleteService fastMemberDeleteService;

    @ApiName(value = "member-用户清除查询列表", folder = {"member"})
    @ApiParamsIn({
            "id:1:int:用户id",
            "retailIds:1:str:分销商们",
            "officialIds:1:str:公众号们",
            "miniIds:1:str:小程序们"
    })
    @ApiParamsOut({
            "userName:操作员",
            "miniName:应用名称",
            "officialName:公众号名称",
            "officialType:公众号类型1微信公众号;2抖音虚拟公众号;3：快手虚拟公众号",
            "miniType:应用类型1：微信小程序;2抖音小程序;3H5;4快手",
            "retailName:分销商名称",
            "remark:备注"
    })
    @PostMapping("/getFastMemberDeleteList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberDeletePO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            // 导出
            return fastMemberDeleteService.exportList(sessionVO, params);
        } else {
            return fastMemberDeleteService.queryPageList(params, pageVO);
        }
    }


    @ApiName(value = "member-查询单个详情", folder = {"member"})
    @PostMapping("/getFastMemberDeleteDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberDeletePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberDeletePO fastMemberDelete = fastMemberDeleteService.queryById(params);
        return ResultVO.success(fastMemberDelete);
    }

    @ApiName(value = "member-用户清除添加", folder = {"member"})
    @ApiParamsIn({
            "id:1:str:用户id",
            "remark:1:str:备注信息"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insertFastMemberDelete")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberDeletePO params) {
        if (params.getId() == null) {
            return ResultVO.error("用户id不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastMemberDeleteService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
