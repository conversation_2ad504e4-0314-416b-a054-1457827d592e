/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.member.FastMemberOrderConsumePO;
import com.fast.po.member.FastMemberPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.drama.FastDramaService;
import com.fast.service.member.FastMemberOrderConsumeExportService;
import com.fast.service.member.FastMemberOrderConsumeService;
import com.fast.service.member.FastMemberService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 用户消费记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMember")
public class FastMemberOrderConsumeController extends BaseController {

    @Autowired
    private FastMemberOrderConsumeService consumeService;
    @Autowired
    private FastMemberOrderConsumeExportService exportService;
    @Autowired
    private FastMemberService memberService;
    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastLinkService linkService;

    /**
     * 用户消费-查询列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "用户消费-查询列表", folder = {StaticFolder.FOLDER_CONSUME})
    @ApiParamsIn({
            "memberId:0:int:用户id-数字",
            "memberNameLike:0:str:昵称",
            "linkId:0:str:链接id",
            "linkNameLike:0:str:链接名称",
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "miniIds:0:str:小程序id-数字(多个逗号分隔)",
            "officialIds:0:str:公众号id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧数字id(多个逗号分隔)",
            "consumeTimeStr:0:str:消费时间：yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "memberId:用户id",
            "memberName:昵称",
            "dramaName:短剧名称",
            "seriesNum:剧集号",
            "linkId:链接id",
            "linkName:链接名称",
            "createTime:消费时间",
            "registerTime:注册时间",
            "coinConsume:消费金币",
            "officialName:公众号名称",
            "miniName:小程序名称",
            "retailName:分销商名称",
    })
    @Slave
    @RequestMapping(value = "/getMemberConsumeList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberOrderConsumePO params, PageVO pageVO) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds(), params.getMemberIds());
        SessionVO sessionVO = getSessionVO(request);
        // 处理时间
        if (notBlank(params.getConsumeTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getConsumeTimeStr());
            params.setConsumeTimeS(date.get(0));
            params.setConsumeTimeE(date.get(1));
        }
        if (notBlank(params.getMemberNameLike())) {
            FastMemberPO query = new FastMemberPO();
            query.setMemberNameLike(params.getMemberNameLike());
            query.setRetailId(params.getRetailId());
            List<Long> ids = memberService.queryMemberIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setMemberIds(StrUtil.joinNoRepeat(ids));
        }
        if (notBlank(params.getDramaNameLike())) {
            FastDramaPO query = new FastDramaPO();
            query.setDramaNameLike(params.getDramaNameLike());
            List<Integer> ids = dramaService.queryDramaIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setDramaIds(StrUtil.joinNoRepeat(ids));
        }
        if (notBlank(params.getLinkNameLike())) {
            FastLinkPO query = new FastLinkPO();
            query.setLinkNameLike(params.getLinkNameLike());
            query.setRetailId(params.getRetailId());
            Set<Integer> ids = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setLinkIds(StrUtil.join(ids));
        }
        params.setContentType(sessionVO.getContentType());
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = exportService.exportMemberConsumeList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        } else {
            return consumeService.queryPageList(params, pageVO);
        }
    }

    @ApiName(value = "用户消费-查询单个详情", folder = {StaticFolder.FOLDER_CONSUME})
    @RequestMapping(value = "/getMemberConsumeDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberOrderConsumePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberOrderConsumePO fastMemberConsume = consumeService.queryById(params);
        return ResultVO.success(fastMemberConsume);
    }
}
