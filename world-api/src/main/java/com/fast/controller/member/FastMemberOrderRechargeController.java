/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.drama.FastDramaSeriesMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.drama.FastDramaSeriesPO;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.drama.FastDramaService;
import com.fast.service.fee.FastFeeModelDetailService;
import com.fast.service.member.FastMemberOrderRechargeService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.upay.UpayPayService;
import com.fast.utils.*;
import com.fast.utils.http.HttpUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * 用户充值订单记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMember")
public class FastMemberOrderRechargeController extends BaseController {

    @Autowired
    private FastDramaSeriesMapper fastDramaSeriesMapper;
    @Autowired
    private FastMemberOrderRechargeService rechargeService;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastFeeModelDetailService modelDetailService;
    @Autowired
    private UpayPayService upayPayService;

    /**
     * 用户充值订单-查询列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "用户充值订单-查询列表", folder = {StaticFolder.FOLDER_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "miniIds:0:str:小程序id-数字(多个逗号分隔)",
            "officialIds:0:str:公众号id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师id-数字(多个逗号分隔)",
            "memberId:0:int:用户id-数字",
            "dramaNameLike:0:str:短剧名称",
            "linkId:0:str:链接id",
            "linkNameLike:0:str:链接名称",
            "linkEntryPage:0:str:入口页面",
            "subLinkId:0:int:次归属渠道id",
            "linkSubType:0:int:次归属类型",
            "outTransId:0:str:商户交易单号",
            "termOrdId:0:str:平台订单号",
            "modelDetailName:0:str:商品名称",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
            "registerTimeStr:0:str:注册时间(第一次)：yyyy-MM-dd - yyyy-MM-dd",
            "orderType:0:int:订单类型：1=充值金币;2=充值VIP",
            "orderTypes:0:str:订单类型：1=充值金币;2=充值VIP(支持多选,【,】拼接)",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "advVersion:1:int:头条版本：1=1.0;2=2.0",
            "state:0:int:支付状态：1=已支付;2=已取消",
            "payForm:0:int:支付类型：1=普通支付;8=虚拟支付",
            "paySequenceFlag:0:int:首充复充1首充，2复充",
            "scene:0:str:场景值",
            "wxFinderName:0:str:微信视频号名称",
            "seriesNum:0:int:付费剧集",
            "dramaPerformType:1短剧2漫剧",
            "seriesNum:0:int:付费剧集",
            "wxFinderIds:0:str:视频号归属人Id",
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "outTransId:商户交易单号",
            "modelDetailName:商品名称",
            "termOrdId:平台订单号",
            "memberId:用户id-数字",
            "moneyRecharge:充值金额",
            "moneyProfit:实际到账金额",
            "payFeeRate:通道费率(不含%)",
            "paySequence:当前订单是第N笔支付成功的",
            "orderType:订单类型：1=充值金币;2=充值VIP",
            "state:支付状态：0=待支付;1=已支付;2=已取消;3=已退款",
            "phoneOs:手机系统::1安卓，2爱疯",
            "payTime:交易时间",
            "registerTime:注册时间",
            "linkId:链接id",
            "linkName:链接名称",
            "linkEntryPage:入口页面",
            "linkTime:链接染色时间",
            "dramaName:短剧名称",
            "officialName:公众号名称",
            "miniName:小程序名称",
            "retailName:分销商名称",
            "backState:回传状态：1-未回传 2-回传成功 3-回传失败",
            "backInfo:回传原因",
            "scene:场景值",
            "seriesNum:付费剧集",
            "sceneContent:场景内容",
            "diamond:抖音钻石支付数量",
            "contractNum:签约期数",
            "contractTemplateId:签约模版id",
            "contractOrder_id:签约单号",
            "wxDramaId:微信视频ID(内容ID)",
            "wxPromotionId:微信视频号加热订单ID",
            "wxFinderId:微信视频号ID",
            "wxFinderName:微信视频号名称",
            "results>>summary>>rechargeCount:充值交易笔数",
            "results>>summary>>moneyRecharge:充值交易金额",
            "results>>summary>>moneyProfit:实际到账金额",
            "results>>summary>>rechargeMemberCount:充值交易人数",
            "finderUserName:视频号归属人",
    })
    @Slave
    @RequestMapping(value = "/getMemberRechargeList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberOrderRechargePO params, PageVO pageVO) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds(), params.getOrderTypes());
        params.setScenes(StrUtil.resetInt(params.getScenes()));
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        // 处理商品名称搜索
        if (notEmpty(params.getModelDetailName())) {
            if (params.getModelDetailName().endsWith("元充值")) {
                try {
                    BigDecimal money = new BigDecimal(params.getModelDetailName().replace("元充值", StaticVar.EMPTY));
                    params.setMoneyRecharge(money);
                    params.setModelDetailName(null);
                } catch (Exception ignored) {
                }
            }
        }
        // 处理时间
        if (notEmpty(params.getPayTimeStr())) {
            List<Date> date = DateUtil.getStartEndDateTime(params.getPayTimeStr());
            params.setPayTimeS(date.get(0));
            params.setPayTimeE(date.get(1));
        }
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDateTime(params.getRegisterTimeStr());
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));
        }
        if (notEmpty(params.getLinkTimeStr())) {
            List<Date> date = DateUtil.getStartEndDateTime(params.getLinkTimeStr());
            params.setLinkTimeS(date.get(0));
            params.setLinkTimeE(date.get(1));
        }
        if (notEmpty(params.getDramaNameLike())) {
            FastDramaPO query = new FastDramaPO();
            query.setDramaNameLike(params.getDramaNameLike());
            List<Integer> ids = dramaService.queryDramaIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setDramaIds(StrUtil.joinNoRepeat(ids));
        }
        Set<Integer> linkIds = new HashSet<>();
        if (notEmpty(params.getLinkNameLike())) {
            FastLinkPO query = new FastLinkPO();
            query.setLinkNameLike(params.getLinkNameLike());
            query.setRetailId(params.getRetailId());
            Set<Integer> ids = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            linkIds.addAll(ids);
            params.setLinkIds(StrUtil.join(linkIds));
        }
        // 次归属链接名称模糊搜索
        if (notEmpty(params.getSubLinkName())) {
            FastLinkPO query = new FastLinkPO();
            query.setLinkNameLike(params.getSubLinkName());
            query.setRetailId(params.getRetailId());
            Set<Integer> ids = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setSubLinkIds(StrUtil.join(ids));
        }
        if (notEmpty(params.getModelDetailName())) {
            FastFeeModelDetailPO query = new FastFeeModelDetailPO();
            query.setTitleLike(params.getModelDetailName());
            List<Integer> ids = modelDetailService.queryModelDetailIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setModelDetailIds(StrUtil.joinNoRepeat(ids));
        }
        // if (params.getState() == null) {
        //     params.setStates("1,2");
        // }

        // 微信视频号相关搜索
        params.setWxFinderIds(StrUtil.joinStr(params.getWxFinderIds()));
        if (notEmpty(params.getWxDramaIdLike()) || notEmpty(params.getWxFinderId()) || notEmpty(params.getWxFinderIds()) || notEmpty(params.getWxPromotionId())) {
            params.setWxFinderFlag(1);
        }

        // 入口页面搜索
        if (notEmpty(params.getLinkEntryPage())) {
            String linkEntryPage = params.getLinkEntryPage().trim();
            String[] linkEntryPages = linkEntryPage.split("-");
            if (linkEntryPages.length != 2) {
                return ResultVO.error("入口页面搜索关键词不合法");
            }
            String dramaName = linkEntryPages[0].trim();
            // 精确查找短剧
            FastDramaPO query = new FastDramaPO();
            query.setDramaName(dramaName);
            query.setDelFlag(StaticVar.NO);
            List<Integer> ids = dramaService.queryDramaIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            Set<Integer> linkIds2 = new HashSet<>();
            Integer dramaSerNum = toInteger(linkEntryPages[1].replace(" ", "").replace("第", "").replace("集", "").trim());
            for (Integer dramaId : ids) {
                FastDramaSeriesPO seriesPO = new FastDramaSeriesPO();
                seriesPO.setDramaId(dramaId);
                seriesPO.setSeriesNum(dramaSerNum);
                FastDramaSeriesPO dramaSeriesPO = fastDramaSeriesMapper.queryOne(seriesPO);
                if (dramaSeriesPO == null) {
                    return ResultVO.success(getDefaultPageListData());
                }
                FastLinkPO queryL = new FastLinkPO();
                queryL.setDramaId(dramaId);
                queryL.setSeriesId(dramaSeriesPO.getId());
                linkIds2.addAll(linkService.queryLinkIds(queryL));
            }
            if (CollUtil.isEmpty(linkIds2)) {
                return ResultVO.success(getDefaultPageListData());
            } else if (CollUtil.hasContent(linkIds)) {
                linkIds = CollUtil.intersection(linkIds, linkIds2);
            } else {
                linkIds.addAll(linkIds2);
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        // 优化师和链接id搜索
        if (notEmpty(params.getAdvUserIds()) || notEmpty(params.getLinkIds())) {
            FastLinkPO query = new FastLinkPO();
            query.setAdvUserIds(params.getAdvUserIds());
            query.setIds(params.getLinkIds());
//            query.setDramaId(params.getDramaId());
//            query.setDramaIds(params.getDramaIds());
            Set<Integer> ids = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            } else if (CollUtil.hasContent(linkIds)) {
                linkIds = CollUtil.intersection(linkIds, ids);
            } else {
                linkIds.addAll(ids);
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        // 项目ids查询
        if (notEmpty(params.getCpProjectIds())) {
            FastDramaPO fastDramaPOQuery = new FastDramaPO();
            fastDramaPOQuery.setProjectIds(params.getCpProjectIds());
            List<Integer> integers = dramaService.queryDramaIds(fastDramaPOQuery);
            if (CollUtil.isEmpty(integers)) {
                return ResultVO.success(getDefaultPageListData());
            }
            StrUtil.joinList(params.getDramaIds(), integers);
        }
        if (params.getExportData() == 1) {
            ResultVO<?> res = exportPre(sessionVO, StaticVar.EXPORT_RECHARGE_LIST);
            if (res.isSuccess()) {
                params.setExportKey(res.getMessage());
                Map<String, String> headers = new HashMap<>();
                headers.put("access_token", ComUtil.getAccessToken(request));
                HttpUtil.postWithHeader(StaticVar.EXPORT_INTERNAL_URL + "fastExport/exportMemberRechargeList", JsonUtil.toString(params), headers);
                return ResultVO.success();
            } else {
                return res;
            }
        } else {
            return rechargeService.queryPageList(params, pageVO);
        }
    }

    /**
     * 用户充值订单-查询单个详情
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "用户充值订单-查询单个详情", folder = {StaticFolder.FOLDER_ORDER})
    @ApiParamsIn({"encryptionId:1:str:短剧加密id"})
    @ApiParamsOut({
            "encryptionId:加密id",
    })
    @RequestMapping(value = "/getMemberRechargeDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberOrderRechargePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberOrderRechargePO fastMemberRecharge = rechargeService.queryById(params);
        return ResultVO.success(fastMemberRecharge);
    }

}
