/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticVar;
import com.fast.po.member.FastMemberPO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.member.FastMemberRecentLogExportService;
import com.fast.service.member.FastMemberRecentLogService;
import com.fast.service.member.FastMemberService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 观看记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMember")
public class FastMemberRecentLogController extends BaseController {

    @Autowired
    private FastMemberRecentLogService recentLogService;
    @Autowired
    private FastMemberService memberService;
    @Autowired
    private FastMemberRecentLogExportService exportService;
    @Autowired
    private FastLinkService linkService;

    /**
     * 观看记录-查询列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "观看记录-查询列表", folder = {StaticFolder.FOLDER_MEMBER_MANGER})
    @ApiParamsIn({
            "memberId:0:int:用户id-数字",
            "memberNameLike:0:str:昵称",
            "linkId:0:int:链接id",
            "retailId:0:int:分销商id",
            "linkNameLike:0:str:链接名称",
            "seriesNum:0:int:剧集号(第N集).数字",
            "dramaIds:0:str:短剧数字id(多个逗号分隔)",
            "miniIds:0:str:小程序-数字(多个逗号分隔)",
            "payRule:0:int:付费规则::1=付费;0=免费",
            "createTimeStr:0:str:观看时间：yyyy-MM-dd - yyyy-MM-dd",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "id:用户id",
            "encryptionId:加密id",
            "memberName:昵称",
            "dramaName:短剧名称",
            "miniName:小程序名称",
            "seriesNum:剧集号",
            "coinConsume:金币消费数",
            "payRule:付费规则:: 1=付费;0=免费",
            "payResult:付费情况:: 0=免费;1=K币消费;2=vip免费看;3=解锁后重复看",
            "createTime:观看时间",
            "registerTime:注册时间",
            "linkName:链接名称",
            "linkId:链接id",
    })
    @Slave
    @RequestMapping(value = "/getRecentLogList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMemberRecentLogPO params, PageVO pageVO) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getMiniIds(), params.getDramaIds(), params.getMemberIds());
        SessionVO sessionVO = getSessionVO(request);
        Set<Long> memberIds = new HashSet<>();
        if (notBlank(params.getMemberNameLike())) {
            FastMemberPO query = new FastMemberPO();
            query.setMemberNameLike(params.getMemberNameLike());
            query.setRetailId(params.getRetailId());
            List<Long> ids = memberService.queryMemberIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            memberIds.addAll(ids);
            params.setMemberIds(StrUtil.join(memberIds));
        }
        // 处理时间
        if (notBlank(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));
        }
        // 处理时间
        if (notBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        if (notBlank(params.getLinkNameLike())) {
            FastLinkPO query = new FastLinkPO();
            query.setLinkNameLike(params.getLinkNameLike());
            query.setRetailId(params.getRetailId());
            Set<Integer> ids = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setLinkIds(StrUtil.join(ids));
        }
        params.setContentType(sessionVO.getContentType());
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            // 判断导出次数是否超限
            String key2 = StaticVar.EXPORT_RECENT_LOG_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
            Integer value2 = RedisUtil.getInteger(key2);
            if (value2 != null && value2 > StaticVar.EXPORT_NUMBER) {
                return ResultVO.error(StaticMsg.MSG_ERROR_29);
            }
            exportService.exportMemberRecentLogList(sessionVO, params, key2);
            return ResultVO.success();
        } else {
            return recentLogService.queryPageList(params, pageVO);
        }
    }

}
