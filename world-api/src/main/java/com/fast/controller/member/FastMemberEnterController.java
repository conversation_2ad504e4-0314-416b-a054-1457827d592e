/*
 * Powered By fast.up
 */
package com.fast.controller.member;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.member.FastMemberEnterPO;
import com.fast.service.member.FastMemberEnterService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberEnter")
public class FastMemberEnterController extends BaseController {

    @Autowired
    private FastMemberEnterService fastMemberEnterService;

    @ApiName(value = "member-查询列表", folder = {"member"})
    @PostMapping("/getFastMemberEnterList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberEnterPO params, PageVO pageVO) {
        return fastMemberEnterService.queryPageList(params, pageVO);
    }

    @ApiName(value = "member-查询单个详情", folder = {"member"})
    @PostMapping("/getFastMemberEnterDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberEnterPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberEnterPO fastMemberEnter = fastMemberEnterService.queryById(params);
        return ResultVO.success(fastMemberEnter);
    }

    @ApiName(value = "member-添加", folder = {"member"})
    @PostMapping("/insertFastMemberEnter")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberEnterPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberEnterService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "member-更新", folder = {"member"})
    @PostMapping("/updateFastMemberEnter")
    public ResultVO<?> update(HttpServletRequest request, FastMemberEnterPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberEnterService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
