/*
 * Powered By fast.up
 */
package com.fast.controller.h5;

import com.fast.base.BaseController;
import com.fast.po.h5.FastIpUaPO;
import com.fast.service.h5.FastIpUaService;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/nologin")
public class FastIpUaController extends BaseController {

    @Autowired
    private FastIpUaService fastIpUaService;

    /**
     * 中间落地页，上传ip、ua、clickId、linkId等信息
     *
     * @param request
     * @param params
     * @return
     */
    @RequestMapping(value = "/recordipua")
    public ResultVO<?> recordipua(HttpServletRequest request, FastIpUaPO params) {
        params.setIp(request.getRemoteAddr());
        params.setUa(request.getHeader("User-Agent"));
        String sb = "recordipua:" + "ip=" +
                params.getIp() +
                ",ua=" +
                params.getUa() +
                ",cid=" +
                params.getCid() +
                ",aid=" +
                params.getAid() +
                ",ctype=" +
                params.getCtype() +
                ",os=" +
                params.getOs() +
                ",imei=" +
                params.getImei() +
                ",linkId=" +
                params.getLinkId() +
                ",clickId=" +
                params.getClickId();
        log.info(sb);
        MethodVO methodVO = fastIpUaService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
