/*
 * Powered By fast.up
 */
package com.fast.controller.log;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.po.log.FastLogVisitPO;
import com.fast.service.log.FastLogVisitService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/log")
public class FastLogVisitController extends BaseController {

    @Autowired
    private FastLogVisitService fastLogVisitService;

    @ApiName(value = "log-查询列表", folder = {"log"})
    @PostMapping("/getFastLogVisitList")
    public ResultVO<?> getList(HttpServletRequest request, FastLogVisitPO params, PageVO pageVO) {
        return fastLogVisitService.queryPageList(params, pageVO);
    }


}
