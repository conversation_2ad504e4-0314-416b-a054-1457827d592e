/*
 * Powered By fast.up
 */
package com.fast.controller.pop;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.pop.FastPopMessagePO;
import com.fast.service.pop.FastPopMessageService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/pop")
public class FastPopMessageController extends BaseController {

    @Autowired
    private FastPopMessageService fastPopMessageService;


    @ApiName(value = "pop-判断是否需要弹框提示", folder = {"pop"})
    @ApiParamsIn({
            "params:1:int:无需参数"
    })
    @ApiParamsOut({
            "popFlag:0不需要弹，1需要弹",
            "popMessageCount:待处理数量"
    })
    @PostMapping("/getPopMessage")
    public ResultVO<?> getPopMessage(HttpServletRequest request, FastPopMessagePO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        return fastPopMessageService.getPopMessage(sessionVO);
    }

    @ApiName(value = "pop-查询列表", folder = {"pop"})
    @ApiParamsIn({
            "type:1:int:类型1异常",
            "handleFlag:1:int:处理标志1已处理0未处理"
    })
    @ApiParamsOut({
            "title:标题",
            "content:内容",
            "type:类型1异常",
            "handleFlag:处理标志0未处理1已处理"
    })
    @PostMapping("/getFastPopMessageList")
    public ResultVO<?> getList(HttpServletRequest request, FastPopMessagePO params, PageVO pageVO) {
        return fastPopMessageService.queryPageList(params, pageVO);
    }

    @ApiName(value = "pop-查询单个详情", folder = {"pop"})
    @PostMapping("/getFastPopMessageDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastPopMessagePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastPopMessagePO fastPopMessage = fastPopMessageService.queryById(params);
        return ResultVO.success(fastPopMessage);
    }

    @ApiName(value = "pop-添加", folder = {"pop"})
    @PostMapping("/insertFastPopMessage")
    public ResultVO<?> insert(HttpServletRequest request, FastPopMessagePO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastPopMessageService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "pop-更新-标记处理", folder = {"pop"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "handleFlag:1:int:处理标志0否1是"
    })
    @PostMapping("/updateHandleFlag")
    public ResultVO<?> updateHandleFlag(HttpServletRequest request, FastPopMessagePO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        if (params.getHandleFlag() == null) {
            return ResultVO.error("处理标志不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        params.setHandleId(sessionVO.getUserId());
        params.setHandleTime(DateUtil.getNowDate());
        MethodVO methodVO = fastPopMessageService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
