/*
 * Powered By fast.up
 */
package com.fast.controller.order;

import com.alibaba.fastjson.JSONObject;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.po.order.FastMemberOrderRefundPO;
import com.fast.service.order.FastMemberOrderRefundService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberOrderRefund")
public class FastMemberOrderRefundController extends BaseController {

    @Autowired
    private FastMemberOrderRefundService fastMemberOrderRefundService;

    @ApiName(value = "order-退款自动回显", folder = {"order"})
    @ApiParamsIn({"outTransId:1:str:交易单号"})
    @ApiParamsOut({
            "detail:订单详情"
    })
    @PostMapping("/getOrderDetail4Refund")
    public ResultVO<?> getOrderDetail4Refund(HttpServletRequest request, FastMemberOrderRefundPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (StrUtil.isEmpty(params.getOutTransId())) {
            return ResultVO.error("交易订单id不能为空");
        }
        return fastMemberOrderRefundService.queryOrderDetailRefund(params);
    }

    @ApiName(value = "order-退款添加", folder = {"order"})
    @ApiParamsIn({
            "moneyRefund:1:str:退费金额",
            "refundByUrl:1:int:远程退款0否1抖音接口2快手接口",
            "orderId:1:str:订单id",
            "remark:1:str:备注"
    })
    @ApiParamsOut({"results:ok"})
    @PostMapping("/insertFastMemberOrderRefund")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberOrderRefundPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setContentType(sessionVO.getContentType());
        MethodVO methodVO = fastMemberOrderRefundService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "order-退款查询列表", folder = {"order"})
    @ApiParamsIn({
            "outTransId:1:str:交易订单号",
            "state:1:int:0失败1成功2退款中",
            "memberId:1:str:用户id",
            "aid:1:str:计划id",
            "promotionId:1:str:广告id",
            "orderType:1:str:充值类型",
            "linkId:1:str:推广链接",
            "retailIds:1:str:分销商",
            "miniIds:1:str:应用",
            "officialIds:1:str:公众号",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "refundStartStr:1:str:开始时间yyyy-MM-DD",
            "refundEndStr:1:str:结束时间"
    })
    @PostMapping("/getFastMemberOrderRefundList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberOrderRefundPO params, PageVO pageVO) {
        if (StrUtil.isNotEmpty(params.getRefundStartStr())) {
            params.setRefundStart(DateUtil.beginOfDay(DateUtil.format09(params.getRefundStartStr())));
        }
        if (StrUtil.isNotEmpty(params.getRefundEndStr())) {
            params.setRefundEnd(DateUtil.endOfDay(DateUtil.format09(params.getRefundEndStr())));
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() != null && sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
            if (biggerZero(sessionVO.getOfficialId())) {
                params.setOfficialId(sessionVO.getOfficialId());
            }
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            return fastMemberOrderRefundService.exportList(sessionVO, params);
        } else {
            return fastMemberOrderRefundService.queryPageList(params, pageVO);
        }
    }

    @RequestMapping("nologin/ttRefundCallBack")
    public JSONObject ttRefundCallBack(@RequestBody JSONObject params) {
        log.info("收到抖音退款回调");
        try {
            if (!"refund".equalsIgnoreCase(params.getString("type"))) {
                log.error("tt refund error:{}", params);
                return null;
            }
            return fastMemberOrderRefundService.ttRefundCallBack(params, 2);
        } catch (Exception e) {
            MyException.print(log, e);
        }
        return null;
    }

    @RequestMapping("nologin/ksRefundCallBack")
    public JSONObject ksRefundCallBack(HttpServletRequest request, @RequestBody JSONObject bodyObj) {
        actionLogService.log("kuaishou_refund_callback", "body:" + bodyObj.toJSONString());
        String signStr = request.getHeader("kwaisign");
        actionLogService.log("kuaishou_refund_callback", "sign:" + signStr);
        // 处理业务
        return fastMemberOrderRefundService.ksRefundCallBack(bodyObj);
    }

}
