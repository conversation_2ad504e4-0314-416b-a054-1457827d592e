/*
 * Powered By fast.up
 */
package com.fast.controller.monitor;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.po.monitor.FastMonitorInterfacePO;
import com.fast.service.monitor.FastMonitorInterfaceService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor")
public class FastMonitorInterfaceController extends BaseController {

    @Autowired
    private FastMonitorInterfaceService fastMonitorInterfaceService;

    @ApiName(value = "monitor-查询列表", folder = {"monitor"})
    @PostMapping("/getFastMonitorInterfaceList")
    public ResultVO<?> getList(HttpServletRequest request, FastMonitorInterfacePO params, PageVO pageVO) {
        return ResultVO.success();
    }


}
