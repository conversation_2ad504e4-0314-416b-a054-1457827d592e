/*
 * Powered By fast.up
 */
package com.fast.controller.monitor;

import com.alibaba.fastjson.JSONObject;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticVar;
import com.fast.mapper.common.FastJobSummaryMapper;
import com.fast.po.common.FastJobSummaryPO;
import com.fast.service.monitor.FastMonitorService;
import com.fast.service.monitor.MonitorService;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.aliyun.AliBpsVO;
import com.fast.vo.common.TimeInDto;
import com.fast.vo.monitor.MonitorDataVO;
import com.fast.vo.monitor.MonitorSpeedVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数据监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor")
public class MonitorController extends BaseController {

    @Autowired
    private MonitorService monitorService;
    @Autowired
    private FastJobSummaryMapper fastJobSummaryMapper;
    @Autowired
    private FastMonitorService fastMonitorService;

    @Slave
    @ApiName(value = "用户/订单监控", folder = {StaticFolder.FOLDER_MONITOR})
    @ApiParamsIn({
            "version:0:str:小程序版本"
    })
    @ApiParamsOut({
            "minitorList 》miniName:小程序名称",
            "minitorList 》speedAndroidUser:安卓用户速度",
            "minitorList 》speedAndroidOrder:安卓订单速度",
            "minitorList 》speedAndroidBack:安卓回传速度",
            "minitorList 》speedAndroidUserAlarm:安卓用户告警状态",
            "minitorList 》speedAndroidOrderAlarm:安卓订单告警状态",
            "minitorList 》speedAndroidBackAlarm:安卓回传告警状态",
            "minitorList 》speedIosUser:苹果用户速度",
            "minitorList 》speedIosOrder:苹果订单速度",
            "minitorList 》speedIosBack:苹果回传速度",
            "minitorList 》speedIosUserAlarm:苹果用户告警状态",
            "minitorList 》speedIosOrderAlarm:苹果订单告警状态",
            "minitorList 》speedIosBackAlarm:苹果回传告警状态"
    })
    @RequestMapping(value = "/nologin/getDataSpeed", method = {RequestMethod.POST})
    public ResultVO<?> getDataSpeed(HttpServletRequest request, MonitorSpeedVO params) {
        if (params.getContentType() == null) {
            params.setContentType(1);
        }
        return monitorService.getSpeedData(params);
    }

    /**
     * 应用数据监控
     */
    @ApiName(value = "用户管理-查询列表", folder = {StaticFolder.FOLDER_MONITOR})
    @ApiParamsIn({
            "miniIds:0:str:小程序id-数字(多个逗号分隔)",
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "timeIn:1:int:-1自定义日期;1今天;5最近;7天;6最近15天;20本月",
            "createTimeStr:0:str:数据时间：yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "list>>miniId:小程序id",
            "list>>miniName:小程序名称",
            "list>>memberNum:新增公众号用户数",
            "list>>memberMiniNum:新增小程序用户数",
            "list>>orderNum:新增充值笔数",
            "list>>orderMoney:新增充值金额",
            "list>>newOrderNum:新增用户充值人数",
            "list>>urlMakeCount:小程序URL生成次数",
            "list1>>miniId:小程序id",
            "list1>>miniName:小程序名称",
            "list1>>memberNum:新增公众号用户数",
            "list1>>memberMiniNum:新增小程序用户数",
            "list1>>orderNum:新增充值笔数",
            "list1>>orderMoney:新增充值金额",
            "list1>>newOrderNum:新增用户充值人数",
            "list1>>urlMakeCount:小程序URL生成次数",
    })
    @Slave
    @RequestMapping(value = "/getAppData", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, MonitorDataVO params, TimeInDto dto) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getMiniIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        if (params.getContentType() == null && sessionVO.getContentType() != 99) {
            params.setContentType(sessionVO.getContentType());
        }
        if (dto.getTimeIn() == null) {
            dto.setTimeIn(1);
        }
        if (dto.getTimeIn() > -1) {
            doTimeIn(dto);
            params.setCreateTimeS(dto.getStartTime());
            params.setCreateTimeE(dto.getEndTime());
        } else {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        List<MonitorDataVO> list = monitorService.getAppData(params);
        List<MonitorDataVO> list1 = new ArrayList<>();
        // 判断是否是今日
        int s = DateUtil.format06Int(params.getCreateTimeS());
        int now = DateUtil.getNowTime06();
        if (s == now) {
            params.setCreateTimeS(DateUtil.addDays(params.getCreateTimeS(), -1));
            params.setCreateTimeE(DateUtil.addDays(params.getCreateTimeE(), -1));
            list1 = monitorService.getAppData(params);
        }
        JSONObject data = new JSONObject();
        data.put("list", list);
        data.put("list1", list1);
        return ResultVO.success(data);
    }

    /**
     * 获取在线用户数量
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/nologin/getBps", method = {RequestMethod.POST})
    public ResultVO<?> getBps(HttpServletRequest request) {
        String key = StaticVar.ALI_BPS;
        String bpsStr = RedisUtil.get(key);
        if (StrUtil.notEmpty(bpsStr)) {
            AliBpsVO bpsVO = JsonUtil.toJavaObject(bpsStr, AliBpsVO.class);
            BigDecimal bps = bpsVO.getBps();
            BigDecimal bpsG = DoubleUtil.divB(bps, BigDecimal.valueOf(1024L));
            ResultVO<Object> vo = ResultVO.success(bpsG + "G");
            JSONObject json = new JSONObject()
                    .fluentPut("onlineNum", RedisUtil.zcard(StaticVar.ONLINE_MEMBER_KEY))
                    .fluentPut("onlineTime", StaticVar.ONLINE_TIME)
                    .fluentPut("bps", bpsG + "G");
            vo.setResults(json);
            return vo;
        } else {
            ResultVO<Object> vo = ResultVO.success("0");
            JSONObject json = new JSONObject()
                    .fluentPut("onlineNum", RedisUtil.zcard(StaticVar.ONLINE_MEMBER_KEY))
                    .fluentPut("onlineTime", StaticVar.ONLINE_TIME)
                    .fluentPut("bps", "0G");
            vo.setResults(json);
            return vo;
        }
    }

    @RequestMapping(value = {"/getJobList", "/nologin/getJobList"})
    public ResultVO<?> getJobList(HttpServletRequest request) {
        List<FastJobSummaryPO> jsList = fastJobSummaryMapper.queryList(null);
        return ResultVO.success(jsList);
    }

    @RequestMapping(value = {"/resetJobState", "/nologin/resetJobState"})
    public ResultVO<?> resetJobState(HttpServletRequest request, String jobCode) {
        FastJobSummaryPO jsParam = new FastJobSummaryPO();
        jsParam.setJobCode(jobCode);
        fastJobSummaryMapper.delByJobCode(jsParam);
        return ResultVO.success();
    }

    @RequestMapping(value = {"/nologin/bigScreen"})
    public void bigScreen(HttpServletRequest request, HttpServletResponse response, String code) {
        SessionVO sessionVO = getSessionVO(request);
        fastMonitorService.bigScreen(request, response, sessionVO);
    }

}












