/*
 * Powered By fast.up
 */
package com.fast.controller.star;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.star.FastStarPO;
import com.fast.service.star.FastStarService;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/star")
public class FastStarController extends BaseController {

    @Autowired
    private FastStarService fastStarService;

    @ApiName(value = "星图服务商-查询列表", folder = {"星图"})
    @ApiParamsIn({
            "starIds:0:str:服务商ID,多个逗号分隔",
            "bindStarName:0:int:是否关联服务商0=未关联,1=已关联",
    })
    @ApiParamsOut({
            "encryptionId:str:加密ID",
            "id:int:ID",
            "starId:str:服务商ID",
            "starName:str:服务商名称",
            "bindStarName:str:是否关联服务商0=未关联,1=已关联",
    })
    @PostMapping("/getFastStarList")
    public ResultVO<?> getList(FastStarPO params, PageVO pageVO) {
        return fastStarService.queryPageList(params, pageVO);
    }

    @ApiName(value = "星图服务商-更新", folder = {"星图"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "starName:1:str:服务商名称",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/updateFastStar")
    public ResultVO<?> update(HttpServletRequest request, FastStarPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastStarService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "星图服务商-服务商下拉列表", folder = {"星图"})
    @ApiParamsIn({
            "starName:str:服务商名称",
    })
    @ApiParamsOut({
            "starIds:int:ID",
            "starName:str:服务商名称",
    })
    @PostMapping("/getFastStarSelectList")
    public ResultVO<?> getSelectList(FastStarPO params) {
        return fastStarService.querySelectList(params);
    }

    @ApiName(value = "星图服务商-服务商Id下拉列表", folder = {"星图"})
    @ApiParamsIn({
            "starIds:0:int:服务商ID",
            "starName:str:服务商名称",
    })
    @ApiParamsOut({
            "starIds:int:ID",
            "starName:str:服务商名称",
    })
    @PostMapping("/getFastStarIdSelectList")
    public ResultVO<?> getSelectIdList(FastStarPO params) {
        return fastStarService.getSelectIdList(params);
    }

}
