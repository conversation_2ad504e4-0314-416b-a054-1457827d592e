/*
 * Powered By fast.up
 */
package com.fast.controller.star;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.star.FastStarUserPO;
import com.fast.service.star.FastStarUserService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/star")
public class FastStarUserController extends BaseController {

    @Autowired
    private FastStarUserService fastStarUserService;

    @ApiName(value = "星图账号-查询列表", folder = {"星图"})
    @ApiParamsIn({
            "accountName:0:str:星图账号",
            "principalName:0:str:账号主体名称",
            "state:0:int:状态，1正常，0停用",
            "authSate:0:int:授权状态，1正常，0未授权，2失效，未授权与失效需要显示授权按钮",
            "ids:0:str:ID集合，逗号分隔",
    })
    @ApiParamsOut({
            "encryptionId:str:加密ID",
            "id:int:ID",
            "accountName:str:星图账号",
            "principalName:str:账号主体",
            "state:int:状态，1正常，0停用",
            "authSate:int:授权状态，1正常，0未授权，2失效，未授权与失效需要显示授权按钮",
            "authUrl:int:授权地址",
    })
    @PostMapping("/getFastStarUserList")
    public ResultVO<?> getList(FastStarUserPO params, PageVO pageVO) {
        return fastStarUserService.queryPageList(params, pageVO);
    }

    @ApiName(value = "星图账号-查询单个详情", folder = {"星图"})
    @ApiParamsIn({
            "encryptionId:0:str:加密id",
    })
    @ApiParamsOut({
            "encryptionId:str:加密ID",
            "id:int:ID",
            "accountName:str:星图账号",
            "principalName:str:账号主体",
    })
    @PostMapping("/getFastStarUserDetail")
    public ResultVO<?> getDetail(FastStarUserPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        FastStarUserPO fastStarUser = fastStarUserService.queryById(params);
        return ResultVO.success(fastStarUser);
    }

    @ApiName(value = "星图账号-添加", folder = {"星图"})
    @ApiParamsIn({
            "accountName:str:星图账号",
            "principalName:str:账号主体",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/insertFastStarUser")
    public ResultVO<?> insert(HttpServletRequest request, FastStarUserPO params) {
        if (isBlank(params.getAccountName())) {
            return ResultVO.error("星图账号不能为空");
        }
        if (isBlank(params.getPrincipalName())) {
            return ResultVO.error("账号主体不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastStarUserService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "星图账号-账号下拉列表", folder = {"星图"})
    @ApiParamsIn({
            "accountName:0:str:账号名称",
            "state:0:int:状态，1正常，0停用",
            "authSate:0:int:授权状态，1正常，0未授权，2失效",
    })
    @ApiParamsOut({
            "accountName:str:星图账号",
            "principalName:str:账号主体",
            "id:int:ID",
    })
    @PostMapping("/getFastStarUserListForSelect")
    public ResultVO<?> getFastStarUserListForSelect(FastStarUserPO params) {
        return fastStarUserService.getFastStarUserListForSelect(params);
    }

    @ApiName(value = "星图账号-主体下拉列表", folder = {"星图"})
    @ApiParamsIn({
            "principalName:str:账号主体",
    })
    @ApiParamsOut({
            "principalName:str:账号主体",
            "state:0:int:状态，1正常，0停用",
            "authSate:0:int:授权状态，1正常，0未授权，2失效",
    })
    @PostMapping("/getPrincipalNameList")
    public ResultVO<?> getPrincipalNameList(FastStarUserPO params) {
        return fastStarUserService.getPrincipalNameList(params);
    }

}
