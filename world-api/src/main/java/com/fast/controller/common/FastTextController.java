/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.common.FastTextPO;
import com.fast.service.common.FastTextService;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastText")
public class FastTextController extends BaseController {

    @Autowired
    private FastTextService fastTextService;

    @ApiName(value = "common-查询单个详情", folder = {"common"})
    @GetMapping("/getFastTextDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastTextPO params) {
        if (StrUtil.isEmpty(params.getCode())) {
            return ResultVO.error("code不能为空");
        }
        FastTextPO textParam = new FastTextPO();
        textParam.setCode(params.getCode());
        FastTextPO fastText = fastTextService.queryOne(textParam);
        return ResultVO.success(fastText);
    }

    @ApiName(value = "common-查询单个详情-非登录", folder = {"common"})
    @ApiParamsIn({
            "code:1:str:文本key"
    })
    @ApiParamsOut({
            "content:内容"
    })
    @GetMapping("/nologin/getFastTextDetail")
    public ResultVO<?> getFastTextDetail(FastTextPO params) {
        if (StrUtil.isEmpty(params.getCode())) {
            return ResultVO.error("code不能为空");
        }
        FastTextPO textParam = new FastTextPO();
        textParam.setCode(params.getCode());
        FastTextPO fastText = fastTextService.queryOneRedis(null, textParam);
        return ResultVO.success(fastText);
    }

    @ApiName(value = "common-查询单个详情-非登录-html形式", folder = {"common"})
    @ApiParamsIn({
            "code:1:str:文本key"
    })
    @ApiParamsOut({
            "content:内容"
    })
    @GetMapping("/nologin/text/{code}")
    public ResultVO<?> getFastTextDetailHtml(HttpServletResponse response, @PathVariable String code) throws IOException {
        if (StrUtil.isEmpty(code)) {
            return ResultVO.error("code不能为空");
        }
        response.reset();//（清空缓冲区）(1.设置此处刷新)
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html; charset=utf-8");
        PrintWriter out = response.getWriter(); //(2.使用getOutputStream(),防止和Springboot中Tomcat不一致)
        FastTextPO textParam = new FastTextPO();
        textParam.setCode(code);
        out.write(fastTextService.queryOneHtmlRedis(textParam));
        out.close();
        return null;
    }

}
