/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.common.FastVideoPO;
import com.fast.service.common.FastVideoService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 视频管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastVideo")
public class FastVideoController extends BaseController {

    @Autowired
    private FastVideoService fastVideoService;

    @ApiName(value = "视频管理-查询列表", folder = {"视频管理"})
    @ApiParamsIn({
            "videoName:1:str:视频名称",
            "videoCover:1:str:视频封面",
            "videoUrl:1:str:视频播放地址",
    })
    @ApiParamsOut({
            "results>>id:视频id",
            "results>>videoName:视频名称",
            "results>>videoCover:视频封面",
            "results>>videoUrl:视频播放地址",
    })
    @PostMapping("/getFastVideoList")
    public ResultVO<?> getList(HttpServletRequest request, FastVideoPO params, PageVO pageVO) {
        params.setDelFlag(StaticVar.NO);
        return fastVideoService.queryPageList(params, pageVO);
    }

    @ApiName(value = "视频管理-查询单个详情", folder = {"视频管理"})
    @ApiParamsIn({"encryptionId:1:str:视频加密id"})
    @ApiParamsOut({
            "results>>id:视频id",
            "results>>videoName:视频名称",
            "results>>videoCover:视频封面",
            "results>>videoUrl:视频播放地址",
    })
    @PostMapping("/getFastVideoDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastVideoPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastVideoPO fastVideo = fastVideoService.queryById(params);
        return ResultVO.success(fastVideo);
    }

    @ApiName(value = "视频管理-添加", folder = {"视频管理"})
    @ApiParamsIn({
            "videoName:1:str:视频名称",
            "videoCover:1:str:视频封面",
            "videoUrl:1:str:视频播放地址",
    })
    @ApiParamsOut({
            "results>>id:视频id",
            "results>>videoName:视频名称",
            "results>>videoCover:视频封面",
            "results>>videoUrl:视频播放地址",
    })
    @PostMapping("/insertFastVideo")
    public ResultVO<?> insert(HttpServletRequest request, FastVideoPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastVideoService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "视频管理-更新", folder = {"视频管理"})
    @ApiParamsIn({"encryptionId:1:str:视频加密id"})
    @ApiParamsOut({
            "results>>id:视频id",
            "results>>videoName:视频名称",
            "results>>videoCover:视频封面",
            "results>>videoUrl:视频播放地址",
    })
    @PostMapping("/updateFastVideo")
    public ResultVO<?> update(HttpServletRequest request, FastVideoPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastVideoService.update(params);
        RedisUtil.del(StaticVar.VIDEO_INFO_ID + id);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "视频管理-删除", folder = {"视频管理"})
    @ApiParamsIn({"encryptionId:1:str:视频加密id"})
    @ApiParamsOut({"success"})
    @PostMapping("/delFastVideo")
    public ResultVO<?> delFastVideo(HttpServletRequest request, FastVideoPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        params.setDelFlag(StaticVar.YES);
        List<String> ids = CollUtil.parseStr2List(params.getEncryptionId());
        int success = 0;
        for (String idStr : ids) {
            Integer id = decodeInt(idStr);
            if (id == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setId(id);
            MethodVO methodVO = fastVideoService.update(params);
            if (methodVO.getCode() == 0) {
                success++;
                RedisUtil.del(StaticVar.VIDEO_INFO_ID + id);
            }
        }

        if (success == 0) {
            return ResultVO.error("操作失败");
        }
        if (success != ids.size()) {
            return ResultVO.error("部分失败");
        }
        return ResultVO.success();
    }
}
