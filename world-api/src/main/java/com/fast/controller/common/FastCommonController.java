/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.alibaba.fastjson.JSON;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.RedisVar;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.member.FastMemberPO;
import com.fast.service.common.MailService;
import com.fast.service.member.FastMemberService;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.sms.SmsSendVO;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@RestController
@RequestMapping("/fastCommon")
public class FastCommonController extends BaseController {
    @Autowired
    private MailService mailService;
    @Autowired
    private FastMemberService fastMemberService;

    @ApiName(value = "发送验证码", folder = {"common"})
    @ApiParamsIn({
            "type:1:int:发送类型：1、短信；2、邮件",
            "emails:1:str:邮箱",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping(value = "/nologin/sendVerifyCodeForDestory")
    public ResultVO<?> sendVerifyCode(HttpServletRequest request, SmsSendVO vo) {
        if (vo.getType() == null) {
            return ResultVO.error("The send type cannot be empty.");
        }
        if (vo.getType() == 1) {
            if (StrUtil.isEmpty(vo.getPhoneNumbers())) {
                return ResultVO.error("The mobile phone number cannot be empty.");
            }
        }
        if (vo.getType() == 2) {
            if (StrUtil.isEmpty(vo.getEmails())) {
                return ResultVO.error("The email cannot be empty.");
            }
            if (!vo.getEmails().matches(StaticVar.EMAIL_REGEX)) {
                return ResultVO.error("The email is incorrectly formatted.");
            }
            if (!Objects.equals(vo.getCk(), 0)) {
                FastMemberPO member = fastMemberService.queryMemberByEmail(vo.getEmails());
                if (member == null) {
                    return ResultVO.error("The user does not exist, check whether the email address is incorrect.");
                }
            }
            mailService.sendVerifyCodeMail(vo.getEmails());
        }
        return ResultVO.success();
    }

    @ApiName(value = "注销APP用户", folder = {"common"})
    @ApiParamsIn({
            "emails:1:str:邮箱",
            "code:1:str:验证码",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping(value = "/nologin/destoryMember")
    public ResultVO<?> destoryMember(HttpServletRequest request, SmsSendVO vo) {
        if (StrUtil.isBlank(vo.getEmails())) {
            return ResultVO.error("The email cannot be empty.");
        }
        if (StrUtil.isBlank(vo.getCode())) {
            return ResultVO.error("The verification code cannot be empty.");
        }
        if (!mailService.checkVerifyCode(vo.getEmails(), vo.getCode())) {
            return ResultVO.error("The verification code is incorrect.");
        }
        FastMemberPO member = fastMemberService.queryMemberByEmail(vo.getEmails());
        if (member == null) {
            return ResultVO.error("The user does not exist, check whether the email address is incorrect.");
        }
        fastMemberService.delMemberById(member.getId());
        return ResultVO.success();
    }

    @PostMapping(value = "/queryCache")
    public ResultVO<?> queryCache(HttpServletRequest request, Integer db, String key, Integer json) {
        if (StrUtil.isBlank(key)) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        String res = null;
        try {
            res = RedisUtil.get(db != null ? db : RedisVar.REDIS_DB, key);
        } catch (Exception e) {
            log.error("query redis cache fail. {}", ExceptionUtils.getStackTrace(e));
        }
        return ResultVO.success(json == 1 ? JSON.parseObject(res) : res);
    }

    @PostMapping(value = "/refreshCache")
    public ResultVO<?> refreshCache(HttpServletRequest request, Integer db, String key) {
        if (StrUtil.isBlank(key)) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        try {
            RedisUtil.del(db != null ? db : RedisVar.REDIS_DB, key);
        } catch (Exception e) {
            log.error("refresh redis cache fail. {}", ExceptionUtils.getStackTrace(e));
        }
        return ResultVO.success();
    }

}
