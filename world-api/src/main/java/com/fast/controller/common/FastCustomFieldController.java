/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.common.FastCustomFieldPO;
import com.fast.service.common.FastCustomFieldService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 自定义列表字段
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fast")
public class FastCustomFieldController extends BaseController {

    private static final int[] MODULAR = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12};
    @Autowired
    private FastCustomFieldService fastCustomFieldService;

    @ApiName(value = "自定义列表字段-查询", folder = {"自定义列表字段"})
    @ApiParamsIn({
            "modular:1:int:模块 1=短剧数据明细;2=剧集数据明细;3=投放分析-分销商明细;4=投放分析-日期明细汇总;5=投放分析-短剧明细汇总;6=投放分析-优化师明细汇总",
            "modular:1:int:模块 7=渠道分析-整体;8=渠道分析-链接;9=渠道分析-分销商;10=渠道分析-优化师;11=渠道分析-短剧;12=渠道分析-链路",
    })
    @ApiParamsOut({
            "results>>state:状态:0=关闭;1=开启",
            "results>>modular:模块 1=短剧数据明细;2=剧集数据明细;3=投放分析-分销商明细;4=单分销商分析-日期明细;5=单分销商分析-短剧;6=单分销商分析-优化师",
            "results>>fieldType:字段类型 模块1 {1=短剧名称;2=观看人数;3=K币消耗数;4=完播统计;5=广告消耗;6=新增ROI;7=付费充值;8=免费充值;9=免费广告;10=上线时间}",
            "results>>fieldType:字段类型 模块2 {1=短剧名称;2=观看人数;3=留存率;4=单集跳出率;5=单集K币消耗数;6=单集完播人数;7=完播率;8=是否付费;9=订阅人数}",
            "results>>fieldType:字段类型 模块3 {1=分销商名称;2=总消耗金额;3=新增用户数;4=染色情况;5=新增充值用户数;6=新增充值金额;7=arppu;8=新增ROI;9=总充值金额;10=累计ROI}",
            "results>>fieldType:字段类型 模块4 {1=日期;2=投放短剧数;3=总消耗金额;4=新增用户数;5=染色情况;6=新增充值用户数;7=新增充值金额;8=付费用户成本;9=新增ROI;10=总充值金额;11=累计ROI}",
            "results>>fieldType:字段类型 模块5 {1=短剧名称;2=总消耗金额;3=新增用户数;4=染色情况;5=新增充值用户数;6=新增充值金额;7=付费用户成本;8=新增ROI;9=总充值金额;10=累计ROI}",
            "results>>fieldType:字段类型 模块6 {1=优化师名称;2=新增用户数;3=染色情况;4=总成本;5=累计充值用户;D0-D60必在}",
            "results>>fieldType:字段类型 模块7 {1=日期;2=有效链接数;3=投放消耗;4=新增用户;5=染色用户;6=充值用户;7=用户成本;8=ARPPU;9=累计充值金额;10=累计回报率;11=续充率;D0-D60必在}",
            "results>>fieldType:字段类型 模块8 {1=链接ID;2=链接名称;3=优化师;4=分销商;5=投放消耗;6=新增用户;7=染色用户;8=充值用户;9=用户成本;10=ARPPU;11=累计充值金额;12=累计回报率;D0-D60必在}",
            "results>>fieldType:字段类型 模块9 {1=分销商;2=分销商类型;3=关联链接数;4=投放消耗;5=新增用户;6=染色用户;7=充值用户;8=用户成本;9=ARPPU;10=累计充值金额;11=累计回报率;D0-D60必在}",
            "results>>fieldType:字段类型 模块10 {1=优化师;2=分销商;3=关联链接数;4=投放消耗;5=新增用户;6=染色用户;7=充值用户;8=用户成本;9=ARPPU;10=累计充值金额;11=累计回报率;D0-D60必在}",
            "results>>fieldType:字段类型 模块11 {1=短剧id;2=短剧名称;3=关联链接数;4=投放消耗;5=新增用户;6=染色用户;7=充值用户;8=用户成本;9=ARPPU;10=累计充值金额;11=累计回报率;D0-D60必在}",
            "results>>fieldType:字段类型 模块12 {1=链路名称;2=关联链接数;3=投放消耗;4=新增用户;5=染色用户;6=充值用户;7=用户成本;8=ARPPU;9=累计充值金额;10=累计回报率;D0-D60必在}",
    })
    @RequestMapping(value = "/getCustomFieldList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastCustomFieldPO params) {
        if (params.getModular() == null || !StrUtil.equalsAny(params.getModular(), MODULAR)) {
            return ResultVO.error("modular不合法");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setUserId(sessionVO.getUserId());
        List<FastCustomFieldPO> list = fastCustomFieldService.queryCustomFieldList(params);
        if (list == null) {
            return ResultVO.success(getDefaultListData());
        }
        return ResultVO.success(list);
    }

    @ApiName(value = "自定义列表字段-保存", folder = {"自定义列表字段"})
    @ApiParamsIn({
            "list是一个json数据",
            "list>>state:1:int:状态 0=关闭;1=开启",
            "list>>modular:1:int:模块 1=短剧数据明细;2=剧集数据明细;3=投放分析-分销商明细;4=投放分析-日期明细汇总;5=投放分析-短剧明细汇总;6=投放分析-优化师明细汇总",
            "list>>fieldType:1:int:字段类型-参考查询接口",
    })
    @ApiParamsOut({"success",})
    @RequestMapping(value = "/saveCustomField", method = {RequestMethod.POST})
    public ResultVO<?> save(HttpServletRequest request, @RequestBody FastCustomFieldPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setUserId(sessionVO.getUserId());
        if (CollUtil.isEmpty(params.getList())) {
            return ResultVO.error("list不合法");
        }
        MethodVO methodVO = fastCustomFieldService.save(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
