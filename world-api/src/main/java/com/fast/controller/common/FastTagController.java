/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.common.FastTagPO;
import com.fast.po.drama.FastDramaTagPO;
import com.fast.service.common.FastTagService;
import com.fast.service.common.FastTagTypeService;
import com.fast.service.drama.FastDramaTagService;
import com.fast.service.language.FastLanguageService;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastTag")
public class FastTagController extends BaseController {

    @Autowired
    private FastTagService fastTagService;
    @Autowired
    private FastTagTypeService fastTagTypeService;
    @Autowired
    private FastDramaTagService dramaTagService;
    @Autowired
    private FastLanguageService fastLanguageService;

    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastTagPO params, PageVO pageVO) {
        params.setDelFlag(StaticVar.NO);
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        return fastTagService.queryPageList(params, pageVO);
    }

    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastTagPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        FastTagPO item = fastTagService.queryById(id, true);
        return ResultVO.summary(item, fastLanguageService.allCodeMapList());
    }

    @ApiName(value = "标签管理-添加", folder = {"标签管理"})
    @ApiParamsIn({
            "tagTypeId:1:int:类型ID",
            "fontColor:1:str:前景色",
            "backColor:1:str:背景色",
            "i18ns:1:array:角标数组，格式 {'languageCode'：'SC','name'：'推荐111'}",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody FastTagPO params) {
        ResultVO<?> resultVO = checkParams(params);
        if (resultVO != null) {
            return resultVO;
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastTagService.insert(params);
        RedisUtil.del(StaticVar.TAG_INFO_ID + params.getId());

        fastTagTypeService.updateCount(params.getTagTypeId());
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "标签管理-更新", folder = {"标签管理"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "tagTypeId:1:int:类型ID",
            "fontColor:1:str:前景色",
            "backColor:1:str:背景色",
            "i18ns:1:array:角标数组，格式 {'languageCode'：'SC','name'：'推荐111'}",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, @RequestBody FastTagPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        ResultVO<?> resultVO = checkParams(params);
        if (resultVO != null) {
            return resultVO;
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastTagService.update(params);
        RedisUtil.del(StaticVar.TAG_INFO_ID + params.getId());

        FastTagPO fastTag = fastTagService.queryById(params);
        fastTagTypeService.updateCount(fastTag.getTagTypeId());
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 校验字段合法性
     *
     * @param params
     * @return
     */
    private ResultVO<?> checkParams(FastTagPO params) {
        // if (isBlank(params.getTagName())) {
        //     return ResultVO.error("名称必填");
        // }
        // if (params.getTagName().length() > 4) {
        //     return ResultVO.error("名称不能超过4字符");
        // }
        if (isBlank(params.getFontColor())) {
            return ResultVO.error("字体颜色必填");
        }
        if (isBlank(params.getBackColor())) {
            return ResultVO.error("背景颜色必填");
        }
        // if (notBlank(params.getRemark()) && params.getRemark().length() > 50) {
        //     return ResultVO.error("描述不能超过50字符");
        // }
        params.setFontColor(params.getFontColor().replace(StaticVar.CROSS_HATCH, StaticVar.EMPTY));
        params.setBackColor(params.getBackColor().replace(StaticVar.CROSS_HATCH, StaticVar.EMPTY));
        return null;
    }

    @RequestMapping(value = "/updateDel", method = {RequestMethod.POST})
    public ResultVO<?> updateDel(HttpServletRequest request, FastTagPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        // 校验是否被使用过
        FastDramaTagPO tagQ = new FastDramaTagPO();
        tagQ.setTagId(id);
        if (dramaTagService.queryCount(tagQ) > 0) {
            return ResultVO.error("该标签正在关联作品使用，暂时不可删除");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastTagService.updateDel(params);
        RedisUtil.del(StaticVar.TAG_INFO_ID + params.getId());

        FastTagPO fastTag = fastTagService.queryById(params);
        fastTagTypeService.updateCount(fastTag.getTagTypeId());
        return ResultVO.fromMethodVO(methodVO);
    }
}
