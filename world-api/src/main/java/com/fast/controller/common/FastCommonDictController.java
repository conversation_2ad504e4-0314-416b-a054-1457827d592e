/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.common.FastCommonDictPO;
import com.fast.service.common.FastCommonDictService;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
public class FastCommonDictController extends BaseController {

    @Autowired
    private FastCommonDictService fastCommonDictService;

    @ApiName(value = "common-查询列表", folder = {"common"})
    @PostMapping("/getFastCommonDictList")
    @ApiParamsIn({
            "code:1:str:字典标识"
    })
    @ApiParamsOut({
            "id:字典ID",
            "code:字典标识",
            "value:键值",
            "key:键值key",
            "seq:排序",
            "createTime:创建时间",
            "delFlag: 删除标志0否1是"
    })
    public ResultVO<?> getList(FastCommonDictPO params) {
        if(StrUtil.isEmpty(params.getCode())){
            return ResultVO.error("参数错误，code不能为空");
        }
        return fastCommonDictService.queryPageList(params);
    }


    @ApiName(value = "common-新增字典", folder = {"common"})
    @PostMapping("/insertFastCommonDict")
    @ApiParamsIn({
            "code:1:str:字典值编码",
            "key:1:str:字典值key",
            "value:1:str:字典值",
            "seq:0:int:排序",
    })
    @ApiParamsOut({
            "status:ok",
    })
    public ResultVO<?> insert(FastCommonDictPO params) {
        if(StrUtil.isEmpty(params.getCode())){
            return ResultVO.error("参数错误，code不能为空");
        }
        if (StrUtil.isEmpty(params.getKey())) {
            return ResultVO.error("参数错误，key不能为空");
        }
        return ResultVO.fromMethodVO(fastCommonDictService.insert(params));
    }


    @ApiName(value = "common-修改字典", folder = {"common"})
    @PostMapping("/updateFastCommonDict")
    @ApiParamsIn({
            "id:1:int:字典值ID",
            "code:1:str:字典值编码",
            "key:1:str:字典值key",
            "value:1:str:字典值",
            "seq:0:int:排序",
    })
    @ApiParamsOut({
            "status:ok",
    })
    public ResultVO<?> update(FastCommonDictPO params) {
        if (params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isEmpty(params.getCode())) {
            return ResultVO.error("参数错误，code不能为空");
        }
        if (StrUtil.isEmpty(params.getKey())) {
            return ResultVO.error("参数错误，key不能为空");
        }
        return ResultVO.fromMethodVO(fastCommonDictService.update(params));
    }
}
