/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.common.FastFileExportPO;
import com.fast.service.common.FastFileExportService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 文件下载管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/download")
public class FastFileExportController extends BaseController {

    @Autowired
    private FastFileExportService fastFileExportService;

    @ApiName(value = "文件下载-查询列表", folder = {"通用"})
    @ApiParamsIn({"null"})
    @ApiParamsOut({
            "title:标题",
            "createTime:生成时间",
            "url:下载地址",
    })
    @RequestMapping(value = "/getDownloadList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastFileExportPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setDeadTime(DateUtil.getNowDate());
        fastFileExportService.updateState(params);
        params.setState(1);
        params.setDeadTime(null);
        return fastFileExportService.queryPageList(params, pageVO);
    }

    @Slave
    @ApiName(value = "文件下载-查询单个详情", folder = {"通用"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastFileExportPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFileExportPO fastFileExport = fastFileExportService.queryById(params);
        return ResultVO.success(fastFileExport);
    }
}
