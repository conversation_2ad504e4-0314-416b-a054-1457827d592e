/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.base.BaseController;
import com.fast.mapper.monitor.FastMonitorPointTimeMapper;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 缓存管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastCache")
public class FastCacheController extends BaseController {
    @Autowired
    private FastMonitorPointTimeMapper fastMonitorPointTimeMapper;


    /**
     * 删除缓存
     *
     * @param key
     * @return
     */
    @RequestMapping(value = "nologin/delCache")
    public ResultVO<?> delCacheByKey(String sec, String key) {
        if ("abc1259".equals(sec)) {
            RedisUtil.del(key);
            return ResultVO.success();
        } else {
            return ResultVO.error("sec错误");
        }
    }

//    @RequestMapping(value = "nologin/getCurrentDevice")
//    public ResultVO<?> getCurrentDevice(String sec, String key) {
//    	return ResultVO.success(StaticVar.DEVICE_NUM);
//    }


//    @RequestMapping(value = "nologin/syntemp")
//    public ResultVO<?> syntemp(String day,String hour) {
//    	String[] hourArray = hour.split(",");
//    	for(int i=0;i<hourArray.length;i++){
//    		fastEcpmDataTtService.sysEcpmDataTemp(day+" "+hourArray[i]);
//    	}
//    	
//    	return ResultVO.success();
//    }

    /**
     * 删除切换cdn授权相关缓存
     *
     * @param sec
     * @param key
     * @return
     */
//    @RequestMapping(value = "nologin/delAuthCache")
//    public ResultVO<?> delAuthCache(String sec, String key) {
//        cacheResetService.resetCacheMiniSetOnly(null);
//        return ResultVO.success("");
//    }

    /**
     * 测试方法内的权限验证
     */
//    @RequestMapping(value = "/testJob")
//    public ResultVO<?> testJob(HttpServletRequest request) {
//
//        return ResultVO.success("");
//    }

    /**
     * 微信小程序切换支付商户号
     *
     * @param miniId
     * @param mchId
     * @return
     */
//    @RequestMapping(value = "nologin/changeMchId")
//    public ResultVO<?> changeSubMchId(Integer miniId, Integer mchId) {
//        if (miniId == null || mchId == null) {
//            return ResultVO.error("miniId和mchId非空");
//        }
//        accountFunService.changeSubMchId(miniId, mchId);
//        return ResultVO.success();
//    }

//    @RequestMapping(value = "nologin/synLinkPVFromTableStore")
//    public ResultVO<?> synLinkPVFromTableStore(Integer maxId,Integer count) {
//    	if(maxId == null || count == null){
//    		return ResultVO.error("maxId不能为空，count不能为空");
//    	}
//    	fastLinkService.synLinkPVFromTableStore(maxId,count);
//    	return ResultVO.success();
//    }

//    @RequestMapping(value = "nologin/test")
//    public ResultVO<?> redis(Integer count,Integer type) {
//		if(type == 1){
//    		tiktok3BackService.tiktokSynPayModel(count);
//		}else if(type == 2){
//			tiktok3BackService.tiktokSynBindPayModel(count);		
//		}
//    	return ResultVO.success();
//    }

//    @RequestMapping(value = "nologin/testactive")
//    public ResultVO<?> active(Long memberId,Integer type) {
//		if(memberId == null){
//			return ResultVO.error("用户id不能为空");
//		}
//		if(type == 1){
//			fastMemberActiveService.active(memberId);
//			return ResultVO.success("已设置为活跃");
//		}else if(type == 2){
//			fastMemberActiveService.disActive(memberId);
//			return ResultVO.success("已设置为非活跃");
//		}
//    	return ResultVO.success("没有任何操作，type值不对");
//    }


//    @RequestMapping(value = "nologin/testTTVipPush")
//    public ResultVO<?> testTTVipPush(Long meberId) {
//    	tiktokOpenService.sendVipPushJob(meberId);
//    	return ResultVO.success();
//    }

//    @RequestMapping(value = "nologin/testGlobalExp")
//    public ResultVO<?> testGlobalExp(Long memberId) {
//    	System.out.println(5/memberId);
//    	return ResultVO.success();
//    }

//    @RequestMapping(value = "nologin/testSettle")
//    public ResultVO<?> testSettle(Long maxId) {
//    	tiktokOpenService.sendSettleToDouyinCommonJob(null,maxId);
//    	return ResultVO.success();
//    }

//    @RequestMapping(value = "nologin/testContract")
//    public ResultVO<?> testContract(Long id) {
//		FastMemberOrderContractRecordPO mocPO = fastMemberOrderContractRecordMapper.queryById(id);
//    	return fastMemberOrderRechargeService.rechargeAddTiktokVip(mocPO);
//    }

//    @RequestMapping(value = "nologin/testUpdateUserNo")
//    public ResultVO<?> testUpdateUserNo(Long start,Long end,Integer limit) {
//    	return fastMemberService.updateMemberUserNo(start, end, limit);
//    }


}
