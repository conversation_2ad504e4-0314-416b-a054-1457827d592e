/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.po.common.FastLogOperationPO;
import com.fast.service.common.FastOperationLogService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.common.TimeInDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 操作日志
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastLogOperation")
public class FastOperationLogController extends BaseController {

    @Autowired
    private FastOperationLogService fastOperationLogService;

    @ApiName(value = "操作日志-查询列表", folder = {StaticFolder.FOLDER_OPER})
    @ApiParamsIn({
            "retailIds:0:分销商id们",
            "linkId:0:int:渠道id",
            "type:0:str:操作类型-11修改推广链接；12修改充值模板的档位；13修改充值模板；14修改回传规则",
            "objectLike:0:str:修改对象",
            "userIds:0:str:操作人id,多个逗号分隔",
            "timeIn:0:int:日期选择：1今天; 2昨天; 5近7天; -1自定义",
            "createTimeStr:0:str:自定义日期 yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "createTime:操作时间",
            "type:操作类型-11修改推广链接；12修改充值模板的档位；13修改充值模板；14修改回传规则",
            "userName:操作人姓名",
            "object:修改对象",
            "remark:修改内容",
            "snapshotOld:修改前的数据",
            "snapshotNew:修改后的数据",
    })
    @PostMapping("/getFastLogOperationList")
    public ResultVO<?> getList(HttpServletRequest request, FastLogOperationPO params, PageVO pageVO, TimeInDto dto) {
        StrUtil.checkMysqlInData(params.getUserIds());
        if (dto.getTimeIn() > -1) {
            doTimeIn(dto);
            params.setCreateTimeS(dto.getStartTime());
            params.setCreateTimeE(dto.getEndTime());
        } else if (notBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setLevel(1);
        return fastOperationLogService.queryPageList(params, pageVO);
    }


    @ApiName(value = "操作日志-查询列表", folder = {StaticFolder.FOLDER_OPER})
    @ApiParamsIn({
            "linkId:0:int:渠道id",
            "type:0:str:操作类型-11修改推广链接；12修改充值模板的档位；13修改充值模板；14修改回传规则",
            "objectLike:0:str:修改对象",
            "userIds:0:str:操作人id,多个逗号分隔",
            "timeIn:0:int:日期选择：1今天; 2昨天; 5近7天; -1自定义",
            "createTimeStr:0:str:自定义日期 yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "createTime:操作时间",
            "type:操作类型-11修改推广链接；12修改充值模板的档位；13修改充值模板；14修改回传规则",
            "userName:操作人姓名",
            "object:修改对象",
            "remark:修改内容",
            "snapshotOld:修改前的数据",
            "snapshotNew:修改后的数据",
    })
    @PostMapping("/getFastLogOperationCommonList")
    public ResultVO<?> getFastLogOperationCommonList(HttpServletRequest request, FastLogOperationPO params, PageVO pageVO, TimeInDto dto) {
        SessionVO sessionVO = getSessionVO(request);
        StrUtil.checkMysqlInData(params.getUserIds());
        if (dto.getTimeIn() > -1) {
            doTimeIn(dto);
            params.setCreateTimeS(dto.getStartTime());
            params.setCreateTimeE(dto.getEndTime());
        } else if (notBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        if (params.getLinkId() != null) {
            params.setObjectId(params.getLinkId());
            params.setObjectType(1);
        }
        params.setContentType(sessionVO.getContentType());
        return fastOperationLogService.queryPageList(params, pageVO);
    }

    @ApiName(value = "操作日志-查询单个详情", folder = {StaticFolder.FOLDER_OPER})
    @PostMapping("/getFastLogOperationDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastLogOperationPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastLogOperationPO fastLogOperation = fastOperationLogService.queryById(params);
        return ResultVO.success(fastLogOperation);
    }

    @ApiName(value = "操作日志-添加", folder = {StaticFolder.FOLDER_OPER})
    @PostMapping("/insertFastLogOperation")
    public ResultVO<?> insert(HttpServletRequest request, FastLogOperationPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastOperationLogService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
