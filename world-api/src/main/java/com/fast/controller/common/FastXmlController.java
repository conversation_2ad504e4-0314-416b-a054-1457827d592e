/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.base.BaseController;
import com.fast.vo.ResultVO;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 缓存管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastXml")
public class FastXmlController extends BaseController {

    /**
     * 删除缓存
     *
     * @param key
     * @return
     */
    @RequestMapping(value = "/test", produces = MediaType.APPLICATION_XML_VALUE)
    public ResultVO<?> test(String sec, String key) {


        return null;
    }


}
