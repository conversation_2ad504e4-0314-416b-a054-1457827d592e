/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.annotation.ApiName;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.common.FastTagPO;
import com.fast.po.common.FastTagTypePO;
import com.fast.po.drama.FastDramaTagPO;
import com.fast.service.common.FastTagService;
import com.fast.service.common.FastTagTypeService;
import com.fast.service.drama.FastDramaTagService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 标签类别
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastTagType")
public class FastTagTypeController extends BaseController {

    @Autowired
    private FastTagTypeService fastTagTypeService;
    @Autowired
    private FastDramaTagService dramaTagService;
    @Autowired
    private FastTagService fastTagService;

    /**
     * 标签类别-查询列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "标签类别-查询列表", folder = {"标签管理"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    @Slave
    public ResultVO<?> getList(HttpServletRequest request, FastTagTypePO params, PageVO pageVO) {
        params.setDelFlag(StaticVar.NO);
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        return fastTagTypeService.queryPageList(params, pageVO);
    }

    /**
     * 标签类别-查询treeList
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "标签类别-查询treeList", folder = {"标签管理"})
    @RequestMapping(value = "/getTreeList", method = {RequestMethod.POST})
    public ResultVO<?> getTreeList(HttpServletRequest request, FastTagTypePO params) {
        params.setDelFlag(StaticVar.NO);
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        return fastTagTypeService.getTreeList(params);
    }

    @ApiName(value = "标签类别-查询单个详情", folder = {"标签管理"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastTagTypePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastTagTypePO fastTagType = fastTagTypeService.queryById(params);
        return ResultVO.success(fastTagType);
    }

    @ApiName(value = "标签类别-添加", folder = {"标签管理"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastTagTypePO params) {
        if (params.getPid() == null) {
            return ResultVO.error("pid必填");
        }
        if (params.getTypeNames() == null) {
            return ResultVO.error("typeNames必填");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastTagTypeService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "标签类别-更新", folder = {"标签管理"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastTagTypePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (isBlank(params.getTypeName())) {
            return ResultVO.error("名称必填");
        }
        if (params.getTypeName().length() > 4) {
            return ResultVO.error("名称不能超过4字符");
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastTagTypeService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "标签类别-删除", folder = {"标签管理"})
    @RequestMapping(value = "/updateDel", method = {RequestMethod.POST})
    public ResultVO<?> updateDel(HttpServletRequest request, FastTagTypePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        // 校验是否被使用过
        {
            FastTagPO tagPO = new FastTagPO();
            tagPO.setTagTypeId(id);
            List<Integer> tagIds = fastTagService.queryTagIds(tagPO);
            // 当前分组下面存在标签
            if (CollUtil.hasContent(tagIds)) {
                FastDramaTagPO tagQ = new FastDramaTagPO();
                tagQ.setTagIds(StrUtil.join(tagIds));
                if (dramaTagService.queryCount(tagQ) > 0) {
                    return ResultVO.error("该类别下的标签正在关联作品使用，暂时不可删除");
                }
            }
        }
        // 查询分组下面的子分组
        FastTagTypePO type = new FastTagTypePO();
        type.setPid(id);
        List<Integer> idList = fastTagTypeService.queryIdList(type);
        if (CollUtil.hasContent(idList)) {
            FastTagPO tagPO = new FastTagPO();
            tagPO.setTagTypeIds(StrUtil.join(idList));
            List<Integer> tagIds = fastTagService.queryTagIds(tagPO);
            // 当前分组下面存在标签
            if (CollUtil.hasContent(tagIds)) {
                FastDramaTagPO tagQ = new FastDramaTagPO();
                tagQ.setTagIds(StrUtil.join(tagIds));
                if (dramaTagService.queryCount(tagQ) > 0) {
                    return ResultVO.error("该类别下的标签正在关联作品使用，暂时不可删除");
                }
            }
        }

        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastTagTypeService.updateDel(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
