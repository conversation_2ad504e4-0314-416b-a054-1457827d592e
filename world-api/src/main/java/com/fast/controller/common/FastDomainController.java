/*
 * Powered By fast.up
 */
package com.fast.controller.common;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.common.FastDomainPO;
import com.fast.service.common.FastDomainService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.apache.commons.validator.routines.DomainValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 域名管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastDomain")
public class FastDomainController extends BaseController {

    @Autowired
    private FastDomainService fastDomainService;

    @ApiName(value = "域名-查询列表", folder = {"域名"})
    @ApiParamsIn({
            "retailId:1:int:分销商id",
            "domainType:1:int:域名N级, 默认2级.",
            "pid:1:int:父级id",
            "type:1:int:1=业务域名;2=授权域名",
            "state:1:int:状态 0=被封禁;1=使用中;2=待使用",
    })
    @ApiParamsOut({
            "results>>state:状态 0=被封禁;1=使用中;2=待使用",
            "results>>type:1=业务域名;2=授权域名",
            "results>>domainType:域名N级",
            "results>>domainName:域名",
            "results>>principalName:主体名称",
            "results>>officialId:公众号ID",
            "results>>officialName:公众号名称",
            "results>>retailId:分销商ID",
            "results>>retailName:分销商名称",
            "results>>createTime:创建时间",
    })
    @Slave
    @RequestMapping(value = "/getFastDomainList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastDomainPO params, PageVO pageVO) {
        if (!StrUtil.equalsAny(params.getDomainType(), 2, 1)) {
            return ResultVO.error("域名级数不合法");
        }
        if (params.getDomainType() == null) {
            params.setDomainType(2);
        }
        if (params.getDomainType() == 2 && params.getPid() == null) {
            return ResultVO.error("二级域名需要pid");
        }
        params.setDelFlag(0);
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        return fastDomainService.queryPageList(params, pageVO);
    }

    @ApiName(value = "域名-查询单个详情", folder = {"域名"})
    @RequestMapping(value = "/getFastDomainDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastDomainPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastDomainPO fastDomain = fastDomainService.queryById(params);
        return ResultVO.success(fastDomain);
    }

    @ApiName(value = "域名-添加", folder = {"域名"})
    @ApiParamsIn({
            "domainName:1:str:域名",
            "domainType:1:int:域名N级, 默认2级.",
            "type:1:int:1=业务域名;2=授权域名",
            "pid:1:int:父级id",
            "principalName:1:str:主体名称",
            "retailId:1:str:分销商ID",
    })
    @ApiParamsOut({"s"})
    @RequestMapping(value = "/insertFastDomain", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, @Validated FastDomainPO params) {
        if (!StrUtil.equalsAny(params.getDomainType(), 2, 1)) {
            return ResultVO.error("域名级数不合法");
        }
        if (params.getType() == null) {
            params.setType(1);
        }
        if (params.getDomainType() == 2) {
            if (params.getPid() == null) {
                return ResultVO.error("请输入父级ID");
            }
            FastDomainPO domainP = fastDomainService.queryById(params.getPid());
            params.setDomainName(params.getDomainName() + "." + domainP.getDomainName());
            params.setMiniId(domainP.getMiniId());
        }
        String[] domainArr = params.getDomainName().split("\\.");
        if (domainArr.length != params.getDomainType() + 1) {
            return ResultVO.error("请输入合法的域名");
        }
        DomainValidator instance = DomainValidator.getInstance();
        if (!instance.isValid(params.getDomainName())) {
            return ResultVO.error("请输入合法的域名");
        }
        if (params.getRetailId() != null && params.getRetailId() > 0) {
            params.setState(1);

            if (params.getDomainType() == 1) {
                FastDomainPO itemQ = new FastDomainPO();
                itemQ.setRetailId(params.getRetailId());
                itemQ.setDomainType(2);
                if (fastDomainService.queryCount(itemQ) > 0) {
                    return ResultVO.error("该分销商已经分配了域名");
                }
            }
        }
        if (params.getType() == 2) {
            FastDomainPO itemQ = new FastDomainPO();
            itemQ.setRetailId(params.getRetailId());
            itemQ.setType(2);
            if (fastDomainService.queryCount(itemQ) > 0) {
                return ResultVO.error("授权域名不能新增多个");
            }
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        MethodVO methodVO = fastDomainService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "域名-更新", folder = {"域名"})
    @ApiParamsIn({
            "encryptionId:1:str:域名加密id",
            "domainName:1:str:域名",
            "domainType:1:int:域名N级, 默认2级.",
            "type:1:int:1=业务域名;2=授权域名",
            "principalName:1:str:主体名称",
            "retailId:1:int:分销商ID",
    })
    @ApiParamsOut({"s"})
    @RequestMapping(value = "/updateFastDomain", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, @Validated FastDomainPO params) {
        params.setType(null);
        if (params.getDomainType() == 2) {
            return ResultVO.error("二级域名不能编辑");
        }
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        String[] domainArr = params.getDomainName().split("\\.");
        if (domainArr.length != params.getDomainType() + 1) {
            return ResultVO.error("请输入合法的域名");
        }
        DomainValidator instance = DomainValidator.getInstance();
        if (!instance.isValid(params.getDomainName())) {
            return ResultVO.error("请输入合法的域名");
        }
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastDomainService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "域名-删除", folder = {"域名"})
    @ApiParamsIn({
            "encryptionId:1:str:域名加密id",
    })
    @ApiParamsOut({"s"})
    @RequestMapping(value = "/deleteFastDomain", method = {RequestMethod.POST})
    public ResultVO<?> delete(HttpServletRequest request, FastDomainPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        List<String> ids = CollUtil.parseStr2List(params.getEncryptionId());
        int success = 0;
        for (String idStr : ids) {
            Integer id = decodeInt(idStr);
            if (id == null) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            params.setId(id);
            params.setDelFlag(1);
            MethodVO methodVO = fastDomainService.update(params);
            if (methodVO.getCode() == 0) {
                success++;
            }
        }
        if (success == 0) {
            return ResultVO.error("操作失败");
        }
        if (success != ids.size()) {
            return ResultVO.error("部分失败");
        }
        return ResultVO.success();
    }
}
