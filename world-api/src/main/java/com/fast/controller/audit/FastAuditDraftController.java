/*
 * Powered By fast.up
 */
package com.fast.controller.audit;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.audit.FastAuditDraftPO;
import com.fast.service.audit.FastAuditDraftService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/audit")
public class FastAuditDraftController extends BaseController {

    @Autowired
    private FastAuditDraftService fastAuditDraftService;

    @ApiName(value = "audit-送审草稿-查询", folder = {"audit"})
    @ApiParamsIn({
            "type:1:int:1腾讯送审草稿；2抖音送审草稿"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/getFastAuditDraftDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastAuditDraftPO params) {
        if (params.getType() == null) {
            return ResultVO.error("草稿类型不能为空");
        }
        params.setDelFlag(0);
        SessionVO sessionVO = getSessionVO(request);
        params.setUserId(sessionVO.getUserId());
        FastAuditDraftPO fastAuditDraft = fastAuditDraftService.queryOne(params);
        return ResultVO.success(fastAuditDraft);
    }

    @ApiName(value = "audit-送审操作-保存", folder = {"audit"})
    @ApiParamsIn({
            "type:1:int:1腾讯送审草稿；2抖音送审草稿",
            "draft:1:str:草稿内容，大json"
    })
    @PostMapping("/saveFastAuditDraft")
    public ResultVO<?> saveFastAuditDraft(HttpServletRequest request, @RequestBody FastAuditDraftPO params) {
        if (params.getType() == null) {
            return ResultVO.error("草稿类型不能为空");
        }
        if (StrUtil.isEmpty(params.getDraft())) {
            return ResultVO.error("草稿内容不能为空");
        }
        if (params.getDramaId() == null) {
            return ResultVO.error("剧id不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUserId(sessionVO.getUserId());
        MethodVO methodVO = fastAuditDraftService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
