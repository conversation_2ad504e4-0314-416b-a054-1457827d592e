/*
 * Powered By fast.up
 */
package com.fast.controller.audit;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.enums.audit.AuditTypeEnum;
import com.fast.po.audit.FastAuditFlowPO;
import com.fast.service.audit.FastAuditFlowService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
public class FastAuditFlowController extends BaseController {

    @Autowired
    private FastAuditFlowService fastAuditFlowService;

    @ApiParamsIn({
            "auditType:1:str:审批流程编码 month_income",
    })
    @ApiParamsOut({"status:ok"})
    @ApiName(value = "audit-审批流程查询列表", folder = {"audit"})
    @PostMapping("/fastAuditFlow/list")
    public ResultVO<?> getList(HttpServletRequest request, FastAuditFlowPO params, PageVO pageVO) {
        if (StrUtil.isBlank(params.getAuditType())) {
            return ResultVO.error("请选择审批流程类型!");
        }
        if (Objects.isNull(AuditTypeEnum.get(params.getAuditType()))) {
            return ResultVO.error("审批流程不存在!");
        }
        params.setDelFlag(0);
        return fastAuditFlowService.queryPageList(params, pageVO);
    }

    @ApiParamsIn({
            "auditType:1:str:审批流程编码 month_income",
            "auditFlowList->userId:1:int:审批人id",
    })
    @ApiParamsOut({"status:ok"})
    @ApiName(value = "audit-审批流程查询待审核数量", folder = {"audit"})
    @PostMapping("/fastAuditFlow/getAwaitAuditCount")
    public ResultVO<?> getAwaitAuditCount(HttpServletRequest request, @RequestBody FastAuditFlowPO params) {
        if (StrUtil.isBlank(params.getAuditType())) {
            return ResultVO.error("请选择审批流程类型!");
        }
        if (Objects.isNull(AuditTypeEnum.get(params.getAuditType()))) {
            return ResultVO.error("审批流程不存在!");
        }
        return fastAuditFlowService.getAwaitAuditCount(params);
    }

    @ApiName(value = "audit-查询单个详情", folder = {"audit"})
    @PostMapping("/getFastAuditFlowDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastAuditFlowPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastAuditFlowPO fastAuditFlow = fastAuditFlowService.queryById(params);
        return ResultVO.success(fastAuditFlow);
    }

    @ApiParamsIn({
            "auditType:1:str:审批流程编码 month_income",
            "auditFlowList->userId:1:int:审批人id",
    })
    @ApiParamsOut({"status:ok"})
    @ApiName(value = "audit-审批流程编辑、新增", folder = {"audit"})
    @PostMapping("/fastAuditFlow/insertOrUpdate")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody FastAuditFlowPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastAuditFlowService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
