/*
 * Powered By fast.up
 */
package com.fast.controller.audit;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.audit.FastAuditRecordPO;
import com.fast.service.audit.FastAuditRecordService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
public class FastAuditRecordController extends BaseController {

    @Autowired
    private FastAuditRecordService fastAuditRecordService;

    @ApiParamsIn({
            "processId:1:int:审批进度id",
    })
    @ApiParamsOut({
            "state:状态 1通过 2驳回",
            "remark:驳回原因",
            "creatorName:审核人名称",
            "createTime:审批时间",

    })
    @ApiName(value = "audit-审批记录-查询审批记录列表", folder = {"audit"})
    @PostMapping("/fastAuditRecord/list")
    public ResultVO<?> getList(HttpServletRequest request, FastAuditRecordPO params, PageVO pageVO) {
        if (Objects.isNull(params.getAuditProcessId())) {
            return ResultVO.error("参数错误!");
        }
        return fastAuditRecordService.queryPageList(params, pageVO);
    }
}
