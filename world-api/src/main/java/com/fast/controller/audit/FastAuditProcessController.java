/*
 * Powered By fast.up
 */
package com.fast.controller.audit;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.po.audit.FastAuditProcessPO;
import com.fast.service.audit.FastAuditProcessService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
public class FastAuditProcessController extends BaseController {

    @Autowired
    private FastAuditProcessService fastAuditProcessService;

    @ApiParamsIn({
            "dataIds:1:str:审批流程ids",
            "remark:1:str:审批意见",
            "state:1:str:状态 1通过 2驳回",
    })
    @ApiParamsOut({"status:ok"})
    @ApiName(value = "audit-审批进度-提交审批", folder = {"audit"})
    @PostMapping("/fastAuditProcess/audit")
    public ResultVO<?> audit(HttpServletRequest request, FastAuditProcessPO params) {
        if (StrUtil.isBlank(params.getDataIds())) {
            return ResultVO.error("请选择审批记录");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastAuditProcessService.audit(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
