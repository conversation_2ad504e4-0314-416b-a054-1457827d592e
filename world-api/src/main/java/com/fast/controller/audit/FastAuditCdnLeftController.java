/*
 * Powered By fast.up
 */
package com.fast.controller.audit;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.audit.FastAuditCdnLeftPO;
import com.fast.service.audit.FastAuditCdnLeftService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastAuditCdnLeft")
public class FastAuditCdnLeftController extends BaseController {

    @Autowired
    private FastAuditCdnLeftService fastAuditCdnLeftService;

    @ApiName(value = "audit-查询列表", folder = StaticFolder.FOLDER_AUDIT)
    @PostMapping("/getFastAuditCdnLeftList")
    public ResultVO<?> getList(HttpServletRequest request, FastAuditCdnLeftPO params, PageVO pageVO) {
        return fastAuditCdnLeftService.queryPageList(params, pageVO);
    }

    @ApiName(value = "audit-查询cdn剩余量", folder = StaticFolder.FOLDER_AUDIT)
    @ApiParamsIn({
            "appId:1:str:小程序id"
    })
    @ApiParamsOut({
            "leftCdn:剩余流量"
    })
    @PostMapping("/getFastAuditCdnLeftDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, String appId) {
        return fastAuditCdnLeftService.queryCdnLeft(appId);
    }

    @ApiName(value = "audit-cdn剩余流量编辑", folder = StaticFolder.FOLDER_AUDIT)
    @ApiParamsIn({
            "leftValue:1:int:剩余流量",
            "createTimeStr:1:str:创建时间yyyy-MM-dd HH:mm:ss"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insertFastAuditCdnLeft")
    public ResultVO<?> insert(HttpServletRequest request, FastAuditCdnLeftPO params) {
        if (StrUtil.isNotEmpty(params.getCreateTimeStr())) {
            params.setCreateTime(DateUtil.format07(params.getCreateTimeStr()));
        } else {
            params.setCreateTime(DateUtil.getNowDate());
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        // 把Gb换算成b
        Long leftValueB = params.getLeftValue() * 1000 * 1000 * 1000;
        params.setLeftValue(leftValueB);
        if (StrUtil.isEmpty(params.getAuditAppId())) {
            params.setAppId(StaticVar.AUDIT_APP_ID);
        } else {
            params.setAppId(params.getAuditAppId());
        }
        MethodVO methodVO = fastAuditCdnLeftService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "audit-更新", folder = StaticFolder.FOLDER_AUDIT)
    @PostMapping("/updateFastAuditCdnLeft")
    public ResultVO<?> update(HttpServletRequest request, FastAuditCdnLeftPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastAuditCdnLeftService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
