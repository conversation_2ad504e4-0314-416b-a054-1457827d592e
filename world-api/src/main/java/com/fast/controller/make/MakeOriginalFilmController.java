/*
 * Powered By fast.up
 */
package com.fast.controller.make;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.make.MakeOriginalFilmPO;
import com.fast.service.make.MakeOriginalFilmService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/makeOriginalFilm")
public class MakeOriginalFilmController extends BaseController {

    @Autowired
    private MakeOriginalFilmService makeOriginalFilmService;

    @ApiName(value = "原片-查询列表", folder = {"原片"})
    @ApiParamsIn({
            "name:0:str:文件名",
            "total:0:str:总集数",
            "createTimeStr:0:str:创建时间区间（yyyy-MM-dd - yyyy-MM-dd）",
            "page:1:int:页码",
            "limit:1:int:每页条数",
    })
    @ApiParamsOut({
            "list 》id: 主键ID",
            "list 》name: 文件名",
            "list 》total: 总集数",
            "list 》createTime: 创建时间",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, @RequestBody MakeOriginalFilmPO params, @RequestBody PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        return makeOriginalFilmService.queryPageList(params, pageVO);
    }

    @ApiName(value = "原片-查询单个详情", folder = {"原片"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "id: 主键ID",
            "name: 文件名",
            "total: 总集数",
            "createTime: 创建时间",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, @RequestBody MakeOriginalFilmPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MakeOriginalFilmPO makeOriginalFilm = makeOriginalFilmService.queryById(params);
        return ResultVO.success(makeOriginalFilm);
    }

    @ApiName(value = "原片-添加", folder = {"原片"})
    @ApiParamsIn({
            "name:1:str:文件名",
            "total:1:int:总集数",
            "videos:0:array:视频合集（seriesNum：剧集数、fid：文件id、name：文件名、url：oss地址）",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated MakeOriginalFilmPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = makeOriginalFilmService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "原片-更新", folder = {"原片"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "name:0:str:文件名",
            "total:0:int:总集数",
            "videos:0:array:视频合集（seriesNum：剧集数、fid: 文件id、name：文件名、url：oss地址）",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody MakeOriginalFilmPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = makeOriginalFilmService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "原片-删除", folder = {"原片"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, @RequestBody MakeOriginalFilmPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = makeOriginalFilmService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "原片-下载", folder = {"原片"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "downVideo:0:int:下载视频（1、是）",
            "seriesAll:0:int:视频全集（1、是），非全集时需要指定起始和结束集数",
            "seriesStart:0:int:视频开始集数",
            "seriesEnd:0:int:视频结束集数",
            "downCaption:0:int:下载字幕（1、是）",
            "aiCalibrate:0:int:AI校准（1、是）",
            "asrCaption:0:int:ASR字幕（1、是）",
            "ocrCaption:0:int:OCR字幕（1、是）",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/download")
    public ResultVO<?> download(HttpServletRequest request, @RequestBody MakeOriginalFilmPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = makeOriginalFilmService.download(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
