/*
 * Powered By fast.up
 */
package com.fast.controller.make;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.make.MakeCompletedFilmPO;
import com.fast.service.make.MakeCompletedFilmService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/makeCompletedFilm")
public class MakeCompletedFilmController extends BaseController {

    @Autowired
    private MakeCompletedFilmService makeCompletedFilmService;

    @ApiName(value = "成片-查询列表", folder = {"成片"})
    @ApiParamsIn({
            "id:0:str:主键ID",
            "name:0:str:文件名",
            "originalFilmId:0:str:原片ID",
            "state:0:str:状态（0、待生产；1、生产中；2、生产完成；3、生产失败）",
            "failReason:0:str:失败的原因",
            "createTimeStr:0:str:创建时间区间（yyyy-MM-dd - yyyy-MM-dd）",
            "page:1:int:页码",
            "limit:1:int:每页条数",
    })
    @ApiParamsOut({
            "list 》id: 主键ID",
            "list 》name: 文件名",
            "list 》total: 总集数",
            "list 》totalCount: 总任务数量",
            "list 》completedNum: 已完成任务数量",
            "list 》originalFilmId: 原片ID",
            "list 》state: 状态（0、待生产；1、生产中；2、生产完成；3、生产失败）",
            "list 》failReason: 失败的原因",
            "list 》createTime: 创建时间",
            "list 》startTime: 开始时间",
            "list 》endTime: 结束时间",
            "list 》takeTime: 耗费时长（毫秒）",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, @RequestBody MakeCompletedFilmPO params, @RequestBody PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        return makeCompletedFilmService.queryPageList(params, pageVO);
    }

    @ApiName(value = "成片-查询单个详情", folder = {"成片"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "id: 主键ID",
            "name: 文件名",
            "total: 总集数",
            "totalCount: 总任务数量",
            "completedNum: 已完成任务数量",
            "originalFilmId: 原片ID",
            "state: 状态（0、待生产；1、生产中；2、生产完成；3、生产失败）",
            "failReason: 失败的原因",
            "createTime: 创建时间",
            "startTime: 开始时间",
            "endTime: 结束时间",
            "takeTime: 耗费时长（毫秒）",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, @RequestBody MakeCompletedFilmPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MakeCompletedFilmPO makeCompletedFilm = makeCompletedFilmService.queryById(params);
        return ResultVO.success(makeCompletedFilm);
    }

    @ApiName(value = "成片-添加，成功之后会立即开始AI视频处理流程", folder = {"成片"})
    @ApiParamsIn({
            "name:1:str:文件名",
            "originalFilmId:1:int:原片ID",
            "sourceLang:1:str:源语言",
            "destLangs:1:str:目标语言，如：ES,EN",
            "region:1:array:擦除区域坐标，是一个二维数组，如：[[x1,y1],[x2,y2],[x3,y3],[x4,y4]]，一个矩形擦除框有4个点位",
            "removeBgAudio:1:int:去除背景音（0、否；1、是）",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated MakeCompletedFilmPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = makeCompletedFilmService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    // @ApiName(value = "成片-更新", folder = {"成片"})
    // @ApiParamsIn({
    //         "encryptionId:1:str:加密ID",
    //         "name:1:str:文件名",
    //         "total:1:int:总集数",
    //         "originalFilmId:1:int:原片ID",
    // })
    // @ApiParamsOut({
    //         "status:ok",
    // })
    // @PostMapping("/update")
    // public ResultVO<?> update(HttpServletRequest request, @RequestBody @Validated MakeCompletedFilmPO params) {
    //     if (isEmpty(params.getEncryptionId())) {
    //         return ResultVO.error(StaticStr.INVALID_PARAM);
    //     }
    //     Integer id = decodeInt(params.getEncryptionId());
    //     if (id == null) {
    //         return ResultVO.error(StaticStr.INVALID_PARAM);
    //     }
    //     params.setId(id);
    //     SessionVO sessionVO = getSessionVO(request);
    //     params.setUpdatorId(sessionVO.getUserId());
    //     MethodVO methodVO = makeCompletedFilmService.update(params);
    //     return ResultVO.fromMethodVO(methodVO);
    // }

    // @ApiName(value = "成片-删除", folder = {"成片"})
    // @ApiParamsIn({
    //         "encryptionId:1:str:加密ID",
    // })
    // @ApiParamsOut({
    //         "status:ok",
    // })
    // @PostMapping("/delete")
    // public ResultVO<?> delete(HttpServletRequest request, @RequestBody MakeCompletedFilmPO params) {
    //     if (isEmpty(params.getEncryptionId())) {
    //         return ResultVO.error(StaticStr.INVALID_PARAM);
    //     }
    //     Integer id = decodeInt(params.getEncryptionId());
    //     if (id == null) {
    //         return ResultVO.error(StaticStr.INVALID_PARAM);
    //     }
    //     params.setId(id);
    //     MethodVO methodVO = makeCompletedFilmService.delete(params);
    //     return ResultVO.fromMethodVO(methodVO);
    // }

    @ApiName(value = "成片-下载", folder = {"成片"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "downVideo:0:int:下载视频（1、是）",
            "videoLang:0:array:视频语言集合（00代表无字幕视频）",
            "seriesAll:0:int:视频全集（1、是），非全集时需要指定起始和结束集数",
            "seriesStart:0:int:视频开始集数",
            "seriesEnd:0:int:视频结束集数",
            "downCaption:0:int:下载字幕（1、是）",
            "captionLang:0:array:字幕语言集合",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/download")
    public ResultVO<?> download(HttpServletRequest request, @RequestBody MakeCompletedFilmPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = makeCompletedFilmService.download(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    // @ApiName(value = "成片-重试", folder = {"成片"})
    // @ApiParamsIn({
    //         "encryptionId:1:str:加密ID",
    // })
    // @ApiParamsOut({
    //         "status:ok",
    // })
    @PostMapping("/retry")
    public ResultVO<?> retry(HttpServletRequest request, @RequestBody MakeCompletedFilmPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = makeCompletedFilmService.retry(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
