package com.fast.controller.make.video;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fast.factory.VolcengineEventHandlerFactory;
import com.fast.service.make.video.VideoProcessServiceV2;
import com.fast.vo.MethodVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 视频处理控制器
 */
@RestController
@RequestMapping("/video-process")
public class VideoProcessController {

    private static final Logger log = LoggerFactory.getLogger(VideoProcessController.class);

    @Autowired
    private VolcengineEventHandlerFactory volcengineEventHandlerFactory;

    @Autowired
    private VideoProcessServiceV2 videoProcessServiceV2;

    // /**
    //  * 查询任务状态
    //  */
    // @GetMapping("/task/status/{taskId}")
    // public MethodVO getTaskStatus(@PathVariable String taskId) {
    //     // todo
    //     return MethodVO.success();
    // }
    //
    // /**
    //  * 回调接口 - 字幕提取完成
    //  */
    // @PostMapping("/callback/nologin/subtitle-extract/{taskId}")
    // public MethodVO subtitleExtractCallback(@PathVariable String taskId, @RequestBody String json) {
    //
    //     log.info("收到字幕提取回调: batchTaskId={}, 入参={}", taskId, json);
    //
    //     JSONObject request = JSON.parseObject(json);
    //
    //     videoProcessServiceV2.subtitleExtractNotify(request);
    //
    //     return MethodVO.success("success");
    // }
    //
    // /**
    //  * 回调接口 - 字幕擦除完成
    //  */
    // @PostMapping("/callback/nologin/subtitle-remove/{taskId}")
    // public MethodVO subtitleRemoveCallback(@PathVariable String taskId, @RequestBody String json) {
    //
    //     log.info("收到字幕擦除回调: batchTaskId={}, 入参={}", taskId, json);
    //
    //     JSONObject request = JSON.parseObject(json);
    //
    //     videoProcessServiceV2.subtitleRemoveNotify(request);
    //
    //     return MethodVO.success("success");
    // }
    //
    // /**
    //  * 回调接口 - 视频压制完成
    //  */
    // @PostMapping("/callback/nologin/video-compress/{taskId}")
    // public MethodVO videoCompressCallback(@PathVariable String taskId, @RequestBody String json) {
    //
    //     log.info("收到视频压制回调: batchTaskId={}, 入参={}", taskId, json);
    //
    //     JSONObject request = JSON.parseObject(json);
    //
    //     // videoProcessServiceV2.videoCompressNotify(request);
    //
    //     return MethodVO.success("success");
    // }

    @PostMapping("/callback/nologin/volcengine")
    public MethodVO videoCompressCallback(@RequestBody String json) {

        log.info("收到火山回调: {}", json);

        JSONObject request = JSON.parseObject(json);

        JSONObject data = request.getJSONObject("Data");
        if (data == null) {
            log.warn("火山回调数据异常，Data返回为空");
            return MethodVO.error("火山回调数据异常");
        }

        String eventType = request.getString("EventType");

        return volcengineEventHandlerFactory.getVolcengineEventHandler(eventType).handle(data);
    }

    @PostMapping("/fillForTask")
    public MethodVO fillForTask(Integer batchTaskId) {
        videoProcessServiceV2.fillForTask(batchTaskId);
        return MethodVO.success();
    }

    @PostMapping("/retryTask")
    public MethodVO retryTask(Integer taskId, String taskType) {
        return MethodVO.success();
    }

}
