/*
 * Powered By fast.up
 */
package com.fast.controller.make;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.enums.LanguageEnum;
import com.fast.po.language.FastLanguagePO;
import com.fast.po.make.MakeFilmVideoPO;
import com.fast.service.make.MakeFilmVideoService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/makeFilmVideo")
public class MakeFilmVideoController extends BaseController {

    @Autowired
    private MakeFilmVideoService makeFilmVideoService;

    @ApiName(value = "视频-查询列表", folder = {"影片库-视频"})
    @ApiParamsIn({
            "filmId:1:str:影片ID",
            "filmType:1:str:影片类型（1、原片；2、成片）",
            "seriesNum:0:str:剧集数（第几集）",
            "name:0:str:文件名",
            "state:0:str:状态（0、待生产；1、生产中；2、生产完成；3、生产失败）",
            "failReason:0:str:失败的原因",
            "url:0:str:OSS地址",
            "createTimeStr:0:str:创建时间区间（yyyy-MM-dd - yyyy-MM-dd）",
            "page:1:int:页码",
            "limit:1:int:每页条数",
    })
    @ApiParamsOut({
            "list 》id: 主键ID",
            "list 》filmId: 影片ID",
            "list 》filmType: 影片类型（1、原片；2、成片）",
            "list 》seriesNum: 剧集数（第几集）",
            "list 》name: 文件名",
            "list 》state: 状态（0、待生产；1、生产中；2、生产完成；3、生产失败）",
            "list 》failReason: 失败的原因",
            "list 》url: 视频OSS地址",
            "list 》originalFilmUrl: 原始视频OSS地址",
            "list 》createTime: 创建时间",
            "list 》startTime: 开始时间",
            "list 》endTime: 结束时间",
            "list 》takeTime: 耗费时长（毫秒）",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, @RequestBody MakeFilmVideoPO params, @RequestBody PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        return makeFilmVideoService.queryPageList(params, pageVO);
    }

    @ApiName(value = "视频-查询单个详情", folder = {"影片库-视频"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "id: 主键ID",
            "filmId: 影片ID",
            "filmType: 影片类型（1、原片；2、成片）",
            "seriesNum: 剧集数（第几集）",
            "name: 文件名",
            "state: 状态（0、待生产；1、生产中；2、生产完成；3、生产失败）",
            "failReason: 失败的原因",
            "url: OSS地址",
            "createTime: 创建时间",
            "startTime: 开始时间",
            "endTime: 结束时间",
            "takeTime: 耗费时长（毫秒）",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, @RequestBody MakeFilmVideoPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MakeFilmVideoPO makeFilmVideo = makeFilmVideoService.queryById(params);
        return ResultVO.success(makeFilmVideo);
    }

    // @ApiName(value = "视频-添加", folder = {"影片库-视频"})
    // @ApiParamsIn({
    //         "filmId:1:int:影片ID",
    //         "filmType:1:int:影片类型（1、原片；2、成片）",
    //         "seriesNum:1:int:剧集数（第几集）",
    //         "name:1:str:文件名",
    //         "url:1:str:OSS地址",
    // })
    // @ApiParamsOut({
    //         "status:ok",
    // })
    // @PostMapping("/insert")
    // public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated MakeFilmVideoPO params) {
    //     SessionVO sessionVO = getSessionVO(request);
    //     params.setCreatorId(sessionVO.getUserId());
    //     params.setUpdatorId(sessionVO.getUserId());
    //     MethodVO methodVO = makeFilmVideoService.insert(params);
    //     return ResultVO.fromMethodVO(methodVO);
    // }

    @ApiName(value = "视频-更新/替换", folder = {"影片库-视频"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "url:1:str:OSS地址",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody @Validated MakeFilmVideoPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = makeFilmVideoService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "视频-删除", folder = {"影片库-视频"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, @RequestBody MakeFilmVideoPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = makeFilmVideoService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "视频-查询已生成的语种", folder = {"影片库-视频"})
    @ApiParamsIn(value = {
            "filmId:1:int:影片ID",
            "filmType:1:int:影片类型（1、原片；2、成片）",
    })
    @ApiParamsOut(value = {
            "name:名称",
            "code:编码（00代表无字幕视频）",
    })
    @PostMapping("/getLanguages")
    public ResultVO<?> getLanguages(HttpServletRequest request, @RequestBody MakeFilmVideoPO params) {
        List<FastLanguagePO> list = new ArrayList<>();
        list.add(new FastLanguagePO("00", "无字幕"));
        
        Set<String> codes = makeFilmVideoService.getLangCodes(params.getFilmId(), params.getFilmType());
        if (CollUtil.isNotEmpty(codes)) {
            for (String code : codes) {
                if (!Objects.equals(code, "00")) {
                    list.add(new FastLanguagePO(code, LanguageEnum.ofCode(code).getName()));
                }
            }
        }
        return ResultVO.success(list);
    }

}
