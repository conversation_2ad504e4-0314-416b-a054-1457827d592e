/*
 * Powered By fast.up
 */
package com.fast.controller.make;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.make.MakeFilePO;
import com.fast.service.make.MakeFileService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/makeFile")
public class MakeFileController extends BaseController {

    @Autowired
    private MakeFileService makeFileService;

    @ApiName(value = "文件-查询列表", folder = {"文件库"})
    @ApiParamsIn({
            "batchId:0:str:批次ID",
            "folder:0:str:文件夹",
            "name:0:str:文件名",
            "path:0:str:磁盘路径",
            "url:0:str:OSS地址",
            "state:0:str:状态（0、上传中；1、上传完成；2、上传失败）",
            "platform:1:str:平台（1、YouTube）",
            "createTimeStr:0:str:创建时间区间（yyyy-MM-dd - yyyy-MM-dd）",
            "page:1:int:页码",
            "limit:1:int:每页条数",
    })
    @ApiParamsOut({
            "list 》id: 主键ID",
            "list 》batchId: 批次ID",
            "list 》folder: 文件夹",
            "list 》name: 文件名",
            "list 》path: 磁盘路径",
            "list 》url: OSS地址",
            "list 》state: 状态（0、上传中；1、上传完成；2、上传失败）",
            "list 》platform: 平台（1、YouTube）",
            "list 》createTime: 创建时间",
    })
    @PostMapping("/getList")
    public ResultVO<?> getList(HttpServletRequest request, @RequestBody MakeFilePO params, @RequestBody PageVO pageVO) {
        // 解析创建时间
        if (StrUtil.isNotBlank(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
        }
        return makeFileService.queryPageList(params, pageVO);
    }

    @ApiName(value = "文件-查询单个详情", folder = {"文件库"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "id: 主键ID",
            "batchId: 批次ID",
            "folder: 文件夹",
            "name: 文件名",
            "path: 磁盘路径",
            "url: OSS地址",
            "state: 状态（0、上传中；1、上传完成；2、上传失败）",
            "platform: 平台（1、YouTube）",
            "createTime: 创建时间",
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, @RequestBody MakeFilePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MakeFilePO makeFile = makeFileService.queryById(params);
        return ResultVO.success(makeFile);
    }

    @ApiName(value = "文件-添加", folder = {"文件库"})
    @ApiParamsIn({
            "batchId:1:str:批次ID",
            "folder:1:str:文件夹",
            "name:1:str:文件名",
            "path:1:str:磁盘路径",
            "url:1:str:OSS地址",
            "state:1:int:状态（0、上传中；1、上传完成；2、上传失败）",
            "platform:1:int:平台（1、YouTube）",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/insert")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated MakeFilePO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = makeFileService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "文件-更新", folder = {"文件库"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
            "batchId:1:str:批次ID",
            "folder:1:str:文件夹",
            "name:1:str:文件名",
            "path:1:str:磁盘路径",
            "url:1:str:OSS地址",
            "state:1:int:状态（0、上传中；1、上传完成；2、上传失败）",
            "platform:1:int:平台（1、YouTube）",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/update")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody @Validated MakeFilePO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = makeFileService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "文件-删除", folder = {"文件库"})
    @ApiParamsIn({
            "encryptionId:1:str:加密ID",
    })
    @ApiParamsOut({
            "status:ok",
    })
    @PostMapping("/delete")
    public ResultVO<?> delete(HttpServletRequest request, @RequestBody MakeFilePO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = makeFileService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
