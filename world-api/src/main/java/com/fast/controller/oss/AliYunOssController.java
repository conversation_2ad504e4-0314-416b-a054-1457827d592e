package com.fast.controller.oss;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.framework.config.AliYunOssConfig;
import com.fast.po.oss.OssFileInfo;
import com.fast.po.oss.UploadParamsDTO;
import com.fast.service.oss.OssFileService;
import com.fast.service.oss.OssService;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.http.HttpUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 阿里云 OSS 文件存储
 *
 * <AUTHOR>
 * @date 2021-10-02
 */
@RestController
@RequestMapping("ali/oss")
@ConditionalOnProperty(name = "fast.deploy_platform", havingValue = "1")
public class AliYunOssController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(AliYunOssController.class);

    private static final long EXPIRE_TIME = 60;// 临时授权有效期(秒)
    private static final String HEAD1 = "Access-Control-Allow-Origin";
    private static final String HEAD2 = "Access-Control-Allow-Methods";

    @Autowired
    private OssService ossService;
    @Autowired
    private AliYunOssConfig ossConfig;
    @Autowired
    private OssFileService ossFileService;

    /**
     * 获取临时授权上传的参数
     */
    @GetMapping("/getUploadParams")
    public ResultVO<?> getUploadParams(HttpServletRequest request, HttpServletResponse response,
                                       Integer fileType,
                                       String fileName,
                                       String fileSuffix,
                                       Integer bizId,
                                       String languageCode,
                                       String subLang,
                                       Integer isCont) {
        try {
            SessionVO sessionVO = getSessionVO(request);
            if (sessionVO == null) {
                return ResultVO.error(StaticStr.USER_LOGIN_HAS_EXPIRED);
            }
            if (fileType == 5 && (StrUtil.isBlank(fileName) || bizId == null)) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            // 上传并返回访问地址
            UploadParamsDTO paramsDTO = ossService.getTempUploadParams(Objects.equals(isCont, 1), sessionVO.getRetailId(), fileType, fileName, fileSuffix, bizId, languageCode + "-" + subLang, EXPIRE_TIME, TimeUnit.SECONDS)
                    .toJavaObject(UploadParamsDTO.class);
            if (paramsDTO != null) {
                response.setHeader(HEAD1, "*");
                response.setHeader(HEAD2, "GET, POST");
                return new ResultVO<>(paramsDTO);
            }
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return ResultVO.error(e.getMessage());
        }
        return ResultVO.error("获取临时授权上传的参数-失败");
    }

    @PostMapping("/{fileType}/upload")
    public ResultVO<?> upload(HttpServletRequest request, @PathVariable Integer fileType, Integer isCont, @RequestParam("file") MultipartFile file) {
        return ResultVO.success(ossService.uploadFile(Objects.equals(isCont, 1), 0, fileType, file));
    }

    /**
     * 上传回调
     *
     * @param callbackBody 回调内容
     * @return ok
     */
    @RequestMapping("/wap/callback")
    public ResultVO<?> callback(HttpServletRequest request, @RequestBody String callbackBody) throws Exception {
        // 验证回调的真实性
        boolean ret = verifyOSSCallbackRequest(request, callbackBody);
        if (ret) {
            // 解码URL
            callbackBody = URLDecoder.decode(callbackBody, StandardCharsets.UTF_8);
            JSONObject data = new JSONObject();
            data.put("scheme", this.ossConfig.getScheme());
            data.put("src", this.ossConfig.getEndpoint());
            String[] query = callbackBody.split("&");
            for (String q : query) {
                String[] ps = q.split("=");
                if (ps.length == 2) {
                    data.put(ps[0], ps[1]);
                }
            }
            String key = data.getString("objectKey");
            data.put("wholeUrl", getWholeUrl(this.ossConfig, key));

            // 记录oss文件信息
            OssFileInfo info = JsonUtil.toJavaObject(data, OssFileInfo.class);
            ossFileService.addOssFileByCallBack(info);

            return new ResultVO<>(data);
        }
        return ResultVO.error("回调验证失败");
    }

    /**
     * 获取文件全路径地址
     *
     * @param config oss配置
     * @param key    文件名
     * @return 文件地址URL
     */
    private static String getWholeUrl(AliYunOssConfig config, String key) {
        return config.getHost() + key;
    }

    /**
     * 验证回调信息
     *
     * @param ossCallbackBody 信息体
     * @return 验证结果
     * @throws Exception e
     */
    private boolean verifyOSSCallbackRequest(HttpServletRequest request, String ossCallbackBody) throws Exception {
        String authorizationInput = request.getHeader("Authorization");
        String pubKeyInput = request.getHeader("x-oss-pub-key-url");
        byte[] authorization = BinaryUtil.fromBase64String(authorizationInput);
        byte[] pubKey = BinaryUtil.fromBase64String(pubKeyInput);
        String pubKeyAddr = new String(pubKey);
        if (!pubKeyAddr.startsWith("http://gosspublic.alicdn.com/") && !pubKeyAddr.startsWith("https://gosspublic.alicdn.com/")) {
            log.error("pub key addr must be oss address");
            return false;
        }
        String retString = HttpUtil.get(pubKeyAddr);
        if (StrUtil.isBlank(retString)) {
            log.error("verifyOSSCallbackRequest is null");
            throw new RuntimeException("verifyOSSCallbackRequest is null");
        }
        retString = retString.replace("-----BEGIN PUBLIC KEY-----", "").replace("-----END PUBLIC KEY-----", "");
        String queryString = request.getQueryString();
        String uri = request.getRequestURI();
        String authStr = URLDecoder.decode(uri, StandardCharsets.UTF_8);
        if (queryString != null && !"".equals(queryString)) {
            authStr += "?" + queryString;
        }
        authStr += "\n" + ossCallbackBody;
        return doCheck(authStr, authorization, retString);
    }

    /**
     * 签名验证
     *
     * @param content
     * @param sign
     * @param publicKey
     * @return
     * @throws InvalidKeyException
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     * @throws SignatureException
     */
    private static boolean doCheck(String content, byte[] sign, String publicKey) throws InvalidKeyException,
            NoSuchAlgorithmException, InvalidKeySpecException, SignatureException {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] encodedKey = BinaryUtil.fromBase64String(publicKey);
        PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
        Signature signature = Signature.getInstance("MD5withRSA");
        signature.initVerify(pubKey);
        signature.update(content.getBytes());
        return signature.verify(sign);
    }
}
