package com.fast.controller.oss;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.oss.LsyUploadParamsDTO;
import com.fast.po.oss.OssFile;
import com.fast.service.oss.OssFileService;
import com.fast.service.oss.OssService;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@RestController
@ConditionalOnProperty(name = "fast.deploy_platform", havingValue = "2")
public class LsyTosController extends BaseController {

    @Autowired
    private OssService ossService;
    private static final long EXPIRE_TIME = 60;// 临时授权有效期(秒)
    @Autowired
    private OssFileService ossFileService;

    /**
     * 获取临时授权上传的参数
     *
     * @param response
     * @param fileType
     * @return
     */
    @GetMapping("/lsy/tos/getUploadParams")
    public ResultVO<?> getUploadParams(HttpServletRequest request, HttpServletResponse response,
                                       Integer fileType,
                                       String fileName,
                                       String fileSuffix,
                                       Integer bizId,
                                       String languageCode,
                                       String subLang,
                                       Integer isCont) {
        try {
            SessionVO sessionVO = getSessionVO(request);
            if (sessionVO == null) {
                return ResultVO.error(StaticStr.USER_LOGIN_HAS_EXPIRED);
            }
            if (!Objects.equals(isCont, 1) && fileType == 5 && (StrUtil.isBlank(fileName) || bizId == null || StrUtil.isBlank(languageCode) || StrUtil.isBlank(subLang))) {
                return ResultVO.error(StaticStr.INVALID_PARAM);
            }
            // 获取临时授权上传的参数
            JSONObject tempUploadParams = ossService.getTempUploadParams(Objects.equals(isCont, 1), sessionVO.getRetailId(), fileType, fileName, fileSuffix, bizId, languageCode + "-" + subLang, EXPIRE_TIME, TimeUnit.SECONDS);
            if (Objects.isNull(tempUploadParams)) {
                return ResultVO.error("获取临时授权上传的参数-失败");
            }
            LsyUploadParamsDTO result = tempUploadParams.toJavaObject(LsyUploadParamsDTO.class);
            // 上传并返回访问地址
            return new ResultVO<>(result);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return ResultVO.error(e.getMessage());
        }
    }

    @PostMapping("/lsy/tos/{fileType}/upload")
    public ResultVO<?> upload(HttpServletRequest request, @PathVariable Integer fileType, Integer isCont, @RequestParam("file") MultipartFile file) {
        return ResultVO.success(ossService.uploadFile(Objects.equals(isCont, 1), 0, fileType, file));
    }

    /**
     * 获取临时授权上传的参数
     *
     * @return
     */
    @PostMapping("/lsy/tos/saveUploadRecord")
    public ResultVO<?> getUploadParams(HttpServletRequest request, String key, Long fileSize) {
        try {
            SessionVO sessionVO = getSessionVO(request);
            if (sessionVO == null) {
                return ResultVO.error(StaticStr.USER_LOGIN_HAS_EXPIRED);
            }
            OssFile ossFile = new OssFile();
            ossFile.setFileSize(fileSize);
            ossFile.setOriginalKey(key);
            ossFile.setRetailId(sessionVO.getRetailId());
            ossFileService.insert(ossFile);
            return new ResultVO<>("保存成功");
        } catch (Exception e) {
            log.error("上传文件记录保存失败!", e);
            return ResultVO.error(e.getMessage());
        }
    }

    /**
     * 获取临时授权上传的参数
     *
     * @return
     */
    @PostMapping("/lsy/wap/callback")
    public ResultVO<?> getUploadParams(HttpServletRequest request, @RequestBody OssFile ossFile) {
        ossFile.setFileSize(ossFile.getFileSize() / 1024);
        log.info("图片上传接口回调: {}", JSON.toJSONString(ossFile));
        ossFileService.insert(ossFile);
        // 返回oss地址
        String wholeUrl = ossService.getWholeUrl(ossFile.isYtCont(), ossFile.getOriginalKey());
        LsyUploadParamsDTO lsyUploadParamsDTO = new LsyUploadParamsDTO();
        lsyUploadParamsDTO.setWholeUrl(wholeUrl);
        return new ResultVO<>(lsyUploadParamsDTO);
    }

}
