/*
 * Powered By fast.up
 */
package com.fast.controller.analysis;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.po.analysis.RetailIndexPO;
import com.fast.service.analysis.RetailIndexService;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.JedisLock;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.common.TimeInDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 分销商首页
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/analysis/retail")
public class RetailIndexController extends BaseController {

    @Autowired
    private RetailIndexService retailIndexService;

    /**
     * 充值花费数据汇总
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "充值花费数据汇总", folder = {StaticFolder.FOLDER_RETAIL_INDEX})
    @ApiParamsIn({
            "timeIn:1:int:日期选择：1 今天; 2 昨天",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
    })
    @ApiParamsOut({
            "results>>adMoneyConsume:总花费",
            "results>>rechargeMoneySum:总充值",
            "results>>rechargeMoneyProfitSum:总实到",
            "results>>roi:roi",
    })
    @Slave
    @RequestMapping(value = "/getRechargeConsume")
    public ResultVO<?> getRechargeConsume(HttpServletRequest request, RetailIndexPO params, TimeInDto dto) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.RETAIL_RECHARGE_CONSUME_LOCK + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            params.setContentType(sessionVO.getContentType());
            if (sessionVO.getRetailId() > 0) {
                params.setRetailId(sessionVO.getRetailId());
            }
            if (dto.getTimeIn() == null) {
                dto.setTimeIn(1);
            }

            if (dto.getTimeIn() > -1) {
                doTimeIn(dto);

                params.setCreateTimeS(dto.getStartTime());
                params.setCreateTimeE(dto.getEndTime());
            }

            return retailIndexService.getRechargeConsume(params);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    /**
     * 核心数据汇总
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "核心数据汇总", folder = {StaticFolder.FOLDER_RETAIL_INDEX})
    @ApiParamsIn({
            "timeIn:0:int:日期选择：1 今天; 2 昨天; -1 自定义日期",
            "createTimeStr:0:str:统计时间 yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "results>>numDay:新增用户数",
            "results>>numDayRech:新增用户-充值人数",
            "results>>numDayOrder:新增用户-充值笔数",
            "results>>moneyDay:新增用户-充值金额",
            "results>>moneyProfitDay:新增用户-实到金额",
            "results>>memberAddPerMoney:新用户笔单价",
            "results>>memberAddPayRate:新用户付费率",
            "results>>memberAddARPU:新用户ARPU",
            "results>>rechargeMoneySum:总充值金额",
            "results>>rechargeMoneyProfitSum:总实到金额",
            "results>>rechargeOrderSum:总充值笔数",
            "results>>coinConsume:K币消耗数",
            "results>>coinGiveConsume:K币消耗数-赠送",
    })
    @Slave
    @RequestMapping(value = "/getCoreAnalysis")
    public ResultVO<?> getCoreAnalysis(HttpServletRequest request, RetailIndexPO params, TimeInDto dto) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.RETAIL_CORE_ANALYSIS_LOCK + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            params.setContentType(sessionVO.getContentType());
            if (sessionVO.getRetailId() > 0) {
                params.setRetailId(sessionVO.getRetailId());
            }
            if (dto.getTimeIn() == null) {
                dto.setTimeIn(1);
            }

            if (dto.getTimeIn() > -1) {
                doTimeIn(dto);

                params.setCreateTimeS(dto.getStartTime());
                params.setCreateTimeE(dto.getEndTime());
            } else if (notEmpty(params.getCreateTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
                params.setCreateTimeS(date.get(0));
                params.setCreateTimeE(date.get(1));
            }

            return retailIndexService.getCoreAnalysis(params);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }
}
