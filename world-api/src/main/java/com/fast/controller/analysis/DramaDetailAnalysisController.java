/*
 * Powered By fast.up
 */
package com.fast.controller.analysis;

import com.alibaba.fastjson.JSONObject;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.po.fee.FastFeeRulePO;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.service.analysis.DramaDetailAnalysisExportService;
import com.fast.service.analysis.DramaDetailAnalysisService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.retail.FastRetailService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 短剧整体分析
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/analysis")
public class DramaDetailAnalysisController extends BaseController {

    @Autowired
    private DramaDetailAnalysisService detailAnalysisService;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private DramaDetailAnalysisExportService exportService;
    @Autowired
    private FastRetailService fastRetailService;
    @Autowired
    private FastMiniMapper fastMiniMapper;

    /**
     * 整体分析-核心数据概览
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "整体分析-核心数据概览", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "dramaId:1:int:短剧id-数字",
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "removeMountLinkFlag:0:int:去除挂载链接 1去掉(默认) 0不去"
    })
    @ApiParamsOut({
            "results>>type:1=注册人数;2=观看人数;3=K币消耗;4=完播率;5=广告消耗(自代投);6=充值金额(自代投);7=充值人数;8=ROI;16=新增充值金额(自代投);" +
                    "17=新增充值人数;18=新增ROI(自代投);23=充值金额(独立分销商);24=新增充值金额(独立分销商);",
            "results>>todayData:今日",
            "results>>totalData:总计",
            "results>>dataRate:较前一日(不含%)",
    })
    @Slave
    @RequestMapping(value = "/getDramaDetailCoreDataAnalysisList")
    public ResultVO<?> getDramaDetailCoreDataAnalysisList(HttpServletRequest request, DramaAnalysisPO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.DRAMA_DETAIL_CORE_DATA_ANALYSIS_LIST_LOCK + params.getDramaId() + ":" + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds());
            // 目前只查短剧的 有短剧id就不需要ContentType
            // params.setContentType(sessionVO.getContentType());
            if (sessionVO.getRetailId() > 0) {
                params.setRetailIds(String.valueOf(sessionVO.getRetailId()));
            }
            if (params.getDramaId() == null) {
                return ResultVO.error("短剧id不能为空");
            }
            setDramaIdContext(params);
            // 优化师和链接id搜索
            if (notEmpty(params.getAdvUserIds()) || notEmpty(params.getLinkIds())) {
                FastLinkPO query = new FastLinkPO();
                query.setAdvUserIds(params.getAdvUserIds());
                query.setIds(params.getLinkIds());
                query.setDramaId(params.getDramaId());
                query.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
                Set<Integer> linkIds = linkService.queryLinkIds(query);
                if (CollUtil.isEmpty(linkIds)) {
                    return ResultVO.success();
                }
                params.setLinkIds(StrUtil.join(linkIds));
            }
            if (notEmpty(params.getCreateTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
                params.setCreateTimeS(date.get(0));
                params.setCreateTimeE(date.get(1));
            }
            List<JSONObject> list = detailAnalysisService.getDramaDetailCoreDataAnalysisList(params, sessionVO);
            if (list == null) {
                return ResultVO.success(getDefaultListData());
            }
            return ResultVO.success(list);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }


    /**
     * 整体分析-短剧付费分析
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "整体分析-短剧付费分析", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "dramaId:1:int:短剧id-数字",
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "startNum:0:int:付费起始集(数字)",
            "noFirstVipNum:0:int:剔除VIP首充人数-复充率专属 1=是;0=否",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "feeFlag:0:int:1付费 2免费 0全部",
            "removeMountLinkFlag:0:int:去除挂载链接 1去掉(默认) 0不去"
    })
    @ApiParamsOut({
            "results>>rechargeCount:充值次数 1充,2充,3充.....10充",
            "results>>rechargeMemberCount:充值人数",
            "results>>repeatRechargeRate:复充率(第一个数据表示转定率)",
    })
    @Slave
    @RequestMapping(value = "/getDramaDetailPayAnalysisList")
    public ResultVO<?> getDramaDetailPayAnalysisList(HttpServletRequest request, DramaAnalysisPO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.DRAMA_DETAIL_PAY_ANALYSIS_LIST_LOCK + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds(), params.getAdvUserIds(), params.getLinkIds());
            params.setContentType(sessionVO.getContentType());
            if (sessionVO.getRetailId() > 0 && sessionVO.getCpUserType() == 3) {// 分销商用户才设置retailId
                params.setRetailIds(String.valueOf(sessionVO.getRetailId()));
            }
            if (params.getNoFirstVipNum() == null) {
                params.setNoFirstVipNum(0);
            }
            if (params.getDramaId() == null) {
                return ResultVO.error("短剧id不能为空");
            }
            if (params.getFeeFlag() != null && params.getFeeFlag() == 0) {
                params.setFeeFlag(null);// 查全部，去掉免费类型条件
            }
            setDramaIdContext(params);
            // 优化师和链接id搜索
            if (notEmpty(params.getAdvUserIds()) || notEmpty(params.getLinkIds())) {
                FastLinkPO query = new FastLinkPO();
                query.setAdvUserIds(params.getAdvUserIds());
                query.setIds(params.getLinkIds());
                query.setDramaId(params.getDramaId());
                query.setFeeFlag(params.getFeeFlag());
                query.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
                Set<Integer> linkIds = linkService.queryLinkIds(query);
                if (CollUtil.isEmpty(linkIds)) {
                    return ResultVO.success(getDefaultListData());
                }
                params.setLinkIds(StrUtil.join(linkIds));
            }
            Set<Integer> linkIds = new HashSet<>();
            if (notEmpty(params.getLinkIds())) {
                Set<Integer> ids = CollUtil.parseIntStr2Set(params.getLinkIds());
                linkIds.addAll(ids);
            }
            // 付费起始集-查询关联的链接id
            if (biggerZero(params.getStartNum())) {
                Set<Integer> ids = linkService.queryLinkIdsByStartNum(params.getDramaId(), params.getStartNum());
                if (CollUtil.isEmpty(ids)) {
                    return ResultVO.success(getDefaultListData());
                } else if (CollUtil.hasContent(linkIds)) {
                    linkIds = CollUtil.intersection(linkIds, ids);
                } else {
                    linkIds.addAll(ids);
                }
                if (CollUtil.isEmpty(linkIds)) {
                    return ResultVO.success(getDefaultListData());
                }
                params.setLinkIds(StrUtil.join(linkIds));
            }
            // 用户注册时间
            if (notEmpty(params.getRegisterTimeStr())) {
                List<Date> date = null;
                if (params.getRegisterTimeStr().length() < 30) {
                    date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
                } else {
                    date = DateUtil.getStartEndDateTime(params.getRegisterTimeStr());
                }
                params.setRegisterTimeS(date.get(0));
                params.setRegisterTimeE(date.get(1));
            }
            List<DramaAnalysisPO> list = detailAnalysisService.getDramaDetailPayAnalysisListV2(params);
            if (list == null) {
                return ResultVO.success(getDefaultListData());
            }
            return ResultVO.success(list);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    /**
     * 整体分析-剧集分析-留存分析
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "整体分析-剧集分析-留存分析", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "dramaId:1:int:短剧id-数字",
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "watchTimeStr:0:str:观看时间：yyyy-MM-dd HH：mm：ss - yyyy-MM-dd HH：mm：ss",
            "feeFlag:0:int:1付费 2免费 0全部",
            "playType:0:int:0通用1腾讯2抖音3快手",
            "removeMountLinkFlag:0:int:去除挂载链接 1去掉(默认) 0不去"
    })
    @ApiParamsOut({
            "results>>seriesNum:剧集号(1,2,3,4.......100,101...)",
            "results>>remainRate:留存率",
    })
    @Slave
    @RequestMapping(value = "/getDramaDetailRemainAnalysisList")
    public ResultVO<?> getDramaDetailRemainAnalysisList(HttpServletRequest request, DramaAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds(), params.getAdvUserIds(), params.getLinkIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0 && sessionVO.getCpUserType() == 3) {// 分销商用户才设置retailId
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getDramaId() == null) {
            return ResultVO.error("短剧id不能为空");
        }
        if (params.getFeeFlag() != null && params.getFeeFlag() == 0) {
            params.setFeeFlag(null);// 查全部，去掉免费类型条件
        }
        setDramaIdContext(params);
        if (notEmpty(params.getWatchTimeStr())) {
            List<Date> date = DateUtil.getStartEndDateTime(params.getWatchTimeStr());
            params.setWatchTimeS(date.get(0));
            params.setWatchTimeE(date.get(1));
        }
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = null;
            if (params.getRegisterTimeStr().length() < 30) {
                date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            } else {
                date = DateUtil.getStartEndDateTime(params.getRegisterTimeStr());
            }
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));
        }
        Set<Integer> miniIdSet = new HashSet<>();
        if (notEmpty(params.getMiniIds())) {
            miniIdSet.addAll(CollUtil.parseIntStr2Set(params.getMiniIds()));
        }
        if (params.getPlayType() != null) { // 0通用1腾讯2抖音3快手
            Integer type = params.getPlayType();
            // 1：微信小程序;2抖音小程序;3H5 4快手
            if (params.getPlayType() == 3) {
                type = 4;
            }
            FastMiniPO queryType = new FastMiniPO();
            queryType.setType(type);
            List<Integer> miniIdList = fastMiniMapper.querySimpleMiniIds(queryType);
            if (CollUtil.isNotEmpty(miniIdList)) {
                miniIdSet.addAll(miniIdList);
            }
        }
        if (CollUtil.isNotEmpty(miniIdSet)) {
            params.setMiniIds(CollUtil.convertIntSetToString(miniIdSet));
        }
        List<?> list = detailAnalysisService.getDramaDetailRemainAnalysisListV2(params);
        if (list == null) {
            return ResultVO.success(getDefaultListData());
        }
        return ResultVO.success(list);
    }

    /**
     * 整体分析-剧集分析-留存分析-对比
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "整体分析-剧集分析-留存分析-对比", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "dramaId:1:int:短剧id-数字",
            "contrastType:1:int:对比维度:1=分销商id;2=公众号id;3=优化师id;4=链接id",
            "retailIds:0:str:分销商id-数字(多个逗号分隔)需要对比的分销商",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "officialIds:0:str:公众号id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "watchTimeStr:0:str:观看时间：yyyy-MM-dd HH：mm：ss - yyyy-MM-dd HH：mm：ss",
            "removeMountLinkFlag:0:int:去除挂载链接 1去掉(默认) 0不去"
    })
    @ApiParamsOut({
            "results>>id:维度id-比如分销商id",
            "results>>name:维度名称-比如分销商名称",
            "results>>list>>seriesNum:剧集号(1,2,3,4.......100,101...)",
            "results>>list>>remainRate:留存率",
    })
    @Slave
    @RequestMapping(value = "/getDramaDetailRemainAnalysisContrast")
    public ResultVO<?> getDramaDetailRemainAnalysisContrast(HttpServletRequest request, DramaAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds(), params.getAdvUserIds(), params.getLinkIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            FastRetailPO param = new FastRetailPO();
            param.setId(sessionVO.getRetailId());
            FastRetailPO fastRetailPO = fastRetailService.queryById(param);
            if (fastRetailPO.getRetailFlag().equals(1)) {
                params.setRetailId(sessionVO.getRetailId());
            }
        }
        if (params.getDramaId() == null) {
            return ResultVO.error("短剧id不能为空");
        }
        setDramaIdContext(params);
        if (params.getContrastType() == null) {
            params.setContrastType(1);
        }
        switch (params.getContrastType()) {
            case 1:
                if (isBlank(params.getRetailIds())) {
                    return ResultVO.error("对比的分销商不能为空");
                }
                if (!params.getRetailIds().contains(StaticVar.COMMA)) {
                    return ResultVO.error("对比的分销商至少要选择2个");
                }
                break;
            case 2:
                if (isBlank(params.getOfficialIds())) {
                    return ResultVO.error("对比的公众号不能为空");
                }
                if (!params.getOfficialIds().contains(StaticVar.COMMA)) {
                    return ResultVO.error("对比的公众号至少要选择2个");
                }
                break;
            case 3:
                if (isBlank(params.getAdvUserIds())) {
                    return ResultVO.error("对比的优化师不能为空");
                }
                if (!params.getAdvUserIds().contains(StaticVar.COMMA)) {
                    return ResultVO.error("对比的优化师至少要选择2个");
                }
                break;
            case 4:
                if (isBlank(params.getLinkIds())) {
                    return ResultVO.error("对比的链接不能为空");
                }
                if (!params.getLinkIds().contains(StaticVar.COMMA)) {
                    return ResultVO.error("对比的链接至少要选择2个");
                }
                break;
        }

        // 用户注册时间
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));
        }
        if (notEmpty(params.getWatchTimeStr())) {
            List<Date> date = DateUtil.getStartEndDateTime(params.getWatchTimeStr());
            params.setWatchTimeS(date.get(0));
            params.setWatchTimeE(date.get(1));
        }
        List<JSONObject> list = detailAnalysisService.getDramaDetailRemainAnalysisContrast(params);
        if (list == null) {
            return ResultVO.success(getDefaultListData());
        }
        return ResultVO.success(list);
    }

    /**
     * 整体分析-剧集分析-留存分析-对比-规则显示
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "整体分析-剧集分析-留存分析-对比-规则显示", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "dramaId:1:int:短剧id-数字",
            "contrastType:1:int:对比维度:2=公众号id;4=链接id",
            "officialIds:0:str:公众号id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
    })
    @ApiParamsOut({
            "results>>officialName:公众号名称",
            "results>>startNum:付费起始剧集",
            "results>>followType:关注规则 1强关2引导关注3不关注",
            "results>>followNum:关注剧集",
    })
    @Slave
    @RequestMapping(value = "/getDramaDetailRemainRuleContrast")
    public ResultVO<?> getDramaDetailRemainRuleContrast(HttpServletRequest request, DramaAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getOfficialIds(), params.getLinkIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            FastRetailPO param = new FastRetailPO();
            param.setId(sessionVO.getRetailId());
            FastRetailPO fastRetailPO = fastRetailService.queryById(param);
            if (fastRetailPO.getRetailFlag().equals(1)) {
                params.setRetailId(sessionVO.getRetailId());
            }
        }
        if (params.getDramaId() == null) {
            return ResultVO.error("短剧id不能为空");
        }
        if (params.getContrastType() == null) {
            params.setContrastType(2);
        }
        switch (params.getContrastType()) {
            case 2:
                if (isBlank(params.getOfficialIds())) {
                    return ResultVO.error("对比的公众号id不能为空");
                }
                break;
            case 4:
                if (isBlank(params.getLinkIds())) {
                    return ResultVO.error("对比的链接id不能为空");
                }
                break;
            default:
                return ResultVO.error("对比的维度不合法");
        }

        List<FastFeeRulePO> list = detailAnalysisService.getDramaDetailRemainRuleContrast(params);
        if (list == null) {
            return ResultVO.success(getDefaultListData());
        }
        return ResultVO.success(list);
    }

    /**
     * 整体分析-剧集分析-跳出分析
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "整体分析-剧集分析-跳出分析", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "dramaId:1:int:短剧id-数字",
            "retailIds:0:str:分销商id-数字(多个逗号分隔)需要对比的分销商",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "watchTimeStr:0:str:观看时间：yyyy-MM-dd HH：mm：ss - yyyy-MM-dd HH：mm：ss",
            "seriesNum:0:int:第N集-数字",
            "openValidAnalysis:0:int:开启有效分析:1=是;0=否",
            "removeMountLinkFlag:0:int:去除挂载链接 1去掉(默认) 0不去"
    })
    @ApiParamsOut({
            "results>>skipTime:秒数(1,2,3,4.......100,101...)",
            "results>>skipRate:跳出率",
            "results>>skipMemberCount:跳出人数",
    })
    @Slave
    @RequestMapping(value = "/getDramaDetailSkipAnalysisList")
    public ResultVO<?> getDramaDetailSkipAnalysisList(HttpServletRequest request, DramaAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds(), params.getAdvUserIds(), params.getLinkIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            FastRetailPO param = new FastRetailPO();
            param.setId(sessionVO.getRetailId());
            FastRetailPO fastRetailPO = fastRetailService.queryById(param);
            if (fastRetailPO.getRetailFlag().equals(1)) {
                params.setRetailId(sessionVO.getRetailId());
            }
        }
        if (params.getDramaId() == null) {
            return ResultVO.error("短剧id不能为空");
        }
        setDramaIdContext(params);
        if (params.getSeriesNum() == null) {
            return ResultVO.error("剧集号不能为空");
        }
        if (params.getOpenValidAnalysis() == null) {
            params.setOpenValidAnalysis(1);
        }
        // 优化师和链接id搜索
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));
        }
        if (notEmpty(params.getWatchTimeStr())) {
            List<Date> date = DateUtil.getStartEndDateTime(params.getWatchTimeStr());
            params.setWatchTimeS(date.get(0));
            params.setWatchTimeE(date.get(1));
        }
        List<?> list = detailAnalysisService.getDramaDetailSkipAnalysisList(params);
        if (list == null) {
            return ResultVO.success(getDefaultListData());
        }
        return ResultVO.success(list);
    }

    /**
     * 整体分析-剧集分析-剧集数据明细
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "整体分析-剧集分析-剧集数据明细", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "dramaId:1:int:短剧id-数字",
            "retailIds:0:str:分销商id-数字(多个逗号分隔)需要对比的分销商",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "watchTimeStr:0:str:观看时间：yyyy-MM-dd HH：mm：ss - yyyy-MM-dd HH：mm：ss",
            "sortType:0:int:排序字段：1=观看人数;2=留存率;3=单集跳出率;4=单集消耗K币;5=单集完播人数;6=完播率;8=是否付费;9=订阅人数;",
            "sortOrder:0:int:排序顺序：1=倒序;2=正序",
            "platformFlag:0:int:1 平台 0 我的短剧",
            "payResultExt:0:int: 0=全部;1=K币消费;2=vip免费看;",
            "removeMountLinkFlag:0:int:去除挂载链接 1去掉(默认) 0不去"
    })
    @ApiParamsOut({
            "results>>seriesNum:集号",
            "results>>watchMemberCount:观看人数",
            "results>>remainRate:留存率",
            "results>>skipRate:跳出率",
            "results>>coinConsume:K币消费总数",
            "results>>coinGiveConsume:金币消费赠送数",
            "results>>watchFinishMemberCount:完播人数",
            "results>>watchFinishRate:完播率",
    })
    @Slave
    @RequestMapping(value = "/getDramaDetailSeriesAnalysisList")
    public ResultVO<?> getDramaDetailSeriesAnalysisList(HttpServletRequest request, DramaAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds(), params.getAdvUserIds(), params.getLinkIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            FastRetailPO param = new FastRetailPO();
            param.setId(sessionVO.getRetailId());
            FastRetailPO fastRetailPO = fastRetailService.queryById(param);
            if (fastRetailPO.getRetailFlag().equals(1)) {
                params.setRetailId(sessionVO.getRetailId());
            }
        }
        if (params.getDramaId() == null) {
            return ResultVO.error("短剧id不能为空");
        }
        setDramaIdContext(params);

        if (params.getPayResultExt() != null && params.getPayResultExt() == 0) {
            params.setPayResultExt(null);
        }
        if (notEmpty(params.getWatchTimeStr())) {
            List<Date> date = DateUtil.getStartEndDateTime(params.getWatchTimeStr());
            params.setWatchTimeS(date.get(0));
            params.setWatchTimeE(date.get(1));
        }
        // 处理时间
        if (notEmpty(params.getLinkTimeStr())) {
            params.setLinkTimeSearch(1);
            List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
            params.setLinkTimeS(date.get(0));
            params.setLinkTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("染色时间范围不能超过90天");
            }
        }
        if (params.getSortType() == null) {
            params.setSortType(0);
        }
        if (params.getSortOrder() == null) {
            params.setSortOrder(1);
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = exportService.exportDramaDetailSeriesAnalysisList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        } else {
            List<?> list = detailAnalysisService.getDramaDetailSeriesAnalysisList(sessionVO, params);
            if (list == null) {
                return ResultVO.success(getDefaultListData());
            }
            return ResultVO.success(list);
        }
    }

//    /**
//     * 核心数据分析
//     *
//     * @return
//     */
//    @ApiName(value = "整体分析-核心数据概览(付费、免费、总数据)", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
//    @ApiParamsIn({
//            "dramaId:1:int:短剧id-数字",
//            "feeFlag:1:int:1、付费 2、免费 0、全部",
//            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
//            "advUserIds:0:str:优化师(投手)id",
//            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
//            "linkIds:0:str:链接id-数字(多个逗号分隔)"
//    })
//    @ApiParamsOut({
//            "results>>type:1=注册人数;2=观看人数;3=K币消耗总值;4=充值人数(新增);5=充值人数(总计);6=投放消耗(自代投);7=实到充值金额(自代投-新增);" +
//                    "8=实到充值金额(自代投-总计);9=实到充值金额(分销商-新增);10=实到充值金额(分销商-总计);11=新增ROI(自代投); 12=广告收益人数;" +
//                    "13=充值实到+广告收益(自代投-新增);14=充值实到+广告收益(自代投-总计);15=充值实到+广告收益(分销商-新增);" +
//                    "16=充值实到+广告收益(分销商-总计);17=完播率",
//            "results>>todayData:今日",
//            "results>>totalData:总计",
//            "results>>dataRate:较前一日(不含%)",
//    })
//    @Slave
//    @RequestMapping(value = "/getDramaDetailCoreDataAnalysisListV1")
//    public ResultVO getDramaDetailCoreDataAnalysisListV1(HttpServletRequest request, DramaAnalysisPO params) {
//        SessionVO sessionVO = getSessionVO(request);
//        if (params.getDramaId() == null) {
//            return ResultVO.error("短剧id不能为空");
//        }
//        if (params.getFeeFlag() == null) {
//            return ResultVO.error("渠道类型不能为空");
//        }
//        // 加锁:同一个用户不能同时操作
//        final JedisLock lock = new JedisLock(StaticVar.DRAMA_DETAIL_CORE_DATA_ANALYSIS_LIST_LOCK + sessionVO.getUserId());
//        try {
//            if (!lock.lock()) {
//                return ResultVO.error(StaticStr.ERROR_001);
//            }
//            StrUtil.checkMysqlInData(params.getRetailIds(), params.getAdvUserIds(),
//                    params.getMiniIds(), params.getLinkIds());
//            params.setContentType(sessionVO.getContentType());
//            if (sessionVO.getRetailId() > 0 && sessionVO.getCpUserType() == 3) {    //分销商平台
//                params.setRetailIds(String.valueOf(sessionVO.getRetailId()));
//            }
//            if (params.getFeeFlag() == 1) {       //付费链路
//                params.setResultSets(StaticVar.NORMAL_LINK_RESULT_SET);
//            } else if (params.getFeeFlag() == 2) { //免费链路
//                params.setResultSets(StaticVar.FREE_LINK_RESULT_SET);
//            } else if (params.getFeeFlag() == 0) { //总计
//                params.setResultSets(StaticVar.TOTAL_LINK_RESULT_SET);
//                params.setFeeFlag(null);
//            } else {
//                return ResultVO.error("参数错误，渠道类型不能为空");
//            }
//            return ResultVO.success(detailAnalysisService.getDramaDetailCoreDataAnalysisListV1(params));
//        } catch (Exception e) {
//            log.error("error:", e);
//            throw new MyException(e);
//        } finally {
//            lock.release();
//        }
//    }

//    /**
//     * 整体分析-广告分析
//     *
//     * @return
//     */
//    @ApiName(value = "整体分析-广告分析", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
//    @ApiParamsIn({
//            "dramaId:1:int:短剧id-数字",
//            "registerTimeStart:1:str:注册起始时间",
//            "registerTimeEnd:1:str:注册结束时间",
//            "watchTimeStart:1:str:观看起始时间",
//            "watchTimeEnd:1:str:观看结束时间",
//            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
//            "advUserIds:0:str:优化师(投手)id",
//            "miniIds:0:str:小程序id-数字(多个逗号分隔)",
//            "linkIds:0:str:链接id-数字(多个逗号分隔)"
//    })
//    @ApiParamsOut({
//            "results>>date:横轴时间（观看时间，当日是小时数，跨天是日期）",
//            "results>>adWatchNum:广告观看人数",
//            "results>>adRechargeNum:产生受益人数",
//            "results>>adRechargeRatio:广告观看率",
//    })
//    @Slave
//    @RequestMapping(value = "/freeLinkAdvertisingAnalysis")
//    public ResultVO freeLinkAdvertisingAnalysis(HttpServletRequest request, DramaAnalysisPO params) {
//        if (params.getDramaId() == null) {
//            return ResultVO.error("短剧id不能为空");
//        }
//        if (StrUtil.isEmpty(params.getRegisterTimeStart()) || StrUtil.isEmpty(params.getRegisterTimeEnd())) {
//            return ResultVO.error("用户注册时间不能为空");
//        }
//        if (StrUtil.isEmpty(params.getWatchTimeStart()) || StrUtil.isEmpty(params.getWatchTimeEnd())) {
//            return ResultVO.error("用户观看时间不能为空");
//        }
//        SessionVO sessionVO = getSessionVO(request);
//        StrUtil.checkMysqlInData(params.getRetailIds(), params.getAdvUserIds(), params.getMiniIds(), params.getLinkIds());
//        params.setContentType(sessionVO.getContentType());
//        if (sessionVO.getRetailId() > 0) { //分销商平台
//            params.setRetailIds(String.valueOf(sessionVO.getRetailId()));
//        }
//        setDramaIdContext(params);
//        //查询渠道ids集合
//        FastLinkPO query = new FastLinkPO();
//        query.setDramaId(params.getDramaId());
//        if (notEmpty(params.getRetailIds())) {
//            query.setRetailIds(params.getRetailIds());
//        }
//        if (notEmpty(params.getAdvUserIds())) {
//            query.setAdvUserIds(params.getAdvUserIds());
//        }
//        if (notEmpty(params.getMiniIds())) {
//            query.setMiniIds(params.getMiniIds());
//        }
//        if (notEmpty(params.getLinkIds())) {
//            query.setIds(params.getLinkIds());
//        }
//        Set<Integer> linkIds = linkService.queryLinkIds(query);
//        if (CollUtil.isEmpty(linkIds)) {
//            return ResultVO.success(defaultAdAnalysisListData(params));
//        }
//        params.setLinkIds(StrUtil.join(linkIds));
//        return ResultVO.success(detailAnalysisService.freeLinkAdvertisingAnalysis(params));
//    }

//    private List defaultAnalysisListData(List<Integer> resultSets) {
//        List<JSONObject> defaultList = Lists.newArrayList();
//        for (int i = 0; i < resultSets.size(); i++) {
//            JSONObject data = new JSONObject();
//            data.fluentPut("type", resultSets.get(i))
//                    .fluentPut("todayData", 0)
//                    .fluentPut("dataRate", 0)
//                    .fluentPut("totalData", 0);
//            defaultList.add(data);
//        }
//        return defaultList;
//    }
//
//    private List defaultAdAnalysisListData(DramaAnalysisPO params) {
//        List<JSONObject> defaultList = Lists.newArrayList();
//        if (DateUtil.format09(params.getWatchTimeStart()).compareTo(DateUtil.format09(params.getWatchTimeEnd())) == 0) {
//            //同一天，按小时数分组
//            for (int i = 0; i < 24; i++) {
//                JSONObject dataItem = new JSONObject();
//                dataItem.fluentPut("date", detailAnalysisService.convertHourToStr(i));
//                dataItem.fluentPut("adWatchNum", 0);
//                dataItem.fluentPut("adRechargeNum", 0);
//                dataItem.fluentPut("adRechargeRatio", 0);
//                defaultList.add(dataItem);
//            }
//        } else {
//            //跨天，按天分组
//            Integer days = DateUtil.daysBetween(DateUtil.format09(params.getWatchTimeStart()), DateUtil.format09(params.getWatchTimeEnd()));
//            for (int i = 0; i < days; i++) {
//                Date currentDay = DateUtil.addDays(DateUtil.format09(params.getWatchTimeStart()), i);
//                JSONObject dataItem = new JSONObject();
//                dataItem.fluentPut("date", DateUtil.format09(currentDay));
//                dataItem.fluentPut("adWatchNum", 0);
//                dataItem.fluentPut("adRechargeNum", 0);
//                dataItem.fluentPut("adRechargeRatio", 0);
//                defaultList.add(dataItem);
//            }
//        }
//        return defaultList;
//    }
}
