/*
 * Powered By fast.up
 */
package com.fast.controller.analysis;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.po.analysis.OrderAnalysisPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.analysis.OrderAnalysisService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.user.FastUserService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 订单数据对比
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/analysis")
public class OrderContrastAnalysisController extends BaseController {

    @Autowired
    private OrderAnalysisService contrastService;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private FastUserService userService;

    /**
     * 订单数据对比
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "订单数据对比", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "list是一个json数据",
            "list>>dramaId:1:int:短剧id-数字",
            "list>>releaseDateStr:1:str:上线日期 yyyy-MM-dd",
            "list>>retailIds:0:str:分销商id-数字",
            "list>>advUserIds:0:int:优化师(投手)id",
            "list>>linkIds:0:str:链接id-数字",
            "contrastDataDay:1:int:对比数据范围-天数",
            "contrastDataType:1:int:对比数据维度 1=支付人数;2=支付订单数;3=支付金额;4=订单完成率;5=用户支付率,6=人均支付金额;7=人均支付次数",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序",
    })
    @ApiParamsOut({
            "results>>dramaId 短剧id",
            "results>>dramaName 短剧名称",
            "results>>dataSum 累计数据",
            "results>>list>>day 第(1,2,3,4....)天",
            "results>>list>>data Y轴数据指标",
    })
    @Slave
    @RequestMapping(value = "/getOrderContrastAnalysis")
    public ResultVO<?> getOrderContrastAnalysis(HttpServletRequest request, @RequestBody OrderAnalysisPO params) {
        List<OrderAnalysisPO> paramsList = params.getList();
        if (CollUtil.isEmpty(paramsList)) {
            return ResultVO.error("list不能为空");
        }
        for (OrderAnalysisPO po : paramsList) {
            StrUtil.checkMysqlInData(po.getDramaIds(), po.getAdvUserIds(), po.getLinkIds());
            if (po.getDramaId() == null) {
                return ResultVO.error("短剧必填");
            }
            // 如果选择了链接,则查询优化师
            if (notEmpty(po.getLinkIds())) {
                FastLinkPO query = new FastLinkPO();
                query.setIds(po.getLinkIds());
                List<Integer> ids = linkService.queryAdvUserIds(query);
                if (CollUtil.isEmpty(ids)) {
                    return ResultVO.success(getDefaultListData());
                }
                if (ids.size() > 1) {
                    return ResultVO.error("链接只能一个");
                }
                po.setAdvUserIds(StrUtil.joinNoRepeat(ids));
            }
            // 如果选择了优化师,则查询分销商
            if (notEmpty(po.getAdvUserIds())) {
                FastUserPO query = new FastUserPO();
                query.setIds(po.getAdvUserIds());
                List<Integer> ids = userService.queryRetailIds(query);
                if (CollUtil.isEmpty(ids)) {
                    return ResultVO.success(getDefaultListData());
                }
                if (ids.size() > 1) {
                    return ResultVO.error("优化师只能一个");
                }
                po.setRetailIds(StrUtil.joinNoRepeat(ids));
                po.setRetailId(ids.get(0));
            }
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getContrastDataDay() == null || params.getContrastDataDay() <= 0 || params.getContrastDataDay() > 180) {
            return ResultVO.error("对比数据范围不合法");
        }
        params.setAddState(1);
        params.setFirstType(0);

        List<JSONObject> list = contrastService.getOrderContrastAnalysis(params, paramsList);
        if (list == null) {
            return ResultVO.success(getDefaultListData());
        }
        return ResultVO.success(list);
    }

    /**
     * 观看支付率对比
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "观看支付率对比", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "list是一个json数据",
            "list>>dramaId:1:int:短剧id-数字",
            "list>>releaseDateStr:1:str:上线日期 yyyy-MM-dd",
            "list>>retailIds:0:str:分销商id-数字",
            "list>>advUserIds:0:int:优化师(投手)id",
            "list>>linkIds:0:str:链接id-数字",
            "contrastDataDay:1:int:对比数据范围-天数",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序",
            "dataType:0:int:1=当日,24=24小时;48=48小时",
    })
    @ApiParamsOut({
            "results>>dramaId 短剧id",
            "results>>dramaName 短剧名称",
            "results>>list>>day 第(1,2,3,4....)天",
            "results>>list>>watchCount:当日注册人数",
            "results>>list>>rechargeMemberCount:支付人数",
            "results>>list>>rechargeRate:支付率",
    })
    @Slave
    @RequestMapping(value = "/getWatchPayRateContrastAnalysis")
    public ResultVO<?> getWatchPayRateContrastAnalysis(HttpServletRequest request, @RequestBody OrderAnalysisPO params) {
        List<OrderAnalysisPO> paramsList = params.getList();
        if (CollUtil.isEmpty(paramsList)) {
            return ResultVO.error("list不能为空");
        }
        for (OrderAnalysisPO po : paramsList) {
            StrUtil.checkMysqlInData(po.getDramaIds(), po.getAdvUserIds(), po.getLinkIds());
            if (po.getDramaId() == null) {
                return ResultVO.error("短剧必填");
            }
            // 如果选择了链接,则查询优化师
            if (notEmpty(po.getLinkIds())) {
                FastLinkPO query = new FastLinkPO();
                query.setIds(po.getLinkIds());
                List<Integer> ids = linkService.queryAdvUserIds(query);
                if (CollUtil.isEmpty(ids)) {
                    return ResultVO.success(getDefaultListData());
                }
                if (ids.size() > 1) {
                    return ResultVO.error("链接只能一个");
                }
                po.setAdvUserIds(StrUtil.joinNoRepeat(ids));
            }
            // 如果选择了优化师,则查询分销商
            if (notEmpty(po.getAdvUserIds())) {
                FastUserPO query = new FastUserPO();
                query.setIds(po.getAdvUserIds());
                List<Integer> ids = userService.queryRetailIds(query);
                if (CollUtil.isEmpty(ids)) {
                    return ResultVO.success(getDefaultListData());
                }
                if (ids.size() > 1) {
                    return ResultVO.error("优化师只能一个");
                }
                po.setRetailIds(StrUtil.joinNoRepeat(ids));
                po.setRetailId(ids.get(0));
            }
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getContrastDataDay() == null || params.getContrastDataDay() <= 0 || params.getContrastDataDay() > 180) {
            return ResultVO.error("对比数据范围不合法");
        }
        params.setAddState(1);

        List<JSONObject> list = contrastService.getWatchPayRateContrastAnalysis(params, paramsList);
        if (list == null) {
            return ResultVO.success(getDefaultListData());
        }
        return ResultVO.success(list);
    }

    /**
     * 累充金额对比
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "累充金额对比", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "list是一个json数据",
            "list>>dramaId:1:int:短剧id-数字",
            "list>>releaseDateStr:1:str:上线日期 yyyy-MM-dd",
            "list>>retailIds:0:str:分销商id-数字",
            "list>>advUserIds:0:int:优化师(投手)id",
            "list>>linkIds:0:str:链接id-数字",
            "contrastDataDay:1:int:对比数据范围-天数",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序",
    })
    @ApiParamsOut({
            "results>>dramaId 短剧id",
            "results>>dramaName 短剧名称",
            "results>>list>>rechargeCount:充值金额(1,2,3,4,5)",
            "results>>list>>rechargeMemberCount:统计人数",
    })
    @Slave
    @RequestMapping(value = "/getCumulativeRechargeContrastList")
    public ResultVO<?> getCumulativeRechargeContrastList(HttpServletRequest request, @RequestBody OrderAnalysisPO params) {
        List<OrderAnalysisPO> paramsList = params.getList();
        if (CollUtil.isEmpty(paramsList)) {
            return ResultVO.error("list不能为空");
        }
        for (OrderAnalysisPO po : paramsList) {
            StrUtil.checkMysqlInData(po.getDramaIds(), po.getAdvUserIds(), po.getLinkIds());
            if (po.getDramaId() == null) {
                return ResultVO.error("短剧必填");
            }
            // 如果选择了链接,则查询优化师
            if (notEmpty(po.getLinkIds())) {
                FastLinkPO query = new FastLinkPO();
                query.setIds(po.getLinkIds());
                List<Integer> ids = linkService.queryAdvUserIds(query);
                if (CollUtil.isEmpty(ids)) {
                    return ResultVO.success(getDefaultListData());
                }
                if (ids.size() > 1) {
                    return ResultVO.error("链接只能一个");
                }
                po.setAdvUserIds(StrUtil.joinNoRepeat(ids));
            }
            // 如果选择了优化师,则查询分销商
            if (notEmpty(po.getAdvUserIds())) {
                FastUserPO query = new FastUserPO();
                query.setIds(po.getAdvUserIds());
                List<Integer> ids = userService.queryRetailIds(query);
                if (CollUtil.isEmpty(ids)) {
                    return ResultVO.success(getDefaultListData());
                }
                if (ids.size() > 1) {
                    return ResultVO.error("优化师只能一个");
                }
                po.setRetailIds(StrUtil.joinNoRepeat(ids));
                po.setRetailId(ids.get(0));
            }
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getContrastDataDay() == null || params.getContrastDataDay() <= 0 || params.getContrastDataDay() > 180) {
            return ResultVO.error("对比数据范围不合法");
        }
        params.setAddState(1);

        List<JSONObject> list = contrastService.getCumulativeRechargeContrastList(params, paramsList);
        if (list == null) {
            return ResultVO.success(getDefaultListData());
        }
        return ResultVO.success(list);
    }
}
