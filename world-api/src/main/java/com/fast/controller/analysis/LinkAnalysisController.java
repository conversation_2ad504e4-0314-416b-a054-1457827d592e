/*
 * Powered By fast.up
 */
package com.fast.controller.analysis;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticVar;
import com.fast.po.analysis.LinkAnalysisPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.analysis.LinkAnalysisExportService;
import com.fast.service.analysis.LinkAnalysisService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.retail.FastRetailService;
import com.fast.service.user.FastUserService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 渠道分析
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/analysis")
public class LinkAnalysisController extends BaseController {

    @Autowired
    private LinkAnalysisService linkAnalysisService;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private FastUserService userService;
    @Autowired
    private FastRetailService retailService;
    @Autowired
    private LinkAnalysisExportService exportService;

    /**
     * ROI梯度表-各类数据汇总
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "ROI梯度表-整体数据汇总(付费)", folder = {StaticFolder.FOLDER_ANALYSIS_LINK})
    @ApiParamsIn({
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "retailTypes:0:str:分销商类型:1=自投、2=代投、3=独立分销商(多个逗号分隔)",
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "advUserIds:0:str:优化师(投手)id(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "roadIds:0:str:链路id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "createTimeStr:0:str:统计时间：yyyy-MM-dd - yyyy-MM-dd",
            "noLaunchZero:0:int:1=不展示投放金额为0的数据(勾选中);0=展示(未勾选)(默认)",
            "noAddMemberZero:0:int:1=不展示新增用户为0的数据(勾选中);0=展示(未勾选)(默认)",
            "groupByColum:0:int:统计维度：0=整体;1=链接;2=分销商;3=优化师;4=短剧;5=链路",
            "sortType:0:int:排序字段：0=默认;1=投放消耗;2=新增用户;3=染色用户;4=充值用户;5=用户成本;6=ARPPU;7=充值金额;8=累计ROI;9=续充率",
            "sortOrder:0:int:排序顺序：1=倒序;2=正序",
            "feeFlag:0:int:付费标志:1=付费;2=免费"
    })
    @ApiParamsOut({
            "results>>statisDate:统计时间 yyyy-MM-dd",
            "results>>validLinkCount:有效链接数",
            "results>>allLinkCount:关联链接数",
            "results>>linkId:链接ID",
            "results>>linkIds:有效/关联的数链接ID",
            "results>>linkName:链接名称",
            "results>>advUserName:优化师名称",
            "results>>retailName:分销商名称",
            "results>>retailType:分销商类型 1=自投、2=代投、3=独立分销商",
            "results>>roadName:链路名称",
            "results>>renewRate:续充率(不含%)",
            "results>>adMoneyConsume:总成本(消耗)",
            "results>>adMoneyConsumeToday:今日成本(消耗)",
            "results>>memberCountAddAll:新增用户数",
            "results>>memberCountAddToday:今日新增用户数",
            "results>>memberCountAddColor:染色人数",
            "results>>memberCountAddColorToday:今日染色人数",
            "results>>memberRateAddColor:染色率(不含%)",
            "results>>memberCost:新增用户成本",
            "results>>payMemberCost:付费用户成本",
            "results>>rechargeMemberCountAddToday:今日充值用户",
            "results>>rechargeMemberCountAddAll:累计充值用户",
            "results>>rechargeMoneySumAddAll:累计充值金额",
            "results>>arppu:arppu",
            "results>>backRate:累计回报率(ROI)",
            "results>>numD60RechAll:D0-D60充值人数",
            "results>>moneyD60RechAll:D0-D60充值金额",
            "results>>moneyD60RechDaySum:D0-D60充值金额每日累加",
            "results>>backD60RechAll:D0-D60回报率(ROI)",
            "results>>backD60RechDaySum:D0-D60回报率(ROI)每日累加",
            "summary>>****汇总数据****字段和上面的results一样",
    })
    @Slave
    @RequestMapping(value = "/getLinkRoiCoreAllAnalysis")
    public ResultVO<?> getLinkRoiCoreAllAnalysis(HttpServletRequest request, LinkAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        // 默认只查付费的推广链接
        if (params.getFeeFlag() == null) {
            params.setFeeFlag(1);
        }
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getGroupByColum() == null || !StrUtil.equalsAny(params.getGroupByColum(), 0, 1, 2, 3, 4, 5)) {
            return ResultVO.error("统计维度不合法");
        }
        if (params.getSortType() == null) {
            params.setSortType(0);
        }
        if (!StrUtil.equalsAny(params.getSortType(), 0, 1, 2, 3, 4, 5, 6, 7, 8, 9)) {
            return ResultVO.error("排序字段不合法");
        }
        if (params.getSortOrder() == null) {
            params.setSortOrder(1);
        }
        // 处理时间
        if (notEmpty(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());

            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));

            // params.setPayTimeS(date.get(0));
            // params.setPayTimeE(date.get(1));

            params.setStatisDateS(DateUtil.format09(date.get(0)));
            params.setStatisDateE(DateUtil.format09(date.get(1)));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("筛选时间范围不能超过90天");
            }
        }
        Set<Integer> linkIdSet = new HashSet<>();
        // 如果选择了链接,则查询优化师
        if (notEmpty(params.getLinkIds())) {
            linkIdSet.addAll(CollUtil.parseIntStr2Set(params.getLinkIds()));
            FastLinkPO query = new FastLinkPO();
            query.setIds(params.getLinkIds());
            List<Integer> ids = linkService.queryAdvUserIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setAdvUserIds(StrUtil.joinNoRepeat(ids));
        }
        // 如果选择了优化师,则查询分销商
        if (notEmpty(params.getAdvUserIds())) {
            FastUserPO query = new FastUserPO();
            query.setIds(params.getAdvUserIds());
            List<Integer> ids = userService.queryRetailIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setRetailIds(StrUtil.joinNoRepeat(ids));
        }
        // 应用搜索
        if (params.getAppType() != null) {
            FastLinkPO query = new FastLinkPO();
            query.setAppType(params.getAppType());
            query.setMiniIds(params.getMiniIds());
            query.setAdvMediaId(params.getAdvMediaId());
            Set<Integer> ids = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultListData());
            } else if (CollUtil.hasContent(linkIdSet)) {
                linkIdSet = CollUtil.intersection(linkIdSet, ids);
            } else {
                linkIdSet.addAll(ids);
            }
            params.setLinkIds(StrUtil.join(linkIdSet));
        }

        params.setAddState(1);
        // 查询分销商id
        if (notEmpty(params.getRetailTypes()) || notEmpty(params.getRetailIds())) {
            FastRetailPO query = new FastRetailPO();
            query.setRetailTypes(params.getRetailTypes());
            query.setRetailIds(params.getRetailIds());
            Set<Integer> retailIds = retailService.queryRetailIds(query);
            List<Integer> ids = CollUtil.parseIntStr2List(params.getRetailIds());
            if (CollUtil.hasContent(ids)) {
                retailIds.retainAll(ids);
            }
            params.setRetailIds(StrUtil.join(retailIds));
        }

        if (params.getNoAddMemberZero() == null) {
            params.setNoAddMemberZero(0);
        }
        if (params.getNoLaunchZero() == null) {
            params.setNoLaunchZero(0);
        }

        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            return exportService.exportLinkRoiCoreAnalysis(sessionVO, params);
        } else {
            LinkAnalysisPO summary = new LinkAnalysisPO();
            List<LinkAnalysisPO> list = linkAnalysisService.getLinkRoiCoreAnalysis(params, summary);
            return ResultVO.summary(list, summary);
        }
    }

    /**
     * 免费链路ROI梯度表
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "ROI梯度表-整体数据汇总(免费)", folder = {StaticFolder.FOLDER_ANALYSIS_LINK})
    @ApiParamsIn({
            "groupByColum:1:int:统计维度：0=整体;1=链接",
            "advMediaId:0:int:媒体类型：空=全部;1=抖音",
            "retailTypes:0:str:分销商类型",
            "retailIds:0:str:分销商ids",
            "advUserIds:0:str:优化师ids",
            "linkIds:0:str:推广链接Ids",
            "miniIds:0:str:应用ids",
            "dramaIds:0:str:短剧ids",
            "statisDateS:0:str:统计时间开始",
            "statisDateE:0:str:统计时间结束",
            "noLaunchZero:0:int:1=不展示投放金额为0的数据(勾选中);0=展示(未勾选)(默认)",
            "noAddMemberZero:0:int:1=不展示新增用户为0的数据(勾选中);0=展示(未勾选)(默认)",
            "sortType:0:int:排序字段：0=默认;1=有效链接数;2=投放消耗;3=新增用户;4=染色用户;5=转化用户;6=用户成本;7=ARPPU;8=累计回报率;9=60DLTV;10=累计收入金额",
            "sortOrder:0:int:排序顺序：1=倒序;2=正序",
    })
    @ApiParamsOut({
            "results>>statisDate:统计时间 yyyy-MM-dd",
            "results>>validLinkCount:有效链接数",
            "results>>adMoneyConsume:投放消耗",
            "results>>memberCountAdd:新增用户人数",
            "results>>memberCountAddColor:染色用户人数",
            "results>>convertMemberCount:转化用户数",
            "results>>convertMemeberCountAd:转化用户(广告)",
            "results>>convertMemberCountRecharge:转化用户(充值)",
            "results>>addMemberCost:新增用户成本",
            "results>>convertMemberCost:转化用户成本",
            "results>>arppu:arppu",
            "results>>income:累计收入",
            "results>>incomeAd:累计广告收入",
            "results>>incomeRecharge:累计充值收入",
            "results>>returnRatio:累计回报率",
            "results>>ltv60D:60DLTV",
            "results>>numD60MemberAll:D0-D60产生收益的人数",
            "results>>numD60MoneyDay:D0-D60当日产生收益金额",
            "results>>numD60MoneyAll:D0-D60累计产生收益金额",
            "results>>numD60ROIDay:D0-D60当日ROI",
            "results>>numD60ROIAll:D0-D60累计ROI",
            "results>>encryptionId:渠道加密id",
            "results>>linkId:渠道Id",
            "results>>linkCreatTime:渠道创建时间",
            "results>>linkName:链接名称",
            "results>>advUserName:优化师",
            "results>>retailName:分销商名称",
            "results>>adMoneyConsume:累计消耗",
            "results>>adMoneyConsumeToday:当日消耗",
            "results>>memberCountAddToday:当日新增用户数",
            "results>>memberCountAddAll:累计新增用户数",
            "results>>memberCountAddColorToday:当日染色用户数",
            "results>>memberCountAddColor:累计染色用户数",
            "results>>convertMemberCountToday:当日转化用户数",
            "results>>convertMemberCount:累计转化用户数",
            "results>>addMemberCost:新增用户成本",
            "results>>convertMemberCost:转化用户成本",
            "results>>arppu:arppu",
            "results>>income:累计收入",
            "results>>incomeAd:累计广告收入",
            "results>>incomeRecharge:累计充值收入",
            "results>>returnRatio:累计回报率",
            "results>>ltv60D:60DLTV",
            "results>>addConvertMemberD60:D0-D60产生收益的人数",
            "results>>numD60MoneyDay:D0-D60当日产生收益金额",
            "results>>numD60MoneyAll:D0-D60累计产生收益金额",
            "results>>numD60ROIDay:D0-D60当日ROI",
            "results>>numD60ROIAll:D0-D60累计ROI",
            "summary>>****汇总数据****字段和上面的results一样",
    })
    @Slave
    @RequestMapping(value = "/getLinkRoiCoreAllAnalysisFree")
    public ResultVO<?> getLinkRoiCoreAllAnalysisFree(HttpServletRequest request, LinkAnalysisPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (Objects.isNull(params.getContentType()) && sessionVO.getContentType() != 99) {
            params.setContentType(sessionVO.getContentType());
        }
        if (params.getStatisDateS() == null || params.getStatisDateS().trim().length() == 0) {
            return ResultVO.error("统计开始时间不能为空");
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            return exportService.exportLinkRoiCoreAnalysisFree(sessionVO, params);
        } else {
            return linkAnalysisService.getLinkRoiCoreAllAnalysisFree(params, pageVO);
        }
    }

    /**
     * ROI梯度表-详情-累计数据汇总
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "ROI梯度表-详情-累计数据汇总", folder = {StaticFolder.FOLDER_ANALYSIS_LINK})
    @ApiParamsIn({
            "dramaId:0:str:短剧id-数字",
            "retailId:0:str:分销商id-数字",
            "advUserId:0:int:优化师id",
            "dramaId:0:str:短剧id-数字",
            "roadId:0:str:链路id-数字",
            "linkId:0:str:链接id-数字",
            "groupByColum:0:int:统计维度：1=链接;2=分销商;3=优化师;4=短剧;5=链路",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
    })
    @ApiParamsOut({
            "results>>validLinkCount:有效链接数",
            "results>>allLinkCount:关联链接数",
            "results>>dramaGrantRetailCount:短剧授权分销商数",
            "results>>linkId:链接ID",
            "results>>linkName:链接名称",
            "results>>advUserName:优化师名称",
            "results>>retailName:分销商名称",
            "results>>retailType:分销商类型 1=自投、2=代投、3=独立分销商",
            "results>>roadName:链路名称",
            "results>>adMoneyConsume:总成本(消耗)",
            "results>>memberCountAddAll:累计新增用户数",
            "results>>memberCountAddColor:染色人数",
            "results>>rechargeMemberCountAddAll:累计充值用户",
            "results>>rechargeMoneySumAddAll:累计充值金额",
            "results>>rechargeOrderAddSumAll:累计充值笔数",
            "results>>memberCost:新增用户成本",
            "results>>payMemberCost:付费用户成本",
            "results>>arpu:arpu",
            "results>>arppu:arppu",
            "results>>backRate:累计回报率(ROI)",
    })
    @Slave
    @RequestMapping(value = "/getLinkRoiDetailSummaryAnalysis")
    public ResultVO<?> getLinkRoiDetailSummaryAnalysis(HttpServletRequest request, LinkAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        ResultVO<?> valid = checkParams(params);
        if (valid != null) {
            return valid;
        }

        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            return exportService.exportLinkRoiDetailSummaryAnalysis(sessionVO, params);
        } else {
            LinkAnalysisPO summary = new LinkAnalysisPO();
            List<LinkAnalysisPO> list = linkAnalysisService.getLinkRoiDetailSummaryAnalysis(params);
            return ResultVO.summary(list, summary);
        }
    }

    /**
     * ROI梯度表-详情-今日数据汇总
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "ROI梯度表-详情-今日数据汇总", folder = {StaticFolder.FOLDER_ANALYSIS_LINK})
    @ApiParamsIn({
            "dramaId:0:str:短剧id-数字",
            "retailId:0:str:分销商id-数字",
            "advUserId:0:int:优化师id",
            "dramaId:0:str:短剧id-数字",
            "roadId:0:str:链路id-数字",
            "linkId:0:str:链接id-数字",
            "groupByColum:0:int:统计维度：1=链接;2=分销商;3=优化师;4=短剧;5=链路",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
    })
    @ApiParamsOut({
            "results>>statisDate:统计时间 yyyy-MM-dd",
            "results>>adMoneyConsume:成本(消耗)",
            "results>>memberCountAddAll:新增用户数",
            "results>>memberCountAddColor:染色人数",
            "results>>backRate:回报率(ROI)",
            "results>>list>>groupByColum:统计维度：1=今日新增用户充值;2=普通充值;3=会员充值;4=今日总用户充值",
            "results>>list>>rechargeMoneySum:金额",
            "results>>list>>rechargeMemberCount:人数",
            "results>>list>>rechargeCount:笔数",
            "results>>list>>memberSingleMoney:客单价",
            "results>>list>>memberPayRate:付费率",
    })
    @Slave
    @RequestMapping(value = "/getLinkRoiDetailTodayAnalysis")
    public ResultVO<?> getLinkRoiDetailTodayAnalysis(HttpServletRequest request, LinkAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        ResultVO<?> valid = checkParams(params);
        if (valid != null) {
            return valid;
        }

        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            return exportService.exportLinkRoiDetailTodayAnalysis(sessionVO, params);
        } else {
            LinkAnalysisPO summary = new LinkAnalysisPO();
            List<LinkAnalysisPO> list = linkAnalysisService.getLinkRoiDetailTodayAnalysis(params);
            return ResultVO.summary(list, summary);
        }
    }

    /**
     * ROI梯度表-详情-每日数据汇总
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "ROI梯度表-详情-每日数据汇总", folder = {StaticFolder.FOLDER_ANALYSIS_LINK})
    @ApiParamsIn({
            "dramaId:0:str:短剧id-数字",
            "retailId:0:str:分销商id-数字",
            "advUserId:0:int:优化师id",
            "dramaId:0:str:短剧id-数字",
            "roadId:0:str:链路id-数字",
            "linkId:0:str:链接id-数字",
            "createTimeStr:0:str:统计时间：yyyy-MM-dd - yyyy-MM-dd",
            "groupByColum:0:int:统计维度：1=链接;2=分销商;3=优化师;4=短剧;5=链路",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "noLaunchZero:0:int:1=不展示投放金额为0的数据(勾选中);0=展示(未勾选)(默认)",
            "noAddMemberZero:0:int:1=不展示新增用户为0的数据(勾选中);0=展示(未勾选)(默认)",
    })
    @ApiParamsOut({
            "results>>statisDate:统计时间 yyyy-MM-dd",
            "results>>validLinkCount:有效链接数",
            "results>>allLinkCount:关联链接数",
            "results>>linkId:链接ID",
            "results>>linkName:链接名称",
            "results>>advUserName:优化师名称",
            "results>>retailName:分销商名称",
            "results>>retailType:分销商类型 1=自投、2=代投、3=独立分销商",
            "results>>roadName:链路名称",
            "results>>adMoneyConsume:总成本(消耗)",
            "results>>memberCountAdd:新增用户数",
            "results>>memberCountAddColor:染色人数",
            "results>>memberRateAddColor:染色率(不含%)",
            "results>>memberCost:新增用户成本",
            "results>>payMemberCost:付费用户成本",
            "results>>rechargeMemberCountAddAll:累计充值用户",
            "results>>rechargeMoneySumAddAll:累计充值金额",
            "results>>arpu:arpu",
            "results>>backRate:累计回报率(ROI)",
            "results>>numD60RechAll:D0-D60充值人数",
            "results>>moneyD60RechAll:D0-D60充值金额",
            "results>>moneyD60RechDaySum:D0-D60充值金额每日累加",
            "results>>backD60RechAll:D0-D60回报率(ROI)",
            "results>>backD60RechDaySum:D0-D60回报率(ROI)每日累加",
            "summary>>****汇总数据****字段和上面的results一样",
    })
    @Slave
    @RequestMapping(value = "/getLinkRoiDetailPerDayAnalysis")
    public ResultVO<?> getLinkRoiDetailPerDayAnalysis(HttpServletRequest request, LinkAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        ResultVO<?> valid = checkParams(params);
        if (valid != null) {
            return valid;
        }
        // 处理时间
        if (notEmpty(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());

            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));

            // params.setPayTimeS(date.get(0));
            // params.setPayTimeE(date.get(1));

            params.setStatisDateS(DateUtil.format09(date.get(0)));
            params.setStatisDateE(DateUtil.format09(date.get(1)));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("筛选时间范围不能超过90天");
            }
        }

        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            return exportService.exportLinkRoiCorePerDayAnalysis(sessionVO, params);
        } else {
            LinkAnalysisPO summary = new LinkAnalysisPO();
            List<LinkAnalysisPO> list = linkAnalysisService.getLinkRoiCorePerDayAnalysis(params, summary);
            return ResultVO.summary(list, summary);
        }
    }

    /**
     * 参数校验
     *
     * @param params
     * @return
     */
    private ResultVO<?> checkParams(LinkAnalysisPO params) {
        if (params.getGroupByColum() == null || !StrUtil.equalsAny(params.getGroupByColum(), 1, 2, 3, 4, 5)) {
            return ResultVO.error("统计维度不合法");
        }

        if (params.getGroupByColum() == 1 && params.getLinkId() == null) {
            return ResultVO.error("linkId不合法");
        }

        if (params.getGroupByColum() == 2 && params.getRetailId() == null) {
            return ResultVO.error("retailId不合法");
        }

        if (params.getGroupByColum() == 3 && params.getAdvUserId() == null) {
            return ResultVO.error("advUserId不合法");
        }

        if (params.getGroupByColum() == 4 && params.getDramaId() == null) {
            return ResultVO.error("dramaId不合法");
        }

        if (params.getGroupByColum() == 5 && params.getRoadId() == null) {
            return ResultVO.error("roadId不合法");
        }
        return null;
    }
}
