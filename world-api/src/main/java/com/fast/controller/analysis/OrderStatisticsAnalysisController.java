/*
 * Powered By fast.up
 */
package com.fast.controller.analysis;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.po.analysis.OrderAnalysisPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.analysis.OrderAnalysisExportService;
import com.fast.service.analysis.OrderAnalysisService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 订单统计
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/analysis")
public class OrderStatisticsAnalysisController extends BaseController {

    @Autowired
    private OrderAnalysisService orderAnalysisService;
    @Autowired
    private OrderAnalysisExportService exportService;
    @Autowired
    private FastLinkService linkService;

    /**
     * 订单统计-统计分析汇总
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "统计分析汇总", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
    })
    @ApiParamsOut({
            "results>>list>>dataType:1=今日;2=昨日;3=本月",
            "results>>list>>list>>rechargeMoneySum:总充值金额",
            "results>>list>>list>>rechargeCoinMoneySum:充值K币金额",
            "results>>list>>list>>rechargeVipMoneySum:充值VIP金额",
            "results>>list>>list>>rechargeMoneyAddSum:新增用户充值金额",
            "results>>list>>list>>rechargeMoneyNewSum:注册用户充值金额",
            "results>>list>>list>>rechargeMemberCount:首充用户数",
    })
    @Slave
    @RequestMapping(value = "/getStatisticsSumList")
    public ResultVO<?> getStatisticsSumList(HttpServletRequest request, OrderAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        if (sessionVO.getContentType() != null && sessionVO.getContentType() == 99) {
            params.setContentType(null);
        } else {
            params.setContentType(sessionVO.getContentType());
        }
        if (notEmpty(params.getRetailIds())) {
            params.setRetailId(toInteger(params.getRetailIds()));
        }
        final JedisLock lock = new JedisLock("get_statistics_sum_list_lock" + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }

            String key = "getStatisticsSumList:" + params.getRetailId() + "-" + params.getContentType();
            // 防止key过长，使用md5
            if (notEmpty(params.getOfficialIds())) {
                key += "-" + Md5Util.getMD5(params.getOfficialIds());
            }
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return JsonUtil.toJavaObject(value, ResultVO.class);
            }
            ResultVO<?> vo = orderAnalysisService.getStatisticsSumList(params);
            RedisUtil.setObject(key, vo, 60);
            return vo;
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    /**
     * 订单统计-统计分析-按维度
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "统计分析-按维度", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "advVersion:1:int:头条投放版本 1=1.0，2=2.0",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "createTimeStr:0:str:统计时间：yyyy-MM-dd - yyyy-MM-dd",
            "dataType:1:int:维度 1=按月;2=按天;3=按分销商;4=按优化师;5=按短剧",
            "sortType:0:int:排序字段：1=充值总金额;2=充值总人数;3=订单总数;4=新增用户充值金额;5=注册用户充值金额;6=染色用户充值金额;7=K币充值数;8=充值VIP金额;9=回传率;",
            "sortOrder:0:int:排序顺序：1=倒序;2=正序",
    })
    @ApiParamsOut({
            "results>>list>>dataDay:月份/日期",
            "results>>list>>rechargeMoneySum:总充值金额",
            "results>>list>>rechargeMemberCount:充值总人数",
            "results>>list>>rechargeOrderCount:订单总数",
            "results>>list>>rechargeMoneyAddSum:新增用户充值金额",
            "results>>list>>rechargeMemberCountAdd:新增用户充值人数",
            "results>>list>>rechargeMoneyNewSum:注册用户充值金额",
            "results>>list>>rechargeMemberCountNew:注册用户充值人数",
            "results>>list>>rechargeMoneyColorSum:染色用户充值金额",
            "results>>list>>rechargeMemberCountColor:染色用户充值人数",
            "results>>list>>rechargeCoinMoneySum:充值K币金额",
            "results>>list>>rechargeCoinMoneyPer:充值K币人均金额",
            "results>>list>>rechargeCoinMemberCount:充值K币人次",
            "results>>list>>rechargeVipMoneySum:充值VIP金额",
            "results>>list>>rechargeVipMoneyPer:充值VIP人均金额",
            "results>>list>>rechargeVipMemberCount:充值VIP人次",
            "results>>list>>backRate:回传率",
            "summary>>****汇总数据****字段和上面的results一样",
    })
    @Slave
    @RequestMapping(value = "/getStatisticsByDateList")
    public ResultVO<?> getStatisticsByDateList(HttpServletRequest request, OrderAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getDataType() == null) {
            return ResultVO.error("维度不合法");
        }
        if (!StrUtil.equalsAny(params.getDataType(), 1, 2, 3, 4, 5)) {
            return ResultVO.error("维度不合法");
        }
        // 应用搜索
        if (params.getAppType() != null) {
            FastLinkPO query = new FastLinkPO();
            query.setAppType(params.getAppType());
            query.setMiniIds(params.getMiniIds());
            query.setAdvMediaId(params.getAdvMediaId());
            Set<Integer> linkIds = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(linkIds)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        // 处理时间
        if (notEmpty(params.getCreateTimeStr())) {
            List<Date> date;
            if (params.getDataType() == 1) {
                date = DateUtil.getStartEndMonthDate(params.getCreateTimeStr());
                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 186) {
                    return ResultVO.error("注册时间范围不能超过6个月");
                }
            } else {
                date = DateUtil.getStartEndDate(params.getCreateTimeStr());
                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("注册时间范围不能超过90天");
                }
            }
            // params.setRegisterTimeS(date.get(0));
            // params.setRegisterTimeE(date.get(1));

            // params.setLinkTimeS(date.get(0));
            // params.setLinkTimeE(date.get(1));

            params.setPayTimeS(date.get(0));
            params.setPayTimeE(date.get(1));
        }

        if (params.getSortType() == null) {
            params.setSortType(0);
        }
        if (params.getSortOrder() == null) {
            params.setSortOrder(1);
        }

        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            return exportService.exportStatisticsByDateList(sessionVO, params);
        } else {
            OrderAnalysisPO summary = new OrderAnalysisPO();
            List<OrderAnalysisPO> list = orderAnalysisService.getStatisticsByDateList(params, summary);
            return ResultVO.summary(list, summary);
        }
    }
}
