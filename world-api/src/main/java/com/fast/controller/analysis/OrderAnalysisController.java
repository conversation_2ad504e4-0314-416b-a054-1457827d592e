/*
 * Powered By fast.up
 */
package com.fast.controller.analysis;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.po.analysis.OrderAnalysisPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.analysis.OrderAnalysisService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 订单分析
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/analysis")
public class OrderAnalysisController extends BaseController {

    @Autowired
    private OrderAnalysisService orderAnalysisService;
    @Autowired
    private FastLinkService linkService;

    /**
     * 订单排名分析
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "订单排名分析", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
            "groupByColum:0:int:统计维度：1=分销商;2=优化师;3=链接;4=短剧;5=月份",
            "sortType:0:int:排名维度：1=订单笔数;2=订单支付人数;3=充值总金额;4=实到总金额",
    })
    @ApiParamsOut({
            "results>>name:统计维度(1=分销商;2=优化师;3=链接;4=短剧;5=月份)名称",
            "results>>rechargeOrderCount:订单笔数",
            "results>>rechargeMemberCount:订单支付人数",
            "results>>rechargeMoneySum:充值总金额",
    })
    @Slave
    @RequestMapping(value = "/getOrderRankAnalysisList")
    public ResultVO<?> getOrderRankAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getSortType() == null) {
            params.setSortType(1);
        }
        if (!StrUtil.equalsAny(params.getGroupByColum(), 1, 2, 3, 4, 5)) {
            return ResultVO.error("统计维度不合法");
        }
        if (!StrUtil.equalsAny(params.getSortType(), 1, 2, 3, 4)) {
            return ResultVO.error("排名维度不合法");
        }
        // 应用搜索
        if (params.getAppType() != null) {
            FastLinkPO query = new FastLinkPO();
            query.setAppType(params.getAppType());
            query.setMiniIds(params.getMiniIds());
            query.setAdvMediaId(params.getAdvMediaId());
            Set<Integer> linkIds = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(linkIds)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        // 处理时间
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("注册时间范围不能超过90天");
            }
        }
        // 处理时间
        if (notEmpty(params.getLinkTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
            params.setLinkTimeS(date.get(0));
            params.setLinkTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("染色时间范围不能超过90天");
            }
        }
        // 处理时间
        if (notEmpty(params.getPayTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
            params.setPayTimeS(date.get(0));
            params.setPayTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("交易时间范围不能超过90天");
            }
        }
        if (params.getGroupByColum() == 5) {
            params.setPayTimeS(DateUtil.beginOfYear());// 获取本年的第一天
            params.setPayTimeE(DateUtil.getNowDate());// 当日
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = orderAnalysisService.exportOrderRankAnalysisList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        } else {
            return orderAnalysisService.getOrderRankAnalysisList(params, 0);
        }
    }

    /**
     * 支付趋势汇总
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "支付趋势汇总", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "results>>dataType:统计维度 1=支付人数;2=支付订单数;3=支付金额;4=订单完成率;5=用户支付率;6=人均支付金额;7=人均支付次数",
            "results>>ringRate:环比(不含%)",
            "results>>data:统计数据",
    })
    @Slave
    @RequestMapping(value = "/getOrderSumTopAnalysisList")
    public ResultVO<?> getOrderSumTopAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        // 应用搜索
        if (params.getAppType() != null) {
            FastLinkPO query = new FastLinkPO();
            query.setAppType(params.getAppType());
            query.setMiniIds(params.getMiniIds());
            query.setAdvMediaId(params.getAdvMediaId());
            Set<Integer> linkIds = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(linkIds)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        // 处理时间
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("注册时间范围不能超过90天");
            }
        }
        // 处理时间
        if (notEmpty(params.getLinkTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
            params.setLinkTimeS(date.get(0));
            params.setLinkTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("染色时间范围不能超过90天");
            }
        }
        // 处理时间
        if (notEmpty(params.getPayTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
            params.setPayTimeS(date.get(0));
            params.setPayTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("交易时间范围不能超过90天");
            }
        }

        // 计算环比专属参数
        OrderAnalysisPO paramsLast = new OrderAnalysisPO();
        BeanUtils.copyProperties(params, paramsLast);
        if (notEmpty(params.getPayTimeStr())) {
            int day = DateUtil.daysBetweenUp(params.getPayTimeS(), params.getPayTimeE());
            paramsLast.setPayTimeS(DateUtil.addDays(params.getPayTimeS(), -day));
            paramsLast.setPayTimeE(DateUtil.addDays(params.getPayTimeE(), -day));
        }

        return orderAnalysisService.getOrderSumTopAnalysisList(params, paramsLast);
    }

    /**
     * 支付趋势线条统计图
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "支付趋势线条统计图", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
            "dataType:0:int:统计维度：1=支付人数;2=支付订单数;3=支付金额;4=订单完成率;5=用户支付率;6=人均支付金额;7=人均支付次数",
    })
    @ApiParamsOut({
            "results>>dataDay:日期",
            "results>>data:统计数据",
    })
    @Slave
    @RequestMapping(value = "/getOrderTrendAnalysisList")
    public ResultVO<?> getOrderTrendAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getDataType() == null) {
            return ResultVO.error("统计维度不合法");
        }
        if (!StrUtil.equalsAny(params.getDataType(), 1, 2, 3, 4, 5, 6, 7, 8)) {
            return ResultVO.error("统计维度不合法");
        }
        // 应用搜索
        if (params.getAppType() != null) {
            FastLinkPO query = new FastLinkPO();
            query.setAppType(params.getAppType());
            query.setMiniIds(params.getMiniIds());
            query.setAdvMediaId(params.getAdvMediaId());
            Set<Integer> linkIds = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(linkIds)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        // 处理时间
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("注册时间范围不能超过90天");
            }
        }
        // 处理时间
        if (notEmpty(params.getLinkTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
            params.setLinkTimeS(date.get(0));
            params.setLinkTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("染色时间范围不能超过90天");
            }
        }
        // 处理时间
        if (notEmpty(params.getPayTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
            params.setPayTimeS(date.get(0));
            params.setPayTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("交易时间范围不能超过90天");
            }
        } else {
            return ResultVO.success("交易时间必须选择", getDefaultListData());
        }

        return orderAnalysisService.getOrderTrendAnalysisList(params);
    }

    /**
     * 支付类型占比
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "支付类型占比", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "results>>list>>dataType:统计维度 1=ios支付人数占比;2=安卓支付人数占比;3=VIP充值人数占比;4=K币充值人数占比",
            "results>>list>>data:统计占比(不含%)",
    })
    @Slave
    @RequestMapping(value = "/getOrderTypeAnalysisList")
    public ResultVO<?> getOrderTypeAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.ORDER_TYPE_ANALYSIS_LIST_LOCK + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
            params.setContentType(sessionVO.getContentType());
            if (sessionVO.getRetailId() > 0) {
                params.setRetailId(sessionVO.getRetailId());
            }
            // 应用搜索
            if (params.getAppType() != null) {
                FastLinkPO query = new FastLinkPO();
                query.setAppType(params.getAppType());
                query.setMiniIds(params.getMiniIds());
                query.setAdvMediaId(params.getAdvMediaId());
                Set<Integer> linkIds = linkService.queryLinkIds(query);
                if (CollUtil.isEmpty(linkIds)) {
                    return ResultVO.success(getDefaultListData());
                }
                params.setLinkIds(StrUtil.join(linkIds));
            }
            // 处理时间
            if (notEmpty(params.getRegisterTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
                params.setRegisterTimeS(date.get(0));
                params.setRegisterTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("注册时间范围不能超过90天");
                }
            }
            // 处理时间
            if (notEmpty(params.getLinkTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
                params.setLinkTimeS(date.get(0));
                params.setLinkTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("染色时间范围不能超过90天");
                }
            }
            // 处理时间
            if (notEmpty(params.getPayTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
                params.setPayTimeS(date.get(0));
                params.setPayTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("交易时间范围不能超过90天");
                }
            }

            return orderAnalysisService.getOrderTypeAnalysisList(params);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    /**
     * 支付时间趋势
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "支付时间趋势", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "period:1:int:1、天；2、小时",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "results>>list>>dataDay:时间",
            "results>>list>>data:统计占比(不含%)",
    })
    @Slave
    @RequestMapping(value = "/getOrderTimeAnalysisList")
    public ResultVO<?> getOrderTimeAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.ORDER_TYPE_ANALYSIS_LIST_LOCK + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
            params.setContentType(sessionVO.getContentType());
            if (sessionVO.getRetailId() > 0) {
                params.setRetailId(sessionVO.getRetailId());
            }
            // 应用搜索
            if (params.getAppType() != null) {
                FastLinkPO query = new FastLinkPO();
                query.setAppType(params.getAppType());
                query.setMiniIds(params.getMiniIds());
                query.setAdvMediaId(params.getAdvMediaId());
                Set<Integer> linkIds = linkService.queryLinkIds(query);
                if (CollUtil.isEmpty(linkIds)) {
                    return ResultVO.success(getDefaultListData());
                }
                params.setLinkIds(StrUtil.join(linkIds));
            }
            // 处理时间
            if (notEmpty(params.getRegisterTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
                params.setRegisterTimeS(date.get(0));
                params.setRegisterTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("注册时间范围不能超过90天");
                }
            }
            // 处理时间
            if (notEmpty(params.getLinkTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
                params.setLinkTimeS(date.get(0));
                params.setLinkTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("染色时间范围不能超过90天");
                }
            }
            // 处理时间
            if (notEmpty(params.getPayTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
                params.setPayTimeS(date.get(0));
                params.setPayTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("交易时间范围不能超过90天");
                }
            }

            return orderAnalysisService.getOrderTimeAnalysisList(params);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    /**
     * 注册支付率
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "注册支付率", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
            "dataType:0:int:1=当日,24=24小时;48=48小时",
    })
    @ApiParamsOut({
            "results>>list>>dataDay:日期",
            "results>>list>>registerCount:当日注册人数",
            "results>>list>>rechargeMemberCount:支付人数",
            "results>>list>>rechargeRate:支付率",
    })
    @Slave
    @RequestMapping(value = "/getPayRateAnalysisList")
    public ResultVO<?> getPayRateAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.PAY_RATE_ANALYSIS_LIST_LOCK + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
            params.setContentType(sessionVO.getContentType());
            if (sessionVO.getRetailId() > 0) {
                params.setRetailId(sessionVO.getRetailId());
            }
            if (params.getDataType() == null) {
                return ResultVO.error("统计维度不合法");
            }
            if (!StrUtil.equalsAny(params.getDataType(), 1, 24, 48)) {
                return ResultVO.error("统计维度不合法");
            }
            // 应用搜索
            if (params.getAppType() != null) {
                FastLinkPO query = new FastLinkPO();
                query.setAppType(params.getAppType());
                query.setMiniIds(params.getMiniIds());
                query.setAdvMediaId(params.getAdvMediaId());
                Set<Integer> linkIds = linkService.queryLinkIds(query);
                if (CollUtil.isEmpty(linkIds)) {
                    return ResultVO.success(getDefaultListData());
                }
                params.setLinkIds(StrUtil.join(linkIds));
            }
            // 处理时间
            if (notEmpty(params.getRegisterTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
                params.setRegisterTimeS(date.get(0));
                params.setRegisterTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("注册时间范围不能超过90天");
                }
            } else {
                params.setRegisterTimeS(DateUtil.beginOfWeek());
                params.setRegisterTimeE(DateUtil.getNowDate());
            }

            return ResultVO.success(orderAnalysisService.getRegisterPayRateAnalysisList(params));
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    /**
     * 充值模版分析
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "充值模版分析", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
            "dataType:0:int:统计维度：1=K币充值档位;2=VIP充值档位",
    })
    @ApiParamsOut({
            "results>>list>>name:充值档位(钱数)",
            "results>>list>>rechargeOrderCount:统计人数",
    })
    @Slave
    @RequestMapping(value = "/getPayTemplateAnalysisList")
    public ResultVO<?> getPayTemplateAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.PAY_TEMPLATE_ANALYSIS_LOCK + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
            params.setContentType(sessionVO.getContentType());
            if (sessionVO.getRetailId() > 0) {
                params.setRetailId(sessionVO.getRetailId());
            }
            if (params.getDataType() == null) {
                return ResultVO.error("统计维度不合法");
            }
            if (!StrUtil.equalsAny(params.getDataType(), 1, 2)) {
                return ResultVO.error("统计维度不合法");
            }
            // 应用搜索
            if (params.getAppType() != null) {
                FastLinkPO query = new FastLinkPO();
                query.setAppType(params.getAppType());
                query.setMiniIds(params.getMiniIds());
                query.setAdvMediaId(params.getAdvMediaId());
                Set<Integer> linkIds = linkService.queryLinkIds(query);
                if (CollUtil.isEmpty(linkIds)) {
                    return ResultVO.success(getDefaultListData());
                }
                params.setLinkIds(StrUtil.join(linkIds));
            }
            // 处理时间
            if (notEmpty(params.getRegisterTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
                params.setRegisterTimeS(date.get(0));
                params.setRegisterTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("注册时间范围不能超过90天");
                }
            }
            // 处理时间
            if (notEmpty(params.getLinkTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
                params.setLinkTimeS(date.get(0));
                params.setLinkTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("染色时间范围不能超过90天");
                }
            }
            // 处理时间
            if (notEmpty(params.getPayTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
                params.setPayTimeS(date.get(0));
                params.setPayTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("交易时间范围不能超过90天");
                }
            }

            return orderAnalysisService.getPayTemplateAnalysisList(params);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    /**
     * 累充分析-复充用户
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "累充分析-复充用户", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
            "dataType:0:int:统计维度：1=短剧;2=用户;3=链接",
            "dramaId:0:str:短剧id-数字(单个)",
            "linkId:0:str:链接id-数字(单个)",
            "moneySearchType:0:str:1=金额小于20;2=金额大于等于20,其他为查询全部",
    })
    @ApiParamsOut({
            "results>>list>>rechargeCount:复值次数(1,2,3,4,5)",
            "results>>list>>rechargeMemberCount:统计人数",
    })
    @Slave
    @RequestMapping(value = "/getCumulativeRechargeMemberAnalysisList")
    public ResultVO<?> getCumulativeRechargeMemberAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getDataType() == null) {
            return ResultVO.error("统计维度不合法");
        }
        if (!StrUtil.equalsAny(params.getDataType(), 1, 2, 3)) {
            return ResultVO.error("统计维度不合法");
        }
        if (params.getDataType() == 1 && params.getDramaId() == null) {
            // return ResultVO.error("dramaId短剧必传");
        }
        if (params.getDataType() == 3 && params.getDramaId() == null) {
            // return ResultVO.error("linkId链接必传");
        }
        // 应用搜索
        if (params.getAppType() != null) {
            FastLinkPO query = new FastLinkPO();
            query.setAppType(params.getAppType());
            query.setMiniIds(params.getMiniIds());
            query.setAdvMediaId(params.getAdvMediaId());
            Set<Integer> linkIds = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(linkIds)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        // 处理时间
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("注册时间范围不能超过90天");
            }
        }
        // 处理时间
        if (notEmpty(params.getLinkTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
            params.setLinkTimeS(date.get(0));
            params.setLinkTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("染色时间范围不能超过90天");
            }
        }
        // 处理时间
        if (notEmpty(params.getPayTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
            params.setPayTimeS(date.get(0));
            params.setPayTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("交易时间范围不能超过90天");
            }
        }
        if (params.getMoneySearchType() == null) {
            params.setMoneySearchType(0);
        }
        if (!StrUtil.equalsAny(params.getMoneySearchType(), 0, 1, 2)) {
            return ResultVO.error("moneySearchType不合法");
        }
        return orderAnalysisService.getCumulativeRechargeMemberAnalysisList(params);
    }

    /**
     * 累充分析-充值金额
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "累充分析-充值金额", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
            "dataType:0:int:统计维度：1=短剧;2=用户;3=链接",
            "dramaId:0:str:短剧id-数字(单个)",
            "linkId:0:str:链接id-数字(单个)",
            "moneySearchType:0:str:1=金额小于20;2=金额大于等于20,其他为查询全部",
    })
    @ApiParamsOut({
            "results>>list>>rechargeCount:充值金额(1,2,3,4,5)",
            "results>>list>>rechargeMemberCount:统计人数",
    })
    @Slave
    @RequestMapping(value = "/getCumulativeRechargeMoneyAnalysisList")
    public ResultVO<?> getCumulativeRechargeMoneyAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getDataType() == null) {
            return ResultVO.error("统计维度不合法");
        }
        if (!StrUtil.equalsAny(params.getDataType(), 1, 2, 3)) {
            return ResultVO.error("统计维度不合法");
        }
        if (params.getDataType() == 1 && params.getDramaId() == null) {
            // return ResultVO.error("dramaId短剧必传");
        }
        if (params.getDataType() == 3 && params.getDramaId() == null) {
            // return ResultVO.error("linkId链接必传");
        }
        // 应用搜索
        if (params.getAppType() != null) {
            FastLinkPO query = new FastLinkPO();
            query.setAppType(params.getAppType());
            query.setMiniIds(params.getMiniIds());
            query.setAdvMediaId(params.getAdvMediaId());
            Set<Integer> linkIds = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(linkIds)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        // 处理时间
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("注册时间范围不能超过90天");
            }
        }
        // 处理时间
        if (notEmpty(params.getLinkTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
            params.setLinkTimeS(date.get(0));
            params.setLinkTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("染色时间范围不能超过90天");
            }
        }
        // 处理时间
        if (notEmpty(params.getPayTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
            params.setPayTimeS(date.get(0));
            params.setPayTimeE(date.get(1));

            if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                return ResultVO.error("交易时间范围不能超过90天");
            }
        }
        params.setMoneySearchType(null);

        return orderAnalysisService.getCumulativeRechargeMoneyAnalysisList(params);
    }

    /**
     * 回传分析-顶部汇总
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "回传分析-顶部汇总", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "advVersion:1:int:头条投放版本 1=1.0，2=2.0",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "results>>dataType:1=总订单数;2=回传成功数;3=回传失败数;4=手动回传成功数;5=回传率",
            "results>>ringRate:环比(不含%)",
            "results>>data:统计数据",
    })
    @Slave
    @RequestMapping(value = "/getBackSumAnalysisList")
    public ResultVO<?> getBackSumAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.BACK_SUM_ANALYSIS_LIST_LOCK + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
            params.setContentType(sessionVO.getContentType());
            if (sessionVO.getRetailId() > 0) {
                params.setRetailId(sessionVO.getRetailId());
            }
            // 应用搜索
            if (params.getAppType() != null) {
                FastLinkPO query = new FastLinkPO();
                query.setAppType(params.getAppType());
                query.setMiniIds(params.getMiniIds());
                query.setAdvMediaId(params.getAdvMediaId());
                Set<Integer> linkIds = linkService.queryLinkIds(query);
                if (CollUtil.isEmpty(linkIds)) {
                    return ResultVO.success(getDefaultListData());
                }
                params.setLinkIds(StrUtil.join(linkIds));
            }
            // 处理时间
            if (notEmpty(params.getRegisterTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
                params.setRegisterTimeS(date.get(0));
                params.setRegisterTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("注册时间范围不能超过90天");
                }
            }
            // 处理时间
            if (notEmpty(params.getLinkTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
                params.setLinkTimeS(date.get(0));
                params.setLinkTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("染色时间范围不能超过90天");
                }
            }
            // 处理时间
            if (notEmpty(params.getPayTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
                params.setPayTimeS(date.get(0));
                params.setPayTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("交易时间范围不能超过90天");
                }
            }

            // 计算环比专属参数
            OrderAnalysisPO paramsLast = new OrderAnalysisPO();
            BeanUtils.copyProperties(params, paramsLast);
            if (notEmpty(params.getPayTimeStr())) {
                int day = DateUtil.daysBetweenUp(params.getPayTimeS(), params.getPayTimeE());
                paramsLast.setPayTimeS(DateUtil.addDays(params.getPayTimeS(), -day));
                paramsLast.setPayTimeE(DateUtil.addDays(params.getPayTimeE(), -day));
            }
            params.setLaunchFlag(1);

            return orderAnalysisService.getBackSumAnalysisList(params, paramsLast);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    /**
     * 回传分析-失败占比
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "回传分析-失败占比", folder = {StaticFolder.FOLDER_ANALYSIS_ORDER})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "advMediaId:1:int:媒体类型：空=全部;1=头条;2=ADQ;3=MP;4=百度;5=快手",
            "appType:1:int:应用类型：1=微信小程序;2=抖音小程序;3=H5",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "dramaIds:0:str:短剧id-数字(多个逗号分隔)",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "linkTimeStr:0:str:染色时间：yyyy-MM-dd - yyyy-MM-dd",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
            "dataType:0:int:统计维度：1=短剧;2=用户;3=链接",
            "dramaId:0:str:短剧id-数字(单个)",
            "linkId:0:str:链接id-数字(单个)",
    })
    @ApiParamsOut({
            "results>>list>>backType:0：未回传，1：回传成功，2：推广链接id为空，3：ip、ua为空，4：ip、ua不匹配，5：未命中规则，6：按比例回传，被忽略，7：超8日不回传，8：公众号web数据源，99：其他",
            "results>>list>>rechargeCount:订单数",
    })
    @Slave
    @RequestMapping(value = "/getBackFailReasonAnalysisList")
    public ResultVO<?> getBackFailReasonAnalysisList(HttpServletRequest request, OrderAnalysisPO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.RETAIL_CORE_ANALYSIS_LOCK + sessionVO.getUserId());
        try {
            if (!lock.lock()) {
                return ResultVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            StrUtil.checkMysqlInData(params.getDramaIds(), params.getMiniIds(), params.getAdvUserIds(), params.getLinkIds(), params.getRetailIds());
            params.setContentType(sessionVO.getContentType());
            if (sessionVO.getRetailId() > 0) {
                params.setRetailId(sessionVO.getRetailId());
            }
            // 应用搜索
            if (params.getAppType() != null) {
                FastLinkPO query = new FastLinkPO();
                query.setAppType(params.getAppType());
                query.setMiniIds(params.getMiniIds());
                query.setAdvMediaId(params.getAdvMediaId());
                Set<Integer> linkIds = linkService.queryLinkIds(query);
                if (CollUtil.isEmpty(linkIds)) {
                    return ResultVO.success(getDefaultListData());
                }
                params.setLinkIds(StrUtil.join(linkIds));
            }
            // 处理时间
            if (notEmpty(params.getRegisterTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
                params.setRegisterTimeS(date.get(0));
                params.setRegisterTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("注册时间范围不能超过90天");
                }
            }
            // 处理时间
            if (notEmpty(params.getLinkTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getLinkTimeStr());
                params.setLinkTimeS(date.get(0));
                params.setLinkTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("染色时间范围不能超过90天");
                }
            }
            // 处理时间
            if (notEmpty(params.getPayTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
                params.setPayTimeS(date.get(0));
                params.setPayTimeE(date.get(1));

                if (DateUtil.daysBetweenUp(date.get(0), date.get(1)) > 90) {
                    return ResultVO.error("交易时间范围不能超过90天");
                }
            }
            params.setLaunchFlag(1);

            return orderAnalysisService.getBackFailReasonAnalysisList(params);
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }
}
