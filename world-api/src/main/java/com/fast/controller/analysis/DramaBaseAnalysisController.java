/*
 * Powered By fast.up
 */
package com.fast.controller.analysis;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticVar;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.po.member.FastMemberCoinMoneyMonthPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.analysis.DramaAnalysisExportService;
import com.fast.service.analysis.DramaAnalysisService;
import com.fast.service.member.FastMemberOrderRechargeService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 短剧分析
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/analysis")
public class DramaBaseAnalysisController extends BaseController {

    @Autowired
    private DramaAnalysisService dramaAnalysisService;
    @Autowired
    private FastMemberOrderRechargeService dataRechargeService;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private DramaAnalysisExportService exportService;

    /**
     * 用户观看和充值分析[2024-02新增看广告分析]
     * <a href="https://lanhuapp.com/link/#/invite?sid=lx0aI5RT">...</a>
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "用户观看和充值分析", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "payTimeStr:0:str:交易时间：yyyy-MM-dd - yyyy-MM-dd",
            "registerTimeStr:0:str:注册时间：yyyy-MM-dd - yyyy-MM-dd",
            "watchTimeStr:0:str:观看时间：yyyy-MM-dd - yyyy-MM-dd",
            "adCostTimeStr:0:str:广告计费时间：yyyy-MM-dd - yyyy-MM-dd",
            "sortType:0:int:排序字段：1=充值人数;2=观看人数;3=产生广告收益人数",
    })
    @ApiParamsOut({
            "results>>dramaId:短剧id",
            "results>>dramaName:短剧名称",
            "results>>watchMemberCount:观看人数",
            "results>>rechargeMemberCount:充值人数",
            "results>>adIncomeMemberCount:广告收益人数",
            "results>>watchMemberCountRate:观看人数占比(不含%)",
            "results>>rechargeMemberCountRate:充值人数占比(不含%)",
            "results>>adIncomeMemberCountRate:广告收益人数占比(不含%)",
            "summary>>allWatchMemberCount:观看总人数",
            "summary>>allRechargeMemberCount:充值总人数",
            "summary>>allAdIncomeMemberCount:产生广告收益总人数",
    })
    @Slave
    @RequestMapping(value = "/getWatchRechargeAnalysisList")
    public ResultVO<?> getWatchRechargeAnalysisList(HttpServletRequest request, DramaAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());

        if (params.getSortType() == null) {
            params.setSortType(1);
        }
        // 优化师和链接id搜索
        if (notEmpty(params.getAdvUserIds()) || notEmpty(params.getLinkIds())) {
            FastLinkPO query = new FastLinkPO();
            query.setAdvUserIds(params.getAdvUserIds());
            query.setIds(params.getLinkIds());
            Set<Integer> linkIds = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(linkIds)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        // 处理时间
        if (notEmpty(params.getRegisterTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getRegisterTimeStr());
            // 查询用户id
            params.setRegisterTimeS(date.get(0));
            params.setRegisterTimeE(date.get(1));
        }
        if (notEmpty(params.getPayTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getPayTimeStr());
            params.setPayTimeS(date.get(0));
            params.setPayTimeE(date.get(1));
        }
        if (notEmpty(params.getWatchTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getWatchTimeStr());
            params.setWatchTimeS(date.get(0));
            params.setWatchTimeE(date.get(1));
        }
        if (notEmpty(params.getAdCostTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getAdCostTimeStr());
            params.setAdCostTimeS(date.get(0));
            params.setAdCostTimeE(date.get(1));
        }
        params.setSortOrder(1);
        return dramaAnalysisService.getWatchRechargeAnalysisList(params);
    }

    /**
     * 用户K币消费分析
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "用户K币消费分析", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "watchTimeStr:0:str:观看时间：yyyy-MM-dd - yyyy-MM-dd",
    })
    @ApiParamsOut({
            "results>>dramaId:短剧id",
            "results>>dramaName:短剧名称",
            "results>>coinConsume:金币消费总数",
            "results>>coinRechargeConsume:金币消费充值数",
            "results>>coinGiveConsume:金币消费赠送数",
    })
    @Slave
    @RequestMapping(value = "/getCoinConsumeAnalysisList")
    public ResultVO<?> getCoinConsumeAnalysisList(HttpServletRequest request, DramaAnalysisPO params) {
        SessionVO sessionVO = getSessionVO(request);
        // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
        String key = StaticVar.OPERATION_RATE + sessionVO.getUserId();
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            return ResultVO.error("您当前有正在查询的任务, 请勿重复操作");
        } else {
            RedisUtil.set(key, "1", RedisUtil.TIME_10M);
        }
        try {
            StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds());
            params.setContentType(sessionVO.getContentType());

            // 优化师和链接id搜索
            if (notEmpty(params.getAdvUserIds()) || notEmpty(params.getLinkIds())) {
                FastLinkPO query = new FastLinkPO();
                query.setAdvUserIds(params.getAdvUserIds());
                query.setIds(params.getLinkIds());
                Set<Integer> linkIds = linkService.queryLinkIds(query);
                if (CollUtil.isEmpty(linkIds)) {
                    return ResultVO.success(getDefaultListData());
                }
                params.setLinkIds(StrUtil.join(linkIds));
            }
            if (notEmpty(params.getWatchTimeStr())) {
                List<Date> date = DateUtil.getStartEndDate(params.getWatchTimeStr());
                params.setWatchTimeS(date.get(0));
                params.setWatchTimeE(date.get(1));
            }
            params.setSortOrder(1);
            return dramaAnalysisService.getCoinConsumeAnalysisList(params);
        } catch (Exception e) {
            return ResultVO.error(e.getMessage());
        } finally {
            RedisUtil.del(key);
        }
    }

    /**
     * 短剧数据明细表
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "短剧数据明细表", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "dramaId:0:int:短剧id-数字(全部传空)",
            "createTimeStr:0:str:统计时间：yyyy-MM-dd - yyyy-MM-dd",
            "sortType:0:int:排序字段：1=观看人数;2=K币总消耗数;3=完播统计(人数);4=广告消耗;5=新增ROI;6=付费充值;7=免费充值;8=免费广告收入",
            "sortOrder:0:int:排序顺序：1=倒序;2=正序",
    })
    @ApiParamsOut({
            "results>>dramaId:短剧id",
            "results>>encryptionId:短剧加密id",
            "results>>dramaName:短剧名称",
            "results>>watchMemberCount:观看人数",
            "results>>coinConsume:金币消费总数",
            "results>>coinGiveConsume:金币消费赠送数",
            "results>>watchFinishMemberCount:短剧完播人数",
            "results>>watchFinishRate:完播率(不含%)",
            "results>>rechargeMoneySumAddPay:新增充值金额-付费",
            "results>>rechargeMoneySumAddFree:新增充值金额-免费",
            "results>>rechargeMoneySumPay:充值金额-付费",
            "results>>rechargeMoneySumFree:充值金额-免费",
            "results>>rechargeMoneyProfitSumAddPay:新增实到金额-付费",
            "results>>rechargeMoneyProfitSumAddFree:新增实到金额-免费",
            "results>>rechargeMoneyProfitSumPay:实到金额-付费",
            "results>>rechargeMoneyProfitSumFree:实到金额-免费",
            "results>>adIncomeMoneySum:广告收入-免费",
            "results>>adIncomeMoneySumAdd:新增广告收入-免费",
            "results>>rechargeMemberCount:充值人数",
            "results>>rechargeMemberCountAdd:新增充值人数",
            "results>>releaseDate:上线日期",
            "results>>adMoneyConsume:广告消耗金额",
            "results>>adMoneyConsumePay:广告消耗金额-付费",
            "results>>adMoneyConsumeFree:广告消耗金额-免费",
            "results>>roi:roi",
            "results>>roiAdd:新增roi",
            "results>>roiAddPay:新增roi-付费",
            "results>>roiAddFree:新增roi-免费",
    })
    @Slave
    @RequestMapping(value = "/getDramaDetailList")
    public ResultVO<?> getDramaDetailList(HttpServletRequest request, DramaAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());

        if (params.getSortType() == null) {
            params.setSortType(1);
        }
        if (params.getSortOrder() == null) {
            params.setSortOrder(1);
        }
        // 优化师和链接id搜索
        if (notEmpty(params.getAdvUserIds()) || notEmpty(params.getLinkIds())) {
            FastLinkPO query = new FastLinkPO();
            query.setAdvUserIds(params.getAdvUserIds());
            query.setIds(params.getLinkIds());
            Set<Integer> linkIds = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(linkIds)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        if (notEmpty(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
            params.setWatchTimeS(date.get(0));
            params.setWatchTimeE(date.get(1));
            params.setPayTimeS(date.get(0));
            params.setPayTimeE(date.get(1));
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            return exportService.exportDramaDetailList(sessionVO, params);
        } else {
            List<DramaAnalysisPO> list = dramaAnalysisService.getDramaDetailList(params);
            if (list == null) {
                return ResultVO.success(getDefaultListData());
            }
            return ResultVO.success(list);
        }
    }

    /**
     * 短剧单日明细表
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "短剧单日明细表", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "retailIds:0:str:分销商id-数字(多个逗号分隔)",
            "miniIds:0:str:(应用)小程序id-数字(多个逗号分隔)",
            "advUserIds:0:int:优化师(投手)id",
            "linkIds:0:str:链接id-数字(多个逗号分隔)",
            "dramaId:0:int:短剧id-数字(全部传空)",
            "createTimeStr:0:str:统计时间：yyyy-MM-dd - yyyy-MM-dd",
            "sortType:0:int:排序字段：1=观看人数;2=K币总消耗数;3=完播统计(人数);4=广告消耗;5=新增ROI;6=付费充值;7=免费充值;8=免费广告收入",
            "sortOrder:0:int:排序顺序：1=倒序;2=正序",
    })
    @ApiParamsOut({
            "results>>dramaId:短剧id",
            "results>>dramaName:短剧名称",
            "results>>dataDay:日期 yyyy-MM-dd",
            "results>>watchMemberCount:观看人数",
            "results>>coinConsume:金币消费总数",
            "results>>coinGiveConsume:金币消费赠送数",
            "results>>watchFinishMemberCount:短剧完播人数",
            "results>>watchFinishRate:完播率(不含%)",
            "results>>rechargeMoneySumAddPay:新增充值金额-付费",
            "results>>rechargeMoneySumAddFree:新增充值金额-免费",
            "results>>rechargeMoneyProfitSumAddPay:新增实到金额-付费",
            "results>>rechargeMoneyProfitSumAddFree:新增实到金额-免费",
            "results>>rechargeMoneySumPay:充值金额-付费",
            "results>>rechargeMoneySumFree:充值金额-免费",
            "results>>rechargeMoneyProfitSumPay:实到金额-付费",
            "results>>rechargeMoneyProfitSumFree:实到金额-免费",
            "results>>adIncomeMoneySum:广告收入-免费",
            "results>>adIncomeMoneySumAdd:新增广告收入-免费",
            "results>>rechargeMemberCount:充值人数",
            "results>>rechargeMemberCountAdd:新增充值人数",
            "results>>releaseDate:上线日期",
            "results>>adMoneyConsume:广告消耗金额",
            "results>>adMoneyConsumePay:广告消耗金额-付费",
            "results>>adMoneyConsumeFree:广告消耗金额-免费",
            "results>>roi:roi",
            "results>>roiAdd:新增roi",
            "results>>roiAddPay:新增roi-付费",
            "results>>roiAddFree:新增roi-免费",
    })
    @Slave
    @RequestMapping(value = "/getDramaDayDetailList")
    public ResultVO<?> getDramaDayDetailList(HttpServletRequest request, DramaAnalysisPO params, PageVO pageVO) {
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getMiniIds(), params.getOfficialIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());

        if (params.getSortType() == null) {
            params.setSortType(0);
        }
        if (params.getSortOrder() == null) {
            params.setSortOrder(1);
        }
        if (params.getDramaId() == null) {
            return ResultVO.error("短剧id不能为空");
        }
        setDramaIdContext(params);
        // 优化师和链接id搜索
        if (notEmpty(params.getAdvUserIds()) || notEmpty(params.getLinkIds())) {
            FastLinkPO query = new FastLinkPO();
            query.setAdvUserIds(params.getAdvUserIds());
            query.setIds(params.getLinkIds());
            Set<Integer> linkIds = linkService.queryLinkIds(query);
            if (CollUtil.isEmpty(linkIds)) {
                return ResultVO.success(getDefaultListData());
            }
            params.setLinkIds(StrUtil.join(linkIds));
        }
        if (notEmpty(params.getCreateTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));
            params.setWatchTimeS(date.get(0));
            params.setWatchTimeE(date.get(1));
            params.setPayTimeS(date.get(0));
            params.setPayTimeE(date.get(1));
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = exportService.exportDramaDayDetailList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        } else {
            List<DramaAnalysisPO> list = dramaAnalysisService.getDramaDayDetailList(params);
            if (list == null) {
                return ResultVO.success(getDefaultListData());
            }
            return ResultVO.success(list);
        }
    }

    /**
     * 财务统计-用户充消表
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "财务统计-用户充消表", folder = {StaticFolder.FOLDER_ORDER})
    @ApiParamsIn({
            "createTimeStr:0:str:交易月份：yyyy-MM",
            "miniIds:0:str:小程序id：数字, 多个逗号分隔",
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "monthStr:交易月份 yyyy-MM",
            "miniName:应用名称",
            "moneyRecharge:用户充值金额",
            "coinRecharge:用户充值K币",
            "coinGive:用户赠送K币",
            "coinRechargeConsume:用户消耗充值K币",
            "coinRechargeRemain:用户剩余未消耗充值K币",
            "coinGiveConsume:用户消耗赠送K币",
            "coinGiveRemain:用户剩余未消耗赠送K币",
            "moneyVipRecharge:用户充值VIP金额",
            "moneyVipConsume:用户消耗VIP金额",
            "moneyVipRemain:用户剩余VIP金额",
    })
    @Slave
    @RequestMapping(value = "/getFinanceRechargeCoinList")
    public ResultVO<?> getFinanceRechargeCoinList(HttpServletRequest request, FastMemberCoinMoneyMonthPO params) {
        // 判断是否被sql注入
        StrUtil.checkMysqlInData(params.getMiniIds());
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());

        // 处理时间
        if (notEmpty(params.getCreateTimeStr())) {
            params.setMonthStr(params.getCreateTimeStr());
            params.setMonth(toInteger(DateUtil.dateReplace2Int(params.getCreateTimeStr())));
        } else {
            return ResultVO.error("月份必填");
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notEmpty(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = exportService.exportMemberRechargeCoinList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        } else {
            List<FastMemberCoinMoneyMonthPO> list = dataRechargeService.getMemberRechargeCoinList2(params);
            if (list == null) {
                return ResultVO.success(getDefaultListData());
            }
            return ResultVO.success(list);
        }
    }
}
