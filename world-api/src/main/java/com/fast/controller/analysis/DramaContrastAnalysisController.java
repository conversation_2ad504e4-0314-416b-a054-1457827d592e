/*
 * Powered By fast.up
 */
package com.fast.controller.analysis;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.service.analysis.DramaContrastAnalysisService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 短剧数据对比
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/analysis")
public class DramaContrastAnalysisController extends BaseController {

    @Autowired
    private DramaContrastAnalysisService contrastAnalysisService;

    /**
     * 短剧数据对比-数据对比分析
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "短剧数据对比-数据对比分析", folder = {StaticFolder.FOLDER_ANALYSIS_DRAMA})
    @ApiParamsIn({
            "list是一个json数据",
            "list>>dramaId:1:int:短剧id-数字",
            "list>>releaseDateStr:1:str:上线日期 yyyy-MM-dd",
            "list>>retailIds:0:str:分销商id-数字",
            "list>>advUserIds:0:int:优化师(投手)id",
            "list>>linkIds:0:str:链接id-数字",
            "contrastDataDay:1:int:对比数据范围-天数",
            "contrastDataType:1:int:对比数据维度 1=广告消耗金额;2=充值金额;3=ROI;4=K币消耗值;5=充值人数;6=观看人数;7=完播率;8=留存率;9=复充率;" +
                    "10=订阅人数;11=订阅金额",
    })
    @ApiParamsOut({
            "results>>dramaId:短剧id",
            "results>>dramaName:短剧名称",
            "results>>dataSum:累计数据",
            "results>>startNum:链接对应的付费起始集(数字)",
            "results>>list>>day:(contrastDataType<=7) 第(1,2,3,4....)天",
            "results>>list>>seriesNum:(留存率专属) 第(1,2,3,4....)集号",
            "results>>list>>rechargeCount:(复充率专属) 第N充",
            "results>>list>>memberCount:(留存/复充率专属) 人数统计数",
            "results>>list>>data:Y轴数据指标/留存率/复充率",
    })
    @Slave
    @RequestMapping(value = "/getDramaContrastCoreAnalysis")
    public ResultVO<?> getDramaContrastCoreAnalysis(HttpServletRequest request, @RequestBody DramaAnalysisPO params) {
        StrUtil.checkMysqlInData(params.getRetailIds(), params.getAdvUserIds(), params.getLinkIds());
        List<DramaAnalysisPO> paramsList = params.getList();
        if (CollUtil.isEmpty(paramsList)) {
            return ResultVO.error("list不能为空");
        }
        for (DramaAnalysisPO po : paramsList) {
            StrUtil.checkMysqlInData(po.getRetailIds(), po.getAdvUserIds(), po.getLinkIds());
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());

        if (params.getContrastDataDay() == null || params.getContrastDataDay() <= 0 || params.getContrastDataDay() > 180) {
            return ResultVO.error("对比数据范围不合法");
        }
        List<JSONObject> list = contrastAnalysisService.getDramaContrastCoreAnalysis(params, paramsList);
        if (list == null) {
            return ResultVO.success(getDefaultListData());
        }
        return ResultVO.success(list);
    }
}
