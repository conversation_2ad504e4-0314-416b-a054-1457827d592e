/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.fee.FastFeeGearRestrictPO;
import com.fast.service.fee.FastFeeGearRestrictService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeGearRestrict")
public class FastFeeGearRestrictController extends BaseController {

    @Autowired
    private FastFeeGearRestrictService fastFeeGearRestrictService;

    @ApiName(value = "fee-档位限制查询列表", folder = {"fee"})
    @ApiParamsIn({
            "name:1:str:规则名称"
    })
    @ApiParamsOut({
            "name:规则名称",
            "state:0无效1有效",
            "vipFlag:类型vip0否1是",
            "vipStart:限制开始金额",
            "vipEnd:限制结束金额",
            "rechargeFlag:类型k币0否1是",
            "rechargeStart:k币限制开始金额",
            "rechargeEnd:k币限制结束金额",
            "dramaCardFlag:类型剧卡0否1是",
            "dramaCardStart:剧卡开始金额",
            "dramaCardEnd:剧卡结束金额",
            "restrictDays:限制天数(废弃)",
            "retailIds:分销商id（废弃）",
            "retailNames:分销商名称（废弃）"
    })
    @PostMapping("/getFastFeeGearRestrictList")
    public ResultVO<?> getList(HttpServletRequest request, FastFeeGearRestrictPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        return fastFeeGearRestrictService.queryPageList(params, pageVO);
    }

    @ApiName(value = "fee-档位限制查询单个详情", folder = {"fee"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id"
    })
    @ApiParamsOut({
            "name:规则名称",
            "state:0无效1有效",
            "vipFlag:类型vip0否1是",
            "rechargeFlag:类型k币0否1是",
            "vipStart:限制开始金额",
            "vipEnd:限制结束金额",
            "rechargeStart:k币限制开始金额",
            "rechargeEnd:k币限制结束金额",
            "restrictDays:限制天数",
            "retailIds:分销商id",
            "retailNames:分销商名称"
    })
    @PostMapping("/getFastFeeGearRestrictDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastFeeGearRestrictPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFeeGearRestrictPO fastFeeGearRestrict = fastFeeGearRestrictService.queryById(params);
        return ResultVO.success(fastFeeGearRestrict);
    }

    @ApiName(value = "fee-档位限制添加", folder = {"fee"})
    @ApiParamsIn({
            "name:1:str:规则名称",
            "vipFlag:1:int:类型vip0否1是",
            "vipStart:限制开始金额",
            "vipEnd:限制结束金额",
            "rechargeFlag:1:int:类型k币0否1是",
            "rechargeStart:k币限制开始金额",
            "rechargeEnd:k币限制结束金额",
            "unvipFlag:1:int:类型vip0否1是",
            "unvipStart:限制开始金额",
            "unvipEnd:限制结束金额",
            "unrechargeFlag:1:int:类型k币0否1是",
            "unrechargeStart:k币限制开始金额",
            "unrechargeEnd:k币限制结束金额",
            "dramaCardFlag:1:int:类型剧卡0否1是（已充值）",
            "dramaCardStart:剧卡限制开始金额（已充值）",
            "dramaCardEnd:剧卡限制结束金额（已充值）",
            "undramaCardFlag:1:int:类型剧卡0否1是（未充值）",
            "undramaCardStart:剧卡限制开始金额（未充值）",
            "undramaCardEnd:剧卡限制结束金额（未充值）",
            "restrictDays:0:int:限制天数",
            "restrictJson:0:str:更多限制规则{// vip当日可用\"vipDaysLimit\"{flag ： 0,priceStart： 10,priceEnd ： 15},// 大7天vip档位金额\"vipPriceLimit\"：{flag ： 1,priceStart ： 20},// 单集K币限制\"coinCountLimit\"：{flag ： 1,countStart ： 100},\"coinGiftCountLimit\" // 赠币限制 ：{flag ： 0,times ： 1 // 1倍}}",
            "retailIds:0:str:分销商id"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insertFastFeeGearRestrict")
    public ResultVO<?> insert(HttpServletRequest request, FastFeeGearRestrictPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setState(0);// 默认关闭规则
        params.setContentType(sessionVO.getContentType());
        MethodVO methodVO = fastFeeGearRestrictService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fee-档位限制更新", folder = {"fee"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "name:1:str:规则名称",
            "vipFlag:1:int:类型vip0否1是",
            "rechargeFlag:1:int:类型k币0否1是",
            "vipStart:限制开始金额",
            "vipEnd:限制结束金额",
            "rechargeStart:k币限制开始金额",
            "rechargeEnd:k币限制结束金额",
            "unvipFlag:1:int:类型vip0否1是",
            "unrechargeFlag:1:int:类型k币0否1是",
            "unvipStart:限制开始金额",
            "unvipEnd:限制结束金额",
            "unrechargeStart:k币限制开始金额",
            "unrechargeEnd:k币限制结束金额",
            "restrictDays:限制天数",
            "restrictJson:0:str:更多限制规则{// vip当日可用\"vipDaysLimit\"{flag ： 0,priceStart： 10,priceEnd ： 15},// 大7天vip档位金额\"vipPriceLimit\"：{flag ： 1,priceStart ： 20},// 单集K币限制\"coinCountLimit\"：{flag ： 1,countStart ： 100},\"coinGiftCountLimit\" // 赠币限制 ：{flag ： 0,times ： 1 // 1倍}}",
            "retailIds:分销商id"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateFastFeeGearRestrict")
    public ResultVO<?> update(HttpServletRequest request, FastFeeGearRestrictPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeGearRestrictService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fee-档位限制更新状态", folder = {"fee"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "state:1:int:状态0无效1有效"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateState")
    public ResultVO<?> updateState(HttpServletRequest request, FastFeeGearRestrictPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        FastFeeGearRestrictPO grParam = new FastFeeGearRestrictPO();
        grParam.setUpdatorId(sessionVO.getUserId());
        grParam.setId(id);
        grParam.setState(params.getState());
        MethodVO methodVO = fastFeeGearRestrictService.update(grParam);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fee-档位限制删除", folder = {"fee"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/del")
    public ResultVO<?> del(HttpServletRequest request, FastFeeGearRestrictPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        FastFeeGearRestrictPO grParam = new FastFeeGearRestrictPO();
        grParam.setUpdatorId(sessionVO.getUserId());
        grParam.setId(id);
        grParam.setDelFlag(1);
        MethodVO methodVO = fastFeeGearRestrictService.update(grParam);
        return ResultVO.fromMethodVO(methodVO);
    }
}
