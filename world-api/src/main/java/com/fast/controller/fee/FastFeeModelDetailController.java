/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.service.fee.FastFeeModelDetailService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeModelDetail")
public class FastFeeModelDetailController extends BaseController {

    @Autowired
    private FastFeeModelDetailService fastFeeModelDetailService;

    @ApiName(value = "费用相关-查询列表", folder = {StaticFolder.FOLDER_FEE})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastFeeModelDetailPO params, PageVO pageVO) {
        return fastFeeModelDetailService.queryPageList(params, pageVO);
    }

    @ApiName(value = "费用相关-查询单个详情", folder = {StaticFolder.FOLDER_FEE})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastFeeModelDetailPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFeeModelDetailPO fastFeeModelDetail = fastFeeModelDetailService.queryById(params);
        return ResultVO.success(fastFeeModelDetail);
    }

    @ApiName(value = "费用相关-添加", folder = {StaticFolder.FOLDER_FEE})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastFeeModelDetailPO params) {
        SessionVO sessionUser = getSessionVO(request);
        params.setCreatorId(sessionUser.getUserId());
        MethodVO methodVO = fastFeeModelDetailService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "费用相关-更新", folder = {StaticFolder.FOLDER_FEE})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastFeeModelDetailPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeModelDetailService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
