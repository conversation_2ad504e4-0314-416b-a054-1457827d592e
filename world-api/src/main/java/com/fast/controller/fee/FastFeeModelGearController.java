/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.*;
import com.fast.po.fee.FastFeeModelGearPO;
import com.fast.service.fee.FastFeeModelGearService;
import com.fast.utils.GooglePlayUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.google.api.services.androidpublisher.model.InAppProduct;
import com.google.api.services.androidpublisher.model.Price;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeModelGear")
public class FastFeeModelGearController extends BaseController {

    @Autowired
    private FastFeeModelGearService fastFeeModelGearService;

    @Autowired
    private GooglePlayUtil googlePlayUtil;

    @ApiName(value = "费用档位-查询列表", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "type:1:int:1充值档位，2vip卡档位列表",
            "miniId:0:int:小程序id",
            "size:0:int:每页条数",
            "page:0:int:页码"
    })
    @ApiParamsOut({
            "type:1充值，2vip卡",
            "title:vip卡名称",
            "moneyRecharge:充值金额/购买金额，2位小数",
            "moneyUnit:单价，2位小数",
            "coinGive:赠送币",
            "corner:角标内容",
            "timesLimit:用户充值次数限制为0不限制",
            "validDate:有效期n月",
            "remark:vip卡描述",
            "contractFlag:是否签约模版 1是 0不是",
            "miniId:小程序id",
            "miniName:小程序名称",
            "contractTemplateId:签约模版id",
            "contractMoneyRecharge:包月金额",
            "state:1启用 0禁用",
            "restrictFlag:是否限制日期标志0否1是",
            "productId:google商品ID",
            "appleProductId:apple商品ID",
    })
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastFeeModelGearPO params, PageVO pageVO) {
        if (params.getType() == null) {
            return ResultVO.error("档位类型不能为空");
        }
        // 默认非签约模版
        if (Objects.isNull(params.getContractFlag())) {
            params.setContractFlag(0);
        }
        SessionVO sessionVO = getSessionVO(request);
//        params.setRetailId(sessionVO.getRetailId());
        params.setRetailIds(sessionVO.getRetailId() + ",0");
        return fastFeeModelGearService.queryPageList(params, pageVO);
    }

    @ApiName(value = "费用档位-查询单个详情", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "encryptionId:1:int:加密的档位id"
    })
    @ApiParamsOut({
            "phoneOs:手机系统：1安卓，2爱疯",
            "type:1充值，2vip卡",
            "title:vip卡名称",
            "moneyRecharge:充值金额/购买金额，一位小数",
            "coinGive:赠送币",
            "corner:角标内容",
            "timesLimit:用户充值次数限制为0不限制",
            "validDate:有效期n月",
            "contractFlag:是否签约模版 1是 0不是",
            "miniId:小程序id",
            "miniName:小程序名称",
            "contractTemplateId:签约模版id",
            "contractMoneyRecharge:包月金额",
            "state:1启用 0禁用",
            "remark:vip卡描述",
            "productId:google商品ID",
            "appleProductId:apple商品ID",
    })
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastFeeModelGearPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        FastFeeModelGearPO fastFeeModelGear = fastFeeModelGearService.queryById(params);
        return ResultVO.success(fastFeeModelGear);
    }

    @ApiName(value = "费用档位-抓取google商品价格", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "productId:1:str:google商品ID"
    })
    @ApiParamsOut({
            "results:美元"
    })
    @RequestMapping(value = "/getGpSkuPrice", method = {RequestMethod.POST})
    public ResultVO<?> getGpSku(HttpServletRequest request, String productId) {
        if (StrUtil.isEmpty(productId)) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        InAppProduct product = googlePlayUtil.getInAppProduct(productId);
        if (product == null) {
            return ResultVO.error("商品不存在");
        }
        Price us = product.getPrices().get("US");
        if (us == null) {
            return ResultVO.error("请先同步汇率");
        }
        BigDecimal price = new BigDecimal(us.getPriceMicros()).divide(new BigDecimal(1000000), 2, RoundingMode.HALF_UP);
        log.info("GP商品 {} 对应的US价格 {}", productId, price);
        return ResultVO.success(price);
    }

    @ApiName(value = "费用档位-查询单个详情", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "encryptionId:1:int:加密的档位id",
            "state:1:int:1启用 0禁用",
    })
    @ApiParamsOut({
            "ok:ok",
    })
    @RequestMapping(value = "/updateState", method = {RequestMethod.POST})
    public ResultVO<?> updateState(HttpServletRequest request, FastFeeModelGearPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        if (Objects.isNull(params.getState())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        FastFeeModelGearPO updatePo = new FastFeeModelGearPO();
        updatePo.setId(id);
        updatePo.setState(params.getState());
        return ResultVO.fromMethodVO(fastFeeModelGearService.updateState(updatePo));
    }

    @ApiName(value = "费用档位-添加", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "type:1:int:1充值，2vip卡",
            "title:1:str:vip卡名称",
            "moneyRecharge:1:double:充值金额/购买金额，一位小数",
            "coinGive:1:int:赠送币",
            "corner:0:str:角标内容",
            "timesLimit:0:int:用户充值次数限制为0不限制",
            "validDate:0:int:有效期n月",
            "moneyUnit:0:str:单价",
            "remark:0:str:vip卡描述",
            "contractFlag:0:str:是否签约模版 1是 0不是",
            "miniId:0:str:小程序id",
            "contractTemplateId:0:str:签约模版id",
            "contractMoneyRecharge:0:str:包月金额",
            "state:0:str:1启用 0禁用",
            "vipType:0:str:vip类型1全场vip2在线vip",
            "productId:1:str:google商品ID",
            "appleProductId:1:str:apple商品ID",
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastFeeModelGearPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (Objects.isNull(params.getMiniId())) {
            return ResultVO.error("生效应用不能为空");
        }
        if (StrUtil.isBlank(params.getProductId())) {
            return ResultVO.error("Google商品ID不能为空");
        }
        if (StrUtil.isBlank(params.getAppleProductId())) {
            return ResultVO.error("Apple商品ID不能为空");
        }
        if (params.getType() == null) {
            return ResultVO.error("type不能为空");
        }
        if (params.getMoneyRecharge() == null) {
            return ResultVO.error("金额不能为空");
        }
        if (params.getMoneyRecharge().compareTo(BigDecimal.ZERO) <= 0) {
            return ResultVO.error("金额不能小于等于0");
        }
        if (params.getMoneyRecharge().compareTo(BigDecimalVar.BD_9999_99) > 0) {
            return ResultVO.error("金额不能大于9999.99");
        }
        params.setContractFlag(ObjectUtils.defaultIfNull(params.getContractFlag(), 0));
        if (params.getContractFlag() == 1) {
            if (Objects.isNull(params.getMiniId()) || StrUtil.isBlank(params.getContractTemplateId()) || Objects.isNull(params.getContractMoneyRecharge())) {
                return ResultVO.error("签约参数错误!");
            }
        }
        params.setCreatorId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId());
        Integer coinRecharge = params.getMoneyRecharge().multiply(BigDecimal.valueOf(StaticVar.CHARGE_RATE)).intValue();
        params.setCoinRecharge(coinRecharge);
        if (params.getType() == 1) {
            // 充值卡
            if (params.getCoinGive() == null) {
                params.setCoinGive(0);
            }
            if (params.getCoinGive() > 99999) {
                return ResultVO.error("赠币不能超过99999");
            }
            if (params.getCoinGive() < 0) {
                return ResultVO.error("赠币不能小于0");
            }
            params.setValidDate(0);
        } else if (params.getType() == 2) {
            // vip卡
            // if (StrUtil.isEmpty(params.getTitle())) {
            //     return ResultVO.error("名称不能为空");
            // }
            if (params.getValidDate() == null) {
                return ResultVO.error("有效期不能为空");
            }
            if (params.getValidDate() > 65535) {
                return ResultVO.error("有效期不能超过65535");
            }
            if (params.getValidDate() <= 0) {
                return ResultVO.error("有效期不能小于等于0");
            }
            if (params.getValidUnit() == null) {
                return ResultVO.error("有效期单位不能为空");
            }
            if (!StrUtil.equalsAny(params.getValidUnit(), 1, 2, 3, 4, 5)) {
                return ResultVO.error("有效期单位不合法");
            }
            if (params.getMoneyUnit() == null || params.getMoneyUnit().compareTo(BigDecimal.ZERO) < 0) {
                return ResultVO.error("单价不合法");
            }
        } else if (params.getType() == 3) {
            // 剧卡
            if (StrUtil.isEmpty(params.getTitle())) {
                return ResultVO.error("名称不能为空");
            }
        } else {
            return ResultVO.error("type不合法");
        }
        params.setContentType(sessionVO.getContentType());
        MethodVO methodVO = fastFeeModelGearService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }
//
//    @ApiName(value="费用档位-更新",folder= {StaticFolder.FOLDER_FEE})
//    @RequestMapping(value = "/update", method = { RequestMethod.POST })
//    public ResultVO<?> update(HttpServletRequest request, FastFeeModelGearPO params) {
//        if (isBlank(params.getEncryptionId())) {
//            return ResultVO.error(StaticStr.ERROR_PARAM);
//        }
//        Integer id = decodeInt(params.getEncryptionId());
//        if (id == null) {
//            return ResultVO.error(StaticStr.ERROR_PARAM);
//        }
//        params.setId(id);
//        SessionVO sessionVO = getSessionVO(request);
//        params.setUpdatorId(sessionVO.getUserId());
//        MethodVO methodVO = fastFeeModelGearService.update(params);
//        return ResultVO.fromMethodVO(methodVO);
//    }

    @ApiName(value = "费用档位-删除", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({"encryptionId:1:str:档位加密id"})
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    public ResultVO<?> delete(HttpServletRequest request, FastFeeModelGearPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        MethodVO methodVO = fastFeeModelGearService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
