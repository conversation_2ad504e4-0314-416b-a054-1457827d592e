/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticFolder;
import com.fast.constant.StaticStr;
import com.fast.po.fee.FastFeeModelBatchPO;
import com.fast.po.fee.FastFeeModelBatchUserPO;
import com.fast.po.fee.FastFeeModelPO;
import com.fast.service.fee.FastFeeModelBatchService;
import com.fast.service.fee.FastFeeModelBatchUserService;
import com.fast.service.fee.FastFeeModelService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeModel")
public class FastFeeModelController extends BaseController {

    @Autowired
    private FastFeeModelService fastFeeModelService;
    @Autowired
    private FastFeeModelBatchService feeModelBatchService;
    @Autowired
    private FastFeeModelBatchUserService feeModelBatchUserService;

    @ApiName(value = "充值模板-查询列表", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "miniId:1:int:小程序id",
            "type:1:int:1充值，2vip卡",
            "modelType:1:int:0未充值，1已充值"
    })
    @ApiParamsOut({
            "list:充值模板列表",
            "list>>detailList:模板列表详情集合"
    })
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastFeeModelPO params, PageVO pageVO) {
        if (params.getType() == null) {
            return ResultVO.error("类型不能为空 ");
        }
        if (params.getModelType() == null) {
            return ResultVO.error("是否充值不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        return fastFeeModelService.queryPageList(params, pageVO);
    }

    @ApiName(value = "充值模板-查询平台默认列表", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "miniId:1:int:小程序id"
    })
    @ApiParamsOut({
            "unpayRechargeList:未支付充值",
            "payRechargeList:已支付充值",
            "unpayVipList:未支付vip卡",
            "payVipList:已支付vip卡"
    })
    @RequestMapping(value = "/getGlobalList", method = {RequestMethod.POST})
    public ResultVO<?> getGlobalList(HttpServletRequest request, FastFeeModelPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getMiniId() == null) {
            return ResultVO.error("小程序id不能为空");
        }
        params.setRetailId(0);
        return fastFeeModelService.queryGlobalList(params);
    }

    @ApiName(value = "充值模板-查询平台默认列表", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "miniId:1:int:小程序id"
    })
    @ApiParamsOut({
            "unpayRechargeList:未支付充值",
            "payRechargeList:已支付充值",
            "unpayVipList:未支付vip卡",
            "payVipList:已支付vip卡"
    })
    @RequestMapping(value = "/getLinkFeeModelList", method = {RequestMethod.POST})
    public ResultVO<?> getLinkFeeModelList(HttpServletRequest request, FastFeeModelPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getMiniId() == null) {
            return ResultVO.error("小程序id不能为空");
        }
        if (params.getRetailId() == null) {
            return ResultVO.error("分销商id不能为空");
        }
        if (params.getOfficialId() == null) {
            return ResultVO.error("公众号id不能为空");
        }
        if (params.getLinkId() == null) {
            return ResultVO.error("链接id不能为空");
        }
        return fastFeeModelService.queryLinkFeeModelList(params);
    }

    @ApiName(value = "充值模板-查询单个详情", folder = {StaticFolder.FOLDER_FEE})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastFeeModelPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFeeModelPO fastFeeModel = fastFeeModelService.queryById(params);
        return ResultVO.success(fastFeeModel);
    }

    @ApiName(value = "充值模板/vip卡-添加", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "miniId:1:int:小程序id",
            "officialId:1:int:公众号id",
            "modelType:1:int:0未充值，1已充值",
            "type:1:int:1充值，2vip卡",
            "gearIds:1:str:档位id集合，逗号分割，按顺序给",
            "defaultGearId:1:int:默认档位id"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastFeeModelPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getMiniId() == null) {
            return ResultVO.error("小程序id不能为空");
        }
        if (sessionVO.getRetailId() > 0 && params.getOfficialId() == null) {
            return ResultVO.error("公众号id不能为空");
        }
        if (params.getModelType() == null) {
            return ResultVO.error("充值类型不能为空");
        }
        if (StrUtil.isEmpty(params.getGearIds())) {
            return ResultVO.error("选择的档位不能为空");
        }
        if (params.getType() == 1 && params.getGearIds().split(",").length < 2) {
            return ResultVO.error("档位个数不能少于2个");
        }
        if (params.getType() == 1 && params.getGearIds().split(",").length > 8) {
            return ResultVO.error("档位个数不能大于8个");
        }
        if (params.getType() == 2 && params.getGearIds().split(",").length < 1) {
            return ResultVO.error("档位个数不能少于1个");
        }
        if (params.getType() == 2 && params.getGearIds().split(",").length > 3) {
            return ResultVO.error("档位个数不能大于3个");
        }
        if (params.getDefaultGearId() == null) {
            return ResultVO.error("默认档位不能为空");
        }
        params.setContentType(sessionVO.getContentType());
        params.setRetailId(sessionVO.getRetailId());
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeModelService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "优化师/全局自定义充值模板添加（四个一起）230926", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "batchName:1:str:批次名称",
            "global:1:int:是否全局0否1是",
            "addType:1:int:添加来源1自主添加2通用模板复制",
            "commonFlag:1:int:是否通用0否1是",
            "paramsList 》miniId:1:int:小程序id",
            "paramsList 》officialId:1:int:公众号id",
            "paramsList 》modelType:1:int:0未充值，1已充值",
            "paramsList 》type:1:int:1充值，2vip卡",
            "paramsList 》gearIds:1:str:档位id集合，逗号分割，按顺序给",
            "paramsList 》defaultGearId:1:int:默认档位id",
            "paramsList:1:str:例子：[{'gearIds':'1,2,8,5','gearGive':'1#330,8#550','miniId':1,'modelType':1,'type':1,'defaultGearId':2},{'gearIds':'1,2,8,5','miniId':1,'modelType':2,'type':1,'defaultGearId':2}]"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/insertBatch", method = {RequestMethod.POST})
    public ResultVO<?> insertBatch(HttpServletRequest request, @RequestBody FastFeeModelBatchPO param) {
        SessionVO sessionVO = getSessionVO(request);
        param.setCreatorId(sessionVO.getUserId());
        param.setUpdatorId(sessionVO.getUserId());
        param.setRetailId(sessionVO.getRetailId());
        param.setOfficialId(sessionVO.getOfficialId());
        param.setContentType(sessionVO.getContentType());
        if (param.getGlobal() == null) {
            param.setGlobal(0);
        }
        if (param.getCommonFlag() == null) {
            param.setCommonFlag(0);
        }
        if (param.getCommonFlag() == 1) {
            // 通用模板
            param.setOfficialId(0); // 通用的不设置公众号
        }
        List<FastFeeModelPO> paramsList = param.getParamsList();
        if (CollUtil.isEmpty(paramsList)) {
            return ResultVO.error("paramsList不能为空");
        }
        for (FastFeeModelPO params : paramsList) {
            if (params.getMiniId() == null) {
                return ResultVO.error("小程序id不能为空");
            }
            if (sessionVO.getRetailId() > 0 && params.getOfficialId() == null) {
                return ResultVO.error("公众号id不能为空");
            }
            if (params.getModelType() == null) {
                return ResultVO.error("充值类型不能为空");
            }
            if (StrUtil.isEmpty(params.getGearIds())) {
                return ResultVO.error("选择的档位不能为空");
            }
            if (params.getType() == 1 && params.getGearIds().split(",").length < 2) {
                return ResultVO.error("档位个数不能少于2个");
            }
            if (params.getType() == 1 && params.getGearIds().split(",").length > 8) {
                return ResultVO.error("档位个数不能大于8个");
            }
            if (params.getType() == 2 && params.getGearIds().split(",").length < 1) {
                return ResultVO.error("档位个数不能少于1个");
            }
            if (params.getType() == 2 && params.getGearIds().split(",").length > 3) {
                return ResultVO.error("档位个数不能大于3个");
            }
            if (params.getDefaultGearId() == null) {
                return ResultVO.error("默认档位不能为空");
            }
            params.setRetailId(sessionVO.getRetailId());
            params.setCreatorId(sessionVO.getUserId());
            params.setUpdatorId(sessionVO.getUserId());
        }
        MethodVO methodVO = fastFeeModelService.insertBatch(param, paramsList);
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiName(value = "优化师/全局自定义充值模板更新（四个一起）230926", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "batchName:1:str:批次名称",
            "encryptionId:1:str:批次id",
            "paramsList 》miniId:1:int:小程序id",
            "paramsList 》modelId:1:int:模板id",
            "paramsList 》officialId:1:int:公众号id",
            "paramsList 》modelType:1:int:0未充值，1已充值",
            "paramsList 》type:1:int:1充值，2vip卡",
            "paramsList 》gearIds:1:str:档位id集合，逗号分割，按顺序给",
            "paramsList 》defaultGearId:1:int:默认档位id",
            "paramsList:1:str:例子：[{'gearIds':'1,2,8,5','gearGive':'1#330,8#550','miniId':1,'modelType':1,'type':1,'defaultGearId':2},{'gearIds':'1,2,8,5','miniId':1,'modelType':2,'type':1,'defaultGearId':2}]"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateBatch", method = {RequestMethod.POST})
    public ResultVO<?> updateBatch(HttpServletRequest request, @RequestBody FastFeeModelBatchPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error("批次id不能为空");
        }
        Integer batchId = decodeInt(params.getEncryptionId());
        params.setId(batchId);
        params.setUpdatorId(sessionVO.getUserId());
        List<FastFeeModelPO> paramsList = params.getParamsList();
        if (CollUtil.isEmpty(paramsList)) {
            return ResultVO.error("paramsList不能为空");
        }
        for (FastFeeModelPO po : paramsList) {
            if (po.getModelId() == null) {
                return ResultVO.error("模板id不能为空");
            }
            if (po.getMiniId() == null) {
                return ResultVO.error("小程序id不能为空");
            }
            if (sessionVO.getRetailId() > 0 && po.getOfficialId() == null) {
                return ResultVO.error("公众号id不能为空");
            }
            if (po.getModelType() == null) {
                return ResultVO.error("充值类型不能为空");
            }
            if (StrUtil.isEmpty(po.getGearIds())) {
                return ResultVO.error("选择的档位不能为空");
            }
            if (po.getType() == 1 && po.getGearIds().split(",").length < 2) {
                return ResultVO.error("档位个数不能少于2个");
            }
            if (po.getType() == 1 && po.getGearIds().split(",").length > 8) {
                return ResultVO.error("档位个数不能大于8个");
            }
            if (po.getType() == 2 && po.getGearIds().split(",").length < 1) {
                return ResultVO.error("档位个数不能少于1个");
            }
            if (po.getType() == 2 && po.getGearIds().split(",").length > 3) {
                return ResultVO.error("档位个数不能大于3个");
            }
            if (po.getDefaultGearId() == null) {
                return ResultVO.error("默认档位不能为空");
            }
            po.setRetailId(sessionVO.getRetailId());
            po.setCreatorId(sessionVO.getUserId());
            po.setUpdatorId(sessionVO.getUserId());
        }
        MethodVO methodVO = fastFeeModelService.updateBatch(request, params, sessionVO, paramsList);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 优化师充值模板-查询批次列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "优化师充值模板-查询批次列表", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "state:0:int:0禁用1启用",
            "searchKey:0:str:优化师名称"
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "batchName:批次名称",
            "retailId:分销商id",
            "userIds:已关联优化师id(多个逗号分隔)",
            "officialId:公众号id",
            "state:启用状态1启用0禁用",
            "addType:添加来源1自主添加2来自模板复制",
            "userCount:关联的优化师个数",
            "creatorName:创建人"
    })
    @RequestMapping(value = "/queryBatchList", method = {RequestMethod.POST})
    public ResultVO<?> queryBatchList(HttpServletRequest request, FastFeeModelBatchPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        return feeModelBatchService.queryPageList(params, pageVO);
    }

    @ApiName(value = "通用充值模板-查询批次列表（管理员查看）230926", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "state:0:int:0禁用1启用",
            "searchKey:0:str:公众号名称"
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "batchName:批次名称",
            "retailId:分销商id",
            "officialIds:已关联公众号id(多个逗号分隔)",
            "state:启用状态1启用0禁用",
            "officialCount:关联的公众号个数",
            "creatorName:创建人"
    })
    @RequestMapping(value = "/queryCommonBatchList", method = {RequestMethod.POST})
    public ResultVO<?> queryCommonBatchList(HttpServletRequest request, FastFeeModelBatchPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(0);
        params.setContentType(sessionVO.getContentType());
        return feeModelBatchService.queryCommonPageList(params, pageVO);
    }

    @ApiName(value = "通用充值模板-查询批次列表（切换公众号后看）230926", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "state:0:int:0禁用1启用",
            "searchKey:0:str:公众号名称"
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "batchName:批次名称",
            "retailId:分销商id",
            "officialIds:已关联公众号id(多个逗号分隔)",
            "state:启用状态1启用0禁用",
            "officialCount:关联的公众号个数",
            "creatorName:创建人"
    })
    @RequestMapping(value = "/queryBatchOfficialList", method = {RequestMethod.POST})
    public ResultVO<?> queryBatchOfficialList(HttpServletRequest request, FastFeeModelBatchPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        return feeModelBatchService.queryBatchOfficialPageList(params, pageVO);
    }

    @ApiName(value = "优化师充值模板-查询批次模板详情（四个）", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "encryptionId:1:str:批次加密id"
    })
    @ApiParamsOut({
            "type:类型:1=充值金币;2=充值VIP",
            "title:卡名称",
            "moneyRecharge:充值金额",
            "coinRecharge:充值币",
            "coinGive:赠送币",
            "corner:角标内容",
            "timesLimit:用户充值次数限制",
            "validDate:有效期n月",
            "validUnit:单位:1=周;2=月;3=年;4=日",
            "moneyUnit:单价x元/天",
            "remark:描述",
            "defaultFlag:默认标志0否1是",
            "batch 》batchName:批次名称"
    })
    @RequestMapping(value = "/queryBatchDetail", method = {RequestMethod.POST})
    public ResultVO<?> queryBatchDetail(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("加密id不能为空");
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error("加密id不能为空");
        }
        params.setId(id);
        return feeModelBatchService.queryBatchDetail(params);
    }

    @ApiName(value = "自定义全局充值模板-查询订单模板详情（四个）", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "officialId:1:int:公众号id"
    })
    @ApiParamsOut({
            "type:类型:1=充值金币;2=充值VIP",
            "title:卡名称",
            "moneyRecharge:充值金额",
            "coinRecharge:充值币",
            "coinGive:赠送币",
            "corner:角标内容",
            "timesLimit:用户充值次数限制",
            "validDate:有效期n月",
            "validUnit:单位:1=周;2=月;3=年;4=日",
            "moneyUnit:单价x元/天",
            "remark:描述",
            "defaultFlag:默认标志0否1是",
            "batch 》batchName:批次名称"
    })
    @RequestMapping(value = "/queryBatchDetailCustom", method = {RequestMethod.POST})
    public ResultVO<?> queryBatchDetailCustom(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (params.getOfficialId() == null) {
            return ResultVO.error("公众号id不能为空");
        }
        return feeModelBatchService.queryBatchDetailCustom(params);
    }

    /**
     * 优化师充值模板-查询已关联优化师
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "优化师充值模板-查询已关联优化师", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "modelBatchId:1:int:modelBatchId",
    })
    @ApiParamsOut({
            "encryptionId:批次加密id",
            "userId:用户id",
            "userName:姓名",
            "roleNames:角色名称，逗号分割",
    })
    @RequestMapping(value = "/queryBatchUserList", method = {RequestMethod.POST})
    public ResultVO<?> queryBatchUserList(HttpServletRequest request, FastFeeModelBatchUserPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getModelBatchId() == null) {
            return ResultVO.error("modelBatchId不能为空");
        }
        return feeModelBatchUserService.queryPageList(sessionVO, params, pageVO);
    }

    @ApiName(value = "优化师充值模板-根据优化师查询充值规则模板列表", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "userId:1:int:优化师用户id"
    })
    @ApiParamsOut({
            "modelBatchId:充值模板id",
            "modelBatchName:充值模板名称"
    })
    @RequestMapping(value = "/queryPageListByUserId", method = {RequestMethod.POST})
    public ResultVO<?> queryPageListByUserId(HttpServletRequest request, FastFeeModelBatchUserPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setOfficialId(sessionVO.getOfficialId());
        return feeModelBatchUserService.queryPageListByUserId(sessionVO, params, pageVO);
    }

    @ApiName(value = "优化师充值模板-批量删除230926", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "encryptionIds:1:str:加密的id，逗号分割"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/deleteBatch", method = {RequestMethod.POST})
    public ResultVO<?> deleteBatch(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (isBlank(params.getEncryptionIds())) {
            return ResultVO.error("加密id不能为空");
        }
        String ids = decodeStrSeperate(params.getEncryptionIds());
        if (StrUtil.isEmpty(ids)) {
            return ResultVO.error("加密id不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        params.setIds(ids);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        MethodVO methodVO = feeModelBatchService.deleteBatch(request, params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "优化师充值模板-启用/禁用", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "encryptionId:1:str:加密的id",
            "state:1:int:启用状态，0禁用;1启用"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateState", method = {RequestMethod.POST})
    public ResultVO<?> updateState(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("加密id不能为空");
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error("加密id不能为空");
        }
        if (params.getState() == null) {
            return ResultVO.error("状态不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setId(id);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = feeModelBatchService.updateState(request, params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "通用充值模板-启用/禁用230926", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "encryptionId:1:str:加密的id",
            "state:1:int:启用状态，0禁用;1启用"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/updateCommonState", method = {RequestMethod.POST})
    public ResultVO<?> updateCommonState(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("加密id不能为空");
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error("加密id不能为空");
        }
        if (params.getState() == null) {
            return ResultVO.error("状态不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setId(id);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(0);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = feeModelBatchService.updateState(request, params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "优化师充值模板-关联优化师（批量）", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "encryptionId:1:str:加密的批次id",
            "userIds:1:str:用户id，逗号分割"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/relateUser", method = {RequestMethod.POST})
    public ResultVO<?> relateUser(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("加密id不能为空");
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error("加密id不能为空");
        }
        if (StrUtil.isEmpty(params.getUserIds())) {
            return ResultVO.error("关联用户不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setId(id);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setUpdatorId(sessionVO.getUserId());
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = feeModelBatchService.relateUser(request, params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "优化师充值模板-取消关联优化师", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "encryptionId:1:str:加密的批次id",
            "userIds:1:str:用户id，逗号分割"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/relateCancelUser", method = {RequestMethod.POST})
    public ResultVO<?> relateCancelUser(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error("加密id不能为空");
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error("加密id不能为空");
        }
        if (StrUtil.isEmpty(params.getUserIds())) {
            return ResultVO.error("关联用户不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setId(id);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setUpdatorId(sessionVO.getUserId());
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = feeModelBatchService.relateCancelUser(request, params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "充值模板-更新", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "encryptionId:1:str:加密的id",
            "type:1:int:1充值，2vip卡",
            "gearIds:1:str:档位id集合，逗号分割，按顺序给",
            "defaultGearId:1:int:默认档位id"
    })
    @ApiParamsOut({"state:ok"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastFeeModelPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        if (StrUtil.isEmpty(params.getGearIds())) {
            return ResultVO.error("选择的档位不能为空");
        }
        if (params.getType() == null) {
            return ResultVO.error("type不能为空");
        }
        if (params.getType() == 1 && params.getGearIds().split(",").length < 2) {
            return ResultVO.error("档位个数不能少于2个");
        }
        if (params.getType() == 1 && params.getGearIds().split(",").length > 8) {
            return ResultVO.error("档位个数不能大于8个");
        }
        if (params.getType() == 2 && params.getGearIds().split(",").length < 1) {
            return ResultVO.error("档位个数不能少于1个");
        }
        if (params.getType() == 2 && params.getGearIds().split(",").length > 3) {
            return ResultVO.error("档位个数不能大于3个");
        }
        if (params.getDefaultGearId() == null) {
            return ResultVO.error("默认档位不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeModelService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "自定义全局充值模板-根据应用id查询自定义模板", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({
            "miniId:1:int:应用id",
            "state:0:int:启用状态1启用0禁用",
            "contentType:0:int:内容类型:1、短剧 2、漫画 3、小说"
    })
    @ApiParamsOut({
            "list:充值模板列表",
            "list>>detailList:模板列表详情集合"
    })
    @RequestMapping(value = "/queryBatchDetailCustomByMiniId", method = {RequestMethod.POST})
    public ResultVO<?> queryBatchDetailCustomByMiniId(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (params.getMiniId() == null) {
            return ResultVO.error("应用id不能为空");
        }
        params.setContentType(null);
        return feeModelBatchService.queryBatchDetailCustomByMiniId(params);
    }
}
