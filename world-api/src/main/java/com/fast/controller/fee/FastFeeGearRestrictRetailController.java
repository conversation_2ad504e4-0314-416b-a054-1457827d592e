/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.fee.FastFeeGearRestrictRetailPO;
import com.fast.service.fee.FastFeeGearRestrictRetailService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeGearRestrictRetail")
public class FastFeeGearRestrictRetailController extends BaseController {

    @Autowired
    private FastFeeGearRestrictRetailService fastFeeGearRestrictRetailService;

    @ApiName(value = "fee-规则应用列表-查询列表", folder = {"fee"})
    @ApiParamsIn({
            "retailIds:1:str:分销商id们，逗号分割",
            "dramaIds:1:str:剧id们，逗号分割",
            "restrictIds:1:str:规则们，逗号分割"
    })
    @ApiParamsOut({
            "dramaName:剧名称",
            "retailName:分销商名称",
            "frontRestrictName:前规则名称",
            "afterRestrictName:后规则名称",
            "createTime:加入规则时间"
    })
    @PostMapping("/getFastFeeGearRestrictRetailList")
    public ResultVO<?> getList(HttpServletRequest request, FastFeeGearRestrictRetailPO params, PageVO pageVO) {
        if (StrUtil.isNotEmpty(params.getRestrictIds())) {
            params.setRestrictIds(StrUtil.resetInt(params.getRestrictIds()));
        }
        if (StrUtil.isNotEmpty(params.getRetailIds())) {
            params.setRetailIds(StrUtil.resetInt(params.getRetailIds()));
        }
        if (StrUtil.isNotEmpty(params.getDramaIds())) {
            params.setDramaIds(StrUtil.resetInt(params.getDramaIds()));
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        return fastFeeGearRestrictRetailService.queryPageList(params, pageVO);
    }

    @ApiName(value = "fee-查询单个详情", folder = {"fee"})
    @PostMapping("/getFastFeeGearRestrictRetailDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastFeeGearRestrictRetailPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFeeGearRestrictRetailPO fastFeeGearRestrictRetail = fastFeeGearRestrictRetailService.queryById(params);
        return ResultVO.success(fastFeeGearRestrictRetail);
    }

    @ApiName(value = "fee-添加", folder = {"fee"})
    @PostMapping("/insertFastFeeGearRestrictRetail")
    public ResultVO<?> insert(HttpServletRequest request, FastFeeGearRestrictRetailPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeGearRestrictRetailService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 批量添加分销商限制规则
     */
    @ApiName(value = "fee-限制规则v2-批量添加添加", folder = {"fee"})
    @ApiParamsIn({
            "params:1:str:body方式传参[{retailIds:'1,2,3',dramaId:88,frontDays:7,afterDays:7,frontRestrictId:99,afterRestrictId:100},{...}]",
            "参数说明:1:str:frontDays=上线前的天数",
            "参数说明:1:str:afterDays=上线后的天数",
            "参数说明:1:str:frontHours=上线前的小时数",
            "参数说明:1:str:afterHours=上线后的小时数",
            "参数说明:1:str:frontRestrictId=上线前限制规则id",
            "参数说明:1:str:afterRestrictId=上线后限制规则id"
    })
    @PostMapping("/insertFastFeeGearRestrictRetailBatch")
    public ResultVO<?> insertFastFeeGearRestrictRetailBatch(HttpServletRequest request, @RequestBody List<FastFeeGearRestrictRetailPO> paramsList) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastFeeGearRestrictRetailService.insertBatch(sessionVO, paramsList);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 批量设置分销商限制规则
     */
    @ApiName(value = "fee-限制规则v2-批量修改设置", folder = {"fee"})
    @ApiParamsIn({
            "params:1:str:body方式传参[{encryptionId:'xxxxxxxx',retailId:id,dramaId:88,frontDays:7,afterDays:7,frontRestrictId:99,afterRestrictId:100},{...}]",
            "参数说明:1:str:frontDays=上线前的天数",
            "参数说明:1:str:afterDays=上线后的天数",
            "参数说明:1:str:frontHours=上线前的小时数",
            "参数说明:1:str:afterHours=上线后的小时数",
            "参数说明:1:str:frontRestrictId=上线前限制规则id",
            "参数说明:1:str:afterRestrictId=上线后限制规则id"
    })
    @PostMapping("/updateBatch")
    public ResultVO<?> updateBatch(HttpServletRequest request, @RequestBody List<FastFeeGearRestrictRetailPO> paramsList) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastFeeGearRestrictRetailService.updateBatch(sessionVO, paramsList);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fee-更新", folder = {"fee"})
    @PostMapping("/updateFastFeeGearRestrictRetail")
    public ResultVO<?> update(HttpServletRequest request, FastFeeGearRestrictRetailPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeGearRestrictRetailService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
