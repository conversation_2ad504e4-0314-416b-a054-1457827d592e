/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.fee.FastFeeKeepBatchPO;
import com.fast.po.fee.FastFeeKeepBatchUserPO;
import com.fast.service.fee.FastFeeKeepBatchService;
import com.fast.service.fee.FastFeeKeepBatchUserService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeKeepBatch")
public class FastFeeKeepBatchController extends BaseController {

    @Autowired
    private FastFeeKeepBatchService fastFeeKeepBatchService;
    @Autowired
    private FastFeeKeepBatchUserService fastFeeKeepBatchUserService;

    @ApiName(value = "fee-挽留查询列表", folder = {"fee"})
    @ApiParamsIn({
            "feeFlag:0:int:1付费 2免费"
    })
    @ApiParamsOut({
            "batchName:批次名称",
            "state:禁用0否1是",
            "userName:创建人",
            "userCount:关联优化师个数"
    })
    @PostMapping("/getFastFeeKeepBatchList")
    public ResultVO<?> getList(HttpServletRequest request, FastFeeKeepBatchPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setGlobalFlag(0);
        if (params.getFeeFlag() == null) {
            params.setFeeFlag(1);// 默认付费
        }
        return fastFeeKeepBatchService.queryPageList(params, pageVO);
    }


    @ApiName(value = "fee-挽留查询列表", folder = {"fee"})
    @ApiParamsIn({
            "feeFlag:0:int:1付费 2免费",
            "miniId:0:int:小程序id"
    })
    @ApiParamsOut({
            "batchName:批次名称",
            "state:禁用0否1是",
            "userName:创建人",
            "userCount:关联优化师个数"
    })
    @PostMapping("/getFastFeeKeepBatchListByMiniId")
    public ResultVO<?> getFastFeeKeepBatchListByMiniId(HttpServletRequest request, FastFeeKeepBatchPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setGlobalFlag(0);
        if (params.getFeeFlag() == null) {
            params.setFeeFlag(1);// 默认付费
        }
        if (params.getState() == null) {
            params.setState(0);// 默认未禁用
        }
        // 默认付费
        if (params.getFeeFlag() == null) {
            params.setFeeFlag(1);// 默认未禁用
        }
        params.setDelFlag(0);
        return fastFeeKeepBatchService.getFastFeeKeepBatchListByMiniId(params, pageVO);
    }

    @ApiName(value = "fee-挽留查询关联优化师列表", folder = {"fee"})
    @ApiParamsIn({
            "encryptionId:0:str:加密id"
    })
    @ApiParamsOut({
            "id:关联id",
            "userName:优化师名称",
            "encryptionId:加密id"
    })
    @PostMapping("/getKeepUserList")
    public ResultVO<?> getKeepUserList(HttpServletRequest request, FastFeeKeepBatchPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        FastFeeKeepBatchUserPO batchUser = new FastFeeKeepBatchUserPO();
        batchUser.setBatchId(id);
        List<FastFeeKeepBatchUserPO> userList = fastFeeKeepBatchUserService.queryList(batchUser);
        Map<String, Object> results = ResultVO.getMap();
        results.put("userList", userList);
        return ResultVO.success(results);
    }

    @ApiName(value = "fee-挽留添加关联优化师", folder = {"fee"})
    @ApiParamsIn({
            "encryptionId:0:str:加密id",
            "userIds:1:str:优化师id们，逗号分割"
    })
    @ApiParamsOut({
            "state:ok"
    })
    @PostMapping("/addKeepUser")
    public ResultVO<?> addKeepUser(HttpServletRequest request, FastFeeKeepBatchPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);

        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeKeepBatchUserService.insertUser(params);
        return ResultVO.fromMethodVO(methodVO);

    }

    @ApiName(value = "fee-挽留-批量删除关联优化师", folder = {"fee"})
    @ApiParamsIn({
            "encryptionIds:0:str:加密id们，逗号分割"
    })
    @ApiParamsOut({
            "state:ok"
    })
    @PostMapping("/delKeepUsers")
    public ResultVO<?> delKeepUsers(HttpServletRequest request, FastFeeKeepBatchPO params) {
        if (StrUtil.isEmpty(params.getEncryptionIds())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }

        MethodVO methodVO = fastFeeKeepBatchUserService.delUser(params);
        return ResultVO.fromMethodVO(methodVO);

    }

    @ApiName(value = "fee-挽留查询单个详情", folder = {"fee"})
    @ApiParamsIn({
            "encryptionId:0:str:加密id"
    })
    @ApiParamsOut({
            "batchName:规则名称",
            "globalFlag:是否全局0否1是",
            "retailId:分销商id",
            "officialId:公众号id",
            "detailList:详情列表",
            "detailList==>memberType:用户充值类型0未充、1充值一次、2充值两次、3充值三次及以上",
            "detailList==>state:开启状态0关闭1开启",
            "detailList==>moneySub:额外减免金额",
            "detailList==>coinAdd:额外赠送币",
            "detailList==>recGearType:推荐档位类型1默认档位2停留档位3自定义",
            "detailList==>popNum:弹窗序号1，2",
            "detailList==>popTitle:弹窗标题",
            "detailList==>memberType:充值类型0未充值1一充2二充3三充及以上",
            "detailList==>benefitOne:福利1订阅0否1是",
            "detailList==>benefitTwo:福利2抖音关注0否1是",
            "detailList==>unlockSeriesNum:解锁集数",
            "detailList==>keepTimes:挽留次数"
    })
    @PostMapping("/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastFeeKeepBatchPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFeeKeepBatchPO fastFeeKeepBatch = fastFeeKeepBatchService.queryById(params);
        return ResultVO.success(fastFeeKeepBatch);
    }

    @ApiName(value = "fee-挽留全局规则查询", folder = {"fee"})
    @ApiParamsIn({
            "feeFlag:1:1付费 2免费"
    })
    @ApiParamsOut({
            "batchName:规则名称",
            "globalFlag:是否全局0否1是",
            "retailId:分销商id",
            "officialId:公众号id",
            "detailList:详情列表",
            "detailList==>memberType:用户充值类型0未充、1充值一次、2充值两次、3充值三次及以上",
            "detailList==>state:开启状态0关闭1开启",
            "detailList==>moneySub:额外减免金额",
            "detailList==>coinAdd:额外赠送币",
            "detailList==>recGearType:推荐档位类型1默认档位2停留档位3自定义",
            "detailList==>popNum:弹窗序号1，2",
            "detailList==>popTitle:弹窗标题",
            "detailList==>memberType:充值类型0未充值1一充2二充3三充及以上",
            "detailList==>benefitOne:福利1订阅0否1是",
            "detailList==>benefitTwo:福利2抖音关注0否1是",
            "detailList==>unlockSeriesNum:解锁集数",
            "detailList==>keepTimes:挽留次数"
    })
    @PostMapping("/getKeepGlobal")
    public ResultVO<?> getKeepGlobal(HttpServletRequest request, FastFeeKeepBatchPO params) {
        if (params.getFeeFlag() == null) {
            params.setFeeFlag(1); // 付费
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        if (params.getOfficialId() == null) {
            params.setOfficialId(sessionVO.getOfficialId());
        }
        FastFeeKeepBatchPO fastFeeKeepBatch = fastFeeKeepBatchService.getKeepGlobal(params);
        Map<String, Object> results = ResultVO.getMap();
        results.put("keep", fastFeeKeepBatch);
        return ResultVO.success(results);
    }

    @ApiName(value = "fee-挽留添加", folder = {"fee"})
    @ApiParamsIn({
            "batchName:1:str:批次名称",
            "globalFlag:1:int:全局0否1是",
            "feeFlag:1:int:1付费 2免费",
            "detailListStr:1:str:详情列表JSON字符串[{moneySub:2,coinAdd:200,gearId:123,popNum:1,memberType:0,benefitOne:0,benefitTwo:0},...]",
            "detailListStr==>state:1:开启状态0关闭1开启",
            "detailListStr==>title:1:卡名称",
            "detailListStr==>unlockSeriesNum:1:解锁集数",
            "detailListStr==>keepTimes:1:挽留次数"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insertFastFeeKeepBatch")
    public ResultVO<?> insert(HttpServletRequest request, FastFeeKeepBatchPO params) {
        // 判断参数是否全
        if (params.getFeeFlag() == null) {
            return ResultVO.error("付费类型不能为空");
        }
        if (params.getGlobalFlag() == null) {
            return ResultVO.error("是否全局未设置：global，0否1是");
        }
        if (params.getGlobalFlag() == 0 && StrUtil.isEmpty(params.getBatchName())) {
            return ResultVO.error("【优化师规则】规则名称不能为空");
        }
        if (StrUtil.isEmpty(params.getDetailListStr())) {
            return ResultVO.error("规则详情不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeKeepBatchService.insert(params, sessionVO);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fee-更新", folder = {"fee"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "batchName:1:str:批次名称",
            "globalFlag:1:int:全局0否1是",
            "detailListStr:1:str:详情列表JSON字符串[{moneySub:2,coinAdd:200,gearId:123,popNum:1,memberType:0,benefitOne:0,benefitTwo:0},...]",
            "detailListStr==>state:1:开启状态0关闭1开启",
            "detailListStr==>title:1:卡名称",
            "detailListStr==>unlockSeriesNum:1:解锁集数",
            "detailListStr==>keepTimes:1:挽留次数"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateFastFeeKeepBatch")
    public ResultVO<?> update(HttpServletRequest request, FastFeeKeepBatchPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeKeepBatchService.updateAll(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fee-挽留规则开启/禁用", folder = {"fee"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "state:1:int:禁用状态0否1是"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateKeepState")
    public ResultVO<?> updateKeepState(HttpServletRequest request, FastFeeKeepBatchPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        if (params.getState() == null) {
            return ResultVO.error("开启状态不能为空");
        }
        SessionVO sessionVO = getSessionVO(request);
        FastFeeKeepBatchPO keep = new FastFeeKeepBatchPO();
        keep.setId(id);
        keep.setState(params.getState());
        keep.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeKeepBatchService.update(keep);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fee-挽留批量删除规则", folder = {"fee"})
    @ApiParamsIn({
            "encryptionIds:0:str:加密id们，逗号分割"
    })
    @ApiParamsOut({
            "state:ok"
    })
    @PostMapping("/delKeep")
    public ResultVO<?> delKeep(HttpServletRequest request, FastFeeKeepBatchPO params) {
        if (StrUtil.isEmpty(params.getEncryptionIds())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        MethodVO methodVO = fastFeeKeepBatchService.delUser(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
