/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.fee.FastFeeKeepDetailPO;
import com.fast.service.fee.FastFeeKeepDetailService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeKeepDetail")
public class FastFeeKeepDetailController extends BaseController {

    @Autowired
    private FastFeeKeepDetailService fastFeeKeepDetailService;

    @ApiName(value = "fee-查询列表", folder = {"fee"})
    @PostMapping("/getFastFeeKeepDetailList")
    public ResultVO<?> getList(HttpServletRequest request, FastFeeKeepDetailPO params, PageVO pageVO) {
        return fastFeeKeepDetailService.queryPageList(params, pageVO);
    }

    @ApiName(value = "fee-查询单个详情", folder = {"fee"})
    @PostMapping("/getFastFeeKeepDetailDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastFeeKeepDetailPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFeeKeepDetailPO fastFeeKeepDetail = fastFeeKeepDetailService.queryById(params);
        return ResultVO.success(fastFeeKeepDetail);
    }

    @ApiName(value = "fee-添加", folder = {"fee"})
    @PostMapping("/insertFastFeeKeepDetail")
    public ResultVO<?> insert(HttpServletRequest request, FastFeeKeepDetailPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeKeepDetailService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fee-更新", folder = {"fee"})
    @PostMapping("/updateFastFeeKeepDetail")
    public ResultVO<?> update(HttpServletRequest request, FastFeeKeepDetailPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeKeepDetailService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
