/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.fee.FastFeeRulePO;
import com.fast.service.fee.FastFeeRuleService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 短剧付费规则
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeRule")
public class FastFeeRuleController extends BaseController {

    @Autowired
    private FastFeeRuleService fastFeeRuleService;

    @ApiName(value = "付费规则-查询列表", folder = {"费用相关"})
    @RequestMapping(value = "/getList")
    public ResultVO<?> getList(HttpServletRequest request, FastFeeRulePO params, PageVO pageVO) {
        return fastFeeRuleService.queryPageList(params, pageVO);
    }

    /**
     * 查询短剧付费起始集
     *
     * @param request
     * @param params
     * @return
     */
    @RequestMapping(value = "/getDramaStartNum")
    @ApiName(value = "查询短剧付费起始集", folder = {"费用相关"})
    @ApiParamsIn({"encryptionId:1:str:短剧加密id"})
    @ApiParamsOut({
            "数组:起始集",
    })
    public ResultVO<?> getDramaStartNum(HttpServletRequest request, FastFeeRulePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer dramaId = decodeInt(params.getEncryptionId());
        if (dramaId == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setDramaId(dramaId);
        SessionVO sessionVO = getSessionVO(request);
        if (sessionVO.getRetailId() > 0) {
            params.setRetailId(sessionVO.getRetailId());
        }
        return fastFeeRuleService.getDramaStartNum(params);
    }

    @ApiName(value = "付费规则-查询单个详情", folder = {"费用相关"})
    @RequestMapping(value = "/getDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastFeeRulePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFeeRulePO fastFeeRule = fastFeeRuleService.queryById(params);
        return ResultVO.success(fastFeeRule);
    }

    @ApiName(value = "付费规则-添加", folder = {"费用相关"})
    @RequestMapping(value = "/insert")
    public ResultVO<?> insert(HttpServletRequest request, FastFeeRulePO params) {
        SessionVO sessionUser = getSessionVO(request);
        params.setCreatorId(sessionUser.getUserId());
        MethodVO methodVO = fastFeeRuleService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
