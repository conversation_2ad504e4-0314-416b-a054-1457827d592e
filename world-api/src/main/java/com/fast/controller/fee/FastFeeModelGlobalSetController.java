/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticFolder;
import com.fast.po.fee.FastFeeModelGlobalSetPO;
import com.fast.service.fee.FastFeeModelGlobalSetService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeModelGlobalSet")
public class FastFeeModelGlobalSetController extends BaseController {

    @Autowired
    private FastFeeModelGlobalSetService globalSetService;

    @ApiName(value = "充值模板全局设置-查询单个详情", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({"no:0:int:无需参数"})
    @ApiParamsOut({"retailId:分销商id", "officialId:公众号id", "type:类型1平台2;自定义"})
    @RequestMapping(value = "/getGlobalSet", method = {RequestMethod.POST})
    public ResultVO<?> getGlobalSet(HttpServletRequest request, FastFeeModelGlobalSetPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setUpdatorId(sessionVO.getUserId());
        FastFeeModelGlobalSetPO fastFeeModelGlobalSet = globalSetService.queryGlobalSet(params);
        return ResultVO.success(fastFeeModelGlobalSet);
    }

    @ApiName(value = "充值模板全局设置-更新", folder = {StaticFolder.FOLDER_FEE})
    @ApiParamsIn({"type:1:int:类型1平台;2自定义"})
    @ApiParamsOut({})
    @RequestMapping(value = "/updateType", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, Integer type) {
        if (type == null || !StrUtil.equalsAny(type, 1, 2)) {
            return ResultVO.error("type不合法");
        }
        FastFeeModelGlobalSetPO params = new FastFeeModelGlobalSetPO();
        SessionVO sessionVO = getSessionVO(request);
        params.setType(type);
        params.setRetailId(sessionVO.getRetailId());
        params.setOfficialId(sessionVO.getOfficialId());
        params.setUpdatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = globalSetService.updateByRetailId(request, sessionVO, params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
