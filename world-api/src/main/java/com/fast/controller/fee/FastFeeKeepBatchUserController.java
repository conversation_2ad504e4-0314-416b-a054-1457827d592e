/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.fee.FastFeeKeepBatchUserPO;
import com.fast.service.fee.FastFeeKeepBatchUserService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeKeepBatchUser")
public class FastFeeKeepBatchUserController extends BaseController {

    @Autowired
    private FastFeeKeepBatchUserService fastFeeKeepBatchUserService;

    @ApiName(value = "fee-查询列表", folder = {"fee"})
    @PostMapping("/getFastFeeKeepBatchUserList")
    public ResultVO<?> getList(HttpServletRequest request, FastFeeKeepBatchUserPO params, PageVO pageVO) {
        return fastFeeKeepBatchUserService.queryPageList(params, pageVO);
    }

    @ApiName(value = "fee-查询单个详情", folder = {"fee"})
    @PostMapping("/getFastFeeKeepBatchUserDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastFeeKeepBatchUserPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFeeKeepBatchUserPO fastFeeKeepBatchUser = fastFeeKeepBatchUserService.queryById(params);
        return ResultVO.success(fastFeeKeepBatchUser);
    }

    @ApiName(value = "fee-添加", folder = {"fee"})
    @PostMapping("/insertFastFeeKeepBatchUser")
    public ResultVO<?> insert(HttpServletRequest request, FastFeeKeepBatchUserPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeKeepBatchUserService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fee-更新", folder = {"fee"})
    @PostMapping("/updateFastFeeKeepBatchUser")
    public ResultVO<?> update(HttpServletRequest request, FastFeeKeepBatchUserPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastFeeKeepBatchUserService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
