/*
 * Powered By fast.up
 */
package com.fast.controller.fee;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.fee.FastFeeModelBatchPO;
import com.fast.service.fee.FastFeeModelBatchService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFeeModelBatch")
public class FastFeeModelBatchController extends BaseController {

    @Autowired
    private FastFeeModelBatchService fastFeeModelBatchService;

    @ApiName(value = "充值模板批次-查询列表", folder = {"充值模板"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastFeeModelBatchPO params, PageVO pageVO) {
        return fastFeeModelBatchService.queryPageList(params, pageVO);
    }

    @ApiName(value = "充值模板批次-查询单个详情", folder = {"充值模板"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFeeModelBatchPO fastFeeModelBatch = fastFeeModelBatchService.queryById(params);
        return ResultVO.success(fastFeeModelBatch);
    }

    @ApiName(value = "充值模板批次-添加", folder = {"充值模板"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastFeeModelBatchPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeModelBatchService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "充值模板批次-更新", folder = {"充值模板"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFeeModelBatchService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
