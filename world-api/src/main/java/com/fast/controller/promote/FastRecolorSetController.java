/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.promote.FastRecolorSetPO;
import com.fast.service.promote.FastRecolorSetService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastRecolorSet")
public class FastRecolorSetController extends BaseController {

    @Autowired
    private FastRecolorSetService fastRecolorSetService;

    @ApiName(value = "染色配置-查询列表", folder = {"promote"})
    @ApiParamsIn({"type:1:int:1全局2链路3小程序"})
    @ApiParamsOut({
            "appType:1微信;2抖音;3H5;4快手;5快应用",
            "miniName:小程序名称"
    })
    @PostMapping("/getFastRecolorSetList")
    public ResultVO<?> getList(HttpServletRequest request, FastRecolorSetPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getType() == null) {
            params.setType(1);
        } else if (params.getType() == 0) {
            params.setType(null);
        }
        if (params.getVersion() == null && (params.getType() != null && params.getType() == 3)) {
            // 兼容老代码
            params.setType(null);
        }
        params.setContentType(sessionVO.getContentType());
//        params.setCreatorId(sessionVO.getUserId());
        return fastRecolorSetService.queryPageList(params, pageVO);
    }

    @ApiName(value = "promote-查询单个详情", folder = {"promote"})
    @PostMapping("/getFastRecolorSetDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastRecolorSetPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastRecolorSetPO fastRecolorSet = fastRecolorSetService.queryById(params);
        return ResultVO.success(fastRecolorSet);
    }

    @ApiName(value = "染色配置-添加", folder = {"promote"})
    @ApiParamsIn({
            "type:1:int:1全局2链路3小程序",
            "roadId:1:int:type=2时必填，链路id",
            "recolorHours:1:int:染色时间小时",
            "appIds:1:str:type=3时必填，小程序appid,逗号分割"
    })
    @PostMapping("/insertFastRecolorSet")
    public ResultVO<?> insert(HttpServletRequest request, FastRecolorSetPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        if (params.getType() == null) {
            params.setType(2);// 新增链路配置，默认非全局
        }
        if (params.getType() == 2 && params.getRoadId() == null) {
            return ResultVO.error("请先选择投放链路");
        }
        if (params.getType() == 3 && StrUtil.isEmpty(params.getAppIds())) {
            return ResultVO.error("小程序appId不能为空");
        }
        params.setActive(1);// 默认激活
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastRecolorSetService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "染色配置-更新", folder = {"promote"})
    @ApiParamsIn({
            "encriptionId:1:str:加密的id",
            "recolorHours:1:int:染色时间小时",
            "active:1:int:激活状态0关闭1开启"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateFastRecolorSet")
    public ResultVO<?> update(HttpServletRequest request, FastRecolorSetPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getType() != null) {
            params.setType(null);// 不能修改type
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastRecolorSetService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
