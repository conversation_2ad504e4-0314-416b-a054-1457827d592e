/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.service.promote.FastStatisDataService;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastStatisData")
public class FastStatisDataController extends BaseController {

    @Autowired
    private FastStatisDataService fastStatisDataService;

    @ApiName(value = "数据统计-充值汇总数据", folder = {"数据统计"})
    @ApiParamsIn({
            "dramaId:1:int:剧id",
    })
    @ApiParamsOut({
            "result:结果",
    })
    @Slave
    @RequestMapping(value = "/nologin/statisData/getRechargeAll", method = {RequestMethod.GET})
    public ResultVO<?> getRechargeAll(HttpServletRequest request, FastMemberOrderRechargePO params) {
        return fastStatisDataService.getRechargeAllSum(params);
    }

    @ApiName(value = "数据统计-剧集留存汇总数据", folder = {"数据统计"})
    @ApiParamsIn({
            "dramaId:1:int:剧id",
    })
    @ApiParamsOut({
            "result:结果",
    })
    @Slave
    @RequestMapping(value = "/nologin/statisData/getDramaSeriesAll", method = {RequestMethod.GET})
    public ResultVO<?> getDramaSeriesAll(HttpServletRequest request, FastMemberRecentLogPO params) {
        return fastStatisDataService.getDramaSeriesAllSum(params);
    }
}