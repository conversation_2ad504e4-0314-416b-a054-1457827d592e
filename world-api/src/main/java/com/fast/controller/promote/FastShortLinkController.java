/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.promote.FastShortLinkPO;
import com.fast.service.promote.FastShortLinkService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/nologin")
public class FastShortLinkController extends BaseController {

    @Autowired
    private FastShortLinkService fastShortLinkService;

    @ApiName(value = "短剧推广-短连接-实现短链跳转", folder = {"分销商/短剧推广/短链接"})
    @RequestMapping(value = "/s/{shortLink}", method = {RequestMethod.GET})
    public void shortLink(HttpServletRequest request, HttpServletResponse response, @PathVariable String shortLink) {
        fastShortLinkService.shortLink(request, response, shortLink);
    }

    @ApiName(value = "短剧推广-短连接-添加", folder = {"分销商/短剧推广/短链接"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastShortLinkPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastShortLinkService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiName(value = "短剧推广-短连接-查询列表", folder = {"分销商/短剧推广/短链接"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastShortLinkPO params, PageVO pageVO) {
        return fastShortLinkService.queryPageList(params, pageVO);
    }

    @ApiName(value = "短剧推广-短连接-查询单个详情", folder = {"分销商/短剧推广/短链接"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastShortLinkPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastShortLinkPO fastShortLink = fastShortLinkService.queryById(params);
        return ResultVO.success(fastShortLink);
    }

    @ApiName(value = "短剧推广-短连接-更新", folder = {"分销商/短剧推广/短链接"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastShortLinkPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastShortLinkService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
