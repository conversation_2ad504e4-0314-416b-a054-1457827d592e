/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.promote.FastLinkRoadPO;
import com.fast.service.promote.FastLinkRoadService;
import com.fast.utils.DictUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastLinkRoad")
public class FastLinkRoadController extends BaseController {

    @Autowired
    private FastLinkRoadService fastLinkRoadService;

    @ApiName(value = "promote-查询列表", folder = {"promote"})
    @PostMapping("/getFastLinkRoadList")
    public ResultVO<?> getList(HttpServletRequest request, FastLinkRoadPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getContentType() == null) {
            params.setContentType(sessionVO.getContentType());
        }
        return fastLinkRoadService.queryPageList(params, pageVO);
    }

    @ApiName(value = "promote-查询单个详情", folder = {"promote"})
    @PostMapping("/getFastLinkRoadDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastLinkRoadPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastLinkRoadPO fastLinkRoad = fastLinkRoadService.queryById(params);
        return ResultVO.success(fastLinkRoad);
    }

    @ApiName(value = "promote-添加", folder = {"promote"})
    @ApiParamsIn({
            "mediaType:1:int:投放媒体1头条2adp3mp",
            "advMode:1:int:投放方式1直投2加粉",
            "fansType:1:int:加粉载体1公众号2企微",
            "appType:1:int:跳转应用1微信;2抖音;3H5;5快应用",
            "feeFlag:1:int:付费标志1=付费;2=免费"
    })
    @PostMapping("/insertFastLinkRoad")
    public ResultVO<?> insert(HttpServletRequest request, FastLinkRoadPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setContentType(sessionVO.getContentType());
        if (params.getMediaType() == null) {
            return ResultVO.error("投放媒体不能为空");
        }
        if (params.getAdvMode() == null) {
            return ResultVO.error("投放方式不能为空");
        }
        if (params.getAppType() == null) {
            return ResultVO.error("跳转应用不能为空");
        }
        if (params.getAdvMode() == 2 && params.getFansType() == null) {
            return ResultVO.error("加粉载体不能为空");
        }
        StringBuffer nameBuffer = new StringBuffer();
        nameBuffer.append(DictUtil.getMediaType(params.getMediaType()));
        nameBuffer.append("-");
        nameBuffer.append(DictUtil.getAdvMode(params.getAdvMode()));
        if (params.getAdvMode() == 2) {
            nameBuffer.append("-");
            nameBuffer.append(DictUtil.getFansType(params.getFansType()));
        }
        nameBuffer.append("-");
        nameBuffer.append(DictUtil.getAppType(params.getAppType()));
        nameBuffer.append("-");
        nameBuffer.append(DictUtil.getFeeFlag(params.getFeeFlag()));
        params.setRoadName(nameBuffer.toString());
        MethodVO methodVO = fastLinkRoadService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

}
