/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.promote.FastActivityPO;
import com.fast.service.promote.FastActivityExportService;
import com.fast.service.promote.FastActivityService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.promote.FastActivityVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 促销活动
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastPromote")
public class FastActivityController extends BaseController {

    @Autowired
    private FastActivityService activityService;
    @Autowired
    private FastActivityExportService exportService;

    /**
     * 促销活动-查询列表
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "促销活动-查询列表", folder = {"促销活动"})
    @ApiParamsIn(value = {
            "rechargeType:充值档位::1=充值金币;2=充值VIP",
            "activityState:活动状态::1=未开始;2=进行中;3=已结束",
            "titleLike:标题",
    })
    @ApiParamsOut(value = {
            "encryptionId:活动加密id",
            "encryptionModelDetailId:模板detail加密id",
            "title:标题",
            "content:内容",
            "startTime:开始时间(yyyy-MM-dd HH:mm:ss)",
            "endTime:结束时间(yyyy-MM-dd HH:mm:ss)",
            "rechargeType:充值档位::1=充值金币;2=充值VIP",
            "activityState:活动状态::1=未开始;2=进行中;3=已结束",
            "moneyRecharge:充值金额",
            "modelGearId:充值档位id",
            "limitType:限购类型::1=按活动;2=按天",
            "limitNum:限购次数(0=不限制)",
            "templateId:模板id-(1,2,3)",
            "state:是否开启::1=是;0=否",
    })
    @RequestMapping(value = "/getPromoteActivityList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastActivityPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setOfficialId(sessionVO.getOfficialId());
        return activityService.queryPageList(params, pageVO);
    }

    /**
     * 促销活动-查询活动数据-按天
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "促销活动-查询活动数据-按天", folder = {"促销活动"})
    @ApiParamsIn(value = {"encryptionId:活动加密id", "activityTimeStr:0:str:活动时间：yyyy-MM-dd - yyyy-MM-dd",})
    @ApiParamsOut(value = {
            "activityDate:活动日期yyyy-MM-dd",
            "clickMemberNum:累计点击人数",
            "rechargeMemberNum:累计充值人数",
            "rechargeNum:累计充值次数",
            "rechargeMoney:累计充值金额",
    })
    @RequestMapping(value = "/getPromoteActivityData", method = {RequestMethod.POST})
    public ResultVO<?> getActivityData(HttpServletRequest request, FastActivityVO params, PageVO pageVO) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer activityId = decodeInt(params.getEncryptionId());
        if (activityId == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setActivityId(activityId);
        // 处理时间
        if (notBlank(params.getActivityTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getActivityTimeStr());
            params.setActivityTimeS(DateUtil.format06Int(date.get(0)));
            params.setActivityTimeE(DateUtil.format06Int(date.get(1)));
        }
        SessionVO sessionVO = getSessionVO(request);
        if (params.getExportData() == 1) {
            return exportService.exportActivityDataList(sessionVO, params);
        } else {
            return activityService.getActivityData(params, pageVO);
        }
    }

    /**
     * 促销活动-查询活动数据-全部
     *
     * @param request
     * @param params
     * @param pageVO
     * @return
     */
    @ApiName(value = "促销活动-查询活动数据-全部", folder = {"促销活动"})
    @ApiParamsIn(value = {"encryptionId:活动加密id",})
    @ApiParamsOut(value = {
            "clickMemberNum:累计点击人数",
            "rechargeMemberNum:累计充值人数",
            "rechargeNum:累计充值次数",
            "rechargeMoney:累计充值金额",
    })
    @RequestMapping(value = "/getPromoteActivityDataAll", method = {RequestMethod.POST})
    public ResultVO<?> getActivityDataAll(HttpServletRequest request, FastActivityVO params, PageVO pageVO) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer activityId = decodeInt(params.getEncryptionId());
        if (activityId == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setActivityId(activityId);
        SessionVO sessionVO = getSessionVO(request);
        if (params.getExportData() == 1) {
            return exportService.exportActivityDataAll(sessionVO, params);
        } else {
            return activityService.getActivityDataAll(params, pageVO);
        }
    }

    /**
     * 促销活动-查询单个详情
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "促销活动-查询单个详情", folder = {"促销活动"})
    @ApiParamsIn(value = {
            "encryptionId:1:str:活动加密id(优先)",
            "id:1:int:活动id",
    })
    @ApiParamsOut(value = {
            "encryptionId:活动加密id",
            "encryptionModelDetailId:模板detail加密id",
            "title:标题",
            "content:内容",
            "startTime:开始时间(yyyy-MM-dd HH:mm:ss)",
            "endTime:结束时间(yyyy-MM-dd HH:mm:ss)",
            "rechargeType:充值档位::1=充值金币;2=充值VIP",
            "activityState:活动状态::1=未开始;2=进行中;3=已结束",
            "moneyRecharge:充值金额",
            "modelGearId:充值档位id",
            "limitType:限购类型::1=按活动;2=按天",
            "limitNum:限购次数(0=不限制)",
            "templateId:模板id-(1,2,3)",
            "state:是否开启::1=是;0=否",
    })
    @RequestMapping(value = "/getPromoteActivityDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastActivityPO params) {
        Integer id = null;
        if (notBlank(params.getEncryptionId())) {
            id = decodeInt(params.getEncryptionId());
        }
        if (id == null) {
            id = params.getId();
        }
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastActivityPO fastPromoteActivity = activityService.queryInfoByRedis(params);
        return ResultVO.success(fastPromoteActivity);
    }

    /**
     * 促销活动-添加
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "促销活动-添加", folder = {"促销活动"})
    @ApiParamsIn(value = {
            "title:1:str:标题",
            "content:1:str:内容",
            "startTimeStr:1:str:开始时间(yyyy-MM-dd HH:mm:ss)",
            "endTimeStr:1:str:结束时间(yyyy-MM-dd HH:mm:ss)",
            "rechargeType:1:int:充值档位::1=充值金币;2=充值VIP",
            "modelGearId:1:int:充值档位id",
            "limitType:1:int:限购类型::1=按活动;2=按天",
            "limitNum:1:int:限购次数(0=不限制)",
            "templateId:1:int:模板id-(1,2,3)",
            "state:1:int:是否开启::1=是;0=否",
    })
    @ApiParamsOut(value = {"success"})
    @RequestMapping(value = "/insertPromoteActivity", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, @Validated FastActivityPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setOfficialId(sessionVO.getOfficialId());
        params.setCreatorId(sessionVO.getUserId());
        params.setStartTime(DateUtil.format07(params.getStartTimeStr()));
        params.setEndTime(DateUtil.format07(params.getEndTimeStr()));
        MethodVO methodVO = activityService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 促销活动-更新
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "促销活动-更新", folder = {"促销活动"})
    @ApiParamsIn(value = {
            "encryptionId:1:str:活动加密id",
            "title:1:str:标题",
            "content:1:str:内容",
            "startTimeStr:1:str:开始时间(yyyy-MM-dd HH:mm:ss)",
            "endTimeStr:1:str:结束时间(yyyy-MM-dd HH:mm:ss)",
            "rechargeType:1:str:充值档位::1=充值金币;2=充值VIP",
            "modelGearId:1:int:充值档位id",
            "limitType:1:int:限购类型::1=按活动;2=按天",
            "limitNum:1:int:限购次数(0=不限制)",
            "templateId:1:int:模板id-(1,2,3)",
            "state:1:int:是否开启::1=是;0=否",
    })
    @ApiParamsOut(value = {"success"})
    @RequestMapping(value = "/updatePromoteActivity", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, @Validated FastActivityPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer activityId = decodeInt(params.getEncryptionId());
        if (activityId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(activityId);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = activityService.update(params);
        // 清除缓存数据
        RedisUtil.del(StaticVar.ACTIVITY_INFO_ID + activityId);
        return ResultVO.fromMethodVO(methodVO);
    }

    /**
     * 促销活动-启用/禁用
     *
     * @param request
     * @param params
     * @return
     */
    @ApiName(value = "促销活动-启用/禁用", folder = {"促销活动"})
    @ApiParamsIn(value = {
            "encryptionId:1:str:活动加密id",
            "state:1:int:是否开启::1=是;0=否",
    })
    @ApiParamsOut(value = {"success"})
    @RequestMapping(value = "/updatePromoteActivityState", method = {RequestMethod.POST})
    public ResultVO<?> updateState(HttpServletRequest request, FastActivityPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer activityId = decodeInt(params.getEncryptionId());
        if (activityId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(activityId);
        if (params.getState() == null || !StrUtil.equalsAny(params.getState(), 0, 1)) {
            return ResultVO.error("启用/禁用状态不合法");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = activityService.updateState(params);
        // 清除缓存数据
        RedisUtil.del(StaticVar.ACTIVITY_INFO_ID + activityId);
        return ResultVO.fromMethodVO(methodVO);
    }
}
