/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.service.promote.FastStatisLinkFreeDayService;
import com.fast.service.promote.FastStatisLinkFreeHourService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastStatisLinkFreeDay")
public class FastStatisLinkFreeDayController extends BaseController {

    @Autowired
    private FastStatisLinkFreeDayService fastStatisLinkFreeDayService;
    @Autowired
    private FastStatisLinkFreeHourService fastStatisLinkFreeHourService;

    @ApiName(value = "设置数据freeDay", folder = {"promote"})
    @PostMapping("/nologin/setData")
    public ResultVO<?> setData(String d) {
        fastStatisLinkFreeDayService.setFreeDayData(DateUtil.format09(d));
        return ResultVO.success();
    }

    @ApiName(value = "设置数据freeHour", folder = {"promote"})
    @PostMapping("/nologin/setDataHour")
    public ResultVO<?> setDataHour(String dateStr) {
        if (StrUtil.isEmpty(dateStr)) {
            dateStr = DateUtil.format09(DateUtil.getNowDate());
        }
        log.info(dateStr);
        fastStatisLinkFreeHourService.setFreeHourData(dateStr);
        return ResultVO.success();
    }
}
