/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.user.FastLinkCreateRulePO;
import com.fast.service.promote.FastLinkExportService;
import com.fast.service.promote.FastLinkImportAdvertiserService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.user.FastLinkCreateRuleService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.promote.FastLinkQueryVO;
import org.apache.commons.validator.routines.DomainValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 链接管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastLink")
public class FastLinkController extends BaseController {

    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastLinkExportService fastLinkExportService;
    @Autowired
    private FastLinkImportAdvertiserService fastLinkImportAdvertiserService;
    @Autowired
    private FastLinkCreateRuleService fastLinkCreateRuleService;
    @Autowired
    private FastDramaMapper fastDramaMapper;

    @ApiName(value = "短剧推广-推广链接-查询列表", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "queryCreateTime:0:str:创建时间段-例如2022-08-01 - 2022-08-19",
            "queryName:0:str:查询字符串-匹配名称和id",
            "wxFinderNameId:0:str:查询字符串-视频号名称和id",
            "dramaName:0:str:剧名称",
            "queryType:0:int:排序方式-1倒序 2正序",
            "size:0:int:每页条数",
            "page:0:int:页码",
            "adGroundId:0:int:广告落地页id",
            "linkPlatform:0:int:1公众号2企微",
            "linkType:1:int:1=投放链接;2=运营链接;3=挂载链接",
            "linkSubType:0:int:1=星图推广;2=小程序推广;3=作品挂载;4=聚星推广;5=微信视频号"
    })
    @ApiParamsOut({
            "linkName:链接名称(派单渠道)",
            "wxFinderName:微信视频号名称",
            "wxFinderId:微信视频号ID",
            "inPage:入口页面",
            "seriesNum:第几集",
            "setInfo:相关设置",
            "shortLink:短链",
            "dramaName:剧名",
            "seriesName:集名",
            "createTime: 创建时间",
            "numAll:用户数据-新增用户数",
            "numDay:用户数据-今日新增(用户数)",
            "moneyAll:总充值数据-充值金额",
            "numAllRech:总充值数据-充值人数",
            "numAllOrder:总充值数据-充值笔数",
            "moneyAllDay:今日充值数据-充值总金额",
            "numAllDayRech:今日充值数据-充值总人数",
            "numAllDayOrder:今日充值数据-充值总笔数",
            "costAll:总成本(投放成本-累计)",
            "profitAll:利润(累计利润)",
            "profitAllRate:回报率",
            "advUserName:优化师名称",
            "pv:pv此链接的点击次数",
            "uv:uv此链接的点击人数",
            "realName:归属人",
    })
    @Slave
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastLinkQueryVO params, PageVO pageVO) {
        if (params.getLinkType() == null) {
            params.setLinkType(1);
        }
        if (biggerZero(params.getLinkSubType())) {
            params.setLinkType(3);
        }
        // 设置取回投手自己创建的链接
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());

        if (params.getOfficialId() == null) {
            params.setOfficialId(sessionVO.getOfficialId());
        }
        if (biggerZero(sessionVO.getRetailId()) && !"2".equals(sessionVO.getRoleIds())) {
            params.setAdvUserId(sessionVO.getUserId());
        }
        if (biggerZero(sessionVO.getRetailId())) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastLinkService.exportLinkList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        } else {
            return fastLinkService.queryPageList(params, pageVO);
        }
    }

    @ApiName(value = "短剧推广-推广链接-查询列表-用于下拉框", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "advUserId:0:int:优化师(投手)id",
            "advUserIds:0:int:优化师(投手)id-多个",
            "dramaId:0:int:短剧id",
            "linkNameLike:0:int:链接搜索关键词",
    })
    @ApiParamsOut({
            "id:链接id",
            "linkName:链接名称",
    })
    @Slave
    @RequestMapping(value = "/getSimpleList", method = {RequestMethod.POST})
    public ResultVO<?> getSimpleList(HttpServletRequest request, FastLinkPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setLimitExport(100);
        return ResultVO.success(fastLinkService.getSimpleList(params));
    }

    @ApiName(value = "数据分析-渠道监控", folder = {"数据统计"})
    @ApiParamsIn({
            "queryCreateTime:0:str:创建时间段-例如2022-08-01 - 2022-08-19",
            "queryStatisTime:0:str:统计时间段-例如2022-08-01 - 2022-08-19",
            "miniIds:0:str:应用id们",
            "dramaIds:0:str:剧id们",
            "linkSubTypes:0:str:挂载类型",
            "queryName:0:str:链接名称或id",
            "inPage:0:str:入口页面",
            "retailName:0:str:分销商名称",
            "officialName:0:str:公众号名称",
            "advUserName:0:str:优化师名称",
            "queryType:0:int:废弃，用下面参数--排序方式-1倒序 2正序  新增用户数排序 --用户没选择时默认1 ",
            "orderNumAll:0:int:排序方式-1倒序 2正序  新增用户数排序 --用户没选择时默认1，注-4个排序参数字段只能设置1个，其他需为空",
            "orderMoneyAll:0:int:排序方式-1倒序 2正序  总充值排序",
            "orderMoneyAllDay:0:int:排序方式-1倒序 2正序  今日充值排序",
            "orderProfitAll:0:int:排序方式-1倒序 2正序  总利润排序",
            "size:0:int:每页条数",
            "page:0:int:页码"
    })
    @ApiParamsOut({
            "linkName:链接名称(派单渠道)",
            "inPage:入口页面",
            "seriesNum:第几集",
            "setInfo:相关设置",
            "shortLink:短链",
            "dramaName:剧名",
            "seriesName:集名",
            "createTime: 创建时间",
            "numAll:用户数据-新增用户数",
            "numDay:用户数据-今日新增(用户数)",
            "moneyAll:总充值数据-充值金额",
            "numAllRech:总充值数据-充值人数",
            "numAllOrder:总充值数据-充值笔数",
            "moneyAllDay:今日充值数据-充值总金额",
            "numAllDayRech:今日充值数据-充值总人数",
            "numAllDayOrder:今日充值数据-充值总笔数",
            "costAll:总成本(投放成本-累计)",
            "profitAll:利润(累计利润)",
            "profitAllRate:回报率",
            "advUserName:优化师名称",
            "retailName:分销商名称",
            "officialName:公众号名称",
            "pv:pv此链接的点击次数",
            "uv:uv此链接的点击人数"
    })
    @Slave
    @RequestMapping(value = "/getListAdmin", method = {RequestMethod.POST})
    public ResultVO<?> getListAdmin(HttpServletRequest request, FastLinkQueryVO params, PageVO pageVO) {
        // 设置取回投手自己创建的链接
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setAdvUserId(sessionVO.getUserId());
        if (params.getOfficialId() == null) {
            params.setOfficialId(sessionVO.getOfficialId());
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            return fastLinkExportService.exportLinkList(sessionVO, params);
        } else {
            return fastLinkService.queryPageListV2(params, pageVO);
        }
    }

    @ApiName(value = "渠道分析-挂载渠道分析", folder = {"数据统计"})
    @ApiParamsIn({
            "queryCreateTime:0:str:创建时间段-例如2022-08-01 - 2022-08-19",
            "miniIds:0:str:应用id们",
            "dramaIds:0:str:剧id们"
    })
    @ApiParamsOut({
            "link_sub_type:挂载类型",
            "moneyRecharge:总充值",
            "moneyProfit:总实到金额"
    })
    @Slave
    @RequestMapping(value = "/getSubListAdmin", method = {RequestMethod.POST})
    public ResultVO<?> getSubListAdmin(HttpServletRequest request, FastLinkQueryVO params) {
        // 设置取回投手自己创建的链接
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setAdvUserId(sessionVO.getUserId());
        if (params.getOfficialId() == null) {
            params.setOfficialId(sessionVO.getOfficialId());
        }
        return fastLinkService.querySubListAdmin(params);
    }

    @ApiName(value = "短剧推广-推广链接-查询单个详情", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "encryptionId:1:str:加密记录id"
    })
    @ApiParamsOut({
            "linkName:链接名称(派单渠道)",
            "dramaId: 短剧id",
            "seriesId: 剧集id",
            "backType: 回传类型:1-全局 2-自定义",
            "backRule: 回传规则id",
            "payType: 充值模板类型:1-全局 2-自定义",
            "payRule: 充值模板规则id",
            "advMediaId: 广告媒体：1-抖音"
    })
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastLinkPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastLinkPO fastLink = fastLinkService.queryById(params);
        return ResultVO.success(fastLink);
    }

    @ApiName(value = "短剧推广-推广链接-添加", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "linkName:1:str:链接名称(派单渠道)",
            "appType:1:int:投放应用类型：1-微信小程序，2-抖音小程序",
            "advMediaId:1:int:广告媒体：1-头条,2-adq,3-mp,4-百度",
            "dramaId:1:int:短剧id",
            "seriesId:1:int:剧集id",
            "seriesNum:1:int:第几集",
            "backType:1:int:回传类型:1-全局 2-自定义",
            "backRule:1:int:回传规则id",
            "payType:1:int:充值模板类型:1-全局 2-自定义 3-通用模板",
            "payRule:1:int:充值模板规则id",
            "adGroundId:1:int:落地页id",
            "adGroundDomain:1:str:域名",
            "linkType:1:int:1-投放链接，2-运营链接",
            "linkPlatform:1:int:1-公众号，2-企微",
            "feeFlag:1:int:付费标志 1=付费;2=免费",
            "anUnlockNum:1:int:安卓每次解锁剧集数",
            "iosUnlockNum:1:int:苹果每次解锁剧集数",
            "ttPayType:1:int:1=跟随链接，2=通用",
            "advUnlockFlag:1:int:是否开启广告解锁 1开启 0关闭",
            "advUnlockNum:1:int:广告解锁集数",
            "ttPayRule:1:int:通用规则id"
    })
    @ApiParamsOut({
            "state: ok",
            "in_promote_link:内推链接",
            "mini_link:小程序路径",
            "promote_link:推广链接"
    })
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastLinkPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setCreatorId(sessionVO.getUserId());
        params.setAdvUserId(sessionVO.getUserId());// 投手id
        params.setRetailId(sessionVO.getRetailId());// 分销商id
        params.setOfficialId(sessionVO.getOfficialId());// 渠道id(公众号账号id)

        // 判断付费免费剧
        FastDramaPO dramaPO = fastDramaMapper.queryById(params.getDramaId());
        if (dramaPO.getFeeFlag() == null || dramaPO.getFeeFlag() == 0) {
            return ResultVO.error("该剧不支持创建链接");
        }
        if (dramaPO.getFeeFlag() != null && dramaPO.getFeeFlag() > 0) {
            if (dramaPO.getFeeFlag() == 1 && params.getFeeFlag() != 1) {
                return ResultVO.error("只能创建付费链接");
            }
            if (dramaPO.getFeeFlag() == 2 && params.getFeeFlag() != 2) {
                return ResultVO.error("只能创建免费链接");
            }
        }


        if (params.getAdvMediaId() != null && params.getAdvMediaId() == 1 && params.getAdvVersion() == null) {
            params.setAdvVersion(1);// 默认巨量1.0
        }
        if (params.getAdvMediaId() != null && params.getAdvMediaId() == 4) {
            params.setAdvVersion(1);// 百度的默认为1好了。
        }
        if (params.getFeeFlag() == null) {
            params.setFeeFlag(1);
        }
        if (params.getFeeFlag() == 2) {
            if (params.getAnUnlockNum() == null || params.getAnUnlockNum() <= 0) {
                return ResultVO.error("安卓每次解锁剧集数不合法");
            }
            if (params.getIosUnlockNum() == null || params.getIosUnlockNum() <= 0) {
                return ResultVO.error("苹果每次解锁剧集数不合法");
            }
        }
        DomainValidator instance = DomainValidator.getInstance();
        if (notBlank(params.getAdGroundDomain()) && !instance.isValid(params.getAdGroundDomain())) {
            return ResultVO.error("请输入合法的域名");
        }
        if (params.getLinkType() == null) {
            params.setLinkType(1);// 推广链接
        }
        ResultVO resVO = fastLinkService.insert(params);
        if (resVO.getState().equals("ok")) {
            // 更新推广链接信息
            fastLinkService.updateLink(params);
            log.info("linkType:" + params.getLinkType());
            if (params.getLinkType() != null && params.getLinkType() == 2) {
                return ResultVO.success();
            } else {
                log.info("准备返回3个链接");
                // 返回3个推广链接
                return ResultVO.success(fastLinkService.getAddLink(params.getId()));
            }
        } else {
            return resVO;
        }
    }

    @ApiName(value = "短剧推广-推广链接-批量添加", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "linkName:1:str:链接名称(派单渠道)",
            "dramaShortName:0:str:短剧简称",
            "batchCount:1:int:批量个数",
            "appType:1:int:投放应用类型：1-微信小程序，2-抖音小程序，3=H5，4=快手小程序，5=快应用",
            "advMediaId:1:int:广告媒体：1-头条,2-adq,3-mp,4-百度,5-快手",
            "dramaId:1:int:短剧id",
            "seriesId:1:int:剧集id",
            "seriesNum:1:int:第几集",
            "backType:1:int:回传类型:1-全局 2-自定义",
            "backRule:1:int:回传规则id",
            "payType:1:int:充值模板类型:1-全局 2-自定义 3-通用模板",
            "payRule:1:int:充值模板规则id",
            "adGroundId:1:int:落地页id",
            "adGroundDomain:1:str:域名",
            "linkType:1:int:1-投放链接，2-运营链接",
            "linkSubType:0:int:1=星图推广;2=小程序推广;3=作品挂载;4=聚星推广;5=微信视频号",
            "linkPlatform:1:int:1-公众号，2-企微",
            "ttSplitPlatform:0:str:'抖音挂载分佣-平台(不含%)",
            "ttSplitXman:0:str:抖音挂载分佣-达人(不含%)",
            "ttSplitMcn:0:str:抖音挂载分佣-mcn(不含%)",
            "feeFlag:1:int:付费标志 1=付费;2=免费",
            "anUnlockNum:1:int:安卓每次解锁剧集数",
            "iosUnlockNum:1:int:苹果每次解锁剧集数",
            "generateLinkNameType:1:int:1 自定义 2 系统生成",
            "principalName:0:int:主体名称",
            "batchImportId:0:int:批量导入广告主账号的id",
            "ttPayType:1:int:1=跟随链接，2=通用",
            "advUnlockFlag:1:int:是否开启广告解锁 1开启 0关闭",
            "advUnlockNum:1:int:广告解锁集数",
            "ttPayRule:1:int:通用规则id"
    })
    @ApiParamsOut({
            "state: ok",
            "in_promote_link:内推链接",
            "mini_link:小程序路径",
            "promote_link:推广链接"
    })
    @RequestMapping(value = "/insertBatch", method = {RequestMethod.POST})
    public ResultVO<?> insertBatch(HttpServletRequest request, FastLinkPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());
        params.setAdvUserId(sessionVO.getUserId());// 投手id
        params.setRetailId(sessionVO.getRetailId());// 分销商id
        params.setOfficialId(sessionVO.getOfficialId());// 渠道id(公众号账号id)

        // 判断付费免费剧
        FastDramaPO dramaPO = fastDramaMapper.queryById(params.getDramaId());
        if (dramaPO.getFeeFlag() == null || dramaPO.getFeeFlag() == 0) {
            return ResultVO.error("该剧不支持创建链接");
        }
        if (params.getFeeFlag() == null) {
            params.setFeeFlag(1);
        }
        if (StrUtil.isNotEmpty(params.getDramaShortName()) &&
                params.getDramaShortName().length() > 10) {
            return ResultVO.error("短剧简称长度不能超过10个字");
        }
        if (dramaPO.getFeeFlag() != null && dramaPO.getFeeFlag() > 0) {
            if (dramaPO.getFeeFlag() == 1 && params.getFeeFlag() != 1) {
                return ResultVO.error("只能创建付费链接");
            }
            if (dramaPO.getFeeFlag() == 2 && params.getFeeFlag() != 2) {
                return ResultVO.error("只能创建免费链接");
            }
        }
        if (params.getAdvMediaId() != null && params.getAdvMediaId() == 1 && params.getAdvVersion() == null) {
            params.setAdvVersion(1);// 默认巨量1.0
        }
        if (params.getAdvMediaId() != null && params.getAdvMediaId() == 4) {
            params.setAdvVersion(1);// 百度的默认为1好了。
        }
        if (params.getLinkType() == null) {
            params.setLinkType(1);// 推广链接
        }
        DomainValidator instance = DomainValidator.getInstance();
        if (notBlank(params.getAdGroundDomain()) && !instance.isValid(params.getAdGroundDomain())) {
            return ResultVO.error("请输入合法的域名");
        }
        if (params.getGenerateLinkNameType() == null) {
            params.setGenerateLinkNameType(1);// 默认自定义
        }
        if (params.getLinkType() == null) {
            params.setLinkType(1);// 推广链接
        }
        if (params.getLinkType() == 3) {
            // 创建挂载链接
        }
        if (params.getFeeFlag() == null) {
            params.setFeeFlag(1);
        }
        if (params.getFeeFlag() == 2) {
            if (params.getAnUnlockNum() == null || params.getAnUnlockNum() <= 0) {
                return ResultVO.error("安卓每次解锁剧集数不合法");
            }
            if (params.getIosUnlockNum() == null || params.getIosUnlockNum() <= 0) {
                return ResultVO.error("苹果每次解锁剧集数不合法");
            }
        }
        FastLinkCreateRulePO verifyPO = new FastLinkCreateRulePO();
        verifyPO.setUserId(sessionVO.getUserId());
        verifyPO.setDramaId(params.getDramaId());
        verifyPO.setRetailId(sessionVO.getRetailId());
        List<FastLinkCreateRulePO> fastLinkCreateRulePOS = fastLinkCreateRuleService.verifyUser(verifyPO);
        if (CollUtil.isNotEmpty(fastLinkCreateRulePOS)) {
            return ResultVO.error("请先观看剧集后获取链接!");
        }
        ResultVO insertRes = fastLinkService.insertBatch(request, params, dramaPO, sessionVO);
        if (insertRes.getCode() == 0) {
            List<FastLinkPO> linkList = (List<FastLinkPO>) insertRes.getResults();
            for (FastLinkPO link : linkList) {
                // 更新推广链接信息
                fastLinkService.updateLink(link);
            }
            if (params.getLinkType() != null && params.getLinkType() == 2) {
                return ResultVO.success();
            } else if (linkList.size() == 1) {
                log.info("准备返回3个链接");
                // 返回3个推广链接
                return ResultVO.success(fastLinkService.getAddLink(params.getId()));
            }
            return ResultVO.success();
        } else {
            return insertRes;
        }
    }

    @ApiName(value = "短剧推广-推广链接-更新", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "encryptionId:1:str:加密记录id",
            "encryptionIds:1:str:批量修改时传的加密id们",
            "linkName:1:str:链接名称(派单渠道)",
            "startNumGlobal:1:int:起始付费是否全局：0否1是",
            "startNum:1:int:起始付费集数",
            "coinPerGlobal:1:int:单集k币是否全局：0否1是",
            "coinPer:1:int:单集付费k币数",
            "backType:1:int:回传类型:1-全局 2-自定义",
            "backRule:1:int:回传规则id",
            "payType:1:int:充值模板类型:1-全局 2-自定义 3-通用模板",
            "payRule:1:int:充值模板规则id",
            "adGroundId:1:int:落地页id",
            "adGroundDomain:1:str:域名",
            "feeFlag:1:int:付费标志 1=付费;2=免费",
            "anUnlockNum:1:int:安卓每次解锁剧集数",
            "iosUnlockNum:1:int:苹果每次解锁剧集数",
            "advUnlockFlag:1:int:是否开启广告解锁 1开启 0关闭",
            "advUnlockNum:1:int:广告解锁集数",
    })
    @ApiParamsOut({
            "state:ok"
    })
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastLinkPO params) {
        if (isBlank(params.getEncryptionId()) && isBlank(params.getEncryptionIds())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setUpdatorId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId()); // 编辑后，换分销商了
        if (StrUtil.isNotEmpty(params.getEncryptionIds())) {
            // 走批量修改
            return fastLinkService.updateBatch(request, params);
        }
        // 单个修改
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        DomainValidator instance = DomainValidator.getInstance();
        if (notBlank(params.getAdGroundDomain()) && !instance.isValid(params.getAdGroundDomain())) {
            return ResultVO.error("请输入合法的域名");
        }
        params.setId(id);
        if (params.getFeeFlag() == null) {
            params.setFeeFlag(1);
        }
        if (params.getFeeFlag() == 2) {
            if (params.getAnUnlockNum() == null || params.getAnUnlockNum() <= 0) {
                return ResultVO.error("安卓每次解锁剧集数不合法");
            }
            if (params.getIosUnlockNum() == null || params.getIosUnlockNum() <= 0) {
                return ResultVO.error("苹果每次解锁剧集数不合法");
            }
        }
        ResultVO resVO = fastLinkService.update(request, params);
        if (resVO.getState().equals("ok")) {
            RedisUtil.del(StaticVar.LINK_INFO_ID + id);
            RedisUtil.del(StaticVar.LINK_EXT_INFO_ID + id);
        }
        return resVO;
    }

    @ApiName(value = "短剧推广-推广链接-复制链接", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "encryptionId:1:int:加密的推广链接id",
    })
    @ApiParamsOut({
            "code:0",
            "state:ok",
            "results.link:参数解释 dramaId-短剧id;seriesId-剧集id; officialId-渠道公众号id;linkId-推广链接id;from-广告媒体id;appId-小程序appid;title-外链标题;path-推广链接路径"
    })
    @RequestMapping(value = "/copyLink", method = {RequestMethod.POST})
    public ResultVO<?> copyLink(HttpServletRequest request, FastLinkPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        // 获取复制链接
        String link = fastLinkService.copyLink(id);
        Map<String, Object> results = ResultVO.getMap();
        results.put("link", link);
        return ResultVO.success(results);
    }

    @ApiName(value = "数据分析-渠道监控(免费)", folder = {"数据统计"})
    @ApiParamsIn({
            "queryCreateTime:0:str:创建时间段-例如2022-08-01 - 2022-08-19",
            "queryStatisTime:0:str:统计时间段-例如2022-08-01 - 2022-08-19",
            "miniIds:0:str:应用id们",
            "dramaIds:0:str:剧id们",
            "linkSubTypes:0:str:挂载类型",
            "queryName:0:str:链接名称或id",
            "inPage:0:str:入口页面",
            "retailName:0:str:分销商名称",
            "officialName:0:str:公众号名称",
            "advUserName:0:str:优化师名称",
            "advUserIds:0:str:优化师ids",
            "queryType:0:int:废弃，用下面参数--排序方式-1倒序 2正序  新增用户数排序 --用户没选择时默认1 ",
            "orderNumAll:0:int:排序方式-1倒序 2正序  新增用户数排序 --用户没选择时默认1，注-4个排序参数字段只能设置1个，其他需为空",
            "orderMoneyAll:0:int:排序方式-1倒序 2正序  总充值排序",
            "orderMoneyAllDay:0:int:排序方式-1倒序 2正序  今日充值排序",
            "orderProfitAll:0:int:排序方式-1倒序 2正序  总利润排序",
            "size:0:int:每页条数",
            "page:0:int:页码"
    })
    @ApiParamsOut({
            "id:链接ID",
            "linkName:链接名称",
            "advMediaId:广告媒体：1-头条;2-adq; 3-mp 4-百度 5-快手",
            "advVersion: 投放版本1：1.0，2：2.0",
            "feeFlag:付费标志:1=付费;2=免费",
            "shortLink:短链",
            "inPage:入口页面",
            "createTime: 创建时间",
            "setInfo:相关设置",
            "dramaName:剧名",
            "seriesName:集名",
            "seriesNum:第几集",
            "startNum:起始付费剧集",
            "coinPer:单集K币",
            "anUnlockNum:安卓单次解锁次数",
            "iosUnlockNum:IOS单次解锁次数",
            "numDay:当日新增用户数",
            "numAll:累计新增用户数",
            "pv:pv",
            "uv:uv",
            "adIncomeAll:累计广告收入",
            "adWatchNum:观看次数",
            "adWatchMemberNum:观看人数",
            "adUnlockNum:解锁次数",
            "adUnlockMemberNum:解锁人数",
            "adIncomeDay:当日广告收入",
            "adWatchNumDay:当日观看次数",
            "adWatchMemberNumDay:当日观看人数",
            "adUnlockNumDay:当日解锁次数",
            "adUnlockMemberNumDay:当日解锁人数",
            "income:利润",
            "returnRatio:汇报率",
            "costAll:总成本",
            "totalBackRatio:累计回传率",
            "followNum:拉起关注集号",
            "advUserName:优化师名称",
            "retailName:分销商名称",
            "officialName:公众号名称",
            "rechargeMoney:充值金额",
            "rechargeNum:充值笔数",
            "rechargeMemberNum:充值人数",
            "totalRechargeMoney:累计充值金额",
            "totalRechargeNum:累计充值笔数",
            "totalRechargeMemberNum:累计充值人数",
            "totalIncome:累计收入（充值收入+广告收入）"
    })
    @Slave
    @RequestMapping(value = "/getListAdminFree", method = {RequestMethod.POST})
    public ResultVO<?> getListAdminFree(HttpServletRequest request, FastLinkQueryVO params, PageVO pageVO) {
        // 处理查询参数
        SessionVO sessionVO = getSessionVO(request);
        params.setStatisDate(DateUtil.format09(DateUtil.beginOfDay()));
        params.setContentType(sessionVO.getContentType());
        if (params.getExportData() == 1) {
            return fastLinkExportService.exportFreeLinkList(sessionVO, params);
        } else {
            return fastLinkService.getFreePageList(params, pageVO);
        }
    }

    @ApiName(value = "短剧推广-推广链接列表(免费模式)", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "dramaIds:0:str:短剧ids",
            "dramaName:0:str:短剧名称",
            "advUserName:0:str:优化师名称",
            "advUserIds:0:str:优化师ids",
            "queryCreateTime:0:str:创建时间段-例如2022-08-01 - 2022-08-19",
            "queryName:0:str:查询字符串-匹配名称和id",
            "size:0:int:每页条数",
            "page:0:int:页码"
    })
    @ApiParamsOut({
            "id:链接ID",
            "linkName:链接名称",
            "advMediaId:广告媒体：1-头条;2-adq; 3-mp 4-百度 5-快手",
            "advVersion: 投放版本1：1.0，2：2.0",
            "feeFlag:付费标志:1=付费;2=免费",
            "shortLink:短链",
            "inPage:入口页面",
            "createTime: 创建时间",
            "setInfo:相关设置",
            "dramaName:剧名",
            "seriesName:集名",
            "seriesNum:第几集",
            "startNum:起始付费剧集",
            "coinPer:单集K币",
            "anUnlockNum:安卓单次解锁次数",
            "iosUnlockNum:IOS单次解锁次数",
            "numDay:当日新增用户数",
            "numAll:累计新增用户数",
            "pv:pv",
            "uv:uv",
            "adIncomeAll:累计广告收入",
            "adWatchNum:观看次数",
            "adWatchMemberNum:观看人数",
            "adUnlockNum:解锁次数",
            "adUnlockMemberNum:解锁人数",
            "adIncomeDay:当日广告收入",
            "adWatchNumDay:当日观看次数",
            "adWatchMemberNumDay:当日观看人数",
            "adUnlockNumDay:当日解锁次数",
            "adUnlockMemberNumDay:当日解锁人数",
            "income:利润",
            "returnRatio:汇报率",
            "costAll:总成本",
            "totalBackRatio:累计回传率",
            "followNum:拉起关注集号",
            "advUserName:优化师名称",
            "retailName:分销商名称",
            "officialName:公众号名称",
            "rechargeMoney:充值金额",
            "rechargeNum:充值笔数",
            "rechargeMemberNum:充值人数",
            "totalRechargeMoney:累计充值金额",
            "totalRechargeNum:累计充值笔数",
            "totalRechargeMemberNum:累计充值人数",
            "totalIncome:累计收入（充值收入+广告收入）"
    })
    @Slave
    @RequestMapping(value = "/getFreeList", method = {RequestMethod.POST})
    public ResultVO<?> getFreeList(HttpServletRequest request, FastLinkQueryVO params, PageVO pageVO) {
        // 处理查询参数
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        params.setStatisDate(DateUtil.format09(DateUtil.beginOfDay()));
        if (params.getOfficialId() == null) {
            params.setOfficialId(sessionVO.getOfficialId());
        }
        if (biggerZero(sessionVO.getRetailId()) && !"2".equals(sessionVO.getRoleIds())) {
            params.setAdvUserId(sessionVO.getUserId());
        }
        if (biggerZero(sessionVO.getRetailId())) {
            params.setRetailId(sessionVO.getRetailId());
        }
        if (params.getExportData() == 1) {
            return fastLinkExportService.exportFreeLinkList(sessionVO, params);
        } else {
            return fastLinkService.getFreePageList(params, pageVO);
        }
    }


    @ApiName(value = "短剧推广-导入广告账户Id", folder = {"business"})
    @ApiParamsIn({
            "file:1:File:导入广告主账户id的文件"
    })
    @ApiParamsOut({
            "result:batchImportId(批量导入id)"
    })
    @PostMapping("/importAdvertiserAccount")
    public ResultVO<?> importAdvertiserAccount(MultipartFile file) {
        if (file == null) {
            return ResultVO.error("请选择导入文件");
        }
        return ResultVO.success(fastLinkImportAdvertiserService.importAdvertiserAccount(file));
    }
}
