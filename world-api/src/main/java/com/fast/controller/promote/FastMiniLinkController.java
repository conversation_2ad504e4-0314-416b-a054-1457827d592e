/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.promote.FastMiniLinkPO;
import com.fast.service.promote.FastMiniLinkService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMiniLink")
public class FastMiniLinkController extends BaseController {

    @Autowired
    private FastMiniLinkService fastMiniLinkService;

    @ApiName(value = "短剧推广-推广链接-小程序常用链接-查询列表", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "size:0:int:每页条数",
            "page:0:int:页码"
    })
    @ApiParamsOut({
            "miniLinkName:小程序常用链接名称",
            "miniLinkUrl:小程序常用链接地址"
    })
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastMiniLinkPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getType() == null) {
            params.setType(1);
        }
        params.setContentType(sessionVO.getContentType());
        return fastMiniLinkService.queryPageList(params, pageVO, sessionVO);
    }

    @ApiName(value = "短剧推广-推广链接-小程序常用链接-查询单个详情", folder = {"分销商/短剧推广"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastMiniLinkPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMiniLinkPO fastMiniLink = fastMiniLinkService.queryById(params);
        return ResultVO.success(fastMiniLink);
    }

    @ApiName(value = "短剧推广-推广链接-小程序常用链接-添加", folder = {"分销商/短剧推广"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastMiniLinkPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMiniLinkService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "短剧推广-推广链接-小程序常用链接-更新", folder = {"分销商/短剧推广"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastMiniLinkPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMiniLinkService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
