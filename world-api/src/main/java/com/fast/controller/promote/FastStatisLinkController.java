/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.annotation.Slave;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.promote.FastStatisLinkPO;
import com.fast.service.promote.FastLinkExportService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.promote.FastStatisLinkFreeService;
import com.fast.service.promote.FastStatisLinkService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.promote.FastLinkQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastStatisLink")
public class FastStatisLinkController extends BaseController {

    @Autowired
    private FastStatisLinkService fastStatisLinkService;

    @Autowired
    private FastLinkService fastLinkService;

    @Autowired
    private FastLinkExportService fastLinkExportService;

    @Autowired
    private FastStatisLinkFreeService fastStatisLinkFreeService;

    @ApiName(value = "短剧推广-推广链接-数据统计-查询列表", folder = {"分销商/短剧推广"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastStatisLinkPO params, PageVO pageVO) {
        return fastStatisLinkService.queryPageList(params, pageVO);
    }

    @ApiName(value = "短剧推广-推广链接-数据统计-查询单个详情", folder = {"分销商/短剧推广"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastStatisLinkPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastStatisLinkPO fastStatisLink = fastStatisLinkService.queryById(params);
        return ResultVO.success(fastStatisLink);
    }

    @ApiName(value = "短剧推广-推广链接-数据统计-添加", folder = {"分销商/短剧推广"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastStatisLinkPO params) {
        SessionVO sessionVO = getSessionVO(request);
//        params.setCreatorId(sessionVO.getUserId());
        params.setRetailId(sessionVO.getRetailId());
        MethodVO methodVO = fastStatisLinkService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "短剧推广-推广链接-数据统计-更新", folder = {"分销商/短剧推广"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastStatisLinkPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
//        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastStatisLinkService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "短剧推广-推广链接-数据统计-修改每日成本（推广链接）", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "encryptionId:1:str:加密记录id",
            "statisDate:1:str:日期",
            "linkId:1:int:链接Id",
            "costDay:1:str:投放成本"
    })
    @ApiParamsOut({
            "state:ok"
    })
    @RequestMapping(value = "/updateCost", method = {RequestMethod.POST})
    public ResultVO<?> updateCost(HttpServletRequest request, FastStatisLinkPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        MethodVO methodVO = fastStatisLinkService.updateCost(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "短剧推广-推广链接-推广数据详情页-累计数据", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "encryptionId:1:str:加密记录id(推广链接id加密)",
    })
    @ApiParamsOut({
            "id:链接id",
            "linkName:链接名称(派单渠道)",
            "inPage: 入口页面",
            "createTime: 创建时间",
            "numAll:用户数据-新增用户数",
            "numDay:用户数据-今日新增(用户数)",
            "moneyAll:总充值数据-充值金额",
            "numAllRech:总充值数据-充值人数",
            "numAllOrder:总充值数据-充值笔数",
            "moneyAllDay:今日充值数据-充值总金额",
            "numAllDayRech:今日充值数据-充值总人数",
            "numAllDayOrder:今日充值数据-充值总笔数",
            "costAll:总成本(投放成本-累计)",
            "profitAll:利润(累计利润)",
            "profitAllRate:回报率",
            "pv:pv此链接的点击次数",
            "uv:uv此链接的点击人数"
    })
    @Slave
    @RequestMapping(value = "/linkStatis/getAllData", method = {RequestMethod.POST})
    public ResultVO<?> getAllData(HttpServletRequest request, FastLinkQueryVO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);

        return fastStatisLinkService.getAllData(params);
    }

    @ApiName(value = "短剧推广-推广链接-推广数据详情页-实时监测数据", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "encryptionId:1:str:加密记录id(推广链接id加密)",
    })
    @ApiParamsOut({
            "statisDate:日期",
            "numDay:今日新增用户数",
            "moneyDay:今日新增用户充值-金额",
            "numDayRech:今日新增用户充值-人数",
            "numDayOrder:今日新增用户充值-笔数",
            "moneyDayArpu:今日新增用户充值-客单价",
            "moneyDayRateUserPay:今日新增用户充值-付费率",
            "moneyAllDay:今日总充值-金额",
            "numAllDayRech:今日总充值-人数",
            "numAllDayOrder:今日总充值-笔数",
            "moneyAllDayArpu:今日总充值-客单价",
            "moneyAllDayRateUserPay:今日总充值-付费率",
            "moneyAllDayCommon:普通充值-金额",
            "numAllDayCommonRech:普通充值-人数",
            "numAllDayCommonOrder:普通充值-笔数",
            "moneyAllDayCommonArpu:普通充值-客单价",
            "moneyAllDayCommonRateUserPay:普通充值-付费率",
            "moneyAllDayVip:用户充值-金额",
            "numAllDayVipRech:用户充值-人数",
            "numAllDayVipOrder:用户充值-笔数",
            "moneyAllDayVipArpu:用户充值-客单价",
            "moneyAllDayVipRateUserPay:用户充值-付费率"
    })
    @Slave
    @RequestMapping(value = "/linkStatis/getImmeMonitorData", method = {RequestMethod.POST})
    public ResultVO<?> getImmeMonitorData(HttpServletRequest request, FastLinkQueryVO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);

        return fastStatisLinkService.getImmeMonitorData(params);
    }

    @ApiName(value = "短剧推广-推广链接-推广数据详情页-每日回收数据列表", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "queryCreateTime:0:str:时间段-例如2022-08-01 - 2022-08-19",
            "isAll:0:int:是否全部显示 0-是 1-否",
            "size:0:int:每页条数",
            "page:0:int:页码"
    })
    @ApiParamsOut({
            "statisDate:日期",
            "linkId:链接id",
            "numDay:新增用户数",
            "costDay:总成本",
            "costDayPu:用户成本",
            "costDayPayPu:付费用户成本",
            "moneyDAllRech:累计充值金额",
            "numDAllRech:累计充值用户",
            "roiDAllRech:累计回报率",
            "moneyD60Rech:D0-D60充值金额 示例 1.20,2.23,,,,,,3.33,9.99",
            "numD60Rech:D0-D60充值人数 示例 1,2,,,,,,3,9",
            "roiD60Rech:D0-D60充值回报率 示例 1.20%,2.23%,,,,,,3.33%,9.99%"
    })
    @Slave
    @RequestMapping(value = "/linkStatis/getDayRoiList", method = {RequestMethod.POST})
    public ResultVO<?> getDayRoiList(HttpServletRequest request, FastLinkQueryVO params, PageVO pageVO) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);

        return fastStatisLinkService.getDayRoiList(params, pageVO);
    }

    /**
     * 查询链接linkid维度的订单统计
     */
    @ApiName(value = "渠道维度的订单统计", folder = {"渠道统计"})
    @ApiParamsIn({
            "linkId:1:int:渠道id",
            "beginDate:1:str:开始日期yyyy-mm-dd",
            "endDate:1:str:结束日期yyyy-mm-dd"
    })
    @ApiParamsOut({
            "date:日期",
            "hour:小时",
            "orderCount:订单数量",
            "orderCountAll:订单总数量",
            "moneyRecharge:订单金额",
            "moneyRechargeAll:订单总金额"
    })
    @RequestMapping(value = "/getLinkOrderStatis", method = {RequestMethod.POST})
    public ResultVO<?> getLinkOrderStatis(HttpServletRequest request, FastLinkQueryVO params) {
        if (params.getLinkId() == null) {
            return ResultVO.error("链接linkId不能为空");
        }
        if (StrUtil.isEmpty(params.getBeginDate())) {
            return ResultVO.error("开始日期不能为空");
        }
        if (StrUtil.isEmpty(params.getEndDate())) {
            return ResultVO.error("结束日期不能为空");
        }
        if (params.getBeginDate().equals(params.getEndDate())) {
            // 单天统计
            return fastStatisLinkService.getLinkOrderStatisHour(params);
        } else {
            // 多天统计
            return fastStatisLinkService.getLinkOrderStatisDay(params);
        }
    }

    @ApiName(value = "短剧推广-推广链接(免费短剧)-推广数据详情页-累计数据", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "encryptionId:1:str:加密记录id(推广链接id加密)",
    })
    @ApiParamsOut({
            "id:链接id",
            "linkName:链接名称(派单渠道)",
            "inPage: 入口页面",
            "createTime: 创建时间",
            "numAll:用户数据-新增用户数",
            "numDay:用户数据-今日新增(用户数)",
            "pv:pv",
            "uv:uv",
            "adIncomeAll:累计广告收入",
            "adWatchNum:观看次数",
            "adWatchMemberNum:观看人数",
            "adUnlockNum:解锁次数",
            "adUnlockMemberNum:解锁人数",
            "adIncomeDay:当日广告收入",
            "adWatchNumDay:当日观看次数",
            "adWatchMemberNumDay:当日观看人数",
            "adUnlockNumDay:当日解锁次数",
            "adUnlockMemberNumDay:当日解锁人数",
            "income:利润",
            "returnRatio:回报率",
            "costAll:总成本",
            "totalBackRatio:累计回传率",
            "rechargeMoney:充值金额",
            "rechargeNum:充值笔数",
            "rechargeMemberNum:充值人数",
            "totalRechargeMoney:累计充值金额",
            "totalRechargeNum:累计充值笔数",
            "totalRechargeMemberNum:累计充值人数",
            "totalIncome:累计收入（充值收入+广告收入）"
    })
    @Slave
    @RequestMapping(value = "/linkStatis/getAllFreeData", method = {RequestMethod.POST})
    public ResultVO<?> getAllFreeData(HttpServletRequest request, FastLinkQueryVO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setRetailId(sessionVO.getRetailId());
        return fastStatisLinkService.getAllFreeData(params);
    }

    @ApiName(value = "短剧推广-推广链接(免费短剧)-推广数据详情页-实时监测数据", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "encryptionId:1:str:加密记录id(推广链接id加密)",
    })
    @ApiParamsOut({
            "statisDate:日期",
            "addMemberNum:今日新增用户数",
            "todayIncome 》 adIncomeDay:新增用户广告收入",
            "todayIncome 》 moneyRecharge:新增用户充值收入",
            "todayIncome 》 adWatchMemberNumDay:新增用户观看人数",
            "todayIncome 》 adWatchNum:新增用户观看次数",
            "todayIncome 》 memberPrice:客单价",
            "todayIncome 》 convertRetio:转化率",
            "todayIncomeAll 》 adIncomeDay:今日广告收入",
            "todayIncomeAll 》 moneyRecharge:今日充值收入",
            "todayIncomeAll 》 adWatchMemberNumDay:今日观看人数",
            "todayIncomeAll 》 adWatchNumDay:今日观看次数",
            "todayIncomeAll 》 memberPrice:客单价",
            "totalROI:累计ROI"
    })
    @Slave
    @RequestMapping(value = "/linkStatis/getFreeImmeMonitorData", method = {RequestMethod.POST})
    public ResultVO<?> getFreeImmeMonitorData(HttpServletRequest request, FastLinkQueryVO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        return fastStatisLinkService.getFreeImmeMonitorData(params);
    }

    @ApiName(value = "短剧推广-推广链接(免费短剧)-推广数据详情页-每日回收数据列表", folder = {"分销商/短剧推广"})
    @ApiParamsIn({
            "queryCreateTime:0:str:时间段-例如2022-08-01 - 2022-08-19",
            "encryptionId:0:str:加密linkID",
            "size:0:int:每页条数",
            "page:0:int:页码"
    })
    @ApiParamsOut({
            "statisDate:日期",
            "addMemberNum:新增用户数",
            "totalCost:总成本",
            "memberCost:用户成本",
            "unlockCost:付费用户成本",
            "unlcokMemberNum:累计观看用户",
            "unlockIncome:累计广告收入",
            "returnRatio:累计回报率",
            "unlockMemberD60:D0-D60累计观看用户",
            "unlockIncomeD60:D0-D60累计广告收入",
            "returnRatioD60:D0-D60累计回报率"
    })
    @Slave
    @RequestMapping(value = "/linkStatis/getFreeDayRoiList", method = {RequestMethod.POST})
    public ResultVO<?> getFreeDayRoiList(HttpServletRequest request, FastLinkQueryVO params, PageVO pageVO) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setLinkId(id);
        SessionVO sessionVO = getSessionVO(request);
        if (params.getExportData() == 1) {
            return fastLinkExportService.exportFreeDayRoiList(sessionVO, params);
        } else {
            return fastStatisLinkService.getFreeDayRoiList(params, pageVO);
        }
    }

    /**
     * 业务数据
     *
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping(value = "nologin/syncFreeLinkStatisV1", method = {RequestMethod.POST})
    public ResultVO<?> syncFreeLinkStatisV1(String startDate, String endDate) {
        Date startDateTime = DateUtil.format09(startDate);
        Date endDateTime = DateUtil.format09(endDate);
        Integer days = DateUtil.daysBetween(startDateTime, endDateTime);
        for (Integer i = 0; i <= days; i++) {
            Date syncDate = DateUtil.addDays(startDateTime, i);
            Long curMillis = System.currentTimeMillis();
            fastStatisLinkFreeService.syncFreeLinkBusinessData(syncDate);
            log.error("=====" + DateUtil.format09(syncDate) + " 同步免费短报表剧推广渠道耗时" + (System.currentTimeMillis() - curMillis) / 1000 + "秒=======");
        }
        return ResultVO.success();
    }

    /**
     * d60数据
     *
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping(value = "nologin/syncFreeLinkStatisV2", method = {RequestMethod.POST})
    public ResultVO<?> syncFreeLinkStatisV2(String startDate, String endDate) {
        Date startDateTime = DateUtil.format09(startDate);
        Date endDateTime = DateUtil.format09(endDate);
        Integer days = DateUtil.daysBetween(startDateTime, endDateTime);
        for (Integer i = 0; i <= days; i++) {
            Date syncDate = DateUtil.addDays(startDateTime, i);
            Long curMillis = System.currentTimeMillis();
            fastStatisLinkFreeService.statisFreeLink(syncDate);
            log.error("=====" + DateUtil.format09(syncDate) + " 同步免费短报表剧推广渠道耗时" + (System.currentTimeMillis() - curMillis) / 1000 + "秒=======");
        }
        return ResultVO.success();
    }

    /**
     * 优化版d60数据
     *
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping(value = "nologin/syncFreeLinkStatisV3", method = {RequestMethod.POST})
    public ResultVO<?> syncFreeLinkStatisV3(String startDate, String endDate) {
        Date startDateTime = DateUtil.format09(startDate);
        Date endDateTime = DateUtil.format09(endDate);
        Long curMillis = System.currentTimeMillis();
        fastStatisLinkFreeService.statisFreeLinkD60(DateUtil.beginOfDay(startDateTime), DateUtil.endOfDay(endDateTime));
        log.error("=====" + DateUtil.format09(startDateTime) + "至" + DateUtil.format09(endDateTime) + " 同步免费短报表剧推广渠道耗时" + (System.currentTimeMillis() - curMillis) / 1000 + "秒=======");
        return ResultVO.success();
    }

}











