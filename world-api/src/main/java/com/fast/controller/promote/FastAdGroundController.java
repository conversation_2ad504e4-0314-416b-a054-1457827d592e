/*
 * Powered By fast.up
 */
package com.fast.controller.promote;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.promote.FastAdGroundPO;
import com.fast.service.promote.FastAdGroundService;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.promote.FastAdGroundVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 广告落地页
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastAdGround")
public class FastAdGroundController extends BaseController {

    @Autowired
    private FastAdGroundService adGroundService;

    @ApiName(value = "广告落地页-查询列表", folder = {"promote"})
    @ApiParamsIn({"token",})
    @ApiParamsOut({
            "results>>state:状态 0=待使用;1=使用中",
            "results>>id:id",
            "results>>pageName:名称",
            "results>>pageFirst:首屏图片地址",
            "results>>buttonType:按钮样式 1=仅按钮;2=按钮+文字",
            "results>>buttonText:按钮文案",
            "results>>textText:文本文案",
            "results>>creatorName:创建人姓名",
            "results>>createTime:创建时间",
            "results>>linkCount:链接使用总数",
    })
    @PostMapping("/getFastAdGroundList")
    public ResultVO<?> getList(HttpServletRequest request, FastAdGroundPO params, PageVO pageVO) {
        params.setDelFlag(0);
        return adGroundService.queryPageList(params, pageVO);
    }

    @ApiName(value = "广告落地页-查询单个详情", folder = {"promote"})
    @ApiParamsIn({"encryptionId:加密id",})
    @ApiParamsOut({
            "results>>state:状态 0=待使用;1=使用中",
            "results>>id:id",
            "results>>pageName:名称",
            "results>>pageFirst:首屏图片地址",
            "results>>buttonType:按钮样式 1=仅按钮;2=按钮+文字",
            "results>>buttonText:按钮文案",
            "results>>textText:文本文案",
            "results>>creatorName:创建人姓名",
            "results>>createTime:创建时间",
            "results>>pages>>picUrl:图片地址",
            "results>>pages>>height:图片高度,数字格式,无单位",
    })
    @PostMapping("/getFastAdGroundDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastAdGroundPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastAdGroundVO fastAdGround = adGroundService.queryInfoByRedis(params);
        return ResultVO.success(fastAdGround);
    }

    @ApiName(value = "广告落地页-添加", folder = {"promote"})
    @ApiParamsIn({
            ">->->:1:str:以下为json数据",
            "pageName:1:str:名称",
            "buttonType:1:str:按钮样式 1=仅按钮;2=按钮+文字",
            "buttonText:1:str:按钮文案",
            "textText:1:str:文本文案",
            "pages>>picUrl:1:str:图片地址",
            "pages>>height:1:str:图片高度,整数格式,不需要单位",
    })
    @ApiParamsOut({"success",})
    @PostMapping("/insertFastAdGround")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody @Validated FastAdGroundPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = adGroundService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "广告落地页-更新", folder = {"promote"})
    @ApiParamsIn({
            ">->->->:1:str:以下为json数据",
            "encryptionId:1:str:加密id",
            "pageName:1:str:名称",
            "buttonType:1:str:按钮样式 1=仅按钮;2=按钮+文字",
            "buttonText:1:str:按钮文案",
            "textText:1:str:文本文案",
            "pages>>picUrl:1:str:图片地址",
            "pages>>height:1:str:图片高度,整数格式,不需要单位",
    })
    @ApiParamsOut({"success",})
    @PostMapping("/updateFastAdGround")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody @Validated FastAdGroundPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = adGroundService.update(params);

        RedisUtil.del(StaticVar.AD_GROUND_ID + params.getId());
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "广告落地页-删除", folder = {"promote"})
    @ApiParamsIn({"encryptionId:1:str:加密id",})
    @ApiParamsOut({"success",})
    @PostMapping("/deleteFastAdGround")
    public ResultVO<?> delete(HttpServletRequest request, FastAdGroundPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = adGroundService.delete(params);

        RedisUtil.del(StaticVar.AD_GROUND_ID + params.getId());
        return ResultVO.fromMethodVO(methodVO);
    }
}
