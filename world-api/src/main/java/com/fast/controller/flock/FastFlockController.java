/*
 * Powered By fast.up
 */
package com.fast.controller.flock;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.flock.FastFlockPO;
import com.fast.service.flock.FastFlockService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFlock")
public class FastFlockController extends BaseController {

    @Autowired
    private FastFlockService fastFlockService;

    @ApiName(value = "flock-查询列表", folder = {"抖音群聊"})
    @ApiParamsIn({"0000"})
    @ApiParamsOut({
            "flockName:群聊名称",
            "containCount:容纳人数",
            "flockType:1企业号2企业员工号3个人号",
            "currentCount:在群人数",
            "flockPass:群口令",
            "validTime:有效期",
            "showTimes:已经曝光次数",
            "maxTimes:可曝光次数",
            "createType:1线下创建2接口创建"
    })
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastFlockPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        params.setMiniId(sessionVO.getMiniId());
        params.setDelFlag(0);
        return fastFlockService.queryPageList(params, pageVO);
    }

    @ApiName(value = "flock-查询单个详情", folder = {"抖音群聊"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastFlockPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFlockPO fastFlock = fastFlockService.queryById(params);
        return ResultVO.success(fastFlock);
    }

    @ApiName(value = "flock-添加", folder = {"抖音群聊"})
    @ApiParamsIn({
            "flockName:1:str:群名称",
            "containCount:1:int:容纳人数",
            "flockType:1:int:群类型1企业号2企业员工号3个人号",
            "currentCount:1:int:群内当前人数",
            "flockPass:1:str:群口令",
            "validTimeStr:0:str:有效期时间yyyy-mm-dd HH:mm:ss",
            "showTimes:1:int:已经曝光次数",
            "maxTimes:1:int:可曝光次数"
    })
    @ApiParamsOut({"status:ok"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastFlockPO params) {
        if (StrUtil.isEmpty(params.getFlockName())) {
            return ResultVO.error("群名称不能为空");
        }
        if (params.getContainCount() == null) {
            return ResultVO.error("容纳人数不能为空");
        }
        if (params.getFlockType() == null) {
            return ResultVO.error("群类型不能为空");
        }
        if (params.getCurrentCount() == null) {
            params.setCurrentCount(0);
        }
        if (StrUtil.isNotEmpty(params.getValidTimeStr())) {
            params.setValidTime(DateUtil.format07(params.getValidTimeStr()));
        }
        if (StrUtil.isNotEmpty(params.getFlockCreateTimeStr())) {
            params.setFlockCreateTime(DateUtil.format07(params.getFlockCreateTimeStr()));
        }
        if (StrUtil.isEmpty(params.getFlockPass())) {
            return ResultVO.error("群口令不能为空");
        }
        params.setCreateType(1);// 线下创建
        params.setDelFlag(0);
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setUpdatorId(sessionVO.getUserId());

        params.setMiniId(sessionVO.getMiniId());
        MethodVO methodVO = fastFlockService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "flock-更新", folder = {"抖音群聊"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "flockName:1:str:群名称",
            "containCount:1:int:容纳人数",
            "flockType:1:int:群类型1企业号2企业员工号3个人号",
            "currentCount:1:int:群内当前人数",
            "flockPass:1:str:群口令",
            "validTimeStr:0:str:有效期时间yyyy-mm-dd HH:mm:ss",
            "showTimes:1:int:已经曝光次数",
            "maxTimes:1:int:可曝光次数"
    })
    @ApiParamsOut({"status:ok"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastFlockPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (StrUtil.isNotEmpty(params.getValidTimeStr())) {
            params.setValidTime(DateUtil.format07(params.getValidTimeStr()));
        }
        if (StrUtil.isNotEmpty(params.getFlockCreateTimeStr())) {
            params.setFlockCreateTime(DateUtil.format07(params.getFlockCreateTimeStr()));
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastFlockService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "删除群", folder = {"抖音群聊"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id"
    })
    @ApiParamsOut({"status:ok"})
    @RequestMapping(value = "/deleteOne", method = {RequestMethod.POST})
    public ResultVO<?> deleteOne(HttpServletRequest request, FastFlockPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        params.setDelFlag(1);
        MethodVO methodVO = fastFlockService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
