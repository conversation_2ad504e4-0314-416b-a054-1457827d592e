/*
 * Powered By fast.up
 */
package com.fast.controller.flock;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.flock.FastFlockOwnerPO;
import com.fast.service.flock.FastFlockOwnerService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastFlockOwner")
public class FastFlockOwnerController extends BaseController {

    @Autowired
    private FastFlockOwnerService fastFlockOwnerService;

    @ApiName(value = "flock-查询列表", folder = {"flock"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastFlockOwnerPO params, PageVO pageVO) {
        return fastFlockOwnerService.queryPageList(params, pageVO);
    }

    @ApiName(value = "flock-查询单个详情", folder = {"flock"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastFlockOwnerPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastFlockOwnerPO fastFlockOwner = fastFlockOwnerService.queryById(params);
        return ResultVO.success(fastFlockOwner);
    }

    @ApiName(value = "flock-添加", folder = {"flock"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastFlockOwnerPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastFlockOwnerService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "flock-更新", folder = {"flock"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastFlockOwnerPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastFlockOwnerService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
