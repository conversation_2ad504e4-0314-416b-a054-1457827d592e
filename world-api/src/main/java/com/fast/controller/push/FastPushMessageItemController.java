/*
 * Powered By fast.up
 */
package com.fast.controller.push;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.push.FastPushMessageItemPO;
import com.fast.service.push.FastPushMessageItemService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastPushMessageItem")
public class FastPushMessageItemController extends BaseController {

    @Autowired
    private FastPushMessageItemService fastPushMessageItemService;

    @ApiName(value = "促销活动-查询列表", folder = {"推送"})
    @RequestMapping(value = "/getList", method = {RequestMethod.POST})
    public ResultVO<?> getList(HttpServletRequest request, FastPushMessageItemPO params, PageVO pageVO) {
        return fastPushMessageItemService.queryPageList(params, pageVO);
    }

    @ApiName(value = "促销活动-查询单个详情", folder = {"推送"})
    @RequestMapping(value = "/getDetail", method = {RequestMethod.POST})
    public ResultVO<?> getDetail(HttpServletRequest request, FastPushMessageItemPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastPushMessageItemPO fastPushMessageItem = fastPushMessageItemService.queryById(params);
        return ResultVO.success(fastPushMessageItem);
    }

    @ApiName(value = "促销活动-添加", folder = {"推送"})
    @RequestMapping(value = "/insert", method = {RequestMethod.POST})
    public ResultVO<?> insert(HttpServletRequest request, FastPushMessageItemPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastPushMessageItemService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "促销活动-更新", folder = {"推送"})
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    public ResultVO<?> update(HttpServletRequest request, FastPushMessageItemPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastPushMessageItemService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
