/*
 * Powered By fast.up
 */
package com.fast.controller.business;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.mapper.business.FastStaffMapper;
import com.fast.po.business.FastStaffSalaryPO;
import com.fast.service.business.FastStaffGroupAmortizationService;
import com.fast.service.business.FastStaffIncomeService;
import com.fast.service.business.FastStaffSalaryService;
import com.fast.service.business.FastStaffService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.poi.ExcelImportUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.github.pagehelper.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/staffSalary")
public class FastStaffSalaryController extends BaseController {

    @Autowired
    private FastStaffSalaryService fastStaffSalaryService;
    @Autowired
    private FastStaffIncomeService fastStaffIncomeService;
    @Autowired
    private FastStaffService fastStaffService;
    @Autowired
    private FastStaffMapper fastStaffMapper;
    @Autowired
    private FastStaffGroupAmortizationService fastStaffGroupAmortizationService;

    @ApiName(value = "business-查询列表", folder = {"business"})
    @ApiParamsIn({
            "month:1:int:月"
    })
    @ApiParamsOut({
            "results>>list>>month:月份202401",
            "results>>list>>monthStr:月份2024-01",
            "results>>list>>staffId:支出人id",
            "results>>list>>realName:支出人姓名",
            "results>>list>>salary:人力成本",
            "results>>list>>salaryOther:其他人力成本"
    })
    @PostMapping("/getFastStaffSalaryList")
    public ResultVO<?> getList(HttpServletRequest request, FastStaffSalaryPO params, PageVO pageVO) {
        if (params.getMonth() != null) {
            params.setMonth(params.getMonth());
        } else if (StringUtil.isNotEmpty(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format09(params.getMonthStr() + "-01")));
        } else {
            params.setMonth(DateUtil.format05Int(new Date()));
        }
        return fastStaffSalaryService.queryPageList(params, pageVO);
    }

    @ApiName(value = "business-查询单个详情", folder = {"business"})
    @PostMapping("/getFastStaffSalaryDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastStaffSalaryPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastStaffSalaryPO fastStaffSalary = fastStaffSalaryService.queryById(params);
        return ResultVO.success(fastStaffSalary);
    }

    @ApiName(value = "business-添加", folder = {"business"})
    @PostMapping("/insertFastStaffSalary")
    public ResultVO<?> insert(HttpServletRequest request, FastStaffSalaryPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastStaffSalaryService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-更新", folder = {"business"})
    @PostMapping("/updateFastStaffSalary")
    public ResultVO<?> update(HttpServletRequest request, FastStaffSalaryPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastStaffSalaryService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-支出与摊销-导入", folder = {"business"})
    @ApiParamsIn({
            "file:1:File:支出与摊销-导入的文件"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/importSalary")
    public ResultVO<?> importSalary(HttpServletRequest request, MultipartFile file) {
        SessionVO sessionVO = getSessionVO(request);
        if (file == null) {
            return ResultVO.error("请选择导入文件");
        }

        List<List<Object>> data = ExcelImportUtil.readExcel(file, 0, "月份");
        if (data.isEmpty() || data.size() == 1) {
            return ResultVO.error("数据为空");
        }
        try {
            Integer nowMonth = DateUtil.format05Int(DateUtil.getNowDate());
            Integer month = DateUtil.format05Int(DateUtil.format10(data.get(1).get(0).toString()));
            if (month > nowMonth) {
                return ResultVO.error("导入月不能大于当前月");
            }
            data.get(0).removeIf(item -> item == null || item.toString().isEmpty());
            log.info(data.get(0).size() + "");
            MethodVO methodVO;
            Date nowDate = DateUtil.getNowDate();
            int type = 0;
            if (data.get(0).size() > 4) {
                type = 2;
                methodVO = fastStaffGroupAmortizationService.importSalaryGroupAmortization(data, sessionVO);
            } else {
                type = 1;
                methodVO = fastStaffSalaryService.importStaffSalary(data, nowDate, sessionVO);
            }
            if (methodVO.getCode() == 0) {
                fastStaffIncomeService.syncStaffIncome(sessionVO, month, nowDate, type);
                return ResultVO.fromMethodVO(methodVO);
            } else {
                return ResultVO.error("数据错误");
            }
        } catch (Exception e) {
            return ResultVO.error("数据错误，请检查导入文件");
        }
    }
}
