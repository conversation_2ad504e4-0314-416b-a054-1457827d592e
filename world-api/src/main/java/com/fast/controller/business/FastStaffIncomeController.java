/*
 * Powered By fast.up
 */
package com.fast.controller.business;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.business.FastStaffSalaryMapper;
import com.fast.po.business.FastStaffIncomeAuditPO;
import com.fast.po.business.FastStaffIncomeDetailPO;
import com.fast.po.business.FastStaffIncomePO;
import com.fast.po.business.FastStaffIncomeTypePO;
import com.fast.po.user.FastUserPO;
import com.fast.service.aliyun.AliCdnService;
import com.fast.service.business.FastStaffIncomeAuditService;
import com.fast.service.business.FastStaffIncomeDetailService;
import com.fast.service.business.FastStaffIncomeService;
import com.fast.service.business.FastStaffIncomeTypeService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.user.FastUserService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.poi.ExcelImportUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastStaffIncome")
public class FastStaffIncomeController extends BaseController {

    @Autowired
    private FastStaffIncomeService fastStaffIncomeService;
    @Autowired
    private FastStaffIncomeDetailService fastStaffIncomeDetailService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastUserService fastUserService;
    @Autowired
    private FastStaffIncomeTypeService fastStaffIncomeTypeService;
    @Autowired
    private FastStaffIncomeAuditService fastStaffIncomeAuditService;
    @Autowired
    private FastStaffSalaryMapper fastStaffSalaryMapper;
    @Autowired
    private AliCdnService aliCdnService;

    @ApiName(value = "business-员工支出查询列表", folder = {"business"})
    @ApiParamsIn({
            "userId:0:string:员工id",
            "realNames:0:string:员工真实姓名，英文逗号分割",
            "incomeTypes:0:string:成本类型，英文逗号分割",
            "monthStr:0:string:月份，例：2023-12",
            "creatorIds:0:string:创建人id，英文逗号分割"
    })
    @ApiParamsOut({
            "results>>list>>title:名称，成本类型标题，顿号分割",
            "results>>list>>month:月份",
            "results>>list>>userId:用户id",
            "results>>list>>realName:用户姓名",
            "results>>list>>income:收入金额",
            "results>>list>>state:状态:0审核中；1通过；2未通过",
            "results>>list>>isAudit:是否需要审核，0不需要，1需要",
            "results>>list>>auditUser:审核人员，多个逗号分割",
            "results>>list>>auditTime:最后审核时间",
            "results>>list>>creatorId:创建人",
            "results>>list>>creatorName:创建人，用户名",
            "results>>list>>updatorId:更新人",
            "results>>list>>remark:备注",
            "results>>list>>auditRemark:审核说明",
            "results>>list>>createTime:创建时间",
            "results>>list>>updateTime:更新时间",
            "results>>summary>>incomeTotal:录入支出",
            "results>>summary>>incomeTotalPass:已入账",
            "results>>summary>>incomeTotalNotPass:审核不通过",
    })
    @PostMapping("/getFastStaffIncomeList")
    public ResultVO<?> getList(HttpServletRequest request, FastStaffIncomePO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getExportData() == 1) {
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastStaffIncomeService.exportQueryList(sessionVO, params);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return fastStaffIncomeService.queryPageList(params, pageVO);
    }


    @PostMapping("/getTemplateUrl")
    public ResultVO<?> getList(HttpServletRequest request) {
        String ryzc = aliCdnService.getAliCdnFullUrl("template/人员支出-导入模版.xlsx");
        String gzs = aliCdnService.getAliCdnFullUrl("template/工作室摊销-导入模版.xlsx");
        HashMap<String, String> result = new HashMap<>();
        result.put("ryzc", ryzc);
        result.put("gzs", gzs);
        return ResultVO.success(result);
    }

    @ApiName(value = "business-员工支出单个详情", folder = {"business"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "title:名称，成本类型标题，顿号分割",
            "month:月份",
            "userId:用户id",
            "realName:用户姓名",
            "income:收入金额",
            "state:状态:0审核中；1通过；2未通过",
            "isAudit:是否需要审核，0不需要，1需要",
            "auditUser:审核人员，多个逗号分割",
            "auditTime:最后审核时间",
            "creatorId:创建人",
            "creatorName:创建人，用户名",
            "detail:支出项列表[{\"exportData\": 0,\"id\": 6,\"income\": 1000.0000,\"incomeType\": 2,\"title\": \"工资\"},...]",
            "updatorId:更新人",
            "remark:备注",
            "auditRemark:审核说明",
            "createTime:创建时间",
            "updateTime:更新时间"
    })
    @PostMapping("/getFastStaffIncomeDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastStaffIncomePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastStaffIncomePO fastStaffIncome = fastStaffIncomeService.queryById(params);
        if (fastStaffIncome != null) {
            fastStaffIncome.setMonthStr(DateUtil.format10(DateUtil.format05(fastStaffIncome.getMonth().toString())));
            FastStaffIncomeDetailPO detailPO = new FastStaffIncomeDetailPO();
            detailPO.setStaffIncomeId(id);
            List<FastStaffIncomeDetailPO> detail = fastStaffIncomeDetailService.queryIncomeList(detailPO);
            fastStaffIncome.setDetail(detail);
        }
        return ResultVO.success(fastStaffIncome);
    }

    @ApiName(value = "business-员工支出添加", folder = {"business"})
    @ApiParamsIn({
            "------:1:str:本接口采取json格式上传参数",
            "monthStr:1:str:月份",
            "userId:1:str:用户id",
            "realName:1:str:用户姓名",
            "state:1:str:状态:0审核中；1通过；2未通过",
            "isAudit:1:str:是否需要审核，0不需要，1需要",
            "auditUser:1:str:审核人员，多个逗号分割，isAudit为0该字段为空，1为必填",
            "remark:0:str:备注",
            "detail:1:json:收入类型详情[{\"title\":\"工资\",\"incomeType\":1,\"income\":999}]"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insertFastStaffIncome")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody FastStaffIncomePO params) {
        FastStaffIncomeAuditPO fastStaffIncomeAudit = fastStaffIncomeAuditService.queryById(1);
        if (fastStaffIncomeAudit == null) {
            fastStaffIncomeAudit.setIsAudit(1);
        }
        if (StrUtil.isBlank(params.getMonthStr())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setMonth(DateUtil.format05Int(DateUtil.format10(params.getMonthStr())));
        params.setState(fastStaffIncomeAudit.getIsAudit() == 1 ? 0 : 1);
        params.setIsAudit(fastStaffIncomeAudit.getIsAudit());
        params.setAuditUser("");

        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        params.setCreatorName(sessionVO.getUserName());
        MethodVO methodVO = fastStaffIncomeService.insertDetail(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-员工支出更新", folder = {"business"})
    @ApiParamsIn({
            "------:1:str:本接口采取json格式上传参数",
            "encryptionId:Z6f2pP40lpVrb1WjuGFZgg",
            "monthStr:1:str:月份",
            "userId:1:str:用户id",
            "realName:1:str:用户姓名",
            "state:1:str:状态:0审核中；1通过；2未通过",
            "isAudit:1:str:是否需要审核，0不需要，1需要",
            "auditUser:1:str:审核人员，多个逗号分割，isAudit为0该字段为空，1为必填",
            "remark:0:str:备注",
            "detail:1:json:收入类型详情[{\"id\":1,\"title\":\"工资\",\"incomeType\":1,\"income\":999}]"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateFastStaffIncome")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody FastStaffIncomePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (!StrUtil.isBlank(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format10(params.getMonthStr())));
        }
        params.setId(id);

        FastStaffIncomePO fastStaffIncome = fastStaffIncomeService.queryById(params);
        if (fastStaffIncome == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (fastStaffIncome.getState() == 1) {
            return ResultVO.error("已审核通过，无法修改");
        }

        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastStaffIncomeService.updateDetail(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-员工支出-模版导出", folder = {"business"})
    @ApiParamsIn({
            "员工支出-模版导出"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/exportOptimist")
    public ResultVO<?> exportOptimist(HttpServletRequest request) {
        SessionVO sessionVO = getSessionVO(request);
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_DRAMA_DAY_DETAIL + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        FastUserPO fastUserPO = new FastUserPO();
        List<FastUserPO> list = fastUserService.queryList(fastUserPO);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        FastStaffIncomeTypePO incomeTypePO = new FastStaffIncomeTypePO();
        incomeTypePO.setSort("asc");
        List<FastStaffIncomeTypePO> typeList = fastStaffIncomeTypeService.queryList(incomeTypePO);
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        List<String> userName = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastUserPO cur : list) {
            if (StrUtil.isEmpty(cur.getRealName())) {
                continue;
            }
//            if (userName.contains(cur.getRealName())) {
//                continue;
//            }
            userName.add(cur.getRealName());

            List<Object> row = new ArrayList<>();// 导出的数据(一行)
            row.add(cur.getRealName());
            CollUtil.addNoRepeat(rowHeadNames, "优化师");
            row.add(cur.getId());
            CollUtil.addNoRepeat(rowHeadNames, "用户id");
            row.add(DateUtil.format05Int(DateUtil.addMonths(new Date(), -1)));
            CollUtil.addNoRepeat(rowHeadNames, "月份");
            for (FastStaffIncomeTypePO type : typeList) {
                row.add("");
                CollUtil.addNoRepeat(rowHeadNames, type.getTitle());
            }
            dataList.add(row);
        }
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error("您至少要选择2项导出字段");// 您选择的导出列至少要有2项
        }
        String title = "优化师支出-模版";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    @ApiName(value = "business-员工支出-导入", folder = {"business"})
    @ApiParamsIn({
            "file:1:File:员工支出，需导入的文件"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/importOptimist")
    public ResultVO<?> importOptimist(HttpServletRequest request, MultipartFile file) {
        SessionVO sessionVO = getSessionVO(request);
        if (file == null) {
            return ResultVO.error("请选择导入文件");
        }
        List<List<Object>> data = ExcelImportUtil.readExcel(file, 0, "优化师");

        FastStaffIncomeTypePO incomeTypePO = new FastStaffIncomeTypePO();
        incomeTypePO.setSort("asc");
        List<FastStaffIncomeTypePO> typeList = fastStaffIncomeTypeService.queryList(incomeTypePO);
        Map<Object, FastStaffIncomeTypePO> typeListMap = new HashMap<>();
        for (FastStaffIncomeTypePO type : typeList) {
            typeListMap.put(type.getTitle(), type);
        }
        FastStaffIncomeAuditPO fastStaffIncomeAudit = fastStaffIncomeAuditService.queryById(1);
        if (fastStaffIncomeAudit == null) {
            fastStaffIncomeAudit.setIsAudit(1);
        }

        List<FastStaffIncomePO> fastStaffIncomeList = new ArrayList<>();
        int size = 0;
        for (List<Object> val : data) {
            if (Objects.equals(val.get(0).toString(), "优化师")) {
                size = val.size();
                // 设置支出总数据
                for (int i = 3; i < size; i++) {
                    if (typeListMap.containsKey(val.get(i).toString())) {
                        typeListMap.put(i, typeListMap.get(val.get(i).toString()));
                    }
                }
                continue;
            }
            if (size == 0) {
                continue;
            }
            if (val.size() <= 3 || StrUtil.isEmpty(val.get(1)) || StrUtil.isEmpty(val.get(2))) {
                continue;
            }
            FastStaffIncomePO po = new FastStaffIncomePO();
            po.setState(fastStaffIncomeAudit.getIsAudit() == 1 ? 0 : 1);
            po.setIsAudit(fastStaffIncomeAudit.getIsAudit());
            po.setAuditUser("");
            po.setRealName(val.get(0).toString());
            po.setUserId(Integer.parseInt(val.get(1).toString()));
            po.setMonth(DateUtil.format05Int(DateUtil.format05(val.get(2).toString().replace("-", ""))));
            po.setRemark("");
            po.setCreatorId(sessionVO.getUserId());
            po.setCreatorName(sessionVO.getUserName());
            // 设置支出分类数据
            List<FastStaffIncomeDetailPO> detailList = new ArrayList<>();
            for (int i = 3; i < size; i++) {
                if (typeListMap.containsKey(i)) {
                    FastStaffIncomeDetailPO detailPO = new FastStaffIncomeDetailPO();
                    detailPO.setTitle(typeListMap.get(i).getTitle());
                    detailPO.setIncome(new BigDecimal(StrUtil.isEmpty(val.get(i)) ? "0" : val.get(i).toString()));
                    detailPO.setIncomeType(typeListMap.get(i).getId());
                    detailList.add(detailPO);
                }
            }
            po.setDetail(detailList);
            fastStaffIncomeList.add(po);
        }
        if (!fastStaffIncomeList.isEmpty()) {
            if (fastStaffIncomeService.insertBatchDetail(fastStaffIncomeList)) {
                return ResultVO.error("数据导入成功");
            } else {
                return ResultVO.error("数据导入失败");
            }
        } else {
            return ResultVO.error("数据错误");
        }
    }

    @ApiName(value = "business-员工支出-删除", folder = {"business"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/delFastStaffIncome")
    public ResultVO<?> delete(HttpServletRequest request, FastStaffIncomePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

        params.setId(id);
        FastStaffIncomePO fastStaffIncome = fastStaffIncomeService.queryById(params);
        if (fastStaffIncome == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (fastStaffIncome.getState() == 1) {
            return ResultVO.error("已审核通过，无法删除");
        }
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastStaffIncomeService.delete(params);
        if (methodVO.getCode() == 0) {
            fastStaffSalaryMapper.deleteByRealName(fastStaffIncome.getRealName());
        }
        return ResultVO.fromMethodVO(methodVO);
    }


    @ApiName(value = "business-员工支出审核", folder = {"business"})
    @ApiParamsIn({
            "encryptionId:Z6f2pP40lpVrb1WjuGFZgg",
            "state:1:str:状态:0审核中；1通过；2未通过",
            "auditRemark:1:str:审核说明"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/fastStaffIncomeAudit")
    public ResultVO<?> fastStaffIncomeAudit(HttpServletRequest request, FastStaffIncomePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getState() == null || (params.getState() != 1 && params.getState() != 2)) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

        params.setId(id);

        FastStaffIncomePO fastStaffIncome = fastStaffIncomeService.queryById(params);
        if (fastStaffIncome == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (fastStaffIncome.getState() != 0) {
            return ResultVO.error("该支出，已审核");
        }
        FastStaffIncomeAuditPO fastStaffIncomeAudit = fastStaffIncomeAuditService.queryById(1);
        if (fastStaffIncomeAudit == null) {
            return ResultVO.error("未设置审核人员");
        }
        if (fastStaffIncomeAudit.getIsAudit() != 1) {
            return ResultVO.error("无需审核");
        }
        if (StrUtil.isEmpty(fastStaffIncomeAudit.getAuditUser())) {
            return ResultVO.error("未设置审核人员");
        }
        SessionVO sessionVO = getSessionVO(request);
        String[] auditUser = fastStaffIncomeAudit.getAuditUser().split(",");
        if (!Arrays.asList(auditUser).contains(sessionVO.getUserId().toString())) {
            return ResultVO.error("暂无审核权限");
        }

        params.setAuditUser(sessionVO.getUserId().toString());
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastStaffIncomeService.updateAuditState(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-审核员-更新", folder = {"business"})
    @ApiParamsIn({
            "isAudit:1:int:状态，0不需要；1需要",
            "auditUser:1:str:审核人，多人逗号分割"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateAuditUser")
    public ResultVO<?> updateAuditUser(HttpServletRequest request, FastStaffIncomeAuditPO params) {
        if (params.getIsAudit() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getIsAudit() != 1) {
            params.setIsAudit(0);
        }
        if (params.getIsAudit() == 1 && isBlank(params.getAuditUser())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setId(1);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastStaffIncomeAuditService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-审核员-详情", folder = {"business"})
    @ApiParamsIn({
            "审核人员"
    })
    @ApiParamsOut({
            "isAudit:状态，0不需要；1需要",
            "auditUser:审核人ID，多人逗号分割"
    })
    @PostMapping("/getAuditUserDetail")
    public ResultVO<?> getAuditDetail(HttpServletRequest request, FastStaffIncomeAuditPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setId(1);
        FastStaffIncomeAuditPO audit = fastStaffIncomeAuditService.queryById(params);
        return ResultVO.success(audit);
    }
}
