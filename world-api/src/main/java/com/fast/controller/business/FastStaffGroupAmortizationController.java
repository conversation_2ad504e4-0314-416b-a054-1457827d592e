/*
 * Powered By fast.up
 */
package com.fast.controller.business;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.business.FastStaffGroupAmortizationPO;
import com.fast.service.business.FastStaffGroupAmortizationService;
import com.fast.service.business.FastStaffIncomeService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.github.pagehelper.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/salaryGroup")
public class FastStaffGroupAmortizationController extends BaseController {

    @Autowired
    private FastStaffGroupAmortizationService fastStaffGroupAmortizationService;
    @Autowired
    private FastStaffIncomeService fastStaffIncomeService;

    @ApiName(value = "business-查询列表", folder = {"business"})
    @ApiParamsIn({
            "month:1:int:月"
    })
    @ApiParamsOut({
            "results>>list>>month:月份202401",
            "results>>list>>monthStr:月份2024-01",
            "results>>list>>groupId:组id",
            "results>>list>>groupName:组名称",
            "results>>list>>manageCost:部门管理成本",
            "results>>list>>operationCost:运营人力成本",
            "results>>list>>technicalCost:技术人力成本",
            "results>>list>>materialCost:素材人力成本",
            "results>>list>>externalTechnicalCost:杭州产研团队费用",
            "results>>list>>externalMaterialCost:外部素材采购费用",
            "results>>list>>reimbursementCost:投放组人均报销费用",
            "results>>list>>companyManageCost:投放组人均公司管理摊销",
            "results>>list>>companyAdministrativeCost:投放组人均公司行政摊销"
    })
    @PostMapping("/getFastStaffGroupAmortizationList")
    public ResultVO<?> getList(HttpServletRequest request, FastStaffGroupAmortizationPO params, PageVO pageVO) {
        if (params.getMonth() != null) {
            params.setMonth(params.getMonth());
        } else if (StringUtil.isNotEmpty(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format09(params.getMonthStr() + "-01")));
        } else {
            params.setMonth(DateUtil.format05Int(new Date()));
        }
        return fastStaffGroupAmortizationService.queryPageList(params, pageVO);
    }

    @ApiName(value = "business-查询单个详情", folder = {"business"})
    @PostMapping("/getFastStaffGroupAmortizationDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastStaffGroupAmortizationPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        FastStaffGroupAmortizationPO fastStaffGroupAmortization = fastStaffGroupAmortizationService.queryById(params);
        return ResultVO.success(fastStaffGroupAmortization);
    }

    @ApiName(value = "business-添加", folder = {"business"})
    @PostMapping("/insertFastStaffGroupAmortization")
    public ResultVO<?> insert(HttpServletRequest request, FastStaffGroupAmortizationPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastStaffGroupAmortizationService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-更新", folder = {"business"})
    @PostMapping("/updateFastStaffGroupAmortization")
    public ResultVO<?> update(HttpServletRequest request, FastStaffGroupAmortizationPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastStaffGroupAmortizationService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
