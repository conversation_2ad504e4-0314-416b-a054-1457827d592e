/*
 * Powered By fast.up
 */
package com.fast.controller.business;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.business.FastStaffIncomeDetailPO;
import com.fast.service.business.FastStaffIncomeDetailService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastStaffIncomeDetail")
public class FastStaffIncomeDetailController extends BaseController {

    @Autowired
    private FastStaffIncomeDetailService fastStaffIncomeDetailService;

    @ApiName(value = "business-查询列表", folder = {"business"})
    @PostMapping("/getFastStaffIncomeDetailList")
    public ResultVO<?> getList(HttpServletRequest request, FastStaffIncomeDetailPO params, PageVO pageVO) {
        return fastStaffIncomeDetailService.queryPageList(params, pageVO);
    }

    @ApiName(value = "business-查询单个详情", folder = {"business"})
    @PostMapping("/getFastStaffIncomeDetailDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastStaffIncomeDetailPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastStaffIncomeDetailPO fastStaffIncomeDetail = fastStaffIncomeDetailService.queryById(params);
        return ResultVO.success(fastStaffIncomeDetail);
    }

    @ApiName(value = "business-添加", folder = {"business"})
    @PostMapping("/insertFastStaffIncomeDetail")
    public ResultVO<?> insert(HttpServletRequest request, FastStaffIncomeDetailPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastStaffIncomeDetailService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-更新", folder = {"business"})
    @PostMapping("/updateFastStaffIncomeDetail")
    public ResultVO<?> update(HttpServletRequest request, FastStaffIncomeDetailPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastStaffIncomeDetailService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
