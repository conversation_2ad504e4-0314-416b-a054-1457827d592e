/*
 * Powered By fast.up
 */
package com.fast.controller.business;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.business.FastStaffIncomeDetailPO;
import com.fast.po.business.FastStaffIncomeTypePO;
import com.fast.service.business.FastStaffIncomeDetailService;
import com.fast.service.business.FastStaffIncomeTypeService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastStaffIncomeType")
public class FastStaffIncomeTypeController extends BaseController {

    @Autowired
    private FastStaffIncomeTypeService fastStaffIncomeTypeService;
    @Autowired
    private FastStaffIncomeDetailService fastStaffIncomeDetailService;

    @ApiName(value = "business-支出类型-列表", folder = {"business"})
    @ApiParamsIn({
            "列表接口"
    })
    @ApiParamsOut({
            "results>>list>>title:支出名称",
            "results>>list>>remark:备注",
            "results>>list>>create_time:创建时间",
            "results>>list>>update_time:更新时间"
    })
    @PostMapping("/getFastStaffIncomeTypeList")
    public ResultVO<?> getList(HttpServletRequest request, FastStaffIncomeTypePO params, PageVO pageVO) {
        return fastStaffIncomeTypeService.queryPageList(params, pageVO);
    }

    @ApiName(value = "business-支出类型-详情", folder = {"business"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id"
    })
    @ApiParamsOut({
            "title:支出名称",
            "remark:备注",
            "create_time:创建时间",
            "update_time:更新时间"
    })
    @PostMapping("/getFastStaffIncomeTypeDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastStaffIncomeTypePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastStaffIncomeTypePO fastStaffIncomeType = fastStaffIncomeTypeService.queryById(params);
        return ResultVO.success(fastStaffIncomeType);
    }

    @ApiName(value = "business-支出类型-添加", folder = {"business"})
    @ApiParamsIn({
            "title:支出名称",
            "remark:备注"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insertFastStaffIncomeType")
    public ResultVO<?> insert(HttpServletRequest request, FastStaffIncomeTypePO params) {
        if (StrUtil.isEmpty(params.getTitle())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        SessionVO sessionVO = getSessionVO(request);
        FastStaffIncomeTypePO po = new FastStaffIncomeTypePO();
        po.setTitle(params.getTitle());
        if (fastStaffIncomeTypeService.queryOne(po) != null) {
            return ResultVO.error("该支出项已存在");
        }

        MethodVO methodVO = fastStaffIncomeTypeService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-支出类型-更新", folder = {"business"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
            "title:支出名称",
            "remark:备注"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateFastStaffIncomeType")
    public ResultVO<?> update(HttpServletRequest request, FastStaffIncomeTypePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

//        FastStaffIncomeTypePO po = new FastStaffIncomeTypePO();
//        po.setTitle(params.getTitle());
//        if (fastStaffIncomeTypeService.queryOne(po) != null) {
//            return ResultVO.error("该支出项已存在");
//        }

        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastStaffIncomeTypeService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-支出类型-删除", folder = {"business"})
    @ApiParamsIn({
            "encryptionId:1:str:加密id",
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/delFastStaffIncomeType")
    public ResultVO<?> delete(HttpServletRequest request, FastStaffIncomeTypePO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

        // 查询是否存在关联收入，如果存在则返回已关联收入，请检查
        FastStaffIncomeDetailPO detailPO = new FastStaffIncomeDetailPO();
        detailPO.setIncomeType(id);
        FastStaffIncomeDetailPO fastStaffIncomeDetail = fastStaffIncomeDetailService.queryOne(detailPO);
        if (fastStaffIncomeDetail != null) {
            return ResultVO.error("该支出类型已关联用户支出，无法删除");
        }

        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastStaffIncomeTypeService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
