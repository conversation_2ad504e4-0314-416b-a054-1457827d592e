/*
 * Powered By fast.up
 */
package com.fast.controller.business;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.business.FastStaffPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.business.FastStaffService;
import com.fast.service.user.FastUserService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastStaff")
public class FastStaffController extends BaseController {

    @Autowired
    private FastStaffService fastStaffService;
    @Autowired
    private FastUserService fastUserService;

    @ApiName(value = "business-支出人-查询列表", folder = {"business"})
    @ApiParamsIn({
            "参数:0:参数:参数"
    })
    @ApiParamsOut({
            "results>>list>>encryptionId:月份202401",
            "results>>list>>id:支出人id",
            "results>>list>>realName:真实姓名",
            "results>>list>>roleId:角色id",
            "results>>list>>roleName:角色名称",
            "results>>list>>state:状态",
            "results>>list>>remark:备注",
            "results>>list>>userNum:用户数",
            "results>>list>>createTime:创建时间",
            "results>>list>>updateTime:更新时间"
    })
    @PostMapping("/getFastStaffList")
    public ResultVO<?> getList(HttpServletRequest request, FastStaffPO params, PageVO pageVO) {
        return fastStaffService.queryPageList(params, pageVO);
    }

    @ApiName(value = "business-支出人-查询单个详情", folder = {"business"})
    @ApiParamsIn({
            "encryptionId:Z6f2pP40lpVrb1WjuGFZgg",
    })
    @ApiParamsOut({
            "encryptionId:加密id",
            "realName:真实姓名",
            "roleId:角色id",
            "roleName:角色名",
            "remark:月份"
    })
    @PostMapping("/getFastStaffDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastStaffPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastStaffPO fastStaff = fastStaffService.queryById(params);
        return ResultVO.success(fastStaff);
    }

    @ApiName(value = "business-支出人-添加", folder = {"business"})
    @ApiParamsIn({
            "realName:1:str:用户姓名",
            "roleId:角色id",
            "roleName:1:str:角色名",
            "remark:0:str:备注"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/insertFastStaff")
    public ResultVO<?> insert(HttpServletRequest request, FastStaffPO params) {
        if (params.getRealName() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        FastStaffPO po = new FastStaffPO();
        po.setRealName(params.getRealName());
        FastStaffPO fastStaff = fastStaffService.queryOne(po);
        if (fastStaff != null) {
            return ResultVO.error("支出人已存在");
        }
        FastUserPO fastUserPO = new FastUserPO();
        fastUserPO.setRealName(params.getRealName());
        FastUserPO fastUser = fastUserService.queryOne(fastUserPO);
        if (fastUser == null) {
            return ResultVO.error("系统管理人员中无该用户，请检查支出人姓名");
        }
        if (params.getRemark() == null) {
            params.setRemark("");
        }
        MethodVO methodVO = fastStaffService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-支出人-更新", folder = {"business"})
    @ApiParamsIn({
            "encryptionId:Z6f2pP40lpVrb1WjuGFZgg",
            "realName:1:str:用户姓名",
            "roleId:角色id",
            "roleName:1:str:角色名",
            "remark:0:str:备注"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/updateFastStaff")
    public ResultVO<?> update(HttpServletRequest request, FastStaffPO params) {
        log.info(toJSONString(params));
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

        FastStaffPO po = new FastStaffPO();
        po.setId(id);
        FastStaffPO fastStaff = fastStaffService.queryById(po);
        if (fastStaff == null) {
            return ResultVO.error("该记录无法获取");
        }

        FastStaffPO po1 = new FastStaffPO();
        po1.setRealName(params.getRealName());
        FastStaffPO fastStaff1 = fastStaffService.queryOne(po1);
        if (fastStaff1 != null && !Objects.equals(fastStaff1.getId(), fastStaff.getId())) {
            return ResultVO.error("支出人已存在");
        }

        FastUserPO fastUserPO = new FastUserPO();
        fastUserPO.setRealName(params.getRealName());
        FastUserPO fastUser = fastUserService.queryOne(fastUserPO);
        if (fastUser == null) {
            return ResultVO.error("系统管理人员中无该用户，请检查支出人姓名");
        }

        params.setId(id);
        MethodVO methodVO = fastStaffService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "business-支出人-删除", folder = {"business"})
    @ApiParamsIn({
            "encryptionId:Z6f2pP40lpVrb1WjuGFZgg"
    })
    @ApiParamsOut({
            "status:ok"
    })
    @PostMapping("/delFastStaff")
    public ResultVO<?> delFastStaff(HttpServletRequest request, FastStaffPO params) {
        if (isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

        FastStaffPO po = new FastStaffPO();
        po.setId(id);
        FastStaffPO fastStaff = fastStaffService.queryById(po);
        if (fastStaff == null) {
            return ResultVO.error("该记录无法获取");
        }

        params.setId(id);
        MethodVO methodVO = fastStaffService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
