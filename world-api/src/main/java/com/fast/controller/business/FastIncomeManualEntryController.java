/*
 * Powered By fast.up
 */
package com.fast.controller.business;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.ContentTypeEnum;
import com.fast.enums.audit.AuditTypeEnum;
import com.fast.mapper.audit.FastAuditFlowMapper;
import com.fast.mapper.audit.FastAuditProcessMapper;
import com.fast.mapper.business.FastIncomeManualEntryMapper;
import com.fast.mapper.common.FastCommonDictMapper;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.po.audit.FastAuditFlowPO;
import com.fast.po.audit.FastAuditProcessPO;
import com.fast.po.business.FastIncomeManualEntryPO;
import com.fast.po.common.FastCommonDictPO;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.setting.FastSettingIncomeTypePO;
import com.fast.service.business.FastIncomeManualEntryService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.setting.FastSettingIncomeTypeService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.poi.ExcelImportUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastIncomeManualEntry")
public class FastIncomeManualEntryController extends BaseController {

    @Autowired
    private FastIncomeManualEntryService fastIncomeManualEntryService;
    @Autowired
    private FastSettingIncomeTypeService fastSettingIncomeTypeService;
    @Autowired
    private FastIncomeManualEntryMapper fastIncomeManualEntryMapper;
    @Autowired
    private FastDramaMapper fastDramaMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastCommonDictMapper fastCommonDictMapper;
    @Autowired
    private FastAuditProcessMapper fastAuditProcessMapper;
    @Autowired
    private FastAuditFlowMapper fastAuditFlowMapper;

    @ApiName(value = "fastIncomeManualEntry-收入管理查询列表", folder = {"business"})
    @ApiParamsIn({
            "timeType:0:int:入账周期，1日 2周 3月",
            "incomeTypeId:0:int:收入类型表id",
            "creatorId:0:int:录入人id",
            "projectIds:0:str:CP项目id(多个逗号分隔)",
            "effectDateStr:0:string:时间yyyy-mm-dd或yyyy-mm",
            "effectDateStart:0:string:时间yyyy-mm-dd",
            "effectDateEnd:0:string:时间yyyy-mm-dd",
            "joinCpSettle:是否已参与CP结算 1是 0否",
            "auditTimeStr:操作入账的时间 yyyy-mm-dd - yyyy-mm-dd",
            "monthGroupId:按月入账id",
    })
    @ApiParamsOut(value = {
            "id:id",
            "incomeTypeId:收入类型表id",
            "effectDate:纳入收入日期-入账期-时间yyyyMMdd",
            "effectDateStr:纳入收入日期-入账期-时间yyyyMMdd",
            "income:收入金额",
            "dramaId:剧id",
            "dramaName:剧名称",
            "state:状态,0审核中；1已入账",
            "type:1收入",
            "timeType:入账周期，1日 2周 3月",
            "creatorId:创建人Id",
            "creatorName:创建人名称",
            "remark:备注",
            "createTime:创建时间",
            "title:收入类型名称",
            "effectMonthStr:纳入CP结算期",
            "isRenovate:是否翻新收入 1是 0否",
            "joinCpSettle:是否已参与CP结算 1是 0否",
            "auditTime:操作入账的时间",
    })
    @PostMapping("/getFastIncomeManualEntryList")
    public ResultVO<?> getList(HttpServletRequest request, FastIncomeManualEntryPO params, PageVO pageVO) {
        if (StrUtil.isNotEmpty(params.getEffectDateStr())) {
            String effectDate = params.getEffectDateStr().replaceAll("-|/", "");
            if (effectDate.matches("\\d+")) {
                params.setEffectDate(Integer.valueOf(effectDate));
            } else {
                return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
            }
        }
        if (StrUtil.isNotEmpty(params.getEffectMonthStr())) {
            String effectDate = params.getEffectMonthStr().replaceAll("-|/", "");
            if (effectDate.matches("\\d+")) {
                params.setEffectMonth(Integer.valueOf(effectDate));
            } else {
                return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
            }
        }
        if (StrUtil.isNotEmpty(params.getEffectDateStart())) {
            params.setEffectDateStart(params.getEffectDateStart().replaceAll("-|/", ""));
        }
        if (StrUtil.isNotEmpty(params.getEffectDateEnd())) {
            params.setEffectDateEnd(params.getEffectDateEnd().replaceAll("-|/", ""));
        }
        // 处理时间
        if (notEmpty(params.getAuditTimeStr())) {
            List<Date> date = DateUtil.getStartEndDate(params.getAuditTimeStr());
            params.setAuditTimeS(date.get(0));
            params.setAuditTimeE(date.get(1));
        }
        if (notEmpty(params.getProjectIds())) {
            FastDramaPO dramaQuery = new FastDramaPO();
            dramaQuery.setDelFlag(0);
            dramaQuery.setProjectIds(params.getProjectIds());
            List<Integer> dramaIds = fastDramaMapper.queryDramaIds(dramaQuery);
            if (CollUtil.isEmpty(dramaIds)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setDramaIds(StrUtil.join(dramaIds));
        }
        if (params.getExportData() == 1) {
            SessionVO sessionVO = getSessionVO(request);
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            fastIncomeManualEntryService.excelExport(params, sessionVO, key);
            RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            return ResultVO.success();
        } else {
            return fastIncomeManualEntryService.queryPageList(params, pageVO);
        }
    }

    @ApiName(value = "fastIncomeManualEntry-收入管理查询列表按月", folder = {"business"})
    @ApiParamsIn({
            "timeType:0:int:入账周期，1日 2周 3月",
            "incomeTypeId:0:int:收入类型表id",
            "creatorId:0:int:录入人id",
            "projectIds:0:str:CP项目id(多个逗号分隔)",
            "effectDateStr:0:string:时间yyyy-mm-dd或yyyy-mm",
            "effectDateStart:0:string:时间yyyy-mm-dd",
            "effectDateEnd:0:string:时间yyyy-mm-dd",
            "joinCpSettle:是否已参与CP结算 1是 0否",
            "auditTimeStr:操作入账的时间 yyyy-mm-dd - yyyy-mm-dd",
            "needAudit:0:str:1需要审批 0不需要审批",
            "incomeChannelIds:0:str:分发平台ids",
            "principalNameLike:0:str:分发平台主体",
            "thirdAccountNameLike:0:str:分发平台账号",

    })
    @ApiParamsOut(value = {
            "id:id",
            "incomeTypeId:收入类型表id",
            "effectDate:纳入收入日期-入账期-时间yyyyMMdd",
            "effectDateStr:纳入收入日期-入账期-时间yyyyMMdd",
            "income:收入金额",
            "dramaId:剧id",
            "dramaName:剧名称",
            "state:状态,0待入账；1已入账 2审核中 3审核驳回",
            "type:1收入",
            "timeType:入账周期，1日 2周 3月",
            "creatorId:创建人Id",
            "creatorName:创建人名称",
            "remark:备注",
            "createTime:创建时间",
            "title:收入类型名称",
            "effectMonthStr:纳入CP结算期",
            "isRenovate:是否翻新收入 1是 0否",
            "joinCpSettle:是否已参与CP结算 1是 0否",
            "auditTime:操作入账的时间",
            "monthGroupId:按月列表id",
            "auditUserId:当前审核人用户id",
            "sourceIncome:收入总金额",
            "auditUserFlag:是否存在审批人列表中"
    })
    @PostMapping("/getFastIncomeManualEntryMonthList")
    public ResultVO<?> getMonthList(HttpServletRequest request, FastIncomeManualEntryPO params, PageVO pageVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (Objects.nonNull(params.getNeedAudit())) {
            params.setAuditUserId(sessionVO.getUserId());
        }
        if (StrUtil.isNotEmpty(params.getEffectDateStr())) {
            String effectDate = params.getEffectDateStr().replaceAll("-|/", "");
            if (effectDate.matches("\\d+")) {
                params.setEffectDate(Integer.valueOf(effectDate));
            } else {
                return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
            }
        }
        if (StrUtil.isNotEmpty(params.getEffectMonthStr())) {
            String effectDate = params.getEffectMonthStr().replaceAll("-|/", "");
            if (effectDate.matches("\\d+")) {
                params.setEffectMonth(Integer.valueOf(effectDate));
            } else {
                return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
            }
        }
        if (params.getExportData() == 1) {
            return fastIncomeManualEntryService.exportMonthList(sessionVO, params);
        }

        return fastIncomeManualEntryService.getMonthList(sessionVO, params, pageVO);
    }

    @ApiName(value = "fastIncomeManualEntry-收入管理查询单个详情", folder = {"business"})
    @ApiParamsIn({
            "id:1:int:数据id",
    })
    @ApiParamsOut(value = {
            "id:id",
            "incomeTypeId:收入类型表id",
            "effectDate:时间yyyymmdd",
            "income:收入金额",
            "dramaId:剧id",
            "dramaName:剧名称",
            "state:状态,0审核中；1已入账",
            "type:1收入",
            "timeType:入账周期，1日 2周 3月",
            "creatorId:创建人Id",
            "creatorName:创建人名称",
            "remark:备注",
            "createTime:创建时间",
            "title:收入类型名称"
    })
    @PostMapping("/getFastIncomeManualEntryDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastIncomeManualEntryPO params) {
        if (params.getId() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        FastIncomeManualEntryPO fastIncomeManualEntry = fastIncomeManualEntryService.queryJoinTypeById(params);
        return ResultVO.success(fastIncomeManualEntry);
    }

    @ApiName(value = "fastIncomeManualEntry-收入管理添加", folder = {"business"})
    @ApiParamsIn({
            "incomeTypeId:1:int:收入类型表id",
            "effectDateStr:1:int:时间yyyy-mm-dd",
            "income:1:int:收入金额",
            "dramaId:1:int:剧id",
            "dramaName:1:int:剧名称",
            "state:1:int:状态,0审核中；1已入账",
            "remark:1:int:备注",
    })
    @ApiParamsOut(value = {
            "code:0",
            "state:ok",
            "success:true"
    })
    @PostMapping("/insertFastIncomeManualEntry")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody List<FastIncomeManualEntryPO> params) {
        Map<Integer, FastSettingIncomeTypePO> type = new HashMap<>();
        List<FastIncomeManualEntryPO> insertData = new ArrayList<>();
        Date nowTime = DateUtil.getNowDate();
        SessionVO sessionVO = getSessionVO(request);
        for (FastIncomeManualEntryPO cur : params) {
            if (cur.getIncome() == null || cur.getIncome().compareTo(BigDecimal.ZERO) < 0) {
                continue;
            }
            if (!type.containsKey(cur.getIncomeTypeId())) {
                type.put(cur.getIncomeTypeId(), fastSettingIncomeTypeService.queryById(cur.getIncomeTypeId()));
            }
            FastSettingIncomeTypePO incomeType = type.get(cur.getIncomeTypeId());
            if (incomeType == null) {
                continue;
            }
            if (incomeType.getTimeType() == 3) {
                cur.setEffectDate(DateUtil.format05Int(DateUtil.format10(cur.getEffectDateStr())));
                cur.setEffectMonth(doDate2Month(nowTime));
            } else {
                cur.setEffectDate(DateUtil.format06Int(DateUtil.format09(cur.getEffectDateStr())));
                cur.setEffectMonth(0);
            }
            cur.setIsRenovate(0);
            cur.setTimeType(incomeType.getTimeType());
            cur.setCreatorId(sessionVO.getUserId());
            cur.setCreatorName(sessionVO.getUserName());
            if (cur.getDramaId() == null) {
                cur.setDramaId(0);
                cur.setDramaName("");
            } else {
                FastDramaPO dramaPO = fastDramaMapper.queryById(cur.getDramaId());
                if (dramaPO != null && dramaPO.getDramaName() != null) {
                    cur.setDramaName(dramaPO.getDramaName());
                } else {
                    cur.setDramaName("");
                }
            }
            cur.setAuditUser("");
            cur.setCreateTime(nowTime);
            cur.setState(0);
            insertData.add(cur);
        }
        if (!insertData.isEmpty()) {
            MethodVO methodVO = fastIncomeManualEntryService.insertBatch(insertData);
            return ResultVO.fromMethodVO(methodVO);
        } else {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
    }

    /**
     * 转换结算日期为结算月份,15日(含)之前的算上月,15日之后的算本月
     *
     * @return yyyyMM
     */
    private static int doDate2Month(Date nowTime) {
        Date today = DateUtil.beginOfDay(nowTime);
        int day = DateUtil.getCurrentDay(today);
        if (day > 16) {
            return DateUtil.format05Int(today);
        } else {
            return DateUtil.format05Int(DateUtil.addMonths(today, -1));
        }
    }

    @ApiName(value = "fastIncomeManualEntry-收入管理更新-确认入账", folder = {"business"})
    @ApiParamsIn({
            "id:1:int:数据id",
            "incomeTypeId:1:int:收入类型表id",
            "effectDateStr:1:int:时间yyyy-mm-dd",
            "income:1:int:收入金额",
            "dramaId:1:int:剧id",
            "dramaName:1:int:剧名称",
            "state:1:int:状态,0审核中；1已入账",
            "remark:1:int:备注",
            "statementRatio:1:str:结算系数",
    })
    @ApiParamsOut(value = {
            "code:0",
            "state:ok",
            "success:true"
    })
    @PostMapping("/updateFastIncomeManualEntry")
    public ResultVO<?> update(HttpServletRequest request, FastIncomeManualEntryPO params) {
        if (params.getId() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        FastIncomeManualEntryPO fastIncomeManualEntry = fastIncomeManualEntryService.queryById(params);

        if (fastIncomeManualEntry == null) {
            return ResultVO.error(StaticCode.ERROR, "无法获取该条数据");
        }
        if (fastIncomeManualEntry.getState() == 1) {
            return ResultVO.error(StaticCode.ERROR, "该数据已入账无法修改");
        }
        // 查询审核记录
        FastAuditProcessPO currentAuditProcess = null;
        if (StrUtil.isNotBlank(fastIncomeManualEntry.getMonthGroupId())) {
            FastAuditProcessPO processQuery = new FastAuditProcessPO();
            processQuery.setDataId(fastIncomeManualEntry.getMonthGroupId());
            processQuery.setAuditType("month_income");
            currentAuditProcess = fastAuditProcessMapper.queryOne(processQuery);
        }
        if (fastIncomeManualEntry.getTimeType() == 3) {
            if (Objects.isNull(params.getStatementRatio())) {
                return ResultVO.error(StaticCode.ERROR, "请输入收入系数");
            }
            if (Objects.nonNull(currentAuditProcess) && currentAuditProcess.getState() != 2) {
                return ResultVO.error(StaticCode.ERROR, "审核通过后才可入账");
            }
            params.setIncome(params.getStatementRatio().multiply(fastIncomeManualEntry.getSourceIncome()).setScale(2, RoundingMode.HALF_UP));
        }

        Date nowDate = DateUtil.getNowDate();
        SessionVO sessionVO = getSessionVO(request);
        if (params.getState() == 1) {
            params.setAuditUser(sessionVO.getUserName());
            params.setAuditTime(nowDate);
            params.setUpdateTime(nowDate);
        }
        if (StrUtil.isNotEmpty(params.getEffectDateStr())) {
            String effectDate = params.getEffectDateStr().replaceAll("-|/", "");
            if (effectDate.matches("\\d+")) {
                params.setEffectDate(Integer.valueOf(effectDate));
            } else {
                return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
            }
        }
        if (params.getDramaId() != null) {
            FastDramaPO dramaPO = fastDramaMapper.queryById(params.getDramaId());
            if (dramaPO != null && dramaPO.getDramaName() != null) {
                params.setDramaName(dramaPO.getDramaName());
            } else {
                params.setDramaName("");
            }
        }
        return ResultVO.fromMethodVO(fastIncomeManualEntryService.update(params));
    }

    @ApiName(value = "fastIncomeManualEntry-收入管理删除", folder = {"business"})
    @ApiParamsIn({
            "id:1:int:数据id",
    })
    @ApiParamsOut(value = {
            "code:0",
            "state:ok",
            "success:true"
    })
    @PostMapping("/deleteFastSettingIncomeType")
    public ResultVO<?> delete(HttpServletRequest request, FastIncomeManualEntryPO params) {
        if (params.getId() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        MethodVO methodVO = fastIncomeManualEntryService.delete(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "fastIncomeManualEntry-模版导出", folder = {"business"})
    @ApiParamsIn({
            "timeType:1:int:1日模版，2周模版，3月模版"
    })
    @ApiParamsOut(value = {
            "code:0",
            "message:url地址",
            "state:ok",
            "success:true"
    })
    @PostMapping("/incomeTemplateExport")
    public ResultVO<?> incomeTemplateExport(HttpServletRequest request, Integer timeType) {
        SessionVO sessionVO = getSessionVO(request);
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_RATE + DateUtil.getNowTime06Str() + "_" + sessionVO.getUserId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        Integer cycle;
        if (timeType == 1) {
            cycle = DateUtil.format06Int(DateUtil.getYesterdayDate());
        } else if (timeType == 3) {
            cycle = DateUtil.format05Int(DateUtil.getLastMonthStartDay());
        } else {
            return ResultVO.error("参数错误");
        }

        FastSettingIncomeTypePO typePO = new FastSettingIncomeTypePO();
        typePO.setTimeType(timeType);
        List<FastSettingIncomeTypePO> typeList = fastSettingIncomeTypeService.queryList(typePO);
        // log.info(toJSONString(typeList));
        // 表头数据
        List<List<Object>> dataList = new ArrayList<>();
        List<String> rowHeadNames = new ArrayList<>();
        for (FastSettingIncomeTypePO cur : typeList) {
            List<Object> row = new ArrayList<>();// 导出的数据
            row.add(String.valueOf(cycle));
            CollUtil.addNoRepeat(rowHeadNames, timeType == 1 ? "周期" : "入账期月份");

            if (timeType == 3) {
                row.add(DateUtil.format05(DateUtil.getNowDate()));
                CollUtil.addNoRepeat(rowHeadNames, "纳入cp结算月份");
            }

            row.add("");
            CollUtil.addNoRepeat(rowHeadNames, "分发平台");
            row.add(cur.getTitle());
            CollUtil.addNoRepeat(rowHeadNames, "收入项目");

            row.add("");
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.DRAMA.name + "名称");

            row.add("0");
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.DRAMA.name + "ID");

            row.add("0");
            CollUtil.addNoRepeat(rowHeadNames, "收入金额");
            // row.add("1");
            // CollUtil.addNoRepeat(rowHeadNames, "参与结算系数");

            if (timeType == 3) {
                row.add("否");
                CollUtil.addNoRepeat(rowHeadNames, "是否翻新剧收入(是/否)");
            }

            row.add("");
            CollUtil.addNoRepeat(rowHeadNames, "分发平台主体");

            if (timeType == 3) {
                row.add("");
                CollUtil.addNoRepeat(rowHeadNames, "分发平台账号");
            }

            dataList.add(row);
        }
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error("导出格式错误");// 您选择的导出列至少要有2项
        }

        String title = timeType == 1 ? "按日-入账模版" : "按月-入账模版";
        String fileUrl = excelExportAsyncService.exportSync(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success(fileUrl);
    }

    @ApiName(value = "fastIncomeManualEntry-导入收入", folder = {"business"})
    @ApiParamsIn({"file:1:File:需导入的文件"})
    @ApiParamsOut({"status:ok"})
    @PostMapping("/importIncome")
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> importIncome(HttpServletRequest request, MultipartFile file) {
        SessionVO sessionVO = getSessionVO(request);
        if (file == null) {
            return ResultVO.error("请选择导入文件");
        }
        FastSettingIncomeTypePO typePO = new FastSettingIncomeTypePO();
        typePO.setTimeType(1);
        Map<String, FastSettingIncomeTypePO> typeMap = new HashMap<>();

        List<List<Object>> data = ExcelImportUtil.readExcel(file, 0, "周期", "入账期月份");
        int size = 0;

        boolean isMonth = false;
        Date nowTime = DateUtil.getNowDate();
        List<FastIncomeManualEntryPO> updateData = new ArrayList<>();
        List<FastIncomeManualEntryPO> insertData = new ArrayList<>();
        Map<String, FastAuditProcessPO> auditProcessPOMap = new HashMap<>();
        Map<String, FastAuditProcessPO> auditUpdateProcessPOMap = new HashMap<>();
        FastCommonDictPO fastCommonDictPO = new FastCommonDictPO();
        fastCommonDictPO.setDelFlag(false);
        fastCommonDictPO.setCode("third_platform");
        Map<String, Integer> channelMap = fastCommonDictMapper.queryList(fastCommonDictPO).stream().collect(Collectors.toMap(FastCommonDictPO::getValue, FastCommonDictPO::getId, (i1, i2) -> i1));
        for (List<Object> val : data) {
            if (StrUtil.equalsAny(val.get(0).toString(), "周期", "入账期月份")) {
                size = val.size();
                if ("入账期月份".equals(val.get(0))) {
                    isMonth = true;
                }
                if (isMonth) {
                    typePO.setTimeType(3);
                }
                List<FastSettingIncomeTypePO> typeList = fastSettingIncomeTypeService.queryList(typePO);

                for (FastSettingIncomeTypePO cur : typeList) {
                    typeMap.put(cur.getTitle(), cur);
                }
                continue;
            }
            // 按月新增了结算月份 第二列换成第10列
            Object remove = val.remove(1);
            val.add(remove);
            if (size == 0) {
                continue;
            }
            if (StrUtil.isEmpty(val.get(0))) {
                continue;
            }
            if (StrUtil.isEmpty(val.get(0)) || StrUtil.isEmpty(val.get(7)) || StrUtil.isEmpty(val.get(2)) || StrUtil.isEmpty(val.get(5)) || StrUtil.isEmpty(val.get(6)) || isMonth && StrUtil.isEmpty(val.get(9))) {
                return ResultVO.error("导入失败，数据为空");
            }
            if (!typeMap.containsKey(val.get(2))) {
                return ResultVO.error("导入失败，收入项目错误");
            }
            FastSettingIncomeTypePO incomeType = typeMap.get(val.get(2));
            FastIncomeManualEntryPO po = new FastIncomeManualEntryPO();
            po.setEffectMonth(doDate2Month(nowTime));
            BigDecimal statementRatio = null;
            String principalName = "";

            // 项目渠道必填
            String channelName = (String) val.get(1);
            if (StrUtil.isNotEmpty(channelName)) {
                Integer i = channelMap.get(channelName);
                if (Objects.isNull(i)) {
                    return ResultVO.error("分发平台不存在");
                }
                po.setIncomeChannel(i);
            }
            String thirdAccountName = "";

            if (incomeType.getTimeType() == 3) {
                if (val.size() != 10) {
                    return ResultVO.error("导入失败，数据列数不合法");
                }
                String isRenovate = String.valueOf(val.get(6));
                po.setIsRenovate("是".equals(isRenovate) ? 1 : 0);
                if (po.getIsRenovate() == 1 && incomeType.getTitle().contains("快手")) {
                    return ResultVO.error("导入失败，快手不支持翻新剧");
                }
                String dateStr = val.get(0).toString().replace(".0", "");
                if (dateStr.length() != 6) {
                    return ResultVO.error("导入失败，按月周期数不合法");
                }
                po.setEffectMonth(Integer.valueOf((String) val.get(9)));
                po.setEffectDate(DateUtil.format05Int(DateUtil.format05(dateStr)));
                // if(po.getEffectDate() > DateUtil.format05Int(DateUtil.getNowDate())) {
                //     return ResultVO.error("导入失败，导入周期错误");
                // }
                statementRatio = null;
                principalName = (String) val.get(7);
                thirdAccountName = (String) val.get(8);
            } else {
                if (val.size() != 8) {
                    return ResultVO.error("导入失败，数据列数不合法");
                }
                String dateStr = val.get(0).toString().replace(".0", "");
                if (dateStr.length() != 8) {
                    return ResultVO.error("导入失败，按日周期数不合法");
                }
                po.setIsRenovate(0);
                po.setEffectDate(DateUtil.format06Int(DateUtil.format06(dateStr)));
                // if(po.getEffectDate() > DateUtil.format06Int(DateUtil.getNowDate())) {
                //     return ResultVO.error("导入失败，导入周期错误");
                // }
                statementRatio = BigDecimal.valueOf(Double.parseDouble((String) val.get(6)));
                principalName = (String) val.get(7);
            }
            if (StrUtil.isBlank(principalName)) {
                return ResultVO.error("分发平台主体必填!");
            }
            Integer dramaId = StrUtil.str2Integer(val.get(4).toString().replace(".0", ""));
            po.setDramaId(dramaId == null ? 0 : dramaId);
            po.setIncomeTypeId(incomeType.getId());
            po.setPrincipalName(principalName);
            po.setThirdAccountName(thirdAccountName);
            FastIncomeManualEntryPO fastIncomeManualEntry = fastIncomeManualEntryService.queryOne(po);
            po.setStatementRatio(statementRatio);
            po.setState(0);
            po.setSourceIncome(new BigDecimal(val.get(5).toString()));
            if (po.getDramaId() > 0) {
                if (StrUtil.isEmpty(val.get(3).toString())) {
                    FastDramaPO dramaPO = fastDramaMapper.queryById(po.getDramaId());
                    if (dramaPO != null && dramaPO.getDramaName() != null) {
                        po.setDramaName(dramaPO.getDramaName());
                    } else {
                        po.setDramaName("");
                    }
                } else {
                    po.setDramaName(val.get(3).toString());
                }
            } else {
                po.setDramaName("");
            }
            po.setIncome(po.getSourceIncome());
            if (incomeType.getTimeType() == 3) {
                // 校验groupId下记录是否存在待入账或已入账数据

                // 当前记录是否审核通过 审核通过不可导入
                po.setMonthGroupId(Md5Util.getMD5("" + po.getIncomeTypeId() + po.getEffectMonth() + po.getEffectDate() + po.getPrincipalName() + ObjectUtils.defaultIfNull(po.getThirdAccountName(), "") + po.getIncomeChannel()));

                FastAuditProcessPO processQuery = new FastAuditProcessPO();
                processQuery.setDataId(po.getMonthGroupId());
                processQuery.setAuditType(AuditTypeEnum.MONTH_INCOME.getType());
                FastAuditProcessPO fastAuditProcessPO = fastAuditProcessMapper.queryOne(processQuery);
                // 存在审核记录 并且已经审核通过 不可导入
                if (Objects.nonNull(fastAuditProcessPO) && fastAuditProcessPO.getState() == 2) {
                    return ResultVO.error("导入失败，导入数据中存在已审核通过数据，请勿重复导入");
                } else {
                    // 未审核通过 审核记录设置为待审核
                    if (Objects.isNull(fastAuditProcessPO)) {
                        // 存在审核流程 插入待审核记录
                        FastAuditFlowPO flowQuery = new FastAuditFlowPO();
                        flowQuery.setDelFlag(0);
                        flowQuery.setAuditType(AuditTypeEnum.MONTH_INCOME.getType());
                        FastAuditFlowPO fastAuditFlowPO = fastAuditFlowMapper.queryOne(flowQuery);
                        if (Objects.nonNull(fastAuditFlowPO)) {
                            // 插入审核记录
                            FastAuditProcessPO saveProcess = new FastAuditProcessPO();
                            saveProcess.setAuditType(AuditTypeEnum.MONTH_INCOME.getType());
                            saveProcess.setDataId(po.getMonthGroupId());
                            saveProcess.setState(1);
                            saveProcess.setLevel(1);
                            saveProcess.setCreatorId(sessionVO.getUserId());
                            saveProcess.setCreateTime(DateUtil.getNowDate());
                            auditProcessPOMap.put(po.getMonthGroupId(), saveProcess);
                        }
                    } else {
                        // 更新审核记录为待审核
                        FastAuditFlowPO flowQuery = new FastAuditFlowPO();
                        flowQuery.setDelFlag(0);
                        flowQuery.setAuditType(AuditTypeEnum.MONTH_INCOME.getType());
                        FastAuditFlowPO fastAuditFlowPO = fastAuditFlowMapper.queryOne(flowQuery);
                        if (Objects.nonNull(fastAuditFlowPO)) {
                            // 插入审核记录
                            FastAuditProcessPO updateProcess = new FastAuditProcessPO();
                            updateProcess.setId(fastAuditProcessPO.getId());
                            updateProcess.setLevel(1);
                            updateProcess.setState(1);
                            updateProcess.setUpdatorId(sessionVO.getUserId());
                            updateProcess.setUpdateTime(DateUtil.getNowDate());
                            auditUpdateProcessPOMap.put(po.getMonthGroupId(), updateProcess);
                        }
                    }

                }
            }
            if (fastIncomeManualEntry != null) {
                if (fastIncomeManualEntry.getState() == 1) {
                    return ResultVO.error("导入失败，导入数据中存在已入账数据，请勿重复导入");
                }
                po.setId(fastIncomeManualEntry.getId());
                po.setUpdateTime(nowTime);
                // 不更新groupId
                po.setMonthGroupId(null);
                updateData.add(po);
            } else {
                po.setIncomeTypeId(incomeType.getId());
                po.setTimeType(incomeType.getTimeType());
                po.setCreatorId(sessionVO.getUserId());
                po.setCreatorName(sessionVO.getUserName());
                po.setAuditUser("");
                po.setCreateTime(nowTime);
                insertData.add(po);
            }
        }

        int res1 = 0;
        int res2 = 0;
        if (!insertData.isEmpty()) {
            res1 = fastIncomeManualEntryMapper.insertBatch(insertData);
        }
        if (!updateData.isEmpty()) {
            res2 = fastIncomeManualEntryMapper.updateBatch(updateData);
        }
        if (CollUtil.isNotEmpty(auditProcessPOMap.values())) {
            Collection<FastAuditProcessPO> values = auditProcessPOMap.values();
            int i = fastAuditProcessMapper.insertBatch((new ArrayList<>(values)));
        }
        if (CollUtil.isNotEmpty(auditUpdateProcessPOMap.values())) {
            for (FastAuditProcessPO value : auditUpdateProcessPOMap.values()) {
                fastAuditProcessMapper.updateById(value);
            }
        }

        String msg = "";
        if (res1 > 0 || res2 > 0) {
            msg += "导入成功，";
            msg += res1 > 0 ? "插入" + res1 + "条记录；" : "";
            msg += res2 > 0 ? "相同记录更新成功。" : "";
            return ResultVO.success(msg);
        } else {
            return ResultVO.error("导入失败");
        }
    }

    @ApiName(value = "fastIncomeManualEntry-更新状态根据创建时间", folder = {"business"})
    @ApiParamsIn({
            "createTimeStr:1:string:时间yyyy-mm-dd",
            "state:1:int:状态1通过，0不通过"
    })
    @ApiParamsOut(value = {
            "code:0",
            "state:ok",
            "success:true"
    })
    @PostMapping("/updateStateByDate")
    public ResultVO<?> updateStateByDate(HttpServletRequest request, FastIncomeManualEntryPO params) {
        if (params.getCreateTimeStr() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        if (params.getState() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Date nowDate = DateUtil.getNowDate();
        SessionVO sessionVO = getSessionVO(request);

        params.setCreateTime(DateUtil.format09(params.getCreateTimeStr()));
        FastIncomeManualEntryPO update = new FastIncomeManualEntryPO();
        update.setCreateTimeStart(DateUtil.getDayStartDate(params.getCreateTime()));
        update.setCreateTimeEnd(DateUtil.getDayEndDate(params.getCreateTime()));
        update.setState(params.getState() == 1 ? 0 : 1);

        if (fastIncomeManualEntryService.queryCount(update) == 0) {
            return ResultVO.error(StaticCode.ERROR, "该录入日期无待确认数据！");
        }
        update.setState(params.getState());
        update.setAuditUser(sessionVO.getUserName());
        update.setAuditTime(nowDate);

        if (StrUtil.isBlank(params.getMonthGroupId())) {
            update.setTimeType(1);
            MethodVO methodVO = fastIncomeManualEntryService.updateStateByCreateTime(update);
            return ResultVO.fromMethodVO(methodVO);
        } else {
            // 按月入账
            FastIncomeManualEntryPO monthParam = new FastIncomeManualEntryPO();
            monthParam.setCreateTimeStart(update.getCreateTimeStart());
            monthParam.setEffectDateEnd(update.getEffectDateEnd());
            monthParam.setMonthGroupId(params.getMonthGroupId());
            monthParam.setStatementRatio(params.getStatementRatio().setScale(2, RoundingMode.HALF_UP));
            return ResultVO.fromMethodVO(fastIncomeManualEntryService.monthBatchConfirm(sessionVO, monthParam));
        }

    }


    @ApiName(value = "fastIncomeManualEntry-按月根据入账时间批量审核", folder = {"business"})
    @ApiParamsIn({
            "createTimeStr:1:string:时间yyyy-mm-dd",
            "state:1:int:状态1通过，0不通过"
    })
    @ApiParamsOut(value = {
            "code:0",
            "state:ok",
            "success:true"
    })
    @PostMapping("/batchAuditUpdateStateByDate")
    public ResultVO<?> batchAuditUpdateStateByDate(HttpServletRequest request, FastIncomeManualEntryPO params) {
        if (params.getCreateTimeStr() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        if (params.getState() == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Date nowDate = DateUtil.getNowDate();
        SessionVO sessionVO = getSessionVO(request);

        params.setNeedAudit(1);
        params.setAuditUserId(sessionVO.getUserId());

        params.setCreateTime(DateUtil.format09(params.getCreateTimeStr()));
        FastIncomeManualEntryPO update = new FastIncomeManualEntryPO();
        update.setCreateTimeStart(DateUtil.getDayStartDate(params.getCreateTime()));
        update.setCreateTimeEnd(DateUtil.getDayEndDate(params.getCreateTime()));
        update.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastIncomeManualEntryService.batchAuditUpdateStateByDate(update);
        return ResultVO.fromMethodVO(methodVO);
    }


    @RequestMapping("/nologin/updateGroupId")
    public ResultVO<?> updateGroupId(HttpServletRequest request) {
        fastIncomeManualEntryService.updateGroupId();
        return ResultVO.success();
    }
}
