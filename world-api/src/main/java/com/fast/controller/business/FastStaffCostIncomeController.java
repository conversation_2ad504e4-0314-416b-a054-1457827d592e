/*
 * Powered By fast.up
 */
package com.fast.controller.business;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticVar;
import com.fast.mapper.business.FastStaffRetailRechargeDayMapper;
import com.fast.mapper.business.FastStaffRetailRechargeMonthMapper;
import com.fast.po.business.FastStaffCostIncomeDayPO;
import com.fast.po.business.FastStaffCostIncomeMonthPO;
import com.fast.po.setting.FastSettingSystemPO;
import com.fast.service.business.FastStaffCostIncomeDayService;
import com.fast.service.business.FastStaffCostIncomeMonthService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.setting.FastSettingSystemService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.github.pagehelper.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastStaffCostIncome")
public class FastStaffCostIncomeController extends BaseController {

    @Autowired
    private FastStaffCostIncomeDayService fastStaffCostIncomeDayService;
    @Autowired
    private FastStaffCostIncomeMonthService fastStaffCostIncomeMonthService;
    @Autowired
    private FastStaffRetailRechargeDayMapper fastStaffRetailRechargeDayMapper;
    @Autowired
    private FastStaffRetailRechargeMonthMapper fastStaffRetailRechargeMonthMapper;
    @Autowired
    private FastSettingSystemService fastSettingSystemService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    @ApiName(value = "business-概览数据", folder = {"business"})
    @ApiParamsIn({
            "概览数据",
            "exportData:0:int:1导出",
            "searchKey:0:int:exportData为1则必填，today今日、yesterday昨日、month月数据"
    })
    @ApiParamsOut({
            "results>>today|yesterday|month>>moneyRecharge:总充值",
            "results>>today|yesterday|month>>moneyProfit:实到充值",
            "results>>today|yesterday|month>>usProfit:自投充值",
            "results>>today|yesterday|month>>agentProfit:代投充值",
            "results>>today|yesterday|month>>aloneProfit:分销充值",
            "results>>today|yesterday|month>>nativeProfit:native充值",
            "results>>today|yesterday|month>>kuaishouMountProfit:快手挂载充值",
            "results>>today|yesterday|month>>douyinMountProfit:抖音挂载",
            "results>>today|yesterday|month>>douyinOriginProfit:抖音原生收入",
            "results>>today|yesterday|month>>kuaishouOriginProfit:快手原生收入",
            "results>>today|yesterday|month>>kuaishouJuxingProfit:快手聚星收入"
    })
    @PostMapping("/mediaRechargeData")
    public ResultVO<?> mediaRechargeData(HttpServletRequest request, FastStaffCostIncomeDayPO params) {
        SessionVO sessionVO = getSessionVO(request);
        String today = DateUtil.format09(DateUtil.getNowDate());
        params.setDateStr(today);
        if (Objects.isNull(params.getContentType()) && sessionVO.getContentType() != 99) {
            params.setContentType(sessionVO.getContentType());
        }
        Map<String, Object> todayList = fastStaffCostIncomeDayService.mediaRechargeOverview(params);
        if (params.getExportData() == 1 && Objects.equals(params.getSearchKey(), "today")) {
            return fastStaffCostIncomeDayService.exportRechargeData(sessionVO, todayList, "today");
        }

        String yesterday = DateUtil.format09(DateUtil.addDays(DateUtil.format09(today), -1));
        params.setDateStr(yesterday);
        Map<String, Object> yesterdayList = fastStaffCostIncomeDayService.mediaRechargeOverview(params);
        if (params.getExportData() == 1 && Objects.equals(params.getSearchKey(), "yesterday")) {
            return fastStaffCostIncomeDayService.exportRechargeData(sessionVO, yesterdayList, "yesterday");
        }

        FastStaffCostIncomeDayPO po = new FastStaffCostIncomeDayPO();
        po.setMonth(DateUtil.format10(DateUtil.getNowDate()));
        po.setContentType(sessionVO.getContentType());
        Map<String, Object> monthList = fastStaffCostIncomeDayService.mediaRechargeOverviewMonth(po);

//        FastStaffCostIncomeMonthPO po = new FastStaffCostIncomeMonthPO();
//        po.setMonth(DateUtil.format05Int(DateUtil.getNowDate()));
//        Map<String, Object> monthList = fastStaffCostIncomeMonthService.mediaRechargeOverview(po);
        if (params.getExportData() == 1 && Objects.equals(params.getSearchKey(), "month")) {
            return fastStaffCostIncomeDayService.exportRechargeData(sessionVO, monthList, "month");
        }

        Map<String, Object> data = new HashMap<>();
        data.put("today", todayList);
        data.put("yesterday", yesterdayList);
        data.put("month", monthList);
        return ResultVO.success(data);
    }

    @ApiName(value = "business-概览月数据明细列表", folder = {"business"})
    @ApiParamsIn({
            "year:1:int:年",
            "exportData:0:int:1导出"
    })
    @ApiParamsOut({
            "results>>month:月份202401",
            "results>>monthStr:月份2024-01",
            "results>>type:类型1巨量，2adq，5快手，102星图，501快手native，502快手挂载",
            "results>>moneyRecharge:总充值",
            "results>>moneyProfit:实到充值",
            "results>>usProfit:自投充值",
            "results>>agentProfit:代投充值",
            "results>>aloneProfit:分销充值",
            "results>>nativeProfit:native充值",
            "results>>kuaishouMountProfit:快手挂载充值",
            "results>>douyinMountProfit:抖音挂载",
            "results>>douyinOriginProfit:抖音原生收入",
            "results>>kuaishouOriginProfit:快手原生收入",
            "results>>kuaishouJuxingProfit:快手聚星收入"
    })
    @PostMapping("/mediaRechargeMonthList")
    public ResultVO<?> mediaRechargeMonthList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (params.getYear() == null) {
            params.setYear(DateUtil.getCurrentYear());
        }
        if (Objects.isNull(params.getContentType()) && sessionVO.getContentType() != 99) {
            params.setContentType(sessionVO.getContentType());
        }
        Date monthStart = DateUtil.format05(params.getYear() + "01");
        params.setMonthStart(DateUtil.format05Int(monthStart));
        params.setMonthEnd(DateUtil.format05Int(DateUtil.addMonths(monthStart, 11)));
        List<Map<String, Object>> dataLists = fastStaffCostIncomeMonthService.mediaRechargeMonthList(params);
        Map<String, Object> total = fastStaffCostIncomeMonthService.mediaRechargeMonthTotal(params);
        dataLists.add(0, total);
        if (params.getExportData() == 1) {
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }

            List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
            List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
            List<String> userName = new ArrayList<>(); // 用来接收表格每行头部的数据
            for (Map<String, Object> cur : dataLists) {
                List<Object> row = new ArrayList<>();// 导出的数据(一行)
                row.add(cur.get("monthStr"));
                CollUtil.addNoRepeat(rowHeadNames, "月份");

                row.add(cur.get("moneyProfit"));
                CollUtil.addNoRepeat(rowHeadNames, "平台充值总金额");
                row.add(cur.get("moneyRecharge"));
                CollUtil.addNoRepeat(rowHeadNames, "平台实到总金额");
                row.add(cur.get("usProfit"));
                CollUtil.addNoRepeat(rowHeadNames, "自投充值");
                row.add(cur.get("agentProfit"));
                CollUtil.addNoRepeat(rowHeadNames, "代投充值");
                row.add(cur.get("aloneProfit"));
                CollUtil.addNoRepeat(rowHeadNames, "分销充值");
                row.add(cur.get("nativeProfit"));
                CollUtil.addNoRepeat(rowHeadNames, "native充值");
                row.add(cur.get("kuaishouMountProfit"));
                CollUtil.addNoRepeat(rowHeadNames, "快手挂载充值");
                row.add(cur.get("douyinMountProfit"));
                CollUtil.addNoRepeat(rowHeadNames, "抖音挂载充值");
                row.add(cur.get("douyinOriginProfit"));
                CollUtil.addNoRepeat(rowHeadNames, "抖音原生收入");
                row.add(cur.get("kuaishouOriginProfit"));
                CollUtil.addNoRepeat(rowHeadNames, "快手原生收入");
                row.add(cur.get("kuaishouJuxingProfit"));
                CollUtil.addNoRepeat(rowHeadNames, "快手聚星收入");
                dataList.add(row);
            }
            if (rowHeadNames.size() <= 1) {
                return ResultVO.error("您至少要选择2项导出字段");// 您选择的导出列至少要有2项
            }
            String title = "经营-概览-月明细列表";
            excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);

            RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            return ResultVO.success();
        }

        return ResultVO.success(dataLists);
    }

    @ApiName(value = "business-投放组数据", folder = {"business"})
    @ApiParamsIn({
            "month:1:int:年月",
            "dateStart:0:String:开始日期",
            "dateEnd:0:String:结束日期",
            "mediaType:0:int:媒体类型"
    })
    @ApiParamsOut({
            "results>>userGroupId:组id",
            "results>>userGroupName:组名称",
            "results>>userGroupNums:组人员数",
            "results>>cashCost:现金消耗",
            "results>>cashCostBack:现金返点后消耗",
            "results>>moneyProfit:实到充值",
            "results>>moneyRecharge:总充值",
            "results>>virtualRecharge:虚拟充值",
            "results>>normalRecharge:普通充值",
            "results>>weekCashCost:周现金消耗",
            "results>>weekMoneyProfit:周实到充值",
            "results>>weekMoneyRecharge:周总充值",
            "results>>staffIncome:固定成本",
            "results>>totalProfit:总利润",
            "results>>companyProfit:公司利润",
            "results>>staffProfit:组利润",
            "results>>estimateStaffIncome:固定成本预估",
            "results>>estimateCompanyProfit:公司利润预估",
            "results>>estimateStaffProfit:组利润预估",
            "results>>weekCashCostRatio:环比，周现金消耗(单位元)",
            "results>>weekMoneyProfitRatio:环比，周总充值",
            "results>>cashCostRatio:环比，现金消耗(单位元)",
            "results>>moneyProfitRatio:环比，总充值",
            "results>>companyProfitRatio:环比，公司利润",
            "results>>staffProfitRatio:环比，员工利润"
    })
    @PostMapping("/launchGroupList")
    public ResultVO<?> launchGroupList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        FastSettingSystemPO fastSettingSystemPO = new FastSettingSystemPO();
        fastSettingSystemPO.setCode("week_start_day");
        FastSettingSystemPO fastSettingSystem = fastSettingSystemService.queryOne(fastSettingSystemPO);
        SessionVO sessionVO = getSessionVO(request);
        if (Objects.isNull(params.getContentType()) && sessionVO.getContentType() != 99) {
            params.setContentType(sessionVO.getContentType());
        }
        Integer weekStartDay = 1;
        if (fastSettingSystem != null) {
            weekStartDay = Integer.parseInt(fastSettingSystem.getContent());
        }
        if (weekStartDay < 1 || weekStartDay > 7) {
            weekStartDay = 1;
        }
        Date week = DateUtil.beginOfWeek();
        Date startDate = DateUtil.addDays(week, weekStartDay - 1);
        if (startDate.compareTo(DateUtil.beginOfDay()) > 0) {
            startDate = DateUtil.addDays(startDate, -7);
        }
        if (StringUtil.isEmpty(params.getDateStart())) {
            params.setDateStart(DateUtil.format09(startDate));
        }
        if (StringUtil.isEmpty(params.getDateEnd())) {
            params.setDateEnd(DateUtil.format09(DateUtil.addDays(new Date(), -1)));
        }

        if (params.getMonth() != null) {
            params.setMonth(params.getMonth());
        } else if (StringUtil.isNotEmpty(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format09(params.getMonthStr() + "-01")));
        } else {
            params.setMonth(DateUtil.format05Int(new Date()));
        }
        params.setRetailTypes("1,2");
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchGroupDataList(params);
        Map<Integer, FastStaffCostIncomeMonthPO> weekMap = fastStaffCostIncomeMonthService.launchGroupWeekDataList(params);

        FastStaffCostIncomeMonthPO preParams = new FastStaffCostIncomeMonthPO();
        preParams.setDateStart(DateUtil.format09(DateUtil.addDays(startDate, -7)));
        preParams.setDateEnd(DateUtil.format09(DateUtil.addDays(startDate, -1)));
        preParams.setMediaType(params.getMediaType());
        Map<Integer, FastStaffCostIncomeMonthPO> weekMap1 = fastStaffCostIncomeMonthService.launchGroupWeekDataList(preParams);

        Map<Integer, FastStaffCostIncomeMonthPO> dataListMap1 = new HashMap<>();
        preParams.setMonth(DateUtil.format05Int(DateUtil.addMonths(DateUtil.format06(params.getMonth() + "01"), -1)));
        preParams.setRetailTypes("1,2");
        List<FastStaffCostIncomeMonthPO> dataList1 = fastStaffCostIncomeMonthService.launchGroupDataList(preParams);
        for (FastStaffCostIncomeMonthPO val : dataList1) {
            if (val.getUserGroupId() == null) {
                continue;
            }
            dataListMap1.put(val.getUserGroupId(), val);
        }
        FastStaffCostIncomeMonthPO totalData = new FastStaffCostIncomeMonthPO();
        for (FastStaffCostIncomeMonthPO val : dataList) {
            if (weekMap.containsKey(val.getUserGroupId())) {
                FastStaffCostIncomeMonthPO weekData = weekMap.get(val.getUserGroupId());
                val.setWeekCashCost(weekData.getWeekCashCost());
                val.setWeekMoneyProfit(weekData.getWeekMoneyProfit());
                val.setWeekMoneyRecharge(weekData.getWeekMoneyRecharge());
            } else {
                val.setWeekCashCost(BigDecimal.ZERO);
                val.setWeekMoneyProfit(BigDecimal.ZERO);
                val.setWeekMoneyRecharge(BigDecimal.ZERO);
            }

            if (weekMap1.containsKey(val.getUserGroupId())) {
                FastStaffCostIncomeMonthPO weekData1 = weekMap1.get(val.getUserGroupId());
                val.setWeekCashCostRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getWeekCashCost(), weekData1.getWeekCashCost()), weekData1.getWeekCashCost()));
                val.setWeekMoneyProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getWeekMoneyProfit(), weekData1.getWeekMoneyProfit()), weekData1.getWeekMoneyProfit()));
            } else {
                val.setWeekCashCostRatio(BigDecimal.ZERO);
                val.setWeekMoneyProfitRatio(BigDecimal.ZERO);
            }
            if (dataListMap1.containsKey(val.getUserGroupId())) {
                FastStaffCostIncomeMonthPO dataListData1 = dataListMap1.get(val.getUserGroupId());
                val.setCashCostRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getCashCost(), dataListData1.getCashCost()), dataListData1.getCashCost()));
                val.setMoneyProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getMoneyProfit(), dataListData1.getMoneyProfit()), dataListData1.getMoneyProfit()));
                val.setCompanyProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getCompanyProfit(), dataListData1.getCompanyProfit()), dataListData1.getCompanyProfit()));
                val.setStaffProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getStaffProfit(), dataListData1.getStaffProfit()), dataListData1.getStaffProfit()));
            } else {
                val.setCashCostRatio(BigDecimal.ZERO);
                val.setMoneyProfitRatio(BigDecimal.ZERO);
                val.setCompanyProfitRatio(BigDecimal.ZERO);
                val.setStaffProfitRatio(BigDecimal.ZERO);
            }

            totalData.setUserGroupName("累计");
            totalData.setWeekMoneyRecharge(DoubleUtil.addBZero(val.getWeekMoneyRecharge(), totalData.getWeekMoneyRecharge()));
            totalData.setWeekMoneyProfit(DoubleUtil.addBZero(val.getWeekMoneyProfit(), totalData.getWeekMoneyProfit()));
            totalData.setWeekCashCost(DoubleUtil.addBZero(val.getWeekCashCost(), totalData.getWeekCashCost()));
            totalData.setMoneyRecharge(DoubleUtil.addBZero(val.getMoneyRecharge(), totalData.getMoneyRecharge()));
            totalData.setMoneyProfit(DoubleUtil.addBZero(val.getMoneyProfit(), totalData.getMoneyProfit()));
            totalData.setCashCost(DoubleUtil.addBZero(val.getCashCost(), totalData.getCashCost()));
            totalData.setEstimateStaffIncome(DoubleUtil.addBZero(val.getEstimateStaffIncome(), totalData.getEstimateStaffIncome()));
            totalData.setEstimateStaffProfit(DoubleUtil.addBZero(val.getEstimateStaffProfit(), totalData.getEstimateStaffProfit()));
            totalData.setEstimateCompanyProfit(DoubleUtil.addBZero(val.getEstimateCompanyProfit(), totalData.getEstimateCompanyProfit()));
            totalData.setStaffProfit(DoubleUtil.addBZero(val.getStaffProfit(), totalData.getStaffProfit()));
            totalData.setCompanyProfit(DoubleUtil.addBZero(val.getCompanyProfit(), totalData.getCompanyProfit()));
        }
        if (StrUtil.isNotBlank(totalData.getUserGroupName())) {
            dataList.add(0, totalData);
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastStaffCostIncomeMonthService.exportGroupList(sessionVO, dataList);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return ResultVO.success(dataList);
    }

    @ApiName(value = "business-媒体数据-(投放组)", folder = {"business"})
    @ApiParamsIn({
            "month:1:int:年月",
            "userGroupId:0:int:组id",
            "userId:0:int:用户id",
            "dateStart:0:String:开始日期",
            "dateEnd:0:String:结束日期",
    })
    @ApiParamsOut({
            "results>>mediaType:组id",
            "results>>cashCost:现金消耗",
            "results>>cashCostBack:现金返点后消耗",
            "results>>moneyProfit:实到充值",
            "results>>moneyRecharge:总充值",
            "results>>virtualRecharge:虚拟充值",
            "results>>normalRecharge:普通充值",
            "results>>weekCashCost:周现金消耗",
            "results>>weekMoneyProfit:周实到充值",
            "results>>weekMoneyRecharge:周总充值",
            "results>>staffIncome:固定成本",
            "results>>totalProfit:总利润",
            "results>>companyProfit:公司利润",
            "results>>staffProfit:组利润",
            "results>>estimateStaffIncome:固定成本预估",
            "results>>estimateCompanyProfit:公司利润预估",
            "results>>estimateStaffProfit:组利润预估"
    })
    @PostMapping("/launchGroupMediaList")
    public ResultVO<?> launchGroupMediaList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        FastSettingSystemPO fastSettingSystemPO = new FastSettingSystemPO();
        fastSettingSystemPO.setCode("week_start_day");
        FastSettingSystemPO fastSettingSystem = fastSettingSystemService.queryOne(fastSettingSystemPO);
        Integer weekStartDay = 1;
        if (fastSettingSystem != null) {
            weekStartDay = Integer.parseInt(fastSettingSystem.getContent());
        }
        if (weekStartDay < 1 || weekStartDay > 7) {
            weekStartDay = 1;
        }
        Date week = DateUtil.beginOfWeek();
        Date startDate = DateUtil.addDays(week, weekStartDay - 1);
        if (startDate.compareTo(DateUtil.beginOfDay()) > 0) {
            startDate = DateUtil.addDays(startDate, -7);
        }
        SessionVO sessionVO = getSessionVO(request);
        if (Objects.isNull(params.getContentType()) && sessionVO.getContentType() != 99) {
            params.setContentType(sessionVO.getContentType());
        }
        if (StringUtil.isEmpty(params.getDateStart())) {
            params.setDateStart(DateUtil.format09(startDate));
        }
        if (StringUtil.isEmpty(params.getDateEnd())) {
            params.setDateEnd(DateUtil.format09(DateUtil.addDays(new Date(), -1)));
        }

        if (params.getMonth() != null) {
            params.setMonth(params.getMonth());
        } else if (StringUtil.isNotEmpty(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format09(params.getMonthStr() + "-01")));
        } else {
            params.setMonth(DateUtil.format05Int(new Date()));
        }
        if (params.getUserGroupId() == null || params.getUserGroupId() == 0) {
            params.setRetailTypes("1,2");
        }
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchGroupMediaDataList(params);
        Map<Integer, FastStaffCostIncomeMonthPO> weekMap = fastStaffCostIncomeMonthService.launchGroupMediaWeekDataList(params);

        params.setMonth(DateUtil.format05Int(DateUtil.addMonths(DateUtil.format06(params.getMonth() + "01"), -1)));
        List<FastStaffCostIncomeMonthPO> dataList1 = fastStaffCostIncomeMonthService.launchGroupMediaDataList(params);
        Map<Integer, FastStaffCostIncomeMonthPO> dataListMap1 = new HashMap<>();
        for (FastStaffCostIncomeMonthPO val : dataList1) {
            dataListMap1.put(val.getMediaType(), val);
//            if (params.getUserGroupId() == null || params.getUserGroupId() == 0) {
//                dataListMap1.put(val.getMediaType(), val);
//            } else {
//                dataListMap1.put(val.getUserGroupId(), val);
//            }
        }

        BigDecimal weekMoneyProfitTotal = BigDecimal.ZERO;
        BigDecimal weekMoneyRechargeTotal = BigDecimal.ZERO;
        for (FastStaffCostIncomeMonthPO val : dataList) {
            if (weekMap.containsKey(val.getMediaType())) {
                FastStaffCostIncomeMonthPO weekData = weekMap.get(val.getMediaType());
                val.setWeekCashCost(weekData.getWeekCashCost());
                val.setWeekMoneyProfit(weekData.getWeekMoneyProfit());
                val.setWeekMoneyRecharge(weekData.getWeekMoneyRecharge());
                weekMoneyProfitTotal = weekMoneyProfitTotal.add(weekData.getWeekMoneyProfit());
                weekMoneyRechargeTotal = weekMoneyRechargeTotal.add(weekData.getWeekMoneyRecharge());
            } else {
                val.setWeekCashCost(BigDecimal.ZERO);
                val.setWeekMoneyProfit(BigDecimal.ZERO);
                val.setWeekMoneyRecharge(BigDecimal.ZERO);
            }
            Integer dataListMap1Key = val.getMediaType();
//            if (params.getUserGroupId() == null || params.getUserGroupId() == 0) {
//                dataListMap1Key = val.getMediaType();
//            } else {
//                dataListMap1Key = val.getUserGroupId();
//            }
            if (dataListMap1.containsKey(dataListMap1Key)) {
                FastStaffCostIncomeMonthPO dataListData1 = dataListMap1.get(dataListMap1Key);
                if (val.getCashCost() != null && dataListData1.getCashCost() != null) {
                    val.setCashCostRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getCashCost(), dataListData1.getCashCost()), dataListData1.getCashCost()));
                } else {
                    val.setCashCostRatio(BigDecimal.ZERO);
                }
                if (val.getCashCostBack() != null && dataListData1.getCashCostBack() != null) {
                    val.setCashCostBackRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getCashCostBack(), dataListData1.getCashCostBack()), dataListData1.getCashCostBack()));
                } else {
                    val.setCashCostBackRatio(BigDecimal.ZERO);
                }
                if (val.getMoneyProfit() != null && dataListData1.getMoneyProfit() != null) {
                    val.setMoneyProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getMoneyProfit(), dataListData1.getMoneyProfit()), dataListData1.getMoneyProfit()));
                } else {
                    val.setMoneyProfitRatio(BigDecimal.ZERO);
                }
                if (val.getCompanyProfit() != null && dataListData1.getCompanyProfit() != null) {
                    val.setCompanyProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getCompanyProfit(), dataListData1.getCompanyProfit()), dataListData1.getCompanyProfit()));
                } else {
                    val.setCompanyProfitRatio(BigDecimal.ZERO);
                }
                if (val.getStaffProfit() != null && dataListData1.getStaffProfit() != null) {
                    val.setStaffProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getStaffProfit(), dataListData1.getStaffProfit()), dataListData1.getStaffProfit()));
                } else {
                    val.setStaffProfitRatio(BigDecimal.ZERO);
                }
            } else {
                val.setCashCostRatio(BigDecimal.ZERO);
                val.setCashCostBackRatio(BigDecimal.ZERO);
                val.setMoneyProfitRatio(BigDecimal.ZERO);
                val.setCompanyProfitRatio(BigDecimal.ZERO);
                val.setStaffProfitRatio(BigDecimal.ZERO);
            }
        }
        if (dataList.size() > 0 && dataList.get(0) != null && dataList.get(0).getMediaType() == -1) {
            dataList.get(0).setWeekMoneyProfit(weekMoneyProfitTotal);
            dataList.get(0).setWeekMoneyRecharge(weekMoneyRechargeTotal);
        }
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastStaffCostIncomeMonthService.exportGroupMediaList(sessionVO, dataList);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return ResultVO.success(dataList);
    }

    @ApiName(value = "business-媒体月份列表数据-(投放组)", folder = {"business"})
    @ApiParamsIn({
            "year:1:int:年",
            "userGroupId:0:int:组id",
            "mediaType:0:int:组id",
            "userId:0:int:用户id"
    })
    @ApiParamsOut({
            "results>>monthStr:月",
            "results>>mediaType:组id",
            "results>>cashCost:现金消耗",
            "results>>cashCostBack:现金返点后消耗",
            "results>>moneyProfit:实到充值",
            "results>>moneyRecharge:总充值",
            "results>>virtualRecharge:虚拟充值",
            "results>>normalRecharge:普通充值",
            "results>>staffIncome:固定成本",
            "results>>totalProfit:总利润",
            "results>>companyProfit:公司利润",
            "results>>staffProfit:组利润"
    })
    @PostMapping("/launchGroupMonthList")
    public ResultVO<?> launchGroupMonthList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (Objects.isNull(params.getContentType()) && sessionVO.getContentType() != 99) {
            params.setContentType(sessionVO.getContentType());
        }
        Map<Integer, String> mediaMap = new HashMap<>();
        mediaMap.put(-1, "媒体累计");
        mediaMap.put(1, "巨量");
        mediaMap.put(2, "adq");
        mediaMap.put(4, "百度");
        mediaMap.put(5, "快手");
        mediaMap.put(501, "快手native");
        mediaMap.put(502, "快手挂载");
        mediaMap.put(102, "星图");
        String title = "自代投媒体数据";
        if (mediaMap.containsKey(params.getMediaType())) {
            title += "-月数据-" + mediaMap.get(params.getMediaType());
        } else {
            title += params.getUserGroupId() != null ? "-组" : "-累计";
            title += params.getYear() != null ? "月明细" : "媒体明细";
        }

        if (params.getYear() == null) {
            params.setYear(DateUtil.getCurrentYear());
        }
        if (params.getMediaType() != null && params.getMediaType() == -1) {
            params.setMediaType(null);
        }

        Date monthStart = DateUtil.format05(params.getYear() + "01");
        params.setMonthStart(DateUtil.format05Int(monthStart));
        params.setMonthEnd(DateUtil.format05Int(DateUtil.addMonths(monthStart, 11)));
        if (params.getUserGroupId() == null || params.getUserGroupId() == 0) {
            params.setRetailTypes("1,2");
        }
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchGroupMediaMonthDataList(params);

        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }

            ResultVO<?> vo = fastStaffCostIncomeMonthService.exportGroupMediaMonthData(sessionVO, dataList, title);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return ResultVO.success(dataList);
    }

    @ApiName(value = "business-投放组-月-成员-列表数据", folder = {"business"})
    @ApiParamsIn({
            "month:1:int:月份",
            "userGroupId:1:int:组id",
            "mediaType:0:int:组id",
            "userId:0:int:用户id"
    })
    @ApiParamsOut({
            "results>>mediaType:组id",
            "results>>cashCost:现金消耗",
            "results>>cashCostBack:现金返点后消耗",
            "results>>moneyProfit:实到充值",
            "results>>moneyRecharge:总充值",
            "results>>virtualProfit:实到虚拟充值",
            "results>>virtualRecharge:虚拟充值",
            "results>>normalProfit:实到普通充值",
            "results>>normalRecharge:普通充值",
            "results>>staffIncome:固定成本",
            "results>>statementMoney:结算金额",
            "results>>totalProfit:总利润",
            "results>>companyProfit:公司利润",
            "results>>staffProfit:组利润"
    })
    @PostMapping("/launchGroupStaffList")
    public ResultVO<?> launchGroupStaffList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        if (params.getMonth() != null) {
            params.setMonth(params.getMonth());
        } else if (StringUtil.isNotEmpty(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format09(params.getMonthStr() + "-01")));
        } else {
            params.setMonth(DateUtil.format05Int(new Date()));
        }

        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchGroupStaffMonthDataList(params);

        if (params.getExportData() == 1) {
            SessionVO sessionVO = getSessionVO(request);
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastStaffCostIncomeMonthService.exportGroupStaffMonthData(sessionVO, dataList);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return ResultVO.success(dataList);
    }


    @ApiName(value = "business-分销-列表数据", folder = {"business"})
    @ApiParamsIn({
            "month:1:int:年月",
            "startDate:0:String:开始日期",
            "endDate:0:String:结束日期",
            "mediaType:0:int:媒体类型"
    })
    @ApiParamsOut({
            "results>>retailId:分销商Id",
            "results>>retailName:分销商名称",
            "results>>moneyProfit:实到充值",
            "results>>moneyRecharge:总充值",
            "results>>moneyProfitRatio:月充值环比",
            "results>>moneyRefund:充值退款",
            "results>>retailProfit:分销商实到金额",
            "results>>retailProfitRatio:分销商实到金额",
            "results>>processCost:投放过程成本",
            "results>>weekMoneyProfit:周实到充值",
            "results>>weekMoneyRecharge:周充值",
            "results>>weekMoneyProfitRatio:周充值环比",
            "results>>companyProfit:公司利润",
            "results>>companyProfitRatio:公司利润环比"
    })
    @PostMapping("/launchRetailList")
    public ResultVO<?> launchRetailList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        SessionVO sessionVO = getSessionVO(request);
        if (Objects.isNull(params.getContentType()) && sessionVO.getContentType() != 99) {
            params.setContentType(sessionVO.getContentType());
        }
        FastSettingSystemPO fastSettingSystemPO = new FastSettingSystemPO();
        fastSettingSystemPO.setCode("week_start_day");
        FastSettingSystemPO fastSettingSystem = fastSettingSystemService.queryOne(fastSettingSystemPO);
        Integer weekStartDay = 1;
        if (fastSettingSystem != null) {
            weekStartDay = Integer.parseInt(fastSettingSystem.getContent());
        }
        if (weekStartDay < 1 || weekStartDay > 7) {
            weekStartDay = 1;
        }
        Date week = DateUtil.beginOfWeek();
        Date startDate = DateUtil.addDays(week, weekStartDay - 1);
        if (startDate.compareTo(DateUtil.beginOfDay()) > 0) {
            startDate = DateUtil.addDays(startDate, -7);
        }
        if (StringUtil.isEmpty(params.getDateStart())) {
            params.setDateStart(DateUtil.format09(startDate));
        }
        if (StringUtil.isEmpty(params.getDateEnd())) {
            params.setDateEnd(DateUtil.format09(DateUtil.addDays(startDate, +6)));
        }

        if (params.getMonth() != null) {
            params.setMonth(params.getMonth());
        } else if (StringUtil.isNotEmpty(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format09(params.getMonthStr() + "-01")));
        } else {
            params.setMonth(DateUtil.format05Int(new Date()));
        }

        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchRetailMonthList(params);
        FastStaffCostIncomeMonthPO totalData = fastStaffCostIncomeMonthService.launchRetailMonthTotal(params);
        Map<Integer, FastStaffCostIncomeMonthPO> weekMap = fastStaffCostIncomeMonthService.launchRetailWeekList(params);

        FastStaffCostIncomeMonthPO preParams = new FastStaffCostIncomeMonthPO();
        preParams.setDateStart(DateUtil.format09(DateUtil.addDays(startDate, -7)));
        preParams.setDateEnd(DateUtil.format09(DateUtil.addDays(startDate, -1)));
        preParams.setMediaType(params.getMediaType());
        Map<Integer, FastStaffCostIncomeMonthPO> weekMap1 = fastStaffCostIncomeMonthService.launchRetailWeekList(params);

        Map<Integer, FastStaffCostIncomeMonthPO> dataListMap1 = new HashMap<>();
        preParams.setMonth(DateUtil.format05Int(DateUtil.addMonths(DateUtil.format06(params.getMonth() + "01"), -1)));
        log.info(toJSONString(preParams));
        List<FastStaffCostIncomeMonthPO> dataList1 = fastStaffCostIncomeMonthService.launchRetailMonthList(preParams);
        for (FastStaffCostIncomeMonthPO val : dataList1) {
            dataListMap1.put(val.getRetailId(), val);
        }
        BigDecimal weekMoneyProfitTotal = BigDecimal.ZERO;
        BigDecimal weekMoneyRechargeTotal = BigDecimal.ZERO;
        for (FastStaffCostIncomeMonthPO val : dataList) {
            if (weekMap.containsKey(val.getRetailId())) {
                FastStaffCostIncomeMonthPO weekData = weekMap.get(val.getRetailId());
                val.setWeekMoneyProfit(weekData.getWeekMoneyProfit());
                val.setWeekMoneyRecharge(weekData.getWeekMoneyRecharge());
                weekMoneyProfitTotal = weekMoneyProfitTotal.add(weekData.getWeekMoneyProfit());
                weekMoneyRechargeTotal = weekMoneyRechargeTotal.add(weekData.getWeekMoneyRecharge());
            } else {
                val.setWeekMoneyProfit(BigDecimal.ZERO);
                val.setWeekMoneyRecharge(BigDecimal.ZERO);
            }

            if (weekMap1.containsKey(val.getRetailId())) {
                FastStaffCostIncomeMonthPO weekData1 = weekMap1.get(val.getRetailId());
                val.setWeekMoneyProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getWeekMoneyProfit(), weekData1.getWeekMoneyProfit()), weekData1.getWeekMoneyProfit()));
            } else {
                val.setWeekMoneyProfitRatio(BigDecimal.ZERO);
            }
            if (dataListMap1.containsKey(val.getRetailId())) {
                FastStaffCostIncomeMonthPO dataListData1 = dataListMap1.get(val.getRetailId());
                val.setMoneyProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getMoneyProfit(), dataListData1.getMoneyProfit()), dataListData1.getMoneyProfit()));
                val.setCompanyProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getCompanyProfit(), dataListData1.getCompanyProfit()), dataListData1.getCompanyProfit()));
                val.setRetailProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getRetailProfit(), dataListData1.getRetailProfit()), dataListData1.getRetailProfit()));
            } else {
                val.setMoneyProfitRatio(BigDecimal.ZERO);
                val.setCompanyProfitRatio(BigDecimal.ZERO);
                val.setRetailProfitRatio(BigDecimal.ZERO);
            }
        }
        totalData.setWeekMoneyProfit(weekMoneyProfitTotal);
        totalData.setWeekMoneyRecharge(weekMoneyRechargeTotal);
        dataList.add(0, totalData);

        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastStaffCostIncomeMonthService.exportRetailMonthData(sessionVO, dataList, 1);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return ResultVO.success(dataList);
    }


    @ApiName(value = "business-分销-月列表数据", folder = {"business"})
    @ApiParamsIn({
            "year:1:int:年",
            "retailId:1:int:分销id",
            "mediaType:0:int:组id",
            "dramaId:0:int:短剧id，不支持忽略该参数"
    })
    @ApiParamsOut({
            "results>>monthStr:月",
            "results>>moneyRecharge:总充值",
            "results>>moneyProfit:实到充值",
            "results>>moneyRefund:退款",
            "results>>processCost:投放过程成本",
            "results>>retailProfit:分销商利润",
            "results>>companyProfit:公司利润"
    })
    @PostMapping("/launchRetailMonthList")
    public ResultVO<?> launchRetailMonthList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        if (params.getRetailId() == null) {
            return ResultVO.error("参数错误");
        }
        if (params.getRetailId() == 0) {
            params.setRetailId(null);
            params.setGroupField("t.`month`");
        }
        if (params.getYear() == null) {
            params.setYear(DateUtil.getCurrentYear());
        }
        Date monthStart = DateUtil.format05(params.getYear() + "01");
        params.setMonthStart(DateUtil.format05Int(monthStart));
        params.setMonthEnd(DateUtil.format05Int(DateUtil.addMonths(monthStart, 11)));
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchRetailMonthList(params);
        FastStaffCostIncomeMonthPO totalData = fastStaffCostIncomeMonthService.launchRetailMonthTotal(params);
        totalData.setMonthStr("累计");
        dataList.add(0, totalData);
        if (params.getExportData() == 1) {
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastStaffCostIncomeMonthService.exportRetailMonthData(sessionVO, dataList, 2);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return ResultVO.success(dataList);
    }

    @ApiName(value = "business-分销-媒体数据", folder = {"business"})
    @ApiParamsIn({
            "month:1:int:年月",
            "retailId:1:int:组id",
            "userId:0:int:用户id"
    })
    @ApiParamsOut({
            "results>>mediaType:媒体id",
            "results>>moneyRecharge:总充值",
            "results>>virtualRecharge:虚拟充值",
            "results>>normalRecharge:普通充值",
            "results>>moneyProfit:实到充值",
            "results>>virtualProfit:虚拟充值",
            "results>>normalProfit:普通充值",
            "results>>normalProfit:普通充值",
            "results>>moneyRefund:总退款",
            "results>>normalRefund:普通退款",
            "results>>virtualRefund:虚拟退款",
            "results>>retailProfit:分成金额",
            "results>>companyProfit:公司利润",
    })
    @PostMapping("/launchRetailMediaList")
    public ResultVO<?> launchRetailMediaList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        if (params.getRetailId() == null) {
            return ResultVO.error("参数错误");
        }
        if (params.getRetailId() == 0) {
            params.setRetailId(null);
        }
        if (params.getMonth() != null) {
            params.setMonth(params.getMonth());
        } else if (StringUtil.isNotEmpty(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format09(params.getMonthStr() + "-01")));
        } else {
            params.setMonth(DateUtil.format05Int(new Date()));
        }
        params.setGroupField("t.media_type");
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchRetailMonthList(params);
        FastStaffCostIncomeMonthPO totalData = fastStaffCostIncomeMonthService.launchRetailMonthTotal(params);
        totalData.setRetailName("累计");
        totalData.setMediaType(-1);
        dataList.add(0, totalData);
        if (params.getExportData() == 1) {
            SessionVO sessionVO = getSessionVO(request);
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastStaffCostIncomeMonthService.exportRetailMonthData(sessionVO, dataList, 3);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return ResultVO.success(dataList);
    }


    @ApiName(value = "business-媒体-组数据", folder = {"business"})
    @ApiParamsIn({
            "month:1:int:年月",
            "startDate:0:String:开始日期",
            "endDate:0:String:结束日期",
            "mediaType:0:int:媒体类型"
    })
    @ApiParamsOut({
            "results>>userGroupId:组id",
            "results>>userGroupName:组名称",
            "results>>moneyRecharge:总充值",
            "results>>virtualRecharge:虚拟充值",
            "results>>normalRecharge:普通充值",
            "results>>moneyProfit:实到充值",
            "results>>virtualProfit:虚拟实到充值",
            "results>>normalProfit:普通实到充值",
            "results>>cashCost:现金消耗",
            "results>>cashCostBack:现金返点后消耗",
            "results>>statementMoney:结算金额",
            "results>>staffIncome:其他成本",
            "results>>companyProfit:公司利润",
            "results>>staffProfit:组利润"
    })
    @PostMapping("/launchMediaGroupList")
    public ResultVO<?> launchMediaGroupList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        if (params.getMonth() != null) {
            params.setMonth(params.getMonth());
        } else if (StringUtil.isNotEmpty(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format09(params.getMonthStr() + "-01")));
        } else {
            params.setMonth(DateUtil.format05Int(new Date()));
        }
        params.setRetailTypes("1,2");
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchGroupDataList(params);

        FastStaffCostIncomeMonthPO totalData = new FastStaffCostIncomeMonthPO();
        for (FastStaffCostIncomeMonthPO val : dataList) {
            totalData.setUserGroupName("累计");
            totalData.setMoneyRecharge(DoubleUtil.addBZero(val.getMoneyRecharge(), totalData.getMoneyRecharge()));
            totalData.setVirtualRecharge(DoubleUtil.addBZero(val.getVirtualRecharge(), totalData.getVirtualRecharge()));
            totalData.setNormalRecharge(DoubleUtil.addBZero(val.getNormalRecharge(), totalData.getNormalRecharge()));
            totalData.setMoneyProfit(DoubleUtil.addBZero(val.getMoneyProfit(), totalData.getMoneyProfit()));
            totalData.setVirtualRecharge(DoubleUtil.addBZero(val.getVirtualProfit(), totalData.getVirtualProfit()));
            totalData.setNormalRecharge(DoubleUtil.addBZero(val.getNormalProfit(), totalData.getNormalProfit()));
            totalData.setCashCost(DoubleUtil.addBZero(val.getCashCost(), totalData.getCashCost()));
            totalData.setCashCostBack(DoubleUtil.addBZero(val.getCashCostBack(), totalData.getCashCostBack()));
            totalData.setStatementMoney(DoubleUtil.addBZero(val.getStatementMoney(), totalData.getStatementMoney()));
            totalData.setStaffIncome(DoubleUtil.addBZero(val.getStaffIncome(), totalData.getStaffIncome()));
            totalData.setStaffProfit(DoubleUtil.addBZero(val.getStaffProfit(), totalData.getStaffProfit()));
            totalData.setCompanyProfit(DoubleUtil.addBZero(val.getCompanyProfit(), totalData.getCompanyProfit()));
        }
        dataList.add(0, totalData);
        if (params.getExportData() == 1) {
            SessionVO sessionVO = getSessionVO(request);
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastStaffCostIncomeMonthService.exportGroupList(sessionVO, dataList);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return ResultVO.success(dataList);
    }

    @ApiName(value = "business-媒体数据-(自代投媒体数据)-未使用", folder = {"business"})
    @ApiParamsIn({
            "month:1:int:年月",
            "userGroupId:0:int:组id",
            "userId:0:int:用户id"
    })
    @ApiParamsOut({
            "results>>mediaType:组id",
            "results>>cashCost:现金消耗",
            "results>>cashCostBack:现金返点后消耗",
            "results>>moneyProfit:实到充值",
            "results>>moneyRecharge:总充值",
            "results>>virtualRecharge:虚拟充值",
            "results>>normalRecharge:普通充值",
            "results>>weekCashCost:周现金消耗",
            "results>>weekMoneyProfit:周实到充值",
            "results>>weekMoneyRecharge:周总充值",
            "results>>staffIncome:固定成本",
            "results>>totalProfit:总利润",
            "results>>companyProfit:公司利润",
            "results>>staffProfit:组利润",
            "results>>estimateStaffIncome:固定成本预估",
            "results>>estimateCompanyProfit:公司利润预估",
            "results>>estimateStaffProfit:组利润预估"
    })
    @PostMapping("launchMediaList")
    public ResultVO<?> launchMediaList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        FastSettingSystemPO fastSettingSystemPO = new FastSettingSystemPO();
        fastSettingSystemPO.setCode("week_start_day");
        FastSettingSystemPO fastSettingSystem = fastSettingSystemService.queryOne(fastSettingSystemPO);
        Integer weekStartDay = 1;
        if (fastSettingSystem != null) {
            weekStartDay = Integer.parseInt(fastSettingSystem.getContent());
        }
        if (weekStartDay < 1 || weekStartDay > 7) {
            weekStartDay = 1;
        }
        Date week = DateUtil.beginOfWeek();
        Date startDate = DateUtil.addDays(week, weekStartDay - 1);
        if (startDate.compareTo(DateUtil.beginOfDay()) > 0) {
            startDate = DateUtil.addDays(startDate, -7);
        }
        params.setDateStart(DateUtil.format09(startDate));
        params.setDateEnd(DateUtil.format09(DateUtil.addDays(new Date(), -1)));

        if (params.getMonth() != null) {
            params.setMonth(params.getMonth());
        } else if (StringUtil.isNotEmpty(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format09(params.getMonthStr() + "-01")));
        } else {
            params.setMonth(DateUtil.format05Int(new Date()));
        }
        if (params.getUserGroupId() == null || params.getUserGroupId() == 0) {
            params.setRetailTypes("1,2");
        }
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchGroupMediaDataList(params);
        Map<Integer, FastStaffCostIncomeMonthPO> weekMap = fastStaffCostIncomeMonthService.launchGroupMediaWeekDataList(params);

        params.setMonth(DateUtil.format05Int(DateUtil.addMonths(DateUtil.format06(params.getMonth() + "01"), -1)));
        List<FastStaffCostIncomeMonthPO> dataList1 = fastStaffCostIncomeMonthService.launchGroupMediaDataList(params);
        Map<Integer, FastStaffCostIncomeMonthPO> dataListMap1 = new HashMap<>();
        for (FastStaffCostIncomeMonthPO val : dataList1) {
            if (val.getUserGroupId() == null) {
                continue;
            }
            dataListMap1.put(val.getUserGroupId(), val);
        }

        BigDecimal weekMoneyProfitTotal = BigDecimal.ZERO;
        BigDecimal weekMoneyRechargeTotal = BigDecimal.ZERO;
        for (FastStaffCostIncomeMonthPO val : dataList) {
            if (weekMap.containsKey(val.getMediaType())) {
                FastStaffCostIncomeMonthPO weekData = weekMap.get(val.getMediaType());
                val.setWeekCashCost(weekData.getWeekCashCost());
                val.setWeekMoneyProfit(weekData.getWeekMoneyProfit());
                val.setWeekMoneyRecharge(weekData.getWeekMoneyRecharge());
                weekMoneyProfitTotal = weekMoneyProfitTotal.add(weekData.getWeekMoneyProfit());
                weekMoneyRechargeTotal = weekMoneyRechargeTotal.add(weekData.getWeekMoneyRecharge());
            } else {
                val.setWeekCashCost(BigDecimal.ZERO);
                val.setWeekMoneyProfit(BigDecimal.ZERO);
                val.setWeekMoneyRecharge(BigDecimal.ZERO);
            }

            if (dataListMap1.containsKey(val.getUserGroupId())) {
                FastStaffCostIncomeMonthPO dataListData1 = dataListMap1.get(val.getUserGroupId());
                val.setCashCostRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getCashCost(), dataListData1.getCashCost()), dataListData1.getCashCost()));
                val.setCashCostBackRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getCashCostBack(), dataListData1.getCashCostBack()), dataListData1.getCashCostBack()));
                val.setMoneyProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getMoneyProfit(), dataListData1.getMoneyProfit()), dataListData1.getMoneyProfit()));
                val.setCompanyProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getCompanyProfit(), dataListData1.getCompanyProfit()), dataListData1.getCompanyProfit()));
                val.setStaffProfitRatio(DoubleUtil.divB4Zero(DoubleUtil.subB(val.getStaffProfit(), dataListData1.getStaffProfit()), dataListData1.getStaffProfit()));
            } else {
                val.setCashCostRatio(BigDecimal.ZERO);
                val.setCashCostBackRatio(BigDecimal.ZERO);
                val.setMoneyProfitRatio(BigDecimal.ZERO);
                val.setCompanyProfitRatio(BigDecimal.ZERO);
                val.setStaffProfitRatio(BigDecimal.ZERO);
            }
        }
        if (dataList.size() > 0 && dataList.get(0) != null && dataList.get(0).getMediaType() == -1) {
            dataList.get(0).setWeekMoneyProfit(weekMoneyProfitTotal);
            dataList.get(0).setWeekMoneyRecharge(weekMoneyRechargeTotal);
        }
        if (params.getExportData() == 1) {
            SessionVO sessionVO = getSessionVO(request);
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }
            ResultVO<?> vo = fastStaffCostIncomeMonthService.exportGroupMediaList(sessionVO, dataList);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return ResultVO.success(dataList);
    }

    @ApiName(value = "business-媒体月份列表数据-(自代投媒体数据)-未使用", folder = {"business"})
    @ApiParamsIn({
            "year:1:int:年",
            "userGroupId:0:int:组id",
            "mediaType:0:int:组id",
            "userId:0:int:用户id"
    })
    @ApiParamsOut({
            "results>>monthStr:月",
            "results>>mediaType:组id",
            "results>>cashCost:现金消耗",
            "results>>cashCostBack:现金返点后消耗",
            "results>>moneyProfit:实到充值",
            "results>>moneyRecharge:总充值",
            "results>>virtualRecharge:虚拟充值",
            "results>>normalRecharge:普通充值",
            "results>>staffIncome:固定成本",
            "results>>totalProfit:总利润",
            "results>>companyProfit:公司利润",
            "results>>staffProfit:组利润"
    })
    @PostMapping("launchMediaMonthList")
    public ResultVO<?> launchMediaMonthList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        Map<Integer, String> mediaMap = new HashMap<>();
        mediaMap.put(-1, "媒体累计");
        mediaMap.put(1, "巨量");
        mediaMap.put(2, "adq");
        mediaMap.put(4, "百度");
        mediaMap.put(5, "快手");
        mediaMap.put(501, "快手native");
        mediaMap.put(502, "快手挂载");
        mediaMap.put(102, "星图");
        String title = "自代投媒体数据";
        if (mediaMap.containsKey(params.getMediaType())) {
            title += "-月数据-" + mediaMap.get(params.getMediaType());
        } else {
            title += params.getUserGroupId() != null ? "-组" : "-累计";
            title += params.getYear() != null ? "月明细" : "媒体明细";
        }

        if (params.getYear() == null) {
            params.setYear(DateUtil.getCurrentYear());
        }
        if (params.getMediaType() != null && params.getMediaType() == -1) {
            params.setMediaType(null);
        }

        Date monthStart = DateUtil.format05(params.getYear() + "01");
        params.setMonthStart(DateUtil.format05Int(monthStart));
        params.setMonthEnd(DateUtil.format05Int(DateUtil.addMonths(monthStart, 11)));
        if (params.getUserGroupId() == null || params.getUserGroupId() == 0) {
            params.setRetailTypes("1,2");
        }
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchGroupMediaMonthDataList(params);

        if (params.getExportData() == 1) {
            SessionVO sessionVO = getSessionVO(request);
            // 限制当前用户操作导出的频次(每个用户同一时间只能导出一个文件)
            String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
            String value = RedisUtil.get(key);
            if (notBlank(value)) {
                return ResultVO.error("您当前有正在导出的任务, 请稍等3~10分钟后再试");
            } else {
                RedisUtil.set(key, "1", RedisUtil.TIME_10M);
            }

            ResultVO<?> vo = fastStaffCostIncomeMonthService.exportGroupMediaMonthData(sessionVO, dataList, title);
            if (!vo.isSuccess()) {
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
            return vo;
        }
        return ResultVO.success(dataList);
    }

    @ApiName(value = "business-获取组用户列表", folder = {"business"})
    @ApiParamsIn({
            "month:0:int:月",
            "year:0:int:年",
            "userGroupId:1:int:组id"
    })
    @ApiParamsOut({
            "results>>userId:用户id",
            "results>>userName:用户名称",
            "results>>retailId:分销id",
            "results>>retailName:分销名称",
            "results>>retailType:分销类型",
            "results>>userGroupId:用户组id",
            "results>>userGroupName:用户组名称"
    })
    @PostMapping("launchGroupUserList")
    public ResultVO<?> launchGroupUserList(HttpServletRequest request, FastStaffCostIncomeMonthPO params) {
        if (params.getMonth() != null) {
            params.setMonth(params.getMonth());
        } else if (StringUtil.isNotEmpty(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format09(params.getMonthStr() + "-01")));
        } else {
            if (params.getYear() == null) {
                params.setMonth(DateUtil.format05Int(DateUtil.getNowDate()));
            }
        }
        params.setRetailTypes("1,2");
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthService.launchGroupUserList(params);
        return ResultVO.success(dataList);
    }

    /**
     * ----任务开始----
     * 分成|结算-比例任务请求
     */
    @ApiName(value = "business-分成|结算-比例任务", folder = {"business"})
    @PostMapping("/nologin/ratio")
    public ResultVO<?> ratio() {
//        fastStaffCostIncomeMonthService.setMediaSettlement();
        fastStaffCostIncomeMonthService.setMediaProfit();
        return ResultVO.success();
    }

    /**
     * 任务请求
     */
    @ApiName(value = "business-查询列表", folder = {"business"})
    @PostMapping("/nologin/fund")
    public ResultVO<?> fund(HttpServletRequest request, @RequestBody FastStaffCostIncomeDayPO params) {
        if (params.getStatDate() == null) {
            params.setStatDate(DateUtil.beginOfDay(DateUtil.getYesterdayDate()));
        }
        if (params.getMediaType() == null) {
            params.setMediaType(1);
        }
        if (params.getContentType() == null) {
            params.setContentType(1);
        }
        log.info(toJSONString(params));
        fastStaffCostIncomeDayService.mediaFund(params);
        return ResultVO.success();
    }

    @ApiName(value = "business-查询列表", folder = {"business"})
    @PostMapping("/nologin/fundMonth")
    public ResultVO<?> fundMonth(HttpServletRequest request, String statDate) {
        Date date = DateUtil.format09(statDate);
        Date startDate = DateUtil.beginOfMonth(date);
        Date endDate = DateUtil.endOfMonth(date);
        fastStaffCostIncomeMonthService.insertUpdateBatch(startDate, date);
        return ResultVO.success();
    }

    @ApiName(value = "business-查询列表", folder = {"business"})
    @PostMapping("/nologin/retailDay")
    public ResultVO<?> retailDay(HttpServletRequest request, String statDate) {
        Date date = DateUtil.format09(statDate);
        Date startDate = DateUtil.beginOfDay(date);
        Date endDate = DateUtil.endOfDay(date);
        fastStaffRetailRechargeDayMapper.insertUpdateBatch(startDate, endDate);
        return ResultVO.success();
    }

    @ApiName(value = "business-查询列表", folder = {"business"})
    @PostMapping("/nologin/retailMonth")
    public ResultVO<?> retailMonth(HttpServletRequest request, String statDate) {
        Date date = DateUtil.format09(statDate);
        Date startDate = DateUtil.beginOfMonth(date);
        Date endDate = DateUtil.endOfMonth(date);
        fastStaffRetailRechargeMonthMapper.insertUpdateBatch(startDate, endDate);
        return ResultVO.success();
    }
}
