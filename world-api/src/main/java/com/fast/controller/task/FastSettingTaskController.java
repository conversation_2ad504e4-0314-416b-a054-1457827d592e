/*
 * Powered By fast.up
 */
package com.fast.controller.task;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticStr;
import com.fast.po.task.FastSettingTaskPO;
import com.fast.service.task.FastSettingTaskService;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastSettingTask")
public class FastSettingTaskController extends BaseController {

    @Autowired
    private FastSettingTaskService fastSettingTaskService;

    @ApiName(value = "task-查询列表", folder = {"task"})
    @PostMapping("/getFastSettingTaskList")
    public ResultVO<?> getList(HttpServletRequest request, FastSettingTaskPO params, PageVO pageVO) {
        return fastSettingTaskService.queryPageList(params, pageVO);
    }

//    @ApiName(value="task-抖小任务查询单个详情",folder= {"task"})
//    @ApiParamsIn({
//    	"miniId:1:int:小程序id"
//    })
//    @ApiParamsOut({
//    	"miniId:小程序id",
//    	"switchSign:签到任务开关0关1开",
//    	"switchAdv:看广告任务开关0关1开",
//    	"switchNew:新手任务开关0关1开",
//    	"switchUsual:日常任务开关0关1开",
//    	"switchPride:成就任务开关0关1开",
//    	"circleAdv:1:看广告周期1天2周3月",
//    	"circleUsual:日常任务周期1天2周3月",
//    	"coinList>>taskType:任务类型 1:签到 2:广告 3:新手 4:日常 5:成就",
//    	"coinList>>markIdx:标记顺序1,2,3,4,5,6;taskType=1或2时-第xxx天/次;taskType=3时-[1保存桌面,2桌面启动,3安全登录];taskType=4时-[1去剧场,2添加5部剧,3邀请5好友,4看一部新剧;taskType=5时-[1看完一整部,2累计看5部]]",
//    	"coinList>>coin:金币数量"
//    })
//    @PostMapping("/getFastSettingTaskDetail")
//    public ResultVO<?> getDetail(HttpServletRequest request, FastSettingTaskPO params) {
//        if (StrUtil.isEmpty(params.getEncryptionId())) {
//            return ResultVO.error(StaticCode.ERROR, StaticStr.ERROR_PARAM);
//        }
//        Integer id = decodeInt(params.getEncryptionId());
//        if (id == null) {
//            return ResultVO.error(StaticCode.ERROR, StaticStr.ERROR_PARAM);
//        }
//        params.setId(id);
//        SessionVO sessionVO = getSessionVO(request);
//        FastSettingTaskPO fastSettingTask = fastSettingTaskService.queryById(params);
//        return ResultVO.success(fastSettingTask);
//    }

    @ApiName(value = "task-抖小任务添加任务配置", folder = {"task"})
    @ApiParamsIn({
            "body:1:str:数据以body的形式传送，结构如下",
            "taskList:1:str:{miniId:123,switchSign:1,switchAdv:1,switchNew:1,switchUsual:1,switchPride:1,circleAdv:1,circleUsual:1,coinList:[{miniId:123,taskType:1,markIdx:1,coin:300}...]}",
            "switchSign:1:int:-签到任务开关0关1开",
            "switchAdv:1:int:-看广告任务开关0关1开",
            "switchNew:1:int:-新手任务开关0关1开",
            "switchUsual:1:int:-日常任务开关0关1开",
            "switchPride:1:int:-成就任务开关0关1开",
            "circleAdv:1:int:-看广告周期1天2周3月",
            "circleUsual:1:int:-日常任务周期1天2周3月",
            "coinList>>taskType:1:int:-任务类型 1:签到 2:广告 3:新手 4:日常 5:成就",
            "coinList>>markIdx:1:int:-标记顺序1,2,3,4,5,6;taskType=1或2时-第xxx天/次;taskType=3时-[1保存桌面,2桌面启动,3安全登录];taskType=4时-[1去剧场,2添加5部剧,3邀请5好友,4看一部新剧;taskType=5时-[1看完一整部,2累计看5部]]",
            "coinList>>coin:1:int:-金币数量",
            "coinList>>switchOn:1:int:开启状态0否1是"
    })
    @ApiParamsOut({"status:ok"})
    @PostMapping("/insertFastSettingTask")
    public ResultVO<?> insert(HttpServletRequest request, @RequestBody FastSettingTaskPO taskPO) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastSettingTaskService.insert(sessionVO, taskPO);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "task-抖小任务更新任务配置", folder = {"task"})
    @ApiParamsIn({
            "body:1:str:数据以body的形式传送，结构如下",
            "taskList:1:str:{encryptionId:xxxx,miniId:123,switchAdv:1,switchNew:1,switchUsual:1,switchPride:1,circleAdv:1,circleUsual:1,coinList:[{id:11,miniId:123,taskType:1,markIdx:1,coin:300}...]}",
            "encryptionId:1:str:加密id",
            "switchSign:1:int:-签到任务开关0关1开",
            "switchAdv:1:int:-看广告任务开关0关1开",
            "switchNew:1:int:-新手任务开关0关1开",
            "switchUsual:1:int:-日常任务开关0关1开",
            "switchPride:1:int:-成就任务开关0关1开",
            "circleAdv:1:int:-看广告周期1天2周3月",
            "circleUsual:1:int:-日常任务周期1天2周3月",
            "coinList>>id:1:int:主键id",
            "coinList>>taskType:1:int:-任务类型 1:签到 2:广告 3:新手 4:日常 5:成就",
            "coinList>>markIdx:1:int:-标记顺序1,2,3,4,5,6;taskType=1或2时-第xxx天/次;taskType=3时-[1保存桌面,2桌面启动,3安全登录];taskType=4时-[1去剧场,2添加5部剧,3邀请5好友,4看一部新剧;taskType=5时-[1看完一整部,2累计看5部]]",
            "coinList>>coin:1:int:-金币数量",
            "coinList>>switchOn:1:int:开启状态0否1是"
    })
    @ApiParamsOut({"status:ok"})
    @PostMapping("/updateFastSettingTask")
    public ResultVO<?> update(HttpServletRequest request, @RequestBody FastSettingTaskPO taskPO) {
        if (isBlank(taskPO.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(taskPO.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        taskPO.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastSettingTaskService.update(sessionVO, taskPO);
        return ResultVO.fromMethodVO(methodVO);
    }
}
