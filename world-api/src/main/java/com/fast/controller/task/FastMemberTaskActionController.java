/*
 * Powered By fast.up
 */
package com.fast.controller.task;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.task.FastMemberTaskActionPO;
import com.fast.service.task.FastMemberTaskActionService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberTaskAction")
public class FastMemberTaskActionController extends BaseController {

    @Autowired
    private FastMemberTaskActionService fastMemberTaskActionService;

    @ApiName(value = "task-查询列表", folder = {"task"})
    @PostMapping("/getFastMemberTaskActionList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberTaskActionPO params, PageVO pageVO) {
        return fastMemberTaskActionService.queryPageList(params, pageVO);
    }

    @ApiName(value = "task-查询单个详情", folder = {"task"})
    @PostMapping("/getFastMemberTaskActionDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberTaskActionPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberTaskActionPO fastMemberTaskAction = fastMemberTaskActionService.queryById(params);
        return ResultVO.success(fastMemberTaskAction);
    }

    @ApiName(value = "task-添加", folder = {"task"})
    @PostMapping("/insertFastMemberTaskAction")
    public ResultVO<?> insert(HttpServletRequest request, FastMemberTaskActionPO params) {
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberTaskActionService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "task-更新", folder = {"task"})
    @PostMapping("/updateFastMemberTaskAction")
    public ResultVO<?> update(HttpServletRequest request, FastMemberTaskActionPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        MethodVO methodVO = fastMemberTaskActionService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
