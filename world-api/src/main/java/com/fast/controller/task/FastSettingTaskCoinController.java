/*
 * Powered By fast.up
 */
package com.fast.controller.task;

import com.fast.annotation.ApiName;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.task.FastSettingTaskCoinPO;
import com.fast.service.task.FastSettingTaskCoinService;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastSettingTaskCoin")
public class FastSettingTaskCoinController extends BaseController {

    @Autowired
    private FastSettingTaskCoinService fastSettingTaskCoinService;

    @ApiName(value = "task-查询列表", folder = {"task"})
    @PostMapping("/getFastSettingTaskCoinList")
    public ResultVO<?> getList(HttpServletRequest request, FastSettingTaskCoinPO params, PageVO pageVO) {
        return fastSettingTaskCoinService.queryPageList(params, pageVO);
    }

    @ApiName(value = "task-查询单个详情", folder = {"task"})
    @PostMapping("/getFastSettingTaskCoinDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastSettingTaskCoinPO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastSettingTaskCoinPO fastSettingTaskCoin = fastSettingTaskCoinService.queryById(params);
        return ResultVO.success(fastSettingTaskCoin);
    }

    @ApiName(value = "task-添加", folder = {"task"})
    @PostMapping("/insertFastSettingTaskCoin")
    public ResultVO<?> insert(HttpServletRequest request, FastSettingTaskCoinPO params) {
        SessionVO sessionVO = getSessionVO(request);
        params.setCreatorId(sessionVO.getUserId());
        MethodVO methodVO = fastSettingTaskCoinService.insert(params);
        return ResultVO.fromMethodVO(methodVO);
    }

    @ApiName(value = "task-更新", folder = {"task"})
    @PostMapping("/updateFastSettingTaskCoin")
    public ResultVO<?> update(HttpServletRequest request, FastSettingTaskCoinPO params) {
        if (isBlank(params.getEncryptionId())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer id = decodeInt(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        params.setUpdatorId(sessionVO.getUserId());
        MethodVO methodVO = fastSettingTaskCoinService.update(params);
        return ResultVO.fromMethodVO(methodVO);
    }
}
