/*
 * Powered By fast.up
 */
package com.fast.controller.task;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.base.BaseController;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.po.task.FastMemberTaskRechargePO;
import com.fast.service.task.FastMemberTaskRechargeService;
import com.fast.utils.StrUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fastMemberTaskRecharge")
public class FastMemberTaskRechargeController extends BaseController {

    @Autowired
    private FastMemberTaskRechargeService fastMemberTaskRechargeService;

    @ApiName(value = "task-福利任务-用户免费赚币记录", folder = {"task"})
    @ApiParamsIn({
            "memberIds:1:str:会员id们，逗号分割",
            "retailIds:1:str:分销商id们，逗号分割",
            "miniIds:1:str:小程序id们，逗号分割",
            "createTimeStart:1:str:开始时间yyyy-MM-DD",
            "createTimeEnd:1:str:结束时间yyyy-MM-DD"
    })
    @ApiParamsOut({
            "memberId:会员id",
            "openid:openid",
            "coinAll:获得K币",
            "coinExpire:过期K币",
            "coinRemain:剩余K币",
            "miniName:小程序名称",
            "miniType:1：微信小程序;2抖音小程序;3H54快手",
            "retailName: 分销商名称"
    })
    @PostMapping("/getFastMemberTaskRechargeList")
    public ResultVO<?> getFastMemberTaskRechargeList(HttpServletRequest request, FastMemberTaskRechargePO params, PageVO pageVO) {
        if (StrUtil.isNotEmpty(params.getCreateTimeStart())) {
            params.setCreateTimeStart(params.getCreateTimeStart() + " 00:00:00");
        }
        if (StrUtil.isNotEmpty(params.getCreateTimeEnd())) {
            params.setCreateTimeEnd(params.getCreateTimeEnd() + " 23:59:59");
        }
        SessionVO sessionVO = getSessionVO(request);
        params.setContentType(sessionVO.getContentType());
        return fastMemberTaskRechargeService.querySummaryPageList(params, pageVO);
    }

    @ApiName(value = "task-福利任务-会员个人-免费赚币记录", folder = {"task"})
    @ApiParamsIn({
            "createTimeStart:1:str:开始时间yyyy-MM-DD",
            "createTimeEnd:1:str:结束时间yyyy-MM-DD",
            "memberId:1:int:会员id"
    })
    @ApiParamsOut({
            "coinRemain:剩余可用币",
            "coinExpire:过期币",
            "coinUsed:已用币",
            "coinAll:总币",
            "list>>remark:免费记录",
            "list>>createTime:操作时间",
            "list>>coinAll:获得K币",
            "list>>miniName:小程序名称",
            "list>>miniType:1：微信小程序;2抖音小程序;3H54快手",
            "list>>retailName: 分销商名称"
    })
    @PostMapping("/getFastMemberOneTaskRechargeList")
    public ResultVO<?> getList(HttpServletRequest request, FastMemberTaskRechargePO params, PageVO pageVO) {
        if (StrUtil.isNotEmpty(params.getCreateTimeStart())) {
            params.setCreateTimeStart(params.getCreateTimeStart() + " 00:00:00");
        }
        if (StrUtil.isNotEmpty(params.getCreateTimeEnd())) {
            params.setCreateTimeEnd(params.getCreateTimeEnd() + " 23:59:59");
        }
        if (params.getMemberId() == null) {
            return ResultVO.error("会员id不能为空");
        }
        return fastMemberTaskRechargeService.queryPageList(params, pageVO);
    }

    @ApiName(value = "task-查询单个详情", folder = {"task"})
    @PostMapping("/getFastMemberTaskRechargeDetail")
    public ResultVO<?> getDetail(HttpServletRequest request, FastMemberTaskRechargePO params) {
        if (StrUtil.isEmpty(params.getEncryptionId())) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        Long id = decodeLong(params.getEncryptionId());
        if (id == null) {
            return ResultVO.error(StaticCode.ERROR, StaticStr.INVALID_PARAM);
        }
        params.setId(id);
        SessionVO sessionVO = getSessionVO(request);
        FastMemberTaskRechargePO fastMemberTaskRecharge = fastMemberTaskRechargeService.queryById(params);
        return ResultVO.success(fastMemberTaskRecharge);
    }

}
