server:
    port: 9085
    shutdown: graceful

# Spring配置
spring:
    profiles:
        active: dev
    application:
        name: world-api
    main:
        allow-circular-references: true
    mvc:
        pathmatch:
            matching-strategy: ANT_PATH_MATCHER


# token配置
token:
    header: access_token


# MybatisPlus配置
mybatis-plus:
    mapper-locations: [ "classpath*:com/fast/mapper/**/*Mapper.xml" ]
    typeAliasesPackage: com.fast.po
    # 自定义TypeHandler
    type-handlers-package: com.fast.framework.typehandler
    global-config:
        banner: false
        db-config:
            id-type: AUTO #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
            insertStrategy: NOT_NULL #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
            updateStrategy: NOT_NULL
            whereStrategy: NOT_NULL
    # 加载全局的配置文件
    configuration:
        cache-enabled: true
        map-underscore-to-camel-case: true


# PageHelper分页插件
pagehelper:
    helperDialect: mysql
    supportMethodsArguments: true
    reasonable: true
    pageSizeZero: true
    params: count=countSql


# 防止XSS攻击
xss:
    # 过滤开关
    enabled: false
    # 排除链接（多个用逗号分隔）
    excludes: /system/notice123/*
    # 匹配链接（多个用逗号分隔）
    urlPatterns: /system/*

javadoc: true
