package com.fast.controller;

import com.fast.framework.response.ResponseResult;
import com.fast.service.make.video.MultiFileDownloadService;
import com.fast.service.make.video.util.MultiFileDownloader.DownloadTask;
import com.fast.utils.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多文件下载控制器
 * 提供多文件下载和打包的Web API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/multi-download")
@Api(tags = "多文件下载API")
public class MultiFileDownloadController {

    @Autowired
    private MultiFileDownloadService multiFileDownloadService;

    /**
     * 下载请求DTO
     */
    public static class DownloadRequest {
        private List<String> urls;
        private String zipFileName;
        private String saveDir;

        // Getters and Setters
        public List<String> getUrls() { return urls; }
        public void setUrls(List<String> urls) { this.urls = urls; }
        public String getZipFileName() { return zipFileName; }
        public void setZipFileName(String zipFileName) { this.zipFileName = zipFileName; }
        public String getSaveDir() { return saveDir; }
        public void setSaveDir(String saveDir) { this.saveDir = saveDir; }
    }

    /**
     * 带文件名的下载请求DTO
     */
    public static class DownloadWithNamesRequest {
        private Map<String, String> urlFileNameMap;
        private String zipFileName;
        private String saveDir;

        // Getters and Setters
        public Map<String, String> getUrlFileNameMap() { return urlFileNameMap; }
        public void setUrlFileNameMap(Map<String, String> urlFileNameMap) { this.urlFileNameMap = urlFileNameMap; }
        public String getZipFileName() { return zipFileName; }
        public void setZipFileName(String zipFileName) { this.zipFileName = zipFileName; }
        public String getSaveDir() { return saveDir; }
        public void setSaveDir(String saveDir) { this.saveDir = saveDir; }
    }

    /**
     * 视频包下载请求DTO
     */
    public static class VideoPackageRequest {
        private String videoUrl;
        private String subtitleUrl;
        private String thumbnailUrl;
        private String zipFileName;
        private String saveDir;

        // Getters and Setters
        public String getVideoUrl() { return videoUrl; }
        public void setVideoUrl(String videoUrl) { this.videoUrl = videoUrl; }
        public String getSubtitleUrl() { return subtitleUrl; }
        public void setSubtitleUrl(String subtitleUrl) { this.subtitleUrl = subtitleUrl; }
        public String getThumbnailUrl() { return thumbnailUrl; }
        public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }
        public String getZipFileName() { return zipFileName; }
        public void setZipFileName(String zipFileName) { this.zipFileName = zipFileName; }
        public String getSaveDir() { return saveDir; }
        public void setSaveDir(String saveDir) { this.saveDir = saveDir; }
    }

    @PostMapping("/download-urls")
    @ApiOperation("下载多个URL文件并打包成ZIP")
    public ResponseResult<Map<String, Object>> downloadUrls(@RequestBody DownloadRequest request) {
        try {
            if (request.getUrls() == null || request.getUrls().isEmpty()) {
                return ResponseResult.error("URL列表不能为空");
            }

            String zipFilePath = multiFileDownloadService.downloadFilesAndZip(
                    request.getUrls(), 
                    request.getZipFileName(), 
                    request.getSaveDir()
            );

            if (zipFilePath == null) {
                return ResponseResult.error("文件下载打包失败");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("zipFilePath", zipFilePath);
            result.put("zipFileName", new File(zipFilePath).getName());
            result.put("fileSize", multiFileDownloadService.getZipFileSize(zipFilePath));
            result.put("downloadCount", request.getUrls().size());

            return ResponseResult.success(result);

        } catch (Exception e) {
            log.error("下载URL文件失败", e);
            return ResponseResult.error("下载失败: " + e.getMessage());
        }
    }

    @PostMapping("/download-with-names")
    @ApiOperation("下载多个URL文件并指定文件名打包成ZIP")
    public ResponseResult<Map<String, Object>> downloadWithNames(@RequestBody DownloadWithNamesRequest request) {
        try {
            if (request.getUrlFileNameMap() == null || request.getUrlFileNameMap().isEmpty()) {
                return ResponseResult.error("URL文件名映射不能为空");
            }

            String zipFilePath = multiFileDownloadService.downloadFilesAndZip(
                    request.getUrlFileNameMap(), 
                    request.getZipFileName(), 
                    request.getSaveDir()
            );

            if (zipFilePath == null) {
                return ResponseResult.error("文件下载打包失败");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("zipFilePath", zipFilePath);
            result.put("zipFileName", new File(zipFilePath).getName());
            result.put("fileSize", multiFileDownloadService.getZipFileSize(zipFilePath));
            result.put("downloadCount", request.getUrlFileNameMap().size());

            return ResponseResult.success(result);

        } catch (Exception e) {
            log.error("下载文件失败", e);
            return ResponseResult.error("下载失败: " + e.getMessage());
        }
    }

    @PostMapping("/download-video-package")
    @ApiOperation("下载视频包（视频+字幕+缩略图）")
    public ResponseResult<Map<String, Object>> downloadVideoPackage(@RequestBody VideoPackageRequest request) {
        try {
            String zipFilePath = multiFileDownloadService.downloadVideoPackage(
                    request.getVideoUrl(),
                    request.getSubtitleUrl(),
                    request.getThumbnailUrl(),
                    request.getZipFileName(),
                    request.getSaveDir()
            );

            if (zipFilePath == null) {
                return ResponseResult.error("视频包下载失败");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("zipFilePath", zipFilePath);
            result.put("zipFileName", new File(zipFilePath).getName());
            result.put("fileSize", multiFileDownloadService.getZipFileSize(zipFilePath));

            return ResponseResult.success(result);

        } catch (Exception e) {
            log.error("下载视频包失败", e);
            return ResponseResult.error("下载失败: " + e.getMessage());
        }
    }

    @GetMapping("/download-file/{fileName}")
    @ApiOperation("下载已生成的ZIP文件")
    public ResponseEntity<Resource> downloadFile(
            @PathVariable @ApiParam("文件名") String fileName,
            @RequestParam(required = false) @ApiParam("文件路径") String filePath) {
        
        try {
            String actualFilePath;
            if (StrUtil.isNotBlank(filePath)) {
                actualFilePath = filePath;
            } else {
                // 默认从临时目录查找
                actualFilePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;
            }

            if (!multiFileDownloadService.isZipFileExists(actualFilePath)) {
                return ResponseEntity.notFound().build();
            }

            File file = new File(actualFilePath);
            Resource resource = new FileSystemResource(file);

            // 设置响应头
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE)
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(file.length()))
                    .body(resource);

        } catch (Exception e) {
            log.error("下载文件失败: {}", fileName, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/file-info/{fileName}")
    @ApiOperation("获取ZIP文件信息")
    public ResponseResult<Map<String, Object>> getFileInfo(
            @PathVariable @ApiParam("文件名") String fileName,
            @RequestParam(required = false) @ApiParam("文件路径") String filePath) {
        
        try {
            String actualFilePath;
            if (StrUtil.isNotBlank(filePath)) {
                actualFilePath = filePath;
            } else {
                actualFilePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;
            }

            if (!multiFileDownloadService.isZipFileExists(actualFilePath)) {
                return ResponseResult.error("文件不存在");
            }

            Map<String, Object> info = new HashMap<>();
            info.put("fileName", fileName);
            info.put("filePath", actualFilePath);
            info.put("fileSize", multiFileDownloadService.getZipFileSize(actualFilePath));
            info.put("exists", true);

            return ResponseResult.success(info);

        } catch (Exception e) {
            log.error("获取文件信息失败: {}", fileName, e);
            return ResponseResult.error("获取文件信息失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete-file/{fileName}")
    @ApiOperation("删除ZIP文件")
    public ResponseResult<String> deleteFile(
            @PathVariable @ApiParam("文件名") String fileName,
            @RequestParam(required = false) @ApiParam("文件路径") String filePath) {
        
        try {
            String actualFilePath;
            if (StrUtil.isNotBlank(filePath)) {
                actualFilePath = filePath;
            } else {
                actualFilePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;
            }

            boolean deleted = multiFileDownloadService.deleteZipFile(actualFilePath);
            
            if (deleted) {
                return ResponseResult.success("文件删除成功");
            } else {
                return ResponseResult.error("文件删除失败");
            }

        } catch (Exception e) {
            log.error("删除文件失败: {}", fileName, e);
            return ResponseResult.error("删除文件失败: " + e.getMessage());
        }
    }
}
