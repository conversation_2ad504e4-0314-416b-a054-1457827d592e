/*
 * Powered By fast.up
 */
package com.fast.po.make;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fast.po.BasePO;
import com.fast.utils.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 内容生产平台成片
 */
@Setter
@Getter
@JsonInclude(Include.NON_NULL)
@TableName("make_completed_film")
public class MakeCompletedFilmPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Integer id; // 主键ID
    @NotBlank(message = "成片名称不能为空")
    @Size(max = 50, message = "成片名称最多输入50位")
    private String name; // 成片名称
    private Integer total; // 总集数
    @NotNull(message = "原片ID不能为空")
    private Integer originalFilmId; // 原片ID
    @JsonIgnore
    private Integer delFlag; // 逻辑删除:0=否;1=是
    private Integer creatorId; // 创建人ID
    private Integer updatorId; // 修改人ID
    @JsonFormat(pattern = DateUtil.sdf07)
    private Date createTime; // 创建时间
    @JsonFormat(pattern = DateUtil.sdf07)
    private Date updateTime; // 更新时间

    // 非数据库字段
    private String createTimeStr; // 创建时间区间（yyyy-MM-dd - yyyy-MM-dd）
    private Date createTimeS; // 创建时间开始
    private Date createTimeE; // 创建时间结束
    private Double[][] region; // 擦除区域坐标，是一个二维数组，两个为一组[[x1,y1],[x2,y2],[x3,y3],[x4,y4]]，一个矩形擦除框有4个点位
    private Integer downVideo; // 下载视频（1、是）
    private List<String> videoLang; // 视频语言集合（00代表无字幕视频）
    private Integer seriesAll; // 视频全集（1、是）
    private Integer seriesStart; // 视频开始集数
    private Integer seriesEnd; // 视频结束集数
    private Integer downCaption; // 下载字幕（1、是）
    private List<String> captionLang; // 字幕语言集合

    @JsonFormat(pattern = DateUtil.sdf07)
    private Date startTime; // 开始时间
    @JsonFormat(pattern = DateUtil.sdf07)
    private Date endTime; // 结束时间
    private Long takeTime; // 耗时（毫秒）

    private Integer totalCount; // 总任务数量
    private Integer completedNum; // 已完成任务数量
    private Integer state; // 状态（0、待生产；1、生产中；2、生产完成；3、生产失败）
    @NotBlank(message = "原始语言不能为空")
    @Size(max = 20, message = "原始语言最多输入20位")
    private String sourceLang; // 原始语言
    @NotNull(message = "目标语言不能为空")
    @Size(max = 100, message = "目标语言列表最多输入100位")
    private String destLangs; // 目标语言列表
    private List<MakeFilmCaptionPO> captions; // 字幕列表
    private Integer removeBgAudio; // 去除背景音（0、否；1、是）
}
