package com.fast.handler;

import com.alibaba.fastjson.JSONObject;
import com.fast.vo.MethodVO;

/**
 * 火山引擎事件处理器接口
 * Created by <PERSON> on 2025/08/02.
 */
public interface VolcengineEventHandler {

    /**
     * 事件类型
     *
     * @return 事件类型
     */
    String eventType();

    /**
     * 处理事件
     *
     * @param data 事件数据（json）
     * @return 处理结果
     */
    MethodVO handle(JSONObject data);

}
