package com.fast.handler;

import com.alibaba.fastjson.JSONObject;
import com.fast.enums.VolFileStateEnum;
import com.fast.po.make.MakeFilePO;
import com.fast.service.make.MakeFileService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 媒体文件上传完成事件处理器
 * Created by <PERSON> on 2025/08/02.
 */
@Slf4j
@Component
public class FileUploadByUrlCompleteEventHandler implements VolcengineEventHandler {

    @Autowired
    private MakeFileService makeFileService;

    @Value("${spring.profiles.active}")
    private String env;

    @Override
    public String eventType() {
        return "FileUploadByUrlComplete";
    }

    @Override
    public MethodVO handle(JSONObject data) {
        Date nowDate = DateUtil.getNowDate();

        // 优先校验env
        String callbackArgs = data.getString("CallbackArgs");
        String[] split = callbackArgs.split("_");
        if (split.length < 2) {
            log.warn("火山回调参数异常，CallbackArgs格式不正确: {}", callbackArgs);
            return MethodVO.error("CallbackArgs is not in the correct format.");
        }

        // 兼容一下历史数据
        String callbackEnv = split[0];
        if (StrUtil.equals(callbackEnv, "test") || StrUtil.equals(callbackEnv, "prod")) {
            if (!StrUtil.equals(callbackEnv, env)) {
                log.warn("火山回调环境不匹配，不予处理，火山: {}，当前: {}", callbackEnv, env);
                return MethodVO.error("No treatment required.");
            }
        }

        // 文件ID
        Integer fileId = Integer.valueOf(split.length > 2 ? split[1] : split[0]);

        MakeFilePO makeFilePO = makeFileService.queryById(fileId);
        if (makeFilePO == null) {
            log.warn("火山回调文件不存在，fileId={}", fileId);
            return MethodVO.error("File not found.");
        }

        String bizCode = data.getString("BizCode");
        String code = data.getString("Code");
        String jobId = data.getString("JobId");

        if (!StrUtil.equals(code, "Success")) {
            log.warn("[URL 批量拉取上传完成] 火山任务状态异常: {}", code);

            makeFilePO.setVolState(VolFileStateEnum.SYNC_FAILED.getCode());
            makeFilePO.setVolCode(code);
            makeFilePO.setVolBizCode(bizCode);
            makeFilePO.setVolJobId(jobId);
            makeFilePO.setVolFailReason(data.getString("Message"));
            makeFilePO.setVolEndTime(nowDate);
            makeFilePO.setVolTakeTime(DateUtil.getDiffTime(makeFilePO.getVolStartTime(), makeFilePO.getVolEndTime()));
            makeFileService.update(makeFilePO);
            log.info("火山媒资文件同步失败，jobId={}，更新文件 {} 的VolState为 失败", jobId, fileId);

            return MethodVO.error("火山回调状态异常: " + code);
        }

        String vid = data.getString("Vid");
        String posterUri = data.getString("PosterUri");

        JSONObject sourceInfo = data.getJSONObject("SourceInfo");
        Double duration = sourceInfo.getDouble("Duration");
        Integer bitrate = sourceInfo.getInteger("Bitrate");
        Integer size = sourceInfo.getInteger("Size");
        Integer width = sourceInfo.getInteger("Width");
        Integer height = sourceInfo.getInteger("Height");

        makeFilePO.setVid(vid);
        makeFilePO.setCover(posterUri);
        makeFilePO.setDuration(duration);
        makeFilePO.setBitrate(bitrate);
        makeFilePO.setSize(size);
        makeFilePO.setWidth(width);
        makeFilePO.setHeight(height);
        makeFilePO.setVolState(VolFileStateEnum.SYNC_COMPLETED.getCode());
        makeFilePO.setVolCode(code);
        makeFilePO.setVolBizCode(bizCode);
        makeFilePO.setVolJobId(jobId);
        makeFilePO.setVolEndTime(nowDate);
        makeFilePO.setVolTakeTime(DateUtil.getDiffTime(makeFilePO.getVolStartTime(), makeFilePO.getVolEndTime()));
        makeFileService.update(makeFilePO);
        log.info("火山媒资文件同步成功，jobId={}，更新文件 {} 的 vid={}", jobId, fileId, vid);

        return MethodVO.success();
    }

}
