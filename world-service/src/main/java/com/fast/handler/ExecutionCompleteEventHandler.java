package com.fast.handler;

import com.alibaba.fastjson.JSONObject;
import com.fast.framework.manager.VThreadManager;
import com.fast.service.make.video.VideoProcessServiceV2;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 媒体任务执行完成事件处理器
 * Created by <PERSON> on 2025/08/02.
 */
@Slf4j
@Component
public class ExecutionCompleteEventHandler implements VolcengineEventHandler {

    @Autowired
    private VideoProcessServiceV2 videoProcessServiceV2;

    @Autowired
    private VThreadManager vThreadManager;

    @Value("${spring.profiles.active}")
    private String env;

    @Override
    public String eventType() {
        return "ExecutionComplete";
    }

    @Override
    public MethodVO handle(JSONObject data) {
        // 优先校验env
        JSONObject control = data.getJSONObject("Control");
        String callbackArgs = control.getString("CallbackArgs");
        String[] split = callbackArgs.split("_");
        if (split.length < 2) {
            log.warn("火山回调参数异常，CallbackArgs格式不正确: {}", callbackArgs);
            return MethodVO.error("CallbackArgs is not in the correct format.");
        }

        // 兼容一下历史数据
        String callbackEnv = split[0];
        if (StrUtil.equals(callbackEnv, "test") || StrUtil.equals(callbackEnv, "prod")) {
            if (!StrUtil.equals(callbackEnv, env)) {
                log.warn("火山回调环境不匹配，不予处理，火山: {}，当前: {}", callbackEnv, env);
                return MethodVO.error("No treatment required.");
            }
        }

        // String status = data.getString("Status");
        // if (!StrUtil.equals(status, "Success")) {
        //     log.warn("[媒体处理任务执行完成] 火山任务状态异常: {}", status);
        //     return MethodVO.error("火山回调状态异常: " + status);
        // }

        JSONObject operation = data.getJSONObject("Operation");
        JSONObject task = operation.getJSONObject("Task");
        String type = task.getString("Type");

        switch (type) {
            case "Asr":
            case "Ocr":
                vThreadManager.executeTaskWithMDC(() -> videoProcessServiceV2.subtitleExtractNotify(data, type));
                break;
            case "Erase":
                vThreadManager.executeTaskWithMDC(() -> videoProcessServiceV2.subtitleRemoveNotify(data));
                break;
            case "AudioExtract":
                break;
            default:
                log.warn("未知火山任务类型: {}", type);
                break;
        }
        return MethodVO.success();
    }

}
