/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.mapper.fee.FastFeeGearRestrictRetailMapper;
import com.fast.po.fee.FastFeeGearRestrictRetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeGearRestrictRetailService extends BaseService {

    @Autowired
    private FastFeeGearRestrictRetailMapper fastFeeGearRestrictRetailMapper;

    /**
     * 通过id查询单个对象
     */
    public FastFeeGearRestrictRetailPO queryById(FastFeeGearRestrictRetailPO params) {
        return fastFeeGearRestrictRetailMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeGearRestrictRetailPO queryById(Integer id) {
        return fastFeeGearRestrictRetailMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeGearRestrictRetailPO queryOne(FastFeeGearRestrictRetailPO params) {
        return fastFeeGearRestrictRetailMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastFeeGearRestrictRetailPO> queryList(FastFeeGearRestrictRetailPO params) {
        return fastFeeGearRestrictRetailMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeGearRestrictRetailPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeGearRestrictRetailPO> list = fastFeeGearRestrictRetailMapper.queryList(params);
        for (FastFeeGearRestrictRetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setEncryptionFrontRestrictId(encode(cur.getFrontRestrictId()));
            cur.setEncryptionAfterRestrictId(encode(cur.getAfterRestrictId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeGearRestrictRetailPO params) {
        return fastFeeGearRestrictRetailMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeGearRestrictRetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastFeeGearRestrictRetailMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(SessionVO sessionVO, List<FastFeeGearRestrictRetailPO> list) {

        if (list.size() < 1) {
            return MethodVO.error("新增内容不能为空");
        }
        List<FastFeeGearRestrictRetailPO> toSaveList = new ArrayList<>();
        Date timeNow = DateUtil.getNowDate();
        Integer dramaId = 0;
        for (FastFeeGearRestrictRetailPO item : list) {
            List<Integer> retailIdList = CollUtil.parseIntStr2List(item.getRetailIds());
            for (Integer retailId : retailIdList) {
                FastFeeGearRestrictRetailPO rrPO = new FastFeeGearRestrictRetailPO();
                BeanUtils.copyProperties(item, rrPO);
                rrPO.setRetailId(retailId);
                rrPO.setCreatorId(sessionVO.getUserId());
                rrPO.setUpdatorId(sessionVO.getUserId());
                rrPO.setCreateTime(timeNow);
                rrPO.setUpdateTime(timeNow);
                rrPO.setDelFlag(0);
                dramaId = rrPO.getDramaId();
                toSaveList.add(rrPO);
            }
        }
        // 删除dramaId关连的全部限制规则
        fastFeeGearRestrictRetailMapper.delByDramaId(dramaId);
        if (toSaveList.size() > 0) {
            if (fastFeeGearRestrictRetailMapper.insertBatch(toSaveList) > 0) {
                return MethodVO.success("成功新增" + toSaveList.size() + "条");
            } else {
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
        }
        return MethodVO.error("新增0条记录");
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateBatch(SessionVO sessionVO, List<FastFeeGearRestrictRetailPO> list) {
        if (list.size() < 1) {
            return MethodVO.error("设置内容不能为空");
        }
        Date timeNow = DateUtil.getNowDate();
        for (FastFeeGearRestrictRetailPO item : list) {
            Integer id = decodeInt(item.getEncryptionId());
            if (id != null) {
                item.setId(id);
                item.setUpdateTime(timeNow);
                item.setUpdatorId(sessionVO.getUserId());
                fastFeeGearRestrictRetailMapper.updateById(item);
            }
        }
        return MethodVO.success("设置成功");
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeGearRestrictRetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastFeeGearRestrictRetailMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
