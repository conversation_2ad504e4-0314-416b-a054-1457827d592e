/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.mapper.fee.FastFeeKeepDetailMapper;
import com.fast.po.fee.FastFeeKeepDetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeKeepDetailService extends BaseService {

    @Autowired
    private FastFeeKeepDetailMapper fastFeeKeepDetailMapper;

    /**
     * 通过id查询单个对象
     */
    public FastFeeKeepDetailPO queryById(FastFeeKeepDetailPO item) {
        return fastFeeKeepDetailMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeKeepDetailPO queryById(Integer id) {
        return fastFeeKeepDetailMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeKeepDetailPO queryOne(FastFeeKeepDetailPO item) {
        return fastFeeKeepDetailMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeKeepDetailPO> queryList(FastFeeKeepDetailPO item) {
        return fastFeeKeepDetailMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeKeepDetailPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeKeepDetailPO> list = fastFeeKeepDetailMapper.queryList(item);
        for (FastFeeKeepDetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeKeepDetailPO item) {
        return fastFeeKeepDetailMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeKeepDetailPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastFeeKeepDetailMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFeeKeepDetailPO> list) {
        if (fastFeeKeepDetailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeKeepDetailPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastFeeKeepDetailMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
