/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.fee.FastFeeRuleMapper;
import com.fast.po.fee.FastFeeRulePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.fee.FastFeeRuleVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 计费规则
 *
 * <AUTHOR>
 */
@Service
public class FastFeeRuleService extends BaseService {

    @Autowired
    private FastFeeRuleMapper fastFeeRuleMapper;

    /**
     * 缓存查询计费规则
     */
    public FastFeeRuleVO queryLinkByRedis(Integer linkId, Integer dramaId) {

        FastFeeRuleVO vo = new FastFeeRuleVO();
        String key = StaticVar.FEE_LINKID_RULE_ID + linkId + StaticVar.UNDERLINE + dramaId;
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            vo = JsonUtil.toJavaObject(value, FastFeeRuleVO.class);
        } else {
            FastFeeRulePO item = new FastFeeRulePO();
            item.setLinkId(linkId);
            item.setDramaId(dramaId);
            FastFeeRulePO po = fastFeeRuleMapper.queryLinkOne(item);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                BeanUtils.copyProperties(po, vo);
                vo.setEncryptionId(encode(vo.getId()));
                vo.setSkipSeries(vo.getSkipSeriesDef());// 是否跳级只能通过平台配置
                RedisUtil.set(key, JsonUtil.toString(vo), RedisUtil.TIME_2D);
            }
        }
        return vo;
    }

    public FastFeeRuleVO queryInfoByRedis(Integer officialId, Integer dramaId) {
        FastFeeRulePO item = new FastFeeRulePO();
        item.setOfficialId(officialId);
        item.setDramaId(dramaId);
        FastFeeRuleVO vo = new FastFeeRuleVO();
        String key = StaticVar.FEE_RULE_ID + officialId + StaticVar.UNDERLINE + dramaId;
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            vo = JsonUtil.toJavaObject(value, FastFeeRuleVO.class);
        } else {
            FastFeeRulePO po = fastFeeRuleMapper.queryOne(item);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                BeanUtils.copyProperties(po, vo);
                vo.setEncryptionId(encode(vo.getId()));
                vo.setSkipSeries(vo.getSkipSeriesDef());// 是否跳级只能通过平台配置
                RedisUtil.set(key, JsonUtil.toString(vo), RedisUtil.TIME_2D);
            }
        }
        return vo;
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeRulePO queryById(FastFeeRulePO item) {
        return fastFeeRuleMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeRulePO queryById(Integer id) {
        FastFeeRulePO itemParam = new FastFeeRulePO();
        itemParam.setId(id);
        return fastFeeRuleMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeRulePO queryOne(FastFeeRulePO item) {
        return fastFeeRuleMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeRulePO> queryList(FastFeeRulePO item) {
        return fastFeeRuleMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeRulePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeRulePO> list = fastFeeRuleMapper.queryList(item);
        for (FastFeeRulePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> getDramaStartNum(FastFeeRulePO item) {
        Set<Integer> startNums = fastFeeRuleMapper.getDramaStartNum(item);
        List<Integer> list = new ArrayList<>(startNums);
        Collections.sort(list);
        return ResultVO.success(list);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeRulePO item) {
        return fastFeeRuleMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeRulePO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setRetailId(0);
        item.setOfficialId(0);
        if (fastFeeRuleMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeRulePO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastFeeRuleMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateStartNum(FastFeeRulePO item) {
        Date timeNow = DateUtil.getNowDate();
        FastFeeRulePO rulePO = new FastFeeRulePO();
        rulePO.setUpdatorId(item.getUpdatorId());
        rulePO.setDramaId(item.getDramaId());
        rulePO.setRetailId(item.getRetailId());
        rulePO.setOfficialId(item.getOfficialId());
        rulePO.setStartNum(item.getStartNum());
        rulePO.setUpdateTime(timeNow);
        if (fastFeeRuleMapper.updateByDramaId(rulePO) == 0) {
            // 判断是否存在
            FastFeeRulePO feeRuleParam = new FastFeeRulePO();
            feeRuleParam.setDramaId(rulePO.getDramaId());
            feeRuleParam.setRetailId(rulePO.getRetailId());
            feeRuleParam.setOfficialId(rulePO.getOfficialId());
            FastFeeRulePO feeItem = fastFeeRuleMapper.queryOne(feeRuleParam);
            if (feeItem == null) {
                FastFeeRuleVO feeRuleDef = queryInfoByRedis(0, item.getDramaId());
                // 新增一条
                rulePO.setCoinPer(feeRuleDef.getCoinPer());
                rulePO.setFollowNum(feeRuleDef.getFollowNum());
                rulePO.setCreateTime(timeNow);
                rulePO.setCreatorId(item.getCreatorId());
                if (fastFeeRuleMapper.insertSelective(rulePO) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.UPDATE_FAILED);
                }
            } else {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateFollowNum(FastFeeRulePO item) {
        Date timeNow = DateUtil.getNowDate();
        FastFeeRulePO rulePO = new FastFeeRulePO();
        rulePO.setDramaId(item.getDramaId());
        rulePO.setUpdatorId(item.getUpdatorId());
        rulePO.setRetailId(item.getRetailId());
        rulePO.setOfficialId(item.getOfficialId());
        rulePO.setFollowNum(item.getFollowNum());
        rulePO.setUpdateTime(timeNow);
        if (fastFeeRuleMapper.updateByDramaId(rulePO) == 0) {
            // 判断是否存在
            FastFeeRulePO feeRuleParam = new FastFeeRulePO();
            feeRuleParam.setDramaId(rulePO.getDramaId());
            feeRuleParam.setRetailId(rulePO.getRetailId());
            feeRuleParam.setOfficialId(rulePO.getOfficialId());
            FastFeeRulePO feeItem = fastFeeRuleMapper.queryOne(feeRuleParam);
            if (feeItem == null) {
                FastFeeRuleVO feeRuleDef = queryInfoByRedis(0, item.getDramaId());
                // 新增一条
                rulePO.setCoinPer(feeRuleDef.getCoinPer());
                rulePO.setStartNum(feeRuleDef.getStartNum());
                rulePO.setCreateTime(timeNow);
                rulePO.setCreatorId(item.getCreatorId());
                if (fastFeeRuleMapper.insertSelective(rulePO) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.UPDATE_FAILED);
                }
            } else {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateCoinPer(FastFeeRulePO item) {
        Date timeNow = DateUtil.getNowDate();
        FastFeeRulePO rulePO = new FastFeeRulePO();
        rulePO.setUpdatorId(item.getUpdatorId());
        rulePO.setDramaId(item.getDramaId());
        rulePO.setRetailId(item.getRetailId());
        rulePO.setOfficialId(item.getOfficialId());
        rulePO.setCoinPer(item.getCoinPer());
        rulePO.setUpdateTime(timeNow);
        if (fastFeeRuleMapper.updateByDramaId(rulePO) == 0) {
            // 判断是否存在
            FastFeeRulePO feeRuleParam = new FastFeeRulePO();
            feeRuleParam.setDramaId(rulePO.getDramaId());
            feeRuleParam.setRetailId(rulePO.getRetailId());
            feeRuleParam.setOfficialId(rulePO.getOfficialId());
            FastFeeRulePO feeItem = fastFeeRuleMapper.queryOne(feeRuleParam);
            if (feeItem == null) {
                FastFeeRuleVO feeRuleDef = queryInfoByRedis(0, item.getDramaId());
                // 新增一条
                rulePO.setCreateTime(timeNow);
                rulePO.setCreatorId(item.getCreatorId());
                rulePO.setStartNum(feeRuleDef.getStartNum());
                rulePO.setFollowNum(feeRuleDef.getFollowNum());
                if (fastFeeRuleMapper.insertSelective(rulePO) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.UPDATE_FAILED);
                }
            } else {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        return MethodVO.success();
    }
}
