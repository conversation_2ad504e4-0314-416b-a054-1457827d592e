/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.mapper.fee.FastFeeKeepDetailTempMapper;
import com.fast.po.fee.FastFeeKeepDetailTempPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeKeepDetailTempService extends BaseService {

    @Autowired
    private FastFeeKeepDetailTempMapper fastFeeKeepDetailTempMapper;

    /**
     * 通过id查询单个对象
     */
    public FastFeeKeepDetailTempPO queryById(FastFeeKeepDetailTempPO item) {
        return fastFeeKeepDetailTempMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeKeepDetailTempPO queryById(Integer id) {
        return fastFeeKeepDetailTempMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeKeepDetailTempPO queryOne(FastFeeKeepDetailTempPO item) {
        return fastFeeKeepDetailTempMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeKeepDetailTempPO> queryList(FastFeeKeepDetailTempPO item) {
        return fastFeeKeepDetailTempMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeKeepDetailTempPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeKeepDetailTempPO> list = fastFeeKeepDetailTempMapper.queryList(item);
        for (FastFeeKeepDetailTempPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeKeepDetailTempPO item) {
        return fastFeeKeepDetailTempMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeKeepDetailTempPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastFeeKeepDetailTempMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFeeKeepDetailTempPO> list) {
        if (fastFeeKeepDetailTempMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeKeepDetailTempPO item) {
        if (fastFeeKeepDetailTempMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
