/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.fee.FastFeeModelDetailMapper;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.fee.FastFeeModelDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeModelDetailService extends BaseService {

    @Autowired
    private FastFeeModelDetailMapper fastFeeModelDetailMapper;

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelDetailVO queryInfoByRedis(FastFeeModelDetailPO item) {
        if (item.getId() == null) {
            return null;
        }
        FastFeeModelDetailVO modelDetailVO = new FastFeeModelDetailVO();
        String key = StaticVar.MODEL_DETAIL_ID + item.getId();
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            modelDetailVO = JsonUtil.toJavaObject(value, FastFeeModelDetailVO.class);
        } else {
            FastFeeModelDetailPO modelDetailPO = fastFeeModelDetailMapper.queryById(item);
            if (modelDetailPO == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                BeanUtils.copyProperties(modelDetailPO, modelDetailVO);
                RedisUtil.set(key, JsonUtil.toString(modelDetailVO), RedisUtil.TIME_2D);
            }
        }
        return modelDetailVO;
    }

    public FastFeeModelDetailPO queryByIdRedis(Integer id) {
        if (id == null) {
            return null;
        }
        String key = StaticVar.MODEL_DETAIL_PO_ID + id;
        String value = RedisUtil.get(key);
        FastFeeModelDetailPO modelDetailPO = null;
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            modelDetailPO = JsonUtil.toJavaObject(value, FastFeeModelDetailPO.class);
        } else {
            modelDetailPO = fastFeeModelDetailMapper.queryById(id);
            if (modelDetailPO == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_30M);
                return null;
            } else {
                RedisUtil.set(key, JsonUtil.toString(modelDetailPO), RedisUtil.TIME_2H);
            }
        }
        return modelDetailPO;
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelDetailVO queryInfoByRedis(Integer id) {
        FastFeeModelDetailPO itemParam = new FastFeeModelDetailPO();
        itemParam.setId(id);
        return queryInfoByRedis(itemParam);
    }

    /**
     * 通过ids查询多个对象
     */
    public List<FastFeeModelDetailVO> queryInfoByRedis(String ids) {
        List<FastFeeModelDetailVO> result = new ArrayList<>();
        if (notBlank(ids)) {
            FastFeeModelDetailPO itemParam = new FastFeeModelDetailPO();
            List<Integer> idList = CollUtil.parseIntStr2List(ids);
            for (Integer id : idList) {
                itemParam.setId(id);
                FastFeeModelDetailVO vo = queryInfoByRedis(itemParam);
                if (vo != null) {
                    result.add(vo);
                }
            }
        }
        return result;
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelDetailPO queryById(FastFeeModelDetailPO item) {
        return fastFeeModelDetailMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelDetailPO queryById(Integer id) {
        FastFeeModelDetailPO itemParam = new FastFeeModelDetailPO();
        itemParam.setId(id);
        return fastFeeModelDetailMapper.queryById(itemParam);
    }


    /**
     * 通过条件查询单个对象
     */
    public FastFeeModelDetailPO queryOne(FastFeeModelDetailPO item) {
        return fastFeeModelDetailMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeModelDetailPO> queryList(FastFeeModelDetailPO item) {
        return fastFeeModelDetailMapper.queryList(item);
    }

    /**
     * 查询全部
     */
    public List<Integer> queryModelDetailIds(FastFeeModelDetailPO item) {
        return fastFeeModelDetailMapper.queryModelDetailIds(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeModelDetailPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeModelDetailPO> list = fastFeeModelDetailMapper.queryList(item);
        for (FastFeeModelDetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeModelDetailPO item) {
        return fastFeeModelDetailMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeModelDetailPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastFeeModelDetailMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFeeModelDetailPO> list) {
        if (fastFeeModelDetailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeModelDetailPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastFeeModelDetailMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
