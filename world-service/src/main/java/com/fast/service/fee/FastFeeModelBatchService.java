/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.enums.OperationLogEnum;
import com.fast.mapper.common.FastOperationLogMapper;
import com.fast.mapper.fee.FastFeeModelBatchMapper;
import com.fast.mapper.fee.FastFeeModelBatchUserMapper;
import com.fast.mapper.fee.FastFeeModelDetailMapper;
import com.fast.mapper.user.FastUserMapper;
import com.fast.po.common.FastLogOperationPO;
import com.fast.po.fee.FastFeeModelBatchPO;
import com.fast.po.fee.FastFeeModelBatchUserPO;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.po.retail.FastRetailMiniPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.FastOperationLogService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.retail.FastRetailMiniService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.IPUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeModelBatchService extends BaseService {

    @Autowired
    private FastFeeModelBatchMapper feeModelBatchMapper;
    @Autowired
    private FastFeeModelDetailMapper feeModelDetailMapper;
    @Autowired
    private FastFeeModelBatchUserMapper feeModelBatchUserMapper;
    @Autowired
    private FastOperationLogMapper operationLogMapper;
    @Autowired
    private FastUserMapper fastUserMapper;
    @Autowired
    private FastOperationLogService operationLogService;
    @Autowired
    private FastRetailMiniService fastRetailMiniService;
    @Autowired
    private FastMiniService fastMiniService;

    /**
     * 自定义全局查询
     *
     * @param params
     * @return
     */
    public ResultVO<Map<String, Object>> queryBatchDetailCustom(FastFeeModelBatchPO params) {
        FastFeeModelBatchPO fastFeeModelBatchPO = new FastFeeModelBatchPO();
        fastFeeModelBatchPO.setOfficialId(params.getOfficialId());
        fastFeeModelBatchPO.setGlobal(1);
        fastFeeModelBatchPO.setDelFlag(0);
        FastFeeModelBatchPO batchPO = feeModelBatchMapper.queryOne(fastFeeModelBatchPO);
        if (batchPO == null) {
            return ResultVO.success("不存在");
        }
        batchPO.setCopySearchFlag(params.getCopySearchFlag());
        return queryBatchDetail(batchPO);
    }

    public ResultVO queryBatchDetailCustomByMiniId(FastFeeModelBatchPO params) {
        FastRetailMiniPO query = new FastRetailMiniPO();
        query.setMiniIds(params.getMiniId().toString());
        List<Integer> retailIds = fastRetailMiniService.queryRetailIdList(query);
        if (CollUtil.isEmpty(retailIds)) {
            return ResultVO.success();
        }
        // 查询自定义模板
        params.setRetailIds(StrUtil.joinNoRepeat(retailIds));
        // params.setGlobal(0); //非全局
        // params.setCommonFlag(0); //不是通用
        params.setDelFlag(0);
        if (params.getMiniId() != null) {
            FastMiniVO miniVO = fastMiniService.queryInfoByRedis(params.getMiniId());
            if (miniVO != null && miniVO.getType() == 2) {
                // 抖小，设置虚拟公众号筛选
                params.setOfficialId(miniVO.getDefOfficialId());
            }
        }
        List<FastFeeModelBatchPO> list = feeModelBatchMapper.queryModelBatchSelectList(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.success();
        }
        return ResultVO.success(list);
    }

    /**
     * 查询批次详情
     *
     * @param params
     * @return
     */
    public ResultVO<Map<String, Object>> queryBatchDetail(FastFeeModelBatchPO params) {
        FastFeeModelBatchPO fastFeeModelBatchPO = new FastFeeModelBatchPO();
        fastFeeModelBatchPO.setId(params.getId());
        FastFeeModelBatchPO batchPO = feeModelBatchMapper.queryById(fastFeeModelBatchPO);
        batchPO.setEncryptionId(encode(batchPO.getId()));
        FastFeeModelDetailPO itemParam = new FastFeeModelDetailPO();
        itemParam.setModelBatchId(params.getId());
        List<FastFeeModelDetailPO> detailList = feeModelDetailMapper.queryBatchDetail(itemParam);
        long count = detailList.stream().filter(item -> {
            return Objects.nonNull(item.getContractFlag()) && item.getContractFlag() == 1;
        }).count();
        if (Objects.nonNull(params.getCopySearchFlag()) && params.getCopySearchFlag() == 1) {
            detailList = detailList.stream().filter(item -> {
                return !(Objects.nonNull(item.getContractFlag()) && item.getContractFlag() == 1);
            }).collect(Collectors.toList());
        }
        List<FastFeeModelDetailPO> feeUnrechargeList = new ArrayList<>();
        List<FastFeeModelDetailPO> feeRechargeList = new ArrayList<>();
        List<FastFeeModelDetailPO> vipUnrechargeList = new ArrayList<>();
        List<FastFeeModelDetailPO> vipRechargeList = new ArrayList<>();
        for (FastFeeModelDetailPO detail : detailList) {
            if (detail.getFeeModelType() == 1 && detail.getModelType() == 0) {
                feeUnrechargeList.add(detail);
            } else if (detail.getFeeModelType() == 1 && detail.getModelType() == 1) {
                feeRechargeList.add(detail);
            } else if (detail.getFeeModelType() == 2 && detail.getModelType() == 0) {
                vipUnrechargeList.add(detail);
            } else if (detail.getFeeModelType() == 2 && detail.getModelType() == 1) {
                vipRechargeList.add(detail);
            }
        }
        Map<String, Object> results = ResultVO.getMap();
        results.put("feeUnrechargeList", feeUnrechargeList);
        results.put("feeRechargeList", feeRechargeList);
        results.put("vipUnrechargeList", vipUnrechargeList);
        results.put("vipRechargeList", vipRechargeList);
        results.put("batch", batchPO);
        results.put("contractCount", count);
        return ResultVO.success(results);
    }

    // 关联用户
    @Transactional
    public MethodVO relateUser(HttpServletRequest request, FastFeeModelBatchPO params) {
        List<Integer> userIdList = CollUtil.parseIntStr2List(params.getUserIds());
        Set<Integer> idSet = new HashSet<>();
        Date nowDate = DateUtil.getNowDate();
        for (Integer userId : userIdList) {
            FastFeeModelBatchUserPO feeUserParam = new FastFeeModelBatchUserPO();
            feeUserParam.setModelBatchId(params.getId());
            feeUserParam.setUserId(userId);
            // 判断是否存在
            FastFeeModelBatchUserPO batchUser = feeModelBatchUserMapper.queryOne(feeUserParam);
            if (batchUser == null) {
                FastFeeModelBatchUserPO item = new FastFeeModelBatchUserPO();
                item.setUserId(userId);
                item.setModelBatchId(params.getId());
                item.setCreateTime(nowDate);
                item.setCreatorId(params.getCreatorId());
                feeModelBatchUserMapper.insertSelective(item);

                idSet.add(userId);
            }
        }

        doLog(request, params, nowDate, idSet);
        return MethodVO.success();
    }

    /**
     * 记录操作日志
     *
     * @param request
     * @param params
     * @param nowDate
     * @param idSet
     */
    private void doLog(HttpServletRequest request, FastFeeModelBatchPO params, Date nowDate, Set<Integer> idSet) {
        FastFeeModelBatchPO dbData = feeModelBatchMapper.queryById(params.getId());
        FastLogOperationPO operationPO = new FastLogOperationPO();
        operationPO.setType(OperationLogEnum.OP_13.index);
        operationPO.setRetailId(params.getRetailId());
        operationPO.setObject(dbData.getId() + "-" + dbData.getBatchName());// 操作对象
        operationPO.setObjectId(dbData.getId());// 操作对象ID
        operationPO.setObjectType(2);
        operationPO.setIp(IPUtil.ipToLong(request));
        operationPO.setUserId(params.getUpdatorId());
        operationPO.setCreateTime(nowDate);
        operationPO.setOfficialId(params.getOfficialId());
        if (CollUtil.hasContent(idSet)) {
            List<String> name = fastUserMapper.queryNameByIds(StrUtil.join(idSet));
            operationPO.setRemark("增加关联优化师：" + StrUtil.join(name));// 修改内容
            operationLogService.insertLog(operationPO);
        }
    }

    /**
     * 取消关联用户
     *
     * @param request
     * @param params
     * @return
     */
    @Transactional
    public MethodVO relateCancelUser(HttpServletRequest request, FastFeeModelBatchPO params) {
        List<Integer> userIdList = CollUtil.parseIntStr2List(params.getUserIds());
        for (Integer userId : userIdList) {
            FastFeeModelBatchUserPO item = new FastFeeModelBatchUserPO();
            item.setUserId(userId);
            item.setModelBatchId(params.getId());
            feeModelBatchUserMapper.delete(item);
        }

        doLog(request, params, userIdList);
        return MethodVO.success();
    }

    /**
     * 记录操作日志
     *
     * @param request
     * @param params
     * @param userIdList
     */
    private void doLog(HttpServletRequest request, FastFeeModelBatchPO params, List<Integer> userIdList) {
        Date nowDate = DateUtil.getNowDate();
        FastFeeModelBatchPO dbData = feeModelBatchMapper.queryById(params.getId());
        FastLogOperationPO operationPO = new FastLogOperationPO();
        operationPO.setType(OperationLogEnum.OP_13.index);
        operationPO.setRetailId(params.getRetailId());
        operationPO.setObject(dbData.getId() + "-" + dbData.getBatchName());// 操作对象
        operationPO.setObjectId(dbData.getId());// 操作对象ID
        operationPO.setObjectType(2);
        operationPO.setIp(IPUtil.ipToLong(request));
        operationPO.setUserId(params.getUpdatorId());
        operationPO.setCreateTime(nowDate);
        operationPO.setOfficialId(params.getOfficialId());
        if (CollUtil.hasContent(userIdList)) {
            List<String> name = fastUserMapper.queryNameByIds(StrUtil.join(userIdList));
            operationPO.setRemark("取消关联优化师：" + StrUtil.join(name));// 修改内容
            operationLogService.insertLog(operationPO);
        }
    }

    @Transactional
    public MethodVO updateState(HttpServletRequest request, FastFeeModelBatchPO params) {
        FastFeeModelBatchPO dbData = feeModelBatchMapper.queryById(params.getId());
        if (feeModelBatchMapper.updateState(params) == 0) {
            return MethodVO.error("更新失败");
        }

        Date nowDate = DateUtil.getNowDate();

        doLog(request, params, dbData, nowDate);
        return MethodVO.success();
    }

    /**
     * 记录操作日志
     *
     * @param request
     * @param params
     * @param dbData
     * @param nowDate
     */
    private void doLog(HttpServletRequest request, FastFeeModelBatchPO params, FastFeeModelBatchPO dbData, Date nowDate) {
        FastLogOperationPO operationPO = new FastLogOperationPO();
        operationPO.setType(OperationLogEnum.OP_13.index);
        operationPO.setRetailId(params.getRetailId());
        operationPO.setObject(dbData.getId() + "-" + dbData.getBatchName());// 操作对象
        operationPO.setObjectId(dbData.getId());// 操作对象ID
        operationPO.setObjectType(2);
        operationPO.setIp(IPUtil.ipToLong(request));
        operationPO.setUserId(params.getUpdatorId());
        operationPO.setCreateTime(nowDate);
        operationPO.setOfficialId(params.getOfficialId());
        if (params.getState() != null) {
            if (!StrUtil.equals(dbData.getState(), params.getState())) {
                if (params.getState() == 1) {
                    operationPO.setRemark("操作启用");// 修改内容
                }
                if (params.getState() == 0) {
                    operationPO.setRemark("操作禁用");// 修改内容
                }
                operationLogService.insertLog(operationPO);
            }
        }
    }

    @Transactional
    public MethodVO deleteBatch(HttpServletRequest request, FastFeeModelBatchPO params) {
        if (feeModelBatchMapper.delModelBatchByIds(params) == 0) {
            return MethodVO.error("删除失败");
        }

        doLog(request, params);

        return MethodVO.success();
    }

    /**
     * 记录操作日志
     *
     * @param request
     * @param params
     */
    private void doLog(HttpServletRequest request, FastFeeModelBatchPO params) {
        Date nowDate = DateUtil.getNowDate();

        List<Integer> ids = CollUtil.parseIntStr2List(params.getIds());
        for (Integer id : ids) {
            FastFeeModelBatchPO dbData = feeModelBatchMapper.queryById(id);
            FastLogOperationPO operationPO = new FastLogOperationPO();
            operationPO.setType(OperationLogEnum.OP_13.index);
            operationPO.setRetailId(params.getRetailId());
            operationPO.setObject(dbData.getId() + "-" + dbData.getBatchName());// 操作对象
            operationPO.setObjectId(dbData.getId());// 操作对象ID
            operationPO.setObjectType(2);
            operationPO.setIp(IPUtil.ipToLong(request));
            operationPO.setUserId(params.getUpdatorId());
            operationPO.setCreateTime(nowDate);
            operationPO.setOfficialId(params.getOfficialId());
            if (!StrUtil.equals(dbData.getState(), params.getState())) {
                operationPO.setRemark("操作删除");// 修改内容
                operationLogService.insertLog(operationPO);
            }
        }
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelBatchPO queryById(FastFeeModelBatchPO item) {
        return feeModelBatchMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelBatchPO queryById(Integer id) {
        FastFeeModelBatchPO itemParam = new FastFeeModelBatchPO();
        itemParam.setId(id);
        return feeModelBatchMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeModelBatchPO queryOne(FastFeeModelBatchPO item) {
        return feeModelBatchMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeModelBatchPO> queryList(FastFeeModelBatchPO item) {
        return feeModelBatchMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeModelBatchPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeModelBatchPO> list = feeModelBatchMapper.queryList(item);
        for (FastFeeModelBatchPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryCommonPageList(FastFeeModelBatchPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeModelBatchPO> list = feeModelBatchMapper.queryCommonList(item);
        for (FastFeeModelBatchPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    public ResultVO<?> queryBatchOfficialPageList(FastFeeModelBatchPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeModelBatchPO> list = feeModelBatchMapper.queryBatchOfficialList(item);
        for (FastFeeModelBatchPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeModelBatchPO item) {
        return feeModelBatchMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeModelBatchPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (feeModelBatchMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFeeModelBatchPO> list) {
        if (feeModelBatchMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeModelBatchPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (feeModelBatchMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
