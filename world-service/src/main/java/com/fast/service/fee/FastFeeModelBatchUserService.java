/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.mapper.fee.FastFeeModelBatchUserMapper;
import com.fast.po.fee.FastFeeModelBatchUserPO;
import com.fast.service.base.BaseService;
import com.fast.service.user.FastUserService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.fee.FastFeeModelBatchUserVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeModelBatchUserService extends BaseService {

    @Autowired
    private FastFeeModelBatchUserMapper fastFeeModelBatchUserMapper;
    @Autowired
    private FastUserService userService;

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelBatchUserPO queryById(FastFeeModelBatchUserPO item) {
        return fastFeeModelBatchUserMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelBatchUserPO queryById(Integer id) {
        FastFeeModelBatchUserPO itemParam = new FastFeeModelBatchUserPO();
        itemParam.setId(id);
        return fastFeeModelBatchUserMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeModelBatchUserPO queryOne(FastFeeModelBatchUserPO item) {
        return fastFeeModelBatchUserMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeModelBatchUserPO> queryList(FastFeeModelBatchUserPO item) {
        return fastFeeModelBatchUserMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(SessionVO sessionVO, FastFeeModelBatchUserPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeModelBatchUserPO> list = fastFeeModelBatchUserMapper.queryList(item);
        for (FastFeeModelBatchUserPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setRoleNames(userService.getRoleNamesByIds(sessionVO.getRetailId(), cur.getRoleIds()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 根据优化师查询充值规则模板列表
     */
    public ResultVO<?> queryPageListByUserId(SessionVO sessionVO, FastFeeModelBatchUserPO params, PageVO pageVO) {
        pageVO.setLimit(1000);
        startPage(pageVO);
        FastFeeModelBatchUserVO FastFeeModelBatchUserVO = new FastFeeModelBatchUserVO();
        FastFeeModelBatchUserVO.setUserId(params.getUserId());
        FastFeeModelBatchUserVO.setOfficialId(params.getOfficialId());
        List<FastFeeModelBatchUserVO> list = fastFeeModelBatchUserMapper.queryListByUserId(FastFeeModelBatchUserVO);
        for (FastFeeModelBatchUserVO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setRoleNames(userService.getRoleNamesByIds(sessionVO.getRetailId(), cur.getRoleIds()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeModelBatchUserPO item) {
        return fastFeeModelBatchUserMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeModelBatchUserPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastFeeModelBatchUserMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFeeModelBatchUserPO> list) {
        if (fastFeeModelBatchUserMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeModelBatchUserPO item) {
        if (fastFeeModelBatchUserMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
