/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.OperationLogEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.fee.*;
import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.mapper.promote.FastLinkMapper;
import com.fast.po.common.FastLogOperationPO;
import com.fast.po.fee.*;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.FastOperationLogService;
import com.fast.service.setting.FastSettingSystemService;
import com.fast.utils.*;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.fee.DetailLinkVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeModelService extends BaseService {

    @Autowired
    private FastFeeModelMapper fastFeeModelMapper;
    @Autowired
    private FastFeeModelGearMapper fastFeeModelGearMapper;
    @Autowired
    private FastFeeModelDetailMapper fastFeeModelDetailMapper;
    @Autowired
    private FastFeeModelBatchMapper fastFeeModelBatchMapper;
    @Autowired
    private FastMemberOrderRechargeMapper fastMemberOrderRechargeMapper;
    @Autowired
    private FastFeeModelGlobalSetMapper fastFeeModelGlobalSetMapper;
    @Autowired
    private FastFeeModelBatchService feeModelBatchService;
    @Autowired
    private FastOperationLogService operationLogService;
    @Autowired
    private FastLinkMapper fastLinkMapper;
    @Autowired
    private FastSettingSystemService fastSettingSystemService;

    /**
     * 查询用户充值模板
     */
    public List<FastFeeModelDetailPO> getMemberFeeModel(Integer type, Integer phoneOs, final SessionVO sessionVO) {
        // 查询当前用户的充值记录
        List<FastMemberOrderRechargePO> rechargeList = queryReChargeLinkList(sessionVO.getMemberId(), sessionVO.getLinkId());
        Map<Integer, Integer> rechargeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(rechargeList)) {
            for (FastMemberOrderRechargePO recharge : rechargeList) {
                Integer count = rechargeMap.get(recharge.getModelDetailId());
                if (count == null) {
                    count = 0;
                }
                count = count + 1;
                rechargeMap.put(recharge.getModelDetailId(), count);
            }
        }
        int modelType = 0;// 是否已经充值
        if (CollUtil.isNotEmpty(rechargeList)) {
            modelType = 1;
        }
        List<FastFeeModelDetailPO> detailList = null;// 充值模板
        // 判断是否有投放计划关联的充值模板
        if (sessionVO.getLinkId() != null && sessionVO.getLinkId() > 0) {
            // 根据linkId来查询充值模板
            String keyRecharge = StaticVar.RECHARGE_MODEL + sessionVO.getLinkId() + "_" + modelType + "_" + type + "_" + phoneOs;
            String modelStr = RedisUtil.get(keyRecharge);
            if (StrUtil.isEmpty(modelStr)) {
                DetailLinkVO linkParam = new DetailLinkVO();
                linkParam.setLinkId(sessionVO.getLinkId());
                linkParam.setModelType(modelType);
                linkParam.setType(type);
                linkParam.setPhoneOs(type);
                detailList = fastFeeModelDetailMapper.queryForMemberList(linkParam);
                RedisUtil.set(keyRecharge, JsonUtil.arrayToJson(detailList), 60 * 10);
            } else {
                detailList = JsonUtil.toList(modelStr, FastFeeModelDetailPO.class);
                log.info("用户:{};从缓存获取充值模板:{}个;linkId:{}", sessionVO.getMemberId(), detailList.size(), sessionVO.getLinkId());
            }
            if (CollUtil.isNotEmpty(detailList) && detailList.getFirst().getPayType() == 1) {
                detailList.clear();// 置空，使用全局模板
            }
        }
        // 判断使用全局模板
        if (detailList == null || detailList.isEmpty()) {
            log.info("用户:{}使用全局模板", sessionVO.getMemberId());
            detailList = getOfficialGlobalList(sessionVO.getMiniId(), sessionVO.getOfficialId(), type, phoneOs, modelType);
        }
        if (detailList == null || detailList.isEmpty()) {
            log.error("未配置充值模板-memberId={},linkId={}", sessionVO.getMemberId(), sessionVO.getLinkId());
            throw new MyException(StaticStr.RECHARGE_TEMPLATE_NOT_EXIST);
        }
        // 判断充值是否有次数限制
        for (int i = detailList.size() - 1; i > -1; i--) {
            FastFeeModelDetailPO detail = detailList.get(i);
            Integer count = rechargeMap.get(detail.getId());
            if (count == null) {
                count = 0;
            }
            if (detail.getTimesLimit() != null && detail.getTimesLimit() <= count) {
                detailList.remove(i);
                continue;
            }
            detail.setEncryptionId(encode(detail.getId()));
            // 设置钻石数量，抖音
            if (sessionVO.getClientType() != null && sessionVO.getClientType() == 2) {
                int diamondCount = fastSettingSystemService.getDiamondCountByMoney(detail.getMoneyRecharge());
                detail.setDiamondCount(diamondCount);
            }
        }
        return detailList;
    }

    /**
     * 查询分销商全局模板
     * type 类型:1=充值金币;2=充值VIP
     * modelType 0未支付1已支付
     * phoneOs 手机系统: 1安卓，2爱疯
     */
    public List<FastFeeModelDetailPO> getOfficialGlobalList(Integer miniId, Integer officialId, Integer type, Integer phoneOs, Integer modelType) {
        // 查询当前全局配置，用平台的还是自定义的
        FastFeeModelGlobalSetPO setParam = new FastFeeModelGlobalSetPO();
        setParam.setOfficialId(officialId);
        setParam.setDelFlag(0);
        FastFeeModelGlobalSetPO globalSet = fastFeeModelGlobalSetMapper.queryCustomOne(setParam);
        // 1平台;2自定义
        Integer globalType = 1;
        if (globalSet != null) {
            globalType = globalSet.getType();
        }
        DetailLinkVO linkParam = new DetailLinkVO();
        List<FastFeeModelDetailPO> detailList;
        // 全局自定义
        if (globalType == 2) {
            linkParam.setType(type);
            linkParam.setModelType(modelType);
            linkParam.setOfficialId(officialId);
            linkParam.setGlobal(1);
            linkParam.setPhoneOs(phoneOs);
            String key = StaticVar.RECHARGE_MODEL_CUSTOM + officialId + "_" + type + "_" + modelType + "_" + phoneOs;
            String resStr = RedisUtil.get(key);
            if (StrUtil.isEmpty(resStr)) {
                detailList = fastFeeModelDetailMapper.queryGlobalForMemberList(linkParam);
                if (CollUtil.isNotEmpty(detailList)) {
                    resStr = JsonUtil.toString(detailList);
                    RedisUtil.set(key, resStr, 60 * 10);
                    actionLogService.log("recharge_custom_cache", key + "设置缓存全局有" + detailList.size() + "个,内容:" + JsonUtil.toString(detailList));
                }
            }
            detailList = JsonUtil.toList(resStr, FastFeeModelDetailPO.class);
        }
        // 全局平台
        else {
            String key = StaticVar.RECHARGE_MODEL_GLOBAL + miniId + "_" + type + "_" + modelType + "_" + phoneOs;
            String resStr = RedisUtil.get(key);
            // log.info("key: {}, value: {}", key, resStr);
            if (StrUtil.isEmpty(resStr) || RedisUtil.aheadExpire(key, 200)) {
                linkParam.setType(type);
                linkParam.setModelType(modelType);
                linkParam.setRetailId(0);
                linkParam.setMiniId(miniId);
                linkParam.setPhoneOs(phoneOs);
                detailList = fastFeeModelDetailMapper.queryGlobalPlatformForMemberList(linkParam);
                if (StrUtil.isNotEmpty(resStr)) {
                    actionLogService.log("nearexpire", "开始执行模糊过期,剩余时间=" + RedisUtil.ttl(key));
                }
                if (CollUtil.isNotEmpty(detailList)) {
                    resStr = JsonUtil.toString(detailList);
                    RedisUtil.set(key, resStr, 60 * 15);
                    actionLogService.log("recharge_custom_cache", "平台-" + key + "设置缓存全局有" + detailList.size() + "个,内容:" + JsonUtil.toString(detailList));
                }
            }
            detailList = JsonUtil.toList(resStr, FastFeeModelDetailPO.class);
        }
        return detailList;
    }

    // 查询当前用户+链接的充值记录
    public List<FastMemberOrderRechargePO> queryReChargeLinkList(Long memberId, Integer linkId) {
        String key = StaticVar.MEMBER_RECHARGE + memberId + "_" + linkId;
        List<FastMemberOrderRechargePO> rechargeList = RedisUtil.getList(key, FastMemberOrderRechargePO.class);
        if (rechargeList == null) {
            FastMemberOrderRechargePO rechargeParam = new FastMemberOrderRechargePO();
            rechargeParam.setMemberId(memberId);
            rechargeParam.setLinkId(linkId);
            rechargeParam.setState(1);
            rechargeList = fastMemberOrderRechargeMapper.queryReChargeLinkList(rechargeParam);
            RedisUtil.setList(key, rechargeList, 60 * 60 * 2);
        }
        return rechargeList;
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelPO queryById(FastFeeModelPO item) {
        return fastFeeModelMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelPO queryById(Integer id) {
        FastFeeModelPO itemParam = new FastFeeModelPO();
        itemParam.setId(id);
        return fastFeeModelMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeModelPO queryOne(FastFeeModelPO item) {
        return fastFeeModelMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeModelPO> queryList(FastFeeModelPO item) {
        return fastFeeModelMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeModelPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeModelPO> list = fastFeeModelMapper.queryList(item);
        StringBuilder ids = new StringBuilder();
        for (FastFeeModelPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (ids.length() > 0) {
                ids.append(",");
            }
            ids.append(cur.getId());
        }
        // 查询全部的模板详情
        FastFeeModelDetailPO detailParam = new FastFeeModelDetailPO();
        detailParam.setModelIds(ids.toString());
        detailParam.setContractFlag(item.getContractFlag());
        List<FastFeeModelDetailPO> detailList = fastFeeModelDetailMapper.queryList(detailParam);
        for (FastFeeModelPO model : list) {
            for (FastFeeModelDetailPO detail : detailList) {
                if (detail.getModelId().equals(model.getId())) {
                    List<FastFeeModelDetailPO> detailSubList = model.getDetailList();
                    if (detailSubList == null) {
                        detailSubList = new ArrayList<>();
                    }
                    detailSubList.add(detail);
                    model.setDetailList(detailSubList);
                }
            }
        }
        int queryCount = 0;
        Map<String, Object> results = getPageListData(list, pageVO);
        if (StrUtil.isNotBlank(ids)) {
            detailParam.setContractFlag(1);
            queryCount = fastFeeModelDetailMapper.queryCount(detailParam);
        }
        results.put("contractCount", queryCount);
        return ResultVO.success(results);
    }

    // 查询全局模板
    public ResultVO<?> queryGlobalList(FastFeeModelPO item) {
        Map<String, Object> results = ResultVO.getMap();
        item.setType(1);// 1=充值金币;2=充值VIP
        item.setModelType(0);// 0未支付，1支付
        List<FastFeeModelPO> unpayRechargeList = queryGlobalModelDetailList(item);
        item.setType(1);// 1=充值金币;2=充值VIP
        item.setModelType(1);// 0未支付，1支付
        List<FastFeeModelPO> payRechargeList = queryGlobalModelDetailList(item);
        item.setType(2);// 1=充值金币;2=充值VIP
        item.setModelType(0);// 0未支付，1支付
        List<FastFeeModelPO> unpayVipList = queryGlobalModelDetailList(item);
        item.setType(2);// 1=充值金币;2=充值VIP
        item.setModelType(1);// 0未支付，1支付
        List<FastFeeModelPO> payVipList = queryGlobalModelDetailList(item);
        results.put("unpayRechargeList", unpayRechargeList);
        results.put("payRechargeList", payRechargeList);
        results.put("unpayVipList", unpayVipList);
        results.put("payVipList", payVipList);

        return ResultVO.success(results);
    }

    // 查询充值模板
    public ResultVO<?> queryRechargeModel(Integer linkId) {
        FastLinkPO linkPO = fastLinkMapper.queryById(linkId);
        FastFeeModelPO modelPO = new FastFeeModelPO();
        modelPO.setLinkId(linkId);
        modelPO.setOfficialId(linkPO.getOfficialId());
        modelPO.setMiniId(linkPO.getMiniId());
        modelPO.setRetailId(linkPO.getRetailId());
        return queryLinkFeeModelList(modelPO);
    }

    // 查询链接关联的充值模板
    public ResultVO<?> queryLinkFeeModelList(FastFeeModelPO item) {
        DetailLinkVO linkParam = new DetailLinkVO();
        linkParam.setLinkId(item.getLinkId());
        List<FastFeeModelPO> unpayRechargeList = new ArrayList<>();
        List<FastFeeModelPO> payRechargeList = new ArrayList<>();
        List<FastFeeModelPO> unpayVipList = new ArrayList<>();
        List<FastFeeModelPO> payVipList = new ArrayList<>();
        boolean global = true;
        if (item.getLinkId() != null) {
            linkParam.setType(1);
            linkParam.setModelType(0);
            List<FastFeeModelDetailPO> unpayRechargeDetailList = fastFeeModelDetailMapper.queryForMemberList(linkParam);
            if (unpayRechargeDetailList.size() > 0) {
                FastFeeModelPO mp = new FastFeeModelPO();
                mp.setDetailList(unpayRechargeDetailList);
                unpayRechargeList.add(mp);
                global = false;
            }
            linkParam.setType(1);
            linkParam.setModelType(1);
            List<FastFeeModelDetailPO> payRechargeDetailList = fastFeeModelDetailMapper.queryForMemberList(linkParam);
            if (payRechargeDetailList.size() > 0) {
                FastFeeModelPO mp = new FastFeeModelPO();
                mp.setDetailList(payRechargeDetailList);
                payRechargeList.add(mp);
                global = false;
            }
            linkParam.setType(2);
            linkParam.setModelType(0);
            List<FastFeeModelDetailPO> unpayVipDetailList = fastFeeModelDetailMapper.queryForMemberList(linkParam);
            if (unpayVipDetailList.size() > 0) {
                FastFeeModelPO mp = new FastFeeModelPO();
                mp.setDetailList(unpayVipDetailList);
                unpayVipList.add(mp);
                global = false;
            }
            linkParam.setType(2);
            linkParam.setModelType(1);
            List<FastFeeModelDetailPO> payVipDetailList = fastFeeModelDetailMapper.queryForMemberList(linkParam);
            if (payVipDetailList.size() > 0) {
                FastFeeModelPO mp = new FastFeeModelPO();
                mp.setDetailList(payVipDetailList);
                payVipList.add(mp);
                global = false;
            }
        }
        Map<String, Object> results = ResultVO.getMap();
        if (global) {
            item.setType(1);// 1=充值金币;2=充值VIP
            item.setModelType(0);// 0未支付，1支付
            List<FastFeeModelDetailPO> unpayRechargeDetailList = getOfficialGlobalList(item.getMiniId(), item.getOfficialId(), 1, item.getPhoneOs(), 0);
            FastFeeModelPO mp1 = new FastFeeModelPO();
            mp1.setDetailList(unpayRechargeDetailList);
            unpayRechargeList.add(mp1);
            item.setType(1);// 1=充值金币;2=充值VIP
            item.setModelType(1);// 0未支付，1支付
            List<FastFeeModelDetailPO> payRechargeDetailList = getOfficialGlobalList(item.getMiniId(), item.getOfficialId(), 1, item.getPhoneOs(), 1);
            FastFeeModelPO mp2 = new FastFeeModelPO();
            mp2.setDetailList(payRechargeDetailList);
            payRechargeList.add(mp2);
            item.setType(2);// 1=充值金币;2=充值VIP
            item.setModelType(0);// 0未支付，1支付
            List<FastFeeModelDetailPO> unpayVipDetailList = getOfficialGlobalList(item.getMiniId(), item.getOfficialId(), 2, item.getPhoneOs(), 0);
            FastFeeModelPO mp3 = new FastFeeModelPO();
            mp3.setDetailList(unpayVipDetailList);
            unpayVipList.add(mp3);
            item.setType(2);// 1=充值金币;2=充值VIP
            item.setModelType(1);// 0未支付，1支付
            List<FastFeeModelDetailPO> payVipDetailList = getOfficialGlobalList(item.getMiniId(), item.getOfficialId(), 2, item.getPhoneOs(), 1);
            FastFeeModelPO mp4 = new FastFeeModelPO();
            mp4.setDetailList(payVipDetailList);
            payVipList.add(mp4);
        }
        results.put("unpayRechargeList", unpayRechargeList);
        results.put("payRechargeList", payRechargeList);
        results.put("unpayVipList", unpayVipList);
        results.put("payVipList", payVipList);

        return ResultVO.success(results);
    }

    // -- 查询模板
    public List<FastFeeModelPO> queryGlobalModelDetailList(FastFeeModelPO item) {
        List<FastFeeModelPO> list = fastFeeModelMapper.queryList(item);
        StringBuilder ids = new StringBuilder();
        for (FastFeeModelPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (ids.length() > 0) {
                ids.append(",");
            }
            ids.append(cur.getId());
        }
        // 查询全部的模板详情
        FastFeeModelDetailPO detailParam = new FastFeeModelDetailPO();
        detailParam.setModelIds(ids.toString());
        List<FastFeeModelDetailPO> detailList = fastFeeModelDetailMapper.queryList(detailParam);
        for (FastFeeModelPO model : list) {
            for (FastFeeModelDetailPO detail : detailList) {
                if (detail.getModelId().equals(model.getId())) {
                    List<FastFeeModelDetailPO> detailSubList = model.getDetailList();
                    if (detailSubList == null) {
                        detailSubList = new ArrayList<>();
                    }
                    detailSubList.add(detail);
                    model.setDetailList(detailSubList);
                }
            }
        }
        return list;
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeModelPO item) {
        return fastFeeModelMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeModelPO item) {

        FastFeeModelPO fastFeeModelPO = new FastFeeModelPO();
        fastFeeModelPO.setType(item.getType());
        fastFeeModelPO.setMiniId(item.getMiniId());
        fastFeeModelPO.setModelType(item.getModelType());
        fastFeeModelPO.setDelFlag(0);
        fastFeeModelPO.setRetailId(item.getRetailId());
        int queryCount = fastFeeModelMapper.queryCount(fastFeeModelPO);
        if (queryCount > 0) {
            return MethodVO.error("模版已经存在，请勿重复添加");
        }
        Date nowTime = DateUtil.getNowDate();
        // 新增主表
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastFeeModelMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        // 新增详细内容
        if (insertModelDetailByGearids(item) == 0) {
            transactionRollBack();
            return MethodVO.error("新增失败");
        }
        return MethodVO.success();
    }

    /**
     * 根据档位自动插入详情
     *
     * @return
     */
    private int insertModelDetailByGearids(FastFeeModelPO model) {
        String gearGive = model.getGearGive();
        Map<Integer, Integer> gearGiveMap = new HashMap<>();
        if (StrUtil.isNotEmpty(gearGive)) {
            String[] ggArray = gearGive.split(",");
            for (String gg : ggArray) {
                String gearIdStr = gg.split("#")[0];
                String coinGiveStr = gg.split("#")[1];
                gearGiveMap.put(Integer.valueOf(gearIdStr), Integer.valueOf(coinGiveStr));
            }
        }
        FastFeeModelGearPO gearParam = new FastFeeModelGearPO();
        gearParam.setIds(model.getGearIds());
        List<FastFeeModelGearPO> gearTempList = fastFeeModelGearMapper.queryList(gearParam);
        List<Integer> gearIdList = CollUtil.parseIntStr2List(model.getGearIds());
        List<FastFeeModelGearPO> gearList = new ArrayList<>();
        for (Integer gearid : gearIdList) {
            for (FastFeeModelGearPO gear : gearTempList) {
                if (gear.getId().equals(gearid)) {
                    gearList.add(gear);
                }
            }
        }
        Date timeNow = DateUtil.getNowDate();
        List<FastFeeModelDetailPO> detailList = new ArrayList<>();
        for (int i = 0; i < gearList.size(); i++) {
            FastFeeModelGearPO gear = gearList.get(i);
            FastFeeModelDetailPO detail = new FastFeeModelDetailPO();
            if (gearGiveMap.containsKey(gear.getId())) {
                detail.setCoinGive(gearGiveMap.get(gear.getId()));
            } else {
                detail.setCoinGive(gear.getCoinGive());
            }
            detail.setCoinRecharge(gear.getCoinRecharge());
            detail.setCorner(gear.getCorner());
            detail.setCreateTime(timeNow);
            detail.setCreatorId(model.getUpdatorId());
            if (gear.getId().equals(model.getDefaultGearId())) {
                detail.setDefaultFlag(1);
            } else {
                detail.setDefaultFlag(0);
            }
            detail.setModelId(model.getId());
            detail.setMoneyRecharge(gear.getMoneyRecharge());
            detail.setMoneyUnit(gear.getMoneyUnit());
            detail.setRemark(gear.getRemark());
            detail.setSequence(i + 1);
            detail.setValidUnit(gear.getValidUnit());
            detail.setTimesLimit(gear.getTimesLimit());
            detail.setTitle(gear.getTitle());
            detail.setType(gear.getType());
            detail.setModelType(model.getModelType());
            detail.setUpdateTime(timeNow);
            detail.setUpdatorId(model.getUpdatorId());
            detail.setValidDate(gear.getValidDate());
            detail.setGearId(gear.getId());
            detail.setVipType(gear.getVipType());
            detail.setContractTemplateId(gear.getContractTemplateId());
            detail.setContractFlag(gear.getContractFlag());
            detail.setContractMoneyRecharge(gear.getContractMoneyRecharge());
            detail.setMiniId(model.getMiniId());
            detail.setDescription(gear.getDescription());
            detailList.add(detail);
        }
        if (fastFeeModelDetailMapper.insertBatch(detailList) == 0) {
            transactionRollBack();
            return 0;
        }
        return 1;
    }

    /**
     * 批量新增，四个一起
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(FastFeeModelBatchPO batch, List<FastFeeModelPO> list) {
        Date nowTime = DateUtil.getNowDate();
        // 批次
        batch.setCreateTime(nowTime);
        batch.setUpdateTime(nowTime);
        // 判断是否已经存在
        FastFeeModelBatchPO batchParam = new FastFeeModelBatchPO();
        batchParam.setDelFlag(0);
        batchParam.setGlobal(batch.getGlobal());
        batchParam.setOfficialId(batch.getOfficialId());
        batchParam.setRetailId(batch.getRetailId());
        batchParam.setCommonFlag(batch.getCommonFlag());
        FastFeeModelBatchPO batchPO = fastFeeModelBatchMapper.queryOne(batchParam);
        if (batchPO != null && batch.getGlobal() == 1) {
            return MethodVO.error("已经存在，请进行编辑");
        }
        if (fastFeeModelBatchMapper.insertSelective(batch) == 0) {
            return MethodVO.error("添加失败");
        }
        for (FastFeeModelPO item : list) {
            // 新增主表
            item.setCreateTime(nowTime);
            item.setUpdateTime(nowTime);
            item.setModelBatchId(batch.getId());
            if (batch.getCommonFlag() == 1) {
                item.setOfficialId(0);
                item.setMiniId(0);
            }
            if (fastFeeModelMapper.insertSelective(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
            // 新增详细内容
            if (insertModelDetailByGearids(item) == 0) {
                transactionRollBack();
                return MethodVO.error("新增失败");
            }
        }
        return MethodVO.success();

    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeModelPO item) {
        // 查询旧的model
        FastFeeModelPO modelParam = new FastFeeModelPO();
        modelParam.setId(item.getId());
        FastFeeModelPO modelOld = fastFeeModelMapper.queryOne(modelParam);

        if (fastFeeModelMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        // 删除就的detail
        if (fastFeeModelDetailMapper.deleteByModelId(modelOld.getId()) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        item.setId(modelOld.getId());
        item.setMiniId(modelOld.getMiniId());
        item.setModelType(modelOld.getModelType());
        item.setUpdateTime(DateUtil.getNowDate());
        // 新增detail
        if (insertModelDetailByGearids(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        // 清理缓存
        // type: 1=充值金币;2=充值VIP
        // modelType: 0=未支付;1=已支付
        String key1 = StaticVar.RECHARGE_MODEL_GLOBAL + modelOld.getMiniId() + "_" + modelOld.getType() + "_" + modelOld.getModelType();
        RedisUtil.del(key1);
        log.info("清除缓存: {}", key1);

        return MethodVO.success();
    }

    /**
     * 批量更新，四个一起
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateBatch(HttpServletRequest request, FastFeeModelBatchPO params, SessionVO sessionVO, List<FastFeeModelPO> list) {
        Date nowDate = DateUtil.getNowDate();

        // 查询自定义模板规则
        FastFeeModelBatchPO feeModelBatchPO = new FastFeeModelBatchPO();
        feeModelBatchPO.setId(params.getId());
        feeModelBatchPO.setOfficialId(sessionVO.getOfficialId());
        ResultVO<Map<String, Object>> customRuleOld = null;
        if (notEmpty(params.getBatchName())) {
            customRuleOld = feeModelBatchService.queryBatchDetail(feeModelBatchPO);
        } else {
            customRuleOld = feeModelBatchService.queryBatchDetailCustom(feeModelBatchPO);
        }

        // 批次，更新批次名称
        params.setUpdateTime(nowDate);
        fastFeeModelBatchMapper.updateById(params);
        for (FastFeeModelPO item : list) {
            FastFeeModelGearPO gearParam = new FastFeeModelGearPO();
            gearParam.setIds(item.getGearIds());
            List<FastFeeModelGearPO> gearList = fastFeeModelGearMapper.queryList(gearParam);
            List<Integer> gearIdList = CollUtil.parseIntStr2List(item.getGearIds());
            // 无需更新model表，查询关联的detail
            FastFeeModelDetailPO fastFeeModelDetailPO = new FastFeeModelDetailPO();
            fastFeeModelDetailPO.setModelId(item.getModelId());
            List<FastFeeModelDetailPO> detailList = fastFeeModelDetailMapper.queryList(fastFeeModelDetailPO);
            List<FastFeeModelDetailPO> newDetailList = new ArrayList<>();
            List<FastFeeModelDetailPO> deleteDetailList = new ArrayList<>();
            for (FastFeeModelDetailPO detailItem : detailList) {
                boolean existGear = false;
                for (FastFeeModelGearPO gearItem : gearList) {
                    if (gearItem.getId().equals(detailItem.getGearId())) {
                        existGear = true;
                        break;
                    }
                }
                if (!existGear) {
                    deleteDetailList.add(detailItem);
                }
            }
            for (int i = 0; i < gearIdList.size(); i++) {
                Integer gearId = gearIdList.get(i);
                boolean exist = false;
                for (FastFeeModelDetailPO detail : detailList) {
                    if (detail.getGearId().equals(gearId)) {
                        detail.setSequence(i + 1);
                        detail.setUpdateTime(nowDate);
                        if (detail.getGearId().equals(item.getDefaultGearId())) {
                            detail.setDefaultFlag(1);
                        } else {
                            detail.setDefaultFlag(0);
                        }
                        exist = true;
                        newDetailList.add(detail);
                    }
                }
                if (!exist) {
                    // 添加新的detail
                    for (FastFeeModelGearPO gear : gearList) {
                        if (gear.getId().equals(gearId)) {
                            FastFeeModelDetailPO detail = new FastFeeModelDetailPO();
                            detail.setCoinGive(gear.getCoinGive());
                            detail.setCoinRecharge(gear.getCoinRecharge());
                            detail.setCorner(gear.getCorner());
                            detail.setCreateTime(nowDate);
                            detail.setCreatorId(params.getUpdatorId());
                            if (gear.getId().equals(item.getDefaultGearId())) {
                                detail.setDefaultFlag(1);
                            } else {
                                detail.setDefaultFlag(0);
                            }
                            detail.setModelId(item.getModelId());
                            detail.setMoneyRecharge(gear.getMoneyRecharge());
                            detail.setMoneyUnit(gear.getMoneyUnit());
                            detail.setRemark(gear.getRemark());
                            detail.setSequence(i + 1);
                            detail.setTimesLimit(gear.getTimesLimit());
                            detail.setTitle(gear.getTitle());
                            detail.setType(gear.getType());
                            detail.setUpdateTime(nowDate);
                            detail.setUpdatorId(params.getUpdatorId());
                            detail.setValidDate(gear.getValidDate());
                            detail.setValidUnit(gear.getValidUnit());
                            detail.setGearId(gear.getId());
                            detail.setVipType(gear.getVipType());
                            detail.setContractFlag(gear.getContractFlag());
                            detail.setContractTemplateId(gear.getContractTemplateId());
                            detail.setMiniId(gear.getMiniId());
                            detail.setContractMoneyRecharge(gear.getContractMoneyRecharge());
                            detail.setDescription(gear.getDescription());
                            newDetailList.add(detail);
                        }
                    }
                }
            }
            // 更新detail，新增detail
            for (FastFeeModelDetailPO detail : newDetailList) {
                if (detail.getId() != null) {
                    // 更新
                    if (fastFeeModelDetailMapper.updateById(detail) == 0) {
                        transactionRollBack();
                        return MethodVO.error("更新失败");
                    }
                } else {
                    // 新增
                    if (fastFeeModelDetailMapper.insertSelective(detail) == 0) {
                        transactionRollBack();
                        return MethodVO.error("添加失败");
                    }
                }
            }
            // 删除detail
            if (CollUtil.hasContent(deleteDetailList)) {
                for (FastFeeModelDetailPO detail : deleteDetailList) {
                    if (fastFeeModelDetailMapper.deleteById(detail.getId()) == 0) {
                        transactionRollBack();
                        return MethodVO.error("添加失败");
                    }
                }
            }
        }

        doLog(request, params, sessionVO, feeModelBatchPO, customRuleOld, nowDate);
        return MethodVO.success();
    }

    public ResultVO<?> getModelBatch(Integer linkId, Integer phoneOs) {
        FastLinkPO linkPO = fastLinkMapper.queryById(linkId);
        ResultVO<Map<String, Object>> model = null;
        FastFeeModelBatchPO feeModelBatchPO = new FastFeeModelBatchPO();
        feeModelBatchPO.setId(linkPO.getPayRule());
        feeModelBatchPO.setOfficialId(linkPO.getOfficialId());
        if (linkPO.getPayType() == 1) {
            // 全局，判断是自定义全局，还是平台全局
            FastFeeModelGlobalSetPO gsParam = new FastFeeModelGlobalSetPO();
            gsParam.setOfficialId(linkPO.getOfficialId());
            gsParam.setDelFlag(0);
            FastFeeModelGlobalSetPO gsp = fastFeeModelGlobalSetMapper.queryOne(gsParam);
            if (gsp != null && gsp.getType() == 2) {
                // 自定义全局
                model = feeModelBatchService.queryBatchDetailCustom(feeModelBatchPO);
            } else {
                // 平台全局
                // 查询小程序维度的模板
                DetailLinkVO linkParam = new DetailLinkVO();
                linkParam.setPhoneOs(phoneOs);
                // 全局平台
                // 金币-未充值
                linkParam.setType(1);
                linkParam.setModelType(0);
                linkParam.setRetailId(0);
                linkParam.setMiniId(linkPO.getMiniId());
                List<FastFeeModelDetailPO> detail10List = fastFeeModelDetailMapper.queryGlobalPlatformForMemberList(linkParam);
                // 金币-已充值
                linkParam.setType(1);
                linkParam.setModelType(1);
                List<FastFeeModelDetailPO> detail11List = fastFeeModelDetailMapper.queryGlobalPlatformForMemberList(linkParam);
                // vip-未充值
                linkParam.setType(2);
                linkParam.setModelType(0);
                List<FastFeeModelDetailPO> detail20List = fastFeeModelDetailMapper.queryGlobalPlatformForMemberList(linkParam);
                // vip-已充值
                linkParam.setType(2);
                linkParam.setModelType(1);
                List<FastFeeModelDetailPO> detail21List = fastFeeModelDetailMapper.queryGlobalPlatformForMemberList(linkParam);

                Map<String, Object> results = ResultVO.getMap();
                results.put("feeUnrechargeList", detail10List);
                results.put("feeRechargeList", detail11List);
                results.put("vipUnrechargeList", detail20List);
                results.put("vipRechargeList", detail21List);
                return ResultVO.success(results);
            }
        } else {
            model = feeModelBatchService.queryBatchDetail(feeModelBatchPO);
        }
        return model;
    }

    /**
     * 记录操作日志
     *
     * @param request
     * @param params
     * @param sessionVO
     * @param feeModelBatchPO
     * @param customRuleOld
     * @param nowDate
     */
    private void doLog(HttpServletRequest request, FastFeeModelBatchPO params, SessionVO sessionVO, FastFeeModelBatchPO feeModelBatchPO, ResultVO<Map<String, Object>> customRuleOld, Date nowDate) {
        // 查询自定义模板规则
        ResultVO<Map<String, Object>> customRuleNew = null;
        if (notEmpty(params.getBatchName())) {
            customRuleNew = feeModelBatchService.queryBatchDetail(feeModelBatchPO);
        } else {
            customRuleNew = feeModelBatchService.queryBatchDetailCustom(feeModelBatchPO);
        }

        // 记录操作日志
        String snapshotOld = JsonUtil.toString(customRuleOld);
        String snapshotNew = JsonUtil.toString(customRuleNew);
        if (!StrUtil.equals(snapshotOld, snapshotNew)) {
            FastLogOperationPO operationPO = new FastLogOperationPO();
            operationPO.setType(OperationLogEnum.OP_12.index);
            operationPO.setRetailId(sessionVO.getRetailId());
            operationPO.setObjectId(sessionVO.getOfficialId());// 操作对象ID
            operationPO.setObjectType(4);
            operationPO.setIp(IPUtil.ipToLong(request));
            operationPO.setUserId(params.getUpdatorId());
            operationPO.setCreateTime(nowDate);
            operationPO.setOfficialId(sessionVO.getOfficialId());
            Map<String, Object> resultsOld = customRuleOld.getResults();
            Map<String, Object> resultsNew = customRuleNew.getResults();
            boolean changeCoin = false;
            boolean changeVip = false;
            // 判断是否修改了K币档位
            if (!StrUtil.equals(JsonUtil.toString(resultsOld.get("feeRechargeList")), JsonUtil.toString(resultsNew.get("feeRechargeList")))) {
                changeCoin = true;
            } else {
                if (!StrUtil.equals(JsonUtil.toString(resultsOld.get("feeUnrechargeList")), JsonUtil.toString(resultsNew.get("feeUnrechargeList")))) {
                    changeCoin = true;
                }
            }
            // 判断是否修改了VIP档位
            if (!StrUtil.equals(JsonUtil.toString(resultsOld.get("vipRechargeList")), JsonUtil.toString(resultsNew.get("vipRechargeList")))) {
                changeVip = true;
            } else {
                if (!StrUtil.equals(JsonUtil.toString(resultsOld.get("vipUnrechargeList")), JsonUtil.toString(resultsNew.get("vipUnrechargeList")))) {
                    changeVip = true;
                }
            }
            if (changeCoin && changeVip) {
                operationPO.setRemark("修改K币,VIP充值下的档位配置");// 操作对象
            } else if (changeCoin) {
                operationPO.setRemark("修改K币充值下的档位配置");// 操作对象
            } else if (changeVip) {
                operationPO.setRemark("修改VIP充值下的档位配置");// 操作对象
            } else {
                return;// 不记录
            }
            if (notEmpty(params.getBatchName())) {
                operationPO.setObject(params.getId() + "-" + params.getBatchName());// 修改内容
            } else {
                operationPO.setObject("全局模板-自定义模板");// 修改内容
            }
            operationPO.setSnapshotOld(snapshotOld);
            operationPO.setSnapshotNew(snapshotNew);
            operationLogService.insertLog(operationPO);
            // 添加关联的渠道日志
            FastLinkPO linkParam = new FastLinkPO();
            linkParam.setPayRule(feeModelBatchPO.getId());
            List<FastLinkPO> linkList = fastLinkMapper.queryList(linkParam);
            for (FastLinkPO linkItem : linkList) {
                if (linkItem.getPayType() > 1) {
                    operationPO.setObjectId(linkItem.getId());
                    operationPO.setObjectType(1);
                    operationPO.setLevel(2);
                    operationPO.setType(OperationLogEnum.OP_12.index);
                    operationLogService.insertLog(operationPO);
                }
            }
        }
    }
}
