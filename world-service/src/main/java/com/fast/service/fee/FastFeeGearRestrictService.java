/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticStr;
import com.fast.mapper.fee.FastFeeGearRestrictMapper;
import com.fast.mapper.retail.FastRetailMapper;
import com.fast.po.fee.FastFeeGearRestrictPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.fee.RestrictJsonVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeGearRestrictService extends BaseService {

    @Autowired
    private FastFeeGearRestrictMapper fastFeeGearRestrictMapper;
    @Autowired
    private FastRetailMapper fastRetailMapper;

    /**
     * 查询完整的限制内容
     */
    public Map<Integer, String> getRestrictMap(Integer id) {
        Map<Integer, String> grMap = new HashMap<>();
        FastFeeGearRestrictPO grPO = fastFeeGearRestrictMapper.queryById(id);
        Integer seq = 1;
        if (grPO.getUnvipFlag() == 1) {
            grMap.put(seq, "VIP档位在首充面板中：" + grPO.getUnvipStart() + "-" + grPO.getUnvipEnd() + "元不可用");
            seq++;
        }
        if (grPO.getVipFlag() == 1) {
            grMap.put(seq, "VIP档位在复充面板中：" + grPO.getVipStart() + "-" + grPO.getVipEnd() + "元不可用");
            seq++;
        }
        if (grPO.getUnrechargeFlag() == 1) {
            grMap.put(seq, "K币档位在首充面板中：" + grPO.getUnrechargeStart() + "-" + grPO.getUnrechargeEnd() + "元不可用");
            seq++;
        }
        if (grPO.getRechargeFlag() == 1) {
            grMap.put(seq, "K币档位在复充面板中：" + grPO.getRechargeStart() + "-" + grPO.getRechargeEnd() + "元不可用");
            seq++;
        }
        if (grPO.getUndramaCardFlag() == 1) {
            grMap.put(seq, "剧卡档位在首充面板中：" + grPO.getUndramaCardStart() + "-" + grPO.getUndramaCardEnd() + "元不可用");
            seq++;
        }
        if (grPO.getDramaCardFlag() == 1) {
            grMap.put(seq, "剧卡档位在复充面板中：" + grPO.getDramaCardStart() + "-" + grPO.getDramaCardEnd() + "元不可用");
            seq++;
        }
        // 解析json
        RestrictJsonVO jsonVO = getJSONVO(grPO.getRestrictJson());
        if (jsonVO.getVipDaysLimitFlag() == 1) {
            // 首复充面板中，VIP 档位若在 10-15元金额范围，VIP时效只能为 1 日卡
            grMap.put(seq, "首复充面板中，VIP档位若在" + jsonVO.getVipDaysLimitStart() + "-" + jsonVO.getVipDaysLimitEnd() + "元金额范围，VIP时效只能为 1 日卡");
            seq++;
        }
        if (jsonVO.getVipPriceLimitFlag() == 1) {
            // 首复充面板中，VIP 时效若＞7日以上，则档位金额需≥20 元
            grMap.put(seq, "首复充面板中，VIP 时效若＞7日以上，则档位金额需≥" + jsonVO.getVipPriceLimitStart() + "元");
            seq++;
        }
        if (jsonVO.getCoinCountLimitFlag() == 1) {
            // 单集K币数不得小于 100K 币
            grMap.put(seq, "单集K币数不得小于" + jsonVO.getCoinCountLimitStart() + "K币");
            seq++;
        }
        if (jsonVO.getCoinGiftCountLimitFlag() == 1) {
            // 1 倍
            grMap.put(seq, "K币充值档位中赠送币数量不得大于档位充值币数的" + jsonVO.getCoinGiftCountTimes() + "倍");
            seq++;
        }
        return grMap;
    }

    public RestrictJsonVO getJSONVO(String jsonStr) {
        RestrictJsonVO jsonVO = new RestrictJsonVO();
//        	{   
//				"vipDaysLimit":{ flag : 0, priceStart: 10, priceEnd : 15 },// vip当日可用
//				"vipPriceLimit":{ flag : 1, priceStart : 20 },// 大7天vip档位金额
//				"coinCountLimit":{ flag : 1, countStart : 100 },// 单集K币限制
//				"coinGiftCountLimit":{ flag : 0, times : 1 // 1倍 }// 赠币限制
//			}
        if (StrUtil.isNotEmpty(jsonStr)) {
            JSONObject obj = JSONObject.parseObject(jsonStr);
            JSONObject vipDaysObj = obj.getJSONObject("vipDaysLimit");
            jsonVO.setVipDaysLimitFlag(vipDaysObj.getIntValue("flag"));
            jsonVO.setVipDaysLimitStart(vipDaysObj.getBigDecimal("priceStart"));
            jsonVO.setVipDaysLimitEnd(vipDaysObj.getBigDecimal("priceEnd"));

            JSONObject vipPriceObj = obj.getJSONObject("vipPriceLimit");
            jsonVO.setVipPriceLimitFlag(vipPriceObj.getIntValue("flag"));
            jsonVO.setVipPriceLimitStart(vipPriceObj.getBigDecimal("priceStart"));

            JSONObject coinCountObj = obj.getJSONObject("coinCountLimit");
            jsonVO.setCoinCountLimitFlag(coinCountObj.getIntValue("flag"));
            jsonVO.setCoinCountLimitStart(coinCountObj.getIntValue("countStart"));

            JSONObject coinGiftCountObj = obj.getJSONObject("coinGiftCountLimit");
            jsonVO.setCoinGiftCountLimitFlag(coinGiftCountObj.getIntValue("flag"));
            jsonVO.setCoinGiftCountTimes(coinGiftCountObj.getBigDecimal("times"));

        }
        return jsonVO;
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeGearRestrictPO queryById(FastFeeGearRestrictPO item) {
        return fastFeeGearRestrictMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeGearRestrictPO queryById(Integer id) {
        return fastFeeGearRestrictMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeGearRestrictPO queryOne(FastFeeGearRestrictPO item) {
        return fastFeeGearRestrictMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeGearRestrictPO> queryList(FastFeeGearRestrictPO item) {
        return fastFeeGearRestrictMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeGearRestrictPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeGearRestrictPO> list = fastFeeGearRestrictMapper.queryList(item);
        for (FastFeeGearRestrictPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeGearRestrictPO item) {
        return fastFeeGearRestrictMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeGearRestrictPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);

        if (StrUtil.isNotEmpty(item.getRetailIds())) {
            // 验证是否重复添加
            List<Integer> retailIdList = CollUtil.parseIntStr2List(item.getRetailIds());
            for (Integer retailId : retailIdList) {
                FastFeeGearRestrictPO grParam = new FastFeeGearRestrictPO();
                grParam.setRetailId(retailId);
                List<FastFeeGearRestrictPO> grList = fastFeeGearRestrictMapper.queryList(grParam);
                if (grList.size() > 0) {
                    FastRetailPO rPO = fastRetailMapper.queryById(retailId);
                    return MethodVO.error("分销商【" + rPO.getRetailName() + "】已经存在规则，新增失败");
                }
            }
        }

        if (fastFeeGearRestrictMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFeeGearRestrictPO> list) {
        if (fastFeeGearRestrictMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeGearRestrictPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);

        if (StrUtil.isNotEmpty(item.getRetailIds())) {
            List<Integer> retailIdList = CollUtil.parseIntStr2List(item.getRetailIds());
            if (retailIdList != null && retailIdList.size() > 0) {
                for (Integer retailId : retailIdList) {
                    FastFeeGearRestrictPO grParam = new FastFeeGearRestrictPO();
                    grParam.setRetailId(retailId);
                    List<FastFeeGearRestrictPO> grList = fastFeeGearRestrictMapper.queryList(grParam);
                    if (grList.size() > 0 && !grList.get(0).getId().equals(item.getId())) {
                        FastRetailPO rPO = fastRetailMapper.queryById(retailId);
                        return MethodVO.error("分销商【" + rPO.getRetailName() + "】已经存在规则，操作失败");
                    }
                }
            }
        }

        if (fastFeeGearRestrictMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
