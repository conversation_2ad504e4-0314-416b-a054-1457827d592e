/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.fee.FastFeeModelGearMapper;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.po.fee.FastFeeModelGearPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.FastCornerService;
import com.fast.utils.DateUtil;
import com.fast.utils.GooglePlayUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.common.FastCornerVO;
import com.google.api.services.androidpublisher.model.InAppProduct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeModelGearService extends BaseService {

    @Autowired
    private FastFeeModelGearMapper fastFeeModelGearMapper;

    @Autowired
    private FastCornerService fastCornerService;

    @Autowired
    private GooglePlayUtil googlePlayUtil;

    @Autowired
    private FastFeeModelDetailService fastFeeModelDetailService;

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelGearPO queryById(FastFeeModelGearPO item) {
        return fastFeeModelGearMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelGearPO queryById(Integer id) {
        FastFeeModelGearPO itemParam = new FastFeeModelGearPO();
        itemParam.setId(id);
        return fastFeeModelGearMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeModelGearPO queryOne(FastFeeModelGearPO item) {
        return fastFeeModelGearMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeModelGearPO> queryList(FastFeeModelGearPO item) {
        return fastFeeModelGearMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeModelGearPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeModelGearPO> list = fastFeeModelGearMapper.queryList(item);
        for (FastFeeModelGearPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            FastCornerVO fastCornerVO = fastCornerService.queryInfoByRedis(cur.getCornerId());
            cur.setCorner(fastCornerVO == null ? null : fastCornerVO.getCornerName());
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeModelGearPO item) {
        return fastFeeModelGearMapper.queryCount(item);
    }

    public FastFeeModelGearPO queryByProductId(String productId) {
        FastFeeModelGearPO itemParam = new FastFeeModelGearPO();
        itemParam.setProductId(productId);
        return queryOne(itemParam);
    }

    public FastFeeModelGearPO queryByAppleProductId(String appleProductId) {
        FastFeeModelGearPO itemParam = new FastFeeModelGearPO();
        itemParam.setAppleProductId(appleProductId);
        return queryOne(itemParam);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeModelGearPO item) {
        InAppProduct product = googlePlayUtil.getInAppProduct(item.getProductId());
        if (product == null) {
            return MethodVO.error("Google商品不存在");
        }
        FastFeeModelGearPO gpExist = queryByProductId(item.getProductId());
        if (gpExist != null) {
            return MethodVO.error("该Google商品已绑定了档位");
        }
        FastFeeModelGearPO apExist = queryByAppleProductId(item.getAppleProductId());
        if (apExist != null) {
            return MethodVO.error("该Apple商品已绑定了档位");
        }
        String key = StaticVar.GLOBAL_SET_VIP_GEAR_RESICT;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res) || "no".equals(res)) {
            if (item.getVipType() != null && item.getVipType() == 2) {
                return MethodVO.error("失败：vip观看已上线剧集功能，需要待小程序端发版后开放！");
            }
        }
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastFeeModelGearMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFeeModelGearPO> list) {
        if (fastFeeModelGearMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeModelGearPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastFeeModelGearMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastFeeModelGearPO item) {
        FastFeeModelDetailPO query = new FastFeeModelDetailPO();
        query.setGearId(item.getId());
        int count = fastFeeModelDetailService.queryCount(query);
        if (count > 0) {
            return MethodVO.error("请先移除金币充值和VIP购买卡的关联");
        }
        if (fastFeeModelGearMapper.delete(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public MethodVO updateState(FastFeeModelGearPO updatePo) {
        int i = fastFeeModelGearMapper.updateById(updatePo);
        if (i == 0) {
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
