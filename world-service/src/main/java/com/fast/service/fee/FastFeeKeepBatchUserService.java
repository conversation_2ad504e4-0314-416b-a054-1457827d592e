/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.mapper.fee.FastFeeKeepBatchUserMapper;
import com.fast.mapper.user.FastUserMapper;
import com.fast.po.fee.FastFeeKeepBatchPO;
import com.fast.po.fee.FastFeeKeepBatchUserPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeKeepBatchUserService extends BaseService {

    @Autowired
    private FastFeeKeepBatchUserMapper fastFeeKeepBatchUserMapper;
    @Autowired
    private FastUserMapper fastUserMapper;

    /**
     * 通过id查询单个对象
     */
    public FastFeeKeepBatchUserPO queryById(FastFeeKeepBatchUserPO item) {
        return fastFeeKeepBatchUserMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeKeepBatchUserPO queryById(Integer id) {
        return fastFeeKeepBatchUserMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeKeepBatchUserPO queryOne(FastFeeKeepBatchUserPO item) {
        return fastFeeKeepBatchUserMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeKeepBatchUserPO> queryList(FastFeeKeepBatchUserPO item) {
        List<FastFeeKeepBatchUserPO> userList = fastFeeKeepBatchUserMapper.queryList(item);
        for (FastFeeKeepBatchUserPO user : userList) {
            user.setEncryptionId(encode(user.getId()));
        }
        return userList;
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeKeepBatchUserPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeKeepBatchUserPO> list = fastFeeKeepBatchUserMapper.queryList(item);
        for (FastFeeKeepBatchUserPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeKeepBatchUserPO item) {
        return fastFeeKeepBatchUserMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeKeepBatchUserPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastFeeKeepBatchUserMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFeeKeepBatchUserPO> list) {
        if (fastFeeKeepBatchUserMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertUser(FastFeeKeepBatchPO keep) {
        List<FastFeeKeepBatchUserPO> list = new ArrayList<>();
        List<Integer> userIdList = CollUtil.parseIntStr2List(keep.getUserIds());
        Date nowTime = DateUtil.getNowDate();
        for (Integer userId : userIdList) {
            // 验证是否存在
            FastFeeKeepBatchUserPO buParam = new FastFeeKeepBatchUserPO();
            buParam.setUserId(userId);
            buParam.setBatchId(keep.getId());
            FastFeeKeepBatchUserPO buexist = fastFeeKeepBatchUserMapper.queryOne(buParam);
            if (buexist == null) {
                FastFeeKeepBatchUserPO bu = new FastFeeKeepBatchUserPO();
                bu.setBatchId(keep.getId());
                bu.setCreateTime(nowTime);
                bu.setCreatorId(keep.getCreatorId());
                bu.setUserId(userId);
                list.add(bu);
            }
        }
        if (fastFeeKeepBatchUserMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO delUser(FastFeeKeepBatchPO keep) {
        List<String> enIdList = CollUtil.parseStr2List(keep.getEncryptionIds());
        for (String encryptionId : enIdList) {
            Integer id = decodeInt(encryptionId);
            fastFeeKeepBatchUserMapper.delUser(id);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeKeepBatchUserPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastFeeKeepBatchUserMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
