/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.fee.FastFeeKeepBatchMapper;
import com.fast.mapper.fee.FastFeeKeepDetailMapper;
import com.fast.mapper.fee.FastFeeModelDetailMapper;
import com.fast.mapper.fee.FastFeeModelGearMapper;
import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.po.fee.FastFeeKeepBatchPO;
import com.fast.po.fee.FastFeeKeepDetailPO;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.po.fee.FastFeeModelGearPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.retail.FastRetailMiniPO;
import com.fast.service.base.BaseService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.retail.FastRetailMiniService;
import com.fast.service.setting.FastSettingSystemService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.fee.FeeKeepVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeKeepBatchService extends BaseService {

    @Autowired
    private FastFeeKeepBatchMapper fastFeeKeepBatchMapper;
    @Autowired
    private FastFeeKeepDetailMapper fastFeeKeepDetailMapper;
    @Autowired
    private FastFeeModelGearMapper fastFeeModelGearMapper;
    @Autowired
    private FastMemberOrderRechargeMapper fastMemberOrderRechargeMapper;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastFeeModelDetailMapper fastFeeModelDetailMapper;
    @Autowired
    private FastFeeModelDetailService fastFeeModelDetailService;
    @Autowired
    private FastSettingSystemService fastSettingSystemService;
    @Autowired
    private FastFeeModelService fastFeeModelService;
    @Autowired
    private FastRetailMiniService fastRetailMiniService;
    @Autowired
    private FastMiniMapper fastMiniMapper;

    /**
     * 查询用户的支付挽留规则
     */
    public ResultVO getMemberFeeKeep(FeeKeepVO keepVO, SessionVO sessionVO) {
        Map<String, Object> results = ResultVO.getMap();
        if (keepVO.getDramaId() == null) {
            results.put("message", "剧id不能为空");
            return ResultVO.success(results);
        }
        if (sessionVO.getMemberId() == null) {
            results.put("message", "非小程序用户");
            return ResultVO.success(results);
        }
        List<FastFeeKeepDetailPO> detailList = new ArrayList<>();
        FastLinkPO linkPO = null;
        Integer feeFlag = 1;
        if (biggerZero(sessionVO.getLinkId())) {
            // 查询自定义规则
            linkPO = fastLinkService.queryInfoByRedis(sessionVO.getLinkId());
            actionLogService.log("ad_keep", "linkPO:" + JSON.toJSONString(linkPO));
            if (linkPO != null) {
                feeFlag = linkPO.getFeeFlag();
            }
            if (linkPO.getKeepType() != null && linkPO.getKeepType() == 2 && biggerZero(linkPO.getKeepId())) {
                detailList = getKeepDetailList(linkPO.getKeepId()).stream().filter(e -> e.getState() == 1).collect(Collectors.toList());
                actionLogService.log("ad_keep", "detailList:" + JSON.toJSONString(detailList));
            }
        }
        if (detailList.size() == 0) {
            // 查询全局规则
            FastFeeKeepBatchPO kbParam = new FastFeeKeepBatchPO();
            kbParam.setRetailId(sessionVO.getRetailId());
            kbParam.setOfficialId(sessionVO.getOfficialId());
            kbParam.setDelFlag(0);
            kbParam.setFeeFlag(feeFlag);
            actionLogService.log("ad_keep", "kbParam:" + JSON.toJSONString(kbParam));
            FastFeeKeepBatchPO keepBatch = getKeepGlobal(kbParam);
            actionLogService.log("ad_keep", "keepBatch:" + JSON.toJSONString(keepBatch));
            if (keepBatch != null) {
                keepBatch.setEncryptionId(encode(keepBatch.getId()));
                detailList = keepBatch.getDetailList().stream().filter(e -> e.getState() == 1).collect(Collectors.toList());
            }
        }
        if (detailList.size() == 0) {
            results.put("message", "未配置挽留规则");
            return ResultVO.success(results);
        }
        // 免费广告挽留配置
        if (linkPO != null && linkPO.getFeeFlag() != null && linkPO.getFeeFlag() == 2) {
            results.put("keepDetail0", detailList.get(0));
            return ResultVO.success(results);
        }
        // 自然量用户或者付费链接走支付挽留配置
        // 查询用户是几次充值
        FastMemberOrderRechargePO orParam = new FastMemberOrderRechargePO();
        if (StrUtil.isNotEmpty(keepVO.getDramaId())) {
            orParam.setDramaId(Integer.valueOf(keepVO.getDramaId()));
        } else {
            orParam.setDramaId(0);
        }
        orParam.setMemberId(sessionVO.getMemberId());
        Integer rechargeCount = fastMemberOrderRechargeMapper.queryMemberRechargeCount(orParam);
        List<FastFeeKeepDetailPO> detailPopList = new ArrayList<>();
        for (FastFeeKeepDetailPO detail : detailList) {
            if (rechargeCount == 0 && detail.getMemberType() == 0 && detail.getState() == 1) {
                detailPopList.add(detail);
            } else if (rechargeCount == 1 && detail.getMemberType() == 1 && detail.getState() == 1) {
                detailPopList.add(detail);
            } else if (rechargeCount == 2 && detail.getMemberType() == 2 && detail.getState() == 1) {
                detailPopList.add(detail);
            } else if (rechargeCount >= 3 && detail.getMemberType() == 3 && detail.getState() == 1) {
                detailPopList.add(detail);
            }
        }
        if (detailPopList.size() == 0) {
            results.put("message", "未配置挽留规则");
            return ResultVO.success(results);
        }
        String defaultDetailId = keepVO.getDefaultDetailId(); // 默认选择的id
        String selectDetailId = keepVO.getSelectDetailId(); // 充值模板的选择id
        // 设置档位信息
        for (FastFeeKeepDetailPO detail : detailPopList) {
            detail.setEncryptionDetailId(encode(detail.getId()));
            Integer detailId = null;
            // 判断是否需要查询充值面板
            if ((detail.getRecGearType() == 1 || detail.getRecGearType() == 2) && (StrUtil.isEmpty(defaultDetailId) || StrUtil.isEmpty(selectDetailId))) {
                actionLogService.log("reget_fee", "重新获取充值模板1,defaultDetailId=" + defaultDetailId);
                // 查询档位
                List<FastFeeModelDetailPO> feeDetailList = fastFeeModelService.getMemberFeeModel(1, keepVO.getPhoneOs(), sessionVO);
                for (FastFeeModelDetailPO fmdPO : feeDetailList) {
                    if (StrUtil.isEmpty(defaultDetailId)) {
                        defaultDetailId = fmdPO.getEncryptionId();
                        selectDetailId = fmdPO.getEncryptionId();
                    }
                    if (fmdPO.getDefaultFlag() != null && fmdPO.getDefaultFlag() == 1) {
                        defaultDetailId = fmdPO.getEncryptionId();
                        selectDetailId = fmdPO.getEncryptionId();
                    }
                }
                actionLogService.log("reget_fee", "重新获取充值模板2,defaultDetailId=" + defaultDetailId);
            }
            if (detail.getRecGearType() == 1) {
                // 默认档位
                detailId = decodeInt(defaultDetailId);
            } else if (detail.getRecGearType() == 2) {
                // 选择档位
                detailId = decodeInt(selectDetailId);
            }
            if (detailId != null) {
//                FastFeeModelDetailPO modDetail = fastFeeModelDetailMapper.queryById(detailId);
                FastFeeModelDetailPO modDetail = fastFeeModelDetailService.queryByIdRedis(detailId);
                actionLogService.log("reget_fee", "重新获取充值模板3,modDetail=" + JsonUtil.toString(modDetail));
                if (modDetail != null) {
                    detail.setCoinGive(modDetail.getCoinGive());
                    detail.setCoinRecharge(modDetail.getCoinRecharge());
                    detail.setMoneyRecharge(modDetail.getMoneyRecharge());
                    detail.setMoneyUnit(modDetail.getMoneyUnit());
                    detail.setCorner(modDetail.getCorner());
                    detail.setGearId(modDetail.getGearId());
                    detail.setRemark(modDetail.getRemark());
                    detail.setTitle(modDetail.getTitle());
                    detail.setValidDate(modDetail.getValidDate());
                    detail.setValidUnit(modDetail.getValidUnit());
                    detail.setModelDetailId(detailId);
                    detail.setType(modDetail.getType());
                    detail.setEncryptionId(encode(detailId));
                    if (sessionVO.getClientType() != null && sessionVO.getClientType() == 2) {
                        int diamondCount = fastSettingSystemService.getDiamondCountByMoney(detail.getMoneyRecharge());
                        detail.setDiamondCount(diamondCount);
                        int subDiamondCount = fastSettingSystemService.getDiamondCountByMoney(detail.getMoneySub());
                        detail.setSubDiamondCount(subDiamondCount);
                    }
                }
            }
        }
        for (int i = 0; i < detailPopList.size(); i++) {
            results.put("keepDetail" + detailPopList.get(i).getPopNum(), detailPopList.get(i));
        }
        return ResultVO.success(results);
    }

    public FastFeeKeepBatchPO getKeepGlobal(FastFeeKeepBatchPO item) {
        String key = StaticVar.FEE_KEEP_GLOBAL + item.getRetailId() + "_" + item.getOfficialId() + "_" + item.getFeeFlag();
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            item.setGlobalFlag(1);
            FastFeeKeepBatchPO batch = fastFeeKeepBatchMapper.queryOne(item);
            if (batch != null) {
                batch.setEncryptionId(encode(batch.getId()));
                batch.setDetailList(getKeepDetailList(batch.getId()));
                res = JsonUtil.arrayToJson(batch);
                RedisUtil.set(key, res, 60 * 60);
            }
        }
        if (StrUtil.isNotEmpty(res)) {
            return JsonUtil.toJavaObject(res, FastFeeKeepBatchPO.class);
        }
        return null;
    }


    public List<FastFeeKeepDetailPO> getKeepDetailList(Integer batchId) {
        List<FastFeeKeepDetailPO> detailList = new ArrayList<>();
        String key = StaticVar.KEEP_BATCH_DETAIL_KEY + "_" + batchId;
        String value = RedisUtil.get(key);
        if (StrUtil.isEmpty(value)) {
            FastFeeKeepDetailPO detailParam = new FastFeeKeepDetailPO();
            detailParam.setBatchId(batchId);
            detailParam.setDelFlag(0);
            detailList = fastFeeKeepDetailMapper.queryList(detailParam);
            for (FastFeeKeepDetailPO detail : detailList) {
                detail.setEncryptionId(encode(detail.getId()));
            }
            RedisUtil.set(key, JSON.toJSONString(detailList), RedisUtil.ONE_DAY);
        } else {
            detailList = JSONObject.parseArray(value, FastFeeKeepDetailPO.class);
        }
        return detailList;
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeKeepBatchPO queryById(FastFeeKeepBatchPO item) {
        FastFeeKeepBatchPO batch = fastFeeKeepBatchMapper.queryById(item);
        batch.setDetailList(getKeepDetailList(batch.getId()));
        return batch;
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeKeepBatchPO queryById(Integer id) {
        return fastFeeKeepBatchMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeKeepBatchPO queryOne(FastFeeKeepBatchPO item) {
        item.setDelFlag(0);
        FastFeeKeepBatchPO batch = fastFeeKeepBatchMapper.queryOne(item);
        batch.setDetailList(getKeepDetailList(batch.getId()));
        return batch;
    }

    /**
     * 查询全部
     */
    public List<FastFeeKeepBatchPO> queryList(FastFeeKeepBatchPO item) {
        item.setDelFlag(0);
        return fastFeeKeepBatchMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeKeepBatchPO item, PageVO pageVO) {
        startPage(pageVO);
        item.setDelFlag(0);
        List<FastFeeKeepBatchPO> list = fastFeeKeepBatchMapper.queryList(item);
        for (FastFeeKeepBatchPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        if (pageVO.getLimit() > 999) {
            for (int i = list.size() - 1; i >= 0; i--) {
                if (list.get(i).getUserCount() == 0) {
                    list.remove(i);
                }
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeKeepBatchPO item) {
        item.setDelFlag(0);
        return fastFeeKeepBatchMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeKeepBatchPO item, SessionVO sessionVO) {
        item.setState(0);// 默认禁用
        if (item.getGlobalFlag() == 1) {
            // 验证是否已经存在全局
            FastFeeKeepBatchPO param = new FastFeeKeepBatchPO();
            param.setRetailId(sessionVO.getRetailId());
            param.setOfficialId(sessionVO.getOfficialId());
            param.setGlobalFlag(1);
            param.setDelFlag(0);
            param.setFeeFlag(item.getFeeFlag());
            FastFeeKeepBatchPO keep = fastFeeKeepBatchMapper.queryOne(param);
            if (keep != null) {
                return MethodVO.error("已经存在全局规则，请修改");
            }
            item.setState(1);// 全局默认开启
        }
        // 新增主体规则
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setDelFlag(0);
        item.setRetailId(sessionVO.getRetailId());
        item.setOfficialId(sessionVO.getOfficialId());
        item.setCreatorId(sessionVO.getUserId());
        item.setUpdatorId(sessionVO.getUserId());
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastFeeKeepBatchMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        String key = StaticVar.FEE_KEEP_GLOBAL + item.getRetailId() + "_" + item.getOfficialId() + "_" + item.getFeeFlag();
        RedisUtil.del(key);
        String detailKey = StaticVar.KEEP_BATCH_DETAIL_KEY + "_" + item.getId();
        RedisUtil.del(detailKey);
        // 新增关联详情
        return insertDetail(item);
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFeeKeepBatchPO> list) {
        if (fastFeeKeepBatchMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    // 添加详情
    private MethodVO insertDetail(FastFeeKeepBatchPO item) {
        List<FastFeeKeepDetailPO> detailList = JsonUtil.toList(item.getDetailListStr(), FastFeeKeepDetailPO.class);
        for (FastFeeKeepDetailPO detail : detailList) {
            detail.setBatchId(item.getId());
            if (item.getFeeFlag() == 1) {
                if (detail.getCoinAdd() == null) {
                    transactionRollBack();
                    return MethodVO.error("额外赠币不能为空");
                }
                if (detail.getMoneySub() == null) {
                    transactionRollBack();
                    return MethodVO.error("减免金额不能为空");
                }
                if (detail.getRecGearType() == null) {
                    transactionRollBack();
                    return MethodVO.error("推荐档位类型不能为空");
                }
                if (detail.getPopNum() == null) {
                    transactionRollBack();
                    return MethodVO.error("弹窗顺序不能为空pop=1");
                }
                if (detail.getMemberType() == null) {
                    transactionRollBack();
                    return MethodVO.error("用户充值类型不能为空memberType");
                }
                if (detail.getState() == null) {
                    detail.setState(0);// 此状态默认关闭
                }
                if (detail.getRecGearType() == 3) {
                    // 自定义
                    if (detail.getGearId() == null) {
                        transactionRollBack();
                        return MethodVO.error("档位id不能为空");
                    }
                    FastFeeModelGearPO gearPO = fastFeeModelGearMapper.queryById(detail.getGearId());
                    if (gearPO == null) {
                        transactionRollBack();
                        return MethodVO.error("选择档位为空");
                    }
                    detail.setCoinGive(gearPO.getCoinGive());
                    detail.setCoinRecharge(gearPO.getCoinRecharge());
                    detail.setCorner(gearPO.getCorner());
                    detail.setMoneyRecharge(gearPO.getMoneyRecharge());
                    detail.setMoneyUnit(gearPO.getMoneyUnit());
                    detail.setRemark(gearPO.getRemark());
                    detail.setTitle(gearPO.getTitle());
                    detail.setValidDate(gearPO.getValidDate());
                    detail.setValidUnit(gearPO.getValidUnit());
                    detail.setType(gearPO.getType());
                }
            } else {
                // 免费
                if (detail.getState() == null) {
                    transactionRollBack();
                    return MethodVO.error("弹窗开启状态不能为空");
                }
                if (StrUtil.isEmpty(detail.getTitle())) {
                    transactionRollBack();
                    return MethodVO.error("弹窗标题不能为空");
                }
                if (detail.getUnlockSeriesNum() == null) {
                    transactionRollBack();
                    return MethodVO.error("免费解锁集数不能为空");
                }
                if (detail.getKeepTimes() == null) {
                    transactionRollBack();
                    return MethodVO.error("挽留次数不能为空");
                }
            }
            detail.setCreateTime(DateUtil.getNowDate());
            if (fastFeeKeepDetailMapper.insertSelective(detail) == 0) {
                transactionRollBack();
                return MethodVO.error("新增内容失败");
            }
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateAll(FastFeeKeepBatchPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastFeeKeepBatchMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        // 删除关联
        fastFeeKeepDetailMapper.delByBatchId(item.getId());
        // 更新关联详情
        MethodVO methodVO = insertDetail(item);
        FastFeeKeepBatchPO bPO = fastFeeKeepBatchMapper.queryById(item);
        String key = StaticVar.FEE_KEEP_GLOBAL + bPO.getRetailId() + "_" + bPO.getOfficialId() + "_" + item.getFeeFlag();
        RedisUtil.del(key);
        String detailKey = StaticVar.KEEP_BATCH_DETAIL_KEY + "_" + bPO.getId();
        RedisUtil.del(detailKey);
        return methodVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeKeepBatchPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastFeeKeepBatchMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        String key = StaticVar.FEE_KEEP_GLOBAL + item.getRetailId() + "_" + item.getOfficialId() + "_" + item.getFeeFlag();
        RedisUtil.del(key);
        String detailKey = StaticVar.KEEP_BATCH_DETAIL_KEY + "_" + item.getId();
        RedisUtil.del(detailKey);
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO delUser(FastFeeKeepBatchPO keep) {
        List<String> enIdList = CollUtil.parseStr2List(keep.getEncryptionIds());
        for (String encryptionId : enIdList) {
            Integer id = decodeInt(encryptionId);
            fastFeeKeepBatchMapper.delUser(id);
        }
        return MethodVO.success();
    }

    public ResultVO<?> getFastFeeKeepBatchListByMiniId(FastFeeKeepBatchPO params, PageVO pageVO) {
        FastRetailMiniPO query = new FastRetailMiniPO();
        query.setMiniIds(params.getMiniId().toString());
        List<Integer> retailIds = fastRetailMiniService.queryRetailIdList(query);
        if (CollUtil.isEmpty(retailIds)) {
            return ResultVO.success();
        }
        FastMiniPO fastMiniPO = fastMiniMapper.queryById(params.getMiniId());
        params.setOfficialId(fastMiniPO.getDefOfficialId());
        params.setRetailIds(StrUtil.join(retailIds));
        List<FastFeeKeepBatchPO> list = fastFeeKeepBatchMapper.queryList(params);
        for (FastFeeKeepBatchPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(list);
    }
}
