/*
 * Powered By fast.up
 */
package com.fast.service.fee;

import com.fast.constant.StaticStr;
import com.fast.enums.OperationLogEnum;
import com.fast.mapper.common.FastOperationLogMapper;
import com.fast.mapper.fee.FastFeeModelGlobalSetMapper;
import com.fast.po.common.FastLogOperationPO;
import com.fast.po.fee.FastFeeModelBatchPO;
import com.fast.po.fee.FastFeeModelGlobalSetPO;
import com.fast.po.fee.FastFeeModelPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.FastOperationLogService;
import com.fast.utils.DateUtil;
import com.fast.utils.IPUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastFeeModelGlobalSetService extends BaseService {

    @Autowired
    private FastFeeModelGlobalSetMapper globalSetMapper;
    @Autowired
    private FastOperationLogMapper operationLogMapper;
    @Autowired
    private FastFeeModelService fastFeeModelService;
    @Autowired
    private FastFeeModelBatchService feeModelBatchService;
    @Autowired
    private FastOperationLogService operationLogService;

    /**
     * 查询全局设置
     */
    public FastFeeModelGlobalSetPO queryGlobalSet(FastFeeModelGlobalSetPO item) {
        FastFeeModelGlobalSetPO setParam = new FastFeeModelGlobalSetPO();
        setParam.setOfficialId(item.getOfficialId());
        setParam.setRetailId(item.getRetailId());
        FastFeeModelGlobalSetPO globalSet = globalSetMapper.queryOne(setParam);
        if (globalSet == null) {
            // 新增一条
            Date nowDate = DateUtil.getNowDate();
            globalSet = new FastFeeModelGlobalSetPO();
            globalSet.setRetailId(item.getRetailId());
            globalSet.setOfficialId(item.getOfficialId());
            globalSet.setType(1);// 默认全局
            globalSet.setCreateTime(nowDate);
            globalSet.setUpdateTime(nowDate);
            globalSet.setCreatorId(item.getUpdatorId());
            globalSet.setUpdatorId(item.getUpdatorId());
            if (globalSetMapper.insert(globalSet) == 0) {
                return null;
            }
        }
        return globalSet;
    }

    public FastFeeModelGlobalSetPO queryById(FastFeeModelGlobalSetPO item) {
        return globalSetMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFeeModelGlobalSetPO queryById(Integer id) {
        FastFeeModelGlobalSetPO itemParam = new FastFeeModelGlobalSetPO();
        itemParam.setId(id);
        return globalSetMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFeeModelGlobalSetPO queryOne(FastFeeModelGlobalSetPO item) {
        return globalSetMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFeeModelGlobalSetPO> queryList(FastFeeModelGlobalSetPO item) {
        return globalSetMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFeeModelGlobalSetPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFeeModelGlobalSetPO> list = globalSetMapper.queryList(item);
        for (FastFeeModelGlobalSetPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFeeModelGlobalSetPO item) {
        return globalSetMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFeeModelGlobalSetPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (globalSetMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFeeModelGlobalSetPO> list) {
        if (globalSetMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFeeModelGlobalSetPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (globalSetMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateByRetailId(HttpServletRequest request, SessionVO sessionVO, FastFeeModelGlobalSetPO params) {
        Date nowDate = DateUtil.getNowDate();
        FastFeeModelGlobalSetPO dbData = globalSetMapper.queryOneRetail(params);
        params.setUpdateTime(nowDate);
        if (globalSetMapper.updateByRetailId(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }

        doLog(request, sessionVO, params, dbData, nowDate);
        return MethodVO.success();
    }

    /**
     * 记录操作日志
     *
     * @param request
     * @param sessionVO
     * @param params
     * @param dbData
     * @param nowDate
     */
    private void doLog(HttpServletRequest request, SessionVO sessionVO, FastFeeModelGlobalSetPO params, FastFeeModelGlobalSetPO dbData, Date nowDate) {
        // 查询全局模板规则
        FastFeeModelPO feeModelPO = new FastFeeModelPO();
        feeModelPO.setRetailId(0);
        feeModelPO.setMiniId(sessionVO.getMiniId());
        ResultVO<?> globalRule = fastFeeModelService.queryGlobalList(feeModelPO);

        // 查询自定义模板规则
        FastFeeModelBatchPO feeModelBatchPO = new FastFeeModelBatchPO();
        feeModelBatchPO.setOfficialId(params.getOfficialId());
        ResultVO<?> customRule = feeModelBatchService.queryBatchDetailCustom(feeModelBatchPO);

        // 记录操作日志
        if (params.getType() != null && !StrUtil.equals(params.getType(), dbData.getType())) {
            FastLogOperationPO operationPO = new FastLogOperationPO();
            operationPO.setType(OperationLogEnum.OP_12.index);
            operationPO.setRetailId(dbData.getRetailId());
            operationPO.setObjectId(params.getOfficialId());// 操作对象ID
            operationPO.setObjectType(4);
            operationPO.setIp(IPUtil.ipToLong(request));
            operationPO.setUserId(params.getUpdatorId());
            operationPO.setCreateTime(nowDate);
            operationPO.setOfficialId(dbData.getOfficialId());
            if (params.getType() == 1) {
                operationPO.setObject("全局模板-平台定义模板");// 操作对象
                operationPO.setRemark("由【全局模板-自定义模板】改为【全局模板-平台定义模板】");// 修改内容
                operationPO.setSnapshotOld(JsonUtil.toString(globalRule));
                operationPO.setSnapshotNew(JsonUtil.toString(customRule));
            } else {
                operationPO.setObject("全局模板-自定义模板");// 操作对象
                operationPO.setRemark("由【全局模板-平台定义模板】改为【全局模板-自定义模板】");// 修改内容
                operationPO.setSnapshotOld(JsonUtil.toString(customRule));
                operationPO.setSnapshotNew(JsonUtil.toString(globalRule));
            }
            operationLogService.insertLog(operationPO);
        }
    }
}
