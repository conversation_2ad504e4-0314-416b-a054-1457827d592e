/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.monitor.FastMonitorOpenidMapper;
import com.fast.po.monitor.FastMonitorOpenidPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMonitorOpenidService extends BaseService {

    @Autowired
    private FastMonitorOpenidMapper fastMonitorOpenidMapper;

    // 黑名单刷新进入缓存
    public void flushIntoCacheJob() {
        Integer page = 1;
        Integer limit = 1000;
        boolean loop = true;
        Integer count = 10;
        while (loop && count > 0) {
            count = count - 1;
            FastMonitorOpenidPO moParam = new FastMonitorOpenidPO();
            moParam.setLimit(limit);
            Integer dataStart = (page - 1) * limit;
            moParam.setDataStart(dataStart);
            List<FastMonitorOpenidPO> moList = fastMonitorOpenidMapper.queryFlushList(moParam);
            if (moList.size() < limit) {
                loop = false;
            }
            page++;
            for (FastMonitorOpenidPO item : moList) {
                String key = StaticVar.BLACK_OPENID + item.getOpenid();
                RedisUtil.set(key, StaticVar.OK, 60 * 60 * 24 * 2);
            }
        }
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorOpenidPO queryById(FastMonitorOpenidPO params) {
        return fastMonitorOpenidMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorOpenidPO queryById(Integer id) {
        return fastMonitorOpenidMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMonitorOpenidPO queryOne(FastMonitorOpenidPO params) {
        return fastMonitorOpenidMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMonitorOpenidPO> queryList(FastMonitorOpenidPO params) {
        return fastMonitorOpenidMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMonitorOpenidPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMonitorOpenidPO> list = fastMonitorOpenidMapper.queryList(params);
        for (FastMonitorOpenidPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMonitorOpenidPO params) {
        return fastMonitorOpenidMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMonitorOpenidPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMonitorOpenidMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMonitorOpenidPO> list) {
        if (fastMonitorOpenidMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMonitorOpenidPO params) {
        if (fastMonitorOpenidMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
