/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.fast.mapper.monitor.FastMonitorMemberLoginMapper;
import com.fast.po.monitor.FastMonitorMemberLoginPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMonitorMemberLoginService extends BaseService {

    @Autowired
    private FastMonitorMemberLoginMapper monitorMemberLoginMapper;


    /**
     * 通过id查询单个对象
     */
    public FastMonitorMemberLoginPO queryById(FastMonitorMemberLoginPO params) {
        return monitorMemberLoginMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorMemberLoginPO queryById(Integer id) {
        return monitorMemberLoginMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMonitorMemberLoginPO queryOne(FastMonitorMemberLoginPO params) {
        return monitorMemberLoginMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMonitorMemberLoginPO> queryList(FastMonitorMemberLoginPO params) {
        return monitorMemberLoginMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMonitorMemberLoginPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMonitorMemberLoginPO> list = monitorMemberLoginMapper.queryList(params);
        for (FastMonitorMemberLoginPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMonitorMemberLoginPO params) {
        return monitorMemberLoginMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Async
    public void insertAsync(FastMonitorMemberLoginPO params) {
        if (params.getCreateTime() == null) {
            Date nowTime = DateUtil.getNowDate();
            params.setCreateTime(nowTime);
            params.setCreateDate(nowTime);
        } else {
            params.setCreateDate(params.getCreateTime());
        }
        params.setCountLogin(1);
        if (monitorMemberLoginMapper.updateByMemberId(params) == 0) {
            if (monitorMemberLoginMapper.insertSelective(params) == 0) {
                transactionRollBack();
            }
        }
    }
}
