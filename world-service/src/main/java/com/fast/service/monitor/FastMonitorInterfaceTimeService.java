/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.monitor.FastMonitorInterfaceTimeMapper;
import com.fast.po.monitor.FastMonitorInterfaceTimePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMonitorInterfaceTimeService extends BaseService {

    @Autowired
    private FastMonitorInterfaceTimeMapper fastMonitorInterfaceTimeMapper;


    public FastMonitorInterfaceTimePO monitorStart(String method) {
        String key = StaticVar.MONITOR_INTERFACE_TIME_BACK + method;
        String res = RedisUtil.get(key);
        if (StrUtil.isNotEmpty(res) && "monitor".equals(res)) {
            FastMonitorInterfaceTimePO mifPO = new FastMonitorInterfaceTimePO();
            mifPO.setCreateTime(DateUtil.getNowDate());
            mifPO.setMethod(method);
            mifPO.setState(1);
            fastMonitorInterfaceTimeMapper.insertSelective(mifPO);
            return mifPO;
        }
        return null;
    }

    public void monitorEnd(FastMonitorInterfaceTimePO mifPO, Long memberId) {
        if (mifPO != null) {
            mifPO.setUpdateTime(DateUtil.getNowDate());
            mifPO.setState(2);
            mifPO.setMemberId(memberId);
            fastMonitorInterfaceTimeMapper.updateById(mifPO);
        }
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorInterfaceTimePO queryById(FastMonitorInterfaceTimePO params) {
        return fastMonitorInterfaceTimeMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorInterfaceTimePO queryById(Integer id) {
        return fastMonitorInterfaceTimeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMonitorInterfaceTimePO queryOne(FastMonitorInterfaceTimePO params) {
        return fastMonitorInterfaceTimeMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMonitorInterfaceTimePO> queryList(FastMonitorInterfaceTimePO params) {
        return fastMonitorInterfaceTimeMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMonitorInterfaceTimePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMonitorInterfaceTimePO> list = fastMonitorInterfaceTimeMapper.queryList(params);
        for (FastMonitorInterfaceTimePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMonitorInterfaceTimePO params) {
        return fastMonitorInterfaceTimeMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMonitorInterfaceTimePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMonitorInterfaceTimeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMonitorInterfaceTimePO> list) {
        if (fastMonitorInterfaceTimeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMonitorInterfaceTimePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMonitorInterfaceTimeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
