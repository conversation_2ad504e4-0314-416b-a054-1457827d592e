/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.fast.constant.StaticStr;
import com.fast.mapper.monitor.FastMonitorFlyLogMapper;
import com.fast.po.monitor.FastMonitorFlyLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMonitorFlyLogService extends BaseService {

    @Autowired
    private FastMonitorFlyLogMapper fastMonitorFlyLogMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMonitorFlyLogPO queryById(FastMonitorFlyLogPO params) {
        return fastMonitorFlyLogMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorFlyLogPO queryById(Integer id) {
        return fastMonitorFlyLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMonitorFlyLogPO queryOne(FastMonitorFlyLogPO params) {
        return fastMonitorFlyLogMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMonitorFlyLogPO> queryList(FastMonitorFlyLogPO params) {
        return fastMonitorFlyLogMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMonitorFlyLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMonitorFlyLogPO> list = fastMonitorFlyLogMapper.queryList(params);
        for (FastMonitorFlyLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMonitorFlyLogPO params) {
        return fastMonitorFlyLogMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMonitorFlyLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMonitorFlyLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMonitorFlyLogPO> list) {
        if (fastMonitorFlyLogMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMonitorFlyLogPO params) {
        if (fastMonitorFlyLogMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
