/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberAdvPlayMapper;
import com.fast.mapper.monitor.FastMonitorMiniMapper;
import com.fast.po.member.FastMemberAdvPlayPO;
import com.fast.po.monitor.FastMonitorMiniPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMonitorMiniService extends BaseService {

    @Autowired
    private FastMonitorMiniMapper fastMonitorMiniMapper;
    @Autowired
    private FastMemberAdvPlayMapper fastMMFastMemberAdvPlayMapper;


    /**
     * 添加上报记录
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(SessionVO sessionVO, FastMonitorMiniPO params) {
        if (params.getType() == null) {
            return MethodVO.error("上报类型不能为空type");
        }
        if (StrUtil.isEmpty(params.getAppId())) {
            return MethodVO.error("小程序appid不能为空");
        }
        Integer dramaId = params.getDramaId();
        if (dramaId == null) {
            dramaId = 0;
        }
        if (params.getType() == 2) {
            FastMemberAdvPlayPO mapPO = new FastMemberAdvPlayPO();
            String code = params.getLastSeriesNum();
            if ("1".equals(code)) {
                code = "";
            }
            mapPO.setCode(code);
            mapPO.setAppId(params.getAppId());
            mapPO.setMemberId(sessionVO.getMemberId());
            mapPO.setMiniId(sessionVO.getMiniId());
            mapPO.setOpenid(sessionVO.getOpenid());
            mapPO.setStartLogId(params.getStartLogId());
            mapPO.setCreateTime(DateUtil.getNowDate());
            fastMMFastMemberAdvPlayMapper.insertSelective(mapPO);

            actionLogService.log("wx_ad_error", params.getAppId() + ",openid=" + sessionVO.getOpenid() + ",错误码：" + params.getLastSeriesNum());
        }
        // key
        String key = StaticVar.MONITOR_MINI_ACTION + params.getType() +
                "_" +
                params.getAppId() +
                "_" +
                dramaId;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            // 查询数据库
            FastMonitorMiniPO mmParam = new FastMonitorMiniPO();
            mmParam.setType(params.getType());
            mmParam.setAppId(params.getAppId());
            mmParam.setDramaId(dramaId);
            FastMonitorMiniPO mmPO = fastMonitorMiniMapper.queryOne(mmParam);
            if (mmPO != null) {
                res = mmPO.getId().toString();
                RedisUtil.set(key, mmPO.getId().toString(), 60 * 60);
            }
        }
        Date nowDate = DateUtil.getNowDate();
        if (StrUtil.isNotEmpty(res)) {
            Integer id = Integer.valueOf(res);
            // 已经存在，直接添加异常次数
            FastMonitorMiniPO miniPO = new FastMonitorMiniPO();
            miniPO.setId(id);
            if (sessionVO.getLinkId() != null && sessionVO.getLinkId() > 0) {
                miniPO.setLastLinkId(sessionVO.getLinkId());
            }
            if (params.getLastLinkId() != null) {
                miniPO.setLastSeriesNum(params.getLastLinkId() + "");
            }
            miniPO.setBadLastTime(nowDate);
            fastMonitorMiniMapper.updateTimesById(miniPO);
        } else {
            // 新增
            FastMonitorMiniPO miniPO = new FastMonitorMiniPO();
            miniPO.setType(params.getType());
            miniPO.setAppId(params.getAppId());
            miniPO.setDramaId(dramaId);
            if (sessionVO.getLinkId() != null && sessionVO.getLinkId() > 0) {
                miniPO.setLastLinkId(sessionVO.getLinkId());
            }
            if (params.getLastSeriesNum() != null) {
                miniPO.setLastSeriesNum(params.getLastSeriesNum());
            }
            miniPO.setBadLastTime(nowDate);
            miniPO.setBadStartTime(nowDate);
            miniPO.setBadTimes(1);
            miniPO.setCreateTime(nowDate);
            miniPO.setRemark(params.getRemark());
            fastMonitorMiniMapper.insertSelective(miniPO);
        }
        return MethodVO.success();
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorMiniPO queryById(FastMonitorMiniPO params) {
        return fastMonitorMiniMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorMiniPO queryById(Integer id) {
        return fastMonitorMiniMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMonitorMiniPO queryOne(FastMonitorMiniPO params) {
        return fastMonitorMiniMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMonitorMiniPO> queryList(FastMonitorMiniPO params) {
        return fastMonitorMiniMapper.queryList(params);
    }


    /**
     * 查询总数
     */
    public int queryCount(FastMonitorMiniPO params) {
        return fastMonitorMiniMapper.queryCount(params);
    }


}
