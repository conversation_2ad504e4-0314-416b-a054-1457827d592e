/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.fast.constant.StaticStr;
import com.fast.mapper.monitor.FastMonitorMapper;
import com.fast.po.monitor.FastMonitorPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMonitorService extends BaseService {

    @Autowired
    private FastMonitorMapper fastMonitorMapper;

    public void bigScreen(HttpServletRequest request, HttpServletResponse response, SessionVO sessionVO) {
        StringBuffer htmlBuffer = new StringBuffer();
        htmlBuffer.append("<html>");
        htmlBuffer.append("  <head>");
        htmlBuffer.append("    <title>Big Monitor</title>");
        htmlBuffer.append("    <style style=\"text/css\">");
        htmlBuffer.append("    </style>");
        htmlBuffer.append("  </head>");
        htmlBuffer.append("  <body>");
        htmlBuffer.append("  	<div>监控大屏</div>");
        htmlBuffer.append("  </body>");
        htmlBuffer.append("  <script>");
        htmlBuffer.append("  </script>");
        htmlBuffer.append("</html>");
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("text/html");
            response.getWriter().write(htmlBuffer.toString());
            response.getWriter().close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorPO queryById(FastMonitorPO item) {
        return fastMonitorMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorPO queryById(Integer id) {
        return fastMonitorMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMonitorPO queryOne(FastMonitorPO item) {
        return fastMonitorMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMonitorPO> queryList(FastMonitorPO item) {
        return fastMonitorMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMonitorPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMonitorPO> list = fastMonitorMapper.queryList(item);
        for (FastMonitorPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMonitorPO item) {
        return fastMonitorMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMonitorPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMonitorMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMonitorPO> list) {
        if (fastMonitorMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMonitorPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMonitorMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
