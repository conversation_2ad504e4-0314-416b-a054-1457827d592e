/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.fast.constant.StaticStr;
import com.fast.mapper.monitor.FastMonitorApiLogMapper;
import com.fast.po.monitor.FastMonitorApiLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMonitorApiLogService extends BaseService {

    @Autowired
    private FastMonitorApiLogMapper fastMonitorApiLogMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMonitorApiLogPO queryById(FastMonitorApiLogPO params) {
        return fastMonitorApiLogMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorApiLogPO queryById(Long id) {
        return fastMonitorApiLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMonitorApiLogPO queryOne(FastMonitorApiLogPO params) {
        return fastMonitorApiLogMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMonitorApiLogPO> queryList(FastMonitorApiLogPO params) {
        return fastMonitorApiLogMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMonitorApiLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMonitorApiLogPO> list = fastMonitorApiLogMapper.queryList(params);
        for (FastMonitorApiLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMonitorApiLogPO params) {
        return fastMonitorApiLogMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMonitorApiLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMonitorApiLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMonitorApiLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMonitorApiLogMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
