/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.fast.constant.StaticStr;
import com.fast.mapper.monitor.FastMonitorLinkMapper;
import com.fast.po.monitor.FastMonitorLinkPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMonitorLinkService extends BaseService {

    @Autowired
    private FastMonitorLinkMapper fastMonitorLinkMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMonitorLinkPO queryById(FastMonitorLinkPO item) {
        return fastMonitorLinkMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorLinkPO queryById(Integer id) {
        return fastMonitorLinkMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMonitorLinkPO queryOne(FastMonitorLinkPO item) {
        return fastMonitorLinkMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMonitorLinkPO> queryList(FastMonitorLinkPO item) {
        return fastMonitorLinkMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMonitorLinkPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMonitorLinkPO> list = fastMonitorLinkMapper.queryList(item);
        for (FastMonitorLinkPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMonitorLinkPO item) {
        return fastMonitorLinkMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMonitorLinkPO item) {
        Date nowTime = DateUtil.getNowDate();

        item.setCreateTime(nowTime);
        if (fastMonitorLinkMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMonitorLinkPO> list) {
        if (fastMonitorLinkMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMonitorLinkPO item) {
        if (fastMonitorLinkMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
