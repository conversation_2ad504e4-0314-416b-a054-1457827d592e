/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.alibaba.fastjson.JSONObject;
import com.fast.base.BaseClass;
import com.fast.constant.StaticVar;
import com.fast.constant.StaticYml;
import com.fast.mapper.member.FastMemberMapper;
import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.monitor.MonitorMapper;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.member.FastMemberPO;
import com.fast.po.mini.FastMiniPO;
import com.fast.service.base.BaseService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.monitor.MonitorDataVO;
import com.fast.vo.monitor.MonitorSpeedVO;
import com.fast.vo.sms.SmsSendVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 数据监控
 *
 * <AUTHOR>
 */
@Service
public class MonitorService extends BaseService {

    @Autowired
    private FastMiniMapper fastMiniMapper;
    @Autowired
    private MonitorMapper monitorMapper;
    @Autowired
    private FastMemberMapper memberMapper;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastMemberOrderRechargeMapper fastMemberOrderRechargeMapper;


    /**
     * 渠道链接对应的媒体
     */
    private static final Map<Integer, Integer> linkMediaMap = new HashMap<>();

    // private String alarmPhones = "15868123247,15522676338";
    private final String alarmPhones = "";// 监控报警短信接受电话

    // 前端查询速度监控数据
    public ResultVO<?> getSpeedData(MonitorSpeedVO params) {
        Long startTime = DateUtil.getNowTimeStamp();
        String key = StaticVar.MONITOR_MINI_LIST + "_" + params.getContentType();
        // 查询全部小程序
        List<FastMiniPO> miniList = RedisUtil.getList(key, FastMiniPO.class);
        if (miniList == null) {
            FastMiniPO miniParam = new FastMiniPO();
            miniParam.setState(1);
//            miniParam.setContentType(params.getContentType());
            miniList = fastMiniMapper.queryList(miniParam);
            RedisUtil.setList(key, miniList, 60 * 60 * 24 * 7);
        }
        Map<String, Object> results = new HashMap<String, Object>();
        List<MonitorSpeedVO> minitorList = new ArrayList<>();
        for (int i = 0; i < miniList.size(); i++) {
            FastMiniPO miniPO = miniList.get(i);
            Map<String, Object> results1 = synOneSpeedData(miniPO.getId(), miniPO.getMiniName(), 1, params.getVersion());// 安卓
            Map<String, Object> results2 = synOneSpeedData(miniPO.getId(), miniPO.getMiniName(), 2, params.getVersion());// 苹果
            MonitorSpeedVO speedVO = new MonitorSpeedVO();
            speedVO.setVersion(params.getVersion());
//            String typeName = "";
//            if(miniPO.getType() == 1){
//            	typeName = ":微信";
//            }else if(miniPO.getType() == 2){
//            	typeName = ":抖音";
//            }else if(miniPO.getType() == 3){
//            	typeName = ":H5";
//            }else if(miniPO.getType() == 4){
//            	typeName = ":快手";
//            }
            speedVO.setMiniName(miniPO.getMiniName());
            speedVO.setMiniType(miniPO.getType());
            speedVO.setCurrentVersion(miniPO.getCurrentVersion());
            if (results1 == null) {
                speedVO.setMonitorSwitch("off");// 监控关闭
                speedVO.setSpeedAndroidUser(0);
                speedVO.setSpeedAndroidOrder(0);
                speedVO.setSpeedAndroidBack(0);
                speedVO.setSpeedAndroidUserAlarm(1);
                speedVO.setSpeedAndroidOrderAlarm(1);
                speedVO.setSpeedAndroidBackAlarm(1);
                speedVO.setSpeedIosUser(0);
                speedVO.setSpeedIosOrder(0);
                speedVO.setSpeedIosBack(0);
                speedVO.setSpeedIosUserAlarm(1);
                speedVO.setSpeedIosOrderAlarm(1);
                speedVO.setSpeedIosBackAlarm(1);
                speedVO.setMaxIosBack("0");
                speedVO.setMaxIosOrder("0");
                speedVO.setMaxIosUser("0");
                speedVO.setLastIosBack("0");
                speedVO.setLastIosOrder("0");
                speedVO.setLastIosUser("0");
                speedVO.setMaxAndroidBack("0");
                speedVO.setMaxAndroidOrder("0");
                speedVO.setMaxAndroidUser("0");
                speedVO.setLastAndroidBack("0");
                speedVO.setLastAndroidOrder("0");
                speedVO.setLastAndroidUser("0");
            } else {
                speedVO.setMonitorSwitch("on");// 监控开启
                speedVO.setSpeedAndroidUser((Integer) results1.get("speedUser"));
                speedVO.setSpeedAndroidOrder((Integer) results1.get("speedOrder"));
                speedVO.setSpeedAndroidBack((Integer) results1.get("speedBack"));
                speedVO.setSpeedAndroidUserAlarm((Integer) results1.get("speedUserAlarm"));
                speedVO.setSpeedAndroidOrderAlarm((Integer) results1.get("speedOrderAlarm"));
                speedVO.setSpeedAndroidBackAlarm((Integer) results1.get("speedBackAlarm"));
                speedVO.setSpeedIosUser((Integer) results2.get("speedUser"));
                speedVO.setSpeedIosOrder((Integer) results2.get("speedOrder"));
                speedVO.setSpeedIosBack((Integer) results2.get("speedBack"));
                speedVO.setSpeedIosUserAlarm((Integer) results2.get("speedUserAlarm"));
                speedVO.setSpeedIosOrderAlarm((Integer) results2.get("speedOrderAlarm"));
                speedVO.setSpeedIosBackAlarm((Integer) results2.get("speedBackAlarm"));
                speedVO.setMaxAndroidBack((String) results1.get("maxBack"));
                speedVO.setMaxAndroidOrder((String) results1.get("maxOrder"));
                speedVO.setMaxAndroidUser((String) results1.get("maxUser"));
                speedVO.setLastAndroidBack((String) results1.get("lastBack"));
                speedVO.setLastAndroidOrder((String) results1.get("lastOrder"));
                speedVO.setLastAndroidUser((String) results1.get("lastUser"));
                speedVO.setMaxIosBack((String) results2.get("maxBack"));
                speedVO.setMaxIosOrder((String) results2.get("maxOrder"));
                speedVO.setMaxIosUser((String) results2.get("maxUser"));
                speedVO.setLastIosBack((String) results2.get("lastBack"));
                speedVO.setLastIosOrder((String) results2.get("lastOrder"));
                speedVO.setLastIosUser((String) results2.get("lastUser"));
            }
            minitorList.add(speedVO);
        }
        List<MonitorSpeedVO> minitorSortList = new LinkedList<>();
        for (int i = 0; i < minitorList.size(); i++) {
            int sortNum = 0;
            for (int j = 0; j < minitorSortList.size(); j++) {
                Integer curNum = minitorList.get(i).getSpeedAndroidUser() + minitorList.get(i).getSpeedIosUser();
                Integer sortCurNum = minitorSortList.get(j).getSpeedAndroidUser() + minitorSortList.get(j).getSpeedIosUser();
                if (curNum < sortCurNum) {
                    sortNum = j + 1;
                }
            }
            if (sortNum >= minitorSortList.size()) {
                sortNum = minitorSortList.size();
            }
            minitorSortList.add(sortNum, minitorList.get(i));
        }
        results.put("minitorList", minitorSortList);
        Long endTime = DateUtil.getNowTimeStamp();
        log.info("方法耗时: " + (endTime - startTime) + "毫秒");
        return ResultVO.success("ok", results);
    }


    // 同步单个小程序速度监控数据到缓存
    public Map<String, Object> synOneSpeedData(Integer miniId, String miniName, Integer phoneOs, String version) {
        if (StaticYml.START_MONITOR == null || "off".equals(StaticYml.START_MONITOR)) {
            return null;
        }
        String key = StaticVar.MONITOR_MINI + miniId + "_" + phoneOs + "_" + version;
        String keySwitch = StaticVar.MONITOR_MINI_SWITCH + "_" + miniId;// 小程序是否开启监控，存储在redis
        String monitorSwitch = RedisUtil.get(keySwitch);
        if ("off".equals(monitorSwitch)) {
            return null;
        }
        Map<String, Object> resultMap = RedisUtil.getObject(key, HashMap.class);
        if (resultMap != null) {
            return resultMap;
        }
        Date nowDate = DateUtil.getNowDate();
        Date time10 = DateUtil.addMinutes(nowDate, -10);// 10分钟
        Date timeAll = DateUtil.addHours(DateUtil.getNowDate(), -1);// 24小时
        String lastUserTime = ""; // 最后一条用户入库时间，发短信用
        String lastOrderTime = ""; // 最后一条订单入库时间，发短信用
        String lastBackTime = ""; // 最后一条回传入库时间，发短信用
        // 查询最近10分钟
        MonitorSpeedVO memberPara = new MonitorSpeedVO();
        memberPara.setMonitorType(1);
        memberPara.setMiniId(miniId);
        memberPara.setCreateTime(time10);
        memberPara.setPhoneOs(phoneOs);
        if (StrUtil.isNotEmpty(version)) {
            memberPara.setVersion(version);
        }
        List<FastMemberPO> memberTimeList = memberMapper.queryMonitorList(memberPara);// 最近10分钟用户
        List<FastMemberOrderRechargePO> orderTimeList = fastMemberOrderRechargeMapper.queryOrderMonitorList(memberPara); // 最近10分钟订单
        List<FastMemberOrderRechargePO> backTimeList = fastMemberOrderRechargeMapper.queryBackMonitorList(memberPara); // 最近10分钟回传
        memberPara.setMonitorType(2);
        memberPara.setCreateTime(timeAll);
        List<FastMemberPO> memberCountList = memberMapper.queryMonitorList(memberPara);// 最近20条用户（24小时内）
        List<FastMemberOrderRechargePO> orderCountList = fastMemberOrderRechargeMapper.queryOrderMonitorList(memberPara);// 最近20条订单（24小时内）
        List<FastMemberOrderRechargePO> backCountList = fastMemberOrderRechargeMapper.queryBackMonitorList(memberPara);// 最近20条（24小时内）

        Integer speedUser = memberTimeList.size();
        Integer speedOrder = orderTimeList.size();
        Integer speedBack = backTimeList.size();

        String maxUser = "";
        String maxOrder = "";
        String maxBack = "";
        String lastUser = "";
        String lastOrder = "";
        String lastBack = "";
        // 计算速度,计算健康，最后用户的创建时间是最大间隔的2倍
        Long speedUserMaxSpace = 0L;// 最大间隔
        Long speedUserLastSpace = 0L;// 最后用户到现在毫秒
        Long speedUserAllSpace = 0L;// 总间隔数
        Long speedUserAvg = 0L;
        for (int i = 0; i < memberCountList.size(); i++) {
            if (i == 0) {
                lastUserTime = DateUtil.format07(memberCountList.get(i).getCreateTime());
                speedUserLastSpace = nowDate.getTime() - memberCountList.get(i).getCreateTime().getTime();
                lastUser = DateUtil.getLongTimeStr(speedUserLastSpace);
            } else {
                if (i < memberCountList.size() - 1) {
                    Long tempLong = memberCountList.get(i).getCreateTime().getTime() - memberCountList.get(i + 1).getCreateTime().getTime();
                    speedUserAllSpace += tempLong;
                    if (tempLong > speedUserMaxSpace) {
                        speedUserMaxSpace = tempLong;
                    }
                }
            }
        }
        if (memberCountList.size() > 0) {
            speedUserAvg = speedUserAllSpace / memberCountList.size();// 平均间隔
        }
        maxUser = DateUtil.getLongTimeStr(speedUserMaxSpace);
        Integer speedUserAlarm = isAlarm(speedUserAvg, speedUserMaxSpace, speedUserLastSpace, memberTimeList.size());// 0：健康，1：报警
        // 计算速度,订单
        Long speedOrderMaxSpace = 0L;// 最大间隔
        Long speedOrderLastSpace = 0L;// 最后用户到现在毫秒
        for (int i = 0; i < orderCountList.size(); i++) {
            if (i == 0) {
                lastOrderTime = DateUtil.format07(orderCountList.get(i).getCreateTime());
                speedOrderLastSpace = nowDate.getTime() - orderCountList.get(i).getCreateTime().getTime();
                lastOrder = DateUtil.getLongTimeStr(speedOrderLastSpace);
            } else {
                if (i < orderCountList.size() - 1) {
                    Long tempLong = orderCountList.get(i).getCreateTime().getTime() - orderCountList.get(i + 1).getCreateTime().getTime();
                    if (tempLong > speedOrderMaxSpace) {
                        speedOrderMaxSpace = tempLong;
                    }
                }
            }
        }
        maxOrder = DateUtil.getLongTimeStr(speedOrderMaxSpace);
        Integer speedOrderAlarm = isAlarm(speedUserAvg, speedOrderMaxSpace, speedOrderLastSpace, orderTimeList.size());// 0：健康，1：报警
        // 计算速度,回传
        Long speedBackMaxSpace = 0L;// 最大间隔
        Long speedBackLastSpace = 0L;// 最后用户到现在毫秒
        for (int i = 0; i < backCountList.size(); i++) {
            if (i == 0) {
                lastBackTime = DateUtil.format07(backCountList.get(i).getCreateTime());
                speedBackLastSpace = nowDate.getTime() - backCountList.get(i).getCreateTime().getTime();
                lastBack = DateUtil.getLongTimeStr(speedBackLastSpace);
            } else {
                if (i < backCountList.size() - 1) {
                    Long tempLong = backCountList.get(i).getCreateTime().getTime() - backCountList.get(i + 1).getCreateTime().getTime();
                    if (tempLong > speedBackMaxSpace) {
                        speedBackMaxSpace = tempLong;
                    }
                }
            }
        }
        maxBack = DateUtil.getLongTimeStr(speedBackMaxSpace);
        Integer speedBackAlarm = isAlarm(speedUserAvg, speedBackMaxSpace, speedBackLastSpace, backTimeList.size());// 0：健康，1：报警
        Map<String, Object> resultsMap = new HashMap<String, Object>();
        resultsMap.put("speedUser", speedUser);
        resultsMap.put("speedOrder", speedOrder);
        resultsMap.put("speedBack", speedBack);
        resultsMap.put("speedUserAlarm", speedUserAlarm);
        resultsMap.put("speedOrderAlarm", speedOrderAlarm);
        resultsMap.put("speedBackAlarm", speedBackAlarm);
        resultsMap.put("maxUser", maxUser);
        resultsMap.put("maxOrder", maxOrder);
        resultsMap.put("maxBack", maxBack);
        resultsMap.put("lastUser", lastUser);
        resultsMap.put("lastOrder", lastOrder);
        resultsMap.put("lastBack", lastBack);
        // 判断是否需要发送短信
        sendAlarmSms(speedUser, speedUserAlarm, 1, phoneOs, miniId, miniName, lastUserTime);
        sendAlarmSms(speedUser, speedOrderAlarm, 2, phoneOs, miniId, miniName, lastOrderTime);
        sendAlarmSms(speedUser, speedBackAlarm, 3, phoneOs, miniId, miniName, lastBackTime);
        Integer flushSeconds = 15;
        if (speedUser != null && speedUser < 10) {
            flushSeconds = 150;
        } else if (speedUser != null && speedUser < 50) {
            flushSeconds = 30;
        }
        RedisUtil.setObject(key, resultsMap, flushSeconds);
        return resultsMap;
    }

    /**
     * 判断是否需要告警
     *
     * @param alarm
     * @param type     1：用户，2：订单，3：回传
     * @param os
     * @param miniId
     * @param miniName
     * @return
     */
    private void sendAlarmSms(Integer speedUserSize, Integer alarm, Integer type, Integer os, Integer miniId, String miniName, String lastTime) {
        String key = StaticVar.MONITOR_MINI_STATUS + "_" + miniId + "_" + os + "_" + type;
        String keySwitch = StaticVar.MONITOR_MINI_SWITCH + "_" + miniId;
        String alarmStatus = RedisUtil.get(key);
        String alarmMonitor = RedisUtil.get(keySwitch);
        Integer sendSms = 0;// 0不发送，1发送
        String templateCode = "";

        if (type == 1) {
            // 用户
            if (alarm == 1 && (StrUtil.isEmpty(alarmStatus) || "0".equals(alarmStatus))) {
                sendSms = 1;
                templateCode = "SMS_255231441";// 报警模板
            }
            if (alarm == 0 && "1".equals(alarmStatus)) {
//    			sendSms = 1;
//    			templateCode = "SMS_255306449";// 解除报警
            }
        } else if (type == 2 || type == 3) {
            // 订单 回传
            if (speedUserSize > 7 && alarm == 1 && (StrUtil.isEmpty(alarmStatus) || "0".equals(alarmStatus))) {
                sendSms = 1;
                templateCode = "SMS_255231441";// 报警模板
            }
            if (alarm == 0 && "1".equals(alarmStatus)) {
//    			sendSms = 1;
//    			templateCode = "SMS_255306449";// 解除报警
            }
        }
        RedisUtil.set(key, alarm + "");

        if (sendSms == 1 && (StrUtil.isEmpty(alarmMonitor) || "on".equals(alarmMonitor))) {
            String keySend = StaticVar.MONITOR_MINI_SEND + "_" + miniId + "_" + os + "_" + type;
            String sendValue = RedisUtil.get(keySend);
            if (StrUtil.isEmpty(sendValue)) {
                String osName = "";
                if (os == 1) {
                    osName = "安卓端";
                } else if (os == 2) {
                    osName = "苹果端";
                }
                SmsSendVO sendVO = new SmsSendVO();
                sendVO.setPhoneNumbers(alarmPhones);
                sendVO.setSignName(StaticVar.SMS_SIGN_NAME);
                sendVO.setTemplateCode(templateCode);
                JSONObject param = new JSONObject();
                param.put("mininame", miniName + "_" + osName);
                String typeName = "";
                if (type == 1) {
                    typeName = "用户";
                } else if (type == 2) {
                    typeName = "订单";
                } else if (type == 3) {
                    typeName = "回传";
                }
                param.put("type", typeName);
                param.put("time", lastTime);
                sendVO.setTemplateParam(param.toJSONString());// 参数
//                SmsUtil.sendSms(sendVO);
                // 发送钉钉告警信息
                if (BaseClass.isDevTestProfile()) {
                    return;
                }
                StringBuilder str = new StringBuilder();
                str.append("服务预警 : 【监控流程数据】");
                str.append("告警详情 : ")
                        .append("小程序:")
                        .append(miniName)
                        .append("_")
                        .append(osName)
                        .append("-")
                        .append(typeName)
                        .append("不足，")
                        .append("请及时关注！");
                // DingDingMessageUtil.sendTextMessage(str.toString());
                RedisUtil.set(keySend, "1", 60 * 60);// 1个小时不重发
            } else {
                log.info("已经发送过了");
            }
        }
    }

    /**
     * 判断是否报警
     *
     * @param avgCount  // 平均间隔时间毫秒
     * @param maxCount
     * @param lastCount
     * @return 0不报警，1报警
     */
    private Integer isAlarm(Long avgCount, Long maxCount, Long lastCount, Integer timeSize) {
        // 倍数
        int aux = 2;
        if (avgCount < 1000 * 60 * 10) {
            aux = 2;
        } else if (avgCount < 1000 * 60 * 30) {
            aux = 3;
        } else {
            aux = 5;
        }
//        log.info("最大间隔:" + maxCount);
//        log.info("最后持续:" + lastCount);
//        log.info("当前间隔:" + aux);
        if (maxCount * aux < lastCount && timeSize == 0) {
            return 1;
        }
        return 0;
    }

    /**
     * 数据监控
     *
     * @param params
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<MonitorDataVO> getAppData(MonitorDataVO params) {
        FastMiniPO queryMini = new FastMiniPO();
        queryMini.setIds(params.getMiniIds());
        queryMini.setContentType(params.getContentType());
        List<FastMiniPO> miniList = fastMiniMapper.queryList(queryMini);
        List<MonitorDataVO> list = new ArrayList<>(miniList.size());
        if (CollUtil.isEmpty(miniList)) {
            return list;
        }
        // 组装查询条件
        MonitorDataVO query = new MonitorDataVO();
        query.setMiniIds(params.getMiniIds());
        query.setRetailIds(params.getRetailIds());
        query.setCreateTimeS(params.getCreateTimeS());
        query.setCreateTimeE(params.getCreateTimeE());
        query.setContentType(params.getContentType());

        // 查询公众号新增人数
        List<MonitorDataVO> listPubMemberNum = monitorMapper.queryPubMemberNumGroupByMini(query);
        Map<Integer, MonitorDataVO> mapPubMemberNum = new HashMap<>();
        listPubMemberNum.forEach(cur -> mapPubMemberNum.put(cur.getMiniId(), cur));

        // 查询小程序新增人数
        List<MonitorDataVO> listMiniMemberNum = monitorMapper.queryMiniMemberNumGroupByMini(query);
        Map<Integer, MonitorDataVO> mapMiniMemberNum = new HashMap<>();
        listMiniMemberNum.forEach(cur -> mapMiniMemberNum.put(cur.getMiniId(), cur));

        // 查询新增充值笔数
        List<MonitorDataVO> listOrderNum = monitorMapper.queryMemberOrderNumGroupByMini(query);
        Map<Integer, MonitorDataVO> mapOrderNum = new HashMap<>();
        listOrderNum.forEach(cur -> mapOrderNum.put(cur.getMiniId(), cur));

        // 查询新增充值金额
        List<MonitorDataVO> listOrderMoney = monitorMapper.queryMemberOrderMoneyProfitGroupByMini(query);
        Map<Integer, MonitorDataVO> mapOrderMoney = new HashMap<>();
        listOrderMoney.forEach(cur -> mapOrderMoney.put(cur.getMiniId(), cur));

        // 查询新增用户充值人数
        List<MonitorDataVO> listNewOrderNum = monitorMapper.queryMemberNewOrderNumGroupByMini(query);
        Map<Integer, MonitorDataVO> mapNewOrderNum = new HashMap<>();
        listNewOrderNum.forEach(cur -> mapNewOrderNum.put(cur.getMiniId(), cur));

        // 小程序url生成次数
        int s = DateUtil.format06Int(params.getCreateTimeS());
        int e = DateUtil.format06Int(params.getCreateTimeE());
        int now = DateUtil.getNowTime06();
        query.setCountDateS(s);
        query.setCountDateE(e);
        List<MonitorDataVO> listUrlMakeCountNum = monitorMapper.queryMiniUrlMakeCountGroupByMini(query);
        Map<Integer, MonitorDataVO> mapUrlMakeCountNum = new HashMap<>();
        listUrlMakeCountNum.forEach(cur -> mapUrlMakeCountNum.put(cur.getMiniId(), cur));

        // 组装数据
        for (FastMiniPO mini : miniList) {
            MonitorDataVO vo = new MonitorDataVO();
            vo.setMiniId(mini.getId());
            vo.setMiniName(mini.getMiniName());
            vo.setType(mini.getType());
            // 公众号用户新增数
            MonitorDataVO data = mapPubMemberNum.get(mini.getId());
            if (data == null || data.getMemberNum() == null) {
                vo.setMemberNum(0);
            } else {
                vo.setMemberNum(data.getMemberNum());
            }
            // 小程序用户新增数
            data = mapMiniMemberNum.get(mini.getId());
            if (data == null || data.getMemberMiniNum() == null) {
                vo.setMemberMiniNum(0);
            } else {
                vo.setMemberMiniNum(data.getMemberMiniNum());
            }
            // 用户充值笔数
            data = mapOrderNum.get(mini.getId());
            if (data == null || data.getOrderNum() == null) {
                vo.setOrderNum(0);
            } else {
                vo.setOrderNum(data.getOrderNum());
            }
            // 用户充值金额
            data = mapOrderMoney.get(mini.getId());
            if (data == null || data.getOrderMoney() == null) {
                vo.setOrderMoneyProfit(BigDecimal.ZERO);
            } else {
                vo.setOrderMoneyProfit(data.getOrderMoney());
            }
            // 新增用户充值人数
            data = mapNewOrderNum.get(mini.getId());
            if (data == null || data.getNewOrderNum() == null) {
                vo.setNewOrderNum(0);
            } else {
                vo.setNewOrderNum(data.getNewOrderNum());
            }
            if (s == now) {
                String key = String.format(StaticVar.WECHAT_MINI_URL_SCHEME, mini.getAppId(), now);
                vo.setUrlMakeCountRedis(toInteger(RedisUtil.get(key), 0));
            } else {
                data = mapUrlMakeCountNum.get(mini.getId());
                if (data == null || data.getUrlMakeCount() == null) {
                    vo.setUrlMakeCountRedis(0);
                } else {
                    vo.setUrlMakeCountRedis(data.getUrlMakeCount());
                }
                // 如果结束日期是今天, 需要加上redis里面的数据
                if (e == now) {
                    String key = String.format(StaticVar.WECHAT_MINI_URL_SCHEME, mini.getAppId(), now);
                    vo.setUrlMakeCountRedis(StrUtil.defaultIfBlank(vo.getUrlMakeCountRedis(), 0) + toInteger(RedisUtil.get(key), 0));
                }
            }
            list.add(vo);
        }

        return list;
    }

}
