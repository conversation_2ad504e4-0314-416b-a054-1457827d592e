/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.fast.constant.StaticStr;
import com.fast.mapper.monitor.FastMonitorPointTimeMapper;
import com.fast.po.monitor.FastMonitorPointTimePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMonitorPointTimeService extends BaseService {

    @Autowired
    private FastMonitorPointTimeMapper fastMonitorPointTimeMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMonitorPointTimePO queryById(FastMonitorPointTimePO params) {
        return fastMonitorPointTimeMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMonitorPointTimePO queryById(Integer id) {
        return fastMonitorPointTimeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMonitorPointTimePO queryOne(FastMonitorPointTimePO params) {
        return fastMonitorPointTimeMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMonitorPointTimePO> queryList(FastMonitorPointTimePO params) {
        return fastMonitorPointTimeMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMonitorPointTimePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMonitorPointTimePO> list = fastMonitorPointTimeMapper.queryList(params);
        for (FastMonitorPointTimePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMonitorPointTimePO params) {
        return fastMonitorPointTimeMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMonitorPointTimePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMonitorPointTimeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMonitorPointTimePO> list) {
        if (fastMonitorPointTimeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMonitorPointTimePO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMonitorPointTimeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
