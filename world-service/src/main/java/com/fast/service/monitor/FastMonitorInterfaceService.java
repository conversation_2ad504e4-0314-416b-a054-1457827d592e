/*
 * Powered By fast.up
 */
package com.fast.service.monitor;

import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberMapper;
import com.fast.mapper.monitor.FastMonitorApiLogMapper;
import com.fast.mapper.monitor.FastMonitorInterfaceMapper;
import com.fast.po.member.FastMemberPO;
import com.fast.po.monitor.FastMonitorApiLogPO;
import com.fast.po.monitor.FastMonitorInterfacePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Service
public class FastMonitorInterfaceService extends BaseService {

    private static final String[] HIGH_API = {"/fastDrama/getDrama", "/fastMiniSetting/nologin/getVersion", "/fastMemberUnlockDrama/unlockDrama", "/member/getLinkFreeUnlockInfo",
            "/member/getUserRelate", "/getDetailPlus", "/getDetailSimple"};
    @Autowired
    private FastMonitorInterfaceMapper fastMonitorInterfaceMapper;
    @Autowired
    private FastMonitorApiLogMapper monitorApiLogMapper;
    @Autowired
    private FastMemberMapper fastMemberMapper;

    private static final Map<String, FastMonitorInterfacePO> interfaceMap;
    private static long lastUpdateTime; // 最后更新时间

    static {
        interfaceMap = new ConcurrentHashMap<>();
        lastUpdateTime = System.currentTimeMillis();
    }

    /**
     * 添加接口访问
     */
    public void addInterfaceCount(HttpServletRequest request) {
        if (interfaceMap.size() > 1000) { // 防止自爆，自动释放
            interfaceMap.clear();
        }
        String mapKey = DateUtil.getNowTime09Str() + "_" + request.getRequestURI();

        request.setAttribute(StaticVar.MONITOR_INTERFACE_TIME, System.currentTimeMillis()); // 设置接口请求开始时间
        request.setAttribute(StaticVar.MONITOR_INTERFACE_KEY, mapKey); // 设置接口key,防止跨天获取的key不一致

        FastMonitorInterfacePO miPO = interfaceMap.get(mapKey);
        if (miPO == null) {
            miPO = getInitMonitorInterfacePO(request.getRequestURI());
        }
        miPO.setVisitCount(miPO.getVisitCount() + 1);
        interfaceMap.put(mapKey, miPO);
    }

    /**
     * 更新接口访问
     */
    @Async
    public void updateInterfaceCount(Long memberId, long startTime, String mapKey, String uri) {
        long costTime = System.currentTimeMillis() - startTime;
        FastMonitorInterfacePO miPO = interfaceMap.get(mapKey);
        if (miPO == null) {
            miPO = getInitMonitorInterfacePO(uri);
        }
        if (costTime < 500) {
            miPO.setSpeedOneCount(miPO.getSpeedOneCount() + 1);
        } else if (costTime < 1000) {
            miPO.setSpeedTwoCount(miPO.getSpeedTwoCount() + 1);
        } else if (costTime < 3000) {
            miPO.setSpeedThreeCount(miPO.getSpeedThreeCount() + 1);
        } else if (costTime < 10000) {
            miPO.setSpeedFourCount(miPO.getSpeedFourCount() + 1);
        } else {
            miPO.setSpeedFiveCount(miPO.getSpeedFiveCount() + 1);
        }
        if (miPO.getVisitCount() > 15) {
            actionLogService.log("moi_interface", "满足30次开始发起刷新" + miPO.getInterfaceUrl());
            flushFastMonitorInterfacePO(miPO);
        }
        // 判断是否同步到缓存(每分钟判断一次)
        if (System.currentTimeMillis() - lastUpdateTime > 1000 * 60) {
            actionLogService.log("moi_interface", "每分钟刷新一次开始");
            for (String key : interfaceMap.keySet()) {
                FastMonitorInterfacePO miPOItem = interfaceMap.get(key);
                // 单个url 每2分钟刷新一次
                if (System.currentTimeMillis() - miPOItem.getLastUpdateTime() > 1000 * 60 && miPOItem.getVisitCount() > 0) {
                    actionLogService.log("moi_interface", "大于2分钟，刷新开始" + miPOItem.getInterfaceUrl());
                    // 单个url2分钟
                    flushFastMonitorInterfacePO(interfaceMap.get(key));
                }
            }
            // 重置检查时间
            lastUpdateTime = System.currentTimeMillis();
        }

        // 统计关键接口调用情况
        if (biggerZero(memberId)) {
            for (String api : HIGH_API) {
                if (uri.contains(api)) {
                    FastMemberPO memberDB = fastMemberMapper.queryByMemberId(memberId);
                    if (memberDB != null) {
                        Date nowDate = DateUtil.getNowDate();
                        FastMonitorApiLogPO apiLogPO = new FastMonitorApiLogPO();
                        apiLogPO.setMemberId(memberId);
                        apiLogPO.setLinkId(memberDB.getLinkId());
                        apiLogPO.setCreateDate(nowDate);
                        apiLogPO.setCreateTime(nowDate);
                        apiLogPO.setUpdateTime(nowDate);
                        apiLogPO.setUri(uri);
                        if (monitorApiLogMapper.updateByMemberId(apiLogPO) == 0) {
                            apiLogPO.setLinkId(memberDB.getLinkId());
                            apiLogPO.setMiniId(memberDB.getMiniId());
                            apiLogPO.setPhoneOs(memberDB.getPhoneOs());
                            apiLogPO.setDramaId(memberDB.getDramaId());
                            apiLogPO.setApiCount(1);
                            monitorApiLogMapper.insertSelective(apiLogPO);
                        }
                    }
                    break;
                }
            }
        }
    }

    /**
     * 刷新进入缓存
     */
    private void flushFastMonitorInterfacePO(FastMonitorInterfacePO item) {
        String key = StaticVar.MONITOR_INTERFACE_CACHE_ONE + "__" + item.getStatisDateStr() + "__" + item.getInterfaceUrl();
        // 数据刷到缓存中去
        RedisUtil.sadd(StaticVar.MONITOR_INTERFACE_CACHE_KEY, key, 60 * 60);
        RedisUtil.incrBy(key + "_visitcount", item.getVisitCount(), 60 * 60 * 24);
        RedisUtil.incrBy(key + "_speed1", item.getSpeedOneCount(), 60 * 60 * 24);
        RedisUtil.incrBy(key + "_speed2", item.getSpeedTwoCount(), 60 * 60 * 24);
        RedisUtil.incrBy(key + "_speed3", item.getSpeedThreeCount(), 60 * 60 * 24);
        RedisUtil.incrBy(key + "_speed4", item.getSpeedFourCount(), 60 * 60 * 24);
        RedisUtil.incrBy(key + "_speed5", item.getSpeedFiveCount(), 60 * 60 * 24);
        // 本地缓存清零
        item.setSpeedFiveCount(0L);
        item.setSpeedFourCount(0L);
        item.setSpeedOneCount(0L);
        item.setSpeedThreeCount(0L);
        item.setSpeedTwoCount(0L);
        item.setVisitCount(0L);
    }

    /**
     * 刷新缓存进入入库
     */
    public void flushCache2DbJob() {
        // 全部key集合
        Set<String> resSet = RedisUtil.smembers(StaticVar.MONITOR_INTERFACE_CACHE_KEY);
        if (resSet != null && resSet.size() > 0) {
            for (String key : resSet) {
                actionLogService.log("moi_interface", "job-获取lock成功，key=" + key);
                String visitCountStr = RedisUtil.get(key + "_visitcount");
                String speed1Str = RedisUtil.get(key + "_speed1");
                String speed2Str = RedisUtil.get(key + "_speed2");
                String speed3Str = RedisUtil.get(key + "_speed3");
                String speed4Str = RedisUtil.get(key + "_speed4");
                String speed5Str = RedisUtil.get(key + "_speed5");

                RedisUtil.set(key + "_visitcount", "0");
                RedisUtil.set(key + "_speed1", "0");
                RedisUtil.set(key + "_speed2", "0");
                RedisUtil.set(key + "_speed3", "0");
                RedisUtil.set(key + "_speed4", "0");
                RedisUtil.set(key + "_speed5", "0");

                Long visitCount = 0L;
                Long speed1 = 0L;
                Long speed2 = 0L;
                Long speed3 = 0L;
                Long speed4 = 0L;
                Long speed5 = 0L;

                if (StrUtil.isNotEmpty(visitCountStr)) {
                    visitCount = Long.valueOf(visitCountStr);
                }
                if (StrUtil.isNotEmpty(speed1Str)) {
                    speed1 = Long.valueOf(speed1Str);
                }
                if (StrUtil.isNotEmpty(speed2Str)) {
                    speed2 = Long.valueOf(speed2Str);
                }
                if (StrUtil.isNotEmpty(speed3Str)) {
                    speed3 = Long.valueOf(speed3Str);
                }
                if (StrUtil.isNotEmpty(speed4Str)) {
                    speed4 = Long.valueOf(speed4Str);
                }
                if (StrUtil.isNotEmpty(speed5Str)) {
                    speed5 = Long.valueOf(speed5Str);
                }
                FastMonitorInterfacePO miToUpdate = new FastMonitorInterfacePO();
                miToUpdate.setVisitCount(visitCount);
                miToUpdate.setSpeedOneCount(speed1);
                miToUpdate.setSpeedTwoCount(speed2);
                miToUpdate.setSpeedThreeCount(speed3);
                miToUpdate.setSpeedFourCount(speed4);
                miToUpdate.setSpeedFiveCount(speed5);
                String resId = RedisUtil.get(key + "_id");
                String staticDateStr = key.split("__")[1];
                String interfaceUrl = key.split("__")[2];
                Date statisDate = DateUtil.format09(staticDateStr);
                if (StrUtil.isEmpty(resId)) {
                    FastMonitorInterfacePO miParam = new FastMonitorInterfacePO();
                    miParam.setStatisDate(statisDate);
                    miParam.setInterfaceUrl(interfaceUrl);
                    miParam.setType(1);
                    FastMonitorInterfacePO miExist = fastMonitorInterfaceMapper.queryOne(miParam);
                    if (miExist != null) {
                        resId = miExist.getId().toString();
                        RedisUtil.set(key + "_id", resId, 60 * 60 * 2);
                    }
                }
                Date timeNow = DateUtil.getNowDate();
                if (StrUtil.isNotEmpty(resId)) {
                    // 更新
                    miToUpdate.setId(Long.valueOf(resId));
                    miToUpdate.setUpdateTime(timeNow);
                    fastMonitorInterfaceMapper.updateCountById(miToUpdate);
                } else {
                    // 新增
                    miToUpdate.setStatisDate(statisDate);
                    miToUpdate.setInterfaceUrl(interfaceUrl);
                    miToUpdate.setCreateTime(timeNow);
                    miToUpdate.setUpdateTime(timeNow);
                    fastMonitorInterfaceMapper.insertSelective(miToUpdate);
                    RedisUtil.set(key + "_id", miToUpdate.getId().toString(), 60 * 60 * 2);
                }
                if (statisDate.before(DateUtil.beginOfDay())) {
                    // 昨天的，删除
                    RedisUtil.srem(StaticVar.MONITOR_INTERFACE_CACHE_KEY, key);
                }
            }
        }
    }

    /**
     * 获取一个默认对象
     */
    private FastMonitorInterfacePO getInitMonitorInterfacePO(String url) {
        Date timeNow = DateUtil.getNowDate();
        FastMonitorInterfacePO miPO = new FastMonitorInterfacePO();
        miPO.setLastUpdateTime(System.currentTimeMillis());
        miPO.setStatisDate(timeNow);
        miPO.setStatisDateStr(DateUtil.format09(timeNow));
        miPO.setInterfaceUrl(url);
        miPO.setType(1);
        return miPO;
    }

}
