/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticStr;
import com.fast.mapper.setting.FastSettingMediaBackLogMapper;
import com.fast.po.setting.FastSettingMediaBackLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 媒体返点设置
 *
 * <AUTHOR>
 */
@Service
public class FastSettingMediaBackLogService extends BaseService {

    @Autowired
    private FastSettingMediaBackLogMapper settingMediaBackLogMapper;

    /**
     * 通过id查询单个对象
     */
    public FastSettingMediaBackLogPO queryById(FastSettingMediaBackLogPO item) {
        return settingMediaBackLogMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingMediaBackLogPO queryById(Integer id) {
        return settingMediaBackLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingMediaBackLogPO queryOne(FastSettingMediaBackLogPO item) {
        return settingMediaBackLogMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastSettingMediaBackLogPO> queryList(FastSettingMediaBackLogPO item) {
        return settingMediaBackLogMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingMediaBackLogPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastSettingMediaBackLogPO> list = settingMediaBackLogMapper.queryHisList(item);
        for (FastSettingMediaBackLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingMediaBackLogPO item) {
        return settingMediaBackLogMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSettingMediaBackLogPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (settingMediaBackLogMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSettingMediaBackLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (params.getEffectDate().compareTo(nowTime) <= 0) {
            return MethodVO.error("生效日期必须是次日");
        }
        FastSettingMediaBackLogPO po = new FastSettingMediaBackLogPO();
        po.setPid(params.getPid());
        po.setDelFlag(0);
        FastSettingMediaBackLogPO lastEffectOne = settingMediaBackLogMapper.queryLastEffectOne(po);
        if (lastEffectOne != null && lastEffectOne.getEffectDate().compareTo(params.getEffectDate()) >= 0) {
            return MethodVO.error("生效日期必须大于最后生效日期");
        }

        params.setCreateTime(nowTime);
        if (settingMediaBackLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDel(FastSettingMediaBackLogPO params) {
        params.setDelFlag(1);
        if (settingMediaBackLogMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
