/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticStr;
import com.fast.mapper.setting.FastSettingArticleMapper;
import com.fast.po.setting.FastSettingArticlePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastSettingArticleService extends BaseService {

    @Autowired
    private FastSettingArticleMapper fastSettingArticleMapper;

    /**
     * 通过id查询单个对象
     */
    public FastSettingArticlePO queryById(FastSettingArticlePO item) {
        return fastSettingArticleMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingArticlePO queryById(Integer id) {
        FastSettingArticlePO itemParam = new FastSettingArticlePO();
        itemParam.setId(id);
        return fastSettingArticleMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingArticlePO queryOne(FastSettingArticlePO item) {
        return fastSettingArticleMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastSettingArticlePO> queryList(FastSettingArticlePO item) {
        return fastSettingArticleMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingArticlePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastSettingArticlePO> list = fastSettingArticleMapper.queryList(item);
        for (FastSettingArticlePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingArticlePO item) {
        return fastSettingArticleMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSettingArticlePO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastSettingArticleMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSettingArticlePO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastSettingArticleMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
