/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.setting.FastSettingArticleMapper;
import com.fast.mapper.setting.FastSettingFloatMapper;
import com.fast.po.setting.FastSettingArticlePO;
import com.fast.po.setting.FastSettingFloatPO;
import com.fast.service.base.BaseService;
import com.fast.service.oss.OssService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastSettingFloatService extends BaseService {

    @Autowired
    private FastSettingFloatMapper settingFloatMapper;
    @Autowired
    private FastSettingArticleMapper articleMapper;
    @Autowired
    private OssService ossService;

    /**
     * 通过id查询单个对象
     */
    public FastSettingFloatPO queryById(FastSettingFloatPO item) {
        FastSettingFloatPO po = settingFloatMapper.queryById(item);
        if (po != null) {
            po.setEncryptionId(encode(po.getId()));
        }
        return po;
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingFloatPO queryById(Integer id) {
        FastSettingFloatPO itemParam = new FastSettingFloatPO();
        itemParam.setId(id);
        return settingFloatMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingFloatPO queryOne(FastSettingFloatPO item) {
        return settingFloatMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastSettingFloatPO> queryList(FastSettingFloatPO item) {
        return settingFloatMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingFloatPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastSettingFloatPO> list = settingFloatMapper.queryList(item);
        for (FastSettingFloatPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询全部(小程序专用)
     */
    public ResultVO<?> queryList4Member(FastSettingFloatPO item, PageVO pageVO) {
        List<FastSettingFloatPO> list;
        String key = StaticVar.SETTING_FLOAT_LIST + item.getOfficialId();
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return ResultVO.success(getDefaultPageListData());
            }
            list = JsonUtil.toList(value, FastSettingFloatPO.class);
        } else {
            list = settingFloatMapper.queryList(item);
            if (CollUtil.isEmpty(list)) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                for (FastSettingFloatPO cur : list) {
                    cur.setEncryptionId(encode(cur.getId()));
                    if (cur.getJumpType() == 4) {
                        cur.setEncryptionActivityId(encode(cur.getJumpContent()));
                    }
                    cur.setArticleHeadImg(ossService.replaceOssDomain(false, cur.getArticleHeadImg()));
                    cur.setArticleContent(ossService.replaceOssDomain(false, cur.getArticleContent()));
                }
                RedisUtil.set(key, JsonUtil.toString(list), RedisUtil.TIME_2D);
            }
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingFloatPO item) {
        return settingFloatMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSettingFloatPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (params.getJumpType() == 2) {
            FastSettingArticlePO article = new FastSettingArticlePO();
            BeanUtils.copyProperties(params, article);
            article.setRetailId(params.getRetailId());
            article.setOfficialId(params.getOfficialId());
            article.setCreatorId(params.getCreatorId());
            article.setCreateTime(nowTime);
            if (articleMapper.insertSelective(article) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
            params.setJumpArticleId(article.getId());
        }
        params.setCreateTime(nowTime);
        if (settingFloatMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSettingFloatPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (params.getJumpType() == 2) {
            FastSettingArticlePO article = new FastSettingArticlePO();
            BeanUtils.copyProperties(params, article);
            if (params.getJumpArticleId() == null) {
                article.setRetailId(params.getRetailId());
                article.setOfficialId(params.getOfficialId());
                article.setCreatorId(params.getCreatorId());
                article.setCreateTime(nowTime);
                if (articleMapper.insertSelective(article) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
                params.setJumpArticleId(article.getId());
            } else {
                article.setId(params.getJumpArticleId());
                article.setUpdatorId(params.getCreatorId());
                article.setUpdateTime(nowTime);
                if (articleMapper.updateById(article) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
                params.setJumpArticleId(null);
            }
        }
        params.setUpdateTime(nowTime);
        if (settingFloatMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新-启用/禁用
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateState(FastSettingFloatPO params) {
        FastSettingFloatPO po = new FastSettingFloatPO();
        Date nowTime = DateUtil.getNowDate();
        po.setUpdateTime(nowTime);
        po.setUpdatorId(params.getUpdatorId());
        po.setId(params.getId());
        po.setState(params.getState());
        if (settingFloatMapper.updateById(po) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新-删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDel(FastSettingFloatPO params) {
        FastSettingFloatPO del = new FastSettingFloatPO();
        Date nowTime = DateUtil.getNowDate();
        del.setUpdateTime(nowTime);
        del.setUpdatorId(params.getUpdatorId());
        del.setId(params.getId());
        del.setDelFlag(StaticVar.YES);
        if (settingFloatMapper.updateById(del) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
