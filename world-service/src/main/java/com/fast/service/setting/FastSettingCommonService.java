/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.setting.FastSettingCommonMapper;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.setting.FastSettingCommonPO;
import com.fast.service.base.BaseService;
import com.fast.service.cache.CacheResetService;
import com.fast.service.mini.FastMiniContVersionService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastSettingCommonService extends BaseService {

    @Autowired
    private FastSettingCommonMapper fastSettingCommonMapper;
    @Autowired
    private CacheResetService cacheResetService;
    @Autowired
    private FastMiniMapper fastMiniMapper;
    @Autowired
    private FastMiniContVersionService fastMiniContVersionService;

    /**
     * 通过id查询单个对象
     */
    public FastSettingCommonPO queryById(FastSettingCommonPO item) {
        return fastSettingCommonMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingCommonPO queryById(Integer id) {
        return fastSettingCommonMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingCommonPO queryOne(FastSettingCommonPO item) {
        return fastSettingCommonMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastSettingCommonPO> queryList(FastSettingCommonPO item) {
        return fastSettingCommonMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingCommonPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastSettingCommonPO> list = fastSettingCommonMapper.queryList(item);
        for (FastSettingCommonPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingCommonPO item) {
        return fastSettingCommonMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSettingCommonPO item) {
        FastSettingCommonPO cParam = new FastSettingCommonPO();
        cParam.setAppType(item.getAppType());
        FastSettingCommonPO cPO = fastSettingCommonMapper.queryOne(cParam);
        if (cPO != null) {
            return MethodVO.error("已经存在，请更新");
        }
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastSettingCommonMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        if (item.getAppType() == 1) {
            cacheResetService.resetCacheMiniSetOnly(null);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastSettingCommonPO> list) {
        if (fastSettingCommonMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSettingCommonPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastSettingCommonMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        FastSettingCommonPO cPO = fastSettingCommonMapper.queryById(item.getId());
        if (cPO != null && cPO.getAppType() == 1) {
            cacheResetService.resetCacheMiniSetOnly(null);
        }

        FastMiniPO query = new FastMiniPO();
        query.setType(item.getAppType());
        List<FastMiniPO> miniList = fastMiniMapper.queryList(query);

        // 清除setting缓存
        miniList.forEach(cur -> {
            // RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + cur.getId());
            // 多版本兼容
            List<Integer> cvids = fastMiniContVersionService.getIdByMiniId(cur.getId());
            if (CollUtil.isNotEmpty(cvids)) {
                for (Integer cvid : cvids) {
                    RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + cur.getId() + ":" + cvid);
                }
            }
        });
        return MethodVO.success();
    }
}
