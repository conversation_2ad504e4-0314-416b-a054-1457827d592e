/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticStr;
import com.fast.mapper.setting.FastSettingMediaBackMapper;
import com.fast.po.setting.FastSettingMediaBackPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 媒体返点设置
 *
 * <AUTHOR>
 */
@Service
public class FastSettingMediaBackService extends BaseService {

    @Autowired
    private FastSettingMediaBackMapper settingMediaBackMapper;

    /**
     * 通过id查询单个对象
     */
    public FastSettingMediaBackPO queryById(FastSettingMediaBackPO item) {
        return settingMediaBackMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingMediaBackPO queryById(Integer id) {
        return settingMediaBackMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingMediaBackPO queryOne(FastSettingMediaBackPO item) {
        return settingMediaBackMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastSettingMediaBackPO> queryList(FastSettingMediaBackPO item) {
        return settingMediaBackMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingMediaBackPO item, PageVO pageVO) {
        if (settingMediaBackMapper.queryCount(null) == 0) {
            Date nowTime = DateUtil.getNowDate();
            // 初始化数据 mediaType:媒体 1=巨量;2=ADQ;3=快手;4=快手native;5=百度;6=抖音挂载;7=快手挂载
            for (int i = 1; i <= 7; i++) {
                FastSettingMediaBackPO po = new FastSettingMediaBackPO();
                po.setCreatorId(item.getCreatorId());
                po.setCreateTime(nowTime);
                po.setMediaType(i);
                po.setBackRatio(BigDecimal.ZERO);
                po.setEffectDate(nowTime);
                settingMediaBackMapper.insertSelective(po);
            }
        }
        startPage(pageVO);
        List<FastSettingMediaBackPO> list = settingMediaBackMapper.queryList(item);
        for (FastSettingMediaBackPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getEffectDateMax() == null) {
                cur.setEffectDateMax(cur.getEffectDate());
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingMediaBackPO item) {
        return settingMediaBackMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSettingMediaBackPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (settingMediaBackMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新分成配置生效日期
     */
    public void synMoneyDivideSetting() {
        Date nowDate = DateUtil.beginOfDay();
        FastSettingMediaBackPO query = new FastSettingMediaBackPO();
        query.setNowDate(nowDate);
        List<FastSettingMediaBackPO> list = settingMediaBackMapper.queryListUpdate(query);
        for (FastSettingMediaBackPO cur : list) {
            settingMediaBackMapper.updateById(cur);
        }
    }

    /**
     * 查询返点规则
     *
     * @param date
     * @return
     */
    public Map<Integer, BigDecimal> queryEffectGroupByMediaType(Date date) {
        String key = "effect_ratio_media_type_v3:" + DateUtil.format06(date);
        String val = RedisUtil.get(key);
        if (notEmpty(val)) {
            return JsonUtil.toMapIntObject(val, BigDecimal.class);
        }
        List<FastSettingMediaBackPO> effectGroupByMediaType = settingMediaBackMapper.queryEffectGroupByMediaType(date);
        if (CollUtil.hasContent(effectGroupByMediaType)) {
            Map<Integer, BigDecimal> map = effectGroupByMediaType.stream().collect(Collectors.toMap(FastSettingMediaBackPO::getMediaType, FastSettingMediaBackPO::getBackRatioActual));
            RedisUtil.setObject(key, map, RedisUtil.TIME_2D);
            return map;
        }
        return new HashMap<>();
    }

}
