/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticStr;
import com.fast.mapper.setting.FastSettingMediaSettlementMapper;
import com.fast.po.setting.FastSettingMediaSettlementPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastSettingMediaSettlementService extends BaseService {

    @Autowired
    private FastSettingMediaSettlementMapper fastSettingMediaSettlementMapper;

    /**
     * 通过id查询单个对象
     */
    public FastSettingMediaSettlementPO queryById(FastSettingMediaSettlementPO params) {
        return fastSettingMediaSettlementMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingMediaSettlementPO queryById(Integer id) {
        return fastSettingMediaSettlementMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingMediaSettlementPO queryOne(FastSettingMediaSettlementPO params) {
        return fastSettingMediaSettlementMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastSettingMediaSettlementPO> queryList(FastSettingMediaSettlementPO params) {
        return fastSettingMediaSettlementMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingMediaSettlementPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastSettingMediaSettlementPO> list = fastSettingMediaSettlementMapper.queryList(params);
        for (FastSettingMediaSettlementPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setEffectMonthStr(cur.getEffectMonth().toString().substring(0, 4) + "-" + cur.getEffectMonth().toString().substring(4));
            cur.setVirtualRatio(cur.getVirtualRatio().multiply(BigDecimal.valueOf(100L)));
            cur.setNormalRatio(cur.getNormalRatio().multiply(BigDecimal.valueOf(100L)));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingMediaSettlementPO params) {
        return fastSettingMediaSettlementMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSettingMediaSettlementPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastSettingMediaSettlementMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastSettingMediaSettlementPO> list) {
        if (fastSettingMediaSettlementMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSettingMediaSettlementPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastSettingMediaSettlementMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
