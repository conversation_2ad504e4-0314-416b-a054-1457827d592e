/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.setting.FastSettingRiskMapper;
import com.fast.po.setting.FastSettingRiskPO;
import com.fast.service.base.BaseService;
import com.fast.service.mini.FastMiniService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class FastSettingRiskService extends BaseService {

    @Autowired
    private FastSettingRiskMapper fastSettingRiskMapper;
    @Autowired
    private FastMiniService fastMiniService;

    /**
     * 查询抖小ios的im配置
     */
    public FastSettingRiskPO getSettingRiskTiktokMiniRedis(Integer miniId, Integer enterType) {
        String keySettingRisk = StaticVar.IOS_RISK + miniId + "_" + enterType;
        FastSettingRiskPO riskPO = null;
        String settingRiskStr = RedisUtil.get(keySettingRisk);
        if (StrUtil.isNotEmpty(settingRiskStr)) {
            riskPO = JsonUtil.toJavaObject(settingRiskStr, FastSettingRiskPO.class);
        }
        if (riskPO == null) {
            FastSettingRiskPO riskParam = new FastSettingRiskPO();
            riskParam.setMiniId(miniId);
            riskParam.setType(enterType);
            riskPO = fastSettingRiskMapper.queryOne(riskParam);
            if (riskPO == null) {
                // 初始化
                initSettingRiskTiktokMini(miniId, enterType);
                riskPO = fastSettingRiskMapper.queryOne(riskParam);
            }
            if (riskPO != null) {
                RedisUtil.set(keySettingRisk, JsonUtil.toString(riskPO), RedisUtil.ONE_DAY);
            }
        }
        return riskPO;
    }

    private void initSettingRiskTiktokMini(Integer miniId, Integer type) {
        FastSettingRiskPO riskPO = new FastSettingRiskPO();
        riskPO.setMiniId(miniId);
        riskPO.setCreatorId(0);
        riskPO.setUpdatorId(0);
        Date timeNow = DateUtil.getNowDate();
        riskPO.setCreateTime(timeNow);
        riskPO.setUpdateTime(timeNow);
        FastSettingRiskPO riskParam = new FastSettingRiskPO();
        riskParam.setMiniId(miniId);
        if (type != null) {
            riskPO.setType(type);
            fastSettingRiskMapper.insertSelective(riskPO);
        } else {
            List<FastSettingRiskPO> riskList = fastSettingRiskMapper.queryList(riskParam);
            Set<Integer> idSet = new HashSet<>();
            for (FastSettingRiskPO risk : riskList) {
                idSet.add(risk.getType());
            }
            for (int i = 1; i <= 5; i++) {
                if (!idSet.contains(i)) {
                    riskPO.setType(i);
                    fastSettingRiskMapper.insertSelective(riskPO);
                }
            }
        }
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingRiskPO queryById(FastSettingRiskPO item) {
        return fastSettingRiskMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingRiskPO queryById(Integer id) {
        return fastSettingRiskMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingRiskPO queryOne(FastSettingRiskPO item) {
        return fastSettingRiskMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastSettingRiskPO> queryList(FastSettingRiskPO item) {
        return fastSettingRiskMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingRiskPO item, PageVO pageVO) {
        initSettingRiskTiktokMini(item.getMiniId(), null);
        startPage(pageVO);
        List<FastSettingRiskPO> list = fastSettingRiskMapper.queryList(item);
        for (FastSettingRiskPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingRiskPO item) {
        return fastSettingRiskMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized MethodVO insertOrUpdate(SessionVO sessionVO, List<FastSettingRiskPO> list) {
        Date nowTime = DateUtil.getNowDate();
        for (FastSettingRiskPO risk : list) {
            if (risk.getType() == null) {
                return MethodVO.error("用户来源分类不能为空type");
            }
            if (risk.getRechargeFlag() == null) {
                return MethodVO.error("充值开关不能为空");
            }
            FastMiniVO mini = fastMiniService.queryInfoByRedis(risk.getMiniId());
            if (mini == null) {
                return MethodVO.error("小程序不存在或被禁用");
            }
            if (risk.getRechargeFlag() == 1 && mini.getType() == 1) {
                // 微小程序充值开关打开，继续验证
                if (StrUtil.isEmpty(risk.getRechargeTime())) {
                    return MethodVO.error("充值时间不能为空");
                }
                if (risk.getRechargeTotalFlag() == null) {
                    return MethodVO.error("全局充值开关不能为空");
                }
                if (risk.getRechargeOneFlag() == null) {
                    return MethodVO.error("单剧充值开关不能为空");
                }
                if (risk.getTotalBaseCount() == null) {
                    return MethodVO.error("满足全量的剧数量，不能为空totalBaseCount");
                }
                if (risk.getTotalRechargeCount() == null) {
                    return MethodVO.error("满足全量的充值次数，不能为空");
                }
                if (risk.getTotalSelect() == null) {
                    return MethodVO.error("全量的条件，不能为空");
                }
                if (risk.getFreeAllCount() == null) {
                    return MethodVO.error("免费播放总时长不能为空");
                }
                if (risk.getFreeSeriesCount() == null) {
                    return MethodVO.error("免费播放集数不能为空");
                }
                if (risk.getFreeSeriesRatio() == null) {
                    return MethodVO.error("免费单集播放时长比例不能为空");
                }
                if (risk.getOneSelect() == null) {
                    return MethodVO.error("单集的条件不能为空");
                }
            } else if (mini.getType() == 2) {
                // 抖音小程序验证
                if (risk.getImActiveFlag() == null) {
                    return MethodVO.error("活跃开关不能为空");
                }
                if (risk.getImHisFlag() == null) {
                    return MethodVO.error("IM调起开关不能为空");
                }
                if (risk.getImRechargeDownFlag() == null) {
                    return MethodVO.error("充值金额开关不能为空");
                }
                if (risk.getImRechargeDownFlag() == 1 && risk.getImRechargeDown() == null) {
                    return MethodVO.error("累计金额不能为空");
                }
                if (risk.getImRechargeTimesFlag() == null) {
                    return MethodVO.error("充值次数开关不能为空");
                }
                if (risk.getImRechargeTimesFlag() == 1 && risk.getImRechargeTimes() == null) {
                    return MethodVO.error("充值次数不能为空");
                }
                if (risk.getImRecentFlag() == null) {
                    return MethodVO.error("播放记录开关不能为空");
                }
                if (risk.getImRecentFlag() == 1 && risk.getImRecentCount() == null) {
                    return MethodVO.error("播放集数不能为空");
                }
                if (risk.getImRecentFlag() == 1 && risk.getImRecentTimeAll() == null) {
                    return MethodVO.error("播放累计时长不能为空");
                }
                if (risk.getImRecentFlag() == 1 && risk.getImRecentTimeOne() == null) {
                    return MethodVO.error("播放单集时长不能为空");
                }
                if (risk.getImRecentFlag() == 1 && risk.getImRecentSelect() == null) {
                    return MethodVO.error("播放组合条件选择不能为空");
                }
            }
            risk.setUpdatorId(sessionVO.getUserId());
            risk.setUpdateTime(nowTime);
            // 查询是否存在
            Integer id = null;
            if (StrUtil.isNotEmpty(risk.getEncryptionId())) {
                id = decodeInt(risk.getEncryptionId());
            }
            if (id == null) {
                FastSettingRiskPO srParam = new FastSettingRiskPO();
                srParam.setMiniId(risk.getMiniId());
                srParam.setType(risk.getType());
                FastSettingRiskPO srPO = fastSettingRiskMapper.queryOne(srParam);
                if (srPO != null) {
                    id = srPO.getId();
                }
            }
            if (id != null) {
                risk.setId(id);
                if (fastSettingRiskMapper.updateById(risk) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            } else {
                risk.setCreateTime(nowTime);
                risk.setCreatorId(sessionVO.getUserId());
                if (fastSettingRiskMapper.insertSelective(risk) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            }
            String keySettingRisk = StaticVar.IOS_RISK + risk.getMiniId() + "_" + risk.getType();
            RedisUtil.del(keySettingRisk);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastSettingRiskPO> list) {
        if (fastSettingRiskMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSettingRiskPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastSettingRiskMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
