/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticStr;
import com.fast.mapper.setting.FastSettingMediaProfitMapper;
import com.fast.po.setting.FastSettingMediaProfitPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastSettingMediaProfitService extends BaseService {

    @Autowired
    private FastSettingMediaProfitMapper fastSettingMediaProfitMapper;

    /**
     * 通过id查询单个对象
     */
    public FastSettingMediaProfitPO queryById(FastSettingMediaProfitPO params) {
        return fastSettingMediaProfitMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingMediaProfitPO queryById(Integer id) {
        return fastSettingMediaProfitMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingMediaProfitPO queryOne(FastSettingMediaProfitPO params) {
        return fastSettingMediaProfitMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastSettingMediaProfitPO> queryList(FastSettingMediaProfitPO params) {
        return fastSettingMediaProfitMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingMediaProfitPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastSettingMediaProfitPO> list = fastSettingMediaProfitMapper.queryList(params);
        for (FastSettingMediaProfitPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setEffectMonthStr(cur.getEffectMonth().toString().substring(0, 4) + "-" + cur.getEffectMonth().toString().substring(4));
            cur.setCompanyRatio(cur.getCompanyRatio().multiply(BigDecimal.valueOf(100L)));
            cur.setStaffRatio(cur.getStaffRatio().multiply(BigDecimal.valueOf(100L)));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingMediaProfitPO params) {
        return fastSettingMediaProfitMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSettingMediaProfitPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastSettingMediaProfitMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastSettingMediaProfitPO> list) {
        if (fastSettingMediaProfitMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSettingMediaProfitPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastSettingMediaProfitMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }


}
