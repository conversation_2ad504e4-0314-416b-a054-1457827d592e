/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticStr;
import com.fast.mapper.setting.FastSettingSystemMapper;
import com.fast.po.setting.FastSettingSystemPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastSettingSystemService extends BaseService {

    @Autowired
    private FastSettingSystemMapper fastSettingSystemMapper;


    private static Integer diamondCount;
    private static BigDecimal diamondRecharge = BigDecimal.valueOf(0.1);
    private static final Map<String, FastSettingSystemPO> ssMap = new HashMap<>();
//    private static FastSettingSystemPO ssPO;

    /**
     * 查询短信渠道的全局配置
     */
    public Integer getSmsChannel() {
        FastSettingSystemPO settingSystemPO = getSettingSystemCache("sms_channel", "2");
        return Integer.valueOf(settingSystemPO.getContent());
    }

    /**
     * 查询后台发短信短信渠道的全局配置
     */
    public Integer getAdminSmsChannel() {
        FastSettingSystemPO settingSystemPO = getSettingSystemCache("admin_sms_channel", "2");
        return Integer.valueOf(settingSystemPO.getContent());
    }

    /**
     * 本地缓存获取全局设置(10分钟，提前1分钟随机过期)
     */
    public FastSettingSystemPO getSettingSystemCache(String code, String defContent) {
        boolean flushCache = false;
        FastSettingSystemPO ssPO = ssMap.get(code);
        if (ssPO == null) {
            flushCache = true;
        } else {
            long expireCacheTime = ssPO.getExpireCacheTime();
            long timeNow = DateUtil.getNowDate().getTime();
            if (expireCacheTime - timeNow < 0) {
                flushCache = true;
            } else if (expireCacheTime - timeNow < 1000 * 100) {
                // 缓存过期前1分钟，开始更新
                if (StrUtil.getRandomInt(0, 100) < 10) {
                    // 随机触发更新机制，防止并发更新
                    flushCache = true;
                }
            }
        }
        if (flushCache) {
            actionLogService.log("flush_setting_cache", code);
            FastSettingSystemPO ssParam = new FastSettingSystemPO();
            ssParam.setCode(code);
            FastSettingSystemPO settingSystemPO = fastSettingSystemMapper.queryOne(ssParam);
            Date timeNow = DateUtil.getNowDate();
            if (settingSystemPO == null && StrUtil.isNotEmpty(defContent)) {
                settingSystemPO = new FastSettingSystemPO();
                settingSystemPO.setCode(code);
                settingSystemPO.setContent(defContent);
                settingSystemPO.setRemark("系统自动生成");
                settingSystemPO.setCreateTime(timeNow);
                settingSystemPO.setUpdateTime(timeNow);
                fastSettingSystemMapper.insertSelective(settingSystemPO);
            }
            ssPO = settingSystemPO;
            if (ssPO != null) {
                ssPO.setExpireCacheTime(timeNow.getTime() + 1000 * 60 * 10);
                ssMap.put(code, ssPO);
            }
        }
        return ssPO;
    }

    /**
     * 金额转换为钻石数量
     */
    public Integer getDiamondCountByMoney(BigDecimal money) {
        if (money == null) {
            return 0;
        }
        return money.multiply(BigDecimal.valueOf(getDiamondCount()))
                .setScale(0, RoundingMode.UP)
                .intValue();
    }

    /**
     * 查询1元等于多少钻
     */
    public Integer getDiamondCount() {
        if (diamondCount == null) {
            // 数据库查询
            FastSettingSystemPO ssParam = new FastSettingSystemPO();
            ssParam.setCode("diamond_count");
            FastSettingSystemPO ssPO = fastSettingSystemMapper.queryOne(ssParam);
            if (ssPO == null) {
                diamondCount = 10;
            } else {
                diamondCount = Integer.valueOf(ssPO.getContent());
            }
        }
        return diamondCount;
    }

    /**
     * 1钻等于多少元
     */
    public BigDecimal getDiamondMoney() {
        if (diamondRecharge == null) {
            // 数据库查询
            FastSettingSystemPO ssParam = new FastSettingSystemPO();
            ssParam.setCode("diamond_recharge");
            FastSettingSystemPO ssPO = fastSettingSystemMapper.queryOne(ssParam);
            if (ssPO == null) {
                diamondRecharge = BigDecimal.valueOf(0.1);
            } else {
                diamondRecharge = toBigDecimal(ssPO.getContent());
            }
        }
        return diamondRecharge;
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingSystemPO queryById(FastSettingSystemPO item) {
        return fastSettingSystemMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingSystemPO queryById(Integer id) {
        return fastSettingSystemMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingSystemPO queryOne(FastSettingSystemPO item) {
        return fastSettingSystemMapper.queryOne(item);
    }

    public FastSettingSystemPO queryByCode(String code) {
        FastSettingSystemPO item = new FastSettingSystemPO();
        item.setCode(code);
        return fastSettingSystemMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastSettingSystemPO> queryList(FastSettingSystemPO item) {
        return fastSettingSystemMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingSystemPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastSettingSystemPO> list = fastSettingSystemMapper.queryList(item);
        for (FastSettingSystemPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingSystemPO item) {
        return fastSettingSystemMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSettingSystemPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastSettingSystemMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastSettingSystemPO> list) {
        if (fastSettingSystemMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSettingSystemPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastSettingSystemMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateByCode(FastSettingSystemPO item) {
        FastSettingSystemPO param = new FastSettingSystemPO();
        param.setCode(item.getCode());
        FastSettingSystemPO existOne = queryOne(param);
        if (existOne == null) {
            if (StrUtil.isEmpty(item.getRemark())) {
                item.setRemark(item.getContent());
            }
            item.setCreateTime(DateUtil.getNowDate());
            // 新增
            if (fastSettingSystemMapper.insertSelective(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        } else {
            // 更新
            item.setId(existOne.getId());
            Date nowTime = DateUtil.getNowDate();
            item.setUpdateTime(nowTime);
            if (fastSettingSystemMapper.updateById(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        return MethodVO.success();
    }
}
