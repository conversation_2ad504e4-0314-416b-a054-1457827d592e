/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticVar;
import com.fast.mapper.setting.FastMemberOrderSettingMapper;
import com.fast.po.setting.FastMemberOrderSettingPO;
import com.fast.service.base.BaseService;
import com.fast.utils.JsonUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderSettingService extends BaseService {

    @Autowired
    private FastMemberOrderSettingMapper orderSettingMapper;

    /**
     * 查询缓存信息
     */
    public FastMemberOrderSettingPO queryInfoByRedis(int type) {
        FastMemberOrderSettingPO po;
        String key = StaticVar.ORDER_FEE_SETTING + type;
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            po = JsonUtil.toJavaObject(value, FastMemberOrderSettingPO.class);
        } else {
            po = orderSettingMapper.queryByType(type);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_90D);
                return null;
            } else {
                po.setEncryptionId(encode(po.getId()));
                RedisUtil.set(key, JsonUtil.toString(po), RedisUtil.TIME_90D);
            }
        }
        return po;
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderSettingPO queryById(FastMemberOrderSettingPO item) {
        return orderSettingMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderSettingPO queryById(Integer id) {
        return orderSettingMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderSettingPO queryOne(FastMemberOrderSettingPO item) {
        return orderSettingMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderSettingPO> queryList(FastMemberOrderSettingPO item) {
        return orderSettingMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderSettingPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderSettingPO> list = orderSettingMapper.queryList(item);
        for (FastMemberOrderSettingPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderSettingPO item) {
        return orderSettingMapper.queryCount(item);
    }

}
