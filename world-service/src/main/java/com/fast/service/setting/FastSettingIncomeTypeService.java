/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.fast.constant.StaticStr;
import com.fast.mapper.setting.FastSettingIncomeTypeMapper;
import com.fast.po.setting.FastSettingIncomeTypePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastSettingIncomeTypeService extends BaseService {

    @Autowired
    private FastSettingIncomeTypeMapper fastSettingIncomeTypeMapper;

    /**
     * 通过id查询单个对象
     */
    public FastSettingIncomeTypePO queryById(FastSettingIncomeTypePO params) {
        return fastSettingIncomeTypeMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingIncomeTypePO queryById(Integer id) {
        return fastSettingIncomeTypeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingIncomeTypePO queryOne(FastSettingIncomeTypePO params) {
        return fastSettingIncomeTypeMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastSettingIncomeTypePO> queryList(FastSettingIncomeTypePO params) {
        return fastSettingIncomeTypeMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingIncomeTypePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastSettingIncomeTypePO> list = fastSettingIncomeTypeMapper.queryList(params);
        for (FastSettingIncomeTypePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingIncomeTypePO params) {
        return fastSettingIncomeTypeMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSettingIncomeTypePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastSettingIncomeTypeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastSettingIncomeTypePO> list) {
        if (fastSettingIncomeTypeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSettingIncomeTypePO params) {
        if (params.getId() < 100) {
            return MethodVO.error("系统默认项目不能编辑");
        }
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastSettingIncomeTypeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastSettingIncomeTypePO params) {
        if (params.getId() < 100) {
            return MethodVO.error("系统默认项目不能删除");
        }
        if (fastSettingIncomeTypeMapper.deleteById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

}
