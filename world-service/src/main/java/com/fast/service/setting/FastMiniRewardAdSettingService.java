/*
 * Powered By fast.up
 */
package com.fast.service.setting;

import com.alibaba.fastjson.JSON;
import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberMapper;
import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.mapper.member.FastMemberSubLinkMapper;
import com.fast.mapper.setting.FastMiniRewardAdSettingMapper;
import com.fast.mapper.unlock.FastMemberUnlockRewardLogMapper;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.member.FastMemberPO;
import com.fast.po.member.FastMemberSubLinkPO;
import com.fast.po.member.FastMemberUnlockRewardLogPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.setting.FastMiniRewardAdSettingPO;
import com.fast.service.base.BaseService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniRewardAdSettingService extends BaseService {

    @Autowired
    private FastMiniRewardAdSettingMapper fastMiniRewardAdSettingMapper;
    @Autowired
    private FastMemberUnlockRewardLogMapper fastMemberUnlockRewardLogMapper;
    @Autowired
    private FastMemberOrderRechargeMapper fastMemberOrderRechargeMapper;
    @Autowired
    private FastMemberMapper fastMemberMapper;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastMemberSubLinkMapper fastMemberSubLinkMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniRewardAdSettingPO queryById(FastMiniRewardAdSettingPO item) {
        return fastMiniRewardAdSettingMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniRewardAdSettingPO queryById(Integer id) {
        return fastMiniRewardAdSettingMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniRewardAdSettingPO queryOne(FastMiniRewardAdSettingPO item) {
        return fastMiniRewardAdSettingMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniRewardAdSettingPO> queryList(FastMiniRewardAdSettingPO item) {
        return fastMiniRewardAdSettingMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniRewardAdSettingPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniRewardAdSettingPO> list = fastMiniRewardAdSettingMapper.queryList(item);
        for (FastMiniRewardAdSettingPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniRewardAdSettingPO item) {
        return fastMiniRewardAdSettingMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniRewardAdSettingPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMiniRewardAdSettingMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniRewardAdSettingPO> list) {
        if (fastMiniRewardAdSettingMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniRewardAdSettingPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMiniRewardAdSettingMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 判断是否弹出广告解锁框
     */
    public ResultVO<?> whetherShowRewardAd(SessionVO sessionVO) {
//        if(sessionVO.getMemberType() == null || sessionVO.getMemberType() != 2){
//            return ResultVO.error("非抖音小程序用户登录！");
//        }
        if (sessionVO.getEnterType() == null) {
            if (sessionVO.getLinkId() != null && sessionVO.getLinkId() > 0) {
                sessionVO.setEnterType(2);// 广告进入的用户
            } else {
                sessionVO.setEnterType(1);
            }
        }
        Integer advUnlockNum = 0; // 默认不开启
        Integer advUnlockFlag = 0;
        if (sessionVO.getLinkId() != null && sessionVO.getLinkId() > 0) {
            // 广告挂载的设置enterType=5
            FastLinkPO linkPO = fastLinkService.queryInfoByRedis(sessionVO.getLinkId());
            if (linkPO != null && linkPO.getLinkType() != null && linkPO.getLinkType() == 3) {
                sessionVO.setEnterType(5); // 挂载进入的用户
            }
        }
        FastMemberSubLinkPO fastMemberSubLinkPO = fastMemberSubLinkMapper.queryById(sessionVO.getMemberId());
        if (Objects.nonNull(fastMemberSubLinkPO) && Objects.nonNull(fastMemberSubLinkPO.getSubLinkId()) && fastMemberSubLinkPO.getSubLinkId() > 0) {
            FastLinkPO linkPO = fastLinkService.queryInfoByRedis(sessionVO.getLinkId());
            if (Objects.nonNull(linkPO) && Objects.nonNull(linkPO.getAdvUnlockFlag())) {
                advUnlockNum = linkPO.getAdvUnlockNum();
                advUnlockFlag = linkPO.getAdvUnlockFlag();
            }
        }
        actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd   账户信息:" + JSON.toJSON(sessionVO));
        StopWatch stopwatch = new StopWatch();
        Map<String, Object> result = Maps.newHashMap();
        Boolean whetherShowRewardAd = Boolean.FALSE; // 是否展示广告
        Boolean registryChargeFlag = Boolean.FALSE; // 注册时间内是否满足弹出条件
        Boolean chargeFlag = Boolean.FALSE; // 充值时间内是否满足弹出条件
        Integer adSequence = 0; // 弹出顺序
        Integer perUnlockAmount = 1; // 每观看一次广告解锁几集
        // 1.查询小程序配置
        FastMiniRewardAdSettingPO fastMiniRewardAdSettingPO = new FastMiniRewardAdSettingPO();
        fastMiniRewardAdSettingPO.setMiniId(sessionVO.getMiniId().longValue());
        fastMiniRewardAdSettingPO.setEnterType(sessionVO.getEnterType());
        FastMiniRewardAdSettingPO setting = fastMiniRewardAdSettingMapper.queryOne(fastMiniRewardAdSettingPO);
        actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd   配置信息:" + JSON.toJSON(setting));
        // 用户链接广告配置先返回
        result.put("advUnlockNum", advUnlockNum);
        result.put("advUnlockFlag", advUnlockFlag);
        log.info("advUnlockNum:{},advUnlockFlag:{}", advUnlockNum, advUnlockFlag);
        if (setting == null) {
            result.put("whetherShowRewardAd", whetherShowRewardAd);
            actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd   配置不存在，耗时" + stopwatch.getTotalTimeSeconds() + "秒");
            result.put("msg", "配置信息不存在，无法解锁");
            return ResultVO.success(result);
        }
        // 2.查询该会员是否充值vip,且是否在vip周期内
        Date vipExpireTime = fastMemberOrderRechargeMapper.queryVipExpireTime(sessionVO.getMemberId());
        if (vipExpireTime != null && vipExpireTime.after(DateUtil.getNowDate())) { // 在vip周期以内
            result.put("whetherShowRewardAd", whetherShowRewardAd);
            actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    在vip充值期内，耗时" + stopwatch.getTotalTimeSeconds() + "秒");
            result.put("msg", "已充值vip，无需解锁");
            return ResultVO.success(result);
        }
        // 3.查询当日解锁次数
        FastMemberUnlockRewardLogPO rewardLogQuery = new FastMemberUnlockRewardLogPO();
        rewardLogQuery.setMemberId(sessionVO.getMemberId());
        rewardLogQuery.setCreateTimeS(DateUtil.getTodayStartTime());
        rewardLogQuery.setCreateTimeE(DateUtil.getTodayEndTime());
        int count = fastMemberUnlockRewardLogMapper.queryCount(rewardLogQuery);
        actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    用户" + sessionVO.getMemberId() + "当日已经解锁" + count + "次");
        // 4.查询最近一次充值记录
        FastMemberOrderRechargePO queryLastChargeRecords = new FastMemberOrderRechargePO();
        queryLastChargeRecords.setMiniId(sessionVO.getMiniId());// 小程序id
        queryLastChargeRecords.setMemberId(sessionVO.getMemberId().longValue());// 会员id
        queryLastChargeRecords.setState(1);// 已支付状态
        FastMemberOrderRechargePO lastCharge = fastMemberOrderRechargeMapper.queryLastRecharge(queryLastChargeRecords);
        actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    上一次充值订单信息:" + JSON.toJSON(lastCharge));
        int registryDays = DateUtil.daysBetween(sessionVO.getRegistTime(), DateUtil.getNowDate());
        actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    注册天数:" + registryDays);
        // 5.判断弹广告逻辑
        if (sessionVO.getPhoneOs() == 1) {
            // 安卓手机
            if (setting.getAndroidAllowFlag() == 0) {// 允许配置
                result.put("whetherShowRewardAd", whetherShowRewardAd);
                actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    安卓用户不允许配置，耗时" + stopwatch.getTotalTimeSeconds() + "秒");
                result.put("msg", " 安卓用户不允许配置");
                return ResultVO.success(result);
            }
            if (count >= setting.getAndroidUnlockMax()) {// 是否超过最大解锁集数
                result.put("whetherShowRewardAd", whetherShowRewardAd);
                actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    安卓用户超过最大集数，耗时" + stopwatch.getTotalTimeSeconds() + "秒");
                result.put("msg", "当日解锁次数已用完");
                return ResultVO.success(result);
            }
            if (sessionVO.getRegistTime() == null) {
                FastMemberPO memberPO = fastMemberMapper.queryById(sessionVO.getMemberId());
                sessionVO.setRegistTime(memberPO.getCreateTime());
            }
            // 注册时间内判断逻辑
            if (registryDays >= setting.getAndroidRegisterDays()) {
                registryChargeFlag = Boolean.TRUE;
                actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    安卓用户注册时间超过配置的集数，registryChargeFlag=" + registryChargeFlag +
                        ",注册的天数为：" + registryDays + "天");
            } else {
                registryChargeFlag = Boolean.FALSE;
                actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    安卓用户注册时间未超过配置的集数，registryChargeFlag=" + registryChargeFlag +
                        ",注册的天数为：" + registryDays + "天");
            }
            // 上次充值时间到现在判断逻辑
            if (lastCharge != null) {
                Integer rechargeDays = DateUtil.daysBetween(lastCharge.getCreateTime(), DateUtil.getNowDate());
                if (rechargeDays >= setting.getAndroidNotChargeDays()) {
                    chargeFlag = Boolean.TRUE;
                    actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    安卓用户充值过并且充值天数超过配置的天数，chargeFlag=" + chargeFlag +
                            ",上一次充值的天数为：" + rechargeDays + "天");
                } else {
                    chargeFlag = Boolean.FALSE;
                    actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    安卓用户充值过并且充值天数没有超过配置的天数，chargeFlag=" + chargeFlag +
                            ",上一次充值的天数为：" + rechargeDays + "天");
                }
            } else {
                actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    安卓用户未充值过，chargeFlag=" + chargeFlag);
            }
            adSequence = setting.getAndroidSequeue();
            perUnlockAmount = setting.getAndroidPerUnlockAmount();
        } else if (sessionVO.getPhoneOs() == 2) {
            // 苹果手机
            if (setting.getIosAllowFlag() == 0) {
                result.put("whetherShowRewardAd", whetherShowRewardAd);
                actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    IOS用户不允许配置，耗时" + stopwatch.getTotalTimeSeconds() + "秒");
                result.put("msg", "ios用户不允许配置");
                return ResultVO.success(result);
            }
            if (count >= setting.getIosUnlockMax()) {
                result.put("whetherShowRewardAd", whetherShowRewardAd);
                actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd     IOS用户超过最大解锁数量,耗时" + stopwatch.getTotalTimeSeconds() + "秒");
                result.put("msg", "当日解锁次数已用完");
                return ResultVO.success(result);
            }
            // 新注册账户判断充值逻辑
            if (sessionVO.getRegistTime() != null && registryDays >= setting.getIosRegisterDays()) {
                registryChargeFlag = Boolean.TRUE;
                actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    IOS用户注册时间超过配置的集数，registryChargeFlag=" + registryChargeFlag +
                        ",注册的天数为：" + registryDays + "天");
            } else {
                registryChargeFlag = Boolean.FALSE;
                actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    IOS用户注册时间未超过配置的集数，registryChargeFlag=" + registryChargeFlag +
                        ",注册的天数为：" + registryDays + "天");
            }
            // 充值过的用户判断充值逻辑
            if (lastCharge != null) {
                Integer rechargeDays = DateUtil.daysBetween(lastCharge.getCreateTime(), DateUtil.getNowDate());
                if (rechargeDays >= setting.getIosNotChargeDays()) {
                    chargeFlag = Boolean.TRUE;
                    actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    IOS用户充值过且充值时间超过配置的天数，chargeFlag=" + chargeFlag +
                            ",上一次充值的天数为：" + rechargeDays + "天");
                } else {
                    chargeFlag = Boolean.FALSE;
                    actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    IOS用户充值过且充值时间未超过配置的天数，chargeFlag=" + chargeFlag +
                            ",上一次充值的天数为：" + rechargeDays + "天");
                }
            } else {
                actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    IOS用户未充值过，chargeFlag=" + chargeFlag);
            }
            adSequence = setting.getIosSequeue();
            perUnlockAmount = setting.getIosPerUnlockAmount();
        }
        if (registryChargeFlag || chargeFlag) {
            whetherShowRewardAd = Boolean.TRUE;
        } else {
            whetherShowRewardAd = Boolean.FALSE;
        }
        // 6.返回结果
        result.put("whetherShowRewardAd", whetherShowRewardAd);
        result.put("perUnlockAmount", perUnlockAmount);
        if (whetherShowRewardAd) {// 是否弹出广告，不弹广告不返回该字段
            result.put("adSequeue", adSequence);
        }
        actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    响应结果信息：" + JSON.toJSONString(result));
        actionLogService.log("tiktok_ad_reward", "whetherShowRewardAd    耗时" + stopwatch.getTotalTimeSeconds() + "秒");
        return ResultVO.success(result);
    }

    public MethodVO insertOrUpdate(SessionVO sessionVO, List<FastMiniRewardAdSettingPO> fastMiniRewardAdSettingPOList) {
        for (FastMiniRewardAdSettingPO fastMiniRewardAdSettingPO : fastMiniRewardAdSettingPOList) {
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getMiniId())) {
                return MethodVO.error("小程序id不能为空");
            }
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getEnterType())) {
                return MethodVO.error("用户进入类型为空");
            }
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getAndroidAllowFlag())) {
                return MethodVO.error("安卓用户弹出广告设置不能为空");
            }
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getAndroidRegisterDays())) {
                return MethodVO.error("安卓用户注册未充值天数不能为空");
            }
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getAndroidNotChargeDays())) {
                return MethodVO.error("安卓用户未充值天数不能为空");
            }
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getAndroidSequeue())) {
                return MethodVO.error("安卓用户弹出顺序不能为空");
            }
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getAndroidUnlockMax())) {
                return MethodVO.error("安卓用户解锁上限集数不能为空");
            }

            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getIosAllowFlag())) {
                return MethodVO.error("IOS用户弹出广告设置不能为空");
            }
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getIosRegisterDays())) {
                return MethodVO.error("IOS用户注册未充值天数不能为空");
            }
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getIosNotChargeDays())) {
                return MethodVO.error("IOS用户未充值天数不能为空");
            }
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getIosSequeue())) {
                return MethodVO.error("IOS用户弹出顺序不能为空");
            }
            if (StrUtil.isEmpty(fastMiniRewardAdSettingPO.getIosUnlockMax())) {
                return MethodVO.error("IOS用户解锁上限集数不能为空");
            }
            FastMiniRewardAdSettingPO query = new FastMiniRewardAdSettingPO();
            query.setMiniId(fastMiniRewardAdSettingPO.getMiniId());
            query.setEnterType(fastMiniRewardAdSettingPO.getEnterType());
            FastMiniRewardAdSettingPO result = queryOne(query);
            if (result != null) {
                fastMiniRewardAdSettingPO.setId(result.getId());
                fastMiniRewardAdSettingPO.setUpdatorId(sessionVO.getUserId());
                fastMiniRewardAdSettingPO.setUpdateTime(DateUtil.getNowDate());
                update(fastMiniRewardAdSettingPO);
            } else {
                fastMiniRewardAdSettingPO.setCreatorId(sessionVO.getUserId());
                fastMiniRewardAdSettingPO.setCreateTime(DateUtil.getNowDate());
                fastMiniRewardAdSettingPO.setUpdatorId(sessionVO.getUserId());
                fastMiniRewardAdSettingPO.setUpdateTime(DateUtil.getNowDate());
                insert(fastMiniRewardAdSettingPO);
            }
        }
        return MethodVO.success();
    }


}
