package com.fast.service.make.video;

import com.fast.service.make.video.util.MultiFileDownloader;
import com.fast.service.make.video.util.MultiFileDownloader.DownloadTask;
import com.fast.utils.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多文件下载服务
 * 提供业务层的多文件下载和打包功能
 */
@Slf4j
@Service
public class MultiFileDownloadService {

    /**
     * 下载多个文件并打包成ZIP
     * 
     * @param urls URL列表
     * @param zipFileName ZIP文件名（不包含路径）
     * @param saveDir 保存目录（可为空，使用临时目录）
     * @return ZIP文件的完整路径，失败返回null
     */
    public String downloadFilesAndZip(List<String> urls, String zipFileName, String saveDir) {
        if (urls == null || urls.isEmpty()) {
            log.error("URL列表为空，无法进行下载");
            return null;
        }

        if (StrUtil.isBlank(zipFileName)) {
            zipFileName = "download_" + System.currentTimeMillis() + ".zip";
        }

        // 确定保存路径
        String zipFilePath = determineZipFilePath(zipFileName, saveDir);
        
        log.info("开始下载 {} 个文件并打包到: {}", urls.size(), zipFilePath);

        // 执行下载和打包
        return MultiFileDownloader.downloadUrlsAndZip(urls, zipFilePath);
    }

    /**
     * 下载多个文件并打包成ZIP（带自定义文件名）
     * 
     * @param urlFileNameMap URL和文件名的映射
     * @param zipFileName ZIP文件名
     * @param saveDir 保存目录
     * @return ZIP文件的完整路径，失败返回null
     */
    public String downloadFilesAndZip(Map<String, String> urlFileNameMap, String zipFileName, String saveDir) {
        if (urlFileNameMap == null || urlFileNameMap.isEmpty()) {
            log.error("URL文件名映射为空，无法进行下载");
            return null;
        }

        if (StrUtil.isBlank(zipFileName)) {
            zipFileName = "download_" + System.currentTimeMillis() + ".zip";
        }

        String zipFilePath = determineZipFilePath(zipFileName, saveDir);
        
        log.info("开始下载 {} 个文件并打包到: {}", urlFileNameMap.size(), zipFilePath);

        return MultiFileDownloader.downloadUrlsAndZip(urlFileNameMap, zipFilePath);
    }

    /**
     * 高级下载功能：支持更多配置选项
     * 
     * @param downloadTasks 下载任务列表
     * @param zipFileName ZIP文件名
     * @param saveDir 保存目录
     * @param tempDir 临时目录
     * @param maxThreads 最大并发线程数
     * @return ZIP文件的完整路径，失败返回null
     */
    public String downloadFilesAndZipAdvanced(List<DownloadTask> downloadTasks, String zipFileName, 
                                            String saveDir, String tempDir, int maxThreads) {
        if (downloadTasks == null || downloadTasks.isEmpty()) {
            log.error("下载任务列表为空，无法进行下载");
            return null;
        }

        if (StrUtil.isBlank(zipFileName)) {
            zipFileName = "download_" + System.currentTimeMillis() + ".zip";
        }

        String zipFilePath = determineZipFilePath(zipFileName, saveDir);
        
        log.info("开始高级下载 {} 个文件并打包到: {}, 最大线程数: {}", 
                downloadTasks.size(), zipFilePath, maxThreads);

        return MultiFileDownloader.downloadAndZip(downloadTasks, zipFilePath, tempDir, maxThreads);
    }

    /**
     * 批量下载视频相关文件（视频、字幕、缩略图等）
     * 
     * @param videoUrl 视频URL
     * @param subtitleUrl 字幕URL
     * @param thumbnailUrl 缩略图URL
     * @param zipFileName ZIP文件名
     * @param saveDir 保存目录
     * @return ZIP文件路径
     */
    public String downloadVideoPackage(String videoUrl, String subtitleUrl, String thumbnailUrl, 
                                     String zipFileName, String saveDir) {
        Map<String, String> urlFileMap = new HashMap<>();
        
        if (StrUtil.isNotBlank(videoUrl)) {
            urlFileMap.put(videoUrl, "video.mp4");
        }
        
        if (StrUtil.isNotBlank(subtitleUrl)) {
            urlFileMap.put(subtitleUrl, "subtitle.srt");
        }
        
        if (StrUtil.isNotBlank(thumbnailUrl)) {
            urlFileMap.put(thumbnailUrl, "thumbnail.jpg");
        }

        if (urlFileMap.isEmpty()) {
            log.error("没有有效的URL，无法下载视频包");
            return null;
        }

        return downloadFilesAndZip(urlFileMap, zipFileName, saveDir);
    }

    /**
     * 批量下载字幕文件包
     * 
     * @param subtitleUrls 字幕URL列表
     * @param languages 对应的语言列表
     * @param zipFileName ZIP文件名
     * @param saveDir 保存目录
     * @return ZIP文件路径
     */
    public String downloadSubtitlePackage(List<String> subtitleUrls, List<String> languages, 
                                        String zipFileName, String saveDir) {
        if (subtitleUrls == null || subtitleUrls.isEmpty()) {
            log.error("字幕URL列表为空");
            return null;
        }

        Map<String, String> urlFileMap = new HashMap<>();
        for (int i = 0; i < subtitleUrls.size(); i++) {
            String url = subtitleUrls.get(i);
            String language = (languages != null && i < languages.size()) ? languages.get(i) : "lang_" + (i + 1);
            String fileName = "subtitle_" + language + ".srt";
            urlFileMap.put(url, fileName);
        }

        return downloadFilesAndZip(urlFileMap, zipFileName, saveDir);
    }

    /**
     * 确定ZIP文件的完整路径
     */
    private String determineZipFilePath(String zipFileName, String saveDir) {
        if (StrUtil.isBlank(saveDir)) {
            // 使用系统临时目录
            saveDir = System.getProperty("java.io.tmpdir");
        }

        // 确保目录存在
        File dir = new File(saveDir);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (created) {
                log.info("创建保存目录: {}", saveDir);
            } else {
                log.warn("无法创建保存目录: {}", saveDir);
            }
        }

        // 构建完整路径
        return saveDir.endsWith(File.separator) ? saveDir + zipFileName : saveDir + File.separator + zipFileName;
    }

    /**
     * 检查ZIP文件是否存在
     * 
     * @param zipFilePath ZIP文件路径
     * @return 是否存在
     */
    public boolean isZipFileExists(String zipFilePath) {
        if (StrUtil.isBlank(zipFilePath)) {
            return false;
        }
        File file = new File(zipFilePath);
        return file.exists() && file.isFile();
    }

    /**
     * 获取ZIP文件大小（字节）
     * 
     * @param zipFilePath ZIP文件路径
     * @return 文件大小，文件不存在返回-1
     */
    public long getZipFileSize(String zipFilePath) {
        if (!isZipFileExists(zipFilePath)) {
            return -1;
        }
        return new File(zipFilePath).length();
    }

    /**
     * 删除ZIP文件
     * 
     * @param zipFilePath ZIP文件路径
     * @return 是否删除成功
     */
    public boolean deleteZipFile(String zipFilePath) {
        if (!isZipFileExists(zipFilePath)) {
            return false;
        }
        try {
            boolean deleted = new File(zipFilePath).delete();
            if (deleted) {
                log.info("删除ZIP文件成功: {}", zipFilePath);
            } else {
                log.warn("删除ZIP文件失败: {}", zipFilePath);
            }
            return deleted;
        } catch (Exception e) {
            log.error("删除ZIP文件异常: {}, 错误: {}", zipFilePath, e.getMessage());
            return false;
        }
    }
}
