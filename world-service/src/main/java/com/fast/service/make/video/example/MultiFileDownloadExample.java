package com.fast.service.make.video.example;

import com.fast.service.make.video.MultiFileDownloadService;
import com.fast.service.make.video.util.MultiFileDownloader;
import com.fast.service.make.video.util.MultiFileDownloader.DownloadTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 多文件下载功能使用示例
 * 展示如何在实际业务中使用多文件下载打包功能
 */
@Slf4j
@Component
public class MultiFileDownloadExample {

    @Autowired
    private MultiFileDownloadService multiFileDownloadService;

    /**
     * 示例1：下载视频制作相关文件
     * 场景：用户完成视频制作后，需要下载所有相关文件
     */
    public String downloadVideoProductionFiles(Long seriesTaskId, String destLang) {
        log.info("开始下载视频制作文件，seriesTaskId={}, destLang={}", seriesTaskId, destLang);

        try {
            // 构建文件URL映射
            Map<String, String> fileMap = new HashMap<>();
            
            // 添加视频文件
            String videoUrl = getVideoUrl(seriesTaskId, destLang);
            if (videoUrl != null) {
                fileMap.put(videoUrl, String.format("video_%s_%s.mp4", seriesTaskId, destLang));
            }

            // 添加字幕文件
            String subtitleUrl = getSubtitleUrl(seriesTaskId, destLang);
            if (subtitleUrl != null) {
                fileMap.put(subtitleUrl, String.format("subtitle_%s_%s.srt", seriesTaskId, destLang));
            }

            // 添加缩略图
            String thumbnailUrl = getThumbnailUrl(seriesTaskId);
            if (thumbnailUrl != null) {
                fileMap.put(thumbnailUrl, String.format("thumbnail_%s.jpg", seriesTaskId));
            }

            // 添加原始字幕（如果存在）
            String originalSubtitleUrl = getOriginalSubtitleUrl(seriesTaskId);
            if (originalSubtitleUrl != null) {
                fileMap.put(originalSubtitleUrl, String.format("original_subtitle_%s.srt", seriesTaskId));
            }

            if (fileMap.isEmpty()) {
                log.warn("没有找到可下载的文件，seriesTaskId={}", seriesTaskId);
                return null;
            }

            // 生成ZIP文件名
            String zipFileName = String.format("video_production_%s_%s_%s.zip", 
                seriesTaskId, destLang, System.currentTimeMillis());

            // 执行下载和打包
            String zipPath = multiFileDownloadService.downloadFilesAndZip(fileMap, zipFileName, null);

            if (zipPath != null) {
                log.info("视频制作文件下载完成，zipPath={}, fileCount={}", zipPath, fileMap.size());
            } else {
                log.error("视频制作文件下载失败，seriesTaskId={}", seriesTaskId);
            }

            return zipPath;

        } catch (Exception e) {
            log.error("下载视频制作文件异常，seriesTaskId={}", seriesTaskId, e);
            return null;
        }
    }

    /**
     * 示例2：批量下载多语言字幕文件
     * 场景：用户需要下载某个视频的所有语言字幕
     */
    public String downloadMultiLanguageSubtitles(Long seriesTaskId, List<String> languages) {
        log.info("开始下载多语言字幕，seriesTaskId={}, languages={}", seriesTaskId, languages);

        try {
            List<String> subtitleUrls = new ArrayList<>();
            List<String> languageNames = new ArrayList<>();

            for (String lang : languages) {
                String subtitleUrl = getSubtitleUrl(seriesTaskId, lang);
                if (subtitleUrl != null) {
                    subtitleUrls.add(subtitleUrl);
                    languageNames.add(lang);
                }
            }

            if (subtitleUrls.isEmpty()) {
                log.warn("没有找到字幕文件，seriesTaskId={}", seriesTaskId);
                return null;
            }

            String zipFileName = String.format("subtitles_%s_%s.zip", seriesTaskId, System.currentTimeMillis());

            String zipPath = multiFileDownloadService.downloadSubtitlePackage(
                subtitleUrls, languageNames, zipFileName, null
            );

            if (zipPath != null) {
                log.info("多语言字幕下载完成，zipPath={}, languageCount={}", zipPath, languageNames.size());
            }

            return zipPath;

        } catch (Exception e) {
            log.error("下载多语言字幕异常，seriesTaskId={}", seriesTaskId, e);
            return null;
        }
    }

    /**
     * 示例3：批量下载用户收藏的文件
     * 场景：用户收藏了多个视频，需要批量下载
     */
    public String downloadUserFavoriteFiles(Long userId, List<Long> seriesTaskIds) {
        log.info("开始下载用户收藏文件，userId={}, taskCount={}", userId, seriesTaskIds.size());

        try {
            List<DownloadTask> tasks = new ArrayList<>();

            for (Long seriesTaskId : seriesTaskIds) {
                // 添加视频文件
                String videoUrl = getVideoUrl(seriesTaskId, "en"); // 默认英语版本
                if (videoUrl != null) {
                    tasks.add(new DownloadTask(videoUrl, String.format("video_%s.mp4", seriesTaskId)));
                }

                // 添加字幕文件
                String subtitleUrl = getSubtitleUrl(seriesTaskId, "en");
                if (subtitleUrl != null) {
                    tasks.add(new DownloadTask(subtitleUrl, String.format("subtitle_%s.srt", seriesTaskId)));
                }
            }

            if (tasks.isEmpty()) {
                log.warn("没有找到可下载的收藏文件，userId={}", userId);
                return null;
            }

            String zipFileName = String.format("user_favorites_%s_%s.zip", userId, System.currentTimeMillis());

            // 使用高级下载功能，设置更多并发线程
            String zipPath = multiFileDownloadService.downloadFilesAndZipAdvanced(
                tasks, zipFileName, null, null, 8
            );

            if (zipPath != null) {
                log.info("用户收藏文件下载完成，zipPath={}, fileCount={}", zipPath, tasks.size());
            }

            return zipPath;

        } catch (Exception e) {
            log.error("下载用户收藏文件异常，userId={}", userId, e);
            return null;
        }
    }

    /**
     * 示例4：定时清理过期的ZIP文件
     * 场景：定期清理临时生成的ZIP文件，释放磁盘空间
     */
    public void cleanupExpiredZipFiles(String directory, long maxAgeMillis) {
        log.info("开始清理过期ZIP文件，directory={}, maxAge={}ms", directory, maxAgeMillis);

        try {
            java.io.File dir = new java.io.File(directory);
            if (!dir.exists() || !dir.isDirectory()) {
                log.warn("目录不存在或不是目录：{}", directory);
                return;
            }

            java.io.File[] files = dir.listFiles((file, name) -> name.toLowerCase().endsWith(".zip"));
            if (files == null) {
                return;
            }

            long currentTime = System.currentTimeMillis();
            int deletedCount = 0;

            for (java.io.File file : files) {
                long fileAge = currentTime - file.lastModified();
                if (fileAge > maxAgeMillis) {
                    boolean deleted = multiFileDownloadService.deleteZipFile(file.getAbsolutePath());
                    if (deleted) {
                        deletedCount++;
                        log.debug("删除过期ZIP文件：{}", file.getName());
                    }
                }
            }

            log.info("清理过期ZIP文件完成，删除文件数：{}", deletedCount);

        } catch (Exception e) {
            log.error("清理过期ZIP文件异常", e);
        }
    }

    /**
     * 示例5：异步下载大量文件
     * 场景：需要下载大量文件，使用异步方式避免阻塞
     */
    public void downloadFilesAsync(List<String> urls, String zipFileName, 
                                 java.util.function.Consumer<String> callback) {
        log.info("开始异步下载文件，fileCount={}", urls.size());

        // 使用线程池异步执行
        java.util.concurrent.CompletableFuture.supplyAsync(() -> {
            return multiFileDownloadService.downloadFilesAndZip(urls, zipFileName, null);
        }).thenAccept(result -> {
            if (result != null) {
                log.info("异步下载完成，zipPath={}", result);
                callback.accept(result);
            } else {
                log.error("异步下载失败");
                callback.accept(null);
            }
        }).exceptionally(throwable -> {
            log.error("异步下载异常", throwable);
            callback.accept(null);
            return null;
        });
    }

    // 模拟获取各种URL的方法（实际项目中应该从数据库或其他服务获取）
    private String getVideoUrl(Long seriesTaskId, String destLang) {
        // 实际实现中应该从数据库查询
        return "https://example.com/video/" + seriesTaskId + "_" + destLang + ".mp4";
    }

    private String getSubtitleUrl(Long seriesTaskId, String destLang) {
        // 实际实现中应该从数据库查询
        return "https://example.com/subtitle/" + seriesTaskId + "_" + destLang + ".srt";
    }

    private String getThumbnailUrl(Long seriesTaskId) {
        // 实际实现中应该从数据库查询
        return "https://example.com/thumbnail/" + seriesTaskId + ".jpg";
    }

    private String getOriginalSubtitleUrl(Long seriesTaskId) {
        // 实际实现中应该从数据库查询
        return "https://example.com/original_subtitle/" + seriesTaskId + ".srt";
    }

    /**
     * 测试所有示例功能
     */
    public void runExamples() {
        log.info("开始运行多文件下载示例");

        // 示例1：下载视频制作文件
        String videoZip = downloadVideoProductionFiles(12345L, "zh");
        log.info("示例1结果：{}", videoZip);

        // 示例2：下载多语言字幕
        List<String> languages = Arrays.asList("en", "zh", "ja");
        String subtitleZip = downloadMultiLanguageSubtitles(12345L, languages);
        log.info("示例2结果：{}", subtitleZip);

        // 示例3：下载用户收藏
        List<Long> favoriteIds = Arrays.asList(12345L, 12346L, 12347L);
        String favoriteZip = downloadUserFavoriteFiles(1001L, favoriteIds);
        log.info("示例3结果：{}", favoriteZip);

        // 示例4：清理过期文件（清理1小时前的文件）
        cleanupExpiredZipFiles(System.getProperty("java.io.tmpdir"), 3600000L);

        // 示例5：异步下载
        List<String> asyncUrls = Arrays.asList(
            "https://example.com/file1.txt",
            "https://example.com/file2.txt"
        );
        downloadFilesAsync(asyncUrls, "async_download.zip", result -> {
            log.info("异步下载回调结果：{}", result);
        });

        log.info("所有示例执行完成");
    }
}
