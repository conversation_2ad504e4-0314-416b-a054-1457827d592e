package com.fast.service.make.video.util;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * SRT字幕文件校验工具
 * 用于校验第三方翻译API返回的SRT文件是否完整
 */
@Slf4j
public class SrtValidator {

    /**
     * 尝试不同编码读取文件
     *
     * @param filePath 文件路径
     * @return 文件内容
     * @throws IOException 文件读取异常
     */
    private static String readFileWithEncoding(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        try {
            // 首先尝试UTF-8编码
            return Files.readString(path);
        } catch (Exception e) {
            try {
                // 如果UTF-8失败，尝试GBK编码
                return new String(Files.readAllBytes(path), "GBK");
            } catch (Exception ex) {
                // 如果都失败，使用系统默认编码
                return new String(Files.readAllBytes(path));
            }
        }
    }

    /**
     * 统计SRT内容字符串中的字幕条目数量
     *
     * @param srtContent SRT内容字符串
     * @return 字幕条目数量
     */
    public static int countSrtEntriesFromContent(String srtContent) {
        if (srtContent == null || srtContent.trim().isEmpty()) {
            return 0;
        }

        // 按空行分割内容为单独的字幕条目
        String[] entries = srtContent.trim().split("\\n\\s*\\n");

        // 统计有效的字幕条目（包含时间戳的条目）
        int validEntries = 0;
        for (String entry : entries) {
            String[] lines = entry.trim().split("\\n");
            if (lines.length >= 2) {
                // 检查是否包含时间戳
                boolean hasTimestamp = false;
                for (String line : lines) {
                    if (line.contains("-->")) {
                        hasTimestamp = true;
                        break;
                    }
                }
                if (hasTimestamp) {
                    validEntries++;
                }
            }
        }

        return validEntries;
    }

    /**
     * 统计SRT文件中的字幕条目数量
     *
     * @param filePath SRT文件路径
     * @return 字幕条目数量，如果文件不存在或读取失败返回-1
     */
    public static int countSrtEntries(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            log.error("文件 {} 不存在", filePath);
            return -1;
        }

        try {
            String content = readFileWithEncoding(filePath);
            return countSrtEntriesFromContent(content);
        } catch (Exception e) {
            System.out.println("读取文件 " + filePath + " 时发生错误: " + e.getMessage());
            return -1;
        }
    }

    /**
     * 获取SRT文件的详细信息
     *
     * @param filePath SRT文件路径
     * @return 文件详细信息Map
     */
    public static Map<String, Object> getDetailedSrtInfo(String filePath) {
        Map<String, Object> info = new HashMap<>();

        File file = new File(filePath);
        if (!file.exists()) {
            info.put("error", "文件 " + filePath + " 不存在");
            return info;
        }

        try {
            String content = readFileWithEncoding(filePath);
            String[] entries = content.trim().split("\\n\\s*\\n");

            int validEntries = 0;
            int invalidEntries = 0;

            for (String entry : entries) {
                String[] lines = entry.trim().split("\\n");
                if (lines.length >= 2) {
                    boolean hasTimestamp = false;
                    for (String line : lines) {
                        if (line.contains("-->")) {
                            hasTimestamp = true;
                            break;
                        }
                    }
                    if (hasTimestamp) {
                        validEntries++;
                    } else {
                        invalidEntries++;
                    }
                }
            }

            info.put("filePath", filePath);
            info.put("totalBlocks", entries.length);
            info.put("validEntries", validEntries);
            info.put("invalidEntries", invalidEntries);
            info.put("fileSize", file.length());

        } catch (Exception e) {
            info.put("error", "分析文件时发生错误: " + e.getMessage());
        }

        return info;
    }

    /**
     * 校验翻译后的SRT文件是否完整
     *
     * @param originalFile   原始SRT文件路径
     * @param translatedFile 翻译后的SRT文件路径
     * @param tolerance      允许的条目数量差异容忍度，默认为0（必须完全一致）
     * @return 校验结果Map
     */
    public static Map<String, Object> validateSrt(String originalFile, String translatedFile, int tolerance) {
        Map<String, Object> result = new HashMap<>();
        result.put("isValid", false);
        result.put("originalCount", 0);
        result.put("translatedCount", 0);
        result.put("difference", 0);
        result.put("message", "");

        // 统计原始文件条目数
        int originalCount = countSrtEntries(originalFile);
        if (originalCount == -1) {
            result.put("message", "无法读取原始文件: " + originalFile);
            return result;
        }

        // 统计翻译文件条目数
        int translatedCount = countSrtEntries(translatedFile);
        if (translatedCount == -1) {
            result.put("message", "无法读取翻译文件: " + translatedFile);
            return result;
        }

        // 更新结果
        result.put("originalCount", originalCount);
        result.put("translatedCount", translatedCount);
        int difference = Math.abs(originalCount - translatedCount);
        result.put("difference", difference);

        // 判断是否在容忍范围内
        if (difference <= tolerance) {
            result.put("isValid", true);
            if (difference == 0) {
                result.put("message", String.format("校验通过！原始文件和翻译文件都有 %d 条字幕", originalCount));
            } else {
                result.put("message", String.format("校验通过（在容忍范围内）！原始文件: %d 条，翻译文件: %d 条，差异: %d 条",
                        originalCount, translatedCount, difference));
            }
        } else {
            result.put("isValid", false);
            if (translatedCount < originalCount) {
                result.put("message", String.format("校验失败！翻译文件缺失字幕条目。原始文件: %d 条，翻译文件: %d 条，缺失: %d 条",
                        originalCount, translatedCount, difference));
            } else {
                result.put("message", String.format("校验失败！翻译文件条目过多。原始文件: %d 条，翻译文件: %d 条，多出: %d 条",
                        originalCount, translatedCount, difference));
            }
        }

        return result;
    }

    /**
     * 校验翻译后的SRT文件是否完整（默认严格模式）
     *
     * @param originalFile   原始SRT文件路径
     * @param translatedFile 翻译后的SRT文件路径
     * @return 校验结果Map
     */
    public static Map<String, Object> validateSrt(String originalFile, String translatedFile) {
        return validateSrt(originalFile, translatedFile, 0);
    }

    /**
     * 校验翻译后的SRT内容是否完整（文件 vs 字符串）
     *
     * @param originalFile      原始SRT文件路径
     * @param translatedContent 翻译后的SRT内容字符串
     * @param tolerance         允许的条目数量差异容忍度，默认为0（必须完全一致）
     * @return 校验结果Map
     */
    public static Map<String, Object> validateSrtWithContent(String originalFile, String translatedContent, int tolerance) {
        Map<String, Object> result = new HashMap<>();
        result.put("isValid", false);
        result.put("originalCount", 0);
        result.put("translatedCount", 0);
        result.put("difference", 0);
        result.put("message", "");

        // 统计原始文件条目数
        int originalCount = countSrtEntries(originalFile);
        if (originalCount == -1) {
            result.put("message", "无法读取原始文件: " + originalFile);
            return result;
        }

        // 统计翻译内容条目数
        int translatedCount = countSrtEntriesFromContent(translatedContent);

        // 更新结果
        result.put("originalCount", originalCount);
        result.put("translatedCount", translatedCount);
        int difference = Math.abs(originalCount - translatedCount);
        result.put("difference", difference);

        // 判断是否在容忍范围内
        if (difference <= tolerance) {
            result.put("isValid", true);
            if (difference == 0) {
                result.put("message", String.format("校验通过！原始文件和翻译内容都有 %d 条字幕", originalCount));
            } else {
                result.put("message", String.format("校验通过（在容忍范围内）！原始文件: %d 条，翻译内容: %d 条，差异: %d 条",
                        originalCount, translatedCount, difference));
            }
        } else {
            result.put("isValid", false);
            if (translatedCount < originalCount) {
                result.put("message", String.format("校验失败！翻译内容缺失字幕条目。原始文件: %d 条，翻译内容: %d 条，缺失: %d 条",
                        originalCount, translatedCount, difference));
            } else {
                result.put("message", String.format("校验失败！翻译内容条目过多。原始文件: %d 条，翻译内容: %d 条，多出: %d 条",
                        originalCount, translatedCount, difference));
            }
        }

        return result;
    }

    /**
     * 校验翻译后的SRT内容是否完整（文件 vs 字符串，默认严格模式）
     *
     * @param originalFile      原始SRT文件路径
     * @param translatedContent 翻译后的SRT内容字符串
     * @return 校验结果Map
     */
    public static Map<String, Object> validateSrtWithContent(String originalFile, String translatedContent) {
        return validateSrtWithContent(originalFile, translatedContent, 0);
    }

    /**
     * 校验两个SRT内容字符串是否完整
     *
     * @param originalContent   原始SRT内容字符串
     * @param translatedContent 翻译后的SRT内容字符串
     * @param tolerance         允许的条目数量差异容忍度，默认为0（必须完全一致）
     * @return 校验结果Map
     */
    public static Map<String, Object> validateSrtContentComparison(String originalContent, String translatedContent, int tolerance) {
        Map<String, Object> result = new HashMap<>();
        result.put("isValid", false);
        result.put("originalCount", 0);
        result.put("translatedCount", 0);
        result.put("difference", 0);
        result.put("message", "");

        // 统计原始内容条目数
        int originalCount = countSrtEntriesFromContent(originalContent);

        // 统计翻译内容条目数
        int translatedCount = countSrtEntriesFromContent(translatedContent);

        // 更新结果
        result.put("originalCount", originalCount);
        result.put("translatedCount", translatedCount);
        int difference = Math.abs(originalCount - translatedCount);
        result.put("difference", difference);

        // 判断是否在容忍范围内
        if (difference <= tolerance) {
            result.put("isValid", true);
            if (difference == 0) {
                result.put("message", String.format("校验通过！原始内容和翻译内容都有 %d 条字幕", originalCount));
            } else {
                result.put("message", String.format("校验通过（在容忍范围内）！原始内容: %d 条，翻译内容: %d 条，差异: %d 条",
                        originalCount, translatedCount, difference));
            }
        } else {
            result.put("isValid", false);
            if (translatedCount < originalCount) {
                result.put("message", String.format("校验失败！翻译内容缺失字幕条目。原始内容: %d 条，翻译内容: %d 条，缺失: %d 条",
                        originalCount, translatedCount, difference));
            } else {
                result.put("message", String.format("校验失败！翻译内容条目过多。原始内容: %d 条，翻译内容: %d 条，多出: %d 条",
                        originalCount, translatedCount, difference));
            }
        }

        return result;
    }

    /**
     * 校验两个SRT内容字符串是否完整（默认严格模式）
     *
     * @param originalContent   原始SRT内容字符串
     * @param translatedContent 翻译后的SRT内容字符串
     * @return 校验结果Map
     */
    public static Map<String, Object> validateSrtContentComparison(String originalContent, String translatedContent) {
        return validateSrtContentComparison(originalContent, translatedContent, 0);
    }

    /**
     * 校验翻译后的SRT文件是否完整（严格模式）
     *
     * @param originalFile   原始SRT文件路径
     * @param translatedFile 翻译后的SRT文件路径
     * @return true/false
     */
    public static boolean validateSrtBol(String originalFile, String translatedFile) {
        Map<String, Object> map = validateSrt(originalFile, translatedFile, 1);
        return (boolean) map.get("isValid");
    }

    /**
     * 校验翻译后的SRT内容是否完整（简化版本，返回boolean）
     *
     * @param originalFile      原始SRT文件路径
     * @param translatedContent 翻译后的SRT内容字符串
     * @return true/false
     */
    public static boolean validateSrtWithContentBol(String originalFile, String translatedContent) {
        Map<String, Object> map = validateSrtWithContent(originalFile, translatedContent, 0);
        return (boolean) map.get("isValid");
    }

    /**
     * 校验翻译后的SRT内容是否完整（简化版本，返回boolean，带容忍度）
     *
     * @param originalFile      原始SRT文件路径
     * @param translatedContent 翻译后的SRT内容字符串
     * @param tolerance         允许的条目数量差异容忍度
     * @return true/false
     */
    public static boolean validateSrtWithContentBol(String originalFile, String translatedContent, int tolerance) {
        Map<String, Object> map = validateSrtWithContent(originalFile, translatedContent, tolerance);
        return (boolean) map.get("isValid");
    }

    /**
     * 校验两个SRT内容字符串是否完整（简化版本，返回boolean）
     *
     * @param originalContent   原始SRT内容字符串
     * @param translatedContent 翻译后的SRT内容字符串
     * @return true/false
     */
    public static boolean validateSrtContentBol(String originalContent, String translatedContent) {
        Map<String, Object> map = validateSrtContentComparison(originalContent, translatedContent, 0);
        return (boolean) map.get("isValid");
    }

    /**
     * 主方法 - 测试和示例
     */
    public static void main(String[] args) {
        System.out.println("=== SRT文件校验工具测试 ===\n");

        // 测试文件路径
        String originalFile = "C:\\Users\\<USER>\\Desktop\\test\\zh.srt";
        String completeFile = "C:\\Users\\<USER>\\Desktop\\test\\en.srt";
        String incompleteFile = "C:\\Users\\<USER>\\Desktop\\test\\en_err.srt";

        // 模拟第三方API返回的SRT内容字符串
        String apiReturnedSrt = "1\n00:00:01,990 --> 00:00:03,270\nHello everyone\n\n2\n00:00:03,270 --> 00:00:04,640\nI've been training\n\n";
        String incompleteSrt = "1\n00:00:01,990 --> 00:00:03,270\nHello everyone\n\n";

        // 测试1: 完整文件校验
        System.out.println("测试1: 完整文件校验");
        System.out.println("-".repeat(40));
        Map<String, Object> result1 = validateSrt(originalFile, completeFile);
        System.out.println("原始文件: " + originalFile);
        System.out.println("翻译文件: " + completeFile);
        System.out.println("校验结果: " + ((Boolean) result1.get("isValid") ? "✅ 通过" : "❌ 失败"));
        System.out.println("详细信息: " + result1.get("message") + "\n");

        // 测试2: 文件 vs 字符串校验（完整）
        System.out.println("测试2: 文件 vs 字符串校验（完整）");
        System.out.println("-".repeat(40));
        Map<String, Object> result2 = validateSrtWithContent(originalFile, apiReturnedSrt);
        System.out.println("原始文件: " + originalFile);
        System.out.println("翻译内容: API返回的SRT字符串");
        System.out.println("校验结果: " + ((Boolean) result2.get("isValid") ? "✅ 通过" : "❌ 失败"));
        System.out.println("详细信息: " + result2.get("message") + "\n");

        // 测试3: 文件 vs 字符串校验（不完整）
        System.out.println("测试3: 文件 vs 字符串校验（不完整）");
        System.out.println("-".repeat(40));
        Map<String, Object> result3 = validateSrtWithContent(originalFile, incompleteSrt);
        System.out.println("原始文件: " + originalFile);
        System.out.println("翻译内容: 不完整的SRT字符串");
        System.out.println("校验结果: " + ((Boolean) result3.get("isValid") ? "✅ 通过" : "❌ 失败"));
        System.out.println("详细信息: " + result3.get("message") + "\n");

        // 测试4: 字符串 vs 字符串校验
        System.out.println("测试4: 字符串 vs 字符串校验");
        System.out.println("-".repeat(40));
        Map<String, Object> result4 = validateSrtContentComparison(apiReturnedSrt, incompleteSrt);
        System.out.println("原始内容: 完整的SRT字符串（2条）");
        System.out.println("翻译内容: 不完整的SRT字符串（1条）");
        System.out.println("校验结果: " + ((Boolean) result4.get("isValid") ? "✅ 通过" : "❌ 失败"));
        System.out.println("详细信息: " + result4.get("message") + "\n");
    }
}
