/*
 * Powered By fast.up
 */
package com.fast.service.make.video;

import com.fast.constant.StaticStr;
import com.fast.enums.video.VideoProcessStatusEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.make.video.MakeVideoBatchTaskMapper;
import com.fast.po.make.video.MakeVideoBatchTaskPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MakeVideoBatchTaskService extends BaseService {

    @Autowired
    private MakeVideoBatchTaskMapper makeVideoBatchTaskMapper;

    @Autowired
    private MakeSubtitleExtractTaskServiceV2 makeSubtitleExtractTaskServiceV2;

    @Autowired
    private MakeSubtitleRemoveTaskServiceV2 makeSubtitleRemoveTaskServiceV2;

    @Autowired
    private MakeSubtitleTranslateTaskService makeSubtitleTranslateTaskService;

    @Autowired
    private MakeVideoCompressTaskServiceV2 makeVideoCompressTaskServiceV2;

    /**
     * 通过id查询单个对象
     */
    public MakeVideoBatchTaskPO queryById(MakeVideoBatchTaskPO params) {
        return makeVideoBatchTaskMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeVideoBatchTaskPO queryById(Integer id) {
        return makeVideoBatchTaskMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeVideoBatchTaskPO queryOne(MakeVideoBatchTaskPO params) {
        return makeVideoBatchTaskMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeVideoBatchTaskPO> queryList(MakeVideoBatchTaskPO params) {
        return makeVideoBatchTaskMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeVideoBatchTaskPO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeVideoBatchTaskPO> list = makeVideoBatchTaskMapper.queryList(params);
        for (MakeVideoBatchTaskPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(MakeVideoBatchTaskPO params) {
        Integer count = makeVideoBatchTaskMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    public List<MakeVideoBatchTaskPO> queryByStatus(VideoProcessStatusEnum statusEnum) {
        if (statusEnum == null) {
            return null;
        }
        MakeVideoBatchTaskPO query = new MakeVideoBatchTaskPO();
        query.setStatus(statusEnum.getCode());
        return queryList(query);
    }

    public MakeVideoBatchTaskPO queryByFilmId(Integer filmId) {
        if (filmId == null) {
            return null;
        }
        MakeVideoBatchTaskPO query = new MakeVideoBatchTaskPO();
        query.setFilmId(filmId);
        return queryOne(query);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeVideoBatchTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeVideoBatchTaskMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeVideoBatchTaskPO> list) {
        if (makeVideoBatchTaskMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeVideoBatchTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeVideoBatchTaskMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeVideoBatchTaskPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeVideoBatchTaskPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        makeVideoBatchTaskMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public int updateStatus(Integer id, Integer status) {
        if (id == null || status == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoBatchTaskPO taskPO = new MakeVideoBatchTaskPO();
        taskPO.setId(id);
        taskPO.setStatus(status);
        return makeVideoBatchTaskMapper.updateById(taskPO);
    }

    public int updateStatus(Integer id, VideoProcessStatusEnum statusEnum) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        return updateStatus(id, statusEnum.getCode());
    }

    public int updateStatusAndStartTime(Integer id, Integer status, Date startTime) {
        if (id == null || status == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoBatchTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        MakeVideoBatchTaskPO batchTaskUpdate = new MakeVideoBatchTaskPO();
        batchTaskUpdate.setId(id);
        batchTaskUpdate.setStatus(status);
        batchTaskUpdate.setStartTime(startTime);
        return makeVideoBatchTaskMapper.updateById(batchTaskUpdate);
    }

    public int updateStatusAndStartTime(Integer id, VideoProcessStatusEnum statusEnum, Date startTime) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        return updateStatusAndStartTime(id, statusEnum.getCode(), startTime);
    }

    public int updateStatusAndEndTime(Integer id, Integer status, Date endTime) {
        if (id == null || status == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoBatchTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        MakeVideoBatchTaskPO batchTaskUpdate = new MakeVideoBatchTaskPO();
        batchTaskUpdate.setId(id);
        batchTaskUpdate.setStatus(status);
        batchTaskUpdate.setEndTime(endTime);
        batchTaskUpdate.setTakeTime(DateUtil.getDiffTime(taskPO.getStartTime(), endTime));
        return makeVideoBatchTaskMapper.updateById(batchTaskUpdate);
    }

    public int updateStatusAndEndTime(Integer id, VideoProcessStatusEnum statusEnum, Date endTime) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        return updateStatusAndEndTime(id, statusEnum.getCode(), endTime);
    }

    public int syncCompletedCountAndEndTime(Integer id, Date endTime) {
        if (id == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoBatchTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        MakeVideoBatchTaskPO batchTaskUpdate = new MakeVideoBatchTaskPO();
        batchTaskUpdate.setId(id);
        batchTaskUpdate.setCompletedCount(calculateCompletedCount(id));
        batchTaskUpdate.setEndTime(endTime);
        batchTaskUpdate.setTakeTime(DateUtil.getDiffTime(taskPO.getStartTime(), endTime));
        return makeVideoBatchTaskMapper.updateById(batchTaskUpdate);
    }

    public int syncFailedCount(Integer id) {
        if (id == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoBatchTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        MakeVideoBatchTaskPO batchTaskUpdate = new MakeVideoBatchTaskPO();
        batchTaskUpdate.setId(id);
        batchTaskUpdate.setFailedCount(calculateFailedCount(id));
        return makeVideoBatchTaskMapper.updateById(batchTaskUpdate);
    }

    public int addCompletedCountAndEndTime(Integer id, Date endTime) {
        if (id == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoBatchTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        if (taskPO.getCompletedCount() >= taskPO.getTotalCount()) {
            return updateTime(id, endTime);
        }
        taskPO.setEndTime(endTime);
        taskPO.setTakeTime(DateUtil.getDiffTime(taskPO.getStartTime(), endTime));
        return makeVideoBatchTaskMapper.addCompletedCountAndTime(taskPO);
    }

    public int updateTime(Integer id, Date endTime) {
        if (id == null || endTime == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoBatchTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        taskPO.setEndTime(endTime);
        taskPO.setTakeTime(DateUtil.getDiffTime(taskPO.getStartTime(), endTime));
        return makeVideoBatchTaskMapper.updateById(taskPO);
    }

    public int addFailedCount(Integer id) {
        if (id == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoBatchTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        if (taskPO.getFailedCount() >= taskPO.getTotalCount()) {
            return 0;
        }
        return makeVideoBatchTaskMapper.addFailedCount(id);
    }

    public int calculateCompletedCount(Integer id) {
        MakeVideoBatchTaskPO batchTask = queryById(id);
        if (batchTask == null) {
            return 0;
        }
        // 提取
        int extractSuccess = makeSubtitleExtractTaskServiceV2.countByBatchTaskId(batchTask.getId(), VideoProcessStatusEnum.COMPLETED);
        // 擦除
        int removeSuccess = makeSubtitleRemoveTaskServiceV2.countByBatchTaskId(batchTask.getId(), VideoProcessStatusEnum.COMPLETED);
        // 翻译
        int translateSuccess = makeSubtitleTranslateTaskService.countByBatchTaskId(batchTask.getId(), VideoProcessStatusEnum.COMPLETED);
        // 压制
        int compressSuccess = makeVideoCompressTaskServiceV2.countByBatchTaskId(batchTask.getId(), VideoProcessStatusEnum.COMPLETED);
        // 不能超过总任务数量
        int completedCount = extractSuccess + removeSuccess + translateSuccess + compressSuccess;
        if (completedCount > batchTask.getTotalCount()) {
            completedCount = batchTask.getTotalCount();
        }
        return completedCount;
    }

    public int calculateFailedCount(Integer id) {
        MakeVideoBatchTaskPO batchTask = queryById(id);
        if (batchTask == null) {
            return 0;
        }
        // 提取
        int extractFail = makeSubtitleExtractTaskServiceV2.countByBatchTaskId(batchTask.getId(), VideoProcessStatusEnum.FAILED);
        // 擦除
        int removeFail = makeSubtitleRemoveTaskServiceV2.countByBatchTaskId(batchTask.getId(), VideoProcessStatusEnum.FAILED);
        // 翻译
        int translateFail = makeSubtitleTranslateTaskService.countByBatchTaskId(batchTask.getId(), VideoProcessStatusEnum.FAILED);
        // 压制
        int compressFail = makeVideoCompressTaskServiceV2.countByBatchTaskId(batchTask.getId(), VideoProcessStatusEnum.FAILED);

        return extractFail + removeFail + translateFail + compressFail;
    }

}
