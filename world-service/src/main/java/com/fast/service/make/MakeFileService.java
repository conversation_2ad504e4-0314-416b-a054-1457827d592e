/*
 * Powered By fast.up
 */
package com.fast.service.make;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticStr;
import com.fast.framework.manager.VThreadManager;
import com.fast.mapper.make.MakeFileMapper;
import com.fast.po.make.MakeFilePO;
import com.fast.service.base.BaseService;
import com.fast.service.make.video.util.ThirdPartyApiRateLimiter;
import com.fast.service.make.video.util.VolcengineApiUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MakeFileService extends BaseService {

    @Autowired
    private MakeFileMapper makeFileMapper;

    @Autowired
    private VolcengineApiUtil volcengineApiUtil;

    @Autowired
    private VThreadManager vThreadManager;

    /**
     * 通过id查询单个对象
     */
    public MakeFilePO queryById(MakeFilePO params) {
        return makeFileMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeFilePO queryById(Integer id) {
        return makeFileMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeFilePO queryOne(MakeFilePO params) {
        return makeFileMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeFilePO> queryList(MakeFilePO params) {
        return makeFileMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeFilePO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeFilePO> list = makeFileMapper.queryList(params);
        for (MakeFilePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(MakeFilePO params) {
        return makeFileMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeFilePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeFileMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        // 同步火山媒资文件
        vThreadManager.executeTaskWithMDC(() -> {
            // 获取许可证
            ThirdPartyApiRateLimiter.acquireUploadMediaByUrl();
            log.info("Qps许可证获取成功，开始同步火山媒资文件，fileId={}, url={}", params.getId(), params.getUrl());
            
            // 拿不到会阻塞，虚拟线程阻塞挂起不会占用载体线程资源
            JSONObject result = volcengineApiUtil.uploadMediaByUrl(params.getId() + "_" + params.getBatchId(), params.getName(), params.getUrl());
            if (result != null) {
                MakeFilePO makeFilePO = new MakeFilePO();
                makeFilePO.setId(params.getId());
                makeFilePO.setVolStartTime(DateUtil.getNowDate());
                update(makeFilePO);
            }
        });

        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeFilePO> list) {
        if (makeFileMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeFilePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeFileMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeFilePO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeFilePO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        makeFileMapper.deleteById(po.getId());
        return MethodVO.success();
    }

}
