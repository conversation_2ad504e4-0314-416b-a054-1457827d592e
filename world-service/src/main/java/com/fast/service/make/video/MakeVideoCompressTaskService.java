// /*
//  * Powered By fast.up
//  */
// package com.fast.service.make.video;
//
// import com.alibaba.fastjson.JSONArray;
// import com.alibaba.fastjson.JSONObject;
// import com.fast.constant.StaticStr;
// import com.fast.enums.video.VideoProcessStatusEnum;
// import com.fast.framework.config.VideoProcessConfig;
// import com.fast.framework.exception.MyException;
// import com.fast.mapper.make.video.MakeVideoCompressTaskMapper;
// import com.fast.po.make.video.*;
// import com.fast.service.base.BaseService;
// import com.fast.service.make.video.util.GhostCutApiUtil;
// import com.fast.utils.CollUtil;
// import com.fast.utils.ComUtil;
// import com.fast.utils.DateUtil;
// import com.fast.utils.StrUtil;
// import com.fast.vo.MethodVO;
// import com.fast.vo.PageVO;
// import com.fast.vo.ResultVO;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;
//
// import java.util.Date;
// import java.util.List;
// import java.util.Objects;
//
// /**
//  * <AUTHOR>
//  */
// @Service
// @Slf4j
// public class MakeVideoCompressTaskService extends BaseService {
//
//     @Autowired
//     private MakeVideoCompressTaskMapper makeVideoCompressTaskMapper;
//
//     @Autowired
//     private GhostCutApiUtil ghostCutApiUtil;
//
//     @Autowired
//     private VideoProcessConfig videoProcessConfig;
//
//     @Autowired
//     private MakeSubtitleExtractTaskServiceV2 makeSubtitleExtractTaskServiceV2;
//
//     @Autowired
//     private MakeSubtitleTranslateTaskService makeSubtitleTranslateTaskService;
//
//     @Autowired
//     private MakeSubtitleRemoveTaskServiceV2 makeSubtitleRemoveTaskServiceV2;
//
//     /**
//      * 通过id查询单个对象
//      */
//     public MakeVideoCompressTaskPO queryById(MakeVideoCompressTaskPO params) {
//         return makeVideoCompressTaskMapper.queryById(params);
//     }
//
//     /**
//      * 通过id查询单个对象
//      */
//     public MakeVideoCompressTaskPO queryById(Integer id) {
//         return makeVideoCompressTaskMapper.queryById(id);
//     }
//
//     /**
//      * 通过条件查询单个对象
//      */
//     public MakeVideoCompressTaskPO queryOne(MakeVideoCompressTaskPO params) {
//         return makeVideoCompressTaskMapper.queryOne(params);
//     }
//
//     /**
//      * 查询全部
//      */
//     public List<MakeVideoCompressTaskPO> queryList(MakeVideoCompressTaskPO params) {
//         return makeVideoCompressTaskMapper.queryList(params);
//     }
//
//     /**
//      * 查询全部(分页)
//      */
//     public ResultVO<?> queryPageList(MakeVideoCompressTaskPO params, PageVO pageVO) {
//         startPage(pageVO);
//         List<MakeVideoCompressTaskPO> list = makeVideoCompressTaskMapper.queryList(params);
//         for (MakeVideoCompressTaskPO cur : list) {
//             cur.setEncryptionId(encode(cur.getId()));
//         }
//         return ResultVO.success(getPageListData(list, pageVO));
//     }
//
//     /**
//      * 查询总数
//      */
//     public Integer queryCount(MakeVideoCompressTaskPO params) {
//         Integer count = makeVideoCompressTaskMapper.queryCount(params);
//         return count == null ? 0 : count;
//     }
//
//     public MakeVideoCompressTaskPO queryByThirdPartyId(String batchId, String taskId) {
//         if (StrUtil.isBlank(batchId) || StrUtil.isBlank(taskId)) {
//             return null;
//         }
//         MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
//         query.setThirdPartyBatchId(batchId);
//         query.setThirdPartyTaskId(taskId);
//         return queryOne(query);
//     }
//
//     public List<MakeVideoCompressTaskPO> queryBySeriesTaskId(Integer seriesTaskId) {
//         if (seriesTaskId == null) {
//             return null;
//         }
//         MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
//         query.setSeriesTaskId(seriesTaskId);
//         return queryList(query);
//     }
//
//     public boolean isAllCompleted(Integer seriesTaskId) {
//         if (seriesTaskId == null) {
//             return false;
//         }
//         List<MakeVideoCompressTaskPO> list = queryBySeriesTaskId(seriesTaskId);
//         if (CollUtil.isEmpty(list)) {
//             return false;
//         }
//         return list.stream().allMatch(item -> Objects.equals(item.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode()));
//     }
//
//     public MakeVideoCompressTaskPO queryBySeriesTaskIdAndLang(Integer seriesTaskId, String destLang) {
//         if (seriesTaskId == null || StrUtil.isBlank(destLang)) {
//             return null;
//         }
//         MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
//         query.setSeriesTaskId(seriesTaskId);
//         query.setLanguageCode(destLang);
//         return queryOne(query);
//     }
//
//     public List<MakeVideoCompressTaskPO> queryByFilmIdAndSeriesNum(Integer filmId, Integer seriesNum) {
//         if (filmId == null || seriesNum == null) {
//             return null;
//         }
//         MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
//         query.setFilmId(filmId);
//         query.setSeriesNum(seriesNum);
//         return queryList(query);
//     }
//
//     public MakeVideoCompressTaskPO queryByFilmIdAndSeriesNumAndLang(Integer filmId, Integer seriesNum, String destLang) {
//         if (filmId == null || seriesNum == null || StrUtil.isBlank(destLang)) {
//             return null;
//         }
//         MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
//         query.setFilmId(filmId);
//         query.setSeriesNum(seriesNum);
//         query.setLanguageCode(destLang);
//         return queryOne(query);
//     }
//
//     /**
//      * 新增
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO insert(MakeVideoCompressTaskPO params) {
//         Date nowTime = DateUtil.getNowDate();
//         params.setCreateTime(nowTime);
//         params.setUpdateTime(nowTime);
//         if (makeVideoCompressTaskMapper.insertSelective(params) == 0) {
//             transactionRollBack();
//             return MethodVO.error(StaticStr.ADD_FAILED);
//         }
//         return MethodVO.success();
//     }
//
//     /**
//      * 批量新增
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO insertBatch(List<MakeVideoCompressTaskPO> list) {
//         if (makeVideoCompressTaskMapper.insertBatch(list) > 0) {
//             return MethodVO.success();
//         } else {
//             return MethodVO.error(StaticStr.ADD_FAILED);
//         }
//     }
//
//     /**
//      * 更新
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO update(MakeVideoCompressTaskPO params) {
//         Date nowTime = DateUtil.getNowDate();
//         params.setUpdateTime(nowTime);
//         if (makeVideoCompressTaskMapper.updateById(params) == 0) {
//             transactionRollBack();
//             return MethodVO.error(StaticStr.UPDATE_FAILED);
//         }
//         return MethodVO.success();
//     }
//
//     /**
//      * 删除
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO delete(MakeVideoCompressTaskPO params) {
//         if (params.getId() == null) {
//             return MethodVO.error(StaticStr.DELETE_FAILED);
//         }
//         MakeVideoCompressTaskPO po = queryById(params.getId());
//         if (po == null) {
//             return MethodVO.error("data not exist");
//         }
//         makeVideoCompressTaskMapper.deleteById(po.getId());
//         return MethodVO.success();
//     }
//
//     public int updateStatus(Integer id, Integer status) {
//         if (id == null || status == null) {
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//         MakeVideoCompressTaskPO taskPO = new MakeVideoCompressTaskPO();
//         taskPO.setId(id);
//         taskPO.setStatus(status);
//         return makeVideoCompressTaskMapper.updateById(taskPO);
//     }
//
//     public int updateStatus(Integer id, VideoProcessStatusEnum statusEnum) {
//         if (id == null || statusEnum == null) {
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//         return updateStatus(id, statusEnum.getCode());
//     }
//
//     /**
//      * 执行视频压制业务逻辑
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public void executeCompress(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {
//         log.info("开始视频压制: batchTaskId={}", batchTask.getId());
//
//         // 获取字幕提取任务
//         MakeSubtitleExtractTaskPO extractTask = makeSubtitleExtractTaskServiceV2.queryBySeriesTaskId(seriesTask.getId());
//         if (extractTask == null || !Objects.equals(extractTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
//             log.warn("剧集任务 {} 的字幕提取任务未完成，跳过压制", seriesTask.getId());
//             return;
//         }
//
//         // 获取字幕擦除任务
//         MakeSubtitleRemoveTaskPO removeTask = makeSubtitleRemoveTaskServiceV2.queryBySeriesTaskId(seriesTask.getId());
//         if (removeTask == null || !Objects.equals(removeTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
//             log.warn("剧集任务 {} 的字幕擦除任务未完成，跳过压制", seriesTask.getId());
//             return;
//         }
//
//         String destLangs = batchTask.getDestLangs();
//
//         // 取擦除区域的y坐标来显示字幕位置，定值会导致字幕便宜的比较厉害，坐标格式：[[0.0,0.74],[1.0,0.74],[1.0,0.8833],[0.0,0.8833]]
//         Double[][] regions = ComUtil.transferRegion(batchTask.getRegion());
//         Double position = null;
//         if (regions != null) {
//             position = regions[0][1];
//         }
//
//         // 为每种目标语言创建压制任务
//         for (String lang : destLangs.split(",")) {
//             MakeSubtitleTranslateTaskPO translateTask = makeSubtitleTranslateTaskService.queryBySeriesTaskIdAndLang(seriesTask.getId(), lang);
//             if (translateTask == null || !Objects.equals(translateTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
//                 log.warn("剧集任务 {} 的 {} 翻译任务未完成，跳过压制", seriesTask.getId(), lang);
//                 continue;
//             }
//
//             JSONObject response = ghostCutApiUtil.videoCompress(
//                     List.of(removeTask.getUrl()),
//                     List.of("[" + batchTask.getFilmId() + "]视频压制_" + batchTask.getId() + "_" + seriesTask.getId() + "_" + seriesTask.getSeriesNum() + "_" + lang),
//                     extractTask.getUrl(),
//                     translateTask.getUrl(),
//                     videoProcessConfig.buildVideoCompressCallback(batchTask.getId()),
//                     null,
//                     position,
//                     batchTask.getRemoveBgAudio());
//             log.info("鬼手视频压制api请求结果: {}", response.toJSONString());
//
//             Integer code = response.getInteger("code");
//             if (code == null || code != 1000) {
//                 throw new MyException("视频压制API调用失败: " + response.getString("msg"));
//             }
//
//             JSONObject body = response.getJSONObject("body");
//             String idProject = body.getString("idProject");
//             JSONArray dataList = body.getJSONArray("dataList");
//
//             for (int i = 0; i < dataList.size(); i++) {
//                 JSONObject item = dataList.getJSONObject(i);
//
//                 MakeVideoCompressTaskPO compressTask = new MakeVideoCompressTaskPO();
//                 compressTask.setSeriesTaskId(seriesTask.getId());
//                 compressTask.setFilmId(batchTask.getFilmId());
//                 compressTask.setSeriesNum(seriesTask.getSeriesNum());
//                 compressTask.setThirdPartyBatchId(idProject);
//                 compressTask.setThirdPartyTaskId(item.getString("id"));
//                 compressTask.setCleanVideoUrl(removeTask.getUrl());
//                 compressTask.setSubtitleUrl(extractTask.getUrl());
//                 compressTask.setLanguageCode(lang);
//                 compressTask.setStatus(VideoProcessStatusEnum.PROCESSING.getCode());
//                 compressTask.setStartTime(DateUtil.getNowDate());
//                 insert(compressTask);
//                 log.info("剧集任务 {} 新增视频压制任务 {} 状态为 处理中", seriesTask.getId(), compressTask.getId());
//             }
//         }
//     }
//
// }
