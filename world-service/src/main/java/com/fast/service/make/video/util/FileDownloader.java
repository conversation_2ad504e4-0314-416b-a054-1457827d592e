package com.fast.service.make.video.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

/**
 * 文件下载器
 */
@Slf4j
public class FileDownloader {

    private static final int BUFFER_SIZE = 8192;

    public static String downloadFile(String fileUrl, String fileName, String savePath) {
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        FileOutputStream outputStream = null;
        try {
            long start = System.currentTimeMillis();

            // 创建URL连接
            URL url = new URL(fileUrl);

            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();

            // 获取文件信息
            int fileSize = connection.getContentLength();
            String contentType = connection.getContentType();
            if (fileName == null) {
                fileName = getCleanFileName(url);
            }

            // 如果没有指定保存路径，使用当前目录
            if (savePath == null || savePath.trim().isEmpty()) {
                savePath = fileName;
            } else if (new File(savePath).isDirectory()) {
                savePath = savePath + File.separator + fileName;
            }

            // 打印文件信息
            log.info("下载文件: {}", fileUrl);
            log.info("文件类型: {}", contentType);
            log.info("文件大小: {}", String.format("%.2f MB", fileSize / (1024.0 * 1024.0)));

            // 确保目标文件夹存在
            File targetFile = new File(savePath);
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (created) {
                    log.info("创建目标文件夹: {}", parentDir.getAbsolutePath());
                } else {
                    log.warn("无法创建目标文件夹: {}", parentDir.getAbsolutePath());
                }
            }

            // 开始下载
            inputStream = new BufferedInputStream(connection.getInputStream());
            outputStream = new FileOutputStream(savePath);

            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            // long totalBytesRead = 0;
            // long lastProgressUpdate = System.currentTimeMillis();

            // 读取并写入文件，同时显示进度
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                // totalBytesRead += bytesRead;

                // 每100ms更新一次进度，避免刷新太频繁
                // long currentTime = System.currentTimeMillis();
                // if (currentTime - lastProgressUpdate > 100) {
                //     updateProgress(totalBytesRead, fileSize);
                //     lastProgressUpdate = currentTime;
                // }
            }

            // 最后更新一次进度到100%
            // updateProgress(totalBytesRead, fileSize);

            long end = System.currentTimeMillis();
            log.info("文件下载成功，已保存至: {}, 耗时: {}s", new File(savePath).getAbsolutePath(), (end - start) / 1000f);

            return savePath;

        } catch (Exception e) {
            log.error("文件下载失败: {}", ExceptionUtils.getStackTrace(e));
            return null;

        } finally {
            // 关闭所有流
            try {
                if (outputStream != null) outputStream.close();
                if (inputStream != null) inputStream.close();
                if (connection != null) connection.disconnect();
            } catch (IOException e) {
                log.error("关闭IO失败: {}", ExceptionUtils.getStackTrace(e));
            }
        }
    }

    public static String downloadFile(String fileUrl, String savePath) {
        return downloadFile(fileUrl, null, savePath);
    }

    private static String getCleanFileName(URL url) {
        // 获取URL的路径部分
        String path = url.getPath();
        String fileName = path.substring(path.lastIndexOf('/') + 1);

        // URL解码
        fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8);

        // 移除查询参数
        if (fileName.contains("?")) {
            fileName = fileName.substring(0, fileName.indexOf("?"));
        }

        // 清理非法字符
        fileName = Pattern.compile("[\\\\/:*?\"<>|]").matcher(fileName).replaceAll("_");

        // 如果文件名为空，使用默认名称
        if (fileName.isEmpty()) {
            fileName = "downloaded_file";
        }

        return fileName;
    }

    private static void updateProgress(long current, long total) {
        int progressBarWidth = 50;
        double percentage = (double) current / total;
        int progressChars = (int) (progressBarWidth * percentage);

        StringBuilder progressBar = new StringBuilder("[");
        for (int i = 0; i < progressBarWidth; i++) {
            if (i < progressChars) progressBar.append("=");
            else if (i == progressChars) progressBar.append(">");
            else progressBar.append(" ");
        }
        progressBar.append("]");

        String format = String.format("%s %.2f%% (%.2f/%.2f MB)",
                progressBar,
                percentage * 100,
                current / (1024.0 * 1024.0),
                total / (1024.0 * 1024.0));

        log.info("下载进度: {}", format);
    }

    public static void main(String[] args) {
        // 测试下载
        String fileUrl = "https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/caption/SC/1754368236507838770.srt";
        downloadFile(fileUrl, null);
    }
}
