package com.fast.service.make.video;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticStr;
import com.fast.enums.CaptionTypeEnum;
import com.fast.enums.FilmTypeEnum;
import com.fast.enums.LanguageEnum;
import com.fast.enums.video.VideoProcessStatusEnum;
import com.fast.enums.video.VolcengineProcessStatusEnum;
import com.fast.framework.exception.MyException;
import com.fast.po.make.MakeCompletedFilmPO;
import com.fast.po.make.MakeFilmCaptionPO;
import com.fast.po.make.MakeFilmVideoPO;
import com.fast.po.make.MakeOriginalFilmPO;
import com.fast.po.make.video.*;
import com.fast.service.make.MakeFilmCaptionService;
import com.fast.service.make.MakeFilmVideoService;
import com.fast.service.make.MakeOriginalFilmService;
import com.fast.service.make.video.util.FileDownloader;
import com.fast.service.make.video.util.SrtProcessor;
import com.fast.service.make.video.util.VolcSubtitleConverter;
import com.fast.service.make.video.util.VolcengineAuthUtil;
import com.fast.service.oss.OssService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.file.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 视频处理服务
 */
@Service
@Slf4j
public class VideoProcessServiceV2 {

    @Autowired
    private MakeOriginalFilmService makeOriginalFilmService;

    @Autowired
    private MakeVideoBatchTaskService makeVideoBatchTaskService;

    @Autowired
    private MakeVideoSeriesTaskService makeVideoSeriesTaskService;

    @Autowired
    private MakeSubtitleExtractTaskServiceV2 makeSubtitleExtractTaskServiceV2;

    @Autowired
    private MakeSubtitleRemoveTaskServiceV2 makeSubtitleRemoveTaskServiceV2;

    @Autowired
    private MakeSubtitleTranslateTaskService makeSubtitleTranslateTaskService;

    @Autowired
    private MakeVideoCompressTaskServiceV2 makeVideoCompressTaskServiceV2;

    @Autowired
    private MakeFilmVideoService makeFilmVideoService;

    @Autowired
    private MakeFilmCaptionService makeFilmCaptionService;

    @Autowired
    private VideoProcessFlowManager videoProcessFlowManager;

    @Autowired
    private OssService ossService;

    @Autowired
    private VolcengineAuthUtil volcengineAuthUtil;

    /**
     * 临时文件存储路径
     * 注意：实际部署时请修改为合适的路径
     */
    private static final String tmpPath = "/home/<USER>/";

    /**
     * 创建视频处理任务
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer createTask(MakeCompletedFilmPO film) {
        MakeOriginalFilmPO originalFilm = makeOriginalFilmService.queryById(film.getOriginalFilmId());
        if (originalFilm == null) {
            throw new MyException("原片不存在");
        }

        List<MakeFilmVideoPO> filmVideos = makeFilmVideoService.queryByOriginalFilmId(originalFilm.getId());
        if (CollUtil.isEmpty(filmVideos)) {
            throw new MyException("原片缺失视频数据");
        }

        String regionJson = JSON.toJSONString(film.getRegion());

        String destLangs = film.getDestLangs();
        String[] destLangArray = destLangs.split(",");

        // 新增批次任务
        MakeVideoBatchTaskPO batchTask = addMakeVideoBatchTask(film, destLangs, regionJson);

        // 每一集都需要新增任务
        for (MakeFilmVideoPO filmVideo : filmVideos) {
            // 新增剧集子任务
            MakeVideoSeriesTaskPO seriesTask = addMakeVideoSeriesTask(batchTask, film, filmVideo);

            // 提取字幕任务
            addMakeSubtitleExtractTask(batchTask, seriesTask, filmVideo, film.getOriginalFilmId());

            // 擦除字幕任务
            addMakeSubtitleRemoveTask(batchTask, seriesTask);

            // 翻译字幕任务
            // 英语
            if (!StrUtil.equals(batchTask.getSourceLang(), LanguageEnum.ENGLISH.getCode())) {
                // 如果源语言是英语，则不需要翻译英语字幕
                addMakeSubtitleTranslateTask(batchTask, seriesTask, LanguageEnum.ENGLISH.getCode());
            }
            // 目标语言
            for (String destLang : destLangArray) {
                if (StrUtil.equals(destLang, LanguageEnum.ENGLISH.getCode())) {
                    continue;
                }
                addMakeSubtitleTranslateTask(batchTask, seriesTask, destLang);
            }

            // 压制视频任务
            // 目标语言
            for (String destLang : destLangArray) {
                addMakeVideoCompressTask(batchTask, seriesTask, destLang);
            }

        }

        return batchTask.getId();
    }

    private MakeVideoBatchTaskPO addMakeVideoBatchTask(MakeCompletedFilmPO film, String destLangs, String regionJson) {

        // 中文 -> 英文、日文、韩文
        // 中文 -> 日文、韩文
        // 英文 -> 日文、韩文

        String[] destLangArray = destLangs.split(",");

        // 提取、擦除
        int extractCount = film.getTotal();
        int removeCount = film.getTotal();
        // 翻译、压制
        int translateCount, compressCount;

        // 英文比较特殊，所有语言的转换都是基于英文，所以就算是目标语言没选择英文，也会默认有一个英文任务
        if (Arrays.asList(destLangArray).contains(LanguageEnum.ENGLISH.getCode())) {
            translateCount = destLangArray.length * film.getTotal();
            compressCount = destLangArray.length * film.getTotal();
        } else {
            translateCount = (destLangArray.length + 1) * film.getTotal();
            compressCount = (destLangArray.length + 1) * film.getTotal();
        }
        int totalCount = extractCount + removeCount + translateCount + compressCount;

        MakeVideoBatchTaskPO batchTask = new MakeVideoBatchTaskPO();
        batchTask.setFilmId(film.getId());
        batchTask.setFilmName(film.getName());
        batchTask.setTotalCount(totalCount);
        batchTask.setSourceLang(film.getSourceLang());
        batchTask.setDestLangs(destLangs);
        batchTask.setRemoveBgAudio(film.getRemoveBgAudio());
        batchTask.setRegion(regionJson);
        makeVideoBatchTaskService.insert(batchTask);
        log.info("创建批次任务成功: batchTaskId={}, filmId={}, filmName={}, totalCount={}, extractCount={}, removeCount={}, translateCount={}, compressCount={}",
                batchTask.getId(), film.getId(), film.getName(), totalCount, extractCount, removeCount, translateCount, compressCount);

        return batchTask;
    }

    private MakeVideoSeriesTaskPO addMakeVideoSeriesTask(MakeVideoBatchTaskPO batchTask, MakeCompletedFilmPO film, MakeFilmVideoPO filmVideo) {
        MakeVideoSeriesTaskPO seriesTask = new MakeVideoSeriesTaskPO();
        seriesTask.setBatchId(batchTask.getId());
        seriesTask.setFilmId(film.getId());
        seriesTask.setSeriesNum(filmVideo.getSeriesNum());
        seriesTask.setOriginalVideoId(filmVideo.getFid());
        seriesTask.setOriginalVideoUrl(filmVideo.getUrl());
        makeVideoSeriesTaskService.insert(seriesTask);
        log.info("[{} -> {}]创建剧集子任务成功: seriesTaskId={}, batchTaskId={}", batchTask.getFilmId(), seriesTask.getSeriesNum(), seriesTask.getId(), batchTask.getId());
        return seriesTask;
    }

    private MakeSubtitleExtractTaskPO addMakeSubtitleExtractTask(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, MakeFilmVideoPO filmVideo, Integer originalFilmId) {
        MakeSubtitleExtractTaskPO extractTask = new MakeSubtitleExtractTaskPO();
        extractTask.setBatchId(batchTask.getId());
        extractTask.setSeriesTaskId(seriesTask.getId());
        extractTask.setFilmId(batchTask.getFilmId());
        extractTask.setSeriesNum(seriesTask.getSeriesNum());
        extractTask.setVideoUrl(filmVideo.getUrl());
        extractTask.setLanguageCode(batchTask.getSourceLang());
        // 查询原片是否有字幕，如果有的话直接用，就不用再去跑提取任务了
        // 优先查询自上传
        MakeFilmCaptionPO ourCaption = makeFilmCaptionService.queryByFilmIdAndTypeAndSeriesNumAndLang(originalFilmId, FilmTypeEnum.ORIGINAL.getCode(), seriesTask.getSeriesNum(), batchTask.getSourceLang(), CaptionTypeEnum.OUR.getCode());
        if (ourCaption != null) {
            extractTask.setType(CaptionTypeEnum.OUR.getCode());
            extractTask.setUrl(ourCaption.getUrl());
            extractTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        }
        // 其次coze
        MakeFilmCaptionPO cozeCaption = makeFilmCaptionService.queryByFilmIdAndTypeAndSeriesNumAndLang(originalFilmId, FilmTypeEnum.ORIGINAL.getCode(), seriesTask.getSeriesNum(), batchTask.getSourceLang(), CaptionTypeEnum.COZE.getCode());
        if (cozeCaption != null) {
            extractTask.setType(CaptionTypeEnum.COZE.getCode());
            extractTask.setUrl(cozeCaption.getUrl());
            extractTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        }
        // 再次ocr
        MakeFilmCaptionPO ocrCaption = makeFilmCaptionService.queryByFilmIdAndTypeAndSeriesNumAndLang(originalFilmId, FilmTypeEnum.ORIGINAL.getCode(), seriesTask.getSeriesNum(), batchTask.getSourceLang(), CaptionTypeEnum.OCR.getCode());
        if (ocrCaption != null) {
            extractTask.setType(CaptionTypeEnum.OCR.getCode());
            extractTask.setUrl(ocrCaption.getUrl());
            extractTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        }
        // 最后asr
        MakeFilmCaptionPO asrCaption = makeFilmCaptionService.queryByFilmIdAndTypeAndSeriesNumAndLang(originalFilmId, FilmTypeEnum.ORIGINAL.getCode(), seriesTask.getSeriesNum(), batchTask.getSourceLang(), CaptionTypeEnum.ASR.getCode());
        if (asrCaption != null) {
            extractTask.setType(CaptionTypeEnum.ASR.getCode());
            extractTask.setUrl(asrCaption.getUrl());
            extractTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        }
        makeSubtitleExtractTaskServiceV2.insert(extractTask);
        log.info("[{} -> {}]创建字幕提取任务成功: extractTaskId={}, seriesTaskId={}, batchTaskId={}", batchTask.getFilmId(), seriesTask.getSeriesNum(), extractTask.getId(), seriesTask.getId(), batchTask.getId());
        return extractTask;
    }

    private MakeSubtitleRemoveTaskPO addMakeSubtitleRemoveTask(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {
        MakeSubtitleRemoveTaskPO removeTask = new MakeSubtitleRemoveTaskPO();
        removeTask.setBatchId(batchTask.getId());
        removeTask.setSeriesTaskId(seriesTask.getId());
        removeTask.setFilmId(batchTask.getFilmId());
        removeTask.setSeriesNum(seriesTask.getSeriesNum());
        removeTask.setVideoUrl(seriesTask.getOriginalVideoUrl());
        // 查询原片是否有视频，如果有的话直接用，就不用再去跑擦除任务了
        MakeFilmVideoPO filmVideo = makeFilmVideoService.queryByFilmIdAndTypeAndSerNum(batchTask.getFilmId(), FilmTypeEnum.ORIGINAL.getCode(), seriesTask.getSeriesNum());
        if (filmVideo != null && !StrUtil.isBlank(filmVideo.getCleanUrl())) {
            removeTask.setUrl(filmVideo.getCleanUrl());
            removeTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        }
        makeSubtitleRemoveTaskServiceV2.insert(removeTask);
        log.info("[{} -> {}]创建字幕擦除任务成功: removeTaskId={}, seriesTaskId={}, batchTaskId={}", batchTask.getFilmId(), seriesTask.getSeriesNum(), removeTask.getId(), seriesTask.getId(), batchTask.getId());
        return removeTask;
    }

    private MakeSubtitleTranslateTaskPO addMakeSubtitleTranslateTask(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, String lang) {
        MakeSubtitleTranslateTaskPO translateTask = new MakeSubtitleTranslateTaskPO();
        translateTask.setBatchId(batchTask.getId());
        translateTask.setSeriesTaskId(seriesTask.getId());
        translateTask.setFilmId(batchTask.getFilmId());
        translateTask.setSeriesNum(seriesTask.getSeriesNum());
        translateTask.setLanguageCode(lang);
        makeSubtitleTranslateTaskService.insert(translateTask);
        log.info("[{} -> {}]创建字幕翻译任务成功: lang={}, translateTaskId={}, seriesTaskId={}, batchTaskId={}", batchTask.getFilmId(), seriesTask.getSeriesNum(), translateTask.getLanguageCode(), translateTask.getId(), seriesTask.getId(), batchTask.getId());
        return translateTask;
    }

    private MakeVideoCompressTaskPO addMakeVideoCompressTask(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, String lang) {
        MakeVideoCompressTaskPO compressTask = new MakeVideoCompressTaskPO();
        compressTask.setBatchId(batchTask.getId());
        compressTask.setSeriesTaskId(seriesTask.getId());
        compressTask.setFilmId(batchTask.getFilmId());
        compressTask.setSeriesNum(seriesTask.getSeriesNum());
        compressTask.setLanguageCode(lang);
        makeVideoCompressTaskServiceV2.insert(compressTask);
        log.info("[{} -> {}]创建视频压制任务成功: lang={}, compressTaskId={}, seriesTaskId={}, batchTaskId={}", batchTask.getFilmId(), seriesTask.getSeriesNum(), compressTask.getLanguageCode(), compressTask.getId(), seriesTask.getId(), batchTask.getId());
        return compressTask;
    }

    /**
     * 启动批量任务
     */
    public void startTask(Integer taskId) {
        MakeVideoBatchTaskPO batchTask = makeVideoBatchTaskService.queryById(taskId);
        if (batchTask == null) {
            throw new MyException("未查询到批量任务，可能创建失败！！！");
        }

        List<MakeVideoSeriesTaskPO> seriesTasks = makeVideoSeriesTaskService.queryByBatchId(batchTask.getId());
        if (CollUtil.isEmpty(seriesTasks)) {
            throw new MyException("未查询到剧集子任务，可能创建失败！！！");
        }

        seriesTasks.forEach(seriesTask -> {
            // 启动异步流程
            videoProcessFlowManager.startProcess(batchTask, seriesTask);
            log.info("[{} -> {}]视频处理用到的所有任务初始化完成，启动视频处理流程，批量任务ID: {}, 剧集子任务ID: {}", batchTask.getFilmId(), seriesTask.getSeriesNum(), batchTask.getId(), seriesTask.getId());

            // 更新剧集子任务状态为处理中
            makeVideoSeriesTaskService.updateStatusAndStartTime(seriesTask.getId(), VideoProcessStatusEnum.PROCESSING, DateUtil.getNowDate());
            log.info("[{} -> {}]视频处理流程启动完毕，更新剧集子任务 {} 的状态为 处理中, batchTaskId={}", batchTask.getFilmId(), seriesTask.getSeriesNum(), seriesTask.getId(), batchTask.getId());
        });
    }

    /**
     * 执行字幕提取 - 回调
     */
    public void subtitleExtractNotify(JSONObject request, String type) {
        String idProject = "volcengine";
        String runId = request.getString("RunId");

        JSONObject output = request.getJSONObject("Output");
        JSONObject task = output.getJSONObject("Task");

        // 只会有asr和ocr两种，不会再有其他
        JSONArray json;
        if (StrUtil.equals(type, "Asr")) {
            JSONObject asr = task.getJSONObject("Asr");
            // Double duration = asr.getDouble("Duration");
            json = asr.getJSONArray("Utterances");
        } else {
            JSONObject ocr = task.getJSONObject("Ocr");
            // Double duration = ocr.getDouble("Duration");
            json = ocr.getJSONArray("Texts");
        }
        List<MakeSubtitleExtractTaskPO> extractTasks = makeSubtitleExtractTaskServiceV2.queryByThirdPartyId(idProject, runId);
        if (CollUtil.isEmpty(extractTasks)) {
            log.error("未找到三方id对应的字幕提取任务: thirdPartyBatchId={}, thirdPartyTaskId={}", idProject, runId);
            throw new MyException(StaticStr.INVALID_PARAM);
        }

        // 火山做了幂等，同一个vid的相同任务会直接返回历史数据，会导致之类查询出来多条
        for (MakeSubtitleExtractTaskPO extractTask : extractTasks) {

            MakeVideoSeriesTaskPO seriesTask = makeVideoSeriesTaskService.queryById(extractTask.getSeriesTaskId());
            if (seriesTask == null) {
                log.error("未找到对应的剧集子任务: seriesTaskId={}", extractTask.getSeriesTaskId());
                throw new MyException(StaticStr.INVALID_PARAM);
            }

            MakeVideoBatchTaskPO batchTask = makeVideoBatchTaskService.queryById(seriesTask.getBatchId());
            if (batchTask == null) {
                log.error("未找到对应的批量任务: batchTaskId={}", seriesTask.getBatchId());
                throw new MyException(StaticStr.INVALID_PARAM);
            }

            if (Objects.equals(extractTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
                log.warn("字幕提取任务 {} 的状态已经是 已完成，本次回调不予处理，batchTaskId={}, seriesTaskId={}", extractTask.getId(), batchTask.getId(), seriesTask.getId());
                continue;
            }

            // Success/Failed
            String processStatus = request.getString("Status");
            extractTask.setThirdPartyStatus(processStatus);
            // extractTask.setCallbackData(request.toJSONString());

            // 过程中不会有回调，理论上只有成功和失败
            if (!StrUtil.equals(processStatus, VolcengineProcessStatusEnum.SUCCESS.getCode())) {
                log.warn("字幕提取任务 {} 回调状态异常: {}，说明火山任务处理失败", extractTask.getId(), processStatus);
                makeSubtitleExtractTaskServiceV2.extractFail(batchTask, seriesTask, extractTask, request.getString("Code"), DateUtil.getNowDate());
                return;
            }

            // 生成文件名
            String fileName = String.format("Subtitle_%d_%d_%s.srt", seriesTask.getId(), seriesTask.getSeriesNum(), batchTask.getSourceLang());
            String filePath = tmpPath + "srt/" + fileName;

            // 短期内文件不会删除，这里也可以作为一个提效的手段
            if (!FileUtil.checkFileExists(filePath)) {
                // 文件存在就不用再去转存
                boolean saved = VolcSubtitleConverter.convertToSrtFromJson(json.toJSONString(), filePath);
                if (!saved) {
                    log.error("字幕提取任务 {} 转换SRT文件失败，无法继续后续处理，batchTaskId={}, seriesTaskId={}", extractTask.getId(), batchTask.getId(), seriesTask.getId());
                    makeSubtitleExtractTaskServiceV2.extractFail(batchTask, seriesTask, extractTask, "转换SRT文件失败", DateUtil.getNowDate());
                    return;
                }
            }
            // 有可能转存失败，如：磁盘满了
            if (!FileUtil.checkFileExists(filePath)) {
                log.error("字幕提取任务 {} 转换后的SRT文件不存在，可能写入磁盘失败，路径: {}", extractTask.getId(), filePath);
                makeSubtitleExtractTaskServiceV2.extractFail(batchTask, seriesTask, extractTask, "提取后的srt文件不存在，可能写入磁盘失败", DateUtil.getNowDate());
                return;
            }
            // 先走一遍修复，存到本地
            String fixedFilePath = tmpPath + "srt/fixed/" + fileName;
            boolean success = SrtProcessor.fixSrt(filePath, fixedFilePath);

            // 上传文件到oss
            String ossUrl = ossService.uploadFile(false, 0, 5, success ? new File(fixedFilePath) : new File(filePath), batchTask.getSourceLang() + "/");
            if (StrUtil.isBlank(ossUrl)) {
                log.error("字幕提取任务 {} 上传到OSS失败，文件路径: {}", extractTask.getId(), filePath);
                makeSubtitleExtractTaskServiceV2.extractFail(batchTask, seriesTask, extractTask, "OSS上传失败", DateUtil.getNowDate());
                return;
            }
            log.info("字幕提取任务 {} 处理完毕，上传到OSS成功，本地磁盘路径: {}, ossUrl: {}", extractTask.getId(), filePath, ossUrl);

            extractTask.setType(CaptionTypeEnum.ofEnCode(type).getCode());
            makeSubtitleExtractTaskServiceV2.extractSuccess(batchTask, seriesTask, extractTask, ossUrl);
        }
    }

    /**
     * 执行字幕擦除 - 回调
     */
    public void subtitleRemoveNotify(JSONObject request) {
        String idProject = "volcengine";
        String runId = request.getString("RunId");

        JSONObject output = request.getJSONObject("Output");
        JSONObject task = output.getJSONObject("Task");

        JSONObject erase = task.getJSONObject("Erase");
        JSONObject file = erase.getJSONObject("File");
        String vodFileName = file.getString("FileName");

        List<MakeSubtitleRemoveTaskPO> removeTasks = makeSubtitleRemoveTaskServiceV2.queryByThirdPartyId(idProject, runId);
        if (CollUtil.isEmpty(removeTasks)) {
            log.error("未找到三方id对应的字幕擦除任务: thirdPartyBatchId={}, thirdPartyTaskId={}", idProject, runId);
            throw new MyException(StaticStr.INVALID_PARAM);
        }

        // 火山做了幂等，同一个vid的相同任务会直接返回历史数据，会导致之类查询出来多条
        for (MakeSubtitleRemoveTaskPO removeTask : removeTasks) {

            MakeVideoSeriesTaskPO seriesTask = makeVideoSeriesTaskService.queryById(removeTask.getSeriesTaskId());
            if (seriesTask == null) {
                log.error("未找到对应的剧集子任务: seriesTaskId={}", removeTask.getSeriesTaskId());
                throw new MyException(StaticStr.INVALID_PARAM);
            }

            MakeVideoBatchTaskPO batchTask = makeVideoBatchTaskService.queryById(seriesTask.getBatchId());
            if (batchTask == null) {
                log.error("未找到对应的批量任务: batchTaskId={}", seriesTask.getBatchId());
                throw new MyException(StaticStr.INVALID_PARAM);
            }

            if (Objects.equals(removeTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
                log.warn("字幕擦除任务 {} 的状态已经是 已完成，本次回调不予处理，batchTaskId={}, seriesTaskId={}", removeTask.getId(), batchTask.getId(), seriesTask.getId());
                return;
            }

            // Success/Failed
            String processStatus = request.getString("Status");
            removeTask.setThirdPartyStatus(processStatus);
            // removeTask.setCallbackData(request.toJSONString());

            // 过程中不会有回调，理论上只有成功和失败
            if (!StrUtil.equals(processStatus, VolcengineProcessStatusEnum.SUCCESS.getCode())) {
                log.warn("字幕擦除任务 {} 回调状态异常: {}，说明火山任务处理失败", removeTask.getId(), processStatus);
                makeSubtitleRemoveTaskServiceV2.removeFail(batchTask, seriesTask, removeTask, request.getString("Code"), DateUtil.getNowDate());
                return;
            }

            // 生成文件名
            String fileName = String.format("Video_%d_%d_%s.mp4", seriesTask.getId(), seriesTask.getSeriesNum(), "00");
            String filePath = tmpPath + "video/" + fileName;

            // 短期内文件不会删除，这里也可以作为一个提效的手段
            if (!FileUtil.checkFileExists(filePath)) {
                // 文件存在就不用再去下载
                // 火山签名
                String authUrl = volcengineAuthUtil.getAuthUrl(vodFileName);
                // 下载签名文件
                String path = FileDownloader.downloadFile(authUrl, fileName, tmpPath + "video");
                log.info("字幕擦除后的视频文件下载到本地完成，准备上传到OSS，文件路径: {}，batchTaskId={}, seriesTaskId={}", path, batchTask.getId(), seriesTask.getId());
                if (StrUtil.isBlank(path)) {
                    log.error("下载文件失败: fileUrl={}", authUrl);
                    makeSubtitleRemoveTaskServiceV2.removeFail(batchTask, seriesTask, removeTask, "下载文件失败", DateUtil.getNowDate());
                    return;
                }
            }
            // 有可能转存失败，如：磁盘满了
            if (!FileUtil.checkFileExists(filePath)) {
                log.error("下载的字幕擦除视频文件不存在，可能写入磁盘失败，路径: {}", filePath);
                makeSubtitleRemoveTaskServiceV2.removeFail(batchTask, seriesTask, removeTask, "下载的文件不存在，可能写入磁盘失败", DateUtil.getNowDate());
                return;
            }

            // 上传文件到oss
            String ossUrl = ossService.uploadFile(false, 0, 3, new File(filePath), "00/");
            if (StrUtil.isBlank(ossUrl)) {
                log.error("字幕擦除任务 {} 上传到OSS失败，文件路径: {}", removeTask.getId(), filePath);
                makeSubtitleRemoveTaskServiceV2.removeFail(batchTask, seriesTask, removeTask, "OSS上传失败", DateUtil.getNowDate());
                return;
            }
            log.info("字幕擦除任务 {} 处理完毕，上传到OSS成功，本地磁盘路径: {}, ossUrl: {}", removeTask.getId(), filePath, ossUrl);

            makeSubtitleRemoveTaskServiceV2.removeSuccess(batchTask, seriesTask, removeTask, ossUrl);
        }
    }

    /**
     * 补齐任务数据，包括数量和状态
     */
    public void fillForTask(Integer batchTaskId) {
        // 处理中
        List<MakeVideoBatchTaskPO> batchTaskList;
        if (batchTaskId != null) {
            MakeVideoBatchTaskPO batchTask = makeVideoBatchTaskService.queryById(batchTaskId);
            if (batchTask == null) {
                log.error("批量任务不存在，任务不予执行: batchTaskId={}", batchTaskId);
                return;
            }
            batchTaskList = List.of(batchTask);
            log.info("处理指定的批量任务: {}", batchTaskId);
        } else {
            batchTaskList = makeVideoBatchTaskService.queryByStatus(VideoProcessStatusEnum.PROCESSING);
            if (batchTaskList.isEmpty()) {
                log.info("没有需要处理的批量任务");
                return;
            }
            log.info("需要处理的批量任务数量: {}", batchTaskList.size());
        }

        // 批量任务
        for (MakeVideoBatchTaskPO batchTask : batchTaskList) {
            List<MakeVideoSeriesTaskPO> seriesTaskList = makeVideoSeriesTaskService.queryByBatchId(batchTask.getId());
            if (seriesTaskList.isEmpty()) {
                log.info("批量任务 {} 没有子任务，跳过", batchTask.getId());
                continue;
            }

            // 已完成
            int completedCount = makeVideoBatchTaskService.calculateCompletedCount(batchTaskId);

            // 已失败
            int failedCount = makeVideoBatchTaskService.calculateFailedCount(batchTaskId);

            // 更新批量任务的数量
            MakeVideoBatchTaskPO batchTaskUpdate = new MakeVideoBatchTaskPO();
            batchTaskUpdate.setId(batchTask.getId());
            batchTaskUpdate.setCompletedCount(completedCount);
            batchTaskUpdate.setFailedCount(failedCount);
            if (completedCount >= batchTask.getTotalCount()) {
                batchTaskUpdate.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
            }
            makeVideoBatchTaskService.update(batchTaskUpdate);
            log.info("批量任务 {} 数量更新成功，已完成: {}, 失败: {}", batchTask.getId(), batchTaskUpdate.getCompletedCount(), batchTaskUpdate.getFailedCount());
        }
    }

}
