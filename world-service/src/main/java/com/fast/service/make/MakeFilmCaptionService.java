/*
 * Powered By fast.up
 */
package com.fast.service.make;

import com.fast.constant.StaticStr;
import com.fast.enums.FilmTypeEnum;
import com.fast.enums.LanguageEnum;
import com.fast.enums.YesOrNoEnum;
import com.fast.enums.video.VideoProcessStatusEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.make.MakeFilmCaptionMapper;
import com.fast.po.make.MakeFilmCaptionPO;
import com.fast.po.make.video.MakeSubtitleExtractTaskPO;
import com.fast.po.make.video.MakeSubtitleTranslateTaskPO;
import com.fast.service.base.BaseService;
import com.fast.service.make.video.MakeSubtitleExtractTaskServiceV2;
import com.fast.service.make.video.MakeSubtitleTranslateTaskService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MakeFilmCaptionService extends BaseService {

    @Autowired
    private MakeFilmCaptionMapper makeFilmCaptionMapper;

    @Autowired
    private MakeSubtitleExtractTaskServiceV2 makeSubtitleExtractTaskServiceV2;

    @Autowired
    private MakeSubtitleTranslateTaskService makeSubtitleTranslateTaskService;

    /**
     * 通过id查询单个对象
     */
    public MakeFilmCaptionPO queryById(MakeFilmCaptionPO params) {
        return makeFilmCaptionMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeFilmCaptionPO queryById(Integer id) {
        return makeFilmCaptionMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeFilmCaptionPO queryOne(MakeFilmCaptionPO params) {
        return makeFilmCaptionMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeFilmCaptionPO> queryList(MakeFilmCaptionPO params) {
        return makeFilmCaptionMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeFilmCaptionPO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeFilmCaptionPO> list = makeFilmCaptionMapper.queryList(params);
        for (MakeFilmCaptionPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            fillTaskInfo(cur);
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(MakeFilmCaptionPO params) {
        return makeFilmCaptionMapper.queryCount(params);
    }

    private void fillTaskInfo(MakeFilmCaptionPO cur) {
        if (cur.getSourced() == YesOrNoEnum.YES.getCode()) {
            MakeSubtitleExtractTaskPO task = makeSubtitleExtractTaskServiceV2.queryByFilmIdAndSeriesNum(cur.getFilmId(), cur.getSeriesNum());
            if (task == null) {
                cur.setState(VideoProcessStatusEnum.PENDING.getCode());
            } else {
                cur.setStartTime(task.getStartTime());
                cur.setEndTime(task.getEndTime());
                cur.setTakeTime(task.getTakeTime());
                cur.setState(task.getStatus());
                cur.setFailReason(task.getErrorMessage());
            }
        } else {
            MakeSubtitleTranslateTaskPO task = makeSubtitleTranslateTaskService.queryByFilmIdAndSeriesNumAndLang(cur.getFilmId(), cur.getSeriesNum(), cur.getLanguageCode());
            if (task == null) {
                cur.setState(VideoProcessStatusEnum.PENDING.getCode());
            } else {
                cur.setStartTime(task.getStartTime());
                cur.setEndTime(task.getEndTime());
                cur.setTakeTime(task.getTakeTime());
                cur.setState(task.getStatus());
                cur.setFailReason(task.getErrorMessage());
            }
        }
    }

    public MakeFilmCaptionPO queryByFilmIdAndTypeAndSeriesNumAndLang(Integer filmId, Integer filmType, Integer seriesNum, String lang, Integer type) {
        if (filmId == null || filmType == null || seriesNum == null || StrUtil.isBlank(lang) || type == null) {
            return null;
        }
        MakeFilmCaptionPO params = new MakeFilmCaptionPO();
        params.setFilmId(filmId);
        params.setFilmType(filmType);
        params.setSeriesNum(seriesNum);
        params.setLanguageCode(lang);
        params.setType(type);
        return makeFilmCaptionMapper.queryOne(params);
    }

    public List<MakeFilmCaptionPO> queryByFilmIdAndType(Integer filmId, Integer filmType) {
        if (filmId == null || filmType == null) {
            return null;
        }
        MakeFilmCaptionPO params = new MakeFilmCaptionPO();
        params.setFilmId(filmId);
        params.setFilmType(filmType);
        return makeFilmCaptionMapper.queryList(params);
    }

    public List<MakeFilmCaptionPO> queryByOriginalFilmId(Integer filmId) {
        return queryByFilmIdAndType(filmId, FilmTypeEnum.ORIGINAL.getCode());
    }

    public List<MakeFilmCaptionPO> queryByCompletedFilmId(Integer filmId) {
        return queryByFilmIdAndType(filmId, FilmTypeEnum.COMPLETED.getCode());
    }

    public Integer countByFilmIdAndType(Integer filmId, Integer filmType) {
        if (filmId == null || filmType == null) {
            return 0;
        }
        MakeFilmCaptionPO params = new MakeFilmCaptionPO();
        params.setFilmId(filmId);
        params.setFilmType(filmType);
        Integer count = makeFilmCaptionMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    public Integer countByOriginalFilmId(Integer filmId) {
        return countByFilmIdAndType(filmId, FilmTypeEnum.ORIGINAL.getCode());
    }

    public Integer countByCompletedFilmId(Integer filmId) {
        return countByFilmIdAndType(filmId, FilmTypeEnum.COMPLETED.getCode());
    }

    public Integer deleteByFilmIdAndType(Integer filmId, Integer filmType) {
        if (filmId == null || filmType == null) {
            return 0;
        }
        return makeFilmCaptionMapper.deleteByFilmIdAndType(filmId, filmType);
    }

    public Integer deleteByOriginalFilmId(Integer filmId) {
        return deleteByFilmIdAndType(filmId, FilmTypeEnum.ORIGINAL.getCode());
    }

    public Integer deleteByCompletedFilmId(Integer filmId) {
        return deleteByFilmIdAndType(filmId, FilmTypeEnum.COMPLETED.getCode());
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeFilmCaptionPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeFilmCaptionMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeFilmCaptionPO> list) {
        if (makeFilmCaptionMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeFilmCaptionPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeFilmCaptionMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeFilmCaptionPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeFilmCaptionPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        makeFilmCaptionMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public Set<String> getLangCodes(Integer filmId, Integer filmType) {
        List<MakeFilmCaptionPO> list = queryByFilmIdAndType(filmId, filmType);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().map(MakeFilmCaptionPO::getLanguageCode).collect(Collectors.toSet());
    }

    public int fillUrlByFilmAndSeriesNumAndLang(Integer filmId, Integer filmType, Integer seriesNum, String languageCode, String url, Integer type) {
        if (filmId == null || filmType == null || seriesNum == null || languageCode == null || url == null || type == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeFilmCaptionPO filmCaption = new MakeFilmCaptionPO();
        filmCaption.setFilmId(filmId);
        filmCaption.setFilmType(filmType);
        filmCaption.setSeriesNum(seriesNum);
        filmCaption.setLanguageCode(languageCode);
        filmCaption.setUrl(url);
        filmCaption.setType(type);
        return makeFilmCaptionMapper.updateByFilmAndSeriesNumAndLang(filmCaption);
    }

    public int initOriginalByFilmAndSeriesNumAndLang(Integer filmId, Integer seriesNum, String languageCode, String url, Integer type) {
        if (filmId == null || seriesNum == null || languageCode == null || url == null || type == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeFilmCaptionPO filmCaption = queryByFilmIdAndTypeAndSeriesNumAndLang(filmId, FilmTypeEnum.ORIGINAL.getCode(), seriesNum, languageCode, type);
        if (filmCaption != null) {
            return 0;
        }
        filmCaption = new MakeFilmCaptionPO();
        filmCaption.setFilmId(filmId);
        filmCaption.setFilmType(FilmTypeEnum.ORIGINAL.getCode());
        filmCaption.setName(LanguageEnum.ofCode(languageCode).getName() + " - " + seriesNum + ".srt");
        filmCaption.setType(type);
        filmCaption.setLanguageCode(languageCode);
        filmCaption.setSeriesNum(seriesNum);
        filmCaption.setUrl(url);
        return makeFilmCaptionMapper.insertSelective(filmCaption);
    }

}
