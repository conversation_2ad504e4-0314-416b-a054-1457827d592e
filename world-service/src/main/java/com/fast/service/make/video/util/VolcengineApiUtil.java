package com.fast.service.make.video.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fast.utils.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 火山API服务
 * <p>
 * 整合了字幕提取、擦除、压制三个API的调用
 */
@Component
@Slf4j
public class VolcengineApiUtil {

    @Value("${spring.profiles.active}")
    private String env;

    private static final String APP_KEY = "AKLTNGEzODBlNWM2MmJlNDgzMTlkZmJhNGVhY2NlNTdhYmQ";
    private static final String APP_SECRET = "T0Rnd05XVmxNV1pqT1RNME5HRmtPV0k0TXpjeFlqUTJNVFUwWkdZNE9UYw==";

    private static final String host = "vod.volcengineapi.com";
    private static final String path = "/";
    private static final String service = "vod";
    private static final String region = "cn-north-1";
    private static final String schema = "https";

    private static final BitSet URLENCODER = new BitSet(256);
    private static final String CONST_ENCODE = "0123456789ABCDEF";
    public static final Charset UTF_8 = StandardCharsets.UTF_8;

    static {
        int i;
        for (i = 97; i <= 122; ++i) {
            URLENCODER.set(i);
        }

        for (i = 65; i <= 90; ++i) {
            URLENCODER.set(i);
        }

        for (i = 48; i <= 57; ++i) {
            URLENCODER.set(i);
        }
        URLENCODER.set('-');
        URLENCODER.set('_');
        URLENCODER.set('.');
        URLENCODER.set('~');
    }

    public JSONObject doRequest(String method, Map<String, String> queryList, byte[] body, Date date, String action, String version) {
        try {
            if (body == null) {
                body = new byte[0];
            }
            String xContentSha256 = hashSHA256(body);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
            sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
            String xDate = sdf.format(date);
            String shortXDate = xDate.substring(0, 8);
            String contentType = "application/json";

            String signHeader = "host;x-date;x-content-sha256;content-type";

            SortedMap<String, String> realQueryList = new TreeMap<>(queryList);
            realQueryList.put("Action", action);
            realQueryList.put("Version", version);
            StringBuilder querySB = new StringBuilder();
            for (String key : realQueryList.keySet()) {
                querySB.append(signStringEncoder(key)).append("=").append(signStringEncoder(realQueryList.get(key))).append("&");
            }
            querySB.deleteCharAt(querySB.length() - 1);

            String canonicalStringBuilder = method + "\n" + path + "\n" + querySB + "\n" +
                    "host:" + host + "\n" +
                    "x-date:" + xDate + "\n" +
                    "x-content-sha256:" + xContentSha256 + "\n" +
                    "content-type:" + contentType + "\n" +
                    "\n" +
                    signHeader + "\n" +
                    xContentSha256;

            String hashcanonicalString = hashSHA256(canonicalStringBuilder.getBytes());
            String credentialScope = shortXDate + "/" + region + "/" + service + "/request";
            String signString = "HMAC-SHA256" + "\n" + xDate + "\n" + credentialScope + "\n" + hashcanonicalString;

            byte[] signKey = genSigningSecretKeyV4(APP_SECRET, shortXDate, region, service);
            String signature = HexFormat.of().formatHex(hmacSHA256(signKey, signString));

            URL url = new URL(schema + "://" + host + path + "?" + querySB);

            log.info("[ url]: {}", url);
            log.info("[sign]: {}", signature);
            log.info("[body]: {}", new String(body, UTF_8));

            long start = System.currentTimeMillis();

            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod(method);
            conn.setRequestProperty("Host", host);
            conn.setRequestProperty("X-Date", xDate);
            conn.setRequestProperty("X-Content-Sha256", xContentSha256);
            conn.setRequestProperty("Content-Type", contentType);
            conn.setRequestProperty("Authorization", "HMAC-SHA256" +
                    " Credential=" + APP_KEY + "/" + credentialScope +
                    ", SignedHeaders=" + signHeader +
                    ", Signature=" + signature);
            if (!Objects.equals(conn.getRequestMethod(), "GET")) {
                conn.setDoOutput(true);
                OutputStream os = conn.getOutputStream();
                os.write(body);
                os.flush();
                os.close();
            }
            conn.connect();

            int responseCode = conn.getResponseCode();

            InputStream is;
            if (responseCode == 200) {
                is = conn.getInputStream();
            } else {
                is = conn.getErrorStream();
            }
            String responseBody = new String(is.readAllBytes());
            is.close();

            log.info("[ res]: {}", responseBody);

            long end = System.currentTimeMillis();
            log.info("[time]: {}s", (end - start) / 1000f);

            return JSON.parseObject(responseBody);
        } catch (Exception e) {
            log.error("火山API请求失败: endpoint={}", host, e);
            return null;
        }
    }

    private String signStringEncoder(String source) {
        if (source == null) {
            return null;
        }
        StringBuilder buf = new StringBuilder(source.length());
        ByteBuffer bb = UTF_8.encode(source);
        while (bb.hasRemaining()) {
            int b = bb.get() & 255;
            if (URLENCODER.get(b)) {
                buf.append((char) b);
            } else if (b == 32) {
                buf.append("%20");
            } else {
                buf.append("%");
                char hex1 = CONST_ENCODE.charAt(b >> 4);
                char hex2 = CONST_ENCODE.charAt(b & 15);
                buf.append(hex1);
                buf.append(hex2);
            }
        }

        return buf.toString();
    }

    public static String hashSHA256(byte[] content) throws Exception {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");

            return HexFormat.of().formatHex(md.digest(content));
        } catch (Exception e) {
            throw new Exception(
                    "Unable to compute hash while signing request: "
                            + e.getMessage(), e);
        }
    }

    public static byte[] hmacSHA256(byte[] key, String content) throws Exception {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(key, "HmacSHA256"));
            return mac.doFinal(content.getBytes());
        } catch (Exception e) {
            throw new Exception(
                    "Unable to calculate a request signature: "
                            + e.getMessage(), e);
        }
    }

    private byte[] genSigningSecretKeyV4(String secretKey, String date, String region, String service) throws Exception {
        byte[] kDate = hmacSHA256((secretKey).getBytes(), date);
        byte[] kRegion = hmacSHA256(kDate, region);
        byte[] kService = hmacSHA256(kRegion, service);
        return hmacSHA256(kService, "request");
    }

    public JSONObject applyUploadInfo() {
        log.info("获取上传地址和凭证");

        String action = "ApplyUploadInfo";
        String version = "2022-01-01";
        Map<String, String> queryMap = new HashMap<>() {
            {
                put("SpaceName", "space5ibkow");
            }
        };
        String body = "";
        Date date = new Date();
        return doRequest("GET", queryMap, body.getBytes(), date, action, version);
    }

    public JSONObject commitUploadInfo(String sessionKey) {
        log.info("确认上传");

        String action = "CommitUploadInfo";
        String version = "2022-01-01";
        Map<String, String> queryMap = new HashMap<>() {
            {
                put("SpaceName", "space5ibkow");
                put("SessionKey", sessionKey);
            }
        };
        String body = "";
        Date date = new Date();
        return doRequest("POST", queryMap, body.getBytes(), date, action, version);
    }

    /**
     * URL 批量拉取上传
     */
    public JSONObject uploadMediaByUrl(String fid, String fname, String url) {
        log.info("URL 批量拉取上传，fid={}, fname={}, url={}", fid, fname, url);

        String action = "UploadMediaByUrl";
        String version = "2020-08-01";
        Map<String, String> queryMap = new HashMap<>() {
        };

        JSONObject item = new JSONObject();
        item.put("Title", fname);
        item.put("FileName", StrUtil.substringAfterLast(url, "/"));
        item.put("SourceUrl", url);
        item.put("CallbackArgs", env + "_" + fid);

        JSONObject param = new JSONObject();
        param.put("SpaceName", "space5ibkow");
        param.put("URLSets", List.of(item));

        String body = param.toJSONString();
        Date date = new Date();
        return doRequest("POST", queryMap, body.getBytes(), date, action, version);
    }

    /**
     * 查询 URL 批量上传任务状态
     */
    public JSONObject queryUploadTaskInfo(String jobIds) {
        log.info("查询 URL 批量上传任务状态，jobIds={}", jobIds);

        String action = "QueryUploadTaskInfo";
        String version = "2020-08-01";
        Map<String, String> queryMap = new HashMap<>() {
            {
                put("JobIds", jobIds);
            }
        };

        String body = "";
        Date date = new Date();
        return doRequest("GET", queryMap, body.getBytes(), date, action, version);
    }

    /**
     * 提交人声分离任务（支持批量）
     */
    public JSONObject audioExtract(String vid) {
        log.info("提交人声分离任务: vid={}", vid);

        JSONObject input = new JSONObject();
        input.put("Type", "Vid");
        input.put("Vid", vid);

        JSONObject audioExtract = new JSONObject();
        audioExtract.put("Voice", true);

        JSONObject task = new JSONObject();
        task.put("Type", "AudioExtract");
        task.put("AudioExtract", audioExtract);

        JSONObject operation = new JSONObject();
        operation.put("Type", "Task");
        operation.put("Task", task);

        JSONObject control = new JSONObject();
        control.put("CallbackArgs", env + "_" + vid);

        JSONObject params = new JSONObject();
        params.put("Input", input);
        params.put("Operation", operation);
        params.put("Control", control);

        String action = "StartExecution";
        String version = "2025-01-01";
        Map<String, String> queryMap = new HashMap<>();
        String body = params.toJSONString();
        Date date = new Date();
        return doRequest("POST", queryMap, body.getBytes(), date, action, version);
    }

    /**
     * 提交字幕提取任务（支持批量），Asr方式
     */
    public JSONObject subtitleExtractAsr(String vid) {
        log.info("[Asr] 提交字幕提取任务: vid={}", vid);

        JSONObject input = new JSONObject();
        input.put("Type", "Vid");
        input.put("Vid", vid);

        JSONObject asr = new JSONObject();
        asr.put("Language", "cmn-Hans-CN");
        asr.put("Type", "speech");

        JSONObject task = new JSONObject();
        task.put("Type", "Asr");
        task.put("Asr", asr);

        JSONObject operation = new JSONObject();
        operation.put("Type", "Task");
        operation.put("Task", task);

        JSONObject control = new JSONObject();
        control.put("CallbackArgs", env + "_" + vid);

        JSONObject params = new JSONObject();
        params.put("Input", input);
        params.put("Operation", operation);
        params.put("Control", control);

        String action = "StartExecution";
        String version = "2025-01-01";
        Map<String, String> queryMap = new HashMap<>();
        String body = params.toJSONString();
        Date date = new Date();
        return doRequest("POST", queryMap, body.getBytes(), date, action, version);
    }

    /**
     * 提交字幕提取任务（支持批量），Ocr方式
     */
    public JSONObject subtitleExtractOcr(String vid) {
        log.info("[Ocr] 提交字幕提取任务: vid={}", vid);

        JSONObject input = new JSONObject();
        input.put("Type", "Vid");
        input.put("Vid", vid);

        JSONObject ocr = new JSONObject();
        // ocr.put("Mode", "Lite");

        JSONObject task = new JSONObject();
        task.put("Type", "Ocr");
        task.put("Ocr", ocr);

        JSONObject operation = new JSONObject();
        operation.put("Type", "Task");
        operation.put("Task", task);

        JSONObject control = new JSONObject();
        control.put("CallbackArgs", env + "_" + vid);

        JSONObject params = new JSONObject();
        params.put("Input", input);
        params.put("Operation", operation);
        params.put("Control", control);

        String action = "StartExecution";
        String version = "2025-01-01";
        Map<String, String> queryMap = new HashMap<>();
        String body = params.toJSONString();
        Date date = new Date();
        return doRequest("POST", queryMap, body.getBytes(), date, action, version);
    }

    /**
     * 提交字幕擦除任务（支持批量）
     */
    public JSONObject subtitleRemove(String vid) {
        log.info("提交字幕擦除任务: vid={}", vid);

        JSONObject input = new JSONObject();
        input.put("Type", "Vid");
        input.put("Vid", vid);

        JSONObject type = new JSONObject();
        type.put("Type", "Subtitle");
        // type.put("Type", "Text");

        JSONObject erase = new JSONObject();
        erase.put("Mode", "Auto");
        erase.put("Auto", type);
        // erase.put("WithEraseInfo", true);

        JSONObject task = new JSONObject();
        task.put("Type", "Erase");
        task.put("Erase", erase);

        JSONObject operation = new JSONObject();
        operation.put("Type", "Task");
        operation.put("Task", task);

        JSONObject control = new JSONObject();
        control.put("CallbackArgs", env + "_" + vid);

        JSONObject params = new JSONObject();
        params.put("Input", input);
        params.put("Operation", operation);
        params.put("Control", control);

        String action = "StartExecution";
        String version = "2025-01-01";
        Map<String, String> queryMap = new HashMap<>();
        String body = params.toJSONString();
        Date date = new Date();
        return doRequest("POST", queryMap, body.getBytes(), date, action, version);
    }

    public static void main(String[] args) throws JsonProcessingException {
        VolcengineApiUtil util = new VolcengineApiUtil();
        util.env = "dev";
        // util.queryUploadTaskInfo("3096ec2d631441f0ac9be30e823ade9f");
        util.subtitleExtractOcr("v0df56g10064d2737kqljht9mk8vl41g");
    }

}
