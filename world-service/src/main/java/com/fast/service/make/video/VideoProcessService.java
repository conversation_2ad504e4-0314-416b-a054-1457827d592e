// package com.fast.service.make.video;
//
// import com.alibaba.fastjson.JSON;
// import com.alibaba.fastjson.JSONObject;
// import com.fast.constant.StaticStr;
// import com.fast.enums.FilmTypeEnum;
// import com.fast.enums.YesOrNoEnum;
// import com.fast.enums.video.GhostCutProcessStatusEnum;
// import com.fast.enums.video.VideoProcessStatusEnum;
// import com.fast.enums.video.VideoProcessStepEnum;
// import com.fast.framework.exception.MyException;
// import com.fast.po.make.MakeCompletedFilmPO;
// import com.fast.po.make.MakeFilmCaptionPO;
// import com.fast.po.make.MakeFilmVideoPO;
// import com.fast.po.make.MakeOriginalFilmPO;
// import com.fast.po.make.video.*;
// import com.fast.service.make.MakeCompletedFilmService;
// import com.fast.service.make.MakeFilmCaptionService;
// import com.fast.service.make.MakeFilmVideoService;
// import com.fast.service.make.MakeOriginalFilmService;
// import com.fast.service.oss.OssService;
// import com.fast.utils.CollUtil;
// import com.fast.utils.DateUtil;
// import com.fast.utils.file.FileUtil;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;
// import org.springframework.transaction.support.TransactionSynchronization;
// import org.springframework.transaction.support.TransactionSynchronizationManager;
//
// import java.io.File;
// import java.util.Date;
// import java.util.List;
// import java.util.Objects;
//
// /**
//  * 视频处理服务
//  */
// @Service
// @Slf4j
// public class VideoProcessService {
//
//     @Autowired
//     private MakeCompletedFilmService makeCompletedFilmService;
//
//     @Autowired
//     private MakeOriginalFilmService makeOriginalFilmService;
//
//     @Autowired
//     private MakeVideoBatchTaskService makeVideoBatchTaskService;
//
//     @Autowired
//     private MakeVideoSeriesTaskService makeVideoSeriesTaskService;
//
//     @Autowired
//     private MakeSubtitleExtractTaskService makeSubtitleExtractTaskService;
//
//     @Autowired
//     private MakeSubtitleRemoveTaskService makeSubtitleRemoveTaskService;
//
//     @Autowired
//     private MakeVideoCompressTaskService makeVideoCompressTaskService;
//
//     @Autowired
//     private MakeFilmVideoService makeFilmVideoService;
//
//     @Autowired
//     private MakeFilmCaptionService makeFilmCaptionService;
//
//     @Autowired
//     private VideoProcessFlowManager videoProcessFlowManager;
//
//     @Autowired
//     private OssService ossService;
//
//     /**
//      * 临时文件存储路径
//      * 注意：实际部署时请修改为合适的路径
//      */
//     private static final String tmpPath = "/home/<USER>/video";
//
//     /**
//      * 创建视频处理任务
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public Integer createTask(Integer filmId, MakeCompletedFilmPO film) {
//         log.info("创建视频处理工作流任务: filmId={}", filmId);
//
//         // MakeCompletedFilmPO film = makeCompletedFilmService.queryById(filmId);
//         // if (film == null) {
//         //     throw new MyException("成片不存在");
//         // }
//
//         MakeOriginalFilmPO originalFilm = makeOriginalFilmService.queryById(film.getOriginalFilmId());
//         if (originalFilm == null) {
//             throw new MyException("原片不存在");
//         }
//
//         List<MakeFilmVideoPO> filmVideos = makeFilmVideoService.queryByOriginalFilmId(originalFilm.getId());
//         if (CollUtil.isEmpty(filmVideos)) {
//             throw new MyException("原片缺失视频数据");
//         }
//
//         Date nowDate = DateUtil.getNowDate();
//         String regionJson = JSON.toJSONString(film.getRegion());
//
//         // 新增批次任务
//         MakeVideoBatchTaskPO batchTask = new MakeVideoBatchTaskPO();
//         batchTask.setFilmId(film.getId());
//         batchTask.setFilmName(film.getName());
//         batchTask.setTotalCount(film.getTotal());
//         batchTask.setSourceLang(film.getSourceLang());
//         batchTask.setDestLangs(film.getDestLangs());
//         batchTask.setRemoveBgAudio(film.getRemoveBgAudio());
//         batchTask.setRegion(regionJson);
//         batchTask.setStartTime(nowDate);
//         makeVideoBatchTaskService.insert(batchTask);
//         log.info("创建视频处理工作流任务成功: batchTaskId={}", batchTask.getId());
//
//         // 新增剧集子任务
//         for (MakeFilmVideoPO filmVideo : filmVideos) {
//             MakeVideoSeriesTaskPO seriesTask = new MakeVideoSeriesTaskPO();
//             seriesTask.setBatchId(batchTask.getId());
//             seriesTask.setFilmId(film.getId());
//             seriesTask.setSeriesNum(filmVideo.getSeriesNum());
//             seriesTask.setOriginalVideoId(filmVideo.getFid());
//             seriesTask.setOriginalVideoUrl(filmVideo.getUrl());
//             seriesTask.setStartTime(nowDate);
//             makeVideoSeriesTaskService.insert(seriesTask);
//             log.info("创建剧集子任务成功: seriesTaskId={}, batchTaskId={}, 启动该剧集的视频处理流程", batchTask.getId(), seriesTask.getId());
//
//             // 启动异步流程
//             videoProcessFlowManager.startProcess(batchTask, seriesTask);
//         }
//
//         return batchTask.getId();
//     }
//
//     /**
//      * 执行字幕提取 - 回调
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public void subtitleExtractNotify(JSONObject request) {
//         Date nowDate = DateUtil.getNowDate();
//
//         String idProject = request.getString("idProject");
//         String id = request.getString("id");
//
//         List<MakeSubtitleExtractTaskPO> extractTasks = makeSubtitleExtractTaskService.queryByThirdPartyId(idProject, id);
//         if (extractTasks == null) {
//             log.error("未找到对应的字幕提取任务: thirdPartyBatchId={}, thirdPartyTaskId={}", idProject, id);
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//         MakeSubtitleExtractTaskPO extractTask = extractTasks.getFirst();
//
//         MakeVideoSeriesTaskPO seriesTask = makeVideoSeriesTaskService.queryById(extractTask.getSeriesTaskId());
//         if (seriesTask == null) {
//             log.error("未找到对应的剧集子任务: seriesTaskId={}", extractTask.getSeriesTaskId());
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//
//         MakeVideoBatchTaskPO batchTask = makeVideoBatchTaskService.queryById(seriesTask.getBatchId());
//         if (batchTask == null) {
//             log.error("未找到对应的批量任务: batchTaskId={}", seriesTask.getBatchId());
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//
//         if (Objects.equals(extractTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
//             log.warn("字幕提取任务 {} 的状态已经是 已完成，本次回调不予处理", extractTask.getId());
//             return;
//         }
//
//         // 鬼手任务处理状态，处理成功后扣除相应点数
//         // <1：处理中
//         // =1：处理成功
//         // >1：处理失败
//         Integer processStatus = request.getInteger("processStatus");
//         extractTask.setThirdPartyStatus(String.valueOf(processStatus));
//         extractTask.setCallbackData(request.toJSONString());
//
//         extractTask.setEndTime(nowDate);
//         extractTask.setTakeTime(DateUtil.getDiffTime(extractTask.getStartTime(), extractTask.getEndTime()));
//
//         // 过程中不会有回调，理论上只有成功和失败
//         if (!Objects.equals(processStatus, GhostCutProcessStatusEnum.SUCCESS.getCode())) {
//             JSONObject statusEnum = request.getJSONObject("processStatusEnum");
//             extractTask.setErrorMessage(statusEnum.getString("description"));
//             extractTask.setStatus(VideoProcessStatusEnum.FAILED.getCode());
//             makeSubtitleExtractTaskService.update(extractTask);
//             log.warn("更新字幕提取任务 {} 的状态为 失败，并录入失败原因，本次调用鬼手api消耗点数 {}", extractTask.getId(), extractTask.getPaidPoint());
//
//             // 更新剧集子任务状态为失败
//             makeVideoSeriesTaskService.updateStatusAndEndTime(seriesTask.getId(), VideoProcessStatusEnum.FAILED, nowDate);
//             log.warn("更新剧集子任务 {} 的状态为 失败", seriesTask.getId());
//
//             // 更新批量任务状态为失败
//             makeVideoBatchTaskService.updateStatusAndEndTime(batchTask.getId(), VideoProcessStatusEnum.FAILED, nowDate);
//             log.warn("更新批量任务 {} 的状态为 失败", batchTask.getId());
//         } else {
//             // 生成文件名
//             String fileName = String.format("Subtitle_%d_%d_%s.srt", seriesTask.getId(), seriesTask.getSeriesNum(), batchTask.getSourceLang());
//             String srcSrtUrl = processOssUrl(request.getString("srcSrtUrl"), 5, fileName, batchTask.getSourceLang());
//
//             extractTask.setUrl(srcSrtUrl);
//             extractTask.setPaidPoint(request.getInteger("paidPoint"));
//             extractTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
//             makeSubtitleExtractTaskService.update(extractTask);
//             log.info("更新字幕提取任务 {} 的状态为 已完成，本次调用鬼手api消耗点数 {}", extractTask.getId(), extractTask.getPaidPoint());
//
//             seriesTask.setExtractCompleted(YesOrNoEnum.YES.getCode());
//             seriesTask.setPaidPoint(seriesTask.getPaidPoint() + extractTask.getPaidPoint());
//             makeVideoSeriesTaskService.update(seriesTask);
//             log.info("更新剧集子任务 {} 的总消耗点数 {}，字幕提取标记更新为 true", seriesTask.getId(), seriesTask.getPaidPoint());
//
//             batchTask.setPaidPoint(batchTask.getPaidPoint() + extractTask.getPaidPoint());
//             makeVideoBatchTaskService.update(batchTask);
//             log.info("更新批量任务 {} 的总消耗点数 {}", extractTask.getId(), batchTask.getPaidPoint());
//
//             // 同步更新成片完成数量
//             makeVideoBatchTaskService.addCompletedCount(batchTask.getId());
//             makeCompletedFilmService.addCompletedNum(batchTask.getFilmId());
//             log.info("字幕提取任务 {} 处理完毕，处理完成总数量+1", extractTask.getId());
//
//             // 同步补齐成片字幕数据
//             MakeFilmCaptionPO caption = new MakeFilmCaptionPO();
//             caption.setFilmId(batchTask.getFilmId());
//             caption.setFilmType(FilmTypeEnum.COMPLETED.getCode());
//             caption.setSeriesNum(seriesTask.getSeriesNum());
//             caption.setLanguageCode(extractTask.getLanguageCode());
//             caption.setUrl(extractTask.getUrl());
//             makeFilmCaptionService.updateByFilmAndSeriesNumAndLang(caption);
//             log.info("字幕提取任务完成，同步补齐成片 {} 源语言的字幕数据", batchTask.getFilmId());
//
//             // 触发下一步流程：字幕擦除、字幕翻译
//             TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//                 @Override
//                 public void afterCommit() {
//                     videoProcessFlowManager.executeNextStep(VideoProcessStepEnum.SUBTITLE_EXTRACT, batchTask, seriesTask);
//                     log.info("字幕提取任务 {} 完成，触发擦除和翻译", extractTask.getId());
//                 }
//             });
//         }
//     }
//
//     /**
//      * 执行字幕擦除 - 回调
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public void subtitleRemoveNotify(JSONObject request) {
//         Date nowDate = DateUtil.getNowDate();
//
//         String idProject = request.getString("idProject");
//         String id = request.getString("id");
//
//         List<MakeSubtitleRemoveTaskPO> removeTasks = makeSubtitleRemoveTaskService.queryByThirdPartyId(idProject, id);
//         if (CollUtil.isEmpty(removeTasks)) {
//             log.error("未找到对应的字幕擦除任务: thirdPartyBatchId={}, thirdPartyTaskId={}", idProject, id);
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//         MakeSubtitleRemoveTaskPO removeTask = removeTasks.getFirst();
//
//         MakeVideoSeriesTaskPO seriesTask = makeVideoSeriesTaskService.queryById(removeTask.getSeriesTaskId());
//         if (seriesTask == null) {
//             log.error("未找到对应的剧集子任务: seriesTaskId={}", removeTask.getSeriesTaskId());
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//
//         MakeVideoBatchTaskPO batchTask = makeVideoBatchTaskService.queryById(seriesTask.getBatchId());
//         if (batchTask == null) {
//             log.error("未找到对应的批量任务: batchTaskId={}", seriesTask.getBatchId());
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//
//         if (Objects.equals(removeTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
//             log.warn("字幕擦除任务 {} 的状态已经是 已完成，本次回调不予处理", removeTask.getId());
//             return;
//         }
//
//         // 鬼手任务处理状态，处理成功后扣除相应点数
//         // <1：处理中
//         // =1：处理成功
//         // >1：处理失败
//         Integer processStatus = request.getInteger("processStatus");
//         removeTask.setThirdPartyStatus(String.valueOf(processStatus));
//         removeTask.setCallbackData(request.toJSONString());
//
//         removeTask.setEndTime(nowDate);
//         removeTask.setTakeTime(DateUtil.getDiffTime(removeTask.getStartTime(), removeTask.getEndTime()));
//
//         // 过程中不会有回调，理论上只有成功和失败
//         if (!Objects.equals(processStatus, GhostCutProcessStatusEnum.SUCCESS.getCode())) {
//             JSONObject statusEnum = request.getJSONObject("processStatusEnum");
//             removeTask.setErrorMessage(statusEnum.getString("description"));
//             removeTask.setStatus(VideoProcessStatusEnum.FAILED.getCode());
//             makeSubtitleRemoveTaskService.update(removeTask);
//             log.warn("更新字幕擦除任务 {} 的状态为 失败，并录入失败原因，本次调用鬼手api消耗点数 {}", removeTask.getId(), removeTask.getPaidPoint());
//
//             // 更新剧集子任务状态为失败
//             makeVideoSeriesTaskService.updateStatusAndEndTime(seriesTask.getId(), VideoProcessStatusEnum.FAILED, nowDate);
//             log.warn("更新剧集子任务 {} 的状态为 失败", seriesTask.getId());
//
//             // 更新批量任务状态为失败
//             makeVideoBatchTaskService.updateStatusAndEndTime(batchTask.getId(), VideoProcessStatusEnum.FAILED, nowDate);
//             log.warn("更新批量任务 {} 的状态为 失败", seriesTask.getId());
//         } else {
//             // 生成文件名
//             String fileName = String.format("Video_%d_%d_%s.mp4", seriesTask.getId(), seriesTask.getSeriesNum(), "00");
//             String videoUrl = processOssUrl(request.getString("videoUrl"), 3, fileName, "00");
//
//             removeTask.setUrl(videoUrl);
//             removeTask.setPaidPoint(request.getInteger("paidPoint"));
//             removeTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
//             makeSubtitleRemoveTaskService.update(removeTask);
//             log.info("更新字幕擦除任务 {} 的状态为 已完成，本次调用鬼手api消耗点数 {}", removeTask.getId(), removeTask.getPaidPoint());
//
//             seriesTask.setRemoveCompleted(YesOrNoEnum.YES.getCode());
//             seriesTask.setPaidPoint(seriesTask.getPaidPoint() + removeTask.getPaidPoint());
//             makeVideoSeriesTaskService.update(seriesTask);
//             log.info("更新剧集子任务 {} 的总消耗点数 {}", seriesTask.getId(), seriesTask.getPaidPoint());
//
//             batchTask.setPaidPoint(batchTask.getPaidPoint() + removeTask.getPaidPoint());
//             makeVideoBatchTaskService.update(batchTask);
//             log.info("更新批量任务 {} 的总消耗点数 {}", removeTask.getId(), batchTask.getPaidPoint());
//
//             // 同步更新成片完成数量
//             makeVideoBatchTaskService.addCompletedCount(batchTask.getId());
//             makeCompletedFilmService.addCompletedNum(batchTask.getFilmId());
//             log.info("字幕擦除任务 {} 处理完毕，处理完成总数量+1", removeTask.getId());
//
//             // 同步补齐成片视频数据
//             MakeFilmVideoPO filmVideo = new MakeFilmVideoPO();
//             filmVideo.setFilmId(batchTask.getFilmId());
//             filmVideo.setFilmType(FilmTypeEnum.COMPLETED.getCode());
//             filmVideo.setSeriesNum(seriesTask.getSeriesNum());
//             filmVideo.setLanguageCode("00");
//             filmVideo.setUrl(removeTask.getUrl());
//             makeFilmVideoService.updateByFilmAndSeriesNumAndLang(filmVideo);
//             log.info("字幕擦除任务完成，同步补齐成片 {} 源语言的视频数据", batchTask.getFilmId());
//
//             // 触发下一步流程：视频压制
//             TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//                 @Override
//                 public void afterCommit() {
//                     videoProcessFlowManager.executeNextStep(VideoProcessStepEnum.SUBTITLE_REMOVE, batchTask, seriesTask);
//                     log.info("字幕擦除任务 {} 完成，触发压制", removeTask.getId());
//                 }
//             });
//         }
//     }
//
//     /**
//      * 执行视频压制 - 回调
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public void videoCompressNotify(JSONObject request) {
//         Date nowDate = DateUtil.getNowDate();
//
//         String idProject = request.getString("idProject");
//         String id = request.getString("id");
//
//         MakeVideoCompressTaskPO compressTask = makeVideoCompressTaskService.queryByThirdPartyId(idProject, id);
//         if (compressTask == null) {
//             log.error("未找到对应的视频压制任务: thirdPartyBatchId={}, thirdPartyTaskId={}", idProject, id);
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//
//         MakeVideoSeriesTaskPO seriesTask = makeVideoSeriesTaskService.queryById(compressTask.getSeriesTaskId());
//         if (seriesTask == null) {
//             log.error("未找到对应的剧集子任务: seriesTaskId={}", compressTask.getSeriesTaskId());
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//
//         MakeVideoBatchTaskPO batchTask = makeVideoBatchTaskService.queryById(seriesTask.getBatchId());
//         if (batchTask == null) {
//             log.error("未找到对应的批量任务: batchTaskId={}", seriesTask.getBatchId());
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//
//         if (Objects.equals(compressTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
//             log.warn("视频压制任务 {} 的状态已经是 已完成，本次回调不予处理", compressTask.getId());
//             return;
//         }
//
//         // 鬼手任务处理状态，处理成功后扣除相应点数
//         // <1：处理中
//         // =1：处理成功
//         // >1：处理失败
//         Integer processStatus = request.getInteger("processStatus");
//         compressTask.setThirdPartyStatus(String.valueOf(processStatus));
//         compressTask.setCallbackData(request.toJSONString());
//
//         compressTask.setEndTime(nowDate);
//         compressTask.setTakeTime(DateUtil.getDiffTime(compressTask.getStartTime(), compressTask.getEndTime()));
//
//         // 过程中不会有回调，理论上只有成功和失败
//         if (!Objects.equals(processStatus, GhostCutProcessStatusEnum.SUCCESS.getCode())) {
//             JSONObject statusEnum = request.getJSONObject("processStatusEnum");
//             compressTask.setErrorMessage(statusEnum.getString("description"));
//             compressTask.setStatus(VideoProcessStatusEnum.FAILED.getCode());
//             makeVideoCompressTaskService.update(compressTask);
//             log.warn("更新视频压制任务 {} 的状态为 失败，并录入失败原因，本次调用鬼手api消耗点数 {}", compressTask.getId(), compressTask.getPaidPoint());
//
//             // 更新剧集子任务状态为失败
//             makeVideoSeriesTaskService.updateStatusAndEndTime(seriesTask.getId(), VideoProcessStatusEnum.FAILED, nowDate);
//             log.warn("更新剧集子任务 {} 的状态为 失败", seriesTask.getId());
//
//             // 更新批量任务状态为失败
//             makeVideoBatchTaskService.updateStatusAndEndTime(batchTask.getId(), VideoProcessStatusEnum.FAILED, nowDate);
//             log.warn("更新批量任务 {} 的状态为 失败", batchTask.getId());
//         } else {
//             // 生成文件名
//             String fileName = String.format("Video_%d_%d_%s.mp4", seriesTask.getId(), seriesTask.getSeriesNum(), compressTask.getLanguageCode());
//             String videoUrl = processOssUrl(request.getString("videoUrl"), 3, fileName, compressTask.getLanguageCode());
//
//             compressTask.setUrl(videoUrl);
//             compressTask.setPaidPoint(request.getInteger("paidPoint"));
//             compressTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
//             makeVideoCompressTaskService.update(compressTask);
//             log.info("更新视频压制任务 {} 的状态为 已完成，本次调用鬼手api消耗点数 {}", compressTask.getId(), compressTask.getPaidPoint());
//
//             // 统计剧集子任务下的压制任务完成情况
//             boolean allCompressTaskCompleted = makeVideoCompressTaskService.isAllCompleted(seriesTask.getId());
//             if (allCompressTaskCompleted) {
//                 seriesTask.setCompressCompleted(YesOrNoEnum.YES.getCode());
//                 seriesTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
//                 seriesTask.setEndTime(nowDate);
//                 seriesTask.setTakeTime(DateUtil.getDiffTime(seriesTask.getStartTime(), seriesTask.getEndTime()));
//                 log.info("剧集子任务 {} 所有压制任务完成，意味着整个流程终结，更新剧集状态为 已完成", seriesTask.getId());
//             }
//             seriesTask.setPaidPoint(seriesTask.getPaidPoint() + compressTask.getPaidPoint());
//             makeVideoSeriesTaskService.update(seriesTask);
//             log.info("更新剧集子任务 {} 的总消耗点数 {}", seriesTask.getId(), seriesTask.getPaidPoint());
//
//             // 统计批量任务下的剧集子任务完成情况
//             boolean allSeriesTaskCompleted = makeVideoSeriesTaskService.isAllCompleted(batchTask.getId());
//             if (allSeriesTaskCompleted) {
//                 batchTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
//                 batchTask.setEndTime(nowDate);
//                 batchTask.setTakeTime(DateUtil.getDiffTime(batchTask.getStartTime(), batchTask.getEndTime()));
//                 log.info("批量任务 {} 所有剧集子任务完成，意味着整个流程终结，更新批量任务状态为 已完成", batchTask.getId());
//             }
//             batchTask.setPaidPoint(batchTask.getPaidPoint() + compressTask.getPaidPoint());
//             makeVideoBatchTaskService.update(batchTask);
//             log.info("更新批量任务 {} 的总消耗点数 {}", compressTask.getId(), batchTask.getPaidPoint());
//
//             // 同步更新成片完成数量
//             makeVideoBatchTaskService.addCompletedCount(batchTask.getId());
//             makeCompletedFilmService.addCompletedNum(batchTask.getFilmId());
//             log.info("视频压制任务 {} 处理完毕，处理完成总数量+1", compressTask.getId());
//         }
//     }
//
// }
