package com.fast.service.make.video.util;

import com.google.common.util.concurrent.RateLimiter;

import java.util.concurrent.Semaphore;

/**
 * 三方API速率限制器，qps过高会导致API调用失败
 * Created by <PERSON> on 2025/08/06.
 */
public class ThirdPartyApiRateLimiter {

    /**
     * 媒体任务提交的限流器
     */
    // public static final RateLimiter START_EXECUTION_QPS_LIMITER = RateLimiter.of(
    //         "start-execution-api-limiter",
    //         RateLimiterConfig.custom()
    //                 .limitForPeriod(10)
    //                 .limitRefreshPeriod(Duration.ofMillis(100)) // 每100ms补充1个令牌
    //                 .timeoutDuration(Duration.ZERO) // 无限等待
    //                 .build()
    // );
    private static final RateLimiter START_EXECUTION_QPS_LIMITER = RateLimiter.create(10);

    /**
     * 通知文件拉取的限流器
     */
    // public static final RateLimiter UPLOAD_MEDIA_BY_URL_QPS_LIMITER = RateLimiter.of(
    //         "upload-media-by-url-api-limiter",
    //         RateLimiterConfig.custom()
    //                 .limitForPeriod(4)
    //                 .limitRefreshPeriod(Duration.ofMillis(100)) // 每100ms补充1个令牌
    //                 .timeoutDuration(Duration.ZERO) // 无限等待
    //                 .build()
    // );
    private static final RateLimiter UPLOAD_MEDIA_BY_URL_QPS_LIMITER = RateLimiter.create(4);

    /**
     * 请求coze翻译的限流器
     */
    private static final Semaphore COZE_CONCURRENT_LIMITER = new Semaphore(10);

    /**
     * 视频压制的限流器
     */
    private static final Semaphore CMOPRESS_CONCURRENT_LIMITER = new Semaphore(Runtime.getRuntime().availableProcessors() + 1);

    public static void acquireStartExecution() {
        START_EXECUTION_QPS_LIMITER.acquire();
    }

    public static void acquireUploadMediaByUrl() {
        UPLOAD_MEDIA_BY_URL_QPS_LIMITER.acquire();
    }

    public static void acquireCoze() {
        try {
            COZE_CONCURRENT_LIMITER.acquire();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            throw new RuntimeException("获取coze令牌失败", e);
        }
    }

    public static void releaseCoze() {
        COZE_CONCURRENT_LIMITER.release();
    }

    public static void acquireCompress() {
        try {
            CMOPRESS_CONCURRENT_LIMITER.acquire();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            throw new RuntimeException("获取视频压制令牌失败", e);
        }
    }

    public static void releaseCompress() {
        CMOPRESS_CONCURRENT_LIMITER.release();
    }

}
