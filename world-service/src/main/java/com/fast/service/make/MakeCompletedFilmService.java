/*
 * Powered By fast.up
 */
package com.fast.service.make;

import com.fast.constant.StaticStr;
import com.fast.enums.*;
import com.fast.enums.video.VideoProcessStatusEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.make.MakeCompletedFilmMapper;
import com.fast.po.make.MakeCompletedFilmPO;
import com.fast.po.make.MakeFilmCaptionPO;
import com.fast.po.make.MakeFilmVideoPO;
import com.fast.po.make.MakeOriginalFilmPO;
import com.fast.po.make.video.MakeVideoBatchTaskPO;
import com.fast.po.make.video.MakeVideoSeriesTaskPO;
import com.fast.service.base.BaseService;
import com.fast.service.make.video.MakeVideoBatchTaskService;
import com.fast.service.make.video.MakeVideoSeriesTaskService;
import com.fast.service.make.video.VideoProcessFlowManager;
import com.fast.service.make.video.VideoProcessServiceV2;
import com.fast.service.make.video.util.FileDownloader;
import com.fast.service.make.video.util.MultiFileDownloader;
import com.fast.service.make.video.util.SrtProcessor;
import com.fast.service.oss.OssService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.file.FileUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class MakeCompletedFilmService extends BaseService {

    @Autowired
    private MakeCompletedFilmMapper makeCompletedFilmMapper;

    @Autowired
    private MakeOriginalFilmService makeOriginalFilmService;

    @Autowired
    private MakeFilmVideoService makeFilmVideoService;

    @Autowired
    private MakeFilmCaptionService makeFilmCaptionService;

    @Autowired
    private VideoProcessServiceV2 videoProcessServiceV2;

    @Autowired
    private VideoProcessFlowManager videoProcessFlowManager;

    @Autowired
    private MakeVideoBatchTaskService makeVideoBatchTaskService;

    @Autowired
    private MakeVideoSeriesTaskService makeVideoSeriesTaskService;

    @Autowired
    private OssService ossService;
    
    @Autowired
    private MultiFileDownloader multiFileDownloader;

    /**
     * 临时文件存储路径
     * 注意：实际部署时请修改为合适的路径
     */
    private static final String tmpPath = "/home/<USER>/";

    /**
     * 通过id查询单个对象
     */
    public MakeCompletedFilmPO queryById(MakeCompletedFilmPO params) {
        return makeCompletedFilmMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeCompletedFilmPO queryById(Integer id) {
        return makeCompletedFilmMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeCompletedFilmPO queryOne(MakeCompletedFilmPO params) {
        return makeCompletedFilmMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeCompletedFilmPO> queryList(MakeCompletedFilmPO params) {
        return makeCompletedFilmMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeCompletedFilmPO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeCompletedFilmPO> list = makeCompletedFilmMapper.queryList(params);
        for (MakeCompletedFilmPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(MakeCompletedFilmPO params) {
        return makeCompletedFilmMapper.queryCount(params);
    }

    public Integer queryOriginalFilmId(Integer id) {
        if (id == null) {
            return null;
        }
        MakeCompletedFilmPO film = queryById(id);
        return film == null ? null : film.getOriginalFilmId();
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeCompletedFilmPO params) {
        MakeOriginalFilmPO original = makeOriginalFilmService.queryById(params.getOriginalFilmId());
        if (original == null) {
            transactionRollBack();
            return MethodVO.error("原片不存在");
        }

        String destLangs = params.getDestLangs();
        String[] destLangArray = destLangs.split(",");

        params.setTotal(original.getTotal());

        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeCompletedFilmMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        // 找到原片对应的剧集视频，复制到成片中
        List<MakeFilmVideoPO> videos = makeFilmVideoService.queryByOriginalFilmId(params.getOriginalFilmId());
        if (CollUtil.isEmpty(videos)) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        // 在复制视频的过程中，需要将字幕数据也生成好
        for (MakeFilmVideoPO originalVideo : videos) {
            // 无字幕的视频
            addMakeFilmVideo("00", params, originalVideo);
            // 原始语言字幕
            addMakeFilmCaption(params.getSourceLang(), YesOrNoEnum.YES.getCode(), params, originalVideo);

            // 目标语言的视频和字幕
            for (String destLang : destLangArray) {
                // 视频
                addMakeFilmVideo(destLang, params, originalVideo);
                // 字幕
                addMakeFilmCaption(destLang, YesOrNoEnum.NO.getCode(), params, originalVideo);
            }
        }

        // 将自上传字幕写入到原片中，权宜之计，后续要改设计，这个入口不该放在这里
        syncOriginalFilmCaption(params);

        Integer taskId = videoProcessServiceV2.createTask(params);
        log.info("原片添加成功，id: {}，批次任务ID: {}", params.getId(), taskId);

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                videoProcessServiceV2.startTask(taskId);
            }
        });

        return MethodVO.success(params.getId());
    }

    private void syncOriginalFilmCaption(MakeCompletedFilmPO film) {
        if (CollUtil.isEmpty(film.getCaptions())) {
            return;
        }
        log.info("上传了自定义原片字幕文件，开始处理... batchTaskId: {}", film.getId());
        for (MakeFilmCaptionPO filmCaption : film.getCaptions()) {
            if (filmCaption.getSeriesNum() <= 0 || filmCaption.getSeriesNum() > film.getTotal()) {
                throw new MyException("本地上传字幕对应的剧集数不正确，请修改！！！");
            }
            // 入库之前，先确保字幕文件格式正确
            String captionUrl = fixFilmSubtitle(film, filmCaption);
            // 同一个原片可能会被重复使用，所以只保留一份最新的字幕文件
            MakeFilmCaptionPO caption = makeFilmCaptionService.queryByFilmIdAndTypeAndSeriesNumAndLang(film.getOriginalFilmId(), FilmTypeEnum.ORIGINAL.getCode(), filmCaption.getSeriesNum(), film.getSourceLang(), CaptionTypeEnum.OUR.getCode());
            if (caption != null) {
                caption.setUrl(captionUrl);
                // 字幕文件不一定都是srt，也可能是vtt
                if (StrUtil.isBlank(captionUrl)) {
                    caption.setName(LanguageEnum.ofCode(film.getSourceLang()).getName() + " - " + caption.getSeriesNum() + ".srt");
                } else {
                    caption.setName(LanguageEnum.ofCode(film.getSourceLang()).getName() + " - " + caption.getSeriesNum() + "." + StrUtil.substringAfterLast(captionUrl, "."));
                }
                makeFilmCaptionService.update(caption);
                log.info("原片 {} 第 {} 集的 {} 自上传字幕文件已存在，更新字幕文件: {}", film.getOriginalFilmId(), filmCaption.getSeriesNum(), film.getSourceLang(), captionUrl);
            } else {
                filmCaption.setFilmId(film.getOriginalFilmId());
                filmCaption.setFilmType(FilmTypeEnum.ORIGINAL.getCode());
                filmCaption.setLanguageCode(film.getSourceLang());
                filmCaption.setType(CaptionTypeEnum.OUR.getCode());
                filmCaption.setSourced(YesOrNoEnum.YES.getCode());
                filmCaption.setState(FilmStateEnum.FINISH.getCode());
                // 字幕文件不一定都是srt，也可能是vtt
                if (StrUtil.isBlank(captionUrl)) {
                    filmCaption.setName(LanguageEnum.ofCode(film.getSourceLang()).getName() + " - " + filmCaption.getSeriesNum() + ".srt");
                } else {
                    filmCaption.setName(LanguageEnum.ofCode(film.getSourceLang()).getName() + " - " + filmCaption.getSeriesNum() + "." + StrUtil.substringAfterLast(captionUrl, "."));
                }
                filmCaption.setUrl(captionUrl);
                makeFilmCaptionService.insert(filmCaption);
                log.info("原片 {} 第 {} 集的 {} 自上传字幕文件不存在，新增字幕文件: {}", film.getOriginalFilmId(), filmCaption.getSeriesNum(), film.getSourceLang(), captionUrl);
            }
        }
    }

    private String fixFilmSubtitle(MakeCompletedFilmPO film, MakeFilmCaptionPO filmCaption) {
        // 如果上传的字幕就有问题，那会导致后面的流程都有问题
        String captionUrl = filmCaption.getUrl();
        if (StrUtil.isBlank(captionUrl)) {
            return null;
        }
        // 将字幕文件下载到本地
        String srcFileName = StrUtil.getRandomFileName() + "." + StrUtil.substringAfterLast(captionUrl, ".");
        String srcFilePath = tmpPath + "srt/" + srcFileName;
        String srcPath = FileDownloader.downloadFile(captionUrl, srcFileName, srcFilePath);
        if (StrUtil.isBlank(srcPath)) {
            return captionUrl;
        }
        // 先走一遍修复，存到本地
        String fixedFilePath = tmpPath + "srt/fixed/" + srcFileName;
        boolean success = SrtProcessor.fixSrt(srcPath, fixedFilePath);
        if (!success) {
            return captionUrl;
        }
        // 将修复后的srt上传到oss
        String ossUrl = ossService.uploadFile(false, 0, 5, new File(fixedFilePath), film.getSourceLang() + "/");
        if (StrUtil.isBlank(ossUrl)) {
            return captionUrl;
        }
        // 删除临时文件
        FileUtil.delFile(srcPath);
        FileUtil.delFile(fixedFilePath);
        return ossUrl;
    }

    private void addMakeFilmVideo(String lang, MakeCompletedFilmPO film, MakeFilmVideoPO originalVideo) {
        MakeFilmVideoPO filmVideo = new MakeFilmVideoPO();
        filmVideo.setFilmId(film.getId());
        filmVideo.setFilmType(FilmTypeEnum.COMPLETED.getCode());
        filmVideo.setSeriesNum(originalVideo.getSeriesNum());
        filmVideo.setLanguageCode(lang);
        filmVideo.setOriginalVideoId(originalVideo.getId());
        filmVideo.setFid(originalVideo.getFid());
        if (lang.equals("00")) {
            filmVideo.setName("无字幕 - " + originalVideo.getSeriesNum() + ".mp4");
        } else {
            filmVideo.setName(LanguageEnum.ofCode(lang).getName() + " - " + originalVideo.getSeriesNum() + ".mp4");
        }
        filmVideo.setState(FilmStateEnum.PRODUCTING.getCode());
        makeFilmVideoService.insert(filmVideo);
    }

    private void addMakeFilmCaption(String lang, Integer sourced, MakeCompletedFilmPO film, MakeFilmVideoPO video) {
        MakeFilmCaptionPO filmCaption = new MakeFilmCaptionPO();
        filmCaption.setFilmId(film.getId());
        filmCaption.setFilmType(FilmTypeEnum.COMPLETED.getCode());
        filmCaption.setSeriesNum(video.getSeriesNum());
        filmCaption.setLanguageCode(lang);
        filmCaption.setSourced(sourced);
        filmCaption.setState(FilmStateEnum.PRODUCTING.getCode());
        // 查询原片是否有字幕，如果有的话直接用，就不用再去跑提取任务了
        // 优先查询自上传
        MakeFilmCaptionPO ourCaption = makeFilmCaptionService.queryByFilmIdAndTypeAndSeriesNumAndLang(film.getOriginalFilmId(), FilmTypeEnum.ORIGINAL.getCode(), video.getSeriesNum(), lang, CaptionTypeEnum.OUR.getCode());
        if (ourCaption != null) {
            filmCaption.setType(CaptionTypeEnum.OUR.getCode());
            filmCaption.setUrl(ourCaption.getUrl());
            filmCaption.setState(FilmStateEnum.FINISH.getCode());
        }
        // 其次coze
        MakeFilmCaptionPO cozeCaption = makeFilmCaptionService.queryByFilmIdAndTypeAndSeriesNumAndLang(film.getOriginalFilmId(), FilmTypeEnum.ORIGINAL.getCode(), video.getSeriesNum(), lang, CaptionTypeEnum.COZE.getCode());
        if (cozeCaption != null) {
            filmCaption.setType(CaptionTypeEnum.COZE.getCode());
            filmCaption.setUrl(cozeCaption.getUrl());
            filmCaption.setState(FilmStateEnum.FINISH.getCode());
        }
        // 再次ocr
        MakeFilmCaptionPO ocrCaption = makeFilmCaptionService.queryByFilmIdAndTypeAndSeriesNumAndLang(film.getOriginalFilmId(), FilmTypeEnum.ORIGINAL.getCode(), video.getSeriesNum(), lang, CaptionTypeEnum.OCR.getCode());
        if (ocrCaption != null) {
            filmCaption.setType(CaptionTypeEnum.OCR.getCode());
            filmCaption.setUrl(ocrCaption.getUrl());
            filmCaption.setState(FilmStateEnum.FINISH.getCode());
        }
        // 最后asr
        MakeFilmCaptionPO asrCaption = makeFilmCaptionService.queryByFilmIdAndTypeAndSeriesNumAndLang(film.getOriginalFilmId(), FilmTypeEnum.ORIGINAL.getCode(), video.getSeriesNum(), lang, CaptionTypeEnum.ASR.getCode());
        if (asrCaption != null) {
            filmCaption.setType(CaptionTypeEnum.ASR.getCode());
            filmCaption.setUrl(asrCaption.getUrl());
            filmCaption.setState(FilmStateEnum.FINISH.getCode());
        }
        // 字幕文件不一定都是srt，也可能是vtt
        if (StrUtil.isBlank(filmCaption.getUrl())) {
            filmCaption.setName(LanguageEnum.ofCode(lang).getName() + " - " + video.getSeriesNum() + ".srt");
        } else {
            filmCaption.setName(LanguageEnum.ofCode(lang).getName() + " - " + video.getSeriesNum() + "." + StrUtil.substringAfterLast(filmCaption.getUrl(), "."));
        }
        makeFilmCaptionService.insert(filmCaption);
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeCompletedFilmPO> list) {
        if (makeCompletedFilmMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeCompletedFilmPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeCompletedFilmMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeCompletedFilmPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeCompletedFilmPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        makeCompletedFilmMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public MethodVO download(MakeCompletedFilmPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.INVALID_PARAM);
        }
        MakeCompletedFilmPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        // 下载视频（1、是）
        Integer downVideo = params.getDownVideo();
        List<String> videoLang = params.getVideoLang();
        Integer seriesAll = params.getSeriesAll();
        Integer seriesStart = params.getSeriesStart();
        Integer seriesEnd = params.getSeriesEnd();
        
        // 下载字幕（1、是）
        Integer downCaption = params.getDownCaption();
        List<String> captionLang = params.getCaptionLang();
        
        // 如果选择了下载视频，就取videoLang的字段看一下它需要哪几种语言的视频
        // 在选择了语言之后，还需要判断一下是下载全集，还是下载指定集数
        // 影片对应的视频表：make_film_video
        
        // 如果选择了下载字幕，就取captionLang的字段看一下它需要哪几种语言的字幕
        // 影片对应的视频表：make_film_caption
        
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO retry(MakeCompletedFilmPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeCompletedFilmPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        MakeVideoBatchTaskPO batchTask = makeVideoBatchTaskService.queryByFilmId(params.getId());
        if (batchTask == null) {
            return MethodVO.error("batch task not exist");
        }
        if (Objects.equals(batchTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
            return MethodVO.error("batch task has finished.");
        }
        List<MakeVideoSeriesTaskPO> seriesTasks = makeVideoSeriesTaskService.queryByBatchId(batchTask.getId());
        if (CollUtil.isEmpty(seriesTasks)) {
            return MethodVO.error("series task not exist");
        }
        for (MakeVideoSeriesTaskPO seriesTask : seriesTasks) {
            if (!Objects.equals(batchTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
                videoProcessFlowManager.startProcess(batchTask, seriesTask);
            }
            // 计数
            // seriesTask.setRetryCount(seriesTask.getRetryCount() + 1);
            // makeVideoSeriesTaskService.update(seriesTask);
        }
        return MethodVO.success();
    }

}
