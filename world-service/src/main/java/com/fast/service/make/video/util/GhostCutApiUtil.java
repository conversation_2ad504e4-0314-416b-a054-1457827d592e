package com.fast.service.make.video.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fast.utils.ComUtil;
import com.fast.utils.encryption.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 鬼手API服务
 * <p>
 * 整合了字幕提取、擦除、压制三个API的调用
 * 使用虚拟线程中的同步HTTP调用
 */
@Service
@Slf4j
public class GhostCutApiUtil {

    @Autowired
    private HttpClient httpClient;

    private static final String APP_KEY = "80510be07cf04aaf8ac0be5d751c8041";
    private static final String APP_SECRET = "d9ae8b11b1aa4004887838d760215668";
    private static final String API_DOMAIN = "https://api.zhaoli.com/v-w-c/gateway/ve";

    /**
     * 提交字幕提取任务（支持批量）
     */
    public JSONObject subtitleExtract(List<String> urls, List<String> names, String callbackUrl, String region) {
        log.info("提交字幕提取任务: urls={}, callbackUrl={}, region={}", urls, callbackUrl, region);

        JSONObject params = new JSONObject();
        params.put("urls", urls);
        params.put("names", names);
        params.put("videoInpaintLang", "zh");
        params.put("lang", "zh");
        params.put("needChineseOcclude", 14);
        if (callbackUrl != null) {
            params.put("callback", callbackUrl);
        }

        // 设置字幕区域
        JSONObject mask = new JSONObject();
        mask.put("type", "trans_only_ocr");
        mask.put("start", 0);
        mask.put("end", 99999);
        mask.put("region", ComUtil.transferRegion(region));
        params.put("videoInpaintMasks", List.of(mask));

        return sendApiRequest("/work/free", params);
    }

    /**
     * 提交字幕擦除任务（支持批量）
     */
    public JSONObject subtitleRemove(List<String> videoUrls, List<String> names, String callbackUrl, String region) {
        log.info("提交字幕擦除任务: videoUrls={}, callbackUrl={}, region={}", videoUrls, callbackUrl, region);

        JSONObject params = new JSONObject();
        params.put("urls", videoUrls);
        params.put("names", names);
        params.put("needChineseOcclude", 2);
        params.put("videoInpaintLang", "all");
        if (callbackUrl != null) {
            params.put("callback", callbackUrl);
        }

        // 设置字幕区域
        JSONObject mask = new JSONObject();
        mask.put("type", "remove_only_ocr");
        mask.put("start", 0);
        mask.put("end", 99999);
        mask.put("region", ComUtil.transferRegion(region));
        params.put("videoInpaintMasks", List.of(mask));

        return sendApiRequest("/work/free", params);
    }

    /**
     * 提交视频压制任务
     */
    public JSONObject videoCompress(List<String> videoUrls, List<String> names, String sourceSrtUrl, String targetSrtUrl,
                                    String callbackUrl, Integer fontSize, Double position, Integer removeBgAudio) {
        log.info("提交视频压制任务: videoUrls={}, sourceSrtUrl={}, targetSrtUrl={}, callbackUrl={},", videoUrls, sourceSrtUrl, targetSrtUrl, callbackUrl);

        JSONObject params = new JSONObject();
        params.put("urls", videoUrls);
        params.put("names", names);
        params.put("sourceLang", "zh");
        params.put("lang", "en");
        params.put("needWanyin", 1);
        params.put("wyTaskType", "NO_TTS");
        params.put("wyNeedText", 1);
        params.put("removeBgAudio", removeBgAudio == null ? 1 : removeBgAudio);
        if (callbackUrl != null) {
            params.put("callback", callbackUrl);
        }

        // 字体参数
        JSONObject fontParam = new JSONObject();
        fontParam.put("style", "n-1-T");
        fontParam.put("font_size", fontSize == null ? 50 : fontSize);
        fontParam.put("position", position == null ? 0.8 : position);
        params.put("wyVoiceParam", Map.of("font_param", fontParam));

        // 字幕文件
        JSONObject srtFiles = new JSONObject();
        srtFiles.put("source", sourceSrtUrl);
        srtFiles.put("translation", targetSrtUrl);
        params.put("extraOptions", Map.of("customer_input_srt", srtFiles));

        return sendApiRequest("/work/free", params);
    }

    /**
     * 查询任务状态
     */
    public JSONObject queryTaskStatus(String taskId) {
        log.info("查询任务状态: taskId={}", taskId);

        JSONObject params = new JSONObject();
        params.put("idWorks", List.of(Integer.parseInt(taskId)));

        return sendApiRequest("/work/status", params);
    }

    /**
     * 发送API请求
     */
    private JSONObject sendApiRequest(String endpoint, JSONObject params) {
        try {
            String body = params.toJSONString();
            String sign = buildSign(body);
            String url = API_DOMAIN + endpoint;

            log.info("[ url]: {}", url);
            log.info("[sign]: {}", sign);
            log.info("[body]: {}", body);

            long start = System.currentTimeMillis();

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Content-Type", "application/json")
                    .header("AppKey", APP_KEY)
                    .header("AppSign", sign)
                    .timeout(Duration.ofMinutes(5))
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            String res = response.body();

            log.info("[ res]: {}", res);

            long end = System.currentTimeMillis();
            log.info("[time]: {}s", (end - start) / 1000f);

            return JSON.parseObject(res);
        } catch (Exception e) {
            log.error("鬼手API请求失败: endpoint={}", endpoint, e);
            return null;
        }
    }

    /**
     * 构建签名
     */
    private String buildSign(String body) {
        try {
            String bodyMd5 = Md5Util.getMD5(body);
            return Md5Util.getMD5(bodyMd5 + APP_SECRET);
        } catch (Exception e) {
            throw new RuntimeException("构建签名失败", e);
        }
    }

    /**
     * 提取任务ID
     */
    private Integer extractTaskId(JSONObject response) {
        try {
            JSONObject body = response.getJSONObject("body");
            if (body != null && body.get("idProject") != null) {
                return body.getInteger("idProject");
            }
        } catch (Exception e) {
            log.warn("提取任务ID失败", e);
        }
        return null;
    }

    /**
     * 提取子任务ID
     */
    private List<Integer> extractSubTaskId(JSONObject response) {
        try {
            JSONObject body = response.getJSONObject("body");
            if (body != null && body.get("dataList") != null) {
                return body.getJSONArray("dataList")
                        .stream()
                        .map(o -> ((JSONObject) o).getInteger("id"))
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("提取子任务ID失败", e);
        }
        return null;
    }

}
