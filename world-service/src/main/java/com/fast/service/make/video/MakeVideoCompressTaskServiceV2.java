/*
 * Powered By fast.up
 */
package com.fast.service.make.video;

import com.fast.constant.StaticStr;
import com.fast.enums.FilmTypeEnum;
import com.fast.enums.YesOrNoEnum;
import com.fast.enums.video.VideoProcessStatusEnum;
import com.fast.framework.exception.MyException;
import com.fast.framework.thread.mdc.MdcThreadPoolExecutor;
import com.fast.mapper.make.video.MakeVideoCompressTaskMapper;
import com.fast.po.make.video.*;
import com.fast.service.base.BaseService;
import com.fast.service.make.MakeCompletedFilmService;
import com.fast.service.make.MakeFilmVideoService;
import com.fast.service.make.video.util.FfmpegExecutor;
import com.fast.service.make.video.util.FileDownloader;
import com.fast.service.make.video.util.ThirdPartyApiRateLimiter;
import com.fast.service.oss.OssService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.LockUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.file.FileUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MakeVideoCompressTaskServiceV2 extends BaseService {

    @Autowired
    private MakeVideoCompressTaskMapper makeVideoCompressTaskMapper;

    @Autowired
    private MakeSubtitleExtractTaskServiceV2 makeSubtitleExtractTaskServiceV2;

    @Autowired
    private MakeSubtitleTranslateTaskService makeSubtitleTranslateTaskService;

    @Autowired
    private MakeSubtitleRemoveTaskServiceV2 makeSubtitleRemoveTaskServiceV2;

    @Autowired
    private OssService ossService;

    @Autowired
    private LockUtil lockUtil;

    @Autowired
    private MakeVideoSeriesTaskService makeVideoSeriesTaskService;

    @Autowired
    private MakeVideoBatchTaskService makeVideoBatchTaskService;

    @Autowired
    private MakeFilmVideoService makeFilmVideoService;

    @Autowired
    private MakeCompletedFilmService makeCompletedFilmService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    // @Autowired
    // private VThreadManager vThreadManager;

    private static final ThreadPoolExecutor THREAD_POOL_EXECUTOR = new MdcThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() + 1,
            Runtime.getRuntime().availableProcessors() + 1,
            0,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1000),
            new ThreadFactoryBuilder().setNameFormat("ffmpeg-execute-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 临时文件存储路径
     * 注意：实际部署时请修改为合适的路径
     */
    private static final String tmpPath = "/home/<USER>/";

    /**
     * 通过id查询单个对象
     */
    public MakeVideoCompressTaskPO queryById(MakeVideoCompressTaskPO params) {
        return makeVideoCompressTaskMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeVideoCompressTaskPO queryById(Integer id) {
        return makeVideoCompressTaskMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeVideoCompressTaskPO queryOne(MakeVideoCompressTaskPO params) {
        return makeVideoCompressTaskMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeVideoCompressTaskPO> queryList(MakeVideoCompressTaskPO params) {
        return makeVideoCompressTaskMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeVideoCompressTaskPO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeVideoCompressTaskPO> list = makeVideoCompressTaskMapper.queryList(params);
        for (MakeVideoCompressTaskPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(MakeVideoCompressTaskPO params) {
        Integer count = makeVideoCompressTaskMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    public Integer countByBatchTaskId(Integer batchTaskId, VideoProcessStatusEnum statusEnum) {
        if (batchTaskId == null || statusEnum == null) {
            return 0;
        }
        MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
        query.setBatchId(batchTaskId);
        query.setStatus(statusEnum.getCode());
        Integer count = queryCount(query);
        return count == null ? 0 : count;
    }

    public Integer countBySeriesTaskId(Integer seriesTaskId, VideoProcessStatusEnum statusEnum) {
        if (seriesTaskId == null || statusEnum == null) {
            return 0;
        }
        MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        query.setStatus(statusEnum.getCode());
        Integer count = queryCount(query);
        return count == null ? 0 : count;
    }

    public List<MakeVideoCompressTaskPO> queryByBatchTaskId(Integer batchTaskId) {
        if (batchTaskId == null) {
            return null;
        }
        MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
        query.setBatchId(batchTaskId);
        return queryList(query);
    }

    public MakeVideoCompressTaskPO queryByThirdPartyId(String batchId, String taskId) {
        if (StrUtil.isBlank(batchId) || StrUtil.isBlank(taskId)) {
            return null;
        }
        MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
        query.setThirdPartyBatchId(batchId);
        query.setThirdPartyTaskId(taskId);
        return queryOne(query);
    }

    public List<MakeVideoCompressTaskPO> queryBySeriesTaskId(Integer seriesTaskId) {
        if (seriesTaskId == null) {
            return null;
        }
        MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        return queryList(query);
    }

    public boolean isAllCompleted(Integer seriesTaskId) {
        if (seriesTaskId == null) {
            return false;
        }
        List<MakeVideoCompressTaskPO> list = queryBySeriesTaskId(seriesTaskId);
        if (CollUtil.isEmpty(list)) {
            return false;
        }
        return list.stream().allMatch(item -> Objects.equals(item.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode()));
    }

    public boolean checkAndMarkCompressCompleted(Integer seriesTaskId) {
        if (seriesTaskId == null) {
            return false;
        }
        boolean allCompressTaskCompleted = isAllCompleted(seriesTaskId);
        if (allCompressTaskCompleted) {
            makeVideoSeriesTaskService.markCompressCompleted(seriesTaskId);
            return true;
        }
        return false;
    }

    public Integer countBySeriesTaskId(Integer seriesTaskId, VideoProcessStatusEnum statusEnum, String destLang) {
        if (seriesTaskId == null || statusEnum == null) {
            return 0;
        }
        MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        query.setStatus(statusEnum.getCode());
        query.setLanguageCode(destLang);
        Integer count = queryCount(query);
        return count == null ? 0 : count;
    }

    public MakeVideoCompressTaskPO queryBySeriesTaskIdAndLang(Integer seriesTaskId, String destLang) {
        if (seriesTaskId == null || StrUtil.isBlank(destLang)) {
            return null;
        }
        MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        query.setLanguageCode(destLang);
        return queryOne(query);
    }

    public List<MakeVideoCompressTaskPO> queryByFilmIdAndSeriesNum(Integer filmId, Integer seriesNum) {
        if (filmId == null || seriesNum == null) {
            return null;
        }
        MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
        query.setFilmId(filmId);
        query.setSeriesNum(seriesNum);
        return queryList(query);
    }

    public MakeVideoCompressTaskPO queryByFilmIdAndSeriesNumAndLang(Integer filmId, Integer seriesNum, String destLang) {
        if (filmId == null || seriesNum == null || StrUtil.isBlank(destLang)) {
            return null;
        }
        MakeVideoCompressTaskPO query = new MakeVideoCompressTaskPO();
        query.setFilmId(filmId);
        query.setSeriesNum(seriesNum);
        query.setLanguageCode(destLang);
        return queryOne(query);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeVideoCompressTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeVideoCompressTaskMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeVideoCompressTaskPO> list) {
        if (makeVideoCompressTaskMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeVideoCompressTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeVideoCompressTaskMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeVideoCompressTaskPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeVideoCompressTaskPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        makeVideoCompressTaskMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public int updateStatus(Integer id, Integer status) {
        if (id == null || status == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoCompressTaskPO taskPO = new MakeVideoCompressTaskPO();
        taskPO.setId(id);
        taskPO.setStatus(status);
        return makeVideoCompressTaskMapper.updateById(taskPO);
    }

    public int updateStatus(Integer id, VideoProcessStatusEnum statusEnum) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        return updateStatus(id, statusEnum.getCode());
    }

    public int updateStatusAndStartTime(Integer id, VideoProcessStatusEnum statusEnum, String videoUrl, String srtUrl, Date startTime) {
        if (id == null || statusEnum == null || StrUtil.isBlank(videoUrl) || StrUtil.isBlank(srtUrl)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoCompressTaskPO taskPO = new MakeVideoCompressTaskPO();
        taskPO.setId(id);
        taskPO.setStatus(statusEnum.getCode());
        taskPO.setCleanVideoUrl(videoUrl);
        taskPO.setSubtitleUrl(srtUrl);
        taskPO.setStartTime(startTime != null ? startTime : DateUtil.getNowDate());
        return makeVideoCompressTaskMapper.updateById(taskPO);
    }

    public int updateSuccess(Integer id, Date startTime, Date endTime, String url) {
        if (id == null || startTime == null || endTime == null || StrUtil.isBlank(url)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoCompressTaskPO compressTask = new MakeVideoCompressTaskPO();
        compressTask.setId(id);
        compressTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        compressTask.setEndTime(endTime);
        compressTask.setTakeTime(DateUtil.getDiffTime(startTime, endTime));
        compressTask.setUrl(url);
        return makeVideoCompressTaskMapper.updateById(compressTask);
    }

    public int updateFail(Integer id, Date startTime, Date endTime, String msg) {
        if (id == null || startTime == null || endTime == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoCompressTaskPO compressTask = new MakeVideoCompressTaskPO();
        compressTask.setId(id);
        compressTask.setStatus(VideoProcessStatusEnum.FAILED.getCode());
        compressTask.setEndTime(endTime);
        compressTask.setTakeTime(DateUtil.getDiffTime(startTime, endTime));
        compressTask.setErrorMessage(msg);
        return makeVideoCompressTaskMapper.updateById(compressTask);
    }

    /**
     * 执行视频压制业务逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeCompress(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {

        String destLangs = batchTask.getDestLangs();

        // 为每种目标语言创建压制任务
        for (String lang : destLangs.split(",")) {
            log.info("开始视频压制: batchTaskId={}, seriesTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), lang);

            // 压制是最后一步，该步骤有多个节点能触发
            String lockKey = "make_video_compress_task_lock_" + seriesTask.getId() + "_" + lang;
            RLock lock = lockUtil.getLock(lockKey);

            // 获取锁，防止并发执行同一剧集任务的压制
            try {
                if (!lockUtil.tryLock(lock, 1, TimeUnit.SECONDS)) {
                    log.warn("剧集任务 {} 的 {} 视频压制任务获取并发锁失败，说明已有进行中的任务, batchTaskId={}", seriesTask.getId(), lang, batchTask.getId());
                    continue;
                }

                // 获取字幕提取任务
                MakeSubtitleExtractTaskPO extractTask = makeSubtitleExtractTaskServiceV2.queryBySeriesTaskId(seriesTask.getId());
                if (extractTask == null || !Objects.equals(extractTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
                    log.warn("剧集任务 {} 的字幕提取任务未完成，跳过压制, batchTaskId={}", seriesTask.getId(), batchTask.getId());
                    return;
                }

                // 获取字幕擦除任务
                MakeSubtitleRemoveTaskPO removeTask = makeSubtitleRemoveTaskServiceV2.queryBySeriesTaskId(seriesTask.getId());
                if (removeTask == null || !Objects.equals(removeTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
                    log.warn("剧集任务 {} 的字幕擦除任务未完成，跳过压制, batchTaskId={}", seriesTask.getId(), batchTask.getId());
                    return;
                }

                // 获取字幕翻译任务
                MakeSubtitleTranslateTaskPO translateTask = makeSubtitleTranslateTaskService.queryBySeriesTaskIdAndLang(seriesTask.getId(), lang);
                if (translateTask == null || !Objects.equals(translateTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
                    log.warn("剧集任务 {} 的 {} 翻译任务未完成，跳过压制, batchTaskId={}", seriesTask.getId(), lang, batchTask.getId());
                    continue;
                }

                MakeVideoCompressTaskPO compressTask = queryBySeriesTaskIdAndLang(seriesTask.getId(), lang);
                if (compressTask == null) {
                    log.warn("视频压制任务不存在，无法进行后续压制，任务前置环节异常，请检查！！！ batchTaskId={}, seriesTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), lang);
                    continue;
                }

                // 若是首次，则更改状态为处理中
                if (Objects.equals(compressTask.getStatus(), VideoProcessStatusEnum.PENDING.getCode())) {
                    // 取个巧
                    compressTask.setStartTime(DateUtil.getNowDate());
                    updateStatusAndStartTime(compressTask.getId(), VideoProcessStatusEnum.PROCESSING, removeTask.getUrl(), extractTask.getUrl(), DateUtil.getNowDate());
                    log.info("更新视频压制任务状态为处理中: batchTaskId={}, seriesTaskId={}, compressTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), compressTask.getId(), lang);
                }
                if (Objects.equals(compressTask.getStatus(), VideoProcessStatusEnum.PROCESSING.getCode())) {
                    log.warn("视频压制任务正在处理中，直接跳过本次压制，batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), compressTask.getId(), lang);
                    continue;
                }
                if (Objects.equals(compressTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
                    log.info("剧集任务 {} 的视频压制任务 {} 已经完成，跳过本次压制, batchTaskId={}, destLang={}", seriesTask.getId(), compressTask.getId(), batchTask.getId(), lang);
                    checkAndMarkCompressCompleted(seriesTask.getId());
                    continue;
                }

                // 执行压制命令
                THREAD_POOL_EXECUTOR.execute(() -> executeCmd(batchTask, seriesTask, extractTask, removeTask, translateTask, compressTask, lang));
            } catch (Exception e) {
                log.error("剧集任务 {} 的 {} 视频压制任务获取并发锁异常: {}", seriesTask.getId(), lang, ExceptionUtils.getStackTrace(e));
            } finally {
                lockUtil.unlock(lock);
            }
        }
    }

    /**
     * 执行ffmpeg指令进行视频压制，非常耗时
     */
    public void executeCmd(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask,
                           MakeSubtitleExtractTaskPO extractTask,
                           MakeSubtitleRemoveTaskPO removeTask,
                           MakeSubtitleTranslateTaskPO translateTask,
                           MakeVideoCompressTaskPO compressTask,
                           String lang) {
        log.info("执行ffmpeg视频压制命令: batchTaskId={}, seriesTaskId={}, extractTaskId={}, removeTaskId={}, translateTaskId={}, compressTaskId={}, destLang={}",
                batchTask.getId(), seriesTask.getId(), extractTask.getId(), removeTask.getId(), translateTask.getId(), compressTask.getId(), lang);

        // 获取许可证
        ThirdPartyApiRateLimiter.acquireCompress();
        log.info("ConCurrent许可证获取成功，开始执行ffmpeg视频压制命令, batchTaskId={}, seriesTaskId={}, compressTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), compressTask.getId(), lang);

        try {
            String srtFileName = String.format("Subtitle_%d_%d_%s." + StrUtil.substringAfterLast(translateTask.getUrl(), "."), seriesTask.getId(), seriesTask.getSeriesNum(), lang);
            String srtFilePath = tmpPath + "srt/" + srtFileName;
            if (!FileUtil.checkFileExists(srtFilePath)) {
                log.warn("字幕文件 {} 不存在，无法进行视频压制，尝试下载文件, batchTaskId={}, seriesTaskId={}, destLang={}", srtFilePath, batchTask.getId(), seriesTask.getId(), lang);
                String path = FileDownloader.downloadFile(translateTask.getUrl(), srtFileName, srtFilePath);
                if (StrUtil.isBlank(path)) {
                    log.error("下载文件失败: fileUrl={}, batchTaskId={}, seriesTaskId={}, destLang={}", translateTask.getUrl(), batchTask.getId(), seriesTask.getId(), lang);
                    return;
                }
                log.info("尝试下载字幕文件成功，字幕文件路径: {}, batchTaskId={}, seriesTaskId={}, destLang={}", srtFilePath, batchTask.getId(), seriesTask.getId(), lang);
                if (!FileUtil.checkFileExists(srtFilePath)) {
                    compressFail(batchTask, seriesTask, compressTask, "未找到字幕文件", DateUtil.getNowDate());
                    return;
                }
            }

            String videoFileName = String.format("Video_%d_%d_%s.mp4", seriesTask.getId(), seriesTask.getSeriesNum(), "00");
            String videoFilePath = tmpPath + "video/" + videoFileName;
            if (!FileUtil.checkFileExists(videoFilePath)) {
                log.warn("视频文件 {} 不存在，无法进行视频压制，尝试下载文件, batchTaskId={}, seriesTaskId={}, destLang={}", videoFilePath, batchTask.getId(), seriesTask.getId(), lang);
                String path = FileDownloader.downloadFile(removeTask.getUrl(), videoFileName, videoFilePath);
                if (StrUtil.isBlank(path)) {
                    log.error("下载文件失败: fileUrl={}, batchTaskId={}, seriesTaskId={}, destLang={}", removeTask.getUrl(), batchTask.getId(), seriesTask.getId(), lang);
                    return;
                }
                log.info("尝试下载视频文件成功，视频文件路径: {}, batchTaskId={}, seriesTaskId={}, destLang={}", videoFilePath, batchTask.getId(), seriesTask.getId(), lang);
                if (!FileUtil.checkFileExists(videoFilePath)) {
                    compressFail(batchTask, seriesTask, compressTask, "未找到字幕擦除后的视频文件", DateUtil.getNowDate());
                    return;
                }
            }

            String compressFileName = String.format("Video_%d_%d_%s.mp4", seriesTask.getId(), seriesTask.getSeriesNum(), lang);
            String compressFilePath = tmpPath + "compress/" + compressFileName;
            // 如果已经有了就不重复压制，短期内磁盘文件不会删除
            if (!FileUtil.checkFileExists(compressFilePath)) {
                long start = System.currentTimeMillis();

                boolean success = FfmpegExecutor.executeFfmpegCommand(
                        tmpPath + "video/" + videoFileName,
                        tmpPath + "compress/" + compressFileName,
                        tmpPath + "srt/" + srtFileName,
                        75);

                long end = System.currentTimeMillis();
                log.info("视频压制任务 {} 的ffmpeg命令执行完毕，耗时: {}s, batchTaskId={}, seriesTaskId={}, destLang={}", compressTask.getId(), (end - start) / 1000f, batchTask.getId(), seriesTask.getId(), lang);

                if (!success) {
                    log.error("视频压制任务 {} 执行ffmpeg命令失败，batchTaskId={}, seriesTaskId={}, destLang={}", compressTask.getId(), batchTask.getId(), seriesTask.getId(), lang);
                    compressFail(batchTask, seriesTask, compressTask, "ffmpeg命令执行失败", DateUtil.getNowDate());
                    return;
                }
            }

            // 同步执行完毕之后，应该会有文件生成
            if (!FileUtil.checkFileExists(compressFilePath)) {
                log.warn("压制视频文件 {} 失败，压制目录下未找到生成的文件, batchTaskId={}, seriesTaskId={}, destLang={}", compressFilePath, batchTask.getId(), seriesTask.getId(), lang);
                compressFail(batchTask, seriesTask, compressTask, "压制目录下未找到生成的文件，可能写入磁盘失败", DateUtil.getNowDate());
                return;
            }

            // 上传到OSS
            String ossUrl = ossService.uploadFile(false, 0, 3, new File(compressFilePath), lang + "/");
            if (StrUtil.isBlank(ossUrl)) {
                log.warn("视频压制任务 {} 上传到OSS失败，batchTaskId={}, seriesTaskId={}, destLang={}", compressTask.getId(), batchTask.getId(), seriesTask.getId(), lang);
                compressFail(batchTask, seriesTask, compressTask, "OSS上传失败", DateUtil.getNowDate());
                return;
            }
            log.info("视频压制任务 {} 处理完毕，上传到OSS成功，本地磁盘路径: {}, ossUrl: {}, batchTaskId={}, seriesTaskId={}, destLang={}", compressTask.getId(), compressFilePath, ossUrl, batchTask.getId(), seriesTask.getId(), lang);

            compressSuccess(batchTask, seriesTask, compressTask, lang, ossUrl);
        } catch (Exception e) {
            log.error("ffmpeg视频压制失败: {}", ExceptionUtils.getStackTrace(e));
            compressFail(batchTask, seriesTask, compressTask, e.getMessage(), DateUtil.getNowDate());
        } finally {
            // 释放许可证
            ThirdPartyApiRateLimiter.releaseCompress();
            log.info("压制任务执行完毕，释放ConCurrent许可证: batchTaskId={}, seriesTaskId={}, compressTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), compressTask.getId(), lang);
        }
    }

    /**
     * 视频压制成功，ffmpeg命令需要同步执行，导致事务超时，触发大事务问题，这里使用编程式事务
     *
     * @param batchTask    批量任务
     * @param seriesTask   剧集子任务
     * @param compressTask 压制任务
     * @param lang         目标语言
     * @param ossUrl       压制后视频的OSS地址
     */
    public void compressSuccess(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, MakeVideoCompressTaskPO compressTask, String lang, String ossUrl) {
        transactionTemplate.executeWithoutResult((status) -> {
            updateSuccess(compressTask.getId(), compressTask.getStartTime(), DateUtil.getNowDate(), ossUrl);
            log.info("视频压制任务执行成功, batchTaskId={}, seriesTaskId={}, compressTaskId={}, destLang={}， 更改状态为 已完成", batchTask.getId(), seriesTask.getId(), compressTask.getId(), lang);

            // 同步补齐成片视频数据
            makeFilmVideoService.fillUrlByFilmAndSeriesNumAndLang(batchTask.getFilmId(), FilmTypeEnum.COMPLETED.getCode(), seriesTask.getSeriesNum(), lang, ossUrl);
            log.info("视频压制任务 {} 处理完毕，同步补齐成片 {} 源语言的视频数据，batchTaskId={}, seriesTaskId={}, destLang={}", compressTask.getId(), batchTask.getFilmId(), batchTask.getId(), seriesTask.getId(), lang);

            // 同步补齐原片视频数据
            Integer originalFilmId = makeCompletedFilmService.queryOriginalFilmId(batchTask.getFilmId());
            if (originalFilmId == null) {
                log.warn("未找到原片ID，无法同步补齐原片视频数据，batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());
            } else {
                makeFilmVideoService.fillCleanUrlByFilmAndSeriesNum(originalFilmId, FilmTypeEnum.ORIGINAL.getCode(), seriesTask.getSeriesNum(), ossUrl);
                log.info("视频压制任务 {} 处理完毕，同步补齐原片 {} 源语言的视频数据，batchTaskId={}, seriesTaskId={}", compressTask.getId(), originalFilmId, batchTask.getId(), seriesTask.getId());
            }
            
            // 同步更新成片完成数量
            makeVideoBatchTaskService.syncCompletedCountAndEndTime(batchTask.getId(), DateUtil.getNowDate());
            log.info("视频压制任务 {} 处理完毕，处理完成总数量+1，batchTaskId={}, seriesTaskId={}, destLang={}", compressTask.getId(), batchTask.getId(), seriesTask.getId(), lang);

            // 统计剧集子任务下的压制任务完成情况
            boolean completed = checkAndMarkCompressCompleted(seriesTask.getId());
            if (completed) {
                log.info("剧集子任务 {} 下的所有语言视频压制完毕，更新剧集子任务 {} 的视频压制标记为 true，batchTaskId={}, destLang={}", seriesTask.getId(), seriesTask.getId(), batchTask.getId(), lang);
                // 这里的标记先打上，后续step需要用到，可以避免频繁查询
                seriesTask.setCompressCompleted(YesOrNoEnum.YES.getCode());
            }

            // 统计批量任务下的剧集子任务完成情况
            boolean allSeriesTaskCompleted = makeVideoSeriesTaskService.isAllCompleted(batchTask.getId());
            if (allSeriesTaskCompleted) {
                makeVideoBatchTaskService.updateStatusAndEndTime(batchTask.getId(), VideoProcessStatusEnum.COMPLETED, DateUtil.getNowDate());
                log.info("批量任务 {} 所有剧集子任务完成，意味着整个流程终结，更新批量任务状态为 已完成, destLang={}", batchTask.getId(), lang);
            }
        });
    }

    /**
     * 视频压制失败，ffmpeg命令需要同步执行，导致事务超时，触发大事务问题，这里使用编程式事务
     *
     * @param batchTask    批量任务
     * @param seriesTask   剧集子任务
     * @param compressTask 压制任务
     * @param msg          错误原因
     * @param endTime      任务结束时间
     */
    public void compressFail(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, MakeVideoCompressTaskPO compressTask, String msg, Date endTime) {
        transactionTemplate.executeWithoutResult((status) -> {
            updateFail(compressTask.getId(), compressTask.getStartTime(), endTime, msg);
            log.warn("更新视频压制任务 {} 的状态为 失败，并录入失败原因", compressTask.getId());

            // 更新剧集子任务状态为失败
            makeVideoSeriesTaskService.updateStatusAndEndTime(seriesTask.getId(), VideoProcessStatusEnum.FAILED, endTime);
            log.warn("更新剧集子任务 {} 的状态为 失败", seriesTask.getId());

            // 更新失败数量
            makeVideoBatchTaskService.syncFailedCount(batchTask.getId());
            log.warn("增加批量任务 {} 的处理失败数量+1", batchTask.getId());
        });
    }

}
