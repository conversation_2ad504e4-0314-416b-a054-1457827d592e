/*
 * Powered By fast.up
 */
package com.fast.service.make.video;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticStr;
import com.fast.enums.FilmTypeEnum;
import com.fast.enums.YesOrNoEnum;
import com.fast.enums.video.VideoProcessStatusEnum;
import com.fast.enums.video.VideoProcessStepEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.make.video.MakeSubtitleRemoveTaskMapper;
import com.fast.po.make.MakeFilePO;
import com.fast.po.make.video.MakeSubtitleRemoveTaskPO;
import com.fast.po.make.video.MakeVideoBatchTaskPO;
import com.fast.po.make.video.MakeVideoSeriesTaskPO;
import com.fast.service.base.BaseService;
import com.fast.service.make.MakeCompletedFilmService;
import com.fast.service.make.MakeFileService;
import com.fast.service.make.MakeFilmVideoService;
import com.fast.service.make.video.util.ThirdPartyApiRateLimiter;
import com.fast.service.make.video.util.VolcengineApiUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MakeSubtitleRemoveTaskServiceV2 extends BaseService {

    @Autowired
    private MakeSubtitleRemoveTaskMapper makeSubtitleRemoveTaskMapper;

    @Autowired
    private MakeFileService makeFileService;

    @Autowired
    private VolcengineApiUtil volcengineApiUtil;

    @Autowired
    private MakeVideoBatchTaskService makeVideoBatchTaskService;

    @Autowired
    private MakeVideoSeriesTaskService makeVideoSeriesTaskService;

    @Autowired
    private MakeFilmVideoService makeFilmVideoService;

    @Autowired
    private VideoProcessFlowManager videoProcessFlowManager;

    @Autowired
    private MakeCompletedFilmService makeCompletedFilmService;

    /**
     * 通过id查询单个对象
     */
    public MakeSubtitleRemoveTaskPO queryById(MakeSubtitleRemoveTaskPO params) {
        return makeSubtitleRemoveTaskMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeSubtitleRemoveTaskPO queryById(Integer id) {
        return makeSubtitleRemoveTaskMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeSubtitleRemoveTaskPO queryOne(MakeSubtitleRemoveTaskPO params) {
        return makeSubtitleRemoveTaskMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeSubtitleRemoveTaskPO> queryList(MakeSubtitleRemoveTaskPO params) {
        return makeSubtitleRemoveTaskMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeSubtitleRemoveTaskPO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeSubtitleRemoveTaskPO> list = makeSubtitleRemoveTaskMapper.queryList(params);
        for (MakeSubtitleRemoveTaskPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(MakeSubtitleRemoveTaskPO params) {
        Integer count = makeSubtitleRemoveTaskMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    public Integer countByBatchTaskId(Integer batchTaskId, VideoProcessStatusEnum statusEnum) {
        if (batchTaskId == null || statusEnum == null) {
            return 0;
        }
        MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
        query.setBatchId(batchTaskId);
        query.setStatus(statusEnum.getCode());
        Integer count = queryCount(query);
        return count == null ? 0 : count;
    }

    public Integer countBySeriesTaskId(Integer seriesTaskId, VideoProcessStatusEnum statusEnum) {
        if (seriesTaskId == null || statusEnum == null) {
            return 0;
        }
        MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        query.setStatus(statusEnum.getCode());
        Integer count = queryCount(query);
        return count == null ? 0 : count;
    }

    public List<MakeSubtitleRemoveTaskPO> queryByBatchTaskId(Integer batchTaskId) {
        if (batchTaskId == null) {
            return null;
        }
        MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
        query.setBatchId(batchTaskId);
        return queryList(query);
    }

    public MakeSubtitleRemoveTaskPO queryBySeriesTaskId(Integer seriesTaskId) {
        if (seriesTaskId == null) {
            return null;
        }
        MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        return queryOne(query);
    }

    public List<MakeSubtitleRemoveTaskPO> queryByThirdPartyId(String batchId, String taskId) {
        if (StrUtil.isBlank(batchId) || StrUtil.isBlank(taskId)) {
            return null;
        }
        MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
        query.setThirdPartyBatchId(batchId);
        query.setThirdPartyTaskId(taskId);
        return queryList(query);
    }

    public List<MakeSubtitleRemoveTaskPO> queryByFilmId(Integer filmId) {
        if (filmId == null) {
            return null;
        }
        MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
        query.setFilmId(filmId);
        return queryList(query);
    }

    public MakeSubtitleRemoveTaskPO queryByFilmIdAndSeriesNum(Integer filmId, Integer seriesNum) {
        if (filmId == null || seriesNum == null) {
            return null;
        }
        MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
        query.setFilmId(filmId);
        query.setSeriesNum(seriesNum);
        return queryOne(query);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeSubtitleRemoveTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeSubtitleRemoveTaskMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeSubtitleRemoveTaskPO> list) {
        if (makeSubtitleRemoveTaskMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeSubtitleRemoveTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeSubtitleRemoveTaskMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeSubtitleRemoveTaskPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeSubtitleRemoveTaskPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        makeSubtitleRemoveTaskMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public int updateStatus(Integer id, Integer status) {
        if (id == null || status == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleRemoveTaskPO taskPO = new MakeSubtitleRemoveTaskPO();
        taskPO.setId(id);
        taskPO.setStatus(status);
        return makeSubtitleRemoveTaskMapper.updateById(taskPO);
    }

    public int updateStatus(Integer id, VideoProcessStatusEnum statusEnum) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        return updateStatus(id, statusEnum.getCode());
    }

    public int updateSuccess(Integer id, Date startTime, Date endTime, String url, String thirdStatus) {
        if (id == null || startTime == null || endTime == null || StrUtil.isBlank(url)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleRemoveTaskPO removeTask = new MakeSubtitleRemoveTaskPO();
        removeTask.setId(id);
        removeTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        removeTask.setEndTime(endTime);
        removeTask.setTakeTime(DateUtil.getDiffTime(startTime, endTime));
        removeTask.setUrl(url);
        removeTask.setThirdPartyStatus(thirdStatus);
        return makeSubtitleRemoveTaskMapper.updateById(removeTask);
    }

    public int updateFail(Integer id, Date startTime, Date endTime, String msg, String thirdStatus) {
        if (id == null || startTime == null || endTime == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleRemoveTaskPO removeTask = new MakeSubtitleRemoveTaskPO();
        removeTask.setId(id);
        removeTask.setStatus(VideoProcessStatusEnum.FAILED.getCode());
        removeTask.setEndTime(endTime);
        removeTask.setTakeTime(DateUtil.getDiffTime(startTime, endTime));
        removeTask.setErrorMessage(msg);
        removeTask.setThirdPartyStatus(thirdStatus);
        return makeSubtitleRemoveTaskMapper.updateById(removeTask);
    }

    public int bindThirdPartyTaskId(Integer id, String thirdPartyBatchId, String thirdPartyTaskId) {
        if (id == null || StrUtil.isBlank(thirdPartyBatchId) || StrUtil.isBlank(thirdPartyTaskId)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleRemoveTaskPO removeTask = new MakeSubtitleRemoveTaskPO();
        removeTask.setId(id);
        removeTask.setThirdPartyBatchId(thirdPartyBatchId);
        removeTask.setThirdPartyTaskId(thirdPartyTaskId);
        removeTask.setStartTime(DateUtil.getNowDate());
        removeTask.setStatus(VideoProcessStatusEnum.PROCESSING.getCode());
        return makeSubtitleRemoveTaskMapper.updateById(removeTask);
    }

    /**
     * 执行字幕擦除业务逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeRemove(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {
        log.info("开始字幕擦除: batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());

        // 获取许可证
        ThirdPartyApiRateLimiter.acquireStartExecution();
        log.info("Qps许可证获取成功，开始字幕擦除，batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());

        MakeFilePO makeFile = makeFileService.queryById(seriesTask.getOriginalVideoId());
        if (makeFile == null) {
            throw new MyException("未找到媒体文件 " + seriesTask.getOriginalVideoId());
        }
        if (StrUtil.isBlank(makeFile.getVid())) {
            throw new MyException("媒体文件 " + seriesTask.getOriginalVideoId() + " 缺失vid");
        }

        MakeSubtitleRemoveTaskPO removeTask = queryBySeriesTaskId(seriesTask.getId());
        if (removeTask == null) {
            log.warn("字幕擦除任务不存在，任务前置环节异常，请检查！！！，batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());
            return;
        }
        if (Objects.equals(removeTask.getStatus(), VideoProcessStatusEnum.PROCESSING.getCode())) {
            log.warn("剧集任务 {} 的 字幕擦除任务 正在处理中，直接跳过本次擦除，batchTaskId={}, removeTaskId={}", seriesTask.getId(), batchTask.getId(), removeTask.getId());
            return;
        }
        if (Objects.equals(removeTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
            log.warn("剧集任务 {} 的 字幕擦除任务 已完成，直接跳过本次擦除，触发下一步操作：压制，batchTaskId={}, removeTaskId={}", seriesTask.getId(), batchTask.getId(), removeTask.getId());
            removeSuccess(batchTask, seriesTask, removeTask, removeTask.getUrl());
            return;
        }
        JSONObject response = volcengineApiUtil.subtitleRemove(makeFile.getVid());
        if (response == null) {
            removeFail(batchTask, seriesTask, removeTask, "字幕擦除API调用失败，返回结果为空", DateUtil.getNowDate());
            return;
        }

        JSONObject responseMetadata = response.getJSONObject("ResponseMetadata");
        JSONObject error = responseMetadata.getJSONObject("Error");
        JSONObject result = response.getJSONObject("Result");

        if (result == null || error != null) {
            removeFail(batchTask, seriesTask, removeTask, "字幕擦除API调用失败: " + error.getString("Message"), DateUtil.getNowDate());
            return;
        }

        // 绑定三方的id，状态更新为进行中
        String batchId = "volcengine";
        String runId = result.getString("RunId");
        bindThirdPartyTaskId(removeTask.getId(), batchId, runId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeSuccess(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, MakeSubtitleRemoveTaskPO removeTask, String ossUrl) {
        // 规避重复处理
        if (!Objects.equals(removeTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
            updateSuccess(removeTask.getId(), removeTask.getStartTime(), DateUtil.getNowDate(), ossUrl, removeTask.getThirdPartyStatus());
            log.info("更新字幕擦除任务 {} 的状态为 已完成，batchTaskId={}, seriesTaskId={}", removeTask.getId(), batchTask.getId(), seriesTask.getId());
        }
        // 同步补齐成片视频数据
        makeFilmVideoService.fillUrlByFilmAndSeriesNumAndLang(batchTask.getFilmId(), FilmTypeEnum.COMPLETED.getCode(), seriesTask.getSeriesNum(), "00", ossUrl);
        log.info("字幕擦除任务 {} 处理完毕，同步补齐成片 {} 源语言的视频数据，batchTaskId={}, seriesTaskId={}", removeTask.getId(), batchTask.getFilmId(), batchTask.getId(), seriesTask.getId());

        // 同步补齐原片视频数据
        Integer originalFilmId = makeCompletedFilmService.queryOriginalFilmId(batchTask.getFilmId());
        if (originalFilmId == null) {
            log.warn("未找到原片ID，无法同步补齐原片视频数据，batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());
        } else {
            makeFilmVideoService.fillCleanUrlByFilmAndSeriesNum(originalFilmId, FilmTypeEnum.ORIGINAL.getCode(), seriesTask.getSeriesNum(), ossUrl);
            log.info("字幕擦除任务 {} 处理完毕，同步补齐原片 {} 源语言的视频数据，batchTaskId={}, seriesTaskId={}", removeTask.getId(), originalFilmId, batchTask.getId(), seriesTask.getId());
        }
        
        // 这里的标记先打上，后续step需要用到，可以避免频繁查询
        seriesTask.setRemoveCompleted(YesOrNoEnum.YES.getCode());
        makeVideoSeriesTaskService.markRemoveCompleted(seriesTask.getId());
        log.info("更新剧集子任务 {} 的字幕擦除标记为 true，batchTaskId={}", seriesTask.getId(), batchTask.getId());

        // 同步更新成片完成数量
        makeVideoBatchTaskService.syncCompletedCountAndEndTime(batchTask.getId(), DateUtil.getNowDate());
        log.info("字幕擦除任务 {} 处理完毕，处理完成总数量+1，batchTaskId={}, seriesTaskId={}", removeTask.getId(), batchTask.getId(), seriesTask.getId());

        // 触发下一步流程：视频压制
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                videoProcessFlowManager.executeNextStep(VideoProcessStepEnum.SUBTITLE_REMOVE, batchTask, seriesTask);
                log.info("字幕擦除任务 {} 完成，触发压制，batchTaskId={}, seriesTaskId={}", removeTask.getId(), batchTask.getId(), seriesTask.getId());
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeFail(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, MakeSubtitleRemoveTaskPO removeTask, String msg, Date endTime) {
        updateFail(removeTask.getId(), removeTask.getStartTime(), endTime, msg, removeTask.getThirdPartyStatus());
        log.warn("更新字幕擦除任务 {} 的状态为 失败，并录入失败原因，batchTaskId={}, seriesTaskId={}", removeTask.getId(), batchTask.getId(), seriesTask.getId());

        // 更新剧集子任务状态为失败
        makeVideoSeriesTaskService.updateStatusAndEndTime(seriesTask.getId(), VideoProcessStatusEnum.FAILED, endTime);
        log.warn("更新剧集子任务 {} 的状态为 失败，batchTaskId={}", seriesTask.getId(), batchTask.getId());

        // 更新批量任务状态为失败
        // makeVideoBatchTaskService.updateStatusAndEndTime(batchTask.getId(), VideoProcessStatusEnum.FAILED, endTime);
        // log.warn("更新批量任务 {} 的状态为 失败，batchTaskId={}", seriesTask.getId(), batchTask.getId());
        // 更新失败数量
        makeVideoBatchTaskService.syncFailedCount(batchTask.getId());
        log.warn("增加批量任务 {} 的处理失败数量+1", batchTask.getId());
    }

}
