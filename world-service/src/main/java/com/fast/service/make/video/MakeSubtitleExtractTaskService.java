// /*
//  * Powered By fast.up
//  */
// package com.fast.service.make.video;
//
// import com.alibaba.fastjson.JSONArray;
// import com.alibaba.fastjson.JSONObject;
// import com.fast.constant.StaticStr;
// import com.fast.enums.video.VideoProcessStatusEnum;
// import com.fast.framework.config.VideoProcessConfig;
// import com.fast.framework.exception.MyException;
// import com.fast.mapper.make.video.MakeSubtitleExtractTaskMapper;
// import com.fast.po.make.video.MakeSubtitleExtractTaskPO;
// import com.fast.po.make.video.MakeVideoBatchTaskPO;
// import com.fast.po.make.video.MakeVideoSeriesTaskPO;
// import com.fast.service.base.BaseService;
// import com.fast.service.make.video.util.GhostCutApiUtil;
// import com.fast.utils.DateUtil;
// import com.fast.utils.StrUtil;
// import com.fast.vo.MethodVO;
// import com.fast.vo.PageVO;
// import com.fast.vo.ResultVO;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;
//
// import java.util.Date;
// import java.util.List;
//
// /**
//  * <AUTHOR>
//  */
// @Service
// @Slf4j
// public class MakeSubtitleExtractTaskService extends BaseService {
//
//     @Autowired
//     private MakeSubtitleExtractTaskMapper makeSubtitleExtractTaskMapper;
//
//     @Autowired
//     private GhostCutApiUtil ghostCutApiUtil;
//
//     @Autowired
//     private VideoProcessConfig videoProcessConfig;
//
//     @Autowired
//     private MakeVideoSeriesTaskService makeVideoSeriesTaskService;
//
//     @Autowired
//     private MakeVideoBatchTaskService makeVideoBatchTaskService;
//
//     /**
//      * 通过id查询单个对象
//      */
//     public MakeSubtitleExtractTaskPO queryById(MakeSubtitleExtractTaskPO params) {
//         return makeSubtitleExtractTaskMapper.queryById(params);
//     }
//
//     /**
//      * 通过id查询单个对象
//      */
//     public MakeSubtitleExtractTaskPO queryById(Integer id) {
//         return makeSubtitleExtractTaskMapper.queryById(id);
//     }
//
//     /**
//      * 通过条件查询单个对象
//      */
//     public MakeSubtitleExtractTaskPO queryOne(MakeSubtitleExtractTaskPO params) {
//         return makeSubtitleExtractTaskMapper.queryOne(params);
//     }
//
//     /**
//      * 查询全部
//      */
//     public List<MakeSubtitleExtractTaskPO> queryList(MakeSubtitleExtractTaskPO params) {
//         return makeSubtitleExtractTaskMapper.queryList(params);
//     }
//
//     /**
//      * 查询全部(分页)
//      */
//     public ResultVO<?> queryPageList(MakeSubtitleExtractTaskPO params, PageVO pageVO) {
//         startPage(pageVO);
//         List<MakeSubtitleExtractTaskPO> list = makeSubtitleExtractTaskMapper.queryList(params);
//         for (MakeSubtitleExtractTaskPO cur : list) {
//             cur.setEncryptionId(encode(cur.getId()));
//         }
//         return ResultVO.success(getPageListData(list, pageVO));
//     }
//
//     /**
//      * 查询总数
//      */
//     public Integer queryCount(MakeSubtitleExtractTaskPO params) {
//         Integer count = makeSubtitleExtractTaskMapper.queryCount(params);
//         return count == null ? 0 : count;
//     }
//
//     public MakeSubtitleExtractTaskPO queryBySeriesTaskId(Integer seriesTaskId) {
//         if (seriesTaskId == null) {
//             return null;
//         }
//         MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
//         query.setSeriesTaskId(seriesTaskId);
//         return queryOne(query);
//     }
//
//     public List<MakeSubtitleExtractTaskPO> queryByThirdPartyId(String batchId, String taskId) {
//         if (StrUtil.isBlank(batchId) || StrUtil.isBlank(taskId)) {
//             return null;
//         }
//         MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
//         query.setThirdPartyBatchId(batchId);
//         query.setThirdPartyTaskId(taskId);
//         return queryList(query);
//     }
//
//     public List<MakeSubtitleExtractTaskPO> queryByFilmId(Integer filmId) {
//         if (filmId == null) {
//             return null;
//         }
//         MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
//         query.setFilmId(filmId);
//         return queryList(query);
//     }
//
//     public MakeSubtitleExtractTaskPO queryByFilmIdAndSeriesNum(Integer filmId, Integer seriesNum) {
//         if (filmId == null || seriesNum == null) {
//             return null;
//         }
//         MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
//         query.setFilmId(filmId);
//         query.setSeriesNum(seriesNum);
//         return queryOne(query);
//     }
//
//     /**
//      * 新增
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO insert(MakeSubtitleExtractTaskPO params) {
//         Date nowTime = DateUtil.getNowDate();
//         params.setCreateTime(nowTime);
//         params.setUpdateTime(nowTime);
//         if (makeSubtitleExtractTaskMapper.insertSelective(params) == 0) {
//             transactionRollBack();
//             return MethodVO.error(StaticStr.ADD_FAILED);
//         }
//         return MethodVO.success();
//     }
//
//     /**
//      * 批量新增
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO insertBatch(List<MakeSubtitleExtractTaskPO> list) {
//         if (makeSubtitleExtractTaskMapper.insertBatch(list) > 0) {
//             return MethodVO.success();
//         } else {
//             return MethodVO.error(StaticStr.ADD_FAILED);
//         }
//     }
//
//     /**
//      * 更新
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO update(MakeSubtitleExtractTaskPO params) {
//         Date nowTime = DateUtil.getNowDate();
//         params.setUpdateTime(nowTime);
//         if (makeSubtitleExtractTaskMapper.updateById(params) == 0) {
//             transactionRollBack();
//             return MethodVO.error(StaticStr.UPDATE_FAILED);
//         }
//         return MethodVO.success();
//     }
//
//     /**
//      * 删除
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO delete(MakeSubtitleExtractTaskPO params) {
//         if (params.getId() == null) {
//             return MethodVO.error(StaticStr.DELETE_FAILED);
//         }
//         MakeSubtitleExtractTaskPO po = queryById(params.getId());
//         if (po == null) {
//             return MethodVO.error("data not exist");
//         }
//         makeSubtitleExtractTaskMapper.deleteById(po.getId());
//         return MethodVO.success();
//     }
//
//     public int updateStatus(Integer id, Integer status) {
//         if (id == null || status == null) {
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//         MakeSubtitleExtractTaskPO taskPO = new MakeSubtitleExtractTaskPO();
//         taskPO.setId(id);
//         taskPO.setStatus(status);
//         return makeSubtitleExtractTaskMapper.updateById(taskPO);
//     }
//
//     public int updateStatus(Integer id, VideoProcessStatusEnum statusEnum) {
//         if (id == null || statusEnum == null) {
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//         return updateStatus(id, statusEnum.getCode());
//     }
//
//     /**
//      * 执行字幕提取业务逻辑
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public void executeExtract(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {
//         log.info("开始字幕提取: batchTaskId={}", batchTask.getId());
//
//         JSONObject response = ghostCutApiUtil.subtitleExtract(
//                 List.of(seriesTask.getOriginalVideoUrl()),
//                 List.of("[" + batchTask.getFilmId() + "]字幕提取_" + batchTask.getId() + "_" + seriesTask.getId() + "_" + seriesTask.getSeriesNum()),
//                 videoProcessConfig.buildSubtitleExtractCallback(batchTask.getId()),
//                 batchTask.getRegion());
//         log.info("鬼手字幕提取api请求结果: {}", response.toJSONString());
//
//         Integer code = response.getInteger("code");
//         if (code == null || code != 1000) {
//             throw new MyException("字幕提取API调用失败: " + response.getString("msg"));
//         }
//
//         JSONObject body = response.getJSONObject("body");
//         String idProject = body.getString("idProject");
//         JSONArray dataList = body.getJSONArray("dataList");
//
//         for (int i = 0; i < dataList.size(); i++) {
//             JSONObject item = dataList.getJSONObject(i);
//
//             MakeSubtitleExtractTaskPO extractTask = new MakeSubtitleExtractTaskPO();
//             extractTask.setSeriesTaskId(seriesTask.getId());
//             extractTask.setFilmId(batchTask.getFilmId());
//             extractTask.setSeriesNum(seriesTask.getSeriesNum());
//             extractTask.setThirdPartyBatchId(idProject);
//             extractTask.setThirdPartyTaskId(item.getString("id"));
//             extractTask.setVideoUrl(seriesTask.getOriginalVideoUrl());
//             extractTask.setStatus(VideoProcessStatusEnum.PROCESSING.getCode());
//             extractTask.setStartTime(DateUtil.getNowDate());
//             extractTask.setLanguageCode(batchTask.getSourceLang());
//             insert(extractTask);
//             log.info("剧集任务 {} 新增字幕提取任务 {} 状态为 处理中", seriesTask.getId(), extractTask.getId());
//         }
//
//         // 更新剧集子任务状态
//         makeVideoSeriesTaskService.updateStatus(seriesTask.getId(), VideoProcessStatusEnum.PROCESSING);
//         log.info("更新剧集子任务 {} 的状态为 处理中", seriesTask.getId());
//        
//         // 更新批量任务状态
//         makeVideoBatchTaskService.updateStatus(batchTask.getId(), VideoProcessStatusEnum.PROCESSING);
//         log.info("更新批量任务 {} 的状态为 处理中", batchTask.getId());
//     }
//
// }
