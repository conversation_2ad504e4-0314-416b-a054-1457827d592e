/*
 * Powered By fast.up
 */
package com.fast.service.make;

import com.fast.constant.StaticStr;
import com.fast.enums.FilmTypeEnum;
import com.fast.mapper.make.MakeOriginalFilmMapper;
import com.fast.po.make.MakeFilmVideoPO;
import com.fast.po.make.MakeOriginalFilmPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MakeOriginalFilmService extends BaseService {

    @Autowired
    private MakeOriginalFilmMapper makeOriginalFilmMapper;

    @Autowired
    private MakeFileService makeFileService;

    @Autowired
    private MakeFilmVideoService makeFilmVideoService;

    @Autowired
    private MakeFilmCaptionService makeFilmCaptionService;

    /**
     * 通过id查询单个对象
     */
    public MakeOriginalFilmPO queryById(MakeOriginalFilmPO params) {
        return makeOriginalFilmMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeOriginalFilmPO queryById(Integer id) {
        return makeOriginalFilmMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeOriginalFilmPO queryOne(MakeOriginalFilmPO params) {
        return makeOriginalFilmMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeOriginalFilmPO> queryList(MakeOriginalFilmPO params) {
        return makeOriginalFilmMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeOriginalFilmPO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeOriginalFilmPO> list = makeOriginalFilmMapper.queryList(params);
        for (MakeOriginalFilmPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setAddNum(makeFilmVideoService.countByOriginalFilmId(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(MakeOriginalFilmPO params) {
        return makeOriginalFilmMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeOriginalFilmPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeOriginalFilmMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        if (CollUtil.isNotEmpty(params.getVideos())) {
            for (MakeFilmVideoPO video : params.getVideos()) {
                video.setFilmId(params.getId());
                video.setFilmType(FilmTypeEnum.ORIGINAL.getCode());
                makeFilmVideoService.insert(video);
            }
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeOriginalFilmPO> list) {
        if (makeOriginalFilmMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeOriginalFilmPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeOriginalFilmMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        if (CollUtil.isNotEmpty(params.getVideos())) {
            Integer row = makeFilmVideoService.deleteByOriginalFilmId(params.getId());
            if (row > 0) {
                for (MakeFilmVideoPO video : params.getVideos()) {
                    video.setFilmId(params.getId());
                    video.setFilmType(FilmTypeEnum.ORIGINAL.getCode());
                    makeFilmVideoService.insert(video);
                }
            }
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeOriginalFilmPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeOriginalFilmPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        // int count = makeFilmVideoService.countByOriginalFilmId(params.getId());
        // if (count > 0) {
        //     return MethodVO.error("该原片已添加" + count + "集");
        // }
        makeOriginalFilmMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public MethodVO download(MakeOriginalFilmPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeOriginalFilmPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }

        return MethodVO.success();
    }

}
