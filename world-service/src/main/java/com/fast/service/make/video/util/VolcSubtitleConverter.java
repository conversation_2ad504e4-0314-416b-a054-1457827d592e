package com.fast.service.make.video.util;

import com.alibaba.fastjson.JSON;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.Duration;
import java.util.List;

@Slf4j
public class VolcSubtitleConverter {

    public static boolean convertToSrtFromFile(String jsonPath, String outputPath) {
        List<VolcSubtitleEntry> entries = readJsonFile(jsonPath);
        return convertToSrt(entries, outputPath);
    }

    public static boolean convertToSrtFromJson(String json, String outputPath) {
        List<VolcSubtitleEntry> entries = JSON.parseArray(json, VolcSubtitleEntry.class);
        return convertToSrt(entries, outputPath);
    }

    private static boolean convertToSrt(List<VolcSubtitleEntry> entries, String outputPath) {
        if (CollUtil.isEmpty(entries) || StrUtil.isBlank(outputPath)) {
            return false;
        }
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputPath))) {
            for (int i = 0; i < entries.size(); i++) {
                VolcSubtitleEntry entry = entries.get(i);
                writer.write(String.valueOf(i + 1));
                writer.newLine();
                writer.write(formatSrtTime(entry.getStart()) + " --> " + formatSrtTime(entry.getEnd()));
                writer.newLine();
                writer.write(entry.getText());
                writer.newLine();
                writer.newLine();
            }
            return true;
        } catch (Exception e) {
            log.error("转写srt文件失败: {}", ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    public static boolean convertToWebVttFromFile(String jsonPath, String outputPath) {
        List<VolcSubtitleEntry> entries = readJsonFile(jsonPath);
        return convertToWebVtt(entries, outputPath);
    }

    public static boolean convertToWebVttFromJson(String json, String outputPath) {
        List<VolcSubtitleEntry> entries = JSON.parseArray(json, VolcSubtitleEntry.class);
        return convertToWebVtt(entries, outputPath);
    }

    public static boolean convertToWebVtt(List<VolcSubtitleEntry> entries, String outputPath) {
        if (CollUtil.isEmpty(entries) || StrUtil.isBlank(outputPath)) {
            return false;
        }
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputPath))) {
            writer.write("WEBVTT");
            writer.newLine();
            writer.newLine();
            for (int i = 0; i < entries.size(); i++) {
                VolcSubtitleEntry entry = entries.get(i);
                writer.write(String.valueOf(i + 1));
                writer.newLine();
                writer.write(formatWebVttTime(entry.getStart()) + " --> " + formatWebVttTime(entry.getEnd()));
                writer.newLine();
                writer.write(entry.getText());
                writer.newLine();
                writer.newLine();
            }
            return true;
        } catch (Exception e) {
            log.error("转写webvtt文件失败: {}", ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    private static List<VolcSubtitleEntry> readJsonFile(String jsonPath) {
        try {
            String json = FileUtils.readFileToString(new File(jsonPath), "UTF-8");
            return JSON.parseArray(json, VolcSubtitleEntry.class);
        } catch (Exception e) {
            log.error("读取json文件失败: {}", ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    private static String formatSrtTime(double seconds) {
        Duration duration = Duration.ofMillis((long) (seconds * 1000));
        long hours = duration.toHours();
        long minutes = duration.toMinutesPart();
        long secs = duration.toSecondsPart();
        long millis = duration.toMillisPart();
        return String.format("%02d:%02d:%02d,%03d", hours, minutes, secs, millis);
    }

    private static String formatWebVttTime(double seconds) {
        Duration duration = Duration.ofMillis((long) (seconds * 1000));
        long hours = duration.toHours();
        long minutes = duration.toMinutesPart();
        long secs = duration.toSecondsPart();
        long millis = duration.toMillisPart();
        return String.format("%02d:%02d:%02d.%03d", hours, minutes, secs, millis);
    }

    public static void main(String[] args) throws IOException {
        // String jsonPath = "C:\\Users\\<USER>\\Desktop\\test\\1.json";
        // SubtitleConverter.convertToSrt(jsonPath, "output.srt");
        // SubtitleConverter.convertToWebVtt(jsonPath, "output.vtt");
    }
}