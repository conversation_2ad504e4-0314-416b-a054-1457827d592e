/*
 * Powered By fast.up
 */
package com.fast.service.make.video;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticStr;
import com.fast.enums.FilmTypeEnum;
import com.fast.enums.YesOrNoEnum;
import com.fast.enums.video.VideoProcessStatusEnum;
import com.fast.enums.video.VideoProcessStepEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.make.video.MakeSubtitleExtractTaskMapper;
import com.fast.po.make.MakeFilePO;
import com.fast.po.make.video.MakeSubtitleExtractTaskPO;
import com.fast.po.make.video.MakeVideoBatchTaskPO;
import com.fast.po.make.video.MakeVideoSeriesTaskPO;
import com.fast.service.base.BaseService;
import com.fast.service.make.MakeCompletedFilmService;
import com.fast.service.make.MakeFileService;
import com.fast.service.make.MakeFilmCaptionService;
import com.fast.service.make.video.util.ThirdPartyApiRateLimiter;
import com.fast.service.make.video.util.VolcengineApiUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MakeSubtitleExtractTaskServiceV2 extends BaseService {

    @Autowired
    private MakeSubtitleExtractTaskMapper makeSubtitleExtractTaskMapper;

    @Autowired
    private MakeFileService makeFileService;

    @Autowired
    private VolcengineApiUtil volcengineApiUtil;

    @Autowired
    private MakeVideoSeriesTaskService makeVideoSeriesTaskService;

    @Autowired
    private MakeVideoBatchTaskService makeVideoBatchTaskService;

    @Autowired
    private MakeFilmCaptionService makeFilmCaptionService;

    @Autowired
    private VideoProcessFlowManager videoProcessFlowManager;

    @Autowired
    private MakeCompletedFilmService makeCompletedFilmService;

    /**
     * 通过id查询单个对象
     */
    public MakeSubtitleExtractTaskPO queryById(MakeSubtitleExtractTaskPO params) {
        return makeSubtitleExtractTaskMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeSubtitleExtractTaskPO queryById(Integer id) {
        return makeSubtitleExtractTaskMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeSubtitleExtractTaskPO queryOne(MakeSubtitleExtractTaskPO params) {
        return makeSubtitleExtractTaskMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeSubtitleExtractTaskPO> queryList(MakeSubtitleExtractTaskPO params) {
        return makeSubtitleExtractTaskMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeSubtitleExtractTaskPO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeSubtitleExtractTaskPO> list = makeSubtitleExtractTaskMapper.queryList(params);
        for (MakeSubtitleExtractTaskPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(MakeSubtitleExtractTaskPO params) {
        Integer count = makeSubtitleExtractTaskMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    public Integer countByBatchTaskId(Integer batchTaskId, VideoProcessStatusEnum statusEnum) {
        if (batchTaskId == null || statusEnum == null) {
            return 0;
        }
        MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
        query.setBatchId(batchTaskId);
        query.setStatus(statusEnum.getCode());
        Integer count = queryCount(query);
        return count == null ? 0 : count;
    }

    public Integer countBySeriesTaskId(Integer seriesTaskId, VideoProcessStatusEnum statusEnum) {
        if (seriesTaskId == null || statusEnum == null) {
            return 0;
        }
        MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        query.setStatus(statusEnum.getCode());
        Integer count = queryCount(query);
        return count == null ? 0 : count;
    }

    public List<MakeSubtitleExtractTaskPO> queryByBatchTaskId(Integer batchTaskId) {
        if (batchTaskId == null) {
            return null;
        }
        MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
        query.setBatchId(batchTaskId);
        return queryList(query);
    }

    public MakeSubtitleExtractTaskPO queryBySeriesTaskId(Integer seriesTaskId) {
        if (seriesTaskId == null) {
            return null;
        }
        MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        return queryOne(query);
    }

    public List<MakeSubtitleExtractTaskPO> queryByThirdPartyId(String batchId, String taskId) {
        if (StrUtil.isBlank(batchId) || StrUtil.isBlank(taskId)) {
            return null;
        }
        MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
        query.setThirdPartyBatchId(batchId);
        query.setThirdPartyTaskId(taskId);
        return queryList(query);
    }

    public List<MakeSubtitleExtractTaskPO> queryByFilmId(Integer filmId) {
        if (filmId == null) {
            return null;
        }
        MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
        query.setFilmId(filmId);
        return queryList(query);
    }

    public MakeSubtitleExtractTaskPO queryByFilmIdAndSeriesNum(Integer filmId, Integer seriesNum) {
        if (filmId == null || seriesNum == null) {
            return null;
        }
        MakeSubtitleExtractTaskPO query = new MakeSubtitleExtractTaskPO();
        query.setFilmId(filmId);
        query.setSeriesNum(seriesNum);
        return queryOne(query);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeSubtitleExtractTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeSubtitleExtractTaskMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeSubtitleExtractTaskPO> list) {
        if (makeSubtitleExtractTaskMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeSubtitleExtractTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeSubtitleExtractTaskMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeSubtitleExtractTaskPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeSubtitleExtractTaskPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        makeSubtitleExtractTaskMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public int updateStatus(Integer id, Integer status) {
        if (id == null || status == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleExtractTaskPO taskPO = new MakeSubtitleExtractTaskPO();
        taskPO.setId(id);
        taskPO.setStatus(status);
        return makeSubtitleExtractTaskMapper.updateById(taskPO);
    }

    public int updateStatus(Integer id, VideoProcessStatusEnum statusEnum) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        return updateStatus(id, statusEnum.getCode());
    }

    public int updateSuccess(Integer id, Date startTime, Date endTime, String url, String thirdStatus, Integer type) {
        if (id == null || startTime == null || endTime == null || StrUtil.isBlank(url)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleExtractTaskPO extractTask = new MakeSubtitleExtractTaskPO();
        extractTask.setId(id);
        extractTask.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        extractTask.setEndTime(endTime);
        extractTask.setTakeTime(DateUtil.getDiffTime(startTime, endTime));
        extractTask.setUrl(url);
        extractTask.setType(type);
        extractTask.setThirdPartyStatus(thirdStatus);
        return makeSubtitleExtractTaskMapper.updateById(extractTask);
    }

    public int updateFail(Integer id, Date startTime, Date endTime, String msg, String thirdStatus) {
        if (id == null || startTime == null || endTime == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleExtractTaskPO extractTask = new MakeSubtitleExtractTaskPO();
        extractTask.setId(id);
        extractTask.setStatus(VideoProcessStatusEnum.FAILED.getCode());
        extractTask.setEndTime(endTime);
        extractTask.setTakeTime(DateUtil.getDiffTime(startTime, endTime));
        extractTask.setErrorMessage(msg);
        extractTask.setThirdPartyStatus(thirdStatus);
        return makeSubtitleExtractTaskMapper.updateById(extractTask);
    }

    public int bindThirdPartyId(Integer id, String thirdPartyBatchId, String thirdPartyTaskId) {
        if (id == null || StrUtil.isBlank(thirdPartyBatchId) || StrUtil.isBlank(thirdPartyTaskId)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleExtractTaskPO extractTask = new MakeSubtitleExtractTaskPO();
        extractTask.setId(id);
        extractTask.setThirdPartyBatchId(thirdPartyBatchId);
        extractTask.setThirdPartyTaskId(thirdPartyTaskId);
        extractTask.setStartTime(DateUtil.getNowDate());
        extractTask.setStatus(VideoProcessStatusEnum.PROCESSING.getCode());
        return makeSubtitleExtractTaskMapper.updateById(extractTask);
    }

    /**
     * 执行字幕提取业务逻辑
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeExtract(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {
        log.info("开始字幕提取: batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());

        // 获取许可证
        ThirdPartyApiRateLimiter.acquireStartExecution();
        log.info("Qps许可证获取成功，开始字幕提取，batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());

        MakeFilePO makeFile = makeFileService.queryById(seriesTask.getOriginalVideoId());
        if (makeFile == null) {
            throw new MyException("未找到媒体文件 " + seriesTask.getOriginalVideoId());
        }
        if (StrUtil.isBlank(makeFile.getVid())) {
            throw new MyException("媒体文件 " + seriesTask.getOriginalVideoId() + " 缺失vid");
        }

        MakeSubtitleExtractTaskPO extractTask = queryBySeriesTaskId(seriesTask.getId());
        if (extractTask == null) {
            log.warn("字幕提取任务不存在，任务前置环节异常，请检查！！！，batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());
            return;
        }
        if (Objects.equals(extractTask.getStatus(), VideoProcessStatusEnum.PROCESSING.getCode())) {
            log.warn("剧集任务 {} 的 字幕提取任务 正在处理中，直接跳过本次提取，batchTaskId={}, extractTaskId={}", seriesTask.getId(), batchTask.getId(), extractTask.getId());
            return;
        }
        if (Objects.equals(extractTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
            log.warn("剧集任务 {} 的 字幕提取任务 已完成，直接跳过本次提取，触发下一步操作：翻译、擦除，batchTaskId={}, extractTaskId={}", seriesTask.getId(), batchTask.getId(), extractTask.getId());
            extractSuccess(batchTask, seriesTask, extractTask, extractTask.getUrl());
            return;
        }

        JSONObject response = volcengineApiUtil.subtitleExtractAsr(makeFile.getVid());
        if (response == null) {
            extractFail(batchTask, seriesTask, extractTask, "字幕擦除API调用失败，返回结果为空", DateUtil.getNowDate());
            return;
        }

        JSONObject responseMetadata = response.getJSONObject("ResponseMetadata");
        JSONObject error = responseMetadata.getJSONObject("Error");
        JSONObject result = response.getJSONObject("Result");

        if (result == null || error != null) {
            extractFail(batchTask, seriesTask, extractTask, "字幕提取API调用失败: " + error.getString("Message"), DateUtil.getNowDate());
            return;
        }

        // 绑定三方的id，状态更新为进行中
        String batchId = "volcengine";
        String runId = result.getString("RunId");
        bindThirdPartyId(extractTask.getId(), batchId, runId);
        log.info("绑定三方任务ID成功，batchTaskId={}, seriesTaskId={}, extractTaskId={}, runId={}", batchTask.getId(), seriesTask.getId(), extractTask.getId(), runId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void extractSuccess(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, MakeSubtitleExtractTaskPO extractTask, String ossUrl) {
        // 规避重复处理
        if (!Objects.equals(extractTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
            updateSuccess(extractTask.getId(), extractTask.getStartTime(), DateUtil.getNowDate(), ossUrl, extractTask.getThirdPartyStatus(), extractTask.getType());
            log.info("更新字幕提取任务 {} 的状态为 已完成，batchTaskId={}, seriesTaskId={}, lang={}", extractTask.getId(), batchTask.getId(), seriesTask.getId(), extractTask.getLanguageCode());
        }
        // 同步补齐成片字幕数据
        makeFilmCaptionService.fillUrlByFilmAndSeriesNumAndLang(batchTask.getFilmId(), FilmTypeEnum.COMPLETED.getCode(), seriesTask.getSeriesNum(), extractTask.getLanguageCode(), ossUrl, extractTask.getType());
        log.info("字幕提取任务 {} 处理完毕，同步补齐成片 {} 源语言的字幕数据，batchTaskId={}, seriesTaskId={}, lang={}", extractTask.getId(), batchTask.getFilmId(), batchTask.getId(), seriesTask.getId(), extractTask.getLanguageCode());

        // 同步补齐原片字幕数据
        Integer originalFilmId = makeCompletedFilmService.queryOriginalFilmId(batchTask.getFilmId());
        if (originalFilmId == null) {
            log.warn("未找到原片ID，无法同步补齐原片字幕数据，batchTaskId={}, seriesTaskId={}, lang={}", batchTask.getId(), seriesTask.getId(), extractTask.getLanguageCode());
        } else {
            makeFilmCaptionService.initOriginalByFilmAndSeriesNumAndLang(originalFilmId, seriesTask.getSeriesNum(), extractTask.getLanguageCode(), ossUrl, extractTask.getType());
            log.info("字幕提取任务 {} 处理完毕，同步补齐原片 {} 源语言的字幕数据，batchTaskId={}, seriesTaskId={}, lang={}", extractTask.getId(), originalFilmId, batchTask.getId(), seriesTask.getId(), extractTask.getLanguageCode());
        }

        // 这里的标记先打上，后续step需要用到，可以避免频繁查询
        seriesTask.setExtractCompleted(YesOrNoEnum.YES.getCode());
        makeVideoSeriesTaskService.markExtractCompleted(seriesTask.getId());
        log.info("更新剧集子任务 {} 的字幕提取标记为 true，batchTaskId={}, lang={}", seriesTask.getId(), batchTask.getId(), extractTask.getLanguageCode());

        // 同步更新成片完成数量
        makeVideoBatchTaskService.syncCompletedCountAndEndTime(batchTask.getId(), DateUtil.getNowDate());
        log.info("字幕提取任务 {} 处理完毕，处理完成总数量+1，batchTaskId={}, seriesTaskId={}, lang={}", extractTask.getId(), batchTask.getId(), seriesTask.getId(), extractTask.getLanguageCode());

        // 触发下一步流程：字幕擦除、字幕翻译
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                videoProcessFlowManager.executeNextStep(VideoProcessStepEnum.SUBTITLE_EXTRACT, batchTask, seriesTask);
                log.info("字幕提取任务 {} 完成，触发擦除和翻译，batchTaskId={}, seriesTaskId={}, lang={}", extractTask.getId(), batchTask.getId(), seriesTask.getId(), extractTask.getLanguageCode());
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void extractFail(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, MakeSubtitleExtractTaskPO extractTask, String msg, Date endTime) {
        updateFail(extractTask.getId(), extractTask.getStartTime(), endTime, msg, extractTask.getThirdPartyStatus());
        log.warn("更新字幕提取任务 {} 的状态为 失败，并录入失败原因，batchTaskId={}, seriesTaskId={}", extractTask.getId(), batchTask.getId(), seriesTask.getId());

        // 更新剧集子任务状态为失败
        makeVideoSeriesTaskService.updateStatusAndEndTime(seriesTask.getId(), VideoProcessStatusEnum.FAILED, endTime);
        log.warn("更新剧集子任务 {} 的状态为 失败, batchTaskId={}", seriesTask.getId(), batchTask.getId());

        // 更新失败数量
        makeVideoBatchTaskService.syncFailedCount(batchTask.getId());
        log.warn("增加批量任务 {} 的处理失败数量+1", batchTask.getId());
    }

}
