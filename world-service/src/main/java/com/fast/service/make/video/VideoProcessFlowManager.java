package com.fast.service.make.video;

import com.fast.enums.YesOrNoEnum;
import com.fast.enums.video.VideoProcessStepEnum;
import com.fast.framework.manager.VThreadManager;
import com.fast.po.make.video.MakeVideoBatchTaskPO;
import com.fast.po.make.video.MakeVideoSeriesTaskPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 视频处理流程管理器
 * 负责管理视频处理的四个步骤的流转
 */
@Component
@Slf4j
public class VideoProcessFlowManager {

    @Autowired
    private VThreadManager vThreadManager;

    @Autowired
    private MakeVideoSeriesTaskService makeVideoSeriesTaskService;

    @Autowired
    private MakeSubtitleExtractTaskServiceV2 makeSubtitleExtractTaskServiceV2;

    @Autowired
    private MakeSubtitleTranslateTaskService makeSubtitleTranslateTaskService;

    @Autowired
    private MakeSubtitleRemoveTaskServiceV2 makeSubtitleRemoveTaskServiceV2;

    @Autowired
    private MakeVideoCompressTaskServiceV2 makeVideoCompressTaskServiceV2;

    /**
     * 启动视频处理流程
     */
    public void startProcess(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {
        log.info("启动视频处理流程: batchTaskId={}, seriesTaskId={}, filmId={}, seriesNum={}", batchTask.getId(), seriesTask.getId(), batchTask.getFilmId(), seriesTask.getSeriesNum());

        // 字幕提取
        vThreadManager.executeTaskWithMDC(() -> executeStep(VideoProcessStepEnum.SUBTITLE_EXTRACT, batchTask, seriesTask));
        // 字幕擦除
        vThreadManager.executeTaskWithMDC(() -> executeStep(VideoProcessStepEnum.SUBTITLE_REMOVE, batchTask, seriesTask));
    }

    /**
     * 执行下一步流程
     */
    public void executeNextStep(VideoProcessStepEnum currentStep, MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {
        List<VideoProcessStepEnum> nextStep = currentStep.getNextStep();
        if (nextStep == null) {
            log.warn("视频处理流程全部完成: batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());
            return;
        }
        log.info("当前步骤: {}, 执行下一步流程: {}, batchTaskId={}, seriesTaskId={}", currentStep, nextStep, batchTask.getId(), seriesTask.getId());

        // 支持多步骤并行
        for (VideoProcessStepEnum step : nextStep) {
            vThreadManager.executeTaskWithMDC(() -> executeStep(step, batchTask, seriesTask));
        }
    }

    /**
     * 执行具体步骤
     */
    private void executeStep(VideoProcessStepEnum step, MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {
        try {
            log.info("开始执行步骤: {}, batchTaskId={}, seriesTaskId={}", step.getDesc(), batchTask.getId(), seriesTask.getId());

            switch (step) {
                case SUBTITLE_EXTRACT:
                    if (Objects.equals(seriesTask.getExtractCompleted(), YesOrNoEnum.YES.getCode())) {
                        log.warn("剧集子任务 {} 下的字幕提取任务已经完成，本次不予执行，直接跳到下一步 字幕翻译、字幕擦除, batchTaskId={}", seriesTask.getId(), batchTask.getId());
                        executeStep(VideoProcessStepEnum.SUBTITLE_TRANSLATE, batchTask, seriesTask);
                        executeStep(VideoProcessStepEnum.SUBTITLE_REMOVE, batchTask, seriesTask);
                    } else {
                        makeSubtitleExtractTaskServiceV2.executeExtract(batchTask, seriesTask);
                    }
                    break;
                case SUBTITLE_TRANSLATE:
                    if (Objects.equals(seriesTask.getTranslateCompleted(), YesOrNoEnum.YES.getCode())) {
                        log.warn("剧集子任务 {} 下的字幕翻译任务已经完成，本次不予执行，直接跳到下一步 视频压制, batchTaskId={}", seriesTask.getId(), batchTask.getId());
                        executeStep(VideoProcessStepEnum.VIDEO_COMPRESS, batchTask, seriesTask);
                    } else {
                        makeSubtitleTranslateTaskService.executeTranslate(batchTask, seriesTask);
                    }
                    break;
                case SUBTITLE_REMOVE:
                    if (Objects.equals(seriesTask.getRemoveCompleted(), YesOrNoEnum.YES.getCode())) {
                        log.warn("剧集子任务 {} 下的字幕擦除任务已经完成，本次不予执行，直接跳到下一步 视频压制, batchTaskId={}", seriesTask.getId(), batchTask.getId());
                        executeStep(VideoProcessStepEnum.VIDEO_COMPRESS, batchTask, seriesTask);
                    } else {
                        makeSubtitleRemoveTaskServiceV2.executeRemove(batchTask, seriesTask);
                    }
                    break;
                case VIDEO_COMPRESS:
                    if (Objects.equals(seriesTask.getCompressCompleted(), YesOrNoEnum.YES.getCode())) {
                        log.warn("剧集子任务 {} 下的视频压制任务已经完成，本次不予执行, batchTaskId={}", seriesTask.getId(), batchTask.getId());
                    } else {
                        makeVideoCompressTaskServiceV2.executeCompress(batchTask, seriesTask);
                    }
                    break;
                default:
                    log.warn("未知的处理步骤: {}", step);
            }
        } catch (Exception e) {
            log.error("执行步骤失败: {}, batchTaskId={}, seriesTaskId={}", step.getDesc(), batchTask.getId(), seriesTask.getId(), e);
        }
    }

}
