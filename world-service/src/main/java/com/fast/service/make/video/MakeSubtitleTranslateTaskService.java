/*
 * Powered By fast.up
 */
package com.fast.service.make.video;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticStr;
import com.fast.enums.CaptionTypeEnum;
import com.fast.enums.FilmTypeEnum;
import com.fast.enums.LanguageEnum;
import com.fast.enums.YesOrNoEnum;
import com.fast.enums.video.VideoProcessStatusEnum;
import com.fast.enums.video.VideoProcessStepEnum;
import com.fast.framework.exception.MyException;
import com.fast.framework.manager.VThreadManager;
import com.fast.mapper.make.video.MakeSubtitleTranslateTaskMapper;
import com.fast.po.make.video.MakeSubtitleExtractTaskPO;
import com.fast.po.make.video.MakeSubtitleTranslateTaskPO;
import com.fast.po.make.video.MakeVideoBatchTaskPO;
import com.fast.po.make.video.MakeVideoSeriesTaskPO;
import com.fast.service.base.BaseService;
import com.fast.service.make.MakeCompletedFilmService;
import com.fast.service.make.MakeFilmCaptionService;
import com.fast.service.make.video.util.*;
import com.fast.service.oss.OssService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.file.FileUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MakeSubtitleTranslateTaskService extends BaseService {

    @Autowired
    private MakeSubtitleTranslateTaskMapper makeSubtitleTranslateTaskMapper;

    @Autowired
    private CozeTranslateUtil cozeTranslateUtil;

    @Autowired
    private OssService ossService;

    @Autowired
    private VThreadManager vThreadManager;

    @Autowired
    private MakeVideoSeriesTaskService makeVideoSeriesTaskService;

    @Autowired
    private VideoProcessFlowManager videoProcessFlowManager;

    @Autowired
    private MakeVideoBatchTaskService makeVideoBatchTaskService;

    @Autowired
    private MakeFilmCaptionService makeFilmCaptionService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private MakeSubtitleExtractTaskServiceV2 makeSubtitleExtractTaskServiceV2;

    @Autowired
    private MakeCompletedFilmService makeCompletedFilmService;

    /**
     * 临时文件存储路径
     * 注意：实际部署时请修改为合适的路径
     */
    private static final String tmpPath = "/home/<USER>/";

    /**
     * 通过id查询单个对象
     */
    public MakeSubtitleTranslateTaskPO queryById(MakeSubtitleTranslateTaskPO params) {
        return makeSubtitleTranslateTaskMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeSubtitleTranslateTaskPO queryById(Integer id) {
        return makeSubtitleTranslateTaskMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeSubtitleTranslateTaskPO queryOne(MakeSubtitleTranslateTaskPO params) {
        return makeSubtitleTranslateTaskMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeSubtitleTranslateTaskPO> queryList(MakeSubtitleTranslateTaskPO params) {
        return makeSubtitleTranslateTaskMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeSubtitleTranslateTaskPO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeSubtitleTranslateTaskPO> list = makeSubtitleTranslateTaskMapper.queryList(params);
        for (MakeSubtitleTranslateTaskPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(MakeSubtitleTranslateTaskPO params) {
        Integer count = makeSubtitleTranslateTaskMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    public Integer countByBatchTaskId(Integer batchTaskId, VideoProcessStatusEnum statusEnum) {
        if (batchTaskId == null || statusEnum == null) {
            return 0;
        }
        MakeSubtitleTranslateTaskPO query = new MakeSubtitleTranslateTaskPO();
        query.setBatchId(batchTaskId);
        query.setStatus(statusEnum.getCode());
        Integer count = queryCount(query);
        return count == null ? 0 : count;
    }

    public Integer countBySeriesTaskId(Integer seriesTaskId, VideoProcessStatusEnum statusEnum) {
        if (seriesTaskId == null || statusEnum == null) {
            return 0;
        }
        MakeSubtitleTranslateTaskPO query = new MakeSubtitleTranslateTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        query.setStatus(statusEnum.getCode());
        Integer count = queryCount(query);
        return count == null ? 0 : count;
    }

    public List<MakeSubtitleTranslateTaskPO> queryByBatchTaskId(Integer batchTaskId) {
        if (batchTaskId == null) {
            return null;
        }
        MakeSubtitleTranslateTaskPO query = new MakeSubtitleTranslateTaskPO();
        query.setBatchId(batchTaskId);
        return queryList(query);
    }

    public List<MakeSubtitleTranslateTaskPO> queryBySeriesTaskId(Integer seriesTaskId) {
        if (seriesTaskId == null) {
            return null;
        }
        MakeSubtitleTranslateTaskPO query = new MakeSubtitleTranslateTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        return queryList(query);
    }

    public boolean isAllCompleted(Integer seriesTaskId) {
        if (seriesTaskId == null) {
            return false;
        }
        List<MakeSubtitleTranslateTaskPO> list = queryBySeriesTaskId(seriesTaskId);
        if (CollUtil.isEmpty(list)) {
            return false;
        }
        return list.stream().allMatch(item -> Objects.equals(item.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode()));
    }

    public boolean checkAndmarkTranslateCompleted(Integer seriesTaskId) {
        if (seriesTaskId == null) {
            return false;
        }
        boolean allTranslateTaskCompleted = isAllCompleted(seriesTaskId);
        if (allTranslateTaskCompleted) {
            makeVideoSeriesTaskService.markTranslateCompleted(seriesTaskId);
            return true;
        }
        return false;
    }

    public Integer countBySeriesTaskId(Integer seriesTaskId, VideoProcessStatusEnum statusEnum, String destLang) {
        if (seriesTaskId == null || statusEnum == null) {
            return 0;
        }
        MakeSubtitleTranslateTaskPO query = new MakeSubtitleTranslateTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        query.setStatus(statusEnum.getCode());
        query.setLanguageCode(destLang);
        Integer count = queryCount(query);
        return count == null ? 0 : count;
    }

    public MakeSubtitleTranslateTaskPO queryBySeriesTaskIdAndLang(Integer seriesTaskId, String destLang) {
        if (seriesTaskId == null || StrUtil.isBlank(destLang)) {
            return null;
        }
        MakeSubtitleTranslateTaskPO query = new MakeSubtitleTranslateTaskPO();
        query.setSeriesTaskId(seriesTaskId);
        query.setLanguageCode(destLang);
        return queryOne(query);
    }

    public List<MakeSubtitleTranslateTaskPO> queryByFilmIdAndSeriesNum(Integer filmId, Integer seriesNum) {
        if (filmId == null || seriesNum == null) {
            return null;
        }
        MakeSubtitleTranslateTaskPO query = new MakeSubtitleTranslateTaskPO();
        query.setFilmId(filmId);
        query.setSeriesNum(seriesNum);
        return queryList(query);
    }

    public MakeSubtitleTranslateTaskPO queryByFilmIdAndSeriesNumAndLang(Integer filmId, Integer seriesNum, String destLang) {
        if (filmId == null || seriesNum == null || StrUtil.isBlank(destLang)) {
            return null;
        }
        MakeSubtitleTranslateTaskPO query = new MakeSubtitleTranslateTaskPO();
        query.setFilmId(filmId);
        query.setSeriesNum(seriesNum);
        query.setLanguageCode(destLang);
        return queryOne(query);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeSubtitleTranslateTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeSubtitleTranslateTaskMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeSubtitleTranslateTaskPO> list) {
        if (makeSubtitleTranslateTaskMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    // @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeSubtitleTranslateTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeSubtitleTranslateTaskMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeSubtitleTranslateTaskPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeSubtitleTranslateTaskPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        makeSubtitleTranslateTaskMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public int updateStatus(Integer id, VideoProcessStatusEnum statusEnum) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleTranslateTaskPO taskPO = new MakeSubtitleTranslateTaskPO();
        taskPO.setId(id);
        taskPO.setStatus(statusEnum.getCode());
        return makeSubtitleTranslateTaskMapper.updateById(taskPO);
    }

    public int updateStatusAndStartTime(Integer id, VideoProcessStatusEnum statusEnum, String srcUrl, Date startTime) {
        if (id == null || statusEnum == null || StrUtil.isBlank(srcUrl)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleTranslateTaskPO taskPO = new MakeSubtitleTranslateTaskPO();
        taskPO.setId(id);
        taskPO.setStatus(statusEnum.getCode());
        taskPO.setSrcUrl(srcUrl);
        taskPO.setStartTime(startTime != null ? startTime : DateUtil.getNowDate());
        return makeSubtitleTranslateTaskMapper.updateById(taskPO);
    }

    public int updateStatusAndEndTime(Integer id, VideoProcessStatusEnum statusEnum, Date endTime) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleTranslateTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        MakeSubtitleTranslateTaskPO translateTaskUpdate = new MakeSubtitleTranslateTaskPO();
        translateTaskUpdate.setId(id);
        translateTaskUpdate.setStatus(statusEnum.getCode());
        translateTaskUpdate.setEndTime(endTime != null ? endTime : DateUtil.getNowDate());
        translateTaskUpdate.setTakeTime(DateUtil.getDiffTime(taskPO.getStartTime(), endTime));
        return makeSubtitleTranslateTaskMapper.updateById(translateTaskUpdate);
    }

    public int startBySeriesTaskIdAndLang(Integer seriesTaskId, String destLang, Date startTime) {
        if (seriesTaskId == null || StrUtil.isBlank(destLang)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleTranslateTaskPO translateTaskUpdate = new MakeSubtitleTranslateTaskPO();
        translateTaskUpdate.setSeriesTaskId(seriesTaskId);
        translateTaskUpdate.setLanguageCode(destLang);
        translateTaskUpdate.setStatus(VideoProcessStatusEnum.PROCESSING.getCode());
        translateTaskUpdate.setStartTime(startTime != null ? startTime : DateUtil.getNowDate());
        return makeSubtitleTranslateTaskMapper.updateStartTimeBySeriesTaskIdAndLang(translateTaskUpdate);
    }

    public int fillUrlBySeriesTaskIdAndLang(Integer seriesTaskId, String destLang, String url, Date endTime) {
        if (seriesTaskId == null || StrUtil.isBlank(destLang) || StrUtil.isBlank(url)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleTranslateTaskPO translateTask = queryBySeriesTaskIdAndLang(seriesTaskId, destLang);
        if (translateTask == null) {
            throw new MyException("Task not found with seriesTaskId: " + seriesTaskId);
        }
        MakeSubtitleTranslateTaskPO translateTaskUpdate = new MakeSubtitleTranslateTaskPO();
        translateTaskUpdate.setSeriesTaskId(seriesTaskId);
        translateTaskUpdate.setLanguageCode(destLang);
        translateTaskUpdate.setUrl(url);
        translateTaskUpdate.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        translateTaskUpdate.setEndTime(endTime != null ? endTime : DateUtil.getNowDate());
        translateTaskUpdate.setTakeTime(DateUtil.getDiffTime(translateTask.getStartTime(), translateTaskUpdate.getEndTime()));
        return makeSubtitleTranslateTaskMapper.updateUrlBySeriesTaskIdAndLang(translateTaskUpdate);
    }

    public int updateSuccess(Integer id, Date endTime, String url) {
        if (id == null || endTime == null || StrUtil.isBlank(url)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleTranslateTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        MakeSubtitleTranslateTaskPO translateTaskUpdate = new MakeSubtitleTranslateTaskPO();
        translateTaskUpdate.setId(id);
        translateTaskUpdate.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        translateTaskUpdate.setEndTime(endTime);
        translateTaskUpdate.setTakeTime(DateUtil.getDiffTime(taskPO.getStartTime(), endTime));
        translateTaskUpdate.setUrl(url);
        return makeSubtitleTranslateTaskMapper.updateById(translateTaskUpdate);
    }

    public int updateFail(Integer id, Date endTime, String msg) {
        if (id == null || endTime == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeSubtitleTranslateTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        MakeSubtitleTranslateTaskPO translateTaskUpdate = new MakeSubtitleTranslateTaskPO();
        translateTaskUpdate.setId(id);
        translateTaskUpdate.setStatus(VideoProcessStatusEnum.FAILED.getCode());
        translateTaskUpdate.setEndTime(endTime);
        translateTaskUpdate.setTakeTime(DateUtil.getDiffTime(taskPO.getStartTime(), endTime));
        translateTaskUpdate.setErrorMessage(msg);
        return makeSubtitleTranslateTaskMapper.updateById(translateTaskUpdate);
    }

    /**
     * 执行字幕翻译业务逻辑
     */
    public void executeTranslate(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {
        log.info("开始字幕翻译: batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());

        // 校验字幕提取，因为原始srt文件是从这里生成的
        MakeSubtitleExtractTaskPO extractTask = makeSubtitleExtractTaskServiceV2.queryBySeriesTaskId(seriesTask.getId());
        if (extractTask == null || !Objects.equals(extractTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
            log.warn("剧集任务 {} 的字幕提取任务未完成，跳过翻译和压制, batchTaskId={}", seriesTask.getId(), batchTask.getId());
            return;
        }

        // 源语言之外的其他语种，都需要基于英文字母来翻译
        String enSrtUrl;

        // 如果源语言就是英文，那这里不会有英文任务
        if (!StrUtil.equals(batchTask.getSourceLang(), LanguageEnum.ENGLISH.getCode())) {
            // 英文字幕任务
            MakeSubtitleTranslateTaskPO enTranslateTask = queryBySeriesTaskIdAndLang(seriesTask.getId(), LanguageEnum.ENGLISH.getCode());
            if (enTranslateTask == null) {
                log.warn("英文字幕翻译任务不存在，无法进行后续翻译，任务前置环节异常，请检查！！！ batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());
                return;
            }
            // 若是首次，则更改状态为处理中
            if (Objects.equals(enTranslateTask.getStatus(), VideoProcessStatusEnum.PENDING.getCode())) {
                // 取个巧
                enTranslateTask.setStartTime(DateUtil.getNowDate());
                updateStatusAndStartTime(enTranslateTask.getId(), VideoProcessStatusEnum.PROCESSING, extractTask.getUrl(), DateUtil.getNowDate());
                log.info("更新英文字幕翻译任务状态为处理中: batchTaskId={}, seriesTaskId={}, translateTaskId={}", batchTask.getId(), seriesTask.getId(), enTranslateTask.getId());
            }

            if (Objects.equals(enTranslateTask.getStatus(), VideoProcessStatusEnum.PROCESSING.getCode())) {
                log.warn("英文字幕翻译任务正在处理中，直接跳过本次翻译，batchTaskId={}, seriesTaskId={}, translateTaskId={}", batchTask.getId(), seriesTask.getId(), enTranslateTask.getId());
                return;
            }

            // 如果之前执行失败，那在这里重试
            if (!Objects.equals(enTranslateTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
                log.warn("英文字幕翻译任务未完成，状态: {}，触发翻译重试，batchTaskId={}, seriesTaskId={}, translateTaskId={}", enTranslateTask.getStatus(), batchTask.getId(), seriesTask.getId(), enTranslateTask.getId());
                enSrtUrl = translateAndWrite(batchTask, seriesTask, enTranslateTask, extractTask.getUrl(), 0);
            } else {
                enSrtUrl = enTranslateTask.getUrl();
            }
        } else {
            // 如果源语言就是英文，那直接使用提取的srt文件，这个数据可能是文件上传的
            enSrtUrl = extractTask.getUrl();
            log.info("源语言是英文，不用再走翻译，直接使用ExtractTask中的字幕文件，batchTaskId={}, seriesTaskId={}, srtUrl={}", batchTask.getId(), seriesTask.getId(), enSrtUrl);
        }

        // 兜底校验一下
        if (StrUtil.isBlank(enSrtUrl)) {
            log.warn("英文字幕翻译任务翻译失败，无法进行后续翻译，batchTaskId={}, seriesTaskId={}", batchTask.getId(), seriesTask.getId());
            return;
        }

        // 开始翻译流程
        String destLangs = batchTask.getDestLangs();

        // 其他语言字幕需要建立在英文字幕基础之上
        for (String lang : destLangs.split(",")) {
            // 此时，英文已经处理好了
            if (StrUtil.equals(lang, LanguageEnum.ENGLISH.getCode())) {
                continue;
            }
            MakeSubtitleTranslateTaskPO otherTranslateTask = queryBySeriesTaskIdAndLang(seriesTask.getId(), lang);
            if (otherTranslateTask == null) {
                log.warn("字幕翻译任务不存在，无法进行翻译，任务前置环节异常，请检查！！！ batchTaskId={}, seriesTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), lang);
                continue;
            }
            // 若是首次，则更改状态为处理中
            if (Objects.equals(otherTranslateTask.getStatus(), VideoProcessStatusEnum.PENDING.getCode())) {
                // 取个巧
                otherTranslateTask.setStartTime(DateUtil.getNowDate());
                updateStatusAndStartTime(otherTranslateTask.getId(), VideoProcessStatusEnum.PROCESSING, enSrtUrl, DateUtil.getNowDate());
                log.info("更新英文字幕翻译任务状态为处理中: batchTaskId={}, seriesTaskId={}, translateTaskId={}", batchTask.getId(), seriesTask.getId(), otherTranslateTask.getId());
            }
            if (Objects.equals(otherTranslateTask.getStatus(), VideoProcessStatusEnum.PROCESSING.getCode())) {
                log.warn("字幕翻译任务正在处理中，直接跳过本次翻译，batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), otherTranslateTask.getId(), lang);
                continue;
            }
            if (Objects.equals(otherTranslateTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
                log.warn("字幕翻译任务 {} 已经完成，跳过本次翻译，触发下一步操作：压制，batchTaskId={}, seriesTaskId={}, destLang={}", otherTranslateTask.getId(), batchTask.getId(), seriesTask.getId(), lang);
                translateSuccess(batchTask, seriesTask, otherTranslateTask, lang, otherTranslateTask.getUrl());
                continue;
            }
            // 开始翻译流程
            vThreadManager.executeTaskWithMDC(() -> translateAndWrite(batchTask, seriesTask, otherTranslateTask, enSrtUrl, 0));
        }
    }

    /**
     * 翻译字幕，并将翻译之后的srt上传到oss
     */
    public String translateAndWrite(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, MakeSubtitleTranslateTaskPO translateTask, String srtUrl, int retryCount) {
        String destLang = translateTask.getLanguageCode();

        log.info("执行剧集子任务 {} 的 {} 字幕翻译: batchTaskId={}, translateTaskId={}, destLang={}", seriesTask.getId(), translateTask.getLanguageCode(), batchTask.getId(), translateTask.getId(), destLang);

        // 获取许可证
        ThirdPartyApiRateLimiter.acquireCoze();
        log.info("ConCurrent许可证获取成功，开始翻译字幕: batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), translateTask.getId(), destLang);

        try {
            JSONObject res;
            if (Objects.equals(destLang, LanguageEnum.ENGLISH.getCode())) {
                res = cozeTranslateUtil.translateZh2En(srtUrl, null);
            } else {
                res = cozeTranslateUtil.translateEn2Any(destLang, srtUrl, null);
            }

            if (res == null) {
                log.warn("翻译API返回空结果: batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), translateTask.getId(), destLang);
                translateFail(batchTask, seriesTask, translateTask, "翻译API返回空结果", DateUtil.getNowDate());
                return null;
            }

            Integer code = res.getInteger("code");
            String msg = res.getString("msg");

            if (code != 0) {
                log.warn("翻译任务执行失败: batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}, errorMsg={}", batchTask.getId(), seriesTask.getId(), translateTask.getId(), destLang, msg);
                translateFail(batchTask, seriesTask, translateTask, msg, DateUtil.getNowDate());
                return null;
            }

            JSONObject data = res.getJSONObject("data");
            String output = data.getString("output");

            if (StrUtil.isBlank(output)) {
                log.warn("翻译API返回结果中output为空: batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), translateTask.getId(), destLang);
                translateFail(batchTask, seriesTask, translateTask, "翻译API返回结果中output为空", DateUtil.getNowDate());
                return null;
            }

            // 生成文件名
            String fileName = String.format("Subtitle_%d_%d_%s." + StrUtil.substringAfterLast(srtUrl, "."), seriesTask.getId(), seriesTask.getSeriesNum(), destLang);
            // 这里需要写入修复之后的srt内容
            String fixedOutput = SrtProcessor.fixSrt(output);
            if (!StrUtil.isBlank(fixedOutput)) {
                output = fixedOutput;
            }
            String path = FileUtil.writeContentToFile(output, fileName, null, tmpPath + "srt");

            if (!FileUtil.checkFileExists(path)) {
                log.warn("翻译生成的字幕文件不存在，可能写入磁盘失败: batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}, path={}", batchTask.getId(), seriesTask.getId(), translateTask.getId(), destLang, path);
                translateFail(batchTask, seriesTask, translateTask, "翻译生成的字幕文件不存在，可能写入磁盘失败", DateUtil.getNowDate());
                return null;
            }

            // 校验coze返回的字幕文件，经常会有问题，给一次重试机会
            // 只有三次重试机会
            if (retryCount > 3) {
                translateFail(batchTask, seriesTask, translateTask, "翻译生成的字幕文件格式不正确，已重试3次均为失败", DateUtil.getNowDate());
                return null;
            }
            // 下载原字幕到本地，与coze返回的翻译字幕进行条数比对
            String srcFileName = StrUtil.getRandomFileName() + "." + StrUtil.substringAfterLast(srtUrl, ".");
            String srcFilePath = tmpPath + "srt/" + srcFileName;
            String srcPath = FileDownloader.downloadFile(srtUrl, srcFileName, srcFilePath);
            // 下载失败就不处理
            if (!StrUtil.isBlank(srcPath)) {
                // 校验字幕条目数是否一致
                boolean success = SrtValidator.validateSrtBol(srcPath, path);
                if (!success) {
                    log.warn("翻译生成的字幕文件与原始文件条目数不一致，触发第{}次翻译重试: batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), translateTask.getId(), destLang, retryCount + 1);
                    return translateAndWrite(batchTask, seriesTask, translateTask, srtUrl, retryCount + 1);
                }
                // 删除临时文件
                FileUtil.delFile(srcPath);
            }

            // 上传到OSS
            String ossUrl = ossService.uploadFile(false, 0, 5, new File(path), destLang + "/");
            if (StrUtil.isBlank(ossUrl)) {
                log.warn("OSS上传失败: batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), translateTask.getId(), destLang);
                translateFail(batchTask, seriesTask, translateTask, "OSS上传失败", DateUtil.getNowDate());
                return null;
            }
            log.info("翻译任务 {} 处理完毕，上传到OSS成功，本地磁盘路径: {}, ossUrl: {}, batchTaskId={}, seriesTaskId={}, destLang={}", translateTask.getId(), path, ossUrl, batchTask.getId(), seriesTask.getId(), destLang);

            translateSuccess(batchTask, seriesTask, translateTask, destLang, ossUrl);
            return ossUrl;
        } catch (Exception e) {
            log.error("翻译任务执行失败: batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), translateTask.getId(), destLang, e);
            translateFail(batchTask, seriesTask, translateTask, e.getMessage(), DateUtil.getNowDate());
            return null;
        } finally {
            // 释放许可证
            ThirdPartyApiRateLimiter.releaseCoze();
            log.info("翻译任务执行完毕，释放ConCurrent许可证: batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), translateTask.getId(), destLang);
        }
    }

    /**
     * 字幕翻译成功，coze工作流是同步接口，导致事务超时，触发大事务问题，这里使用编程式事务
     *
     * @param batchTask     批量任务
     * @param seriesTask    剧集子任务
     * @param translateTask 翻译任务
     * @param destLang      目标语言
     * @param ossUrl        翻译后字幕的OSS地址
     */
    public void translateSuccess(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, MakeSubtitleTranslateTaskPO translateTask, String destLang, String ossUrl) {
        transactionTemplate.executeWithoutResult((status) -> {
            // 规避重复处理
            if (!Objects.equals(translateTask.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode())) {
                updateSuccess(translateTask.getId(), DateUtil.getNowDate(), ossUrl);
                log.info("翻译任务执行成功, batchTaskId={}, seriesTaskId={}, translateTaskId={}, destLang={}， 更改状态为 已完成", batchTask.getId(), seriesTask.getId(), translateTask.getId(), destLang);
            }
            // 同步补齐成片字幕数据
            makeFilmCaptionService.fillUrlByFilmAndSeriesNumAndLang(batchTask.getFilmId(), FilmTypeEnum.COMPLETED.getCode(), seriesTask.getSeriesNum(), translateTask.getLanguageCode(), ossUrl, CaptionTypeEnum.COZE.getCode());
            log.info("字幕翻译任务 {} 处理完毕，同步补齐成片 {} 源语言的字幕数据，batchTaskId={}, seriesTaskId={}, destLang={}", translateTask.getId(), batchTask.getFilmId(), batchTask.getId(), seriesTask.getId(), destLang);

            // 同步补齐原片字幕数据
            Integer originalFilmId = makeCompletedFilmService.queryOriginalFilmId(batchTask.getFilmId());
            if (originalFilmId == null) {
                log.warn("未找到原片ID，无法同步补齐原片字幕数据，batchTaskId={}, seriesTaskId={}, destLang={}", batchTask.getId(), seriesTask.getId(), destLang);
            } else {
                makeFilmCaptionService.initOriginalByFilmAndSeriesNumAndLang(originalFilmId, seriesTask.getSeriesNum(), destLang, ossUrl, CaptionTypeEnum.COZE.getCode());
                log.info("字幕翻译任务 {} 处理完毕，同步补齐原片 {} 源语言的字幕数据，batchTaskId={}, seriesTaskId={}, destLang={}", translateTask.getId(), originalFilmId, batchTask.getId(), seriesTask.getId(), destLang);
            }

            // 统计剧集子任务下的翻译任务完成情况
            boolean completed = checkAndmarkTranslateCompleted(seriesTask.getId());
            if (completed) {
                log.info("剧集子任务 {} 下的所有语言字幕翻译完毕，更新剧集子任务 {} 的字幕翻译标记为 true，batchTaskId={}", seriesTask.getId(), seriesTask.getId(), batchTask.getId());
                // 这里的标记先打上，后续step需要用到，可以避免频繁查询
                seriesTask.setTranslateCompleted(YesOrNoEnum.YES.getCode());
            }

            // 同步更新成片完成数量
            makeVideoBatchTaskService.syncCompletedCountAndEndTime(batchTask.getId(), DateUtil.getNowDate());
            log.info("翻译任务 {} 处理完毕，处理完成总数量+1, batchTaskId={}, seriesTaskId={}, destLang={}", translateTask.getId(), batchTask.getId(), seriesTask.getId(), destLang);
        });

        // 触发下一步流程：视频压制
        videoProcessFlowManager.executeNextStep(VideoProcessStepEnum.SUBTITLE_TRANSLATE, batchTask, seriesTask);
        log.info("字幕翻译任务 {} 完成，触发压制，batchTaskId={}, seriesTaskId={}", translateTask.getId(), batchTask.getId(), seriesTask.getId());
    }

    /**
     * 字幕翻译失败，coze工作流是同步接口，导致事务超时，触发大事务问题，这里使用编程式事务
     *
     * @param batchTask     批量任务
     * @param seriesTask    剧集子任务
     * @param translateTask 翻译任务
     * @param msg           错误原因
     * @param endTime       任务结束时间
     */
    public void translateFail(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask, MakeSubtitleTranslateTaskPO translateTask, String msg, Date endTime) {
        transactionTemplate.executeWithoutResult((status) -> {
            updateFail(translateTask.getId(), endTime, msg);
            log.warn("更新字幕翻译任务 {} 的状态为 失败，并录入失败原因，batchTaskId={}, seriesTaskId={}, destLang={}", translateTask.getId(), batchTask.getId(), seriesTask.getId(), translateTask.getLanguageCode());

            // 更新剧集子任务状态为失败
            makeVideoSeriesTaskService.updateStatusAndEndTime(seriesTask.getId(), VideoProcessStatusEnum.FAILED, endTime);
            log.warn("更新剧集子任务 {} 的状态为 失败，batchTaskId={}, destLang={}", seriesTask.getId(), batchTask.getId(), translateTask.getLanguageCode());

            // 更新失败数量
            makeVideoBatchTaskService.syncFailedCount(batchTask.getId());
            log.warn("增加批量任务 {} 的处理失败数量+1", batchTask.getId());
        });
    }

}
