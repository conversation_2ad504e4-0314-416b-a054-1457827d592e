package com.fast.service.make.video.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fast.enums.LanguageEnum;
import com.fast.framework.config.VideoProcessConfig;
import com.fast.framework.exception.MyException;
import com.fast.utils.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 字幕翻译服务 - 虚拟线程版本
 * <p>
 * 核心特点：
 * 1. 虚拟线程中使用同步HTTP调用
 * 2. 并发控制保护第三方API
 * 3. 简化的错误处理和重试机制
 */
@Service
@Slf4j
public class CozeTranslateUtil {

    @Autowired
    private HttpClient httpClient;

    @Autowired
    private VideoProcessConfig videoProcessConfig;

    /**
     * 从URL读取SRT文件内容
     */
    public String readSrtFromUrl(String srtUrl) {
        log.info("开始读取SRT文件: url={}", srtUrl);

        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(srtUrl))
                    .timeout(Duration.ofMinutes(2))
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                throw new MyException("读取SRT文件失败，状态码: " + response.statusCode());
            }

            String content = response.body();
            log.info("SRT文件读取成功，内容长度: {}", content.length());

            return content;
        } catch (Exception e) {
            log.error("读取SRT文件失败: url={}", srtUrl, e);
            throw new MyException("读取SRT文件失败: " + e.getMessage());
        }
    }

    public JSONObject translateZh2En(String url, String text) {
        log.info("提交 中文 => 英文 翻译: url={}", url);

        String workflowId = videoProcessConfig.getCoze().getZh2enWorkflowId();
        String lang = "英文";

        if (text == null && url != null) {
            text = readSrtFromUrl(url);
            // log.info("srt文件内容: {}", text);
        }

        // 跟产品确认，失败重试3次
        JSONObject result = null;
        for (int i = 0; i < 3; i++) {
            if (i > 0) {
                log.warn("第 {} 次重试翻译: url={}", i + 1, url);
            }
            result = translate(workflowId, lang, text);
            if (isSuccess(result)) {
                break;
            }
        }
        return result;
    }

    public JSONObject translateEn2Any(String lang, String url, String text) {
        log.info("提交 英文 => {} 翻译: url={}", lang, url);

        String workflowId = videoProcessConfig.getCoze().getEn2anyWorkflowId();

        if (text == null && url != null) {
            text = readSrtFromUrl(url);
            // log.info("srt文件内容: {}", text);
        }

        // 跟产品确认，失败重试3次
        JSONObject result = null;
        for (int i = 0; i < 3; i++) {
            if (i > 0) {
                log.warn("第 {} 次重试翻译: url={}", i + 1, url);
            }
            result = translate(workflowId, LanguageEnum.ofCode(lang).getName(), text);
            if (isSuccess(result)) {
                break;
            }
        }
        return result;
    }

    /**
     * 同步翻译（在虚拟线程中执行）
     */
    private JSONObject translate(String workflowId, String destLang, String subtitleContent) {
        try {
            String url = videoProcessConfig.getCoze().getApiUrl();
            String body = buildRequestBody(workflowId, destLang, subtitleContent);

            log.info("[ url]: {}", url);
            log.info("[body]: {}", body);

            long start = System.currentTimeMillis();

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + videoProcessConfig.getCoze().getApiToken())
                    .timeout(Duration.ofMinutes(5))
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            String res = response.body();

            log.info("[ res]: {}", res);

            long end = System.currentTimeMillis();
            log.info("[time]: {}s", (end - start) / 1000f);

            return JSON.parseObject(res);
        } catch (Exception e) {
            log.error("Coze API请求失败", e);
            return null;
        }
    }

    /**
     * 构建请求体
     */
    private String buildRequestBody(String workflowId, String destLang, String subtitleContent) {
        Map<String, Object> body = new HashMap<>();
        body.put("workflow_id", workflowId);

        Map<String, Object> parameters = new HashMap<>();
        if (!StrUtil.equals(destLang, "英文")) {
            parameters.put("input_language", destLang);
        }
        parameters.put("input", subtitleContent);
        body.put("parameters", parameters);

        return JSON.toJSONString(body);
    }

    /**
     * 解析响应
     */
    private String parseResponse(JSONObject res) {
        try {
            if (res == null) {
                log.error("Coze翻译响应为空");
                return null;
            }
            Integer code = res.getInteger("code");
            String msg = res.getString("msg");
            if (code != 0) {
                return null;
            }
            JSONObject data = res.getJSONObject("data");
            return data.getString("output");
        } catch (Exception e) {
            log.error("解析Coze翻译响应失败: {}", res, e);
            return null;
        }
    }

    private boolean isSuccess(JSONObject result) {
        if (result == null) {
            return false;
        }
        Integer code = result.getInteger("code");
        return code == 0;
    }

}
