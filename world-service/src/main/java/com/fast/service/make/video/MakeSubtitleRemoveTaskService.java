// /*
//  * Powered By fast.up
//  */
// package com.fast.service.make.video;
//
// import com.alibaba.fastjson.JSONArray;
// import com.alibaba.fastjson.JSONObject;
// import com.fast.constant.StaticStr;
// import com.fast.enums.video.VideoProcessStatusEnum;
// import com.fast.framework.config.VideoProcessConfig;
// import com.fast.framework.exception.MyException;
// import com.fast.mapper.make.video.MakeSubtitleRemoveTaskMapper;
// import com.fast.po.make.video.MakeSubtitleRemoveTaskPO;
// import com.fast.po.make.video.MakeVideoBatchTaskPO;
// import com.fast.po.make.video.MakeVideoSeriesTaskPO;
// import com.fast.service.base.BaseService;
// import com.fast.service.make.video.util.GhostCutApiUtil;
// import com.fast.utils.DateUtil;
// import com.fast.utils.StrUtil;
// import com.fast.vo.MethodVO;
// import com.fast.vo.PageVO;
// import com.fast.vo.ResultVO;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;
//
// import java.util.Date;
// import java.util.List;
//
// /**
//  * <AUTHOR>
//  */
// @Service
// @Slf4j
// public class MakeSubtitleRemoveTaskService extends BaseService {
//
//     @Autowired
//     private MakeSubtitleRemoveTaskMapper makeSubtitleRemoveTaskMapper;
//
//     @Autowired
//     private GhostCutApiUtil ghostCutApiUtil;
//
//     @Autowired
//     private VideoProcessConfig videoProcessConfig;
//
//     /**
//      * 通过id查询单个对象
//      */
//     public MakeSubtitleRemoveTaskPO queryById(MakeSubtitleRemoveTaskPO params) {
//         return makeSubtitleRemoveTaskMapper.queryById(params);
//     }
//
//     /**
//      * 通过id查询单个对象
//      */
//     public MakeSubtitleRemoveTaskPO queryById(Integer id) {
//         return makeSubtitleRemoveTaskMapper.queryById(id);
//     }
//
//     /**
//      * 通过条件查询单个对象
//      */
//     public MakeSubtitleRemoveTaskPO queryOne(MakeSubtitleRemoveTaskPO params) {
//         return makeSubtitleRemoveTaskMapper.queryOne(params);
//     }
//
//     /**
//      * 查询全部
//      */
//     public List<MakeSubtitleRemoveTaskPO> queryList(MakeSubtitleRemoveTaskPO params) {
//         return makeSubtitleRemoveTaskMapper.queryList(params);
//     }
//
//     /**
//      * 查询全部(分页)
//      */
//     public ResultVO<?> queryPageList(MakeSubtitleRemoveTaskPO params, PageVO pageVO) {
//         startPage(pageVO);
//         List<MakeSubtitleRemoveTaskPO> list = makeSubtitleRemoveTaskMapper.queryList(params);
//         for (MakeSubtitleRemoveTaskPO cur : list) {
//             cur.setEncryptionId(encode(cur.getId()));
//         }
//         return ResultVO.success(getPageListData(list, pageVO));
//     }
//
//     /**
//      * 查询总数
//      */
//     public Integer queryCount(MakeSubtitleRemoveTaskPO params) {
//         Integer count = makeSubtitleRemoveTaskMapper.queryCount(params);
//         return count == null ? 0 : count;
//     }
//
//     public MakeSubtitleRemoveTaskPO queryBySeriesTaskId(Integer seriesTaskId) {
//         if (seriesTaskId == null) {
//             return null;
//         }
//         MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
//         query.setSeriesTaskId(seriesTaskId);
//         return queryOne(query);
//     }
//
//     public List<MakeSubtitleRemoveTaskPO> queryByThirdPartyId(String batchId, String taskId) {
//         if (StrUtil.isBlank(batchId) || StrUtil.isBlank(taskId)) {
//             return null;
//         }
//         MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
//         query.setThirdPartyBatchId(batchId);
//         query.setThirdPartyTaskId(taskId);
//         return queryList(query);
//     }
//
//     public List<MakeSubtitleRemoveTaskPO> queryByFilmId(Integer filmId) {
//         if (filmId == null) {
//             return null;
//         }
//         MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
//         query.setFilmId(filmId);
//         return queryList(query);
//     }
//
//     public MakeSubtitleRemoveTaskPO queryByFilmIdAndSeriesNum(Integer filmId, Integer seriesNum) {
//         if (filmId == null || seriesNum == null) {
//             return null;
//         }
//         MakeSubtitleRemoveTaskPO query = new MakeSubtitleRemoveTaskPO();
//         query.setFilmId(filmId);
//         query.setSeriesNum(seriesNum);
//         return queryOne(query);
//     }
//
//     /**
//      * 新增
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO insert(MakeSubtitleRemoveTaskPO params) {
//         Date nowTime = DateUtil.getNowDate();
//         params.setCreateTime(nowTime);
//         params.setUpdateTime(nowTime);
//         if (makeSubtitleRemoveTaskMapper.insertSelective(params) == 0) {
//             transactionRollBack();
//             return MethodVO.error(StaticStr.ADD_FAILED);
//         }
//         return MethodVO.success();
//     }
//
//     /**
//      * 批量新增
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO insertBatch(List<MakeSubtitleRemoveTaskPO> list) {
//         if (makeSubtitleRemoveTaskMapper.insertBatch(list) > 0) {
//             return MethodVO.success();
//         } else {
//             return MethodVO.error(StaticStr.ADD_FAILED);
//         }
//     }
//
//     /**
//      * 更新
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO update(MakeSubtitleRemoveTaskPO params) {
//         Date nowTime = DateUtil.getNowDate();
//         params.setUpdateTime(nowTime);
//         if (makeSubtitleRemoveTaskMapper.updateById(params) == 0) {
//             transactionRollBack();
//             return MethodVO.error(StaticStr.UPDATE_FAILED);
//         }
//         return MethodVO.success();
//     }
//
//     /**
//      * 删除
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public MethodVO delete(MakeSubtitleRemoveTaskPO params) {
//         if (params.getId() == null) {
//             return MethodVO.error(StaticStr.DELETE_FAILED);
//         }
//         MakeSubtitleRemoveTaskPO po = queryById(params.getId());
//         if (po == null) {
//             return MethodVO.error("data not exist");
//         }
//         makeSubtitleRemoveTaskMapper.deleteById(po.getId());
//         return MethodVO.success();
//     }
//
//     public int updateStatus(Integer id, Integer status) {
//         if (id == null || status == null) {
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//         MakeSubtitleRemoveTaskPO taskPO = new MakeSubtitleRemoveTaskPO();
//         taskPO.setId(id);
//         taskPO.setStatus(status);
//         return makeSubtitleRemoveTaskMapper.updateById(taskPO);
//     }
//
//     public int updateStatus(Integer id, VideoProcessStatusEnum statusEnum) {
//         if (id == null || statusEnum == null) {
//             throw new MyException(StaticStr.INVALID_PARAM);
//         }
//         return updateStatus(id, statusEnum.getCode());
//     }
//
//     /**
//      * 执行字幕擦除业务逻辑
//      */
//     @Transactional(rollbackFor = Exception.class)
//     public void executeRemove(MakeVideoBatchTaskPO batchTask, MakeVideoSeriesTaskPO seriesTask) {
//         log.info("开始字幕擦除: batchTaskId={}", batchTask.getId());
//
//         JSONObject response = ghostCutApiUtil.subtitleRemove(
//                 List.of(seriesTask.getOriginalVideoUrl()),
//                 List.of("[" + batchTask.getFilmId() + "]字幕擦除_" + batchTask.getId() + "_" + seriesTask.getId() + "_" + seriesTask.getSeriesNum()),
//                 videoProcessConfig.buildSubtitleRemoveCallback(batchTask.getId()),
//                 batchTask.getRegion());
//         log.info("鬼手字幕擦除api请求结果: {}", response.toJSONString());
//
//         Integer code = response.getInteger("code");
//         if (code == null || code != 1000) {
//             throw new MyException("字幕擦除API调用失败: " + response.getString("msg"));
//         }
//
//         JSONObject body = response.getJSONObject("body");
//         String idProject = body.getString("idProject");
//         JSONArray dataList = body.getJSONArray("dataList");
//
//         for (int i = 0; i < dataList.size(); i++) {
//             JSONObject item = dataList.getJSONObject(i);
//
//             MakeSubtitleRemoveTaskPO removeTask = new MakeSubtitleRemoveTaskPO();
//             removeTask.setSeriesTaskId(seriesTask.getId());
//             removeTask.setFilmId(batchTask.getFilmId());
//             removeTask.setSeriesNum(seriesTask.getSeriesNum());
//             removeTask.setThirdPartyBatchId(idProject);
//             removeTask.setThirdPartyTaskId(item.getString("id"));
//             removeTask.setVideoUrl(seriesTask.getOriginalVideoUrl());
//             removeTask.setStatus(VideoProcessStatusEnum.PROCESSING.getCode());
//             removeTask.setStartTime(DateUtil.getNowDate());
//             insert(removeTask);
//             log.info("剧集任务 {} 新增字幕擦除任务 {} 状态为 处理中", seriesTask.getId(), removeTask.getId());
//         }
//     }
//
// }
