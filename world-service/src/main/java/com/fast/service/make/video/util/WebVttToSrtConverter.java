package com.fast.service.make.video.util;

import java.io.*;
import java.nio.charset.StandardCharsets;

public class WebVttToSrtConverter {

    public static void convert(File inputFile, File outputFile) throws IOException {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                new FileInputStream(inputFile), StandardCharsets.UTF_8));
             BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                     new FileOutputStream(outputFile), StandardCharsets.UTF_8))) {

            String line;
            boolean isFirstLine = true;
            int lineCount = 0;
            boolean skipNextEmpty = false;

            while ((line = reader.readLine()) != null) {
                // 跳过BOM（字节顺序标记）如果存在
                if (isFirstLine) {
                    line = line.replace("\uFEFF", "");
                    isFirstLine = false;
                }

                // 跳过WEBVTT文件头
                if (line.equals("WEBVTT") || line.startsWith("WEBVTT ")) {
                    skipNextEmpty = true;
                    continue;
                }

                // 跳过文件头后的空行
                if (skipNextEmpty && line.trim().isEmpty()) {
                    skipNextEmpty = false;
                    continue;
                }

                // 跳过注释行
                if (line.startsWith("NOTE")) {
                    continue;
                }

                // 处理时间码行：替换.为,
                if (line.contains("-->")) {
                    line = line.replace('.', ',');
                    // 确保时间码格式正确（毫秒部分三位数）
                    line = formatTimeCode(line);
                }

                // 移除样式标签
                line = removeWebVttTags(line);

                // 处理标识符行（位于序号和时间码之间的行）
                if (lineCount % 4 == 1 && !line.contains("-->")) {
                    continue; // 跳过标识符行
                }

                // 写入处理后的行
                writer.write(line);
                writer.newLine();

                // 增加行计数器（每4行为一个字幕块）
                lineCount = (line.trim().isEmpty()) ? 0 : lineCount + 1;
            }
        }
    }

    private static String formatTimeCode(String timeLine) {
        // 确保毫秒部分为三位数（SRT规范要求）
        return timeLine.replaceAll("(\\d{2}:\\d{2}:\\d{2}),(\\d{1,2})\\b", "$1,00$2")
                .replaceAll("(\\d{2}:\\d{2}:\\d{2}),(\\d{1,2})\\b", "$1,0$2");
    }

    private static String removeWebVttTags(String line) {
        // 移除简单标签：<b>, </b>, <i>, </i>, <u>, </u>
        line = line.replaceAll("</?[biu]>", "");

        // 移除CSS类标签：<c.someclass>text</c>
        line = line.replaceAll("</?c\\.\\w+>", "");

        // 移除语音标签：<v Speaker>text</v>
        line = line.replaceAll("</?v(\\s+.*?)?>", "");

        return line;
    }

    // 使用示例
    public static void main(String[] args) {
        File input = new File("C:\\Users\\<USER>\\Downloads\\1.vtt");
        File output = new File("C:\\Users\\<USER>\\Downloads\\1.srt");

        try {
            convert(input, output);
            System.out.println("转换成功完成！");
        } catch (IOException e) {
            System.err.println("转换出错: " + e.getMessage());
        }
    }
}