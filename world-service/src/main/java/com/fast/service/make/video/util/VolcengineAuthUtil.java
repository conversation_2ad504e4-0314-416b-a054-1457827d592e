package com.fast.service.make.video.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class VolcengineAuthUtil {

    private static final String CDN = "https://cdn.popreel.tv";
    private static final String PRIMARY_KEY = "f7f2dc3a8c2c4b7684021f29dad1cab7";
    private static final String SIGN_NAME = "auth_key";
    private static final String T_NAME = "t";
    private static final String UID = "0";

    private static String getMd5(String text) {
        MessageDigest hash = null;
        try {
            hash = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        hash.update(StandardCharsets.UTF_8.encode(text));
        return String.format("%032x", new BigInteger(1, hash.digest()));
    }

    private static String getRandomString(int length) {
        int leftLimit = 48; // numeral '0'
        int rightLimit = 122; // letter 'z'
        Random random = new Random();
        String str = random.ints(leftLimit, rightLimit + 1)
                .filter(i -> (i <= 57 || i >= 65) && (i <= 90 || i >= 97))
                .limit(length)
                .collect(StringBuilder::new, StringBuilder::appendCodePoint, StringBuilder::append)
                .toString();
        return str;
    }

    private static Map<String, String> splitUrl(String url) {
        String pattern = "^(http://|https://)?([^/?]+)(/[^?]*)?(\\?.*)?$";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(url);
        Map<String, String> result = new HashMap<>();
        String scheme = "", domain = "", uri = "", args = "";
        if (m.find()) {
            scheme = m.group(1) == null ? "http://" : m.group(1);
            domain = m.group(2) == null ? "" : m.group(2);
            uri = m.group(3) == null ? "/" : m.group(3);
            args = m.group(4) == null ? "" : m.group(4);
        }
        result.put("scheme", scheme);
        result.put("domain", domain);
        result.put("uri", uri);
        result.put("args", args);
        return result;
    }

    private static String genTypeAUrl(String url, String key, String signName, String uid, long ts) {

        Map<String, String> uriInfo = splitUrl(url);
        String scheme = uriInfo.get("scheme"), domain = uriInfo.get("domain"), uri = uriInfo.get("uri"), args = uriInfo.get("args");
        String rand = getRandomString(10);
        String text = String.format("%s-%d-%s-%s-%s", uri, ts, rand, uid, key);
        String hash = getMd5(text);
        String authArg = String.format("%s=%d-%s-%s-%s", signName, ts, rand, uid, hash);
        if (args == "") return String.format("%s%s%s?%s", scheme, domain, uri, authArg);
        else return String.format("%s%s%s%s&%s", scheme, domain, uri, args, authArg);
    }

    private static String genTypeBUrl(String url, String key, long ts) {
        Map<String, String> uriInfo = splitUrl(url);
        String scheme = uriInfo.get("scheme"), domain = uriInfo.get("domain"), uri = uriInfo.get("uri"), args = uriInfo.get("args");
        String tsStr = new SimpleDateFormat("yyyyMMddHHmm").format(ts * 1000);
        String text = String.format("%s%s%s", key, tsStr, uri);
        String hash = getMd5(text);
        return String.format("%s%s/%s/%s%s%s", scheme, domain, tsStr, hash, uri, args);
    }

    private static String genTypeCUrl(String url, String key, long ts) {
        Map<String, String> uriInfo = splitUrl(url);
        String scheme = uriInfo.get("scheme"), domain = uriInfo.get("domain"), uri = uriInfo.get("uri"), args = uriInfo.get("args");
        String hexts = Long.toHexString(ts);
        String text = String.format("%s%s%s", key, uri, hexts);
        String hash = getMd5(text);
        return String.format("%s%s/%s/%s%s%s", scheme, domain, hash, hexts, uri, args);
    }

    private static String genTypeDUrl(String url, String key, String signName, String timeName, long ts, int base) {
        Map<String, String> uriInfo = splitUrl(url);
        String scheme = uriInfo.get("scheme"), domain = uriInfo.get("domain"), uri = uriInfo.get("uri"), args = uriInfo.get("args");
        String tsStr = String.format("%d", ts);
        if (base == 16)
            tsStr = Long.toHexString(ts);
        String text = String.format("%s%s%s", key, uri, tsStr);
        String hash = getMd5(text);
        String authArg = String.format("%s=%s&%s=%d", signName, hash, timeName, ts);
        if (args == "") return String.format("%s%s%s?%s", scheme, domain, uri, authArg);
        else return String.format("%s%s%s%s&%s", scheme, domain, uri, args, authArg);
    }

    // GenTypeEUrl Genrate signed url by custom rule(eg.：key+domain+uri+timestamp)
    private static String genTypeEUrl(String url, String key, String signName, String timeName, long ts, int base) {
        Map<String, String> uriInfo = splitUrl(url);
        String scheme = uriInfo.get("scheme"), domain = uriInfo.get("domain"), uri = uriInfo.get("uri"), args = uriInfo.get("args");
        String tsStr = String.format("%d", ts);
        if (base == 16)
            tsStr = Long.toHexString(ts);
        String text = String.format("%s%s%s%s", key, domain, uri, tsStr);
        String hash = getMd5(text);
        String authArg = String.format("%s=%s&%s=%s", signName, hash, timeName, tsStr);
        if (args == "") return String.format("%s%s%s?%s", scheme, domain, uri, authArg);
        else return String.format("%s%s%s%s&%s", scheme, domain, uri, args, authArg);
    }

    public String getAuthUrl(String fileName) {
        long ts = System.currentTimeMillis() / 1000 + 3600;// 默认一小时过期
        return genTypeAUrl(CDN + "/" + fileName, PRIMARY_KEY, SIGN_NAME, UID, ts);
    }

    public static void main(String[] args) {

        String url = "https://cdn.popreel.tv/c2fa7ad1c4904985b959364708ddac16";
        String primaryKey = "f7f2dc3a8c2c4b7684021f29dad1cab7";
        String signName = "auth_key";
        String tName = "t";
        String uid = "0";
        long ts = System.currentTimeMillis() / 1000 + 3600;// 默认一小时过期

        System.out.println("OriginUrl:" + url);
        System.out.println("TypeA:" + genTypeAUrl(url, primaryKey, signName, uid, ts));
        System.out.println("TypeB:" + genTypeBUrl(url, primaryKey, ts));
        System.out.println("TypeC:" + genTypeCUrl(url, primaryKey, ts));
        System.out.println("TypeD:" + genTypeDUrl(url, primaryKey, signName, tName, ts, 10));
        System.out.println("TypeE:" + genTypeEUrl(url, primaryKey, signName, tName, ts, 10));
    }
}
