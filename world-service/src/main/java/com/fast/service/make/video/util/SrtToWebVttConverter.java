package com.fast.service.make.video.util;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SrtToWebVttConverter {
    public static void convert(File var0, File var1) throws IOException {
        try (
                BufferedReader var2 = new BufferedReader(new InputStreamReader(new FileInputStream(var0), StandardCharsets.UTF_8));
                BufferedWriter var3 = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(var1), StandardCharsets.UTF_8))
        ) {
            var3.write("WEBVTT");
            var3.newLine();
            var3.newLine();
            boolean var5 = true;
            StringBuilder var6 = new StringBuilder();
            byte var7 = 0;

            String var4;
            while ((var4 = var2.readLine()) != null) {
                if (var5) {
                    var4 = var4.replace("\ufeff", "");
                    var5 = false;
                }

                var4 = var4.trim();
                if (var4.isEmpty()) {
                    if (var7 == 2 && var6.length() > 0) {
                        var3.write(var6.toString());
                        var3.newLine();
                        var6.setLength(0);
                        var7 = 0;
                    }
                } else {
                    switch (var7) {
                        case 0:
                            if (isNumeric(var4)) {
                                var7 = 1;
                            }
                            break;
                        case 1:
                            if (var4.contains("-->")) {
                                String var8 = convertTimeCode(var4);
                                var6.append(var8);
                                var6.append("\n");
                                var7 = 2;
                            }
                            break;
                        case 2:
                            var6.append(var4);
                            var6.append("\n");
                    }
                }
            }

            if (var6.length() > 0) {
                var3.write(var6.toString());
                var3.newLine();
            }
        }

    }

    private static String convertTimeCode(String var0) {
        String var1 = var0.replace(',', '.');
        Pattern var2 = Pattern.compile("(\\d{2}:\\d{2}:\\d{2})\\.(\\d{1,3})");
        Matcher var3 = var2.matcher(var1);

        StringBuffer var4;
        String var5;
        String var6;
        for (var4 = new StringBuffer(); var3.find(); var3.appendReplacement(var4, var5 + "." + var6)) {
            var5 = var3.group(1);

            for (var6 = var3.group(2); var6.length() < 3; var6 = var6 + "0") {
            }

            if (var6.length() > 3) {
                var6 = var6.substring(0, 3);
            }
        }

        var3.appendTail(var4);
        return var4.toString();
    }

    private static boolean isNumeric(String var0) {
        if (var0 != null && !var0.isEmpty()) {
            try {
                Integer.parseInt(var0);
                return true;
            } catch (NumberFormatException var2) {
                return false;
            }
        } else {
            return false;
        }
    }

    public static void convert(File var0, File var1, ConvertOptions var2) throws IOException {
        try (
                BufferedReader var3 = new BufferedReader(new InputStreamReader(new FileInputStream(var0), StandardCharsets.UTF_8));
                BufferedWriter var4 = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(var1), StandardCharsets.UTF_8))
        ) {
            var4.write("WEBVTT");
            if (var2.title != null && !var2.title.isEmpty()) {
                var4.write(" - " + var2.title);
            }

            var4.newLine();
            var4.newLine();
            if (var2.includeStyles) {
                var4.write("STYLE");
                var4.newLine();
                var4.write("::cue {");
                var4.newLine();
                var4.write("  background-color: black;");
                var4.newLine();
                var4.write("  color: white;");
                var4.newLine();
                var4.write("}");
                var4.newLine();
                var4.newLine();
            }

            boolean var6 = true;
            StringBuilder var7 = new StringBuilder();
            byte var8 = 0;
            int var9 = 1;

            String var5;
            while ((var5 = var3.readLine()) != null) {
                if (var6) {
                    var5 = var5.replace("\ufeff", "");
                    var6 = false;
                }

                var5 = var5.trim();
                if (var5.isEmpty()) {
                    if (var8 == 2 && var7.length() > 0) {
                        var4.write(var7.toString());
                        var4.newLine();
                        var7.setLength(0);
                        var8 = 0;
                        ++var9;
                    }
                } else {
                    switch (var8) {
                        case 0:
                            if (isNumeric(var5)) {
                                if (var2.includeIdentifiers) {
                                    var7.append("subtitle_").append(var9);
                                    var7.append("\n");
                                }

                                var8 = 1;
                            }
                            break;
                        case 1:
                            if (var5.contains("-->")) {
                                String var10 = convertTimeCode(var5);
                                var7.append(var10);
                                var7.append("\n");
                                var8 = 2;
                            }
                            break;
                        case 2:
                            var7.append(var5);
                            var7.append("\n");
                    }
                }
            }

            if (var7.length() > 0) {
                var4.write(var7.toString());
                var4.newLine();
            }
        }

    }

    public static void main(String[] var0) {
        File var1 = new File("world-service/src/test/java/com/fast/service/video/Subtitle_296_1_EN_fixed.srt");
        File var2 = new File("Subtitle_296_1_EN_converted.vtt");

        try {
            System.out.println("=== SRT转WebVTT转换器测试 ===\n");
            System.out.println("1. 基本转换:");
            convert(var1, var2);
            System.out.println("   ✅ 基本转换完成！");
            System.out.println("   输入文件: " + var1.getName());
            System.out.println("   输出文件: " + var2.getName());
            System.out.println("\n2. 带选项的转换:");
            File var3 = new File("Subtitle_296_1_EN_with_options.vtt");
            ConvertOptions var4 = new ConvertOptions("测试字幕", true, true);
            convert(var1, var3, var4);
            System.out.println("   ✅ 带选项转换完成！");
            System.out.println("   输入文件: " + var1.getName());
            System.out.println("   输出文件: " + var3.getName());
            System.out.println("   选项: 包含标题、样式和标识符");
        } catch (IOException var5) {
            System.err.println("❌ 转换出错: " + var5.getMessage());
            var5.printStackTrace();
        }

    }

    public static class ConvertOptions {
        public String title = null;
        public boolean includeStyles = false;
        public boolean includeIdentifiers = false;

        public ConvertOptions() {
        }

        public ConvertOptions(String var1, boolean var2, boolean var3) {
            this.title = var1;
            this.includeStyles = var2;
            this.includeIdentifiers = var3;
        }
    }
}
