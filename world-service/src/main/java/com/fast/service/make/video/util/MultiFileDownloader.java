package com.fast.service.make.video.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 多文件下载并打包工具类
 * 基于FileDownloader实现多个URL文件的下载和ZIP打包功能
 */
@Component
@Slf4j
public class MultiFileDownloader {

    /**
     * 下载文件信息
     */
    @Data
    static class DownloadTask {
        private String url;
        private String fileName;
        private String localPath;
        private boolean success;
        private String errorMessage;

        public DownloadTask(String url, String fileName) {
            this.url = url;
            this.fileName = fileName;
            this.success = false;
        }
    }

    /**
     * 创建虚拟线程执行器（单例）
     */
    private final ExecutorService virtualThreadExecutor = Executors.newThreadPerTaskExecutor(
            Thread.ofVirtual()
                    .name("DownloadFile-VT-", 0)
                    .uncaughtExceptionHandler((thread, ex) -> log.error("虚拟线程 {} 异常: {}", thread.getName(), ExceptionUtils.getStackTrace(ex)))
                    .factory()
    );

    /**
     * 临时文件存储路径
     * 注意：实际部署时请修改为合适的路径
     */
    private static final String tmpPath = "/home/<USER>/";

    /**
     * 下载多个文件并打包成ZIP（使用虚拟线程）
     *
     * @param downloadTasks 下载任务列表
     * @param zipFilePath   ZIP文件保存路径
     * @param tempDir       临时文件夹路径（可为空，使用系统临时目录）
     * @return ZIP文件路径，失败返回null
     */
    public String downloadAndZip(List<DownloadTask> downloadTasks, String zipFilePath, String tempDir) {
        if (downloadTasks == null || downloadTasks.isEmpty()) {
            log.error("下载任务列表为空");
            return null;
        }

        // 创建临时目录
        String actualTempDir = createTempDirectory(tempDir);
        if (actualTempDir == null) {
            return null;
        }

        log.info("开始使用虚拟线程下载 {} 个文件到临时目录: {}", downloadTasks.size(), actualTempDir);

        try {
            // 使用虚拟线程并发下载文件
            boolean allSuccess = downloadFilesParallel(downloadTasks, actualTempDir);

            if (!allSuccess) {
                log.warn("部分文件下载失败，将继续打包已成功下载的文件");
            }

            // 获取成功下载的文件
            List<DownloadTask> successTasks = new ArrayList<>();
            for (DownloadTask task : downloadTasks) {
                if (task.isSuccess()) {
                    successTasks.add(task);
                }
            }

            if (successTasks.isEmpty()) {
                log.error("没有文件下载成功，无法创建ZIP包");
                return null;
            }

            // 创建ZIP文件
            String zipPath = createZipFile(successTasks, zipFilePath);

            // 清理临时文件
            cleanupTempFiles(successTasks);

            log.info("多文件下载打包完成，ZIP文件路径: {}, 成功文件数: {}/{}", zipPath, successTasks.size(), downloadTasks.size());

            return zipPath;

        } catch (Exception e) {
            log.error("多文件下载打包失败: {}", ExceptionUtils.getStackTrace(e));
            // 清理临时文件
            cleanupTempFiles(downloadTasks);
            return null;
        }
    }

    /**
     * 创建临时目录
     */
    private String createTempDirectory(String tempDir) {
        try {
            String actualTempDir;
            if (tempDir == null || tempDir.trim().isEmpty()) {
                actualTempDir = System.getProperty("java.io.tmpdir") + File.separator +
                        "multi_download_" + System.currentTimeMillis();
            } else {
                actualTempDir = tempDir;
            }

            Path tempPath = Paths.get(actualTempDir);
            if (!Files.exists(tempPath)) {
                Files.createDirectories(tempPath);
                log.info("创建临时目录: {}", actualTempDir);
            }

            return actualTempDir;
        } catch (Exception e) {
            log.error("创建临时目录失败: {}", ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    /**
     * 并发下载文件（使用虚拟线程）
     */
    private boolean downloadFilesParallel(List<DownloadTask> downloadTasks, String tempDir) {
        // 使用Stream + CompletableFuture实现优雅的并发下载
        List<CompletableFuture<Boolean>> futures = downloadTasks.stream()
                .map(task -> CompletableFuture.supplyAsync(() -> downloadSingleFile(task, tempDir), virtualThreadExecutor))
                .toList();

        // 等待所有任务完成并收集结果
        return futures.stream().allMatch(this::safeGet);
    }

    /**
     * 下载单个文件
     */
    private boolean downloadSingleFile(DownloadTask task, String tempDir) {
        try {
            String downloadedPath = FileDownloader.downloadFile(task.getUrl(), task.getFileName(), tempDir);

            if (downloadedPath != null) {
                task.setLocalPath(downloadedPath);
                task.setSuccess(true);
                log.info("文件下载成功: {} -> {}", task.getUrl(), downloadedPath);
                return true;
            } else {
                task.setErrorMessage("下载失败");
                log.error("文件下载失败: {}", task.getUrl());
                return false;
            }
        } catch (Exception e) {
            task.setErrorMessage(e.getMessage());
            log.error("文件下载异常: {}, 错误: {}", task.getUrl(), e.getMessage());
            return false;
        }
    }

    /**
     * 安全获取CompletableFuture结果
     */
    private Boolean safeGet(CompletableFuture<Boolean> future) {
        try {
            return future.get(300, TimeUnit.SECONDS); // 5分钟超时
        } catch (Exception e) {
            log.error("等待下载任务完成时发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 创建ZIP文件
     */
    private String createZipFile(List<DownloadTask> successTasks, String zipFilePath) throws IOException {
        // 确保ZIP文件的目录存在
        File zipFile = new File(zipFilePath);
        File parentDir = zipFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (created) {
                log.info("创建ZIP文件目录: {}", parentDir.getAbsolutePath());
            }
        }

        try (FileOutputStream fos = new FileOutputStream(zipFilePath);
             ZipArchiveOutputStream zos = new ZipArchiveOutputStream(fos)) {

            zos.setEncoding("UTF-8"); // 设置编码避免中文乱码

            for (DownloadTask task : successTasks) {
                File file = new File(task.getLocalPath());
                if (file.exists()) {
                    addFileToZip(zos, file, task.getFileName());
                }
            }

            zos.finish();
        }

        log.info("ZIP文件创建成功: {}", zipFilePath);
        return zipFilePath;
    }

    /**
     * 添加文件到ZIP
     */
    private void addFileToZip(ZipArchiveOutputStream zos, File file, String entryName) throws IOException {
        ZipArchiveEntry entry = new ZipArchiveEntry(file, entryName);
        zos.putArchiveEntry(entry);

        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream bis = new BufferedInputStream(fis)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = bis.read(buffer)) != -1) {
                zos.write(buffer, 0, bytesRead);
            }
        }

        zos.closeArchiveEntry();
        log.debug("文件已添加到ZIP: {}", entryName);
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(List<DownloadTask> tasks) {
        for (DownloadTask task : tasks) {
            if (task.getLocalPath() != null) {
                try {
                    File file = new File(task.getLocalPath());
                    if (file.exists() && file.delete()) {
                        log.debug("清理临时文件: {}", task.getLocalPath());
                    }
                } catch (Exception e) {
                    log.warn("清理临时文件失败: {}, 错误: {}", task.getLocalPath(), e.getMessage());
                }
            }
        }
    }

    /**
     * 便利方法：通过URL列表创建下载任务
     *
     * @param urls URL列表
     * @return 下载任务列表
     */
    public List<DownloadTask> createDownloadTasks(List<String> urls) {
        List<DownloadTask> tasks = new ArrayList<>();
        for (int i = 0; i < urls.size(); i++) {
            String url = urls.get(i);
            String fileName = extractFileNameFromUrl(url, i);
            tasks.add(new DownloadTask(url, fileName));
        }
        return tasks;
    }

    /**
     * 便利方法：通过URL和文件名映射创建下载任务
     *
     * @param urlFileNameMap URL和文件名的映射
     * @return 下载任务列表
     */
    public List<DownloadTask> createDownloadTasks(java.util.Map<String, String> urlFileNameMap) {
        List<DownloadTask> tasks = new ArrayList<>();
        for (java.util.Map.Entry<String, String> entry : urlFileNameMap.entrySet()) {
            tasks.add(new DownloadTask(entry.getKey(), entry.getValue()));
        }
        return tasks;
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url, int index) {
        try {
            String fileName = url.substring(url.lastIndexOf('/') + 1);
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.indexOf("?"));
            }
            if (!fileName.contains(".")) {
                fileName = "file_" + (index + 1) + ".tmp";
            }
            return fileName;
        } catch (Exception e) {
            return "file_" + (index + 1) + ".tmp";
        }
    }

    /**
     * 便利方法：直接通过URL列表下载并打包
     *
     * @param urls        URL列表
     * @param zipFilePath ZIP文件路径
     * @return ZIP文件路径，失败返回null
     */
    public String downloadUrlsAndZip(List<String> urls, String zipFilePath) {
        List<DownloadTask> tasks = createDownloadTasks(urls);
        return downloadAndZip(tasks, zipFilePath, tmpPath + "download/");
    }

    /**
     * 便利方法：通过URL和文件名映射下载并打包
     *
     * @param urlFileNameMap URL和文件名的映射
     * @param zipFilePath    ZIP文件路径
     * @return ZIP文件路径，失败返回null
     */
    public String downloadUrlsAndZip(Map<String, String> urlFileNameMap, String zipFilePath) {
        List<DownloadTask> tasks = createDownloadTasks(urlFileNameMap);
        return downloadAndZip(tasks, zipFilePath, tmpPath + "download/");
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        MultiFileDownloader multiFileDownloader = new MultiFileDownloader();

        // 测试用例1：使用URL列表
        List<String> urls = new ArrayList<>();
        urls.add("https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/caption/SC/1754368236507838770.srt");
        urls.add("https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/video/00/1754809410659836124.mp4");

        String zipPath1 = multiFileDownloader.downloadUrlsAndZip(urls, "C:\\Users\\<USER>\\Desktop\\test\\test_download1.zip");
        log.info("测试1结果: {}", zipPath1);

        // 测试用例2：使用URL和文件名映射
        Map<String, String> urlFileMap = new HashMap<>();
        urlFileMap.put("https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/caption/SC/1754368236507838770.srt", "subtitle.srt");
        urlFileMap.put("https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/video/00/1754809410659836124.mp4", "video.mp4");

        String zipPath2 = multiFileDownloader.downloadUrlsAndZip(urlFileMap, "C:\\Users\\<USER>\\Desktop\\test\\test_download2.zip");
        log.info("测试2结果: {}", zipPath2);

        // 测试用例3：使用DownloadTask列表（最灵活的方式）
        List<DownloadTask> tasks = new ArrayList<>();
        tasks.add(new DownloadTask("https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/caption/SC/1754368236507838770.srt", "subtitle.srt"));

        String zipPath3 = multiFileDownloader.downloadAndZip(tasks, "C:\\Users\\<USER>\\Desktop\\test\\test_download3.zip", tmpPath + "download/");
        log.info("测试3结果: {}", zipPath3);
    }
}
