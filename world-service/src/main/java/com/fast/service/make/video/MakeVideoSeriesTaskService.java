/*
 * Powered By fast.up
 */
package com.fast.service.make.video;

import com.fast.constant.StaticStr;
import com.fast.enums.YesOrNoEnum;
import com.fast.enums.video.VideoProcessStatusEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.make.video.MakeVideoSeriesTaskMapper;
import com.fast.po.make.video.MakeVideoSeriesTaskPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class MakeVideoSeriesTaskService extends BaseService {

    @Autowired
    private MakeVideoSeriesTaskMapper makeVideoSeriesTaskMapper;

    @Autowired
    private MakeVideoBatchTaskService makeVideoBatchTaskService;

    /**
     * 通过id查询单个对象
     */
    public MakeVideoSeriesTaskPO queryById(MakeVideoSeriesTaskPO params) {
        return makeVideoSeriesTaskMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeVideoSeriesTaskPO queryById(Integer id) {
        return makeVideoSeriesTaskMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeVideoSeriesTaskPO queryOne(MakeVideoSeriesTaskPO params) {
        return makeVideoSeriesTaskMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeVideoSeriesTaskPO> queryList(MakeVideoSeriesTaskPO params) {
        return makeVideoSeriesTaskMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeVideoSeriesTaskPO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeVideoSeriesTaskPO> list = makeVideoSeriesTaskMapper.queryList(params);
        for (MakeVideoSeriesTaskPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(MakeVideoSeriesTaskPO params) {
        Integer count = makeVideoSeriesTaskMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    public List<MakeVideoSeriesTaskPO> queryByBatchId(Integer batchId) {
        if (batchId == null) {
            return null;
        }
        MakeVideoSeriesTaskPO query = new MakeVideoSeriesTaskPO();
        query.setBatchId(batchId);
        return queryList(query);
    }

    public MakeVideoSeriesTaskPO queryByFilmIdAndSeriesNum(Integer filmId, Integer seriesNum) {
        if (filmId == null || seriesNum == null) {
            return null;
        }
        MakeVideoSeriesTaskPO query = new MakeVideoSeriesTaskPO();
        query.setFilmId(filmId);
        query.setSeriesNum(seriesNum);
        return queryOne(query);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeVideoSeriesTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeVideoSeriesTaskMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeVideoSeriesTaskPO> list) {
        if (makeVideoSeriesTaskMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeVideoSeriesTaskPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeVideoSeriesTaskMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeVideoSeriesTaskPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeVideoSeriesTaskPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        makeVideoSeriesTaskMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public int updateStatus(Integer id, Integer status) {
        if (id == null || status == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoSeriesTaskPO taskPO = new MakeVideoSeriesTaskPO();
        taskPO.setId(id);
        taskPO.setStatus(status);
        return makeVideoSeriesTaskMapper.updateById(taskPO);
    }

    public int updateStatus(Integer id, VideoProcessStatusEnum statusEnum) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        return updateStatus(id, statusEnum.getCode());
    }

    public int updateStatusAndStartTime(Integer id, Integer status, Date startTime) {
        if (id == null || status == null || startTime == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoSeriesTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        MakeVideoSeriesTaskPO seriesTaskUpdate = new MakeVideoSeriesTaskPO();
        seriesTaskUpdate.setId(id);
        seriesTaskUpdate.setStatus(status);
        seriesTaskUpdate.setStartTime(startTime);
        int row = makeVideoSeriesTaskMapper.updateById(seriesTaskUpdate);
        // 如果是启动状态，同步批量任务
        if (row > 0 && Objects.equals(status, VideoProcessStatusEnum.PROCESSING.getCode())) {
            makeVideoBatchTaskService.updateStatusAndStartTime(taskPO.getBatchId(), VideoProcessStatusEnum.PROCESSING, startTime);
        }
        return row;
    }

    public int updateStatusAndStartTime(Integer id, VideoProcessStatusEnum statusEnum, Date startTime) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        return updateStatusAndStartTime(id, statusEnum.getCode(), startTime);
    }

    public int updateStatusAndEndTime(Integer id, Integer status, Date endTime) {
        if (id == null || status == null || endTime == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoSeriesTaskPO taskPO = queryById(id);
        if (taskPO == null) {
            throw new MyException("Task not found with id: " + id);
        }
        MakeVideoSeriesTaskPO seriesTaskUpdate = new MakeVideoSeriesTaskPO();
        seriesTaskUpdate.setId(id);
        seriesTaskUpdate.setStatus(status);
        seriesTaskUpdate.setEndTime(endTime);
        seriesTaskUpdate.setTakeTime(DateUtil.getDiffTime(taskPO.getStartTime(), endTime));
        return makeVideoSeriesTaskMapper.updateById(seriesTaskUpdate);
    }

    public int updateStatusAndEndTime(Integer id, VideoProcessStatusEnum statusEnum, Date endTime) {
        if (id == null || statusEnum == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        return updateStatusAndEndTime(id, statusEnum.getCode(), endTime);
    }

    public int markExtractCompleted(Integer id) {
        if (id == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoSeriesTaskPO taskPO = new MakeVideoSeriesTaskPO();
        taskPO.setId(id);
        taskPO.setExtractCompleted(YesOrNoEnum.YES.getCode());
        return makeVideoSeriesTaskMapper.updateById(taskPO);
    }

    public int markTranslateCompleted(Integer id) {
        if (id == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoSeriesTaskPO taskPO = new MakeVideoSeriesTaskPO();
        taskPO.setId(id);
        taskPO.setTranslateCompleted(YesOrNoEnum.YES.getCode());
        return makeVideoSeriesTaskMapper.updateById(taskPO);
    }

    public int markRemoveCompleted(Integer id) {
        if (id == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoSeriesTaskPO taskPO = new MakeVideoSeriesTaskPO();
        taskPO.setId(id);
        taskPO.setRemoveCompleted(YesOrNoEnum.YES.getCode());
        return makeVideoSeriesTaskMapper.updateById(taskPO);
    }

    public int markCompressCompleted(Integer id) {
        if (id == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeVideoSeriesTaskPO taskPO = new MakeVideoSeriesTaskPO();
        taskPO.setId(id);
        taskPO.setCompressCompleted(YesOrNoEnum.YES.getCode());
        // 压制是最后一步，如果压制完成，意味着整个流程终结
        taskPO.setStatus(VideoProcessStatusEnum.COMPLETED.getCode());
        return makeVideoSeriesTaskMapper.updateById(taskPO);
    }

    public boolean isAllCompleted(Integer batchTaskId) {
        if (batchTaskId == null) {
            return false;
        }
        List<MakeVideoSeriesTaskPO> list = queryByBatchId(batchTaskId);
        if (CollUtil.isEmpty(list)) {
            return false;
        }
        return list.stream().allMatch(item -> Objects.equals(item.getStatus(), VideoProcessStatusEnum.COMPLETED.getCode()));
    }

}
