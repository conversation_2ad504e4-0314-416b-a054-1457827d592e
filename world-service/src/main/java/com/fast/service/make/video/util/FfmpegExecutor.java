package com.fast.service.make.video.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * ffmpeg命令执行器
 */
@Slf4j
public class FfmpegExecutor {

    public static void main(String[] args) throws IOException, InterruptedException {
        // 配置工作目录
        String workingDir = "C:/Users/<USER>/Desktop/test/压制";
        String inputFile = "input.mp4";
        String outputFile = "output_en_001.mp4";
        String subtitleFile = "en.srt";
        int marginV = 75;

        // 执行FFmpeg命令
        executeFfmpegCommand(workingDir, inputFile, outputFile, subtitleFile, marginV);
    }

    public static boolean executeFfmpegCommand(
            String inputFile,
            String outputFile,
            String subtitleFile,
            int marginV) throws IOException, InterruptedException {
        return executeFfmpegCommand(null, inputFile, outputFile, subtitleFile, marginV);
    }

    public static boolean executeFfmpegCommand(
            String workingDir,
            String inputFile,
            String outputFile,
            String subtitleFile,
            int marginV) throws IOException, InterruptedException {

        List<String> command = new ArrayList<>();
        command.add("ffmpeg");
        command.add("-i");
        command.add(inputFile);

        // 方法1：直接构建
        String filter = "subtitles=" + subtitleFile +
                ":force_style=Alignment=2\\\\\\,MarginV=" + marginV;
        command.add("-vf");
        command.add(filter);

        // 方法2：使用ASS文件（推荐）
        // Path styleFile = Files.createTempFile("substyle", ".ass");
        // Files.write(styleFile, ("[V4+ Styles]\nFormat: Name, Fontname, Fontsize, PrimaryColour, Alignment, MarginV\n" +
        //                       "Style: Default,Arial,20,&H00FFFFFF,2," + marginV).getBytes());
        // command.add("-vf");
        // command.add("ass=" + styleFile.toAbsolutePath());
        // 执行完成后删除临时文件
        // Files.deleteIfExists(styleFile);

        command.add("-c:v");
        command.add("libx264");
        command.add("-c:a");
        command.add("copy");
        command.add(outputFile);

        // 打印执行的命令
        log.info("执行ffmpeg命令: {}", String.join(" ", command));

        // 创建进程构建器
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        // 设置工作目录
        if (workingDir != null) {
            processBuilder.directory(new File(workingDir));
        }
        processBuilder.redirectErrorStream(true);

        // 启动进程
        Process process = processBuilder.start();

        // 获取并打印输出
        InputStream inputStream = process.getInputStream();
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));

        log.info("FFmpeg output:");

        StringBuilder output = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            log.info(line);
            output.append(line).append("\n");
        }
        log.info(output.toString());

        // 等待进程结束
        int exitCode = process.waitFor();
        log.info("Process exited with code: {}", exitCode);

        if (exitCode == 0) {
            log.info("✅ FFmpeg command executed successfully!");
            return true;
        } else {
            log.info("❌ FFmpeg command failed!");
            return false;
        }
    }

}