/*
 * Powered By fast.up
 */
package com.fast.service.make;

import com.fast.constant.StaticStr;
import com.fast.enums.FilmTypeEnum;
import com.fast.enums.video.VideoProcessStatusEnum;
import com.fast.framework.config.LsyTosConfig;
import com.fast.framework.exception.MyException;
import com.fast.mapper.make.MakeFilmVideoMapper;
import com.fast.po.make.MakeFilmVideoPO;
import com.fast.po.make.video.MakeSubtitleRemoveTaskPO;
import com.fast.po.make.video.MakeVideoCompressTaskPO;
import com.fast.service.base.BaseService;
import com.fast.service.make.video.MakeSubtitleRemoveTaskServiceV2;
import com.fast.service.make.video.MakeVideoCompressTaskServiceV2;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MakeFilmVideoService extends BaseService {

    @Autowired
    private MakeFilmVideoMapper makeFilmVideoMapper;

    @Autowired
    private LsyTosConfig lsyTosConfig;

    @Autowired
    private MakeSubtitleRemoveTaskServiceV2 makeSubtitleRemoveTaskServiceV2;

    @Autowired
    private MakeVideoCompressTaskServiceV2 makeVideoCompressTaskServiceV2;

    /**
     * 通过id查询单个对象
     */
    public MakeFilmVideoPO queryById(MakeFilmVideoPO params) {
        return makeFilmVideoMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public MakeFilmVideoPO queryById(Integer id) {
        return makeFilmVideoMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public MakeFilmVideoPO queryOne(MakeFilmVideoPO params) {
        return makeFilmVideoMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<MakeFilmVideoPO> queryList(MakeFilmVideoPO params) {
        return makeFilmVideoMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(MakeFilmVideoPO params, PageVO pageVO) {
        startPage(pageVO);
        List<MakeFilmVideoPO> list = makeFilmVideoMapper.queryList(params);
        for (MakeFilmVideoPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setUrl(getVideoUrl(cur.getUrl()));
            cur.setOriginalFilmUrl(getVideoUrl(cur.getOriginalVideoId()));
            fillTaskInfo(cur);
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(MakeFilmVideoPO params) {
        return makeFilmVideoMapper.queryCount(params);
    }

    private String getVideoUrl(Integer id) {
        if (id == null || id <= 0) {
            return null;
        }
        MakeFilmVideoPO po = queryById(id);
        String url = po == null ? null : po.getUrl();
        return getVideoUrl(url);
    }

    private String getVideoUrl(String url) {
        if (StrUtil.isBlank(url)) {
            return null;
        }
        // 国内oss做了同步，直接替换域名
        return url.replace(lsyTosConfig.getHost(), lsyTosConfig.getCont().getHost());
    }

    private void fillTaskInfo(MakeFilmVideoPO cur) {
        if (Objects.equals(cur.getLanguageCode(), "00")) {
            MakeSubtitleRemoveTaskPO task = makeSubtitleRemoveTaskServiceV2.queryByFilmIdAndSeriesNum(cur.getFilmId(), cur.getSeriesNum());
            if (task == null) {
                cur.setState(VideoProcessStatusEnum.PENDING.getCode());
            } else {
                cur.setStartTime(task.getStartTime());
                cur.setEndTime(task.getEndTime());
                cur.setTakeTime(task.getTakeTime());
                cur.setState(task.getStatus());
                cur.setFailReason(task.getErrorMessage());
            }
        } else {
            MakeVideoCompressTaskPO task = makeVideoCompressTaskServiceV2.queryByFilmIdAndSeriesNumAndLang(cur.getFilmId(), cur.getSeriesNum(), cur.getLanguageCode());
            if (task == null) {
                cur.setState(VideoProcessStatusEnum.PENDING.getCode());
            } else {
                cur.setStartTime(task.getStartTime());
                cur.setEndTime(task.getEndTime());
                cur.setTakeTime(task.getTakeTime());
                cur.setState(task.getStatus());
                cur.setFailReason(task.getErrorMessage());
            }
        }
    }

    public MakeFilmVideoPO queryByFilmIdAndTypeAndSerLang(Integer filmId, Integer filmType, Integer seriesNum, String lang) {
        if (filmId == null || filmType == null || lang == null) {
            return null;
        }
        MakeFilmVideoPO params = new MakeFilmVideoPO();
        params.setFilmId(filmId);
        params.setFilmType(filmType);
        params.setSeriesNum(seriesNum);
        params.setLanguageCode(lang);
        return makeFilmVideoMapper.queryOne(params);
    }

    public MakeFilmVideoPO queryByFilmIdAndTypeAndSerNum(Integer filmId, Integer filmType, Integer seriesNum) {
        if (filmId == null || filmType == null) {
            return null;
        }
        MakeFilmVideoPO params = new MakeFilmVideoPO();
        params.setFilmId(filmId);
        params.setFilmType(filmType);
        params.setSeriesNum(seriesNum);
        return makeFilmVideoMapper.queryOne(params);
    }

    public List<MakeFilmVideoPO> queryByFilmIdAndType(Integer filmId, Integer filmType) {
        if (filmId == null || filmType == null) {
            return null;
        }
        MakeFilmVideoPO params = new MakeFilmVideoPO();
        params.setFilmId(filmId);
        params.setFilmType(filmType);
        return makeFilmVideoMapper.queryList(params);
    }

    public List<MakeFilmVideoPO> queryByOriginalFilmId(Integer filmId) {
        return queryByFilmIdAndType(filmId, FilmTypeEnum.ORIGINAL.getCode());
    }

    public List<MakeFilmVideoPO> queryByCompletedFilmId(Integer filmId) {
        return queryByFilmIdAndType(filmId, FilmTypeEnum.COMPLETED.getCode());
    }

    public Integer countByFilmIdAndType(Integer filmId, Integer filmType) {
        if (filmId == null || filmType == null) {
            return 0;
        }
        MakeFilmVideoPO params = new MakeFilmVideoPO();
        params.setFilmId(filmId);
        params.setFilmType(filmType);
        Integer count = makeFilmVideoMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    public Integer countByOriginalFilmId(Integer filmId) {
        return countByFilmIdAndType(filmId, FilmTypeEnum.ORIGINAL.getCode());
    }

    public Integer countByCompletedFilmId(Integer filmId) {
        return countByFilmIdAndType(filmId, FilmTypeEnum.COMPLETED.getCode());
    }

    public Integer deleteByFilmIdAndType(Integer filmId, Integer filmType) {
        if (filmId == null || filmType == null) {
            return 0;
        }
        return makeFilmVideoMapper.deleteByFilmIdAndType(filmId, filmType);
    }

    public Integer deleteByOriginalFilmId(Integer filmId) {
        return deleteByFilmIdAndType(filmId, FilmTypeEnum.ORIGINAL.getCode());
    }

    public Integer deleteByCompletedFilmId(Integer filmId) {
        return deleteByFilmIdAndType(filmId, FilmTypeEnum.COMPLETED.getCode());
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(MakeFilmVideoPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (makeFilmVideoMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<MakeFilmVideoPO> list) {
        if (makeFilmVideoMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(MakeFilmVideoPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (makeFilmVideoMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(MakeFilmVideoPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        MakeFilmVideoPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        makeFilmVideoMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public Set<String> getLangCodes(Integer filmId, Integer filmType) {
        List<MakeFilmVideoPO> list = queryByFilmIdAndType(filmId, filmType);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().map(MakeFilmVideoPO::getLanguageCode).collect(Collectors.toSet());
    }

    public int fillUrlByFilmAndSeriesNumAndLang(Integer filmId, Integer filmType, Integer seriesNum, String languageCode, String url) {
        if (filmId == null || filmType == null || seriesNum == null || StrUtil.isBlank(languageCode) || StrUtil.isBlank(url)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeFilmVideoPO filmVideo = new MakeFilmVideoPO();
        filmVideo.setFilmId(filmId);
        filmVideo.setFilmType(filmType);
        filmVideo.setSeriesNum(seriesNum);
        filmVideo.setLanguageCode(languageCode);
        filmVideo.setUrl(url);
        return makeFilmVideoMapper.updateByFilmAndSeriesNumAndLang(filmVideo);
    }

    public int fillCleanUrlByFilmAndSeriesNum(Integer filmId, Integer filmType, Integer seriesNum, String url) {
        if (filmId == null || filmType == null || seriesNum == null || StrUtil.isBlank(url)) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        MakeFilmVideoPO filmVideo = new MakeFilmVideoPO();
        filmVideo.setFilmId(filmId);
        filmVideo.setFilmType(filmType);
        filmVideo.setSeriesNum(seriesNum);
        filmVideo.setCleanUrl(url);
        return makeFilmVideoMapper.updateByFilmAndSeriesNumAndLang(filmVideo);
    }

}
