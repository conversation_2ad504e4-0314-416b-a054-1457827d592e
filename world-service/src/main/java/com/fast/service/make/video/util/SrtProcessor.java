package com.fast.service.make.video.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * SRT字幕文件处理器
 * 功能：
 * 1. 正确处理编号
 * 2. 保持字幕条目之间的空行
 * 3. 成功清理掉末尾的所有非标准内容
 */
@Slf4j
public class SrtProcessor {

    /**
     * 处理SRT字幕
     *
     * @param input 输入文件内容
     * @return 处理后的srt字幕内容
     */
    public static String fixSrt(String input) {
        try {
            // 首先预处理输入内容，确保数字序号前有换行
            String preprocessedInput = preprocessSrtContent(input);

            // 按空行分割内容为单独的字幕条目
            String[] entries = preprocessedInput.trim().split("\\n\\s*\\n");

            List<String> processedEntries = new ArrayList<>();
            int index = 1;

            for (String entry : entries) {
                String[] lines = entry.trim().split("\\n");

                if (lines.length >= 2) { // 确保是有效条目
                    // 检查是否包含时间戳（判断是否为字幕条目）
                    boolean hasTimestamp = false;
                    for (String line : lines) {
                        if (line.contains("-->")) {
                            hasTimestamp = true;
                            break;
                        }
                    }

                    if (hasTimestamp) {
                        // 过滤掉只包含特殊字符或markdown的行
                        List<String> filteredLines = new ArrayList<>();
                        Pattern specialCharsPattern = Pattern.compile("^[`\\-=*\\s]*$");

                        for (String line : lines) {
                            if (!line.trim().isEmpty() && !specialCharsPattern.matcher(line.trim()).matches()) {
                                filteredLines.add(line);
                            }
                        }

                        if (filteredLines.size() >= 2) { // 过滤后仍然是有效条目
                            // 清理字幕文本，移除末尾的垃圾内容
                            List<String> cleanedLines = cleanSubtitleText(filteredLines);

                            if (cleanedLines.size() >= 2) { // 清理后仍然有效
                                try {
                                    // 检查第一行是否为数字或临时数字编号
                                    String firstLine = cleanedLines.getFirst();
                                    if ("TEMP_NUMBER".equals(firstLine) || isNumericSequence(firstLine)) {
                                        // 替换为正确的索引
                                        cleanedLines.set(0, String.valueOf(index));
                                    } else {
                                        // 如果第一行不是数字，在开头插入索引
                                        cleanedLines.addFirst(String.valueOf(index));
                                    }
                                } catch (NumberFormatException e) {
                                    // 如果第一行不是数字，在开头插入索引
                                    cleanedLines.addFirst(String.valueOf(index));
                                }

                                index++;
                                processedEntries.add(String.join("\n", cleanedLines));
                            }
                        }
                    }
                    // 跳过不包含时间戳的条目（可能是非标准内容）
                }
            }

            // 生成清理后的内容
            return String.join("\n\n", processedEntries);

        } catch (Exception e) {
            log.error("处理SRT字幕失败: {}", ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    /**
     * 预处理SRT内容，确保数字序号前有适当的换行
     * 处理缺少换行的情况，如：
     * 1. 数字序号前缺少换行
     * 2. 时间戳粘连（缺少数字编号和换行）
     * 例如：
     * 1
     * 00:00:07,840 --> 00:00:09,260
     * 队报案是警区保安
     * 00:00:09,320 --> 00:00:10,580  <- 这里缺少数字编号和换行
     * 男 23岁
     */
    private static String preprocessSrtContent(String input) {
        if (input == null || input.trim().isEmpty()) {
            return input;
        }

        String[] lines = input.split("\\n");
        List<String> processedLines = new ArrayList<>();

        for (int i = 0; i < lines.length; i++) {
            String currentLine = lines[i].trim();

            // 检查当前行是否是数字序号
            if (isNumericSequence(currentLine)) {
                // 如果不是第一行，且前一行不是空行，则在数字前添加空行
                if (i > 0 && !processedLines.isEmpty()) {
                    String lastLine = processedLines.get(processedLines.size() - 1).trim();
                    if (!lastLine.isEmpty()) {
                        processedLines.add(""); // 添加空行
                    }
                }
            }
            // 检查当前行是否是时间戳（可能是粘连的字幕）
            else if (isTimestamp(currentLine)) {
                // 如果前一行不是空行且不是数字序号，说明这是粘连的字幕
                if (i > 0 && !processedLines.isEmpty()) {
                    String lastLine = processedLines.get(processedLines.size() - 1).trim();
                    if (!lastLine.isEmpty() && !isNumericSequence(lastLine) && !isTimestamp(lastLine)) {
                        // 这是粘连的字幕，需要添加空行和临时数字编号
                        processedLines.add(""); // 添加空行
                        processedLines.add("TEMP_NUMBER"); // 添加临时数字编号，后续会被正确编号替换
                    }
                }
            }

            processedLines.add(lines[i]); // 保持原始格式（包括空格）
        }

        return String.join("\n", processedLines);
    }

    /**
     * 检查字符串是否为数字序号
     *
     * @param str 要检查的字符串
     * @return 是否为数字序号
     */
    private static boolean isNumericSequence(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }

        // 排除临时数字编号
        if ("TEMP_NUMBER".equals(str.trim())) {
            return true;
        }

        try {
            int num = Integer.parseInt(str.trim());
            return num > 0; // 序号应该是正数
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 检查字符串是否为时间戳格式
     *
     * @param str 要检查的字符串
     * @return 是否为时间戳
     */
    private static boolean isTimestamp(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含时间戳标识符 "-->"
        return str.contains("-->") &&
                str.matches(".*\\d{2}:\\d{2}:\\d{2}[,.]\\d{3}\\s*-->\\s*\\d{2}:\\d{2}:\\d{2}[,.]\\d{3}.*");
    }

    /**
     * 清理字幕文本，严格按照SRT格式：序号 + 时间码 + 单行字幕文本
     * @param lines 字幕行列表
     * @return 清理后的字幕行列表
     */
    private static List<String> cleanSubtitleText(List<String> lines) {
        if (lines == null || lines.isEmpty()) {
            return lines;
        }

        List<String> cleanedLines = new ArrayList<>();
        boolean foundTimestamp = false;
        boolean hasSubtitleText = false;

        // 严格按照SRT格式处理：序号 -> 时间码 -> 单行字幕文本
        for (String line : lines) {
            if (isTimestamp(line)) {
                cleanedLines.add(line);
                foundTimestamp = true;
                hasSubtitleText = false; // 重置字幕文本标记
            } else if (foundTimestamp && !hasSubtitleText && isValidSubtitleText(line)) {
                // 只保留时间码后的第一行有效字幕文本
                cleanedLines.add(line);
                hasSubtitleText = true;
                // 后续的文本行都会被忽略，确保只有一行字幕文本
            } else if (!foundTimestamp) {
                // 时间戳之前的内容（如数字序号）
                cleanedLines.add(line);
            }
            // 其他情况（多余的文本行、垃圾内容）都会被跳过
        }

        return cleanedLines;
    }

    /**
     * 检查是否为有效的字幕文本
     *
     * @param text 要检查的文本
     * @return 是否为有效字幕文本
     */
    private static boolean isValidSubtitleText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        String trimmed = text.trim();

        // 1. 数字序号
        if (isNumericSequence(trimmed)) {
            return false;
        }

        // 2. 时间戳
        if (isTimestamp(trimmed)) {
            return false;
        }

        // 3. 只包含特殊字符的行
        if (trimmed.matches("^[`\\-=*\\s]+$")) {
            return false;
        }

        // 4. 过长的重复字符
        if (trimmed.length() > 30 && isRepeatingPattern(trimmed)) {
            return false;
        }

        // 5. 明显的随机字符串
        if (isRandomString(trimmed)) {
            return false;
        }

        // 6. 检查是否包含大量无意义字符组合
        return !isGarbageText(trimmed);
    }

    /**
     * 检查是否为垃圾文本
     *
     * @param text 要检查的文本
     * @return 是否为垃圾文本
     */
    private static boolean isGarbageText(String text) {
        // 1. 纯字母且无空格，长度超过15的可能是垃圾
        if (text.matches("^[a-zA-Z]+$") && text.length() > 15) {
            return true;
        }

        // 2. 包含大量重复的字符模式
        if (text.length() > 10) {
            // 检查是否有重复的子字符串模式
            for (int len = 2; len <= text.length() / 3; len++) {
                String pattern = text.substring(0, len);
                if (text.startsWith(pattern + pattern + pattern)) {
                    return true;
                }
            }
        }

        // 3. 特定的垃圾模式（如包含sdf等无意义字符组合）
        return text.matches(".*sdf.*") && text.length() > 10;
    }

    /**
     * 检查是否为重复模式的字符串
     *
     * @param str 要检查的字符串
     * @return 是否为重复模式
     */
    private static boolean isRepeatingPattern(String str) {
        if (str.length() < 10) {
            return false;
        }

        // 检查是否有大量重复的字符
        char firstChar = str.charAt(0);
        int sameCharCount = 0;
        for (char c : str.toCharArray()) {
            if (c == firstChar) {
                sameCharCount++;
            }
        }

        // 如果超过80%是同一个字符，认为是重复模式
        return (double) sameCharCount / str.length() > 0.8;
    }

    /**
     * 检查是否为随机字符串
     *
     * @param str 要检查的字符串
     * @return 是否为随机字符串
     */
    private static boolean isRandomString(String str) {
        // 1. 无空格且很长的字符串
        if (!str.contains(" ") && str.length() > 20) {
            return true;
        }

        // 2. 检查连续相同字符
        if (str.length() > 10) {
            int maxConsecutive = 1;
            int currentConsecutive = 1;

            for (int i = 1; i < str.length(); i++) {
                if (str.charAt(i) == str.charAt(i - 1)) {
                    currentConsecutive++;
                    maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
                } else {
                    currentConsecutive = 1;
                }
            }

            // 如果有超过8个连续相同字符，可能是垃圾内容
            return maxConsecutive > 8;
        }

        return false;
    }

    /**
     * 处理SRT字幕文件
     *
     * @param inputFile  输入文件路径
     * @param outputFile 输出文件路径
     * @throws IOException 文件读写异常
     */
    public static boolean fixSrt(String inputFile, String outputFile) {
        try {
            String content = readFileWithEncoding(inputFile);

            String cleanedContent = fixSrt(content);

            if (cleanedContent == null || cleanedContent.trim().isEmpty()) {
                return false;
            }

            // 确保目标文件夹存在
            File targetFile = new File(outputFile);
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (created) {
                    log.info("创建目标文件夹: {}", parentDir.getAbsolutePath());
                } else {
                    log.warn("无法创建目标文件夹: {}", parentDir.getAbsolutePath());
                }
            }
            
            // 写入输出文件
            try (BufferedWriter writer = Files.newBufferedWriter(Paths.get(outputFile), StandardCharsets.UTF_8)) {
                writer.write(cleanedContent);
            }
            return true;
        } catch (Exception e) {
            log.error("处理SRT文件失败: {}", ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * 尝试不同编码读取文件
     *
     * @param filePath 文件路径
     * @return 文件内容
     * @throws IOException 文件读取异常
     */
    private static String readFileWithEncoding(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        try {
            // 首先尝试UTF-8编码
            return Files.readString(path);
        } catch (Exception e) {
            try {
                // 如果UTF-8失败，尝试GBK编码
                return new String(Files.readAllBytes(path), "GBK");
            } catch (Exception ex) {
                // 如果都失败，使用系统默认编码
                return new String(Files.readAllBytes(path));
            }
        }
    }

    /**
     * 主方法 - 示例用法
     */
    public static void main(String[] args) {
        String inputFile = "C:\\Users\\<USER>\\Desktop\\test\\error.srt";
        String outputFile = "C:\\Users\\<USER>\\Desktop\\test\\error_fixed.srt";

        fixSrt(inputFile, outputFile);
        System.out.println("SRT文件处理完成！");
        System.out.println("输入文件: " + inputFile);
        System.out.println("输出文件: " + outputFile);
    }
}
