/*
 * Powered By fast.up
 */
package com.fast.service.statis;

import com.fast.mapper.statis.FastStatisLinkPvMapper;
import com.fast.po.statis.FastStatisLinkPvPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class FastStatisLinkPvService extends BaseService {

    @Autowired
    private FastStatisLinkPvMapper fastStatisLinkPvMapper;


    private static final Set<String> linkSet;

    static {
        linkSet = new HashSet<>();
    }

    public Long queryLinkPVCount(Integer linkId) {
        return fastStatisLinkPvMapper.queryLinkPVCount(linkId);
    }

    @Async
    public MethodVO addLinkPv(Integer linkId) {
        actionLogService.log("remove_tablescore_pv", "linkSet.size=" + linkSet.size());
        String nowDate = DateUtil.format09(DateUtil.getNowDate());// yyyyy-MM-DD
        String key = nowDate + "_" + linkId;
        boolean updateFlag = false;
        if (linkSet.contains(key)) {
            updateFlag = true;
        } else {
            String res = RedisUtil.get(key);
            if (StrUtil.isNotEmpty(res)) {
                updateFlag = true;
                linkSet.add(key);
            } else {
                // 查询数据库
                FastStatisLinkPvPO lpParam = new FastStatisLinkPvPO();
                lpParam.setLinkId(linkId);
                lpParam.setStatisDate(DateUtil.format09(nowDate));
                FastStatisLinkPvPO lpPO = fastStatisLinkPvMapper.queryOne(lpParam);
                if (lpPO != null) {
                    // 更新
                    updateFlag = true;
                    linkSet.add(key);
                    RedisUtil.set(key, "ok", 60 * 60 * 24);
                }
            }
        }
        if (updateFlag) {
            // 更新
            FastStatisLinkPvPO lpParam = new FastStatisLinkPvPO();
            lpParam.setUpdateTime(DateUtil.getNowDate());
            lpParam.setLinkId(linkId);
            lpParam.setStatisDate(DateUtil.format09(nowDate));
            fastStatisLinkPvMapper.updateAddPv(lpParam);
        } else {
            // 新增
            FastStatisLinkPvPO lpParam = new FastStatisLinkPvPO();
            lpParam.setUpdateTime(DateUtil.getNowDate());
            lpParam.setLinkId(linkId);
            lpParam.setStatisDate(DateUtil.format09(nowDate));
            lpParam.setPv(1L);
            lpParam.setCreateTime(DateUtil.getNowDate());
            fastStatisLinkPvMapper.insertSelective(lpParam);
        }
        return MethodVO.success();
    }
}
