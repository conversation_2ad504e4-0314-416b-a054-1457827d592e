/*
 * Powered By fast.up
 */
package com.fast.service.feishu;

import com.fast.constant.StaticVar;
import com.fast.mapper.common.FastCommonDictMapper;
import com.fast.po.common.FastCommonDictPO;
import com.fast.po.monitor.FastMonitorFlyLogPO;
import com.fast.po.setting.FastSettingSystemPO;
import com.fast.service.base.BaseService;
import com.fast.service.monitor.FastMonitorFlyLogService;
import com.fast.service.setting.FastSettingSystemService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.feishu.FSMessageUtil;
import com.fast.utils.redis.RedisUtil;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class FeiShuService extends BaseService {

    @Autowired
    private FastSettingSystemService fastSettingSystemService;
    @Autowired
    private FastCommonDictMapper fastCommonDictMapper;
    @Autowired
    private FastMonitorFlyLogService fastMonitorFlyLogService;

    public static Set<String> ignoreSet; // 忽略异常，不发送飞书
    public static Long lastUpdateIgnoreSetTime; // 上次更新时间

    /**
     * 异步发送全局异常信息
     */
    @Async
    public void sendErrorLogMsgAsync(String requestURI, Exception ex) {
        if (StaticVar.LIULINGYI_PLATFORM == 1) {
            // 入库
            FastMonitorFlyLogPO flPO = new FastMonitorFlyLogPO();
            flPO.setType(1);
            StringBuffer sball = new StringBuffer();
            sball.append("服务器编号：");
            sball.append(StaticVar.DEVICE_NUM);
            sball.append("\n");
            sball.append(">>> URL：");
            sball.append(requestURI);
            sball.append("\n");
            sball.append(">>> TraceId：");
            sball.append(MDC.get("traceId"));
            sball.append("\n");
            sball.append(ex.getClass().getName());
            sball.append("-");
            sball.append(ex.getLocalizedMessage());
            StackTraceElement[] stArray = ex.getStackTrace();
            for (StackTraceElement item : stArray) {
                sball.append("\n");
                sball.append(item.getClassName());
                sball.append("  ");
                sball.append(item.getMethodName());
                sball.append("  ");
                sball.append(item.getLineNumber());
            }
            String message = sball.toString();
            Set<String> igSet = getIgnoreset();
            for (String item : igSet) {
                if (message.indexOf(item) >= 0) {
                    return;
                }
            }
            flPO.setContent(message);
            flPO.setCreateTime(DateUtil.getNowDate());
            fastMonitorFlyLogService.insert(flPO);

            // 准备发送飞书，仅发送异常信息
            StringBuffer sbsimple = new StringBuffer();
            if (StaticVar.DEVICE_NUM.contains("keep1")) {
                sbsimple.append("【灰度环境】异常信息报警============");
            } else {
                sbsimple.append("【正式环境】异常信息报警============");
            }
            sbsimple.append("服务器编号：");
            sbsimple.append(StaticVar.DEVICE_NUM);
            sbsimple.append("\n");
            sbsimple.append(">>> URL：");
            sbsimple.append(requestURI);
            sbsimple.append("\n");
            sbsimple.append(">>> TraceId：");
            sbsimple.append(MDC.get("traceId"));
            sbsimple.append("\n");
            sbsimple.append(ex.getClass().getName());
            sbsimple.append("-");
            sbsimple.append(ex.getLocalizedMessage());
            Integer count = 0;
            for (StackTraceElement item : stArray) {
                if (item.getClassName().contains("com.fast.") && count < 8) {
                    sbsimple.append("\n");
                    sbsimple.append(item.getClassName());
                    sbsimple.append("  ");
                    sbsimple.append(item.getMethodName());
                    sbsimple.append("  ");
                    sbsimple.append(item.getLineNumber());
                    count++;
                }
            }
            sbsimple.append("\n详细信息查看fast_monitor_fly_log表，id=" + flPO.getId());
            sendTextMessage(sbsimple.toString(), StaticVar.FEISHU_ROBOT_ONE);
        }
    }

    private Set<String> getIgnoreset() {
        long nowTime = System.currentTimeMillis();
        if (lastUpdateIgnoreSetTime == null) {
            lastUpdateIgnoreSetTime = nowTime;
        }
        if (ignoreSet == null || nowTime - lastUpdateIgnoreSetTime > 300 * 1000) {
            if (ignoreSet == null) {
                ignoreSet = new HashSet<>();
            } else {
                ignoreSet.clear();
            }
            FastCommonDictPO cdParam = new FastCommonDictPO();
            cdParam.setCode("ignore_error");
            List<FastCommonDictPO> cdList = fastCommonDictMapper.queryList(cdParam);
            for (FastCommonDictPO cdItem : cdList) {
                ignoreSet.add(cdItem.getValue());
            }
        }
        return ignoreSet;
    }

    /**
     * 发送飞书消息
     */
    public void sendTextMessage(String msg, String robotName) {
        // 指定飞书机器人
        FastSettingSystemPO ssPO = null;
        if (StrUtil.isNotEmpty(robotName)) {
            ssPO = fastSettingSystemService.getSettingSystemCache(robotName, StaticVar.EMPTY_FLAG);
        }
        if (ssPO == null || StaticVar.EMPTY_FLAG.equals(ssPO.getContent())) {
            // 使用默认飞书机器人
            ssPO = fastSettingSystemService.getSettingSystemCache("feishu_warn_def", StaticVar.EMPTY_FLAG);
            if (ssPO == null || StaticVar.EMPTY_FLAG.equals(ssPO.getContent())) {
                log.error("未配置飞书机器人,机器人名称：" + robotName);
                return;
            }
        }
        FSMessageUtil.sendTextMessage(msg, ssPO.getContent());
    }

    /**
     * 发送飞书消息
     *
     * @param msg       消息内容
     * @param robotName 机器人名称
     * @param sec       消息有效时间
     */
    public void sendTextMessage(String msg, String robotName, String reKey, int sec) {
        if ("1".equals(RedisUtil.get(reKey))) {
            return;
        }
        sendTextMessage(msg, robotName);
        RedisUtil.set(reKey, "1", sec);
    }

}
