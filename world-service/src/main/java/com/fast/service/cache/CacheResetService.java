package com.fast.service.cache;

import com.fast.constant.StaticVar;
import com.fast.mapper.drama.FastDramaSeriesMapper;
import com.fast.mapper.fee.FastFeeRuleMapper;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.retail.FastRetailMapper;
import com.fast.po.drama.FastDramaSeriesPO;
import com.fast.po.fee.FastFeeRulePO;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaI18nService;
import com.fast.service.mini.FastMiniContVersionService;
import com.fast.utils.CollUtil;
import com.fast.utils.redis.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
public class CacheResetService extends BaseService {

    @Autowired
    private FastMiniMapper fastMiniMapper;
    @Autowired
    private FastDramaSeriesMapper fastDramaSeriesMapper;
    @Autowired
    private FastRetailMapper fastRetailMapper;
    @Autowired
    private FastMiniContVersionService fastMiniContVersionService;
    @Autowired
    private FastDramaI18nService fastDramaI18nService;
    @Autowired
    private FastFeeRuleMapper fastFeeRuleMapper;

    /**
     * 重置计费规则缓存
     *
     * @param officialId
     * @param dramaId
     */
    public void resetCacheFeeRule(Integer officialId, Integer dramaId) {
        // 删除key
        FastFeeRulePO item = new FastFeeRulePO();
        item.setDramaId(dramaId);
        List<FastFeeRulePO> pos = fastFeeRuleMapper.queryList(item);
        if (CollUtil.isNotEmpty(pos)) {
            for (FastFeeRulePO po : pos) {
                String key = StaticVar.FEE_RULE_ID + po.getOfficialId() + StaticVar.UNDERLINE + dramaId;
                RedisUtil.del(key);
                log.info("清除了计费规则缓存: {}", key);
                
                String linkKey = StaticVar.FEE_LINKID_RULE_ID + po.getLinkId() + StaticVar.UNDERLINE + dramaId;
                RedisUtil.del(linkKey);
                log.info("清除了link计费规则缓存: {}", linkKey);
            }
        }
    }

    /**
     * 重置小程序端配置缓存
     *
     * @param miniId
     * @param dramaId
     */
    public void resetCacheMiniSet(Integer miniId, Integer dramaId) {
        try {
            // 清除剧集缓存
            String key = StaticVar.DRAMA_INFO_ID + dramaId;
            RedisUtil.del(key);
            log.info("清除了剧缓存" + key);

            // key = StaticVar.DRAMA_SERIES + dramaId;
            // RedisUtil.del(key);
            Set<String> langCodes = fastDramaI18nService.getLangCodes(dramaId);
            if (CollUtil.isNotEmpty(langCodes)) {
                for (String langCode : langCodes) {
                    key = StaticVar.DRAMA_SERIES + dramaId + ":" + langCode + ":1";
                    RedisUtil.del(key);
                    key = StaticVar.DRAMA_SERIES + dramaId + ":" + langCode + ":2";
                    RedisUtil.del(key);
                }
            }

            // 清除小程序端配置
            if (miniId != null) {
                // // 删除指定
                // RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniId);
                // // 更新版本号
                // incrVersion(miniId);
                // 多版本兼容
                List<Integer> cvids = fastMiniContVersionService.getIdByMiniId(miniId);
                if (CollUtil.isNotEmpty(cvids)) {
                    for (Integer cvid : cvids) {
                        // 删除指定
                        RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniId + ":" + cvid);
                        // 更新版本号
                        incrVersion(miniId, cvid);
                    }
                }
            } else {
                // 批量删除
                FastMiniPO miniParam = new FastMiniPO();
                miniParam.setState(1);
                List<FastMiniPO> miniList = fastMiniMapper.queryList(miniParam);
                for (FastMiniPO miniItem : miniList) {
                    // RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniItem.getId().toString());
                    // // 更新版本号
                    // incrVersion(miniItem.getId());
                    // 多版本兼容
                    List<Integer> cvids = fastMiniContVersionService.getIdByMiniId(miniItem.getId());
                    if (CollUtil.isNotEmpty(cvids)) {
                        for (Integer cvid : cvids) {
                            // 删除指定
                            RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniItem.getId() + ":" + cvid);
                            // 更新版本号
                            incrVersion(miniItem.getId(), cvid);
                        }
                    }
                }
            }
        } catch (Exception ignored) {
        }
    }

    // 分销商剧列表缓存
    public void resetCacheRetailDramaList() {
        try {
            // 批量删除
            FastRetailPO retailParam = new FastRetailPO();
            retailParam.setState(1);
            retailParam.setRetailFlag(1);
            List<FastRetailPO> retailList = fastRetailMapper.querySimpleList(retailParam);
            for (FastRetailPO item : retailList) {
                RedisUtil.del(StaticVar.RETAIL_MINI_DRAMA_LIST + item.getId());
            }
        } catch (Exception ignored) {
        }
    }

    /**
     * 重置短剧剧集缓存
     *
     * @param dramaId
     */
    public void resetCacheDramaNumSet(Integer dramaId) {
        try {
            // 清除短剧剧集缓存
            FastDramaSeriesPO seriesParam = new FastDramaSeriesPO();
            seriesParam.setDramaId(dramaId);
            List<FastDramaSeriesPO> seriesList = fastDramaSeriesMapper.queryList(seriesParam);
            for (FastDramaSeriesPO seriesItem : seriesList) {
                RedisUtil.del(StaticVar.DRAMA_SERIES_NUM + dramaId + "_" + seriesItem.getLanguageCode() + "_" + seriesItem.getSeriesNum() + "_" + seriesItem.getCaptionType());
            }
        } catch (Exception ignored) {
        }
    }

    public void resetCacheMiniSetOnly(Integer miniId) {
        try {
            // 清除小程序端配置
//        	String miniIdStr = miniId == null ? "*" : miniId.toString();
            if (miniId != null) {
                // RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniId);
                // incrVersion(miniId);
                // 多版本兼容
                List<Integer> cvids = fastMiniContVersionService.getIdByMiniId(miniId);
                if (CollUtil.isNotEmpty(cvids)) {
                    for (Integer cvid : cvids) {
                        RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniId + ":" + cvid);
                        incrVersion(miniId, cvid);
                    }
                }
            } else {
                FastMiniPO miniParam = new FastMiniPO();
                miniParam.setState(1);
                List<FastMiniPO> miniList = fastMiniMapper.queryList(miniParam);
                for (FastMiniPO miniItem : miniList) {
                    // RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniItem.getId().toString());
                    // // 更新版本号
                    // incrVersion(miniItem.getId());
                    // 多版本兼容
                    List<Integer> cvids = fastMiniContVersionService.getIdByMiniId(miniItem.getId());
                    if (CollUtil.isNotEmpty(cvids)) {
                        for (Integer cvid : cvids) {
                            RedisUtil.del(StaticVar.MINI_CONTENT_SETTING + miniItem.getId() + ":" + cvid);
                            // 更新版本号
                            incrVersion(miniItem.getId(), cvid);
                        }
                    }
                }
            }
            // 更新版本号
        } catch (Exception ignored) {
        }
    }

    /**
     * 自增版本号
     *
     * @param miniId
     */
    public void incrVersion(Integer miniId, Integer cvid) {
        if (miniId == null) {
            FastMiniPO miniParam = new FastMiniPO();
            miniParam.setState(1);
            List<FastMiniPO> miniList = fastMiniMapper.queryList(miniParam);
            for (FastMiniPO miniItem : miniList) {
                if (cvid == null) {
                    // 多版本兼容
                    List<Integer> cvids = fastMiniContVersionService.getIdByMiniId(miniItem.getId());
                    if (CollUtil.isNotEmpty(cvids)) {
                        for (Integer cvidItem : cvids) {
                            RedisUtil.incr(StaticVar.MINI_CONTENT_SETTING_VER + miniItem.getId() + ":" + cvidItem);
                        }
                    }
                } else {
                    RedisUtil.incr(StaticVar.MINI_CONTENT_SETTING_VER + miniItem.getId() + ":" + cvid);
                }
            }
        } else {
            RedisUtil.incr(StaticVar.MINI_CONTENT_SETTING_VER + miniId + ":" + cvid);
        }
    }

    /**
     * 删除用户token
     */
    public void deleteMemberToken(Long memberId) {
//    	RedisUtil.del(FORMAT_ZERO);
    }


}
