package com.fast.service.bosskg;

import com.alibaba.fastjson.JSON;
import com.fast.enums.FunCodeEnum;
import com.fast.po.bosskg.ResponseMessageVO;
import com.fast.po.bosskg.SignSohoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/***
 *
 *
 * 描    述：用户签约接口
 *
 * 创 建 者：<AUTHOR>
 * 创建时间：2021/7/22下午4:35
 * 创建描述：
 *
 */
@Service
@Slf4j
public class UserSignService extends BaseService {


    /**
     * @param funCodeEnum
     * @param signSohoDTO
     * @return
     * @throws Exception
     */
    public SignSohoDTO doBusiness(FunCodeEnum funCodeEnum, SignSohoDTO signSohoDTO, Integer retailId) {
        log.info("用户签约接口开始:{}，{}", funCodeEnum, JSON.toJSONString(signSohoDTO));
        ResponseMessageVO responseMessage = applyYouFuByHttpPost(funCodeEnum, signSohoDTO, retailId);
        SignSohoDTO sohoDTO = JSON.parseObject(responseMessage.getResData(), SignSohoDTO.class);
        log.info("用户签约接口结束:{}", JSON.toJSONString(sohoDTO));
        return sohoDTO;
    }

}
