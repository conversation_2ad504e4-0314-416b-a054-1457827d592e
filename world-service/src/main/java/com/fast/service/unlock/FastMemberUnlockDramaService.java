/*
 * Powered By fast.up
 */
package com.fast.service.unlock;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberAccountMapper;
import com.fast.mapper.member.FastMemberOrderConsumeMapper;
import com.fast.mapper.task.FastMemberTaskRechargeMapper;
import com.fast.mapper.unlock.FastMemberUnlockDramaMapper;
import com.fast.po.member.FastMemberAccountPO;
import com.fast.po.member.FastMemberAdmobLogPO;
import com.fast.po.member.FastMemberOrderConsumePO;
import com.fast.po.member.FastMemberUnlockDramaPO;
import com.fast.service.base.BaseService;
import com.fast.service.member.FastMemberAccountService;
import com.fast.service.member.FastMemberAdmobLogService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.task.FastMemberTaskActionService;
import com.fast.service.task.FastMemberTaskRechargeService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.fee.FastFeeRuleVO;
import com.fast.vo.member.MemberAccountVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 短剧剧集解锁
 *
 * <AUTHOR>
 */
@Service
public class FastMemberUnlockDramaService extends BaseService {

    @Autowired
    private FastMemberUnlockDramaMapper unlockDramaMapper;
    @Autowired
    private FastMemberAccountMapper accountMapper;
    @Autowired
    private FastMemberOrderConsumeMapper consumeMapper;
    @Autowired
    private FastMemberAccountService accountService;
    @Autowired
    private FastMemberTaskRechargeMapper fastMemberTaskRechargeMapper;
    @Autowired
    private FastMemberTaskRechargeService fastMemberTaskRechargeService;
    @Autowired
    private FastMemberTaskActionService fastMemberTaskActionService;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMemberAdmobLogService fastMemberAdmobLogService;

    /**
     * 查询剧集解锁缓存
     */
    public List<Integer> queryInfoByRedis(Long memberId, Integer dramaId) {
        List<Integer> unlockIds;
        // 查询已经解锁
        String key = StaticVar.MEMBER_SERIES_UNLOCK + memberId + StaticVar.UNDERLINE + dramaId;
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            unlockIds = CollUtil.parseIntStr2List(value);
        } else {
            FastMemberUnlockDramaPO unlockParam = new FastMemberUnlockDramaPO();
            unlockParam.setMemberId(memberId);
            unlockParam.setDramaId(dramaId);
            unlockIds = unlockDramaMapper.querySeriesNumList(unlockParam);
            if (CollUtil.hasContent(unlockIds)) {
                CollUtil.sort(unlockIds);
                RedisUtil.set(key, StrUtil.join(unlockIds), RedisUtil.TIME_12H);
            } else {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_12H);
            }
        }
        return unlockIds;
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberUnlockDramaPO queryById(FastMemberUnlockDramaPO item) {
        return unlockDramaMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberUnlockDramaPO queryById(Long id) {
        FastMemberUnlockDramaPO itemParam = new FastMemberUnlockDramaPO();
        itemParam.setId(id);
        return unlockDramaMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberUnlockDramaPO queryOne(FastMemberUnlockDramaPO item) {
        return unlockDramaMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberUnlockDramaPO> queryList(FastMemberUnlockDramaPO item) {
        return unlockDramaMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberUnlockDramaPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberUnlockDramaPO> list = unlockDramaMapper.queryList(item);
        for (FastMemberUnlockDramaPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberUnlockDramaPO item) {
        return unlockDramaMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberUnlockDramaPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (unlockDramaMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 解锁剧集
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO unlockDrama(SessionVO sessionVO, FastMemberUnlockDramaPO params, FastFeeRuleVO feeRule, FastMemberAdmobLogPO admobLog) {
        Date nowTime = DateUtil.getNowDate();

        // IAA
        if (Objects.equals(params.getType(), 1)) {
            // 消耗记录
            FastMemberAdmobLogPO logUpdate = new FastMemberAdmobLogPO();
            logUpdate.setId(admobLog.getId());
            logUpdate.setState(1);
            logUpdate.setUpdateTime(nowTime);
            MethodVO methodVO = fastMemberAdmobLogService.update(logUpdate);
            if (methodVO.getCode() != 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
            // 解锁记录
            params.setCreateTime(nowTime);
            if (unlockDramaMapper.insertSelective(params) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
            log.info("用户 {} 成功解锁短剧 {} 的第 {} 集，消耗的admob记录 {}", sessionVO.getMemberId(), params.getDramaId(), params.getSeriesNum(), admobLog.getId());
        }
        // IAP
        else {
            // 剧集解锁需要的金币，也就是需要扣掉用户账户的金币数量
            Integer coinPer = feeRule.getCoinPer();

            // 账户信息
            MemberAccountVO account = accountService.queryInfoByRedis(params.getMemberId());
            if (account.getCoinRemain() < coinPer) {
                log.error("用户 {} 的账户可用金币余额 {}，不够支付 {}，短剧:{}，剧集:{}", sessionVO.getMemberId(), account.getCoinRemain(), coinPer, params.getDramaId(), params.getSeriesNum());
                return MethodVO.error(StaticStr.INSUFFICIENT_COINS_IN_ACCOUNT);
            }

            // 赠送金币
            int giveRemainDeduct;
            // 任务金币
            int taskRemainDeduct = 0;
            // 充值金币
            int rechargeRemainDeduct = 0;

            // 扣费顺序：赠币、任务币、充值币
            if (account.getCoinGiveRemain() >= coinPer) {
                giveRemainDeduct = coinPer;
                coinPer = 0;
            } else {
                giveRemainDeduct = account.getCoinGiveRemain();
                coinPer = coinPer - taskRemainDeduct;
                // 赠送金币不够，扣任务币
                if (account.getCoinTaskRemain() >= coinPer) {
                    taskRemainDeduct = coinPer;
                    coinPer = 0;
                } else {
                    taskRemainDeduct = account.getCoinTaskRemain();
                    coinPer = coinPer - taskRemainDeduct;
                    // 任务金币不够，扣充值币
                    if (account.getCoinRechargeRemain() >= coinPer) {
                        rechargeRemainDeduct = coinPer;
                        coinPer = 0;
                    } else {
                        rechargeRemainDeduct = account.getCoinRechargeRemain();
                        coinPer = coinPer - rechargeRemainDeduct;
                    }
                }
            }
            // 按顺序扣完，如果还不够，那就是第一个校验有问题，证明账户总账和细账不对
            if (coinPer > 0) {
                log.error("用户 {} 的账户可用金币余额不对等，总金币:{}，充值金币:{}，赠送金币:{}，任务金币:{}", sessionVO.getMemberId(),
                        account.getCoinRemain(), account.getCoinRechargeRemain(), account.getCoinGiveRemain(), account.getCoinTaskRemain());
                return MethodVO.error(StaticStr.INSUFFICIENT_COINS_IN_ACCOUNT);
            }

            log.error("用户 {} 成功解锁 短剧:{}，剧集:{}，共花费 {} 个金币（{}个任务币，{}个赠送币，{}个充值币）", sessionVO.getMemberId(), params.getDramaId(), params.getSeriesNum(),
                    feeRule.getCoinPer(), taskRemainDeduct, giveRemainDeduct, rechargeRemainDeduct);

            // 同步账户扣除
            FastMemberAccountPO accountPO = new FastMemberAccountPO();
            accountPO.setMemberId(params.getMemberId());
            accountPO.setCoinRechargeAll(rechargeRemainDeduct);
            accountPO.setCoinGiveAll(giveRemainDeduct);
            accountPO.setCoinTaskAll(taskRemainDeduct);
            accountPO.setCoinAll(feeRule.getCoinPer());
            accountPO.setCoinRechargeRemain(rechargeRemainDeduct);
            accountPO.setCoinGiveRemain(giveRemainDeduct);
            accountPO.setCoinTaskRemain(taskRemainDeduct);
            accountPO.setCoinRemain(feeRule.getCoinPer());
            accountPO.setRemark(StaticStr.UNLOCK_DRAMASERIES);
            MethodVO methodVO = accountService.updateDeductById(accountPO);
            if (methodVO.getCode() != 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }

            // 记录用户消费日志
            FastMemberOrderConsumePO consume = new FastMemberOrderConsumePO();
            consume.setCreateTime(nowTime);
            consume.setMemberId(params.getMemberId());
            consume.setDramaId(params.getDramaId());
            consume.setSeriesNum(params.getSeriesNum());
            consume.setRetailId(sessionVO.getRetailId());
            consume.setOfficialId(sessionVO.getOfficialId());
            consume.setMiniId(sessionVO.getMiniId());
            consume.setLinkId(sessionVO.getLinkId());
            consume.setCoinRechargeConsume(rechargeRemainDeduct);
            consume.setCoinGiveConsume(giveRemainDeduct);
            consume.setCoinTaskConsume(taskRemainDeduct);
            consume.setCoinConsume(feeRule.getCoinPer());
            consume.setPhoneOs(sessionVO.getPhoneOs());
            if (consumeMapper.insertSelective(consume) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }

            // 解锁记录
            params.setConsumeId(consume.getId());
            params.setCreateTime(nowTime);
            if (unlockDramaMapper.insertSelective(params) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }

            // 添加一把短剧解锁的锁,方便后续使用(此行代码勿动)
            RedisUtil.set(StaticVar.DRAMA_HAS_UNLOCK + params.getMemberId() + "_" + params.getDramaId() + "_" + params.getSeriesNum(), feeRule.getCoinPer().toString(), RedisUtil.TIME_90D);
        }

        // 清除缓存信息
        String key = StaticVar.MEMBER_SERIES_UNLOCK + params.getMemberId() + StaticVar.UNDERLINE + params.getDramaId();
        RedisUtil.del(key);
        key = StaticVar.MEMBER_ACCOUNT_ID + params.getMemberId();
        RedisUtil.del(key);

        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberUnlockDramaPO> list) {
        if (unlockDramaMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberUnlockDramaPO item) {
        if (unlockDramaMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
