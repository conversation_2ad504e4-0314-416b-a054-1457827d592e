/*
 * Powered By fast.up
 */
package com.fast.service.unlock;

import com.alibaba.fastjson.JSON;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.mapper.setting.FastMiniRewardAdSettingMapper;
import com.fast.mapper.unlock.FastMemberUnlockDramaMapper;
import com.fast.mapper.unlock.FastMemberUnlockRewardLogMapper;
import com.fast.mapper.unlock.FastMemberUnlockStartLogMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.fee.FastFeeKeepBatchPO;
import com.fast.po.fee.FastFeeKeepDetailPO;
import com.fast.po.member.FastMemberUnlockDramaPO;
import com.fast.po.member.FastMemberUnlockRewardLogPO;
import com.fast.po.setting.FastMiniRewardAdSettingPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.fee.FastFeeKeepBatchService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.fee.FastFeeRuleVO;
import com.fast.vo.promote.FastLinkVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberUnlockRewardLogService extends BaseService {

    @Autowired
    private FastMemberUnlockRewardLogMapper fastMemberUnlockRewardLogMapper;
    @Autowired
    private FastMemberUnlockDramaMapper fastMemberUnlockDramaMapper;
    @Autowired
    private FastMiniRewardAdSettingMapper fastMiniRewardAdSettingMapper;
    @Autowired
    private FastDramaMapper fastDramaMapper;
    @Autowired
    private FastMemberUnlockStartLogMapper unlockStartLogMapper;
    @Autowired
    private FastDramaService fastDramaService;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private FastFeeKeepBatchService fastFeeKeepBatchService;
    @Autowired
    private FastMiniService fastMiniService;

    /**
     * 通过id查询单个对象
     */
    public FastMemberUnlockRewardLogPO queryById(FastMemberUnlockRewardLogPO item) {
        return fastMemberUnlockRewardLogMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberUnlockRewardLogPO queryById(Integer id) {
        return fastMemberUnlockRewardLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberUnlockRewardLogPO queryOne(FastMemberUnlockRewardLogPO item) {
        return fastMemberUnlockRewardLogMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberUnlockRewardLogPO> queryList(FastMemberUnlockRewardLogPO item) {
        return fastMemberUnlockRewardLogMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberUnlockRewardLogPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberUnlockRewardLogPO> list = fastMemberUnlockRewardLogMapper.queryList(item);
        for (FastMemberUnlockRewardLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberUnlockRewardLogPO item) {
        return fastMemberUnlockRewardLogMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberUnlockRewardLogPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberUnlockRewardLogMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberUnlockRewardLogPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberUnlockRewardLogMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 完成观看广告记录日志-解锁剧集
     *
     * @param sessionVO
     * @param params
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> insertRewardAdLog(SessionVO sessionVO, FastMemberUnlockRewardLogPO params) {
        FastLinkVO linkVO = null;
        if (biggerZero(params.getLinkId())) {
            linkVO = linkService.queryInfoByRedis(params.getLinkId());
        }
        Date nowDate = DateUtil.getNowDate();
        // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  账号信息:" + JSON.toJSON(sessionVO));
        actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  参数信息:" + JSON.toJSON(params));
        String msg = "";

        // 1.查询短剧起始解锁集数，最大集数
        FastDramaPO fastDramaPO = fastDramaMapper.queryStartNumByDramaId(params.getDramaId());
        // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  短剧信息:" + JSON.toJSON(fastDramaPO));
        // 查询该剧起始剧集
        FastFeeRuleVO feeRule = fastDramaService.getFeeRule(sessionVO.getLinkId(), sessionVO.getOfficialId(), params.getDramaId());
        Integer ruleStartNum = feeRule.getStartNum();// 起始剧集
        // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  规则信息:" + JSON.toJSON(feeRule));
        if (fastDramaPO == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

        Integer startNum = 0;           // 解锁起始集数
        Integer perUnlockAmount = 1;    // 解锁数量

        if (params.getUnlockType() == 1) {// 下滑解锁
            if (linkVO != null && linkVO.getFeeFlag() != null && linkVO.getFeeFlag() == 2) {
                // 免费链路
                if (sessionVO.getPhoneOs() == 1) {
                    perUnlockAmount = linkVO.getAnUnlockNum();
                } else if (sessionVO.getPhoneOs() == 2) {
                    perUnlockAmount = linkVO.getIosUnlockNum();
                }
                actionLogService.log("free_link_unlock", "免费渠道解锁获取解锁几集,perUnlockAmount=" + perUnlockAmount);
            } else {
                // 付费和混合投放
                // 2.查询配置，每次解锁N集
                FastMiniRewardAdSettingPO queryParam = new FastMiniRewardAdSettingPO();
                queryParam.setMiniId(params.getMiniId().longValue());
                queryParam.setEnterType(params.getEnterType());
                FastMiniRewardAdSettingPO setting = fastMiniRewardAdSettingMapper.queryOne(queryParam);
                // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  配置信息:" + JSON.toJSON(setting));
                if (setting == null) {
                    return ResultVO.error(StaticStr.SYS_ERROR);
                }
                if (sessionVO.getPhoneOs() == 1 && setting.getAndroidAllowFlag() == 0) {
                    return ResultVO.error(StaticStr.SYS_ERROR);
                }
                if (sessionVO.getPhoneOs() == 2 && setting.getIosAllowFlag() == 0) {
                    return ResultVO.error(StaticStr.SYS_ERROR);
                }
                if (sessionVO.getPhoneOs() == 1) {
                    perUnlockAmount = setting.getAndroidPerUnlockAmount();
                } else if (sessionVO.getPhoneOs() == 2) {
                    perUnlockAmount = setting.getIosPerUnlockAmount();
                }
            }
        } else if (params.getUnlockType() == 2) {// 挽留解锁
            List<FastFeeKeepDetailPO> detailList = new ArrayList<>();
            if (linkVO != null && linkVO.getKeepType() != null && linkVO.getKeepType() == 2 && biggerZero(linkVO.getKeepId())) {
                // 自定义
                List<FastFeeKeepDetailPO> keepDetailList = fastFeeKeepBatchService.getKeepDetailList(linkVO.getKeepId());
                if (CollUtil.isNotEmpty(keepDetailList)) {
                    detailList = keepDetailList.stream().filter(e -> e.getState() == 1).collect(Collectors.toList());
                }
            }
            if (detailList.isEmpty()) {
                // 查全局
                FastFeeKeepBatchPO kbParam = new FastFeeKeepBatchPO();
                kbParam.setRetailId(sessionVO.getRetailId());
                kbParam.setOfficialId(sessionVO.getOfficialId());
                kbParam.setDelFlag(0);
                kbParam.setFeeFlag(2);// 免费广告
                FastFeeKeepBatchPO keepBatch = fastFeeKeepBatchService.getKeepGlobal(kbParam);
                if (keepBatch != null) {
                    keepBatch.setEncryptionId(encode(keepBatch.getId()));
                    detailList = keepBatch.getDetailList().stream().filter(e -> e.getState() == 1).collect(Collectors.toList());
                }
            }
            if (CollUtil.isNotEmpty(detailList) && (detailList.get(0) != null) && (detailList.get(0).getUnlockSeriesNum() != null)) {
                perUnlockAmount = detailList.get(0).getUnlockSeriesNum();
            }
        } else if (params.getUnlockType() == 3) {
            // 链接广告解锁
            if (linkVO.getAdvUnlockFlag() == 1) {
                perUnlockAmount = linkVO.getAdvUnlockNum();
            } else {
                return ResultVO.error(StaticStr.FREE_AD_UNLOCK_NOT_SUPPORTED);
            }
        }
        // 3.查询已经解锁的最大集数
        params.setMemberId(sessionVO.getMemberId());
        params.setStartNum(ruleStartNum);
        Integer unlockMaxSeriesNum = fastMemberUnlockDramaMapper.queryUnlockDramaNum(params);
        // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  最大解锁集数:" + JSON.toJSON(unlockMaxSeriesNum));
        // 4.需要解锁的集数
        List<Integer> unlockNumList = new ArrayList<>();
        Integer linkSkipSeries = 0;
        if (linkVO != null && linkVO.getSkipSeries() != null && linkVO.getSkipSeries() == 1) {
            linkSkipSeries = 1;
        }
        if (fastDramaPO.getSkipSeries() == 1 || linkSkipSeries == 1) {
            // 支持跳级解锁
            startNum = params.getStartSeriesNum();
            // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  支持跳级解锁，解锁集数是:" + startNum);
        } else if (fastDramaPO.getSkipSeries() == 0) {
            // 不支持跳级解锁
            if (unlockMaxSeriesNum == null || unlockMaxSeriesNum == 0) {// 未解锁过剧集
                startNum = ruleStartNum;
            } else {// 解锁过，按顺序解锁后一集
                startNum = unlockMaxSeriesNum + 1;
            }
            // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  不支持跳级解锁，解锁集数是:" + startNum);
        }
        // 超过最大集数
        if (startNum > fastDramaPO.getSeriesNumAll()) {
            // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  解锁失败，解锁集数不存在或者已全部解锁");
            return ResultVO.error(StaticStr.UNLOCK_FAILED);
        }
        // 解锁短剧
        for (int i = 0; i < perUnlockAmount; i++) {
            // 5.数据写入短剧解锁记录表
            Integer seriesNum = startNum + i;
            FastMemberUnlockDramaPO queryUnlock = new FastMemberUnlockDramaPO();
            queryUnlock.setMemberId(sessionVO.getMemberId());
            queryUnlock.setSeriesNum(seriesNum);
            queryUnlock.setDramaId(params.getDramaId());
            FastMemberUnlockDramaPO existUnlock = fastMemberUnlockDramaMapper.queryOne(queryUnlock);
            if (existUnlock == null && seriesNum <= fastDramaPO.getSeriesNumAll()) {
                FastMemberUnlockDramaPO fastMemberUnlockDramaPO = new FastMemberUnlockDramaPO();
                fastMemberUnlockDramaPO.setMemberId(sessionVO.getMemberId());
                fastMemberUnlockDramaPO.setDramaId(params.getDramaId());
                fastMemberUnlockDramaPO.setSeriesNum(seriesNum);
                fastMemberUnlockDramaPO.setConsumeId(0L);
                fastMemberUnlockDramaPO.setCreateTime(nowDate);
                fastMemberUnlockDramaMapper.insertSelective(fastMemberUnlockDramaPO);
                unlockNumList.add(seriesNum);
            }
        }
        String unlockNum = CollUtil.convertIntListToString(unlockNumList);
        // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  解锁集数:" + JSON.toJSON(unlockNum));
        // 6.数据写入短剧看广告解锁记录表
        if (unlockNumList.isEmpty()) {
            actionLogService.log("tiktok_ad_reward", "insertRewardAdLog   重复解锁");
            msg = "请勿重复解锁";
            return ResultVO.error(msg);
        }
        FastMemberUnlockRewardLogPO rewardLogPO = new FastMemberUnlockRewardLogPO();
        rewardLogPO.setLinkId(params.getLinkId());
        rewardLogPO.setMiniId(params.getMiniId());
        rewardLogPO.setAdUnitId(params.getAdUnitId());
        rewardLogPO.setEnterType(params.getEnterType());
        rewardLogPO.setMemberId(sessionVO.getMemberId());
        rewardLogPO.setDramaId(params.getDramaId());
        rewardLogPO.setSeriesNum(unlockNum);
        rewardLogPO.setCreateTime(nowDate);
        rewardLogPO.setCreateDate(nowDate);
        insert(rewardLogPO);

        // 7.删除改账号下已缓存的短剧
        String key = StaticVar.MEMBER_SERIES_UNLOCK + sessionVO.getMemberId() + StaticVar.UNDERLINE + params.getDramaId();
        RedisUtil.del(key);
        // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog  看广告解锁短剧日志信息:" + JSON.toJSON(rewardLogPO));
        msg = "已解锁第" + unlockNum + "集";
        // actionLogService.log("tiktok_ad_reward", "insertRewardAdLog   记录日志成功，耗时" + stopwatch.getTotalTimeSeconds() + "秒");
        if (biggerZero(params.getWatchAdStartId())) {
            unlockStartLogMapper.updateWatchEnd(params.getWatchAdStartId(), nowDate);
        }
        return ResultVO.success(msg);
    }
}
