/*
 * Powered By fast.up
 */
package com.fast.service.unlock;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.ContentTypeEnum;
import com.fast.enums.MiniTypeEnum;
import com.fast.framework.thread.ExportThreadExecutor;
import com.fast.mapper.member.FastMemberLinkAdvMapper;
import com.fast.mapper.member.FastMemberMapper;
import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.mapper.promote.FastLinkMapper;
import com.fast.mapper.unlock.FastMemberUnlockStartLogAdvMapper;
import com.fast.mapper.unlock.FastMemberUnlockStartLogMapper;
import com.fast.po.member.FastMemberLinkAdvPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.member.FastMemberPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.unlock.FastMemberUnlockStartLogAdvPO;
import com.fast.po.unlock.FastMemberUnlockStartLogPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.*;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 广告解锁开始日志
 *
 * <AUTHOR>
 */
@Service
public class FastMemberUnlockStartLogService extends BaseService {

    @Autowired
    private FastMemberUnlockStartLogMapper startLogMapper;
    @Autowired
    private FastMemberOrderRechargeMapper rechargeMapper;
    @Autowired
    private FastMemberMapper memberMapper;
    @Autowired
    private FastLinkMapper linkMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastMemberLinkAdvMapper fastMemberLinkAdvMapper;
    @Autowired
    private FastMemberUnlockStartLogAdvMapper fastMemberUnlockStartLogAdvMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberUnlockStartLogPO queryById(FastMemberUnlockStartLogPO params) {
        return startLogMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberUnlockStartLogPO queryById(Long id) {
        return startLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberUnlockStartLogPO queryOne(FastMemberUnlockStartLogPO params) {
        return startLogMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberUnlockStartLogPO> queryList(FastMemberUnlockStartLogPO params) {
        List<FastMemberUnlockStartLogPO> list = startLogMapper.queryList(params);
        for (FastMemberUnlockStartLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return list;
    }

    /**
     * 三方接口
     */
    public ResultVO querygetRechargeFreeList(FastMemberUnlockStartLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberUnlockStartLogPO> list = startLogMapper.querygetRechargeFreeList(params);
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<Map<String, Object>> queryPageList(FastMemberUnlockStartLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberUnlockStartLogPO> list = queryList(params);
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询用户数
     */
    public ResultVO<FastMemberUnlockStartLogPO> getMemberSummary(FastMemberUnlockStartLogPO params) {
        int memberCount = startLogMapper.queryMemberCount(params);
        FastMemberUnlockStartLogPO summary = new FastMemberUnlockStartLogPO();
        summary.setMemberCount(memberCount);
        return ResultVO.success(summary);
    }

    /**
     * 查询ECPM数据
     */
    public ResultVO<FastMemberUnlockStartLogPO> getEcpmSummary(FastMemberUnlockStartLogPO params) {
        FastMemberUnlockStartLogPO summary = startLogMapper.querySummary(params);
        if (summary == null) {
            summary = new FastMemberUnlockStartLogPO();
            summary.setMemberCount(0);
            summary.setEcpmCostSum(BigDecimal.ZERO);
            summary.setEcpmCostAvg(BigDecimal.ZERO);
        } else {
            if (summary.getEcpmCount() != null && summary.getEcpmCount() > 0) {
                summary.setEcpmCostAvg(DoubleUtil.divB(DoubleUtil.mulB(summary.getEcpmCostSum(), BigDecimalVar.BD_1000, 5), summary.getEcpmCount(), 5).stripTrailingZeros());
            } else {
                summary.setEcpmCostAvg(BigDecimal.ZERO);
            }
        }
        return ResultVO.success(summary);
    }

    /**
     * 查询ECPM数据
     */
    public ResultVO<FastMemberUnlockStartLogPO> getRechargeSummary(FastMemberUnlockStartLogPO params) {
        FastMemberOrderRechargePO query = BeanUtil.toBean(params, FastMemberOrderRechargePO.class);
        if (notEmpty(params.getCostTimeStr())) {
            query.setPayTimeS(params.getCostTimeS());
            query.setPayTimeE(params.getCostTimeE());
        }
        query.setState(1);
        query.setCoinChangeId(0);
        BigDecimal rechargeMoney = defaultIfNull(rechargeMapper.queryFreeLinkMoneySum(query), BigDecimal.ZERO);
        FastMemberUnlockStartLogPO summary = new FastMemberUnlockStartLogPO();
        summary.setRechargeMoney(rechargeMoney);
        return ResultVO.success(summary);
    }

    /**
     * 设置Ecpm平均价格
     *
     * @param startLogQ
     */
    @Async
    public void setEcpmAvg(FastMemberUnlockStartLogPO startLogQ, Long id, Integer linkId) {
        startLogQ.setCreateTimeS(null);
        startLogQ.setCreateTimeE(null);
        startLogQ.setEcpmIdHas(1);
        startLogQ.setMaxId(id + 1);
        FastMemberUnlockStartLogPO po = startLogMapper.querySummary(startLogQ);
        if (po == null) {
            po = new FastMemberUnlockStartLogPO();
            po.setEcpmCostAvg(BigDecimal.ZERO);
        } else {
            if (po.getEcpmCount() != null && po.getEcpmCount() > 0) {
                po.setEcpmCostAvg(DoubleUtil.divB(DoubleUtil.mulB(po.getEcpmCostSum(), BigDecimalVar.BD_1000, 5), po.getEcpmCount(), 5).stripTrailingZeros());
            } else {
                po.setEcpmCostAvg(BigDecimal.ZERO);
            }
        }
        po.setId(id);
        // po.setArpu(po.getEcpmCostSum()); 2024-11-15 修改成查主库
        po.setArpu(defaultIfNull(startLogMapper.querySummaryLink(startLogQ), BigDecimal.ZERO));

        startLogQ.setLinkId(linkId);
        po.setArpuLink(defaultIfNull(startLogMapper.querySummaryLink(startLogQ), BigDecimal.ZERO));
        startLogMapper.updateEcpmCost(po);
    }

    /**
     * 广告接收明细-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    @Async(ExportThreadExecutor.NAME)
    public void exportList(SessionVO sessionVO, FastMemberUnlockStartLogPO params) {
        int size = startLogMapper.queryList_COUNT(params);
        Long maxId = null; // 最大id
        List<FastMemberUnlockStartLogPO> list = new ArrayList<>(size + 10);
        params.setLimitExport(StaticVar.DATA_1W);
        do {
            params.setMaxId(maxId);
            List<FastMemberUnlockStartLogPO> temp = startLogMapper.queryList(params);
            if (CollUtil.hasContent(temp)) {
                list.addAll(temp);

                maxId = temp.get(temp.size() - 1).getId();
            }
            if (temp.size() < StaticVar.DATA_1W) {
                break;
            }
        } while (true);

        if (CollUtil.isEmpty(list)) {
            return; // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberUnlockStartLogPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getMemberId());
            CollUtil.addNoRepeat(rowHeadNames, "用户ID");

            row.add(cur.getEcpmCost());
            CollUtil.addNoRepeat(rowHeadNames, "单次ECPM价格");

            row.add(cur.getRechargeMoney());
            CollUtil.addNoRepeat(rowHeadNames, "累积充值金额");

            row.add(cur.getEcpmCostAvg());
            CollUtil.addNoRepeat(rowHeadNames, "截止本次ECPM平均(*1000)");

            row.add(cur.getArpu());
            CollUtil.addNoRepeat(rowHeadNames, "截止本次ARPU(用户维度)");

            row.add(cur.getArpuLink());
            CollUtil.addNoRepeat(rowHeadNames, "截止本次ARPU(当前渠道维度)");

            row.add(cur.getWatchSeq());
            CollUtil.addNoRepeat(rowHeadNames, "广告接收次数");

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "应用");

            row.add(MiniTypeEnum.getName(defaultIfNull(cur.getMiniType(), 0)));
            CollUtil.addNoRepeat(rowHeadNames, "应用类型");

            row.add(cur.getDramaName());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "名称");

            row.add(cur.getPhoneOs() == 1 ? "安卓" : cur.getPhoneOs() == 2 ? "ios" : "未知");
            CollUtil.addNoRepeat(rowHeadNames, "手机系统");

            row.add(DateUtil.format07(cur.getCostTime()));
            CollUtil.addNoRepeat(rowHeadNames, "计费时间");

            row.add(DateUtil.format07(cur.getRegisterTime()));
            CollUtil.addNoRepeat(rowHeadNames, "注册时间");

            row.add(DateUtil.format07(cur.getLinkTime()));
            CollUtil.addNoRepeat(rowHeadNames, "染色时间");

            row.add(cur.getLinkId());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接ID");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接名称");

            if (sessionVO.getRetailId() == 0) {
                row.add(cur.getRetailName());
                CollUtil.addNoRepeat(rowHeadNames, "所属分销商");
            }

            row.add(cur.getBackState() == 1 ? "未回传" : cur.getBackState() == 2 ? "回传成功" : cur.getBackState() == 3 ? "回传失败" : "未知");
            CollUtil.addNoRepeat(rowHeadNames, "回传状态");

            row.add(cur.getBackInfo());
            CollUtil.addNoRepeat(rowHeadNames, "上报信息");

            row.add("'" + defaultIfEmpty(cur.getAid(), ""));
            CollUtil.addNoRepeat(rowHeadNames, "广告计划id");

            row.add("'" + defaultIfEmpty(cur.getCid(), ""));
            CollUtil.addNoRepeat(rowHeadNames, "广告创意id");

            row.add("'" + defaultIfEmpty(cur.getPromotionId(), ""));
            CollUtil.addNoRepeat(rowHeadNames, "广告id");

            row.add("'" + defaultIfEmpty(cur.getProjectId(), ""));
            CollUtil.addNoRepeat(rowHeadNames, "项目id");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return;// 您选择的导出列至少要有2项
        }
        String title = "广告接收明细";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, params.getExportKey());
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberUnlockStartLogPO params) {
        return startLogMapper.queryCount(params);
    }

    /**
     * 开始观看广告记录日志
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> insertWatchAdStart(FastMemberUnlockStartLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setCreateDate(nowTime);

        FastMemberPO memberPO = memberMapper.queryOneByMemberId(params.getMemberId());
        if (memberPO == null) {
            return null;
        }
        params.setRetailId(memberPO.getRetailId());
        params.setMemberId(memberPO.getId());
        params.setOfficialId(memberPO.getOfficialId());
        params.setLinkId(memberPO.getLastLinkId());
        params.setLinkTime(memberPO.getLastLinkTime());
        params.setRegisterTime(memberPO.getCreateTime());
        params.setPhoneOs(memberPO.getPhoneOs());
        params.setAid(memberPO.getAid());
        params.setCid(memberPO.getCid());
        if (!"__PROMOTION_ID__".equals(memberPO.getPromotionId())) {
            params.setPromotionId(memberPO.getPromotionId());
        }
        if (!"__PROJECT_ID__".equals(memberPO.getProjectId())) {
            params.setProjectId(memberPO.getProjectId());
        }
        params.setClickId(memberPO.getClickId());
        // 如果最后一次染色时间是今天则表示当前用户是新增用户
        if (DateUtil.format06Int(memberPO.getLastLinkTime()) == DateUtil.format06Int(params.getCreateTime())) {
            params.setAddState(1);
        } else {
            params.setAddState(0);
        }

        FastLinkPO linkPO = linkMapper.queryById(params.getLinkId());
        if (linkPO == null) {
            params.setAdvUserId(0);
        } else {
            params.setAdvUserId(linkPO.getAdvUserId());
            params.setMiniId(linkPO.getMiniId());
        }

        FastMemberUnlockStartLogPO startLog = new FastMemberUnlockStartLogPO();
        startLog.setMemberId(params.getMemberId());
        int count = startLogMapper.queryCount(startLog);
        params.setWatchSeq(count + 1);

        // 写入广告记录
        if (startLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return ResultVO.error(StaticStr.ADD_FAILED);
        }
        // 写入免费广告素材
        FastMemberLinkAdvPO maPO = fastMemberLinkAdvMapper.queryById(params.getMemberId());
        if (maPO != null) {
            FastMemberUnlockStartLogAdvPO insert = new FastMemberUnlockStartLogAdvPO();
            insert.setId(params.getId());
            if (StrUtil.isNotEmpty(maPO.getMaterialId())) {
                insert.setMaterialId(maPO.getMaterialId());
            }
            insert.setCreateTime(DateUtil.getNowDate());
            if (fastMemberUnlockStartLogAdvMapper.insertSelective(insert) == 0) {
                actionLogService.log("unlock_log_material", "免费广告素材Id写入失败:" + JSONObject.toJSONString(insert));
            } else {
                actionLogService.log("unlock_log_material", "免费广告素材Id写入成功");
            }
        }
        // 设置次数
        // fastBackRuleFreeService.setStartTimes(params);
        // 免费回传
        // fastBackRuleFreeService.backToMediaFreePlus(params, 1);
        return ResultVO.success(new JSONObject().fluentPut("watchAdStartId", params.getId()));
    }

    /**
     * 结束观看广告记录日志
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> closeWatchAd(FastMemberUnlockStartLogPO params) {
        if (startLogMapper.updatePlaySeconds(params.getWatchAdStartId(), params.getPlaySeconds(), params.getState()) == 0) {
            return ResultVO.error(StaticStr.UPDATE_FAILED);
        }
        return ResultVO.success();
    }

    /**
     * 更新观看广告记录state
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> updateWatchAdState(FastMemberUnlockStartLogPO params) {
        if (startLogMapper.updateWatchAdState(params.getWatchAdStartId(), params.getState()) == 0) {
            return ResultVO.error(StaticStr.UPDATE_FAILED);
        }
        return ResultVO.success();
    }
}
