/*
 * Powered By fast.up
 */
package com.fast.service.drama;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.mapper.drama.FastDramaSeriesMapper;
import com.fast.mapper.fee.FastFeeRuleMapper;
import com.fast.mapper.user.FastUserRecentLogMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.drama.FastDramaSeriesPO;
import com.fast.po.fee.FastFeeRulePO;
import com.fast.po.user.FastUserRecentLogPO;
import com.fast.service.aliyun.AliCdnService;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.drama.FastDramaSeriesExcel;
import com.fast.vo.drama.FastDramaSeriesVO;
import com.fast.vo.drama.SeriesBatchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastDramaSeriesService extends BaseService {

    @Autowired
    private AliCdnService aliCdnService;
    @Autowired
    private FastDramaMapper dramaMapper;
    @Autowired
    private FastFeeRuleMapper feeRuleMapper;
    @Autowired
    private FastDramaSeriesMapper seriesMapper;
    @Autowired
    private FastUserRecentLogMapper fastUserRecentLogMapper;
    @Autowired
    private FastDramaCaptionsService fastDramaCaptionsService;

    /**
     * 智能排序
     *
     * @param params
     * @return
     */
    @Transactional
    public MethodVO resortByMind(FastDramaSeriesPO params) {
        // 查询全部剧集
        FastDramaSeriesPO seriesParam = new FastDramaSeriesPO();
        seriesParam.setDramaId(params.getDramaId());
        seriesParam.setDelFlag(0);
        if (params.getPlayType() != null) {
            seriesParam.setPlayType(params.getPlayType());
        }
        List<FastDramaSeriesPO> seriesList = seriesMapper.queryList(seriesParam);
        for (FastDramaSeriesPO item : seriesList) {
            if (item.getSeriesNumPart() == null || item.getSeriesNumPart() == 0) {
                item.setSeriesNumPart(Double.valueOf(item.getSeriesNum()));
            }
        }
        // 开始排序
        Collections.sort(seriesList, new Comparator<FastDramaSeriesPO>() {
            @Override
            public int compare(FastDramaSeriesPO o1, FastDramaSeriesPO o2) {
                return Double.compare(o1.getSeriesNumPart(), o2.getSeriesNumPart());
            }
        });
        Date timeNow = DateUtil.getNowDate();
        // 开始更新数据库
        for (int i = 1; i <= seriesList.size(); i++) {
            seriesList.get(i - 1).setSeriesNum(i);
            seriesList.get(i - 1).setSeriesNumPart(Double.valueOf(i));
            seriesList.get(i - 1).setUpdateTime(timeNow);
            seriesList.get(i - 1).setUpdatorId(params.getUpdatorId());
            if (seriesMapper.updateById(seriesList.get(i - 1)) == 0) {
                transactionRollBack();
                return MethodVO.error("设置第" + i + "集异常");
            }
        }
        return MethodVO.success();
    }

    /**
     * 缓存查询剧集信息
     *
     * @param dramaId
     * @param seriesNum
     * @return
     */
    public FastDramaSeriesVO queryInfoByRedis(Integer dramaId, Integer seriesNum, String lang, Integer captionType) {
        if (dramaId == null || seriesNum == null) {
            return null;
        }
        FastDramaSeriesVO po;
        String key = StaticVar.DRAMA_SERIES_NUM + dramaId + "_" + lang + "_" + seriesNum + "_" + captionType;
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            po = JsonUtil.toJavaObject(value, FastDramaSeriesVO.class);
        } else {
            FastDramaSeriesPO query = new FastDramaSeriesPO();
            query.setDelFlag(StaticVar.NO);
            query.setDramaId(dramaId);
            query.setLanguageCode(lang);
            query.setCaptionType(captionType);
            query.setSeriesNum(seriesNum);
            po = seriesMapper.queryOneVO(query);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                RedisUtil.set(key, JsonUtil.toString(po), RedisUtil.TIME_2D);
            }
        }
        return po;
    }

    /**
     * 缓存查询剧集信息
     *
     * @param seriesId
     * @return
     */
    public FastDramaSeriesVO queryInfoByIDRedis(Integer seriesId) {
        if (seriesId == null) {
            return null;
        }
        FastDramaSeriesVO po;
        String key = StaticVar.DRAMA_SERIES_ID + seriesId;
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            po = JsonUtil.toJavaObject(value, FastDramaSeriesVO.class);
        } else {
            FastDramaSeriesPO query = new FastDramaSeriesPO();
            query.setDelFlag(StaticVar.NO);
            query.setId(seriesId);
            po = seriesMapper.queryOneVO(query);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                RedisUtil.set(key, JsonUtil.toString(po), RedisUtil.TIME_2D);
            }
        }
        return po;
    }

    /**
     * 缓存查询剧集信息
     *
     * @param item
     * @return
     */
    public List<FastDramaSeriesVO> queryInfoByRedis(FastDramaSeriesPO item, String lang, Integer captionType) {
        if (item.getDramaId() == null) {
            return null;
        }
        List<FastDramaSeriesVO> voList;
        // 先拿对应语言的剧集，拿不到就用英文
        String key = StaticVar.DRAMA_SERIES + item.getDramaId() + ":" + lang + ":" + captionType;
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            voList = JsonUtil.toList(value, FastDramaSeriesVO.class);
        } else {
            item.setDelFlag(StaticVar.NO);
            item.setLanguageCode(lang);
            item.setCaptionType(captionType);
            voList = seriesMapper.query4MemberList(item);
            if (voList == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                RedisUtil.set(key, JsonUtil.toString(voList), RedisUtil.TIME_2D);
            }
        }
        return voList;
    }

    /**
     * 通过id查询单个对象
     */
    public List<FastDramaSeriesVO> queryInfoByRedis(Integer dramaId, String lang, Integer captionType) {
        FastDramaSeriesPO itemParam = new FastDramaSeriesPO();
        itemParam.setDramaId(dramaId);
        return queryInfoByRedis(itemParam, lang, captionType);
    }

    /**
     * 通过ids查询多个对象
     */
    public List<FastDramaSeriesVO> queryInfoByRedis(String dramaIds, String lang, Integer captionType) {
        List<FastDramaSeriesVO> result = new ArrayList<>();
        if (notEmpty(dramaIds)) {
            FastDramaSeriesPO itemParam = new FastDramaSeriesPO();
            List<Integer> idList = CollUtil.parseIntStr2List(dramaIds);
            for (Integer id : idList) {
                itemParam.setDramaId(id);
                List<FastDramaSeriesVO> voList = queryInfoByRedis(itemParam, lang, captionType);
                if (voList != null) {
                    result.addAll(voList);
                }
            }
        }
        return result;
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaSeriesPO queryById(SessionVO sessionVO, FastDramaSeriesPO item) {
        FastDramaSeriesPO seriesPO = seriesMapper.queryById(item);
        if (seriesPO != null) {
            if (sessionVO.getContentType() == 1 || sessionVO.getContentType() == 4) {
                // 短剧
                seriesPO.setUrl(aliCdnService.getVideoFullUrl(seriesPO.getUrl(), sessionVO.getMiniId(), seriesPO.getDramaId()));
            } else if (sessionVO.getContentType() == 2) {
                // 漫画
                List<String> urls = new ArrayList<>();
                List<String> urlNoHost = CollUtil.parseStr2List(seriesPO.getUrl());
                for (String url : urlNoHost) {
                    if (url != null) {
                        urls.add(aliCdnService.getImgFullUrl(url));
                    }
                }
                seriesPO.setUrl(StrUtil.join(urls));
                if (notEmpty(seriesPO.getSeriesCover())) {
                    seriesPO.setSeriesCover(aliCdnService.getImgFullUrl(seriesPO.getSeriesCover()));
                }
            }
        }
        return seriesPO;
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaSeriesPO queryById(Integer id) {
        FastDramaSeriesPO itemParam = new FastDramaSeriesPO();
        itemParam.setId(id);
        return seriesMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastDramaSeriesPO queryOne(FastDramaSeriesPO item) {
        return seriesMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastDramaSeriesPO> queryList(FastDramaSeriesPO item) {
        return seriesMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(SessionVO sessionVO, FastDramaSeriesPO item, PageVO pageVO) {
        // 查询剧详情
        FastDramaPO dramaPO = dramaMapper.queryById(item.getDramaId());
        Integer canDel = 0;// 默认不可删除剧集
        if (dramaPO.getCloneFromId() != null && dramaPO.getCloneFromId() > 0 && (dramaPO.getAuditDramaId() == null || dramaPO.getAuditDramaId() == 0)) {
            canDel = 1; // 克隆剧，未发起审核，可以删除剧集
        }
        // 查询播放源剧
        FastDramaPO fromDramaPO = null;
        if (item.getPlayType() != null && item.getPlayType() == 2) {
            fromDramaPO = dramaMapper.queryById(dramaPO.getPlayTiktok());
        }
        // 查询当前剧配置
        FastFeeRulePO feeRuleParam = new FastFeeRulePO();
        feeRuleParam.setDramaId(item.getDramaId());
        feeRuleParam.setRetailId(item.getRetailId());
        feeRuleParam.setOfficialId(item.getOfficialId());
        FastFeeRulePO feeItem = feeRuleMapper.queryOne(feeRuleParam);
        if (feeItem == null) {
            // 查询平台默认
            feeRuleParam.setRetailId(0);
            feeRuleParam.setOfficialId(0);
            feeItem = feeRuleMapper.queryOne(feeRuleParam);
        }
        startPage(pageVO);
        List<FastDramaSeriesPO> list = seriesMapper.queryList(item);
        // 填充用户观看进度
        if (Objects.nonNull(item.getUserId())) {
            FastUserRecentLogPO fastUserRecentLogPO = new FastUserRecentLogPO();
            fastUserRecentLogPO.setDramaId(item.getDramaId());
            fastUserRecentLogPO.setUserId(item.getUserId());
            List<FastUserRecentLogPO> userRecentLogPOS = fastUserRecentLogMapper.queryList(fastUserRecentLogPO);
            if (CollUtil.isNotEmpty(userRecentLogPOS)) {
                Map<Integer, FastUserRecentLogPO> recentMap = userRecentLogPOS.stream().collect(Collectors.toMap(FastUserRecentLogPO::getSeriesNum, Function.identity(), (i1, i2) -> i1));
                list.forEach(tempItem -> {
                    FastUserRecentLogPO recentLogPO = recentMap.get(tempItem.getSeriesNum());
                    if (Objects.nonNull(recentLogPO)) {
                        tempItem.setPlayRatio(recentLogPO.getPlayRatio());
                        tempItem.setPlaySecond(recentLogPO.getPlaySecond());
                    }
                });
            }
        }
        for (FastDramaSeriesPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setAlbumId(null); // 数据库切换的播控id不展示
            cur.setEpisodeId(null); // 数据库切换的播控id不展示
            if (sessionVO.getContentType() == 1 || sessionVO.getContentType() == 4) {
                // 短剧
                cur.setUrl(aliCdnService.getAliCdnFullUrl(cur.getUrl(), cur.getDramaId()));
            } else if (sessionVO.getContentType() == 2) {
                // 漫画
                List<String> urls = new ArrayList<>();
                List<String> urlNoHost = CollUtil.parseStr2List(cur.getUrl());
                for (String url : urlNoHost) {
                    if (url != null) {
                        urls.add(aliCdnService.getImgFullUrl(url));
                    }
                }
                cur.setUrl(StrUtil.join(urls));
                if (notEmpty(cur.getSeriesCover())) {
                    cur.setSeriesCover(aliCdnService.getImgFullUrl(cur.getSeriesCover()));
                }
            }
            int follow = 0;
            if (feeItem != null && feeItem.getFollowNum().equals(cur.getSeriesNum())) {
                follow = 1;
            }
            cur.setFollow(follow);
            cur.setCanDel(canDel);
            cur.setCaptionLangs(fastDramaCaptionsService.getSubLangs(cur.getDramaId(), cur.getLanguageCode(), cur.getSeriesNum()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        results.put("fromDrama", fromDramaPO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastDramaSeriesPO item) {
        return seriesMapper.queryCount(item);
    }

    /**
     * 查询总数
     */
    public int queryMaxSeriesNum(FastDramaSeriesPO item) {
        return seriesMapper.queryMaxSeriesNum(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastDramaSeriesPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (seriesMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        // 更新短剧更新集数
        FastDramaSeriesPO query = new FastDramaSeriesPO();
        query.setDramaId(item.getDramaId());
        query.setDelFlag(StaticVar.NO);
        Integer maxSeriesNum = seriesMapper.queryMaxSeriesNum(query);
        if (maxSeriesNum != null) {
            FastDramaPO update = new FastDramaPO();
            update.setId(item.getDramaId());
            update.setSeriesNumUpdate(maxSeriesNum);
            update.setUpdateTime(nowTime);
            update.setUpdatorId(item.getUpdatorId());
            dramaMapper.updateById(update);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增小说章节
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(SeriesBatchVO item) {
        FastDramaPO drama = dramaMapper.queryById(item.getDramaId());

        List<FastDramaSeriesPO> seriesList = new ArrayList<>();
        Date nowTime = DateUtil.getNowDate();
        for (FastDramaSeriesPO series : item.getSeriesList()) {
            series.setCreateTime(nowTime);
            series.setUpdateTime(nowTime);
            series.setCreatorId(item.getCreatorId());
            series.setUpdatorId(item.getCreatorId());
            series.setDramaId(item.getDramaId());
            series.setDelFlag(0);
            series.setState(1);
            series.setSeriesTime(series.getUrl().length());
            // 判断是否已经存在
            FastDramaSeriesPO sParam = new FastDramaSeriesPO();
            sParam.setDelFlag(0);
            sParam.setDramaId(item.getDramaId());
            sParam.setSeriesNum(series.getSeriesNum());
            FastDramaSeriesPO sPO = seriesMapper.queryOne(sParam);
            if (sPO == null) {
                seriesList.add(series);
            } else {
                return MethodVO.error("第" + series.getSeriesNum() + "集【" + series.getTitle() + "】已经存在");
            }
        }
        if (seriesMapper.insertBatch(seriesList) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        // 更新短剧更新集数
        FastDramaSeriesPO query = new FastDramaSeriesPO();
        query.setDramaId(item.getDramaId());
        query.setDelFlag(StaticVar.NO);
        Integer maxSeriesNum = seriesMapper.queryMaxSeriesNum(query);
        if (maxSeriesNum != null) {
            FastDramaPO update = new FastDramaPO();
            update.setId(item.getDramaId());
            update.setSeriesNumUpdate(maxSeriesNum);
            update.setUpdateTime(nowTime);
            update.setUpdatorId(item.getCreatorId());
            dramaMapper.updateById(update);
        }
        return MethodVO.success();
    }

    /**
     * 导入短剧剧集
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO importBatch(FastDramaSeriesPO item, List<FastDramaSeriesExcel> importList) {
        Date nowTime = DateUtil.getNowDate();

        // 查询当前短剧剧集
        FastDramaSeriesPO query = new FastDramaSeriesPO();
        query.setDramaId(item.getDramaId());
        query.setLanguageCode(item.getLanguageCode());
        query.setDelFlag(StaticVar.NO);
        List<FastDramaSeriesPO> seriesList = seriesMapper.queryList(query);

        Map<String, FastDramaSeriesPO> seriesMap = new HashMap<>();
        if (CollUtil.isNotEmpty(seriesList)) {
            for (FastDramaSeriesPO po : seriesList) {
                seriesMap.put(po.getDramaId() + "_" + po.getLanguageCode() + "_" + po.getSeriesNum() + "_" + po.getCaptionType(), po);
            }
        }

        item.setCreateTime(nowTime);
        for (FastDramaSeriesExcel excel : importList) {
            FastDramaSeriesPO seriesPO = seriesMap.get(item.getDramaId() + "_" + item.getLanguageCode() + "_" + excel.getSeriesNum() + "_" + item.getCaptionType());
            if (seriesPO == null || (excel.getSeriesNumPart() > excel.getSeriesNum())) {
                item.setSeriesNum(excel.getSeriesNum());
                item.setSeriesTime(excel.getSeriesTime());
                item.setSeriesNumPart(excel.getSeriesNumPart());
                item.setUrl(excel.getUrl());
                if (StrUtil.isNotEmpty(excel.getTitle())) {
                    item.setTitle(excel.getTitle());
                }
                if (seriesMapper.insertSelective(item) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            } else {
                seriesPO.setSeriesNum(excel.getSeriesNum());
                seriesPO.setSeriesNumPart(excel.getSeriesNumPart());
                seriesPO.setSeriesTime(excel.getSeriesTime());
                seriesPO.setUrl(excel.getUrl());
                seriesPO.setUpdatorId(item.getUpdatorId());
                seriesPO.setUpdateTime(nowTime);
                seriesPO.setMediaId(0L); // 重置媒资文件id
                if (StrUtil.isNotEmpty(excel.getTitle())) {
                    seriesPO.setTitle(excel.getTitle());
                }
                if (seriesMapper.updateById(seriesPO) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.UPDATE_FAILED);
                }
            }
        }
        // 更新短剧更新集数
        Integer maxSeriesNum = seriesMapper.queryMaxSeriesNum(query);
        if (maxSeriesNum != null) {
            FastDramaPO update = new FastDramaPO();
            update.setId(item.getDramaId());
            update.setSeriesNumUpdate(maxSeriesNum);
            update.setUpdateTime(nowTime);
            update.setUpdatorId(item.getUpdatorId());
            dramaMapper.updateById(update);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastDramaSeriesPO> list) {
        if (seriesMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastDramaSeriesPO params) {
        params.setUpdateTime(DateUtil.getNowDate());
        if (seriesMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDel(FastDramaSeriesPO item) {
        // 查询短剧
        FastDramaSeriesPO seriesPO = seriesMapper.queryById(item);
        FastDramaPO dramaPO = dramaMapper.queryById(seriesPO.getDramaId());
        if (biggerZero(dramaPO.getCloneFromId()) && (dramaPO.getAuditDramaId() == null || dramaPO.getAuditDramaId() == 0)) {
            Date timeNow = DateUtil.getNowDate();
            FastDramaSeriesPO del = new FastDramaSeriesPO();
            del.setUpdatorId(item.getUpdatorId());
            del.setUpdateTime(timeNow);
            del.setId(item.getId());
            del.setDelFlag(StaticVar.YES);
            if (seriesMapper.updateById(del) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
            // 更新剧的集数
            FastDramaSeriesPO seriesParam = new FastDramaSeriesPO();
            seriesParam.setDelFlag(0);
            seriesParam.setDramaId(seriesPO.getDramaId());
            Integer seriesCount = seriesMapper.queryCount(seriesParam);
//    		dramaPO.setSeriesNumAll(seriesCount);
            dramaPO.setSeriesNumUpdate(seriesCount);
            dramaPO.setUpdateTime(timeNow);
            if (dramaMapper.updateById(dramaPO) == 0) {
                transactionRollBack();
                return MethodVO.error("更新剧的集数失败");
            }
            return MethodVO.success();
        } else {
            return MethodVO.error("剧集删除失败，非克隆剧集或已经审核！");
        }


    }

    /**
     * 删除投发放剧集
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateInvestDel(FastDramaSeriesPO item) {
        // 查询短剧
        Date timeNow = DateUtil.getNowDate();
        FastDramaSeriesPO del = new FastDramaSeriesPO();
        del.setUpdatorId(item.getUpdatorId());
        del.setUpdateTime(timeNow);
        del.setId(item.getId());
        del.setDelFlag(StaticVar.YES);
        if (seriesMapper.updateById(del) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();

    }

    /**
     * 获得剧集url
     *
     * @param dramaId   剧集id
     * @param seriesNum 剧集号
     * @param miniId    小程序id
     */
    public String getDramaUrl(Integer dramaId, Integer seriesNum, Integer miniId, String lang, Integer captionType) {
        String key = String.format("get_drama_url:%s_%s_%s_%s", dramaId, seriesNum, lang, captionType);
        String result = RedisUtil.get(key);
        // redis中存在并且uri不为空
        if (!StrUtil.isBlank(result)) {
            // log.info("get_drama_url key exist: {}, value: {}", key, result);
            return aliCdnService.getVideoFullUrl(result, miniId, dramaId);
        }
        // log.info("get_drama_url key not exist: {}", key);
        // 缓存中不存在则查询数据库
        String url = seriesMapper.seriesMapper(dramaId, seriesNum, lang, captionType);
        RedisUtil.set(key, url, RedisUtil.TIME_5M, true);
        // 不为空则转换url
        if (StrUtil.isNotBlank(url)) {
            // log.info("get_drama_url key: {}, url: {}", key, url);
            url = aliCdnService.getVideoFullUrl(url, miniId, dramaId);
        }
        return url;
    }

}
