/*
 * Powered By fast.up
 */
package com.fast.service.drama;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.common.FastTagMapper;
import com.fast.mapper.drama.FastDramaTagMapper;
import com.fast.po.common.FastTagPO;
import com.fast.po.drama.FastDramaTagPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class FastDramaTagService extends BaseService {

    @Autowired
    private FastDramaTagMapper fastDramaTagMapper;
    @Autowired
    private FastTagMapper fastTagMapper;

    /**
     * 通过id查询单个对象
     */
    public FastDramaTagPO queryById(FastDramaTagPO item) {
        return fastDramaTagMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaTagPO queryById(Integer id) {
        FastDramaTagPO itemParam = new FastDramaTagPO();
        itemParam.setId(id);
        return fastDramaTagMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastDramaTagPO queryOne(FastDramaTagPO item) {
        return fastDramaTagMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastDramaTagPO> queryList(FastDramaTagPO item) {
        return fastDramaTagMapper.queryList(item);
    }

    /**
     * 根据标签id查询短剧id
     */
    public Set<Integer> queryDramaIds(String tagIds) {
        return fastDramaTagMapper.queryDramaIds(tagIds);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastDramaTagPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastDramaTagPO> list = fastDramaTagMapper.queryList(item);
        for (FastDramaTagPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    // 根据剧id查询tags标签字符串，逗号分割
    public String queryTagsByDramaId(Integer dramaId) {

        String key = StaticVar.DRAMA_TAG + dramaId;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            List<FastTagPO> tagList = fastTagMapper.queryByDramaIdList(dramaId);
            StringBuffer tagStr = new StringBuffer();
            for (FastTagPO tag : tagList) {
                if (tagStr.length() > 0) {
                    tagStr.append(",");
                }
                tagStr.append(tag.getTagName());
            }
            res = tagStr.toString();
            RedisUtil.set(key, res, 60 * 60 * 24 * 7);
        }

        return res;
    }

    /**
     * 查询总数
     */
    public int queryCount(FastDramaTagPO item) {
        return fastDramaTagMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastDramaTagPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastDramaTagMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastDramaTagPO> list) {
        if (fastDramaTagMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastDramaTagPO item) {
        if (fastDramaTagMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
