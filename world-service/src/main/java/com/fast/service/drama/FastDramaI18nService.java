/*
 * Powered By fast.up
 */
package com.fast.service.drama;

import com.fast.constant.StaticStr;
import com.fast.mapper.drama.FastDramaI18nMapper;
import com.fast.po.drama.FastDramaI18nPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastDramaI18nService extends BaseService {

    @Autowired
    private FastDramaI18nMapper fastDramaI18nMapper;

    /**
     * 通过id查询单个对象
     */
    public FastDramaI18nPO queryById(FastDramaI18nPO params) {
        return fastDramaI18nMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaI18nPO queryById(Integer id) {
        return fastDramaI18nMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastDramaI18nPO queryOne(FastDramaI18nPO params) {
        return fastDramaI18nMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastDramaI18nPO> queryList(FastDramaI18nPO params) {
        return fastDramaI18nMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastDramaI18nPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastDramaI18nPO> list = fastDramaI18nMapper.queryList(params);
        for (FastDramaI18nPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastDramaI18nPO params) {
        return fastDramaI18nMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastDramaI18nPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastDramaI18nMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastDramaI18nPO> list) {
        if (fastDramaI18nMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastDramaI18nPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastDramaI18nMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public Map<String, FastDramaI18nPO> getMapByIdForLang(Integer id) {
        if (id == null) {
            return null;
        }

        FastDramaI18nPO po = new FastDramaI18nPO();
        po.setDramaId(id);
        List<FastDramaI18nPO> list = queryList(po);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().collect(Collectors.toMap(FastDramaI18nPO::getLanguageCode, fastDramaI18nPO -> fastDramaI18nPO, (a, b) -> b, LinkedHashMap::new));
    }

    public Set<String> getLangCodes(Integer id) {
        Map<String, FastDramaI18nPO> map = getMapByIdForLang(id);
        return MapUtils.isEmpty(map) ? null : map.keySet();
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByDramaId(Integer dramaId) {
        if (dramaId == null) {
            return 0;
        }
        return fastDramaI18nMapper.deleteByDramaId(dramaId);
    }

    public List<FastDramaI18nPO> queryByDramaId(Integer dramaId) {
        if (dramaId == null) {
            return null;
        }
        FastDramaI18nPO po = new FastDramaI18nPO();
        po.setDramaId(dramaId);
        return queryList(po);
    }

    public FastDramaI18nPO queryByDramaIdAndLangCode(Integer dramaId, String languageCode) {
        if (dramaId == null || languageCode == null) {
            return null;
        }
        FastDramaI18nPO po = new FastDramaI18nPO();
        po.setDramaId(dramaId);
        po.setLanguageCode(languageCode);
        return queryOne(po);
    }

    public Set<Integer> queryDramaIds(Set<String> languageCodes) {
        if (CollUtil.isEmpty(languageCodes)) {
            return null;
        }
        FastDramaI18nPO po = new FastDramaI18nPO();
        po.setLanguageCodes(languageCodes);
        List<FastDramaI18nPO> list = queryList(po);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().map(FastDramaI18nPO::getDramaId).collect(Collectors.toSet());
    }
    
}
