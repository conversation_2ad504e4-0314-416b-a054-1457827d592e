/*
 * Powered By fast.up
 */
package com.fast.service.drama;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.drama.FastDramaRetailMapper;
import com.fast.po.drama.FastDramaRetailPO;
import com.fast.service.base.BaseService;
import com.fast.service.mini.FastMiniService;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 短剧关联分销商
 *
 * <AUTHOR>
 */
@Service
public class FastDramaRetailService extends BaseService {

    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastDramaRetailMapper fastDramaRetailMapper;

    /**
     * 通过id查询单个对象
     */
    public List<Integer> queryInfoByRedis(Integer retailId) {
        if (retailId == null) {
            return null;
        }
        List<Integer> set;
        String key = StaticVar.RETAIL_DRAMA_LIST + retailId;
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            set = CollUtil.parseIntStr2List(value);
        } else {
            set = fastDramaRetailMapper.queryDramaIds(retailId);
            if (CollUtil.isEmpty(set)) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                RedisUtil.set(key, StrUtil.join(set), RedisUtil.TIME_2D);
            }
        }
        return set;
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaRetailPO queryById(FastDramaRetailPO item) {
        return fastDramaRetailMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaRetailPO queryById(Integer id) {
        return fastDramaRetailMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastDramaRetailPO queryOne(FastDramaRetailPO item) {
        return fastDramaRetailMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastDramaRetailPO> queryList(FastDramaRetailPO item) {
        return fastDramaRetailMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> getDramaRetailDetail(FastDramaRetailPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastDramaRetailPO> list = fastDramaRetailMapper.queryDramaRetailDetail(item);
        for (FastDramaRetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (notEmpty(cur.getMiniIds())) {
                cur.setMiniList(fastMiniService.queryInfoByRedis(cur.getMiniIds()));
            }
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastDramaRetailPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastDramaRetailPO> list = fastDramaRetailMapper.queryList(item);
        for (FastDramaRetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCountByDramaId(Integer dramaId) {
        return fastDramaRetailMapper.queryCountByDramaId(dramaId);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastDramaRetailPO item) {
        return fastDramaRetailMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastDramaRetailPO params) {
        Set<Integer> dramaIds = CollUtil.parseIntStr2Set(params.getDramaIds());
        Set<Integer> retailIds = CollUtil.parseIntStr2Set(params.getRetailIds());
        FastDramaRetailPO query = new FastDramaRetailPO();

        for (Integer dramaId : dramaIds) {
            // 查询当前短剧已授权的分销商
            query.setDramaId(dramaId);
            params.setDramaId(dramaId);
            List<Integer> retailIdHas = fastDramaRetailMapper.queryRetailIds(query);
            for (Integer retailId : retailIds) {
                if (!retailIdHas.contains(retailId)) {
                    params.setRetailId(retailId);
                    if (fastDramaRetailMapper.insertSelective(params) == 0) {
                        transactionRollBack();
                        return MethodVO.error(StaticStr.ADD_FAILED);
                    }
                }
            }
        }


        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastDramaRetailPO> list) {
        if (fastDramaRetailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO cancel(FastDramaRetailPO params) {
        Set<Integer> retailIds = CollUtil.parseIntStr2Set(params.getRetailIds());
        for (Integer retailId : retailIds) {
            params.setRetailId(retailId);
            if (params.getDramaId() != null) {
                fastDramaRetailMapper.delete(params);
            } else if (StrUtil.isNotEmpty(params.getDramaIds())) {
                List<Integer> dramaIdList = CollUtil.parseIntStr2List(params.getDramaIds());
                if (dramaIdList != null) {
                    for (Integer dramaId : dramaIdList) {
                        FastDramaRetailPO delPO = new FastDramaRetailPO();
                        delPO.setRetailId(retailId);
                        delPO.setDramaId(dramaId);
                        fastDramaRetailMapper.delete(delPO);
                    }
                }
            }
        }
        return MethodVO.success();
    }
}
