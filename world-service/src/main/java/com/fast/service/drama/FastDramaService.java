/*
 * Powered By fast.up
 */
package com.fast.service.drama;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.ContentTypeEnum;
import com.fast.enums.LanguageEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.common.FastCommonDictMapper;
import com.fast.mapper.drama.FastDramaLineTimeMapper;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.mapper.drama.FastDramaSeriesMapper;
import com.fast.mapper.drama.FastDramaTagMapper;
import com.fast.mapper.fee.FastFeeRuleMapper;
import com.fast.mapper.member.FastMemberAddictionMapper;
import com.fast.po.common.FastCommonDictPO;
import com.fast.po.common.FastTagPO;
import com.fast.po.drama.*;
import com.fast.po.fee.FastFeeRulePO;
import com.fast.po.member.FastMemberCoreLoadPO;
import com.fast.po.member.FastMemberOrderDramaPO;
import com.fast.po.member.FastMemberSettingPO;
import com.fast.po.mini.FastMiniContVersionPO;
import com.fast.po.mini.FastMiniRecommendPO;
import com.fast.po.mini.FastMiniSettingPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.aliyun.AliCdnService;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.common.FastCornerService;
import com.fast.service.common.FastTagService;
import com.fast.service.fee.FastFeeRuleService;
import com.fast.service.member.*;
import com.fast.service.mini.FastMiniContVersionService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.mini.FastMiniSettingService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.unlock.FastMemberUnlockDramaService;
import com.fast.utils.*;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.thread.FastUserContext;
import com.fast.utils.thread.LanguageContext;
import com.fast.utils.thread.SysTypeContext;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.common.FastTagVO;
import com.fast.vo.drama.*;
import com.fast.vo.fee.FastFeeRuleVO;
import com.fast.vo.member.MemberAccountVO;
import com.fast.vo.member.MemberDramaVO;
import com.fast.vo.mini.FastMiniVO;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
public class FastDramaService extends BaseService {

    @Autowired
    private FastDramaMapper fastDramaMapper;
    @Autowired
    private FastFeeRuleMapper fastFeeRuleMapper;
    @Autowired
    private FastDramaTagMapper fastDramaTagMapper;
    @Autowired
    private FastTagService fastTagService;
    @Autowired
    private FastCornerService fastCornerService;
    @Autowired
    private FastDramaTagService fastDramaTagService;
    @Autowired
    private FastDramaSeriesService fastDramaSeriesService;
    @Autowired
    private FastDramaCaptionsService fastDramaCaptionsService;
    @Autowired
    private FastMemberAccountService fastMemberAccountService;
    @Autowired
    private FastMemberService fastMemberService;
    @Autowired
    private FastMemberUnlockDramaService fastMemberUnlockDramaService;
    @Autowired
    private FastFeeRuleService fastFeeRuleService;
    @Autowired
    private FastDramaRetailService fastDramaRetailService;
    @Autowired
    private AliCdnService aliCdnService;
    @Autowired
    private FastDramaSeriesMapper fastDramaSeriesMapper;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMemberOrderDramaService fastMemberOrderDramaService;
    @Autowired
    private FastMemberSettingService fastMemberSettingService;
    @Autowired
    private FastMemberVipOnlineService fastMemberVipOnlineService;
    @Autowired
    private FastMiniSettingService fastMiniSettingService;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastMemberCoreLoadService fastMemberCoreLoadService;
    @Autowired
    private FastDramaLineTimeMapper fastDramaLineTimeMapper;
    @Autowired
    private FastCommonDictMapper fastCommonDictMapper;
    @Autowired
    private FastMemberOrderRechargeService fastMemberOrderRechargeService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastDramaI18nService fastDramaI18nService;
    @Autowired
    private FastMiniContVersionService fastMiniContVersionService;
    @Autowired
    private FastMemberAddictionMapper fastMemberAddictionMapper;

    /**
     * 查询全部短剧
     *
     * @return
     */
    public Map<Integer, FastDramaPO> queryAllDramaMap() {
        String key = "all_drama_redis";
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            return JsonUtil.toMapIntObject(value, FastDramaPO.class);
        }
        List<FastDramaPO> list = fastDramaMapper.querySimpleList(new FastDramaPO());
        Map<Integer, FastDramaPO> map = new HashMap<>();
        list.forEach(cur -> map.put(cur.getId(), cur));
        RedisUtil.setObject(key, map, 600);
        return map;
    }

    /**
     * 小程序-查询剧详情-总入口
     */
    public ResultVO<?> queryDramaAgent(FastDramaPO drama, SessionVO sessionVO) {
        // 参数校验-用户
        if (sessionVO.getMemberId() == null) {
            return ResultVO.error(StaticStr.USER_LOGIN_HAS_EXPIRED);
        }
        // 参数校验-短剧
        if (drama.getId() == null) {
            return ResultVO.error(StaticStr.DRAMAID_CANNOT_BE_EMPTY);
        }
        FastDramaVO dramaObj = queryInfoByRedis(drama);
        if (dramaObj == null) {
            return ResultVO.error100(StaticStr.DRAMA_NOT_EXIST);
        }
        if (dramaObj.getShelfState() == 0) {
            return ResultVO.error100(StaticStr.DRAMA_TAKEN_OFF_SHELVES);
        }

        if (dramaObj.getRecDramaId() != null && dramaObj.getRecDramaId() > 0) {
            // 查询推荐剧名称
            FastDramaVO recDrama = queryInfoByRedis(dramaObj.getRecDramaId());
            if (recDrama != null) {
                dramaObj.setRecDramaName(recDrama.getDramaName());
            }
        }

        String keyNew = StaticVar.MEMBER_PURE_NEW + sessionVO.getMemberId();
        String resNew = RedisUtil.get(keyNew);
        log.info("当前用户 {} 的新用户标识: {}", sessionVO.getMemberId(), resNew);

        // 纯新用户，资产为空
        ResultVO<?> resVO;
        if (StaticVar.PURE_NEW.equals(resNew)) {
            resVO = queryDramaWithoutMemberRedis(dramaObj, sessionVO);
            actionLogService.log("pure_new", JsonUtil.toString(resVO));
        } else {
            resVO = queryDramaWithMemberRedis(dramaObj, sessionVO);
            actionLogService.log("pure_new", "普通查询-----" + JsonUtil.toString(resVO));
        }

        if (resVO.getCode() == 0) {
            // 插入核心加载，播放器是否完成核心加载初始化
            if (StrUtil.isNotEmpty(drama.getCoreLoad())) {
                FastMemberCoreLoadPO mcPO = new FastMemberCoreLoadPO();
                mcPO.setCoreLoad(drama.getCoreLoad());
                mcPO.setMemberId(sessionVO.getMemberId());
                mcPO.setDramaId(drama.getId());
                fastMemberCoreLoadService.insertAsync(mcPO);
            }
            // 标签关联的剧
            if (Objects.equals(drama.getTagRelation(), 1)) {
                FastDramaTagPO fastDramaTagPO = new FastDramaTagPO();
                fastDramaTagPO.setTagIds(dramaObj.getTagIds());
                List<FastDramaTagPO> list = fastDramaTagService.queryList(fastDramaTagPO);
                if (CollUtil.isNotEmpty(list)) {
                    drama.setRetailId(sessionVO.getRetailId());
                    drama.setOfficialId(sessionVO.getOfficialId());
                    drama.setContentType(sessionVO.getContentType());
                    drama.setExId(drama.getId());
                    drama.setIds(StrUtil.join(list.stream().map(FastDramaTagPO::getDramaId).collect(Collectors.toList()), ","));
                    drama.setId(null);
                    List<FastDramaPO> pos = queryList(drama);
                    if (CollUtil.isNotEmpty(pos)) {
                        pos.forEach(po -> fillDramaData(po, drama));
                    }
                    dramaObj.setTagRecDramas(pos);
                }
            }
        }
        return resVO;
    }

    private Integer getCaptionType(Integer dramaId, String lang) {
        List<FastDramaSeriesVO> external = fastDramaSeriesService.queryInfoByRedis(dramaId, lang, 2);
        if (CollUtil.isNotEmpty(external)) {
            log.info("查询到短剧 {} 的 {} 语言的外挂字幕视频，优先走外挂", dramaId, lang);
            return 2;
        } else {
            log.info("未查询到短剧 {} 的 {} 语言的外挂字幕视频", dramaId, lang);
        }
        List<FastDramaSeriesVO> embedded = fastDramaSeriesService.queryInfoByRedis(dramaId, lang, 1);
        if (CollUtil.isNotEmpty(embedded)) {
            log.info("查询到短剧 {} 的 {} 语言的内嵌字幕视频，优先走内嵌", dramaId, lang);
            return 1;
        } else {
            log.info("未查询到短剧 {} 的 {} 语言的内嵌字幕视频", dramaId, lang);
        }
        log.info("未查询到短剧 {} 的 {} 语言的 外挂字幕视频 或 内嵌字幕视频", dramaId, lang);
        return null;
    }

    /**
     * 查询短剧信息（包含用户）
     */
    public ResultVO<?> queryDramaWithMemberRedis(FastDramaVO dramaObj, SessionVO sessionVO) {
        dramaObj.setAddictionState(0); // 追剧状态
        dramaObj.setRecentSeriesNum(0); // 观看记录
        Integer dramaId = dramaObj.getId();
        Integer continueFlag; // 连播开关 
        // 查询我的追剧
        List<MemberDramaVO> addictionList = fastMemberService.getMemberAddictionListRedis(sessionVO.getMemberId());
        if (CollUtil.hasContent(addictionList)) {
            for (MemberDramaVO vo : addictionList) {
                if (vo.getDramaId().equals(dramaId)) {
                    dramaObj.setAddictionState(1);
                    break;
                }
            }
        }
        // 查询我最近观看
        List<MemberDramaVO> recentList = fastMemberService.getMemberRecentListRedis(sessionVO.getMemberId());
        if (CollUtil.hasContent(recentList)) {
            for (MemberDramaVO vo : recentList) {
                if (vo.getDramaId().equals(dramaId)) {
                    dramaObj.setRecentSeriesNum(vo.getSeriesNum());
                    break;
                }
            }
        }
        // 查询是否连续播放
        FastMemberSettingPO msPO = fastMemberSettingService.queryByIdRedis(sessionVO);
        continueFlag = msPO.getContinueFlag();

        // 计算字幕类型
        Integer captionType = getCaptionType(dramaId, LanguageContext.getLanguageType());
        if (captionType == null) {
            log.info("查询到短剧 {} 的 {} 语言的视频数据对应的字幕类型，走一次兜底操作", dramaId, LanguageContext.getLanguageType());
            captionType = getCaptionType(dramaId, dramaObj.getDefaultLang());
            if (captionType == null) {
                log.info("查询到短剧 {} 的 {} 兜底语言的视频数据对应的字幕类型也是空，说明数据配置有误", dramaId, dramaObj.getDefaultLang());
                return ResultVO.error(StaticStr.DATA_EMPTY);
            }
        }
        log.info("[With] 最终计算出用户 {} 查询短剧 {} 的字幕类型: {}", sessionVO.getMemberId(), dramaId, captionType);

        // 原始剧集列表
        List<FastDramaSeriesVO> seriesList = fastDramaSeriesService.queryInfoByRedis(dramaId, LanguageContext.getLanguageType(), captionType);
        // 英文兜底
        if (CollUtil.isEmpty(seriesList)) {
            log.info("未查询到短剧 {} 的 {} 语言的剧集，采用 {} 来兜底", dramaId, LanguageContext.getLanguageType(), dramaObj.getDefaultLang());
            seriesList = fastDramaSeriesService.queryInfoByRedis(dramaId, dramaObj.getDefaultLang(), captionType);
        }
        // 添加用户观看属性后的列表
        List<MemberDramaSeriesVO> seriesMemberList = new ArrayList<>();
        int vipRestrict = 0;// vip可用，限制此剧
        if (CollUtil.hasContent(seriesList)) {
            FastFeeRuleVO feeRule = getFeeRule(sessionVO.getLinkId(), sessionVO.getOfficialId(), dramaId);
            if (feeRule == null) {
                return ResultVO.error(StaticStr.RULE_NOT_CONFIGURED);
            }
            boolean isVip = false;
            boolean buyDrama = false;// 是否购买剧卡
            List<Integer> unlockIds = null;// 已经解锁剧集
            // 查用户
            MemberAccountVO accountVO = fastMemberAccountService.queryInfoByRedis(sessionVO.getMemberId());
            // -- 判断vip到期
            if (accountVO.getVipState() == 1) {
                // 判断vip类型，上线vip
                if (fastMemberVipOnlineService.checkOnline(sessionVO, dramaId)) {
                    isVip = true;
                } else {
                    vipRestrict = 1;// 因为上线日期被限制
                }
            }
            if (isVip) {
                // 有效vip不处理
            } else {
                // 查询购买的剧卡
                FastMemberOrderDramaPO odParam = new FastMemberOrderDramaPO();
                odParam.setDramaId(dramaId);
                odParam.setMemberId(sessionVO.getMemberId());
                String buyDramaStr = fastMemberOrderDramaService.queryOrderDramaRedis(odParam);
                if (StaticVar.YES_STR.equals(buyDramaStr)) {
                    buyDrama = true;
                } else {
                    unlockIds = fastMemberUnlockDramaService.queryInfoByRedis(sessionVO.getMemberId(), dramaId);
                }
            }
            CalTime calTime = new CalTime();

            List<FastDramaCaptionsPO> captions = fastDramaCaptionsService.queryByDramaIdAndLang(dramaId, LanguageContext.getLanguageType());
            if (CollUtil.isEmpty(captions)) {
                captions = fastDramaCaptionsService.queryByDramaIdAndLang(dramaId, dramaObj.getDefaultLang());
            }
            Map<Integer, FastDramaCaptionsPO> map = new HashMap<>();
            if (CollUtil.isNotEmpty(captions)) {
                captions.forEach(caption -> map.put(caption.getSeriesNum(), caption));
            }

            for (FastDramaSeriesVO series : seriesList) {
                MemberDramaSeriesVO vo = new MemberDramaSeriesVO();
                BeanUtils.copyProperties(series, vo);
                int itemLock = 0;
                int follow = 0;
                int itemCoinPer;
                // log.info("dramaId: {}, seriesNum: {}, startFeeNum: {}", dramaId, series.getSeriesNum(), feeRule.getStartNum());
                if (series.getSeriesNum() >= feeRule.getStartNum()) {
                    itemLock = 1;
                }
                itemCoinPer = feeRule.getCoinPer();
                if (isVip || buyDrama || (unlockIds != null && unlockIds.contains(series.getSeriesNum()))) {
                    itemLock = 0;
                }
                if (series.getSeriesNum().equals(feeRule.getFollowNum())) {
                    follow = 1;
                }
                vo.setLock(itemLock);
                vo.setCoinPer(itemCoinPer);
                vo.setFollow(follow);
                if (dramaObj.getContentType() == 1 || dramaObj.getContentType() == 4) {
                    // vo.setUrl(aliCdnService.getVideoFullUrl(series.getUrl(), sessionVO.getMiniId(), dramaId));
                    // TODO 为了测试
                    String videoFullUrl = aliCdnService.getVideoFullUrl(series.getUrl(), sessionVO.getMiniId(), dramaId);
                    if (StaticVar.isTestDrama(dramaId)) {
                        videoFullUrl = videoFullUrl.replace("https://world-test.tos-ap-southeast-1.volces.com/", "https://fastup-cdn-prod.601book.com/");
                    }
                    vo.setUrl(videoFullUrl);
                } else if (dramaObj.getContentType() == 2) {
                    // 漫画
                    List<String> urls = new ArrayList<>();
                    List<String> urlNoHost = CollUtil.parseStr2List(series.getUrl());
                    for (String url : urlNoHost) {
                        if (url != null) {
                            urls.add(aliCdnService.getImgFullUrl(url));
                        }
                    }
                    vo.setUrl(StrUtil.join(urls));
                    if (notEmpty(series.getSeriesCover())) {
                        vo.setSeriesCover(aliCdnService.getImgFullUrl(series.getSeriesCover()));
                    }
                    vo.setTitle(series.getTitle());
                }
                // 字幕
                FastDramaCaptionsPO captionsPO = map.get(series.getSeriesNum());
                if (captionsPO != null) {
                    log.info("短剧 {} 在当前语言下找到的字幕文件ID {}", dramaId, captionsPO.getId());
                    MemberDramaCaptionsVO captionsVO = new MemberDramaCaptionsVO();
                    captionsVO.setSubtitleId(captionsPO.getId());
                    captionsVO.setLanguage(LanguageEnum.getZjcode(captionsPO.getSubLang()));
                    captionsVO.setLanguageId(LanguageEnum.getZjid(captionsPO.getSubLang()));
                    captionsVO.setUrl(captionsPO.getUrl());
                    captionsVO.setFormat(StrUtil.substringAfterLast(captionsPO.getUrl(), "."));
                    vo.setCaption(captionsVO);
                }
                seriesMemberList.add(vo);
            }
            actionLogService.log("get_drama_cost", "dramaId=" + dramaId + "整个series循环耗时: " + calTime.getCostTime());
            // 查询渠道链接，渠道本身设置的优先级最高
            FastLinkPO linkPO = fastLinkService.queryInfoByRedis(sessionVO.getLinkId());
            if (linkPO != null && linkPO.getSkipSeries() != null) {
                actionLogService.log("skipSeries", "设置skipSeries=" + linkPO.getSkipSeries());
                dramaObj.setSkipSeries(linkPO.getSkipSeries());
            } else {
                actionLogService.log("skipSeries", "未设置skipSeries=0");
                dramaObj.setSkipSeries(0);
            }
        }
        actionLogService.log("skipSeries", "设置2skipSeries=" + dramaObj.getSkipSeries());

        Map<String, Object> results = new HashMap<>();
        // 播放器是否加速
        BigDecimal playSpeed = getPlaySpeed(sessionVO);
        if (playSpeed != null) {
            results.put("playSpeed", playSpeed);
        } else {
            results.put("playSpeed", 1);// 正常倍速
        }
        results.put("dramaObj", dramaObj);
        results.put("vipRestrict", vipRestrict);
        results.put("seriesList", seriesMemberList);
        // results.put("captionsList", captionsList);
        results.put("continueFlag", continueFlag);
        return ResultVO.success(results);
    }

    // 返回播放速率
    private BigDecimal getPlaySpeed(SessionVO sessionVO) {
        if (sessionVO.getLinkId() != null && sessionVO.getLinkId() > 0) {
            FastMiniVO miniVO = fastMiniService.queryInfoByRedis(sessionVO.getMiniId());
            FastLinkPO linkPO = fastLinkService.queryInfoByRedis(sessionVO.getLinkId());
            if (miniVO != null && miniVO.getType() == 2) { // 抖小
                Date timeLine = DateUtil.format07("2025-01-26 19:37:08");
                if (timeLine.before(linkPO.getCreateTime())) { // 指定时间后创建的渠道链接，返回加速
                    return new BigDecimal("1.25");
                }
            }
        }
        return null;
    }

    /**
     * 查询短剧信息（不包含用户，用户默认无资产）
     */
    public ResultVO<?> queryDramaWithoutMemberRedis(FastDramaVO dramaObj, SessionVO sessionVO) {
        StringBuilder keyBuf = new StringBuilder();
        keyBuf.append(StaticVar.GET_DRAMA_WITHOUT_MEMBER);
        keyBuf.append(sessionVO.getLinkId());
        keyBuf.append("_");
        keyBuf.append(sessionVO.getOfficialId());
        keyBuf.append("_");
        keyBuf.append(dramaObj.getId());

        Map<String, Object> results = new HashMap<>();
        String res = RedisUtil.get(keyBuf.toString());
        if (StrUtil.isEmpty(res)) {
            Integer dramaId = dramaObj.getId();
            dramaObj.setAddictionState(0); // 追剧状态
            dramaObj.setRecentSeriesNum(0); // 观看记录

            // 计算字幕类型
            Integer captionType = getCaptionType(dramaId, LanguageContext.getLanguageType());
            if (captionType == null) {
                log.info("查询到短剧 {} 的 {} 语言的视频数据对应的字幕类型，走一次兜底操作", dramaId, LanguageContext.getLanguageType());
                captionType = getCaptionType(dramaId, dramaObj.getDefaultLang());
                if (captionType == null) {
                    log.info("查询到短剧 {} 的 {} 兜底语言的视频数据对应的字幕类型也是空，说明数据配置有误", dramaId, dramaObj.getDefaultLang());
                    return ResultVO.error(StaticStr.DATA_EMPTY);
                }
            }
            log.info("[Without] 最终计算出用户 {} 查询短剧 {} 的字幕类型: {}", sessionVO.getMemberId(), dramaId, captionType);

            // 原始剧集列表
            List<FastDramaSeriesVO> seriesList = fastDramaSeriesService.queryInfoByRedis(dramaId, LanguageContext.getLanguageType(), captionType);
            // 英文兜底
            if (CollUtil.isEmpty(seriesList)) {
                log.info("未查询到短剧 {} 的 {} 语言的剧集，采用 {} 来兜底", dramaId, LanguageContext.getLanguageType(), dramaObj.getDefaultLang());
                seriesList = fastDramaSeriesService.queryInfoByRedis(dramaId, dramaObj.getDefaultLang(), captionType);
            }
            // 添加用户观看属性后的列表
            List<MemberDramaSeriesVO> seriesMemberList = new ArrayList<>();
            if (CollUtil.hasContent(seriesList)) {
                FastFeeRuleVO feeRule = getFeeRule(sessionVO.getLinkId(), sessionVO.getOfficialId(), dramaId);
                if (feeRule == null) {
                    return ResultVO.error(StaticStr.RULE_NOT_CONFIGURED);
                }
                List<FastDramaCaptionsPO> captions = fastDramaCaptionsService.queryByDramaIdAndLang(dramaId, LanguageContext.getLanguageType());
                if (CollUtil.isEmpty(captions)) {
                    captions = fastDramaCaptionsService.queryByDramaIdAndLang(dramaId, dramaObj.getDefaultLang());
                }
                Map<Integer, FastDramaCaptionsPO> map = new HashMap<>();
                if (CollUtil.isNotEmpty(captions)) {
                    captions.forEach(caption -> map.put(caption.getSeriesNum(), caption));
                }
                for (FastDramaSeriesVO series : seriesList) {
                    MemberDramaSeriesVO vo = new MemberDramaSeriesVO();
                    BeanUtils.copyProperties(series, vo);
                    int itemLock = 0; // 解锁
                    int follow = 0; // 关注
                    int itemCoinPer; // 每集k币数
                    // log.info("dramaId: {}, seriesNum: {}, startFeeNum: {}", dramaId, series.getSeriesNum(), feeRule.getStartNum());
                    if (series.getSeriesNum() >= feeRule.getStartNum()) {
                        itemLock = 1;
                    }
                    itemCoinPer = feeRule.getCoinPer();
                    if (series.getSeriesNum().equals(feeRule.getFollowNum())) {
                        follow = 1;
                    }
                    vo.setLock(itemLock);
                    vo.setCoinPer(itemCoinPer);
                    vo.setFollow(follow);
                    if (dramaObj.getContentType() == 1 || dramaObj.getContentType() == 4) {
                        // vo.setUrl(aliCdnService.getVideoFullUrl(series.getUrl(), sessionVO.getMiniId(), dramaId));
                        // TODO 为了测试
                        String videoFullUrl = aliCdnService.getVideoFullUrl(series.getUrl(), sessionVO.getMiniId(), dramaId);
                        if (StaticVar.isTestDrama(dramaId)) {
                            videoFullUrl = videoFullUrl.replace("https://world-test.tos-ap-southeast-1.volces.com/", "https://fastup-cdn-prod.601book.com/");
                        }
                        vo.setUrl(videoFullUrl);
                    } else if (dramaObj.getContentType() == 2) {
                        // 漫画
                        List<String> urls = new ArrayList<>();
                        List<String> urlNoHost = CollUtil.parseStr2List(series.getUrl());
                        for (String url : urlNoHost) {
                            if (url != null) {
                                urls.add(aliCdnService.getImgFullUrl(url));
                            }
                        }
                        vo.setUrl(StrUtil.join(urls));
                        if (notEmpty(series.getSeriesCover())) {
                            vo.setSeriesCover(aliCdnService.getImgFullUrl(series.getSeriesCover()));
                        }
                        vo.setTitle(series.getTitle());
                    }
                    // 字幕
                    FastDramaCaptionsPO captionsPO = map.get(series.getSeriesNum());
                    if (captionsPO != null) {
                        log.info("短剧 {} 在当前语言下找到的字幕文件ID {}", dramaId, captionsPO.getId());
                        MemberDramaCaptionsVO captionsVO = new MemberDramaCaptionsVO();
                        captionsVO.setSubtitleId(captionsPO.getId());
                        captionsVO.setLanguage(LanguageEnum.getZjcode(captionsPO.getSubLang()));
                        captionsVO.setLanguageId(LanguageEnum.getZjid(captionsPO.getSubLang()));
                        captionsVO.setUrl(captionsPO.getUrl());
                        captionsVO.setFormat(StrUtil.substringAfterLast(captionsPO.getUrl(), "."));
                        vo.setCaption(captionsVO);
                    }
                    seriesMemberList.add(vo);
                }
                dramaObj.setSkipSeries(feeRule.getSkipSeries());
            }
            // 播放器是否加速
            BigDecimal playSpeed = getPlaySpeed(sessionVO);
            if (playSpeed != null) {
                results.put("playSpeed", playSpeed);
            } else {
                results.put("playSpeed", 1);// 正常倍速
            }
            results.put("dramaObj", dramaObj);
            results.put("vipRestrict", 0); // vip限制
            results.put("continueFlag", 1); // 连续播放设置
            results.put("seriesList", seriesMemberList);
            // results.put("captionsList", captionsList);
            res = JsonUtil.toString(results);
            RedisUtil.set(keyBuf.toString(), res, 60 * 10);
        } else {
            results = JsonUtil.toJSONObject(res);
        }
        return ResultVO.success(results);
    }

    /**
     * 获取计费规则
     *
     * @return
     */
    public FastFeeRuleVO getFeeRule(Integer linkId, Integer officialId, Integer dramaId) {
        // 根据渠道id查询计费规则
        FastFeeRuleVO feeLinkRule = fastFeeRuleService.queryLinkByRedis(linkId, dramaId);
        // 根据公众号查询计费规则
        FastFeeRuleVO feeRule = fastFeeRuleService.queryInfoByRedis(officialId, dramaId);
        if (feeRule == null) {
            feeRule = fastFeeRuleService.queryInfoByRedis(0, dramaId);
            if (feeRule == null) {
                return null;
            }
        }
        if (feeLinkRule != null) {
            if (feeLinkRule.getStartNumGlobal() != null && feeLinkRule.getStartNumGlobal() == 0 && feeLinkRule.getStartNum() != null && feeLinkRule.getStartNum() > 0) {
                feeRule.setStartNum(feeLinkRule.getStartNum()); // 付费起始剧集
            }
            if (feeLinkRule.getCoinPerGlobal() != null && feeLinkRule.getCoinPerGlobal() == 0 && feeLinkRule.getCoinPer() != null && feeLinkRule.getCoinPer() > 0) {
                feeRule.setCoinPer(feeLinkRule.getCoinPer());// 每集金币数量
            }
            if (feeLinkRule.getFollowNumGlobal() != null && feeLinkRule.getFollowNumGlobal() == 0 && feeLinkRule.getFollowNum() != null && feeLinkRule.getFollowNum() > 0) {
                feeRule.setFollowNum(feeLinkRule.getFollowNum());// 起始关注剧集
            }
        }
        return feeRule;
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaVO queryInfoByRedis(FastDramaPO item) {
        if (item.getId() == null) {
            return null;
        }
        FastDramaVO vo = new FastDramaVO();
        String key = StaticVar.DRAMA_INFO_ID + item.getId();
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            vo = JsonUtil.toJavaObject(value, FastDramaVO.class);
        } else {
            item.setDelFlag(StaticVar.NO);
            FastDramaPO po = fastDramaMapper.queryOne(item);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                BeanUtils.copyProperties(po, vo);
                vo.setEncryptionId(encode(vo.getId()));
                RedisUtil.set(key, JsonUtil.toString(vo), RedisUtil.TIME_2D);
            }
        }
        if (notEmpty(vo.getTagIds())) {
            vo.setTagNameList(fastTagService.queryInfoByRedis(vo.getTagIds()));
        }
        if (vo.getCornerId() != null) {
            vo.setCorner(fastCornerService.queryInfoByRedis(vo.getCornerId()));
        }
        if (notEmpty(vo.getDramaCover())) {
            // 图片默认为0，0
            vo.setDramaCover(aliCdnService.getImgFullUrl(vo.getDramaCover()));
            actionLogService.log("cnd_replace", "替换地址后dramaId:" + vo.getId() + ",url:" + vo.getDramaCover());
        }
        if (notEmpty(vo.getDramaCoverHor())) {
            // 图片默认为0，0
            vo.setDramaCoverHor(aliCdnService.getImgFullUrl(vo.getDramaCoverHor()));
        }
        // 在追数量
        Integer addictionCount = fastMemberAddictionMapper.queryCountByDramaId(vo.getId());
        vo.setAddictionCount(addictionCount == null ? 0 : addictionCount);
        // i18n
        fillI18n(vo);
        return vo;
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaVO queryInfoByRedis(Integer id) {
        FastDramaPO itemParam = new FastDramaPO();
        itemParam.setId(id);
        return queryInfoByRedis(itemParam);
    }

    /**
     * 通过ids查询多个对象
     */
    public List<FastDramaVO> queryInfoByRedis(String ids) {
        List<FastDramaVO> result = new ArrayList<>();
        if (notEmpty(ids)) {
            FastDramaPO itemParam = new FastDramaPO();
            List<Integer> idList = CollUtil.parseIntStr2List(ids);
            for (Integer id : idList) {
                itemParam.setId(id);
                FastDramaVO vo = queryInfoByRedis(itemParam);
                if (vo != null) {
                    result.add(vo);
                }
            }
        }
        return result;
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaPO queryById(FastDramaPO item, boolean i18n) {
        item.setPlatformFlag(getPlatformFlag(item));
        FastDramaPO po = fastDramaMapper.queryDetailById(item);
        if (po != null) {
            // 查询原版剧的最大版本
            FastDramaPO fastDramaPO = new FastDramaPO();
            if (po.getVersionType() == 1) {
                fastDramaPO.setSourceDramaId(po.getId());
            } else {
                fastDramaPO.setSourceDramaId(po.getSourceDramaId());
            }
            // 使用用途列表
            if (Objects.nonNull(item.getNativeFlag()) && item.getNativeFlag() == 1) {
                // 三方分发和端原生返回使用时间
                FastDramaLineTimePO fastDramaLineTimePO = new FastDramaLineTimePO();
                fastDramaLineTimePO.setType(2);
                fastDramaLineTimePO.setDramaId(item.getId());
                List<FastDramaLineTimePO> fastDramaLineTimePOS = fastDramaLineTimeMapper.queryList(fastDramaLineTimePO);
                po.setDramaLineTimeList(fastDramaLineTimePOS);
            } else if (Objects.nonNull(item.getThirdFlag()) && item.getThirdFlag() == 1) {
                FastDramaLineTimePO fastDramaLineTimePO = new FastDramaLineTimePO();
                fastDramaLineTimePO.setType(3);
                fastDramaLineTimePO.setDramaId(item.getId());
                List<FastDramaLineTimePO> fastDramaLineTimePOS = fastDramaLineTimeMapper.queryList(fastDramaLineTimePO);
                po.setDramaLineTimeList(fastDramaLineTimePOS);
            }

            int queryCount = fastDramaMapper.queryCount(fastDramaPO);
            po.setNextVersion(queryCount + 2);
            if (notEmpty(po.getDramaCover())) {
                // 图片默认为0，0
                po.setDramaCover(aliCdnService.getImgFullUrl(po.getDramaCover()));
            }
            if (notEmpty(po.getDramaCoverHor())) {
                // 图片默认为0，0
                po.setDramaCoverHor(aliCdnService.getImgFullUrl(po.getDramaCoverHor()));
            }
            if (notEmpty(po.getTagIds())) {
                po.setTagNameList(fastTagService.queryInfoByRedis(po.getTagIds()));
            }
            po.setCorner(fastCornerService.queryInfoByRedis(po.getCornerId()));

            if (i18n) {
                fillI18n(po);
            }
        }
        return po;
    }

    private void fillI18n(FastDramaPO item) {
        Map<String, FastDramaI18nPO> map = fastDramaI18nService.getMapByIdForLang(item.getId());
        if (CollUtil.isEmpty(map)) {
            return;
        }
        if (SysTypeContext.isBackend()) {
            Set<String> languageCodes = new HashSet<>();
            Map<String, String> nameMap = new LinkedHashMap<>();
            Map<String, String> coverMap = new LinkedHashMap<>();
            Map<String, String> coverHorMap = new LinkedHashMap<>();
            Map<String, String> introduceMap = new LinkedHashMap<>();
            map.forEach((k, v) -> {
                languageCodes.add(LanguageEnum.ofCode(k).getName());
                nameMap.put(k, v.getName());
                coverMap.put(k, v.getCover());
                coverHorMap.put(k, v.getCoverHor());
                introduceMap.put(k, v.getIntroduce());
            });
            item.setLanguages(languageCodes);
            item.setDramaNames(nameMap);
            item.setDramaCovers(coverMap);
            item.setDramaCoverHors(coverHorMap);
            item.setDramaIntroduces(introduceMap);
            // 给前端做一下兼容
            List<FastDramaI18nPO> list = new ArrayList<>();
            map.values().forEach(po -> {
                FastDramaI18nPO i18nPO = new FastDramaI18nPO();
                i18nPO.setLanguageCode(po.getLanguageCode());
                i18nPO.setName(po.getName());
                i18nPO.setIntroduce(po.getIntroduce());
                i18nPO.setCover(po.getCover());
                i18nPO.setCoverHor(po.getCoverHor());
                list.add(i18nPO);
            });
            item.setI18ns(list);
        }
        if (SysTypeContext.isApp()) {
            String languageType = LanguageContext.getLanguageType();
            FastDramaI18nPO i18nPO = map.get(languageType);
            if (i18nPO == null) {
                return;
            }
            item.setDramaName(i18nPO.getName());
            item.setDramaCover(i18nPO.getCover());
            item.setDramaCoverHor(i18nPO.getCoverHor());
            item.setDramaIntroduce(i18nPO.getIntroduce());
        }
    }

    public void fillI18n(FastDramaVO item) {
        if (item == null || item.getId() == null) {
            return;
        }
        Map<String, FastDramaI18nPO> map = fastDramaI18nService.getMapByIdForLang(item.getId());
        if (CollUtil.isEmpty(map)) {
            return;
        }
        if (SysTypeContext.isApp()) {
            String languageType = LanguageContext.getLanguageType();
            FastDramaI18nPO i18nPO = map.get(languageType);
            if (i18nPO == null) {
                return;
            }
            item.setDramaName(i18nPO.getName());
            item.setDramaCover(i18nPO.getCover());
            item.setDramaCoverHor(i18nPO.getCoverHor());
            item.setDramaIntroduce(i18nPO.getIntroduce());
        }
    }

    /**
     * 通过条件查询单个对象
     */
    public FastDramaPO queryOne(FastDramaPO item) {
        return fastDramaMapper.queryOne(item);
    }

    /**
     * 通过条件查询单个对象
     */
    public List<FastDramaPO> querySimpleList(FastDramaPO params, SessionVO sessionVO) {
        if (sessionVO.getRetailId() > 0) {
            Set<Integer> dramaIds = new HashSet<>();
            List<Integer> ids = fastDramaRetailService.queryInfoByRedis(sessionVO.getRetailId());
            if (CollUtil.isEmpty(ids)) {
                return new ArrayList<>();
            }
            if (CollUtil.hasContent(dramaIds)) {
                dramaIds = CollUtil.intersection(dramaIds, new HashSet<>(ids));
            } else {
                dramaIds = new HashSet<>(ids);
            }
            if (CollUtil.isEmpty(dramaIds)) {
                return new ArrayList<>();
            }
            params.setIds(StrUtil.join(dramaIds));
        }
        return fastDramaMapper.querySimpleList(params);
    }

    /**
     * 查询全部
     */
    public List<FastDramaPO> queryList(FastDramaPO item) {
        if (Objects.nonNull(item.getPage()) && item.getExportData() == 0) {
            PageHelper.startPage(item.getPage(), item.getLimit());
        }
        long startList = System.currentTimeMillis();
        List<FastDramaPO> fastDramaPOS = fastDramaMapper.queryList(item);
        log.info("短剧列表查询,查询列表sql耗时startList:{}", System.currentTimeMillis() - startList);
        return fastDramaPOS;
    }

    /**
     * 查询全部
     */
    public List<FastDramaPO> queryRecList(FastDramaPO item) {
        List<FastDramaPO> dramaList = RedisUtil.getList(StaticVar.DRAMA_INTRODUCTION, FastDramaPO.class);
        if (dramaList == null) {
            dramaList = fastDramaMapper.queryRecList(item);
            RedisUtil.setList(StaticVar.DRAMA_INTRODUCTION, dramaList, 60 * 5);
        }
        return dramaList;
    }

    /**
     * 查询全部id
     */
    public List<Integer> queryDramaIds(FastDramaPO item) {
        return fastDramaMapper.queryDramaIds(item);
    }

    /**
     * 查询全部id
     */
    public List<Integer> queryAllIds(FastDramaPO item) {
        return fastDramaMapper.queryDramaIds(item);
    }

    /**
     * 查询全部的剧id
     */
    public List<Integer> queryAllIdsRedis() {
        String key = StaticVar.DRAMA_ID_LIST_ALL_CACHE;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            FastDramaPO dramaParam = new FastDramaPO();
            dramaParam.setDelFlag(0);
            List<Integer> idList = fastDramaMapper.queryDramaIds(dramaParam);
            res = StrUtil.getStrFromIntList(idList);
            RedisUtil.set(key, res, 60 * 20);
        }
        return StrUtil.getIntListFromStr(res);
    }

    /**
     * 查询全部名称
     */
    public Map<Integer, String> queryDramaNameMap(FastDramaPO item) {
        List<FastDramaPO> list = fastDramaMapper.queryDramaName(item);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, String> map = new HashMap<>();
        list.forEach(cur -> map.put(cur.getId(), cur.getDramaName()));
        return map;
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastDramaPO item, PageVO pageVO, SessionVO sessionVO) {
        Set<Integer> dramaIds = new HashSet<>();
        if (notEmpty(item.getTagIds())) {
            Set<Integer> ids = fastDramaTagService.queryDramaIds(item.getTagIds());
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            dramaIds.addAll(ids);
            item.setIds(StrUtil.join(dramaIds));
        }
        if (CollUtil.isNotEmpty(item.getLanguages())) {
            Set<Integer> ids = fastDramaI18nService.queryDramaIds(item.getLanguages());
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            dramaIds.addAll(ids);
            item.setIds(StrUtil.join(dramaIds));
        }
        if (sessionVO.getRetailId() > 0) {
            List<Integer> ids = fastDramaRetailService.queryInfoByRedis(sessionVO.getRetailId());
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            if (CollUtil.hasContent(dramaIds)) {
                dramaIds = CollUtil.intersection(dramaIds, new HashSet<>(ids));
            } else {
                dramaIds = new HashSet<>(ids);
            }
            if (CollUtil.isEmpty(dramaIds)) {
                return ResultVO.success(getDefaultPageListData());
            }
            item.setIds(StrUtil.join(dramaIds));
        }
        // 判断是否是分销商
        if (sessionVO.getRetailId() > 0) {
            item.setFeeFlags(String.format("%s,9", item.getFeeFlag()));
            item.setFeeFlag(null);
        }
        startPage(pageVO);
        List<FastDramaPO> list = queryList(item);
        for (FastDramaPO cur : list) {
            fillDramaData(cur, item);
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    private void fillDramaData(FastDramaPO cur, FastDramaPO item) {
        if (notEmpty(cur.getDramaCover())) {
            // 图片默认为0，0
            cur.setDramaCover(aliCdnService.getImgFullUrl(cur.getDramaCover()));
        }
        if (notEmpty(cur.getDramaCoverHor())) {
            // 图片默认为0，0
            cur.setDramaCoverHor(aliCdnService.getImgFullUrl(cur.getDramaCoverHor()));
        }
        cur.setEncryptionId(encode(cur.getId()));
        if (cur.getProjectId() != null) {
            cur.setEncryptionProjectId(encode(cur.getProjectId()));
        }
        if (notEmpty(cur.getTagIds())) {
            cur.setTagNameList(fastTagService.queryInfoByRedis(cur.getTagIds()));
        }
        cur.setCorner(fastCornerService.queryInfoByRedis(cur.getCornerId()));
        if (item.getRetailId() > 0 && item.getOfficialId() > 0 && cur.getStartNum() == null && cur.getCoinPer() == null && cur.getSkipSeries() == null) {
            cur.setStartNum(cur.getStartNumDef());
            cur.setCoinPer(cur.getCoinPerDef());
            cur.setSkipSeries(cur.getSkipSeriesDef());
            cur.setFollowNum(cur.getFollowNumDef());
        }
        // FastAuditDramaTiktokV2PO dtPO = tiktokMap.get(cur.getId());
        // if(dtPO != null && dtPO.getAuditStatus() != null){
        // 	cur.setTiktokAuditStatus(dtPO.getAuditStatus());
        // }else{
        // 	cur.setTiktokAuditStatus(0);
        // }
        cur.setDefaultLangName(LanguageEnum.ofCode(cur.getDefaultLang()).getName());

        // i18n
        fillI18n(cur);
    }

    public ResultVO<?> exportDramaList(SessionVO sessionVO, FastDramaPO item) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_MEMBER_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        item.setLimitExport(StaticVar.MILLION);

        if (Objects.nonNull(item.getNativeFlag()) && item.getNativeFlag() == 1) {
            return nativeExport(sessionVO, item, key);
        } else if (Objects.nonNull(item.getThirdFlag()) && item.getThirdFlag() == 1) {
            return thirdExport(sessionVO, item, key);
        }
        Set<Integer> dramaIds = new HashSet<>();
        if (notEmpty(item.getTagIds())) {
            Set<Integer> ids = fastDramaTagService.queryDramaIds(item.getTagIds());
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            dramaIds.addAll(ids);
            item.setIds(StrUtil.join(dramaIds));
        }
        if (sessionVO.getRetailId() > 0) {
            List<Integer> ids = fastDramaRetailService.queryInfoByRedis(sessionVO.getRetailId());
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            if (CollUtil.hasContent(dramaIds)) {
                dramaIds.retainAll(ids);
            } else {
                dramaIds.addAll(ids);
            }
            item.setIds(StrUtil.join(dramaIds));
        }
        // 查询剧总充值
        Map<Integer, BigDecimal> dmMap = fastMemberOrderRechargeService.getDramaMoneyMap();
        List<FastDramaPO> list = queryList(item);
        for (FastDramaPO cur : list) {
            if (notEmpty(cur.getDramaCover())) {
                // 图片默认为0，0
                cur.setDramaCover(aliCdnService.getImgFullUrl(cur.getDramaCover()));
            }
            if (notEmpty(cur.getDramaCoverHor())) {
                // 图片默认为0，0
                cur.setDramaCoverHor(aliCdnService.getImgFullUrl(cur.getDramaCoverHor()));
            }
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getProjectId() != null) {
                cur.setEncryptionProjectId(encode(cur.getProjectId()));
            }
            if (notEmpty(cur.getTagIds())) {
                cur.setTagNameList(fastTagService.queryInfoByRedis(cur.getTagIds()));
            }
            cur.setCorner(fastCornerService.queryInfoByRedis(cur.getCornerId()));
            if (item.getRetailId() > 0 && item.getOfficialId() > 0 && cur.getStartNum() == null && cur.getCoinPer() == null && cur.getSkipSeries() == null) {
                cur.setStartNum(cur.getStartNumDef());
                cur.setCoinPer(cur.getCoinPerDef());
                cur.setSkipSeries(cur.getSkipSeriesDef());
                cur.setFollowNum(cur.getFollowNumDef());
            }
//            FastAuditDramaTiktokV2PO dtPO =  tiktokMap.get(cur.getId());
//            if(dtPO != null && dtPO.getAuditStatus() != null){
//            	cur.setTiktokAuditStatus(dtPO.getAuditStatus());
//            }else{
//            	cur.setTiktokAuditStatus(0);
//            }
            if (dmMap.containsKey(cur.getId())) {
                cur.setMoneyRecharge(dmMap.get(cur.getId()));
            } else {
                cur.setMoneyRecharge(BigDecimal.ZERO);
            }
        }

        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastDramaPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)


            row.add(cur.getId());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "id");

            // 短剧编码
            row.add(cur.getDramaCode());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "编码");
            row.add(cur.getProjectId());
            CollUtil.addNoRepeat(rowHeadNames, "项目id");
            row.add(cur.getProjectName());
            CollUtil.addNoRepeat(rowHeadNames, "关联项目");

            row.add(cur.getAuditDramaId());
            CollUtil.addNoRepeat(rowHeadNames, "微信送审ID");

            row.add(cur.getDramaName());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "名称");
            row.add(cur.getDramaPerformType() == 1 ? "短剧" : "漫剧");
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "类型");

            row.add(cur.getDramaCover());
            CollUtil.addNoRepeat(rowHeadNames, "封面");

            row.add(cur.getDramaIntroduce());
            CollUtil.addNoRepeat(rowHeadNames, "介绍");
            StringBuffer tagName = new StringBuffer();
            if (cur.getTagNameList() != null && cur.getTagNameList().size() > 0) {
                for (FastTagVO tag : cur.getTagNameList()) {
                    if (tagName.length() > 0) {
                        tagName.append(",");
                    }
                    tagName.append(tag.getTagName());
                }
            }
            row.add(tagName.toString());
            CollUtil.addNoRepeat(rowHeadNames, "角标");

            if (cur.getOpenState() == 0) {
                row.add("隐藏");
            } else {
                row.add("显示");
            }
            CollUtil.addNoRepeat(rowHeadNames, "显示状态");

            row.add(cur.getSeriesNumUpdate());
            CollUtil.addNoRepeat(rowHeadNames, "更新集数");

            row.add(cur.getSeriesNumAll());
            CollUtil.addNoRepeat(rowHeadNames, "总集数");
            if (cur.getUpdateState() == 0) {
                row.add("完结");
            } else {
                row.add("连载");
            }
            CollUtil.addNoRepeat(rowHeadNames, "更新状态");

            row.add(DateUtil.format09(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "创建时间");

            row.add(DateUtil.format07(cur.getReleaseTime()));
            CollUtil.addNoRepeat(rowHeadNames, "上线时间");

            if (cur.getCdnWechat() == 1) {
                row.add("开");
            } else {
                row.add("关");
            }
            CollUtil.addNoRepeat(rowHeadNames, "腾讯cdn");

            row.add(cur.getAlbumId() + "\u200b");
            CollUtil.addNoRepeat(rowHeadNames, "抖音剧目id");

            row.add(cur.getLicenseNum());
            CollUtil.addNoRepeat(rowHeadNames, "许可证号");
            row.add(cur.getRegistrationNum());
            CollUtil.addNoRepeat(rowHeadNames, "登记号");
            row.add(cur.getOrdinaryRecordNum());
            CollUtil.addNoRepeat(rowHeadNames, "普通备案号");
            row.add(cur.getKeyRecordNum());
            CollUtil.addNoRepeat(rowHeadNames, "重点备案号");

            if (cur.getCdnTiktok() == 1) {
                row.add("开");
            } else {
                row.add("关");
            }
            CollUtil.addNoRepeat(rowHeadNames, "抖音cdn");

            if (cur.getFoundationState() == 1) {
                row.add("开");
            } else {
                row.add("关");
            }
            CollUtil.addNoRepeat(rowHeadNames, "基建开关");

            if (cur.getShelfState() == 1) {
                row.add("上架");
            } else {
                row.add("下架");
            }
            CollUtil.addNoRepeat(rowHeadNames, "上架状态");

            if (cur.getTencentAuditStatus() != null) {
                if (cur.getTencentAuditStatus() == 0) {
                    row.add("未提审");
                } else if (cur.getTiktokAuditStatus() == 1) {
                    row.add("未通过");
                } else if (cur.getTiktokAuditStatus() == 2) {
                    row.add("全部通过");
                }
                CollUtil.addNoRepeat(rowHeadNames, "腾讯审核状态");
            }

            if (cur.getTiktokAuditStatus() != null) {
                if (cur.getTiktokAuditStatus() == 0) {
                    row.add("未提审");
                } else if (cur.getTiktokAuditStatus() == 99) {
                    row.add("未审核");
                } else if (cur.getTiktokAuditStatus() == 98) {
                    row.add("审核中");
                } else if (cur.getTiktokAuditStatus() == 1) {
                    row.add("未通过");
                } else if (cur.getTiktokAuditStatus() == 2) {
                    row.add("全部通过");
                } else {
                    row.add("-");
                }
                CollUtil.addNoRepeat(rowHeadNames, "抖音审核状态");
            }

            row.add(cur.getStartNum());
            CollUtil.addNoRepeat(rowHeadNames, "付费规则-起始剧集");

            row.add(cur.getCoinPer());
            CollUtil.addNoRepeat(rowHeadNames, "付费规则-单集K币");

            row.add(cur.getSourceFrom());
            CollUtil.addNoRepeat(rowHeadNames, "内容来源");

            row.add(cur.getRecDramaName());
            CollUtil.addNoRepeat(rowHeadNames, "完播推荐");

            // 剪辑来源
            row.add(cur.getSourceDramaName());
            CollUtil.addNoRepeat(rowHeadNames, "剪辑来源");

            // 版本类型
            String versionTypeName = "-";
            if (Objects.nonNull(cur.getVersionType())) {
                versionTypeName = cur.getVersionType() == 1 ? "原版" : "剪辑版";
            }
            row.add(versionTypeName);
            CollUtil.addNoRepeat(rowHeadNames, "版本类型");

            // 内容用途
            List<String> listFlag = new ArrayList<>();
            if (cur.getLaunchFlag() == 1) {
                listFlag.add("投流");
            }
            if (cur.getNativeFlag() == 1) {
                listFlag.add("端原生");
            }
            if (cur.getThirdFlag() == 1) {
                listFlag.add("三方分发");
            }
            row.add(StrUtil.join(listFlag, "\n"));
            CollUtil.addNoRepeat(rowHeadNames, "内容用途");


            row.add(cur.getMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "总充值");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = ContentTypeEnum.getName(sessionVO.getContentType()) + "管理-" + ContentTypeEnum.getName(sessionVO.getContentType()) + "列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    private ResultVO<?> thirdExport(SessionVO sessionVO, FastDramaPO item, String key) {

        Set<Integer> dramaIds = new HashSet<>();
        if (notEmpty(item.getTagIds())) {
            Set<Integer> ids = fastDramaTagService.queryDramaIds(item.getTagIds());
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            dramaIds.addAll(ids);
            item.setIds(StrUtil.join(dramaIds));
        }
        if (sessionVO.getRetailId() > 0) {
            List<Integer> ids = fastDramaRetailService.queryInfoByRedis(sessionVO.getRetailId());
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            if (CollUtil.hasContent(dramaIds)) {
                dramaIds.retainAll(ids);
            } else {
                dramaIds.addAll(ids);
            }
            item.setIds(StrUtil.join(dramaIds));
        }
        List<FastDramaPO> list = queryList(item);
        for (FastDramaPO cur : list) {
            if (notEmpty(cur.getDramaCover())) {
                // 图片默认为0，0
                cur.setDramaCover(aliCdnService.getImgFullUrl(cur.getDramaCover()));
            }
            if (notEmpty(cur.getDramaCoverHor())) {
                // 图片默认为0，0
                cur.setDramaCoverHor(aliCdnService.getImgFullUrl(cur.getDramaCoverHor()));
            }
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getProjectId() != null) {
                cur.setEncryptionProjectId(encode(cur.getProjectId()));
            }
            if (notEmpty(cur.getTagIds())) {
                cur.setTagNameList(fastTagService.queryInfoByRedis(cur.getTagIds()));
            }
            cur.setCorner(fastCornerService.queryInfoByRedis(cur.getCornerId()));
            if (item.getRetailId() > 0 && item.getOfficialId() > 0 && cur.getStartNum() == null && cur.getCoinPer() == null && cur.getSkipSeries() == null) {
                cur.setStartNum(cur.getStartNumDef());
                cur.setCoinPer(cur.getCoinPerDef());
                cur.setSkipSeries(cur.getSkipSeriesDef());
                cur.setFollowNum(cur.getFollowNumDef());
            }
//            FastAuditDramaTiktokV2PO dtPO =  tiktokMap.get(cur.getId());
//            if(dtPO != null && dtPO.getAuditStatus() != null){
//            	cur.setTiktokAuditStatus(dtPO.getAuditStatus());
//            }else{
//            	cur.setTiktokAuditStatus(0);
//            }
        }

        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastDramaPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getId());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "id");

            // 短剧编码
            row.add(cur.getDramaCode());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "编码");
            row.add(cur.getProjectId());
            CollUtil.addNoRepeat(rowHeadNames, "项目id");
            row.add(cur.getProjectName());
            CollUtil.addNoRepeat(rowHeadNames, "关联项目");
            row.add(cur.getThirdAccountName());
            CollUtil.addNoRepeat(rowHeadNames, "账号");
            row.add(cur.getPrincipalName());
            CollUtil.addNoRepeat(rowHeadNames, "主体");

            row.add(cur.getDramaName());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "名称");
            row.add(cur.getDramaPerformType() == 1 ? "短剧" : "漫剧");
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "类型");

            row.add(cur.getDramaCover());
            CollUtil.addNoRepeat(rowHeadNames, "封面");

            row.add(cur.getDramaIntroduce());
            CollUtil.addNoRepeat(rowHeadNames, "介绍");
            StringBuffer tagName = new StringBuffer();
            if (cur.getTagNameList() != null && cur.getTagNameList().size() > 0) {
                for (FastTagVO tag : cur.getTagNameList()) {
                    if (tagName.length() > 0) {
                        tagName.append(",");
                    }
                    tagName.append(tag.getTagName());
                }
            }
            row.add(tagName.toString());
            CollUtil.addNoRepeat(rowHeadNames, "角标");

            if (cur.getOpenState() == 0) {
                row.add("隐藏");
            } else {
                row.add("显示");
            }
            CollUtil.addNoRepeat(rowHeadNames, "显示状态");

            row.add(cur.getSeriesNumUpdate());
            CollUtil.addNoRepeat(rowHeadNames, "更新集数");

            row.add(cur.getSeriesNumAll());
            CollUtil.addNoRepeat(rowHeadNames, "总集数");
            if (cur.getUpdateState() == 0) {
                row.add("完结");
            } else {
                row.add("连载");
            }
            CollUtil.addNoRepeat(rowHeadNames, "更新状态");

            row.add(DateUtil.format09(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "创建时间");

            row.add(DateUtil.format07(cur.getThirdOnlineTime()));
            CollUtil.addNoRepeat(rowHeadNames, "上线时间");

            if (Objects.nonNull(cur.getThirdShelfState()) && cur.getThirdShelfState() == 1) {
                row.add("上架");
            } else {
                row.add("下架");
            }
            CollUtil.addNoRepeat(rowHeadNames, "上架状态");


            row.add(cur.getStartNum());
            CollUtil.addNoRepeat(rowHeadNames, "付费规则-起始剧集");

            row.add(cur.getCoinPer());
            CollUtil.addNoRepeat(rowHeadNames, "付费规则-单集K币");

            row.add(cur.getSourceFrom());
            CollUtil.addNoRepeat(rowHeadNames, "内容来源");

            // row.add(cur.getRecDramaName());
            // CollUtil.addNoRepeat(rowHeadNames, "完播推荐");

            // 剪辑来源
            row.add(cur.getSourceDramaName());
            CollUtil.addNoRepeat(rowHeadNames, "剪辑来源");

            // 版本类型
            String versionTypeName = "-";
            if (Objects.nonNull(cur.getVersionType())) {
                versionTypeName = cur.getVersionType() == 1 ? "原版" : cur.getVersionType() == 2 ? "剪辑版" : "";
            }
            row.add(versionTypeName);
            CollUtil.addNoRepeat(rowHeadNames, "版本类型");

            // 内容用途
            List<String> listFlag = new ArrayList<>();
            if (cur.getLaunchFlag() == 1) {
                listFlag.add("投流");
            }
            if (cur.getNativeFlag() == 1) {
                listFlag.add("端原生");
            }
            if (cur.getThirdFlag() == 1) {
                listFlag.add("三方分发");
            }
            row.add(StrUtil.join(listFlag, "\n"));
            CollUtil.addNoRepeat(rowHeadNames, "内容用途");


            row.add(cur.getThirdPlatformNames());
            CollUtil.addNoRepeat(rowHeadNames, "具体用途");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "短剧管理-短剧列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    private ResultVO<?> nativeExport(SessionVO sessionVO, FastDramaPO item, String key) {
        Set<Integer> dramaIds = new HashSet<>();
        if (notEmpty(item.getTagIds())) {
            Set<Integer> ids = fastDramaTagService.queryDramaIds(item.getTagIds());
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            dramaIds.addAll(ids);
            item.setIds(StrUtil.join(dramaIds));
        }
        if (sessionVO.getRetailId() > 0) {
            List<Integer> ids = fastDramaRetailService.queryInfoByRedis(sessionVO.getRetailId());
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            if (CollUtil.hasContent(dramaIds)) {
                dramaIds.retainAll(ids);
            } else {
                dramaIds.addAll(ids);
            }
            item.setIds(StrUtil.join(dramaIds));
        }
        List<FastDramaPO> list = queryList(item);
        for (FastDramaPO cur : list) {
            if (notEmpty(cur.getDramaCover())) {
                // 图片默认为0，0
                cur.setDramaCover(aliCdnService.getImgFullUrl(cur.getDramaCover()));
            }
            if (notEmpty(cur.getDramaCoverHor())) {
                // 图片默认为0，0
                cur.setDramaCoverHor(aliCdnService.getImgFullUrl(cur.getDramaCoverHor()));
            }
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getProjectId() != null) {
                cur.setEncryptionProjectId(encode(cur.getProjectId()));
            }
            if (notEmpty(cur.getTagIds())) {
                cur.setTagNameList(fastTagService.queryInfoByRedis(cur.getTagIds()));
            }
            cur.setCorner(fastCornerService.queryInfoByRedis(cur.getCornerId()));
            if (item.getRetailId() > 0 && item.getOfficialId() > 0 && cur.getStartNum() == null && cur.getCoinPer() == null && cur.getSkipSeries() == null) {
                cur.setStartNum(cur.getStartNumDef());
                cur.setCoinPer(cur.getCoinPerDef());
                cur.setSkipSeries(cur.getSkipSeriesDef());
                cur.setFollowNum(cur.getFollowNumDef());
            }
//            FastAuditDramaTiktokV2PO dtPO =  tiktokMap.get(cur.getId());
//            if(dtPO != null && dtPO.getAuditStatus() != null){
//            	cur.setTiktokAuditStatus(dtPO.getAuditStatus());
//            }else{
//            	cur.setTiktokAuditStatus(0);
//            }
        }

        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastDramaPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getId());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "id");

            // 短剧编码
            row.add(cur.getDramaCode());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "编码");
            row.add(cur.getProjectId());
            CollUtil.addNoRepeat(rowHeadNames, "项目id");
            row.add(cur.getProjectName());
            CollUtil.addNoRepeat(rowHeadNames, "关联项目");
            row.add(cur.getNativePlatformAccountName());
            CollUtil.addNoRepeat(rowHeadNames, "账号");
            row.add(cur.getNativePrincipalName());
            CollUtil.addNoRepeat(rowHeadNames, "主体");

            row.add(cur.getDramaName());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "名称");
            row.add(cur.getDramaPerformType() == 1 ? "短剧" : "漫剧");
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "类型");

            row.add(cur.getDramaCover());
            CollUtil.addNoRepeat(rowHeadNames, "封面");

            row.add(cur.getDramaIntroduce());
            CollUtil.addNoRepeat(rowHeadNames, "介绍");
            StringBuffer tagName = new StringBuffer();
            if (cur.getTagNameList() != null && cur.getTagNameList().size() > 0) {
                for (FastTagVO tag : cur.getTagNameList()) {
                    if (tagName.length() > 0) {
                        tagName.append(",");
                    }
                    tagName.append(tag.getTagName());
                }
            }
            row.add(tagName.toString());
            CollUtil.addNoRepeat(rowHeadNames, "角标");

            if (cur.getOpenState() == 0) {
                row.add("隐藏");
            } else {
                row.add("显示");
            }
            CollUtil.addNoRepeat(rowHeadNames, "显示状态");

            row.add(cur.getSeriesNumUpdate());
            CollUtil.addNoRepeat(rowHeadNames, "更新集数");

            row.add(cur.getSeriesNumAll());
            CollUtil.addNoRepeat(rowHeadNames, "总集数");
            if (cur.getUpdateState() == 0) {
                row.add("完结");
            } else {
                row.add("连载");
            }
            CollUtil.addNoRepeat(rowHeadNames, "更新状态");

            row.add(DateUtil.format09(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "创建时间");

            row.add(DateUtil.format07(cur.getNativeOnlineTime()));
            CollUtil.addNoRepeat(rowHeadNames, "上线时间");

            if (Objects.nonNull(cur.getThirdShelfState()) && cur.getThirdShelfState() == 1) {
                row.add("上架");
            } else {
                row.add("下架");
            }
            CollUtil.addNoRepeat(rowHeadNames, "上架状态");


            row.add(cur.getStartNum());
            CollUtil.addNoRepeat(rowHeadNames, "付费规则-起始剧集");

            row.add(cur.getCoinPer());
            CollUtil.addNoRepeat(rowHeadNames, "付费规则-单集K币");

            row.add(cur.getSourceFrom());
            CollUtil.addNoRepeat(rowHeadNames, "内容来源");

            row.add(cur.getNativeDramaSource() == 1 ? "手动创建" : "抖音内容合作平台");
            CollUtil.addNoRepeat(rowHeadNames, "端原生内容来源");

            // row.add(cur.getRecDramaName());
            // CollUtil.addNoRepeat(rowHeadNames, "完播推荐");

            // 剪辑来源
            row.add(cur.getSourceDramaName());
            CollUtil.addNoRepeat(rowHeadNames, "剪辑来源");

            // 版本类型
            String versionTypeName = "-";
            if (Objects.nonNull(cur.getVersionType())) {
                versionTypeName = cur.getVersionType() == 1 ? "原版" : cur.getVersionType() == 2 ? "剪辑版" : "";
            }
            row.add(versionTypeName);
            CollUtil.addNoRepeat(rowHeadNames, "版本类型");

            // 内容用途
            List<String> listFlag = new ArrayList<>();
            if (cur.getLaunchFlag() == 1) {
                listFlag.add("投流");
            }
            if (cur.getNativeFlag() == 1) {
                listFlag.add("端原生");
            }
            if (cur.getThirdFlag() == 1) {
                listFlag.add("三方分发");
            }
            row.add(StrUtil.join(listFlag, "\n"));
            CollUtil.addNoRepeat(rowHeadNames, "内容用途");

            // 所属团队
            String teamName = "";
            if (Objects.nonNull(cur.getTeamId())) {
                FastCommonDictPO fastCommonDictPO = fastCommonDictMapper.queryById(cur.getTeamId());
                if (Objects.nonNull(fastCommonDictPO)) {
                    teamName = fastCommonDictPO.getValue();
                }
            }
            row.add(teamName);
            CollUtil.addNoRepeat(rowHeadNames, "所属团队");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "短剧管理-短剧列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 查询总数
     */
    public int queryCount(FastDramaPO item) {
        return fastDramaMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastDramaPO item, SessionVO sessionVO) {
        if (item.getShelfState() == 1) {
            return MethodVO.error("上架之前，必须完善剧集和字幕信息！");
        }
        // 版本类型
        if (Objects.isNull(item.getVersionType())) {
            return MethodVO.error(StaticStr.INVALID_PARAM);
        }
        if (Objects.nonNull(item.getContentType())) {
            if (item.getContentType() == 4) {
                item.setDramaPerformType(2);
            } else {
                item.setDramaPerformType(1);
            }
        }
        if (item.getVersionType() == 2) {
            if (Objects.isNull(item.getSourceDramaId())) {
                return MethodVO.error("剪辑来源不能为空!");
            }
            FastDramaPO fastDramaPO1 = new FastDramaPO();
            fastDramaPO1.setId(item.getSourceDramaId());
            fastDramaPO1.setPlatformFlag(getPlatformFlag(item));
            FastDramaPO fastDramaPO = fastDramaMapper.queryDetailById(fastDramaPO1);
            if (Objects.isNull(fastDramaPO) || fastDramaPO.getVersionType() != 1) {
                return MethodVO.error("剪辑来源剧信息不存在!");
            }
            FastDramaPO queryCount = new FastDramaPO();
            queryCount.setSourceDramaId(item.getSourceDramaId());
            int count = fastDramaMapper.queryCount(queryCount);
            item.setVersionNum(count + 2);
        } else {
            // 原版是1
            item.setVersionNum(1);
        }
        // 上线时间保存
        if (notBlank(item.getReleaseDateStr())) {
            String releaseDateStr = item.getReleaseDateStr();
            String releaseTimeStr = item.getReleaseDateStr();
            if (releaseDateStr.length() > 10) {
                releaseDateStr = releaseDateStr.substring(0, 10);
            }
            if (releaseTimeStr.length() == 10) {
                releaseTimeStr = releaseTimeStr + " 00:00:00";
            }
            item.setReleaseDate(DateUtil.format09(releaseDateStr));
            item.setReleaseTime(DateUtil.format07(releaseTimeStr));
        }

        // 写一份默认国际化内容
        FastDramaI18nPO i18nPO = takeDefaultContent(item);
        item.setDramaName(i18nPO.getName());
        item.setDramaCover(i18nPO.getCover());
        item.setDramaCoverHor(i18nPO.getCoverHor());
        item.setDramaIntroduce(i18nPO.getIntroduce());

        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastDramaMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        if (Objects.nonNull(item.getProjectId())) {
            updateDramaCode(item.getId());
        }

        // 克隆剧集资源和mp4资源
        if (Objects.nonNull(item.getVersionSource()) && item.getVersionSource() == 2) {
            if (StrUtil.notBlank(item.getCloneResource()) && item.getCloneResource().contains("1")) {
                // 剧集资源复制
                FastDramaSeriesPO seriesPO = new FastDramaSeriesPO();
                seriesPO.setDramaId(item.getCloneFromId());
                seriesPO.setDelFlag(0);
                List<FastDramaSeriesPO> fastDramaSeriesPOS = fastDramaSeriesMapper.queryList(seriesPO);
                for (FastDramaSeriesPO fastDramaSeriesPO : fastDramaSeriesPOS) {
                    // 单个复制 m3u8和mp4的id是一样的
                    Integer sourceId = fastDramaSeriesPO.getId();
                    fastDramaSeriesPO.setId(null);
                    fastDramaSeriesPO.setDramaId(item.getId());
                    fastDramaSeriesPO.setMediaId(null);
                    fastDramaSeriesPO.setAlbumId(null);
                    fastDramaSeriesPO.setEpisodeId(null);
                    fastDramaSeriesMapper.insertSelective(fastDramaSeriesPO);
                }
            }
            if (StrUtil.notBlank(item.getCloneResource()) && item.getCloneResource().contains("2")) {
                // 需要先克隆剧集资源
                if (!item.getCloneResource().contains("1")) {
                    transactionRollBack();
                    return MethodVO.error("克隆MP4资源请先克隆m3u8资源");
                }
            }
        }

        List<FastDramaLineTimePO> dramaLineTimeList = item.getDramaLineTimeList();
        if (CollUtil.isNotEmpty(dramaLineTimeList)) {
            dramaLineTimeList.forEach(lineTimeItem -> {
                lineTimeItem.setDramaId(item.getId());
                lineTimeItem.setCreateTime(DateUtil.getNowDate());
                if (Objects.nonNull(item.getThirdPlatformId())) {
                    // 指定平台新增
                    if (item.getThirdPlatformId().equals(lineTimeItem.getSubType())) {
                        lineTimeItem.setShelfState(item.getThirdShelfState());
                    }
                } else {
                    // 全部新增
                    lineTimeItem.setShelfState(item.getThirdShelfState());
                }
            });
            fastDramaLineTimeMapper.insertBatch(dramaLineTimeList);
        }
        // 初始化短剧付费规则
        FastFeeRulePO fastFeeRulePO = new FastFeeRulePO();
        fastFeeRulePO.setRetailId(sessionVO.getRetailId());
        fastFeeRulePO.setDramaId(item.getId());
        fastFeeRulePO.setSkipSeries(item.getSkipSeries());
        fastFeeRulePO.setCoinPer(item.getCoinPer());
        fastFeeRulePO.setStartNum(item.getStartNum());
        fastFeeRulePO.setCreateTime(nowTime);
        fastFeeRulePO.setCreatorId(item.getCreatorId());
        if (fastFeeRuleMapper.insertSelective(fastFeeRulePO) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        // 新增标签
        if (notEmpty(item.getTagIds())) {
            List<Integer> list = CollUtil.parseIntStr2List(item.getTagIds());
            CollUtil.removeRepeat(list);
            List<FastDramaTagPO> listTag = new ArrayList<>();
            for (Integer tagId : list) {
                FastDramaTagPO tag = new FastDramaTagPO();
                tag.setDramaId(item.getId());
                tag.setTagId(tagId);

                listTag.add(tag);
            }
            fastDramaTagMapper.insertBatch(listTag);
        }
        // 克隆剧的相关剧集
        if (biggerZero(item.getCloneFromId())) {
            FastDramaSeriesPO seriesParam = new FastDramaSeriesPO();
            seriesParam.setDramaId(item.getCloneFromId());
            seriesParam.setLanguageCode(item.getLanguageCode());
            seriesParam.setCaptionType(item.getCaptionType());
            seriesParam.setLimitExport(item.getSeriesNumAll());
            // 查询了关联的有效的mediaId
            List<FastDramaSeriesPO> seriesList = fastDramaSeriesMapper.queryList4Clone(seriesParam);
            Date timeNow = DateUtil.getNowDate();
            if (seriesList.size() > 0) {
                for (FastDramaSeriesPO series : seriesList) {
                    series.setDramaId(item.getId());
                    series.setCreateTime(timeNow);
                    series.setUpdateTime(timeNow);
                    series.setMediaId(0L);// 不复制媒资id
                    series.setSeriesNumPart(Double.valueOf(series.getSeriesNum()));
                }
                if (fastDramaSeriesMapper.insertBatch(seriesList) < seriesList.size()) {
                    transactionRollBack();
                    return MethodVO.error("克隆剧集异常");
                }
            }
            // 更新剧的更新剧集数
            FastDramaPO dramaPO = new FastDramaPO();
            dramaPO.setId(item.getId());
            dramaPO.setSeriesNumUpdate(seriesList.size());
            dramaPO.setUpdateTime(timeNow);
            dramaPO.setUpdatorId(item.getCreatorId());
            if (fastDramaMapper.updateById(dramaPO) == 0) {
                transactionRollBack();
                return MethodVO.error("克隆剧集异常，更新集数失败");
            }
        }

        // 国际化
        item.getI18ns().forEach(i18n -> {
            if (StrUtil.isBlank(i18n.getCoverHor())) {
                i18n.setCoverHor(i18n.getCover());
            }
            i18n.setDramaId(item.getId());
            i18n.setCreateTime(nowTime);
            i18n.setCreatorId(FastUserContext.getUserId());
        });
        fastDramaI18nService.insertBatch(item.getI18ns());

        return MethodVO.success(encode(item.getId()));
    }

    private FastDramaI18nPO takeDefaultContent(FastDramaPO item) {
        FastDramaI18nPO defaulted = null;
        for (FastDramaI18nPO content : item.getI18ns()) {
            if (Objects.equals(content.getLanguageCode(), LanguageEnum.ENGLISH.getCode())) {
                defaulted = content;
                break;
            }
        }
        if (defaulted == null) {
            throw new MyException("英文是默认兜底版，不允许为空");
        }
        return defaulted;
    }

    public Integer getPlatformFlag(FastDramaPO item) {
        if (Objects.nonNull(item.getPlatformFlag())) {
            return item.getPlatformFlag();
        }
        if (Objects.nonNull(item.getLaunchFlag()) && item.getLaunchFlag() == 1) {
            return 1;
        } else if (Objects.nonNull(item.getNativeFlag()) && item.getNativeFlag() == 1) {
            return 2;
        } else if (Objects.nonNull(item.getThirdFlag()) && item.getThirdFlag() == 1) {
            return 3;
        }
        return 1;
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastDramaPO item, SessionVO sessionVO) {
        FastDramaVO fastDramaVO = queryInfoByRedis(item.getId());
        if (fastDramaVO == null) {
            return MethodVO.error(StaticStr.INVALID_PARAM);
        }
        if (item.getShelfState() == 1 && !check(item.getId(), fastDramaVO.getDefaultLang())) {
            return MethodVO.error("请先在默认语言下上传剧集");
        }
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (notBlank(item.getReleaseDateStr())) {
            String releaseDateStr = item.getReleaseDateStr();
            String releaseTimeStr = item.getReleaseDateStr();
            if (releaseDateStr.length() > 10) {
                releaseDateStr = releaseDateStr.substring(0, 10);
            }
            if (releaseTimeStr.length() == 10) {
                releaseTimeStr = releaseTimeStr + " 00:00:00";
            }
            item.setReleaseDate(DateUtil.format09(releaseDateStr));
            item.setReleaseTime(DateUtil.format07(releaseTimeStr));
        }

        // 下架状态处理 更改三方和端原生的时候不更新投流剧状态
        if (Objects.nonNull(item.getPlatformFlag()) && item.getPlatformFlag() != 1) {
            item.setShelfState(null);
        }
        // 区分短剧和漫剧
        if (Objects.nonNull(sessionVO.getContentType())) {
            if (sessionVO.getContentType() == 4) {
                item.setDramaPerformType(2);
            } else {
                item.setDramaPerformType(1);
            }
        }

        // 写一份默认国际化内容
        FastDramaI18nPO i18nPO = takeDefaultContent(item);
        item.setDramaName(i18nPO.getName());
        item.setDramaCover(i18nPO.getCover());
        item.setDramaCoverHor(i18nPO.getCoverHor());
        item.setDramaIntroduce(i18nPO.getIntroduce());

        if (fastDramaMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }

        // 更新上线时间
        updateLineTime(item);

        // 更新短剧付费规则
        FastFeeRulePO fastFeeRulePO = new FastFeeRulePO();
        fastFeeRulePO.setRetailId(sessionVO.getRetailId());
        fastFeeRulePO.setOfficialId(sessionVO.getOfficialId());
        fastFeeRulePO.setDramaId(item.getId());
        fastFeeRulePO.setSkipSeries(item.getSkipSeries());
        fastFeeRulePO.setCoinPer(item.getCoinPer());
        fastFeeRulePO.setStartNum(item.getStartNum());
        fastFeeRulePO.setUpdateTime(nowTime);
        fastFeeRulePO.setUpdatorId(item.getCreatorId());
        if (fastFeeRuleMapper.updateByDramaId(fastFeeRulePO) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        // 删除标签
        FastDramaTagPO delete = new FastDramaTagPO();
        delete.setDramaId(item.getId());
        fastDramaTagMapper.deleteByDramaId(delete);
        // 新增标签
        if (notEmpty(item.getTagIds())) {
            List<Integer> list = CollUtil.parseIntStr2List(item.getTagIds());
            CollUtil.removeRepeat(list);
            List<FastDramaTagPO> listTag = new ArrayList<>();
            for (Integer tagId : list) {
                FastDramaTagPO tag = new FastDramaTagPO();
                tag.setDramaId(item.getId());
                tag.setTagId(tagId);

                listTag.add(tag);
            }
            fastDramaTagMapper.insertBatch(listTag);
        }
        updateDramaCode(item.getId());

        // 国际化
        fastDramaI18nService.deleteByDramaId(item.getId());
        item.getI18ns().forEach(i18n -> {
            if (StrUtil.isBlank(i18n.getCoverHor())) {
                i18n.setCoverHor(i18n.getCover());
            }
            i18n.setDramaId(item.getId());
            i18n.setCreateTime(nowTime);
            i18n.setCreatorId(FastUserContext.getUserId());
        });
        fastDramaI18nService.insertBatch(item.getI18ns());

        return MethodVO.success();
    }

    public void updateProjectByDramaId(FastDramaPO item) {
        FastDramaPO fastDramaPO = fastDramaMapper.queryById(item.getId());
        if (Objects.isNull(fastDramaPO)) {
            return;
        }

        if (Objects.isNull(fastDramaPO.getProjectId())) {
            return;
        }
        Date onlineTime = fastDramaPO.getReleaseDate();
        // 查询端原生或三方分发上线时间
        FastDramaLineTimePO fastDramaLineTimePO = new FastDramaLineTimePO();
        fastDramaLineTimePO.setDramaId(item.getId());
        List<FastDramaLineTimePO> fastDramaLineTimePOS = fastDramaLineTimeMapper.queryList(fastDramaLineTimePO);
        if (CollUtil.isNotEmpty(fastDramaLineTimePOS)) {
            List<Date> collect = fastDramaLineTimePOS.stream().map(FastDramaLineTimePO::getOnlineTime)
                    .collect(Collectors.toList());
            collect.add(onlineTime);
            onlineTime = collect.stream()
                    .filter(Objects::nonNull)
                    .min(Date::compareTo)
                    .orElse(null);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastDramaPO params) {
        Date nowTime = DateUtil.getNowDate();
        FastDramaPO item = new FastDramaPO();
        item.setId(params.getId());
        item.setUpdatorId(params.getUpdatorId());
        item.setRecDramaId(params.getRecDramaId());
        item.setReleaseDateStr(params.getReleaseDateStr());
        item.setCdnTiktok(params.getCdnTiktok());
        item.setCdnWechat(params.getCdnWechat());
        item.setUpdateTime(nowTime);
        item.setFoundationState(params.getFoundationState());
        item.setFreeFoundationState(params.getFreeFoundationState());
        item.setFreeOtherFoundationState(params.getFreeOtherFoundationState());
        item.setNatureFoundationState(params.getNatureFoundationState());
        if (fastDramaMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateFeeFlag(FastDramaPO params, List<Integer> idList) {
        for (Integer id : idList) {
            Date nowTime = DateUtil.getNowDate();
            FastDramaPO item = new FastDramaPO();
            item.setId(id);
            item.setFeeFlag(params.getFeeFlag());
            item.setUpdatorId(params.getUpdatorId());
            item.setUpdateTime(nowTime);
            if (fastDramaMapper.updateById(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        return MethodVO.success();
    }

    /**
     * 更新快应用备案号
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateAuditNumber(FastDramaPO params) {
        Date nowTime = DateUtil.getNowDate();
        FastDramaPO item = new FastDramaPO();
        item.setId(params.getId());
        item.setAuditNumber(params.getAuditNumber());
        item.setUpdatorId(params.getUpdatorId());
        item.setUpdateTime(nowTime);
        if (fastDramaMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDel(FastDramaPO item) {
        FastDramaPO del = new FastDramaPO();
        del.setUpdatorId(item.getUpdatorId());
        del.setUpdateTime(DateUtil.getNowDate());
        del.setId(item.getId());
        del.setDelFlag(StaticVar.YES);
        if (fastDramaMapper.updateById(del) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateShelf(FastDramaPO item) {
        FastDramaVO fastDramaVO = queryInfoByRedis(item.getId());
        if (fastDramaVO == null) {
            return MethodVO.error(StaticStr.INVALID_PARAM);
        }
        if (item.getShelfState() == 1 && !check(item.getId(), fastDramaVO.getDefaultLang())) {
            return MethodVO.error("请先在默认语言下上传剧集");
        }
        FastDramaPO shelf = new FastDramaPO();
        shelf.setUpdatorId(item.getUpdatorId());
        shelf.setUpdateTime(DateUtil.getNowDate());
        shelf.setId(item.getId());
        shelf.setShelfState(item.getShelfState());
        if (fastDramaMapper.updateById(shelf) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        FastDramaPO updateThird = new FastDramaPO();
        updateThird.setId(item.getId());
        updateThird.setPlatformFlag(1);
        updateShelfThird(updateThird);
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateOpenState(FastDramaPO item) {
        FastDramaPO state = new FastDramaPO();
        state.setUpdatorId(item.getUpdatorId());
        state.setUpdateTime(DateUtil.getNowDate());
        state.setId(item.getId());
        state.setOpenState(item.getOpenState());
        if (fastDramaMapper.updateById(state) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 快应用专用方法
    public ResultVO<?> getBoutiqueList(FastMiniSettingPO params, SessionVO sessionVO) {
        // 拿到用户配置的语言，找到对应的版本
        String lang = LanguageContext.getLanguageType();
        FastMiniContVersionPO contVersion = fastMiniContVersionService.queryByLangCodeNotDefaulted(params.getMiniId(), lang);
        if (contVersion == null) {
            // 如果对应语言的版本未找到，那就找默认的兜底版本
            contVersion = fastMiniContVersionService.queryDefaulted(params.getMiniId());
            if (contVersion == null) {
                return ResultVO.error(StaticStr.APP_CONTENT_VERSION_NOT_CONFIG);
            }
        }
        params.setContVersionId(contVersion.getId());
        log.info("boutiqueType: {}, 当前用户 {} 手机配置的语言: {}，查询到的APP内容版本: {} - {}", params.getBoutiqueType(), sessionVO.getMemberId(), lang, contVersion.getId(), contVersion.getName());
        // 查询配置
        FastMiniSettingPO miniSettingPO = fastMiniSettingService.queryInfoByRedis(params);
        if (miniSettingPO == null) {
            return ResultVO.error(StaticStr.CONTENT_CONFIG_DATA_MISSING);
        }

        fastMiniSettingService.resetQuick(miniSettingPO, sessionVO);

        // 精品剧id
        FastMiniRecommendPO rePO;

        // null/0、剧场推荐；1、精选TAB
        if (params.getBoutiqueType() != null && params.getBoutiqueType() == 1) {
            rePO = miniSettingPO.getRecommendSuperList().get(0);   // 精选
        } else {
            rePO = miniSettingPO.getRecommendTheaterList().get(0); // 推荐
        }

        List<FastDramaVO> dramaList = rePO.getDramaList();
        log.info("memberId: {}, boutiqueType: {}, dramaListSize: {}", sessionVO.getMemberId(), params.getBoutiqueType(), dramaList.size());

        // 查询我最近观看
        List<MemberDramaVO> recentList = fastMemberService.getMemberRecentListRedis(sessionVO.getMemberId());
        // 查询我订阅
        List<MemberDramaVO> addictionList = fastMemberService.getMemberAddictionListRedis(sessionVO.getMemberId());

        for (FastDramaVO cur : dramaList) {
            if (notEmpty(cur.getDramaCover())) {
                // 图片默认为0，0
                cur.setDramaCover(aliCdnService.getImgFullUrl(cur.getDramaCover()));
            }
            if (notEmpty(cur.getDramaCoverHor())) {
                // 图片默认为0，0
                cur.setDramaCoverHor(aliCdnService.getImgFullUrl(cur.getDramaCoverHor()));
            }
            // 设置默认剧集
            cur.setSeriesNum(1);
            // 设置我的观看集数
            if (recentList != null) {
                recentList.stream()
                        .filter(e -> e.getDramaId().equals(cur.getId()))
                        .findFirst()
                        .ifPresent(e -> cur.setSeriesNum(e.getSeriesNum()));
            }

            // 计算字幕类型
            Integer captionType = getCaptionType(cur.getId(), LanguageContext.getLanguageType());
            if (captionType == null) {
                log.info("查询到短剧 {} 的 {} 语言的视频数据对应的字幕类型，走一次兜底操作", cur.getId(), LanguageContext.getLanguageType());
                captionType = getCaptionType(cur.getId(), cur.getDefaultLang());
                if (captionType == null) {
                    log.info("查询到短剧 {} 的 {} 兜底语言的视频数据对应的字幕类型也是空，说明数据配置有误", cur.getId(), cur.getDefaultLang());
                    continue;
                }
            }
            log.info("Boutique -> 短剧 {} 的字幕类型: {}", cur.getId(), captionType);

            // 设置剧集url
            String url = fastDramaSeriesService.getDramaUrl(cur.getId(), cur.getSeriesNum(), sessionVO.getMiniId(), lang, captionType);
            // 英文兜底
            if (StrUtil.isBlank(url)) {
                log.info("短剧 {} 对应的 {} 语言的剧集 {} 不存在，采用 {} 兜底", cur.getId(), lang, cur.getSeriesNum(), cur.getDefaultLang());
                url = fastDramaSeriesService.getDramaUrl(cur.getId(), cur.getSeriesNum(), sessionVO.getMiniId(), cur.getDefaultLang(), captionType);
            }
            // log.info("最终查询到的数据 =>> dramaId: {}, seriesNum: {}, url: {}", cur.getId(), cur.getSeriesNum(), url);
            // 英文也没有，那就真没有
            if (StrUtil.isNotBlank(url)) {
                cur.setUrl(url);
            }
            // 设置我的订阅
            cur.setAddiction(0); // 默认未订阅
            if (addictionList != null) {
                addictionList.stream()
                        .filter(e -> e.getDramaId().equals(cur.getId()))
                        .findFirst()
                        .ifPresent(e -> cur.setAddiction(1));
            }
        }
        Map<String, Object> results = new HashMap<>();// initialCapacity = (需要存储的元素个数 / 负载因子) + 1
        results.put("list", dramaList);
        return ResultVO.success(results);
    }

    /**
     * 全量查询分销商剧-供客户端用-性能优化
     */
    public ResultVO getRetailDramaAllList(SessionVO sessionVO) {
        Map<Integer, MiniDramaVO> dramaMap = getRetailDramaAllMap(sessionVO.getRetailId());
        Map<String, Object> results = new HashMap<>();
        results.put("dramaMap", dramaMap);
        results.put("total", dramaMap.size());
        return ResultVO.success(results);
    }

    // 分销商全量剧
    public Map<Integer, MiniDramaVO> getRetailDramaAllMap(Integer retailId) {
        String key = StaticVar.RETAIL_MINI_DRAMA_LIST + retailId;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            // 查询数据库
            List<MiniDramaVO> dramaList = fastDramaMapper.queryRetailDramaAllList(retailId);
            Map<Integer, MiniDramaVO> dramaMap = new HashMap<>();
            for (MiniDramaVO item : dramaList) {
                dramaMap.put(item.getDramaId(), item);
                if (StrUtil.isNotEmpty(item.getDramaCover())) {
                    item.setDramaCover(aliCdnService.getImgFullUrl(item.getDramaCover()));
                }
            }
            res = JsonUtil.toString(dramaMap);
            RedisUtil.set(key, res, 60 * 60 * 10);
        }
        return JsonUtil.toMapIntObject(res, MiniDramaVO.class);
    }

    /**
     * 更新支付宝小程序备案号
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateAliAuditNumber(FastDramaPO params) {
        Date nowTime = DateUtil.getNowDate();
        FastDramaPO item = new FastDramaPO();
        item.setId(params.getId());
        item.setAliAuditNumber(params.getAliAuditNumber());
        item.setUpdatorId(params.getUpdatorId());
        item.setUpdateTime(nowTime);
        if (fastDramaMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 校验数据完整性，在上架和改变默认语言时调用
     *
     * @param dramaId 短剧ID
     * @param lang    语言编码
     * @return true/false
     */
    private boolean check(Integer dramaId, String lang) {
        // 是否存在对应语言的剧集数据

        // 如果只存在内嵌，那就只校验内嵌字幕的剧集是否存在；
        // 如果只存在外挂，需要先校验外挂字幕的剧集是否存在，然后再校验对应语言的字幕是否存在；
        // 如果二者都存在，优先校验外挂字幕的剧集，然后再校验内嵌字幕的剧集，二者只要有一个满足即可；

        // 外挂字幕的剧集
        List<FastDramaSeriesVO> external = fastDramaSeriesService.queryInfoByRedis(dramaId, lang, 2);
        // 存在外挂
        if (CollUtil.isNotEmpty(external)) {
            log.info("短剧 {} 在对应语言 {} 下存在外挂字幕剧集 {} 条", dramaId, lang, external.size());
            List<FastDramaCaptionsPO> list = fastDramaCaptionsService.queryByDramaIdAndLang(dramaId, lang, lang);
            // 校验字幕
            if (CollUtil.isNotEmpty(list)) {
                log.info("短剧 {} 在对应语言 {} 下存在字幕文件 {} 条", dramaId, lang, list.size());
                return true;
            } else {
                log.info("短剧 {} 在对应语言 {} 下不存在字幕文件", dramaId, lang);
                // return false;
            }
        } else {
            log.info("短剧 {} 在对应语言 {} 下不存在外挂字幕剧集", dramaId, lang);
            // return false;
        }

        // 内嵌字幕的剧集
        List<FastDramaSeriesVO> embedded = fastDramaSeriesService.queryInfoByRedis(dramaId, lang, 1);
        // 存在内嵌
        if (CollUtil.isNotEmpty(embedded)) {
            log.info("短剧 {} 在对应语言 {} 下存在内嵌字幕剧集 {} 条", dramaId, lang, embedded.size());
            return true;
        } else {
            log.info("短剧 {} 在对应语言 {} 下不存在内嵌字幕剧集", dramaId, lang);
            // return false;
        }

        return false;
    }

    /**
     * 更改默认语言
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDefaultLang(FastDramaPO params) {
        if (!check(params.getId(), params.getDefaultLang())) {
            return MethodVO.error("请先在该语言下上传剧集");
        }
        Date nowTime = DateUtil.getNowDate();
        FastDramaPO item = new FastDramaPO();
        item.setId(params.getId());
        item.setDefaultLang(params.getDefaultLang());
        item.setUpdatorId(params.getUpdatorId());
        item.setUpdateTime(nowTime);
        if (fastDramaMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public ResultVO<?> getRecommendBottomList(FastMiniSettingPO params, SessionVO sessionVO, PageVO pageVO) {
        // 拿到用户配置的语言，找到对应的版本
        String lang = LanguageContext.getLanguageType();
        FastMiniContVersionPO contVersion = fastMiniContVersionService.queryByLangCodeNotDefaulted(params.getMiniId(), lang);
        if (contVersion == null) {
            // 如果对应语言的版本未找到，那就找默认的兜底版本
            contVersion = fastMiniContVersionService.queryDefaulted(params.getMiniId());
            if (contVersion == null) {
                return ResultVO.error(StaticStr.APP_CONTENT_VERSION_NOT_CONFIG);
            }
        }
        params.setContVersionId(contVersion.getId());
        // 查询配置
        FastMiniSettingPO miniSettingPO = fastMiniSettingService.queryInfoByRedis(params);
        // 精品剧id
        FastMiniRecommendPO rePO = miniSettingPO.getRecommendBottom();
        List<FastDramaVO> dramaList = rePO.getDramaList();
        if (CollUtil.isEmpty(dramaList)) {
            Map<String, Object> results = new HashMap<>();
            results.put("list", new ArrayList<>());
            pageVO.setTotalPage(0L);
            pageVO.setTotal(0L);
            results.put("page", pageVO);
            return ResultVO.success(results);
        }
        int totalSize = dramaList.size();
        int start = (pageVO.getPage() - 1) * pageVO.getLimit();
        int end = pageVO.getPage() * pageVO.getLimit();
        if (start > dramaList.size()) {
            Map<String, Object> results = new HashMap<>();
            results.put("list", new ArrayList<>());
            pageVO.setTotalPage((long) (totalSize % pageVO.getPage() == 0 ? totalSize / pageVO.getPage() : (totalSize / pageVO.getPage()) + 1));
            pageVO.setTotal((long) totalSize);
            results.put("page", pageVO);
            return ResultVO.success(results);
        }
        dramaList = dramaList.subList(start, Math.min(dramaList.size(), end));
        // 查询我最近观看
        List<MemberDramaVO> recentList = fastMemberService.getMemberRecentListRedis(sessionVO.getMemberId());
        // 查询我订阅
        List<MemberDramaVO> addictionList = fastMemberService.getMemberAddictionListRedis(sessionVO.getMemberId());

        for (FastDramaVO cur : dramaList) {
            if (notEmpty(cur.getDramaCover())) {
                // 图片默认为0，0
                cur.setDramaCover(aliCdnService.getImgFullUrl(cur.getDramaCover()));
            }
            if (notEmpty(cur.getDramaCoverHor())) {
                // 图片默认为0，0
                cur.setDramaCoverHor(aliCdnService.getImgFullUrl(cur.getDramaCoverHor()));
            }
            // 设置默认剧集
            cur.setSeriesNum(1);
            // 设置我的观看集数
            if (recentList != null) {
                recentList.stream()
                        .filter(e -> e.getDramaId().equals(cur.getId()))
                        .findFirst()
                        .ifPresent(e -> cur.setSeriesNum(e.getSeriesNum()));
            }

            // 计算字幕类型
            Integer captionType = getCaptionType(cur.getId(), LanguageContext.getLanguageType());
            if (captionType == null) {
                log.info("查询到短剧 {} 的 {} 语言的视频数据对应的字幕类型，走一次兜底操作", cur.getId(), LanguageContext.getLanguageType());
                captionType = getCaptionType(cur.getId(), cur.getDefaultLang());
                if (captionType == null) {
                    log.info("查询到短剧 {} 的 {} 兜底语言的视频数据对应的字幕类型也是空，说明数据配置有误", cur.getId(), cur.getDefaultLang());
                    continue;
                }
            }
            log.info("Recommend -> 短剧 {} 的字幕类型: {}", cur.getId(), captionType);

            // 设置剧集url
            String url = fastDramaSeriesService.getDramaUrl(cur.getId(), cur.getSeriesNum(), sessionVO.getMiniId(), lang, captionType);
            if (StrUtil.isBlank(url)) {
                url = fastDramaSeriesService.getDramaUrl(cur.getId(), cur.getSeriesNum(), sessionVO.getMiniId(), LanguageEnum.ENGLISH.getCode(), captionType);
            }
            if (StrUtil.isNotBlank(url)) {
                cur.setUrl(url);
            }
            // 设置我的订阅
            cur.setAddiction(0); // 默认未订阅
            if (addictionList != null) {
                addictionList.stream()
                        .filter(e -> e.getDramaId().equals(cur.getId()))
                        .findFirst()
                        .ifPresent(e -> cur.setAddiction(1));
            }
        }
        Map<String, Object> results = new HashMap<>();// initialCapacity = (需要存储的元素个数 / 负载因子) + 1
        results.put("list", dramaList);
        pageVO.setTotalPage((long) (totalSize % pageVO.getPage() == 0 ? totalSize / pageVO.getPage() : (totalSize / pageVO.getPage()) + 1));
        pageVO.setTotal((long) totalSize);
        results.put("page", pageVO);
        return ResultVO.success(results);
    }

    public void updateLineTime(FastDramaPO params) {
        if (StrUtil.isNotBlank(params.getDramaLineTimeListStr())) {
            params.setDramaLineTimeList(JsonUtil.toList(params.getDramaLineTimeListStr(), FastDramaLineTimePO.class));
        }
        List<FastDramaLineTimePO> existsLineList = new ArrayList<>();
        if (Objects.nonNull(params.getNativeFlag()) && params.getNativeFlag() == 1) {
            FastDramaLineTimePO fastDramaLineTimePO = new FastDramaLineTimePO();
            fastDramaLineTimePO.setType(2);
            fastDramaLineTimePO.setDramaId(params.getId());
            existsLineList = fastDramaLineTimeMapper.queryList(fastDramaLineTimePO);

        } else if (Objects.nonNull(params.getThirdFlag()) && params.getThirdFlag() == 1) {
            FastDramaLineTimePO fastDramaLineTimePO = new FastDramaLineTimePO();
            fastDramaLineTimePO.setType(3);
            fastDramaLineTimePO.setDramaId(params.getId());
            existsLineList = fastDramaLineTimeMapper.queryList(fastDramaLineTimePO);
        }
        Set<Integer> existsSubTypes = existsLineList.stream().map(FastDramaLineTimePO::getSubType).collect(Collectors.toSet());

        List<FastDramaLineTimePO> dramaLineTimeList = params.getDramaLineTimeList();
        Map<Integer, FastDramaLineTimePO> saveMap;
        Map<Integer, FastDramaLineTimePO> allMap;
        if (CollUtil.isNotEmpty(dramaLineTimeList)) {
            List<FastDramaLineTimePO> saveList = dramaLineTimeList
                    .stream().filter(item -> !existsSubTypes.contains(item.getSubType()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(saveList)) {
                saveList.forEach(lineTimeItem -> {
                    // 上下架状态 存在指定平台id
                    if (Objects.nonNull(params.getThirdPlatformId())) {
                        if (params.getThirdPlatformId().equals(lineTimeItem.getSubType())) {
                            lineTimeItem.setShelfState(params.getThirdShelfState());
                        }
                    } else {
                        // 不存在指定平台 全部更新状态
                        lineTimeItem.setShelfState(params.getThirdShelfState());
                    }
                    lineTimeItem.setDramaId(params.getId());
                    lineTimeItem.setCreateTime(DateUtil.getNowDate());
                });
                fastDramaLineTimeMapper.insertBatch(saveList);
            }
            saveMap = saveList.stream().collect(Collectors.toMap(FastDramaLineTimePO::getSubType, Function.identity(), (i1, i2) -> i1));
            allMap = dramaLineTimeList.stream().collect(Collectors.toMap(FastDramaLineTimePO::getSubType, Function.identity(), (i1, i2) -> i1));
        } else {
            saveMap = new HashMap<>();
            allMap = new HashMap<>();
        }
        // 更新列表
        existsLineList.forEach(item -> {
            if (!saveMap.containsKey(item.getSubType())) {
                item.setOnlineTime(allMap.get(item.getSubType()).getOnlineTime());
                item.setUpdateTime(DateUtil.getNowDate());
                // 上下架状态 存在指定平台id
                if (Objects.nonNull(params.getThirdPlatformId())) {
                    if (params.getThirdPlatformId().equals(item.getSubType())) {
                        item.setShelfState(params.getThirdShelfState());
                    }
                } else {
                    // 不存在指定平台 全部更新状态
                    item.setShelfState(params.getThirdShelfState());
                }
                fastDramaLineTimeMapper.updateById(item);
            }
        });
    }

    @Transactional
    public MethodVO updateDramaProject(FastDramaPO item) {
        FastDramaPO existsDrama = fastDramaMapper.queryById(item.getId());
        if (Objects.isNull(existsDrama)) {
            return MethodVO.error("短剧不存在!");
        }

        FastDramaPO updatePO = new FastDramaPO();
        updatePO.setId(item.getId());
        updatePO.setProjectId(item.getProjectId());
        if (item.getContentLevel() != null) {
            updatePO.setContentLevel(item.getContentLevel());
        }

        // 版本类型为0才可进行更改
        if (existsDrama.getVersionType() == 0 && Objects.nonNull(item.getVersionType())) {
            if (!Arrays.asList(1, 2).contains(item.getVersionType())) {
                return MethodVO.error("剪辑来源类型错误!");
            }
            updatePO.setVersionType(item.getVersionType());
            if (item.getVersionType() == 2) {
                if (Objects.isNull(item.getSourceDramaId())) {
                    return MethodVO.error("剪辑来源不能为空!");
                }
                FastDramaPO fastDramaPO1 = new FastDramaPO();
                fastDramaPO1.setId(item.getSourceDramaId());
                fastDramaPO1.setPlatformFlag(getPlatformFlag(item));
                FastDramaPO fastDramaPO = fastDramaMapper.queryDetailById(fastDramaPO1);
                if (Objects.isNull(fastDramaPO) || fastDramaPO.getVersionType() != 1) {
                    return MethodVO.error("剪辑来源剧信息不存在!");
                }
                FastDramaPO queryCount = new FastDramaPO();
                queryCount.setSourceDramaId(item.getSourceDramaId());
                int count = fastDramaMapper.queryCount(queryCount);
                // 剪辑版更新版本和剧来源
                updatePO.setVersionNum(count + 2);
                updatePO.setSourceDramaId(item.getSourceDramaId());
            } else {
                // 原版只更新版本号
                // 原版是1
                updatePO.setVersionNum(1);
            }
        }
        int i = fastDramaMapper.updateById(updatePO);
        if (i == 0) {
            transactionRollBack();
            return MethodVO.error("更新失败!");
        }
        updateDramaCode(item.getId());
        updateProjectByDramaId(updatePO);
        return MethodVO.success();
    }

    public void updateDramaCode(Integer dramaId) {
        FastDramaPO item = fastDramaMapper.queryById(dramaId);

        // if (StrUtil.isNotBlank(item.getDramaCode())) {
        // 	return;
        // }
        FastDramaPO updatePO = new FastDramaPO();
        updatePO.setId(item.getId());
        if (Objects.nonNull(item.getVersionType()) && item.getVersionType() != 0 && Objects.nonNull(item.getProjectId())) {
            Integer sourceDramaId = item.getSourceDramaId();
            if (item.getVersionType() == 2) {
                // FastDramaPO fastDramaPO1 = new FastDramaPO();
                // fastDramaPO1.setId(item.getSourceDramaId());
                // FastDramaPO queryCount = new FastDramaPO();
                // queryCount.setSourceDramaId(item.getSourceDramaId());
                // int count = fastDramaMapper.queryCount(queryCount);
                // updatePO.setVersionNum(count + 1);
                // updatePO.setSourceDramaId(item.getSourceDramaId());
            } else {
                // 原版是1
                // updatePO.setVersionNum(1);
                sourceDramaId = item.getId();
            }
            updatePO.setVersionType(item.getVersionType());
            updatePO.setVersionSource(item.getVersionSource());
            // 短剧编码生成
            String prefix = item.getLaunchFlag() == 1 ? "T" : item.getNativeFlag() == 1 ? "D" : "F";
            // 使用原先的前缀
            if (StrUtil.isNotBlank(item.getDramaCode())) {
                prefix = item.getDramaCode().substring(0, 1);
            } else {
                // 原先没有编码 并且存在多个使用范围
                if (Stream.of(item.getLaunchFlag(), item.getNativeFlag(), item.getThirdFlag()).filter(flag -> flag == 1)
                        .count() > 1) {
                    return;
                }
            }

            updatePO.setDramaCode(prefix + "-" + item.getProjectId() + "-" + sourceDramaId + "-" + item.getVersionNum());
            fastDramaMapper.updateById(updatePO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO batchUpdateDramaProject(FastDramaPO params) {
        String[] split = params.getIds().split(",");
        for (String s : split) {
            params.setId(Integer.valueOf(s));
            MethodVO methodVO = updateDramaProject(params);
            if (methodVO.getCode() != 0) {
                transactionRollBack();
                return methodVO;
            }
        }
        return MethodVO.success();
    }

    public MethodVO updateShelfThird(FastDramaPO params) {
        FastDramaPO fastDramaPO = fastDramaMapper.queryById(params.getId());
        FastDramaLineTimePO lineQuery = new FastDramaLineTimePO();
        lineQuery.setShelfState(params.getShelfState());
        lineQuery.setDramaId(params.getId());
        String prefix = "";
        // 三方和端原生上下架
        if (params.getPlatformFlag() == 2) {
            // 端原生更新
            lineQuery.setType(2);
            prefix = "D";
        } else if (params.getPlatformFlag() == 3) {
            // 三方分发更新
            lineQuery.setType(3);
            lineQuery.setSubType(params.getThirdPlatformId());
            prefix = "F";
        } else if (params.getPlatformFlag() == 1) {
            // 投流剧更新 三方分发和端原生存在的话都下架
            prefix = "T";
        } else {
            return MethodVO.error("更新失败!");
        }
        // 原始剧下架其他平台都下架
        if (StrUtil.isNotBlank(fastDramaPO.getDramaCode()) && fastDramaPO.getDramaCode().startsWith(prefix)) {
            lineQuery.setType(null);
        }
        int count = fastDramaLineTimeMapper.updateShelfStateByDramaId(lineQuery);
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDelPlus(FastDramaPO item) {
        FastDramaPO fastDramaPO = new FastDramaPO();
        fastDramaPO.setId(item.getId());
        FastDramaPO existsDrama = fastDramaMapper.queryOne(fastDramaPO);
        if (Objects.isNull(item.getPlatformFlag())) {
            return MethodVO.success();
        }
        String prefix = "";
        if (item.getPlatformFlag() == 2) {
            // 端原生删除
            prefix = "D";
        } else if (item.getPlatformFlag() == 3) {
            // 三方分发删除
            prefix = "F";
        } else if (item.getPlatformFlag() == 1) {
            // 投流
            prefix = "T";
        }
        // 原始剧下架其他平台都下架
        if (StrUtil.isNotBlank(existsDrama.getDramaCode()) && existsDrama.getDramaCode().startsWith(prefix)) {
            if (item.getPlatformFlag() == 2) {
                // 本身剧删除
                delDrama(item);
            } else if (item.getPlatformFlag() == 3) {
                // 删除三方分发的时候在全部删除需要删除端原生引用
                if (Objects.isNull(item.getThirdPlatformId())) {
                    // 本身剧删除
                    delDrama(item);
                }
            } else if (item.getPlatformFlag() == 1) {
                // 本身剧删除
                delDrama(item);
            }
        }
        return MethodVO.success();
    }

    public MethodVO delDrama(FastDramaPO item) {
        FastDramaPO del = new FastDramaPO();
        del.setUpdatorId(item.getUpdatorId());
        del.setUpdateTime(DateUtil.getNowDate());
        del.setId(item.getId());
        del.setDelFlag(StaticVar.YES);
        fastDramaMapper.updateById(del);
        return MethodVO.success();
    }

    public List<FastTagPO> getDramaTags(Integer contentType, Collection<Integer> tagIds) {
        List<FastTagPO> tags = fastDramaMapper.getDramaTags(contentType, tagIds);
        if (CollUtil.isNotEmpty(tags)) {
            // i18n
            tags.forEach(tag -> fastTagService.fillI18n(tag));
        }
        return tags;
    }

    public ResultVO<?> getDramaList(FastDramaPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastDramaPO> list = fastDramaMapper.getAllSimpleDramaList(params);

        return ResultVO.success(getPageListData(list, pageVO));
    }

    public ResultVO<?> getDramaRankings(FastDramaPO params, PageVO pageVO) {
        // TODO 暂时没有计算规则，只要求使用在追数量来做排行，后期会有评论数以及权重计算规则
        if (params.getRetailId() > 0) {
            List<Integer> ids = fastDramaRetailService.queryInfoByRedis(params.getRetailId());
            if (CollUtil.isEmpty(ids)) {
                return ResultVO.success(getDefaultPageListData());
            }
            params.setIds(StrUtil.join(ids));
        }
        Integer limit = pageVO.getLimit(); // 如果条数不够，补齐第一页数据
        startPage(pageVO);
        List<FastDramaPO> list = fastDramaMapper.getDramaRankings(params);
        // 兜底
        if (CollUtil.isEmpty(list)) {
            startPage(pageVO);
            list = fastDramaMapper.querySimpleList(params);
        } else {
            if (list.size() < limit) {
                // 才上线时，数据不够，补齐第一页
                pageVO.setLimit(limit - list.size());
                startPage(pageVO);
                params.setExIds(list.stream().map(FastDramaPO::getId).collect(Collectors.toList()));
                List<FastDramaPO> more = fastDramaMapper.querySimpleList(params);
                if (CollUtil.isNotEmpty(more)) {
                    list.addAll(more);
                }
            }
        }
        double baseMultiplier = 1000.0; // 基础倍数
        double decayFactor = 0.1; // 衰减因子
        for (int i = 0; i < list.size(); i++) {
            FastDramaPO po = list.get(i);
            fillDramaData(po, params);
            // 使用非线性函数计算heat值
            int addictionCount = po.getAddictionCount() == null ? 0 : po.getAddictionCount();
            // 根据位置计算衰减因子
            double positionFactor = Math.exp(-decayFactor * i);
            po.setAddictionCount(addictionCount);
            po.setHeat((int) ((addictionCount * baseMultiplier) * positionFactor));
        }
        // 恢复limit
        pageVO.setLimit(limit);
        return ResultVO.success(getPageListData(list, pageVO));
    }
}















