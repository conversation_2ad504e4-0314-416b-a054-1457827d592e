/*
 * Powered By fast.up
 */
package com.fast.service.drama;

import com.fast.constant.StaticStr;
import com.fast.enums.LanguageEnum;
import com.fast.mapper.drama.FastDramaCaptionsMapper;
import com.fast.po.drama.FastDramaCaptionsPO;
import com.fast.po.language.FastLanguagePO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastDramaCaptionsService extends BaseService {

    @Autowired
    private FastDramaCaptionsMapper fastDramaCaptionsMapper;

    /**
     * 通过id查询单个对象
     */
    public FastDramaCaptionsPO queryById(FastDramaCaptionsPO params) {
        return fastDramaCaptionsMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaCaptionsPO queryById(Integer id) {
        return fastDramaCaptionsMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastDramaCaptionsPO queryOne(FastDramaCaptionsPO params) {
        return fastDramaCaptionsMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastDramaCaptionsPO> queryList(FastDramaCaptionsPO params) {
        return fastDramaCaptionsMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastDramaCaptionsPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastDramaCaptionsPO> list = fastDramaCaptionsMapper.queryList(params);
        for (FastDramaCaptionsPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setSubLangName(LanguageEnum.ofCode(cur.getSubLang()).getName());
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastDramaCaptionsPO params) {
        return fastDramaCaptionsMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastDramaCaptionsPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastDramaCaptionsMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastDramaCaptionsPO> list) {
        if (fastDramaCaptionsMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastDramaCaptionsPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastDramaCaptionsMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastDramaCaptionsPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        FastDramaCaptionsPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        fastDramaCaptionsMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO deleteBy(FastDramaCaptionsPO params) {
        if (params == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        List<FastDramaCaptionsPO> list = queryList(params);
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(po -> fastDramaCaptionsMapper.deleteById(po.getId()));
        }
        return MethodVO.success();
    }

    public List<FastDramaCaptionsPO> queryByDramaIdAndLang(Integer dramaId, String lang, String subLang) {
        if (dramaId == null) {
            return null;
        }
        FastDramaCaptionsPO po = new FastDramaCaptionsPO();
        po.setDramaId(dramaId);
        po.setLanguageCode(lang);
        po.setSubLang(subLang);
        return queryList(po);
    }

    public List<FastDramaCaptionsPO> queryByDramaIdAndLang(Integer dramaId, String lang) {
        if (dramaId == null) {
            return null;
        }
        return queryByDramaIdAndLang(dramaId, lang, lang);
    }

    public List<FastDramaCaptionsPO> queryByDramaIdAndSeriesNum(Integer dramaId, String lang, Integer seriesNum) {
        if (dramaId == null) {
            return null;
        }
        FastDramaCaptionsPO po = new FastDramaCaptionsPO();
        po.setDramaId(dramaId);
        po.setLanguageCode(lang);
        po.setSeriesNum(seriesNum);
        return queryList(po);
    }

    public Set<String> getSubLangCodes(Integer dramaId, String lang, Integer seriesNum) {
        List<FastDramaCaptionsPO> list = queryByDramaIdAndSeriesNum(dramaId, lang, seriesNum);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().map(FastDramaCaptionsPO::getSubLang).collect(Collectors.toSet());
    }

    public List<FastLanguagePO> getSubLangs(Integer dramaId, String lang, Integer seriesNum) {
        List<FastDramaCaptionsPO> list = queryByDramaIdAndSeriesNum(dramaId, lang, seriesNum);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().map(p -> {
            FastLanguagePO item = new FastLanguagePO();
            item.setCode(p.getSubLang());
            item.setName(LanguageEnum.ofCode(p.getSubLang()).getName());
            return item;
        }).collect(Collectors.toList());
    }

    public List<FastDramaCaptionsPO> queryByDramaIdAndSeriesNumAndLang(Integer dramaId, String lang, Integer seriesNum, String subLang) {
        if (dramaId == null) {
            return null;
        }
        FastDramaCaptionsPO po = new FastDramaCaptionsPO();
        po.setDramaId(dramaId);
        po.setLanguageCode(lang);
        if (seriesNum != null) {
            po.setSeriesNum(seriesNum);
        }
        po.setSubLang(subLang);
        return queryList(po);
    }
}
