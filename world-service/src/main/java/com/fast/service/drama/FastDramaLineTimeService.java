/*
 * Powered By fast.up
 */
package com.fast.service.drama;

import com.fast.constant.StaticStr;
import com.fast.mapper.drama.FastDramaLineTimeMapper;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.po.drama.FastDramaLineTimePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastDramaLineTimeService extends BaseService {

    @Autowired
    private FastDramaLineTimeMapper fastDramaLineTimeMapper;

    @Autowired
    private FastDramaMapper fastDramaMapper;

    @Autowired
    private FastDramaService fastDramaService;

    /**
     * 通过id查询单个对象
     */
    public FastDramaLineTimePO queryById(FastDramaLineTimePO params) {
        return fastDramaLineTimeMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaLineTimePO queryById(Integer id) {
        return fastDramaLineTimeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastDramaLineTimePO queryOne(FastDramaLineTimePO params) {
        return fastDramaLineTimeMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastDramaLineTimePO> queryList(FastDramaLineTimePO params) {
        return fastDramaLineTimeMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastDramaLineTimePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastDramaLineTimePO> list = fastDramaLineTimeMapper.queryList(params);
        for (FastDramaLineTimePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastDramaLineTimePO params) {
        return fastDramaLineTimeMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastDramaLineTimePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastDramaLineTimeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastDramaLineTimePO> list) {
        if (fastDramaLineTimeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastDramaLineTimePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastDramaLineTimeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public MethodVO batchInsert(FastDramaLineTimePO params) {
        List<FastDramaLineTimePO> dramaLineTimeList = params.getDramaLineTimeList();
        dramaLineTimeList.forEach(item -> {
            item.setDramaId(params.getDramaId());
            item.setCreateTime(DateUtil.getNowDate());
            item.setType(params.getType());
        });
        fastDramaLineTimeMapper.insertBatch(dramaLineTimeList);
        return MethodVO.success();
    }
}
