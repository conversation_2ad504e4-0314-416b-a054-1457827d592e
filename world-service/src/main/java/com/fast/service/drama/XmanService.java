/*
 * Powered By fast.up
 */
package com.fast.service.drama;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticVar;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.mapper.drama.FastDramaRetailMapper;
import com.fast.mapper.fee.FastFeeRuleMapper;
import com.fast.mapper.retail.FastRetailMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.drama.FastDramaRetailPO;
import com.fast.po.fee.FastFeeRulePO;
import com.fast.po.retail.FastRetailPO;
import com.fast.service.aliyun.AliCdnService;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.http.HttpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class XmanService extends BaseService {

    @Autowired
    private AliCdnService aliCdnService;
    @Autowired
    private FastRetailMapper fastRetailMapper;
    @Autowired
    private FastDramaMapper fastDramaMapper;
    @Autowired
    private FastFeeRuleMapper fastFeeRuleMapper;
    @Autowired
    private FastDramaRetailMapper fastDramaRetailMapper;

    /**
     * 给挂载分销商发送剧信息(首次同步推剧)
     *
     * @param retailId 分销商id
     */
    @Async
    public void sendAllDramaToRetail(Integer retailId) {
        // 先拿分销商
        FastRetailPO retailPO = fastRetailMapper.queryById(retailId);
        if (retailPO == null || StrUtil.isEmpty(retailPO.getPushDramaUrl())) {
            return;
        }
        // 查询关联剧
        FastDramaRetailPO drParam = new FastDramaRetailPO();
        drParam.setRetailId(retailId);
        List<FastDramaRetailPO> drList = fastDramaRetailMapper.queryList(drParam);
        for (FastDramaRetailPO item : drList) {
            sendToXiangYueHui(retailPO, item.getDramaId());
        }
    }

    /**
     * 给挂载分销商发送剧信息(修改剧)
     *
     * @param dramaId 剧id
     */
    public int sendAllRetailToDrama(Integer dramaId) {
        // 查询分销商
        FastDramaRetailPO drParam = new FastDramaRetailPO();
        drParam.setDramaId(dramaId);
        List<FastDramaRetailPO> drList = fastDramaRetailMapper.queryList(drParam);
        for (FastDramaRetailPO item : drList) {
            // 查询达人挂载分销商
            FastRetailPO retailParam = new FastRetailPO();
            retailParam.setRetailType(4);
            FastRetailPO retailPO = fastRetailMapper.queryOne(retailParam);
            if (retailPO != null && StrUtil.isNotEmpty(retailPO.getPushDramaUrl())) {
                sendToXiangYueHui(retailPO, item.getDramaId());
            }
        }
        return 1;
    }

    /**
     * 发送给享悦荟
     *
     * @return
     */
    public int sendToXiangYueHui(FastRetailPO retailPO, Integer dramaId) {
        // https://pmpx.ibidian.com/v1/inject/createthirdmedia
        actionLogService.log("xman_upload_drama", "开始推送剧" + dramaId + "推送地址：" + retailPO.getPushDramaUrl());
        FastDramaPO dramaPO = fastDramaMapper.queryById(dramaId);
        if (dramaPO.getReleaseDate() == null) {
            log.error("剧" + dramaId + "未上线，同步给挂载分销商失败");
            return 0;
        }
        if (notEmpty(dramaPO.getDramaCover())) {
            dramaPO.setDramaCover(aliCdnService.getImgFullUrl(dramaPO.getDramaCover()));
        }
        if (notEmpty(dramaPO.getDramaCoverHor())) {
            dramaPO.setDramaCoverHor(aliCdnService.getImgFullUrl(dramaPO.getDramaCoverHor()));
        } else {
            dramaPO.setDramaCoverHor(dramaPO.getDramaCover());// 使用横图代替
        }
        JSONObject params = new JSONObject();
        Long timestamp = DateUtil.getNowTimeStampShort();
        // 必填参数
        params.put("timestamp", timestamp);// 时间戳
        params.put("customer", "tt_boyi"); // 合作⽅标识 抖音
        params.put("ctime", timestamp);// 秒级时间戳
        params.put("resid", dramaPO.getId().toString());// 合作⽅的资源ID，唯⼀标识 剧id
        params.put("name", dramaPO.getDramaName());// 资源名称
        params.put("poster", dramaPO.getDramaCover());// 竖图url
        params.put("still", dramaPO.getDramaCoverHor());// 横图url
        params.put("channel", "短剧");// 频道(⼀级分类)
        params.put("status", 1);// 是否上架，1=上架，0=下架
        params.put("fee_mode", "1");// 付费模式0=全免费,1=会员可免费看,2=仅单点可看
        params.put("totalnum", dramaPO.getSeriesNumAll());// 总集数
        params.put("updatenum", dramaPO.getSeriesNumUpdate());// 更新⾄的集数
        params.put("release_date", DateUtil.format06(dramaPO.getReleaseDate()));
        if (dramaPO.getUpdateState() == 0) {
            params.put("is_end", 1);
        } else {
            params.put("is_end", 0);
        }
        // 子集 查询通用模板
        FastFeeRulePO feeParam = new FastFeeRulePO();
        feeParam.setRetailId(0);
        feeParam.setOfficialId(0);
        feeParam.setDramaId(dramaId);
        FastFeeRulePO feePO = fastFeeRuleMapper.queryOne(feeParam);
        JSONArray episodesArray = new JSONArray();
        for (int i = 1; i <= dramaPO.getSeriesNumAll(); i++) {
            JSONObject itemObj = new JSONObject();
            itemObj.put("sub_resid", dramaId + "_" + i);
            itemObj.put("num", i);
            itemObj.put("name", dramaPO.getDramaName() + "第" + i + "集");
            if (feePO == null || i < feePO.getStartNum()) {
                itemObj.put("fee_mode", 0);// 0=免费，1=付费
            } else {
                itemObj.put("fee_mode", 1);// 0=免费，1=付费
            }
            itemObj.put("status", 1);// ⼦资源是否上架，1=上架，0=下架
            episodesArray.add(itemObj);
        }
        params.put("episodes", episodesArray);

        Map<String, String> headers = new HashMap<>();
        String sign = getXiangYueHuiTtSign(params, timestamp);
        actionLogService.log("xman_upload_drama", "签名：" + sign);

        headers.put("customer-sign", sign);

        actionLogService.log("xman_upload_drama", "dramaId=" + dramaId + ",上传参数：" + params.toJSONString());
        // 发送抖音短剧同步
        String ttRes = HttpUtil.postBody(retailPO.getPushDramaUrl(), params.toJSONString(), headers);
        actionLogService.log("xman_upload_drama", "dramaId=" + dramaId + ",上传抖音返回：" + ttRes);
        // 发送快手短剧同步
        params.put("customer", "kuaishou_boyi"); // 合作⽅标识 快手
        headers.put("customer-sign", getXiangYueHuiKuaiShouSign(params, timestamp)); // 重新设置签名
        HttpUtil.postBody(retailPO.getPushDramaUrl(), params.toJSONString(), headers);
        actionLogService.log("xman_upload_drama", "dramaId=" + dramaId + ",上传快手返回：" + ttRes);
        return 1;
    }

    /**
     * 享悦荟签名-抖音
     *
     * @return
     */
    public String getXiangYueHuiTtSign(JSONObject body, Long timestamp) {
        String sb = body.toJSONString() +
                timestamp +
                StaticVar.YXH_SECRET_TT;// 秘钥
        return Md5Util.getMD5(sb);
    }

    /**
     * 签名-快手
     *
     * @param body
     * @param timestamp
     * @return
     */
    public String getXiangYueHuiKuaiShouSign(JSONObject body, Long timestamp) {
        String sb = body.toJSONString() +
                timestamp +
                StaticVar.YXH_SECRET_KS;// 秘钥
        return Md5Util.getMD5(sb);
    }


}




























