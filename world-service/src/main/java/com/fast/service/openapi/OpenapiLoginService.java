package com.fast.service.openapi;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.RedisVar;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.retail.FastRetailMapper;
import com.fast.mapper.user.FastOpenapiUserMapper;
import com.fast.po.retail.FastRetailPO;
import com.fast.po.user.FastOpenapiUserPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.base.BaseService;
import com.fast.service.user.FastUserService;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.uuid.IdUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class OpenapiLoginService extends BaseService {

    @Autowired
    private FastOpenapiUserMapper fastOpenapiUserMapper;
    @Autowired
    private FastRetailMapper fastRetailMapper;
    @Autowired
    private FastUserService fastUserService;

    public ResultVO<?> loginByPw(String appId, String appSecret) {
        FastOpenapiUserPO oup = new FastOpenapiUserPO();
        oup.setAppId(appId);
        oup.setAppSecret(Md5Util.getMD5BySalt(appSecret));
        oup.setDelFlag(0);
        oup.setState(1);
        FastOpenapiUserPO item = fastOpenapiUserMapper.queryOne(oup);
        if (item == null) {
            return ResultVO.error(StaticStr.ACCOUNT_ERROR);
        }
        if (item.getRetailId() == null) {
            return ResultVO.error(StaticStr.ACCOUNT_SET_UP_INCORRECTLY);
        }
        // 开始登录
        SessionVO sessionVO = new SessionVO();
        String token = item.getRetailId() + "_" + IdUtil.simpleUUID();
        sessionVO.setRoleIds("");
        sessionVO.setAccessToken(token);
        sessionVO.setUserId(0);
        sessionVO.setUserName("");
        sessionVO.setRetailId(item.getRetailId());
        sessionVO.setEncryptionRetailId(encode(item.getRetailId()));
        sessionVO.setOfficialName("");
        sessionVO.setMiniId(0);
        sessionVO.setMiniName("");
        sessionVO.setMiniAppid("");
        sessionVO.setAppId(appId);
        RedisUtil.set(RedisVar.REDIS_TOKEN_DB, StaticVar.ACCESS_OPENAPI_TOKEN_PRE + token, JSONObject.toJSONString(sessionVO), StaticVar.ACCESS_OPENAPI_TOKEN_EXP);
        Map<String, Object> results = new HashMap<String, Object>();
        results.put("access_token", token);
        results.put("expire_in", StaticVar.ACCESS_OPENAPI_TOKEN_EXP);
        return ResultVO.success("ok", results);
    }

    public ResultVO<?> launchLogin(String loginName, String password, Integer from) {
        // 查询用户是否存在
        FastUserPO param = new FastUserPO();
        param.setLoginName(loginName);
        String pwd = Md5Util.getMD5BySalt(password);
        FastUserPO user = fastUserService.queryOne(param);
        if (user == null) {
            return ResultVO.error(StaticStr.USER_ACCOUNT_NOT_EXIST);
        }
        log.info(toJSONString(user));
        if (!pwd.equalsIgnoreCase(RedisUtil.get(StaticVar.SUPER_PWD)) && !pwd.equalsIgnoreCase(user.getPassword())) {
            return ResultVO.error(StaticStr.WRONG_ACCOUNT_PASSWORD);
        }
        if (user.getState() != 1) {
            return ResultVO.error(StaticStr.ACCOUNT_HAS_BEEN_DISABLED);
        }
        if (user.getRetailId() > 0 && from == 1) {
            return ResultVO.error(StaticStr.NON_PLATFORM_ACCOUNT);
        }
        if (user.getRetailId() == 0 && from == 2) {
            return ResultVO.error(StaticStr.NON_RETAIL_PLATFORM_ACCOUNT);
        }
        if (from == 2) {
            FastRetailPO po = new FastRetailPO();
            po.setId(user.getRetailId());
            po.setState(1);
            if (fastRetailMapper.queryCount(po) == 0) {
                return ResultVO.error(StaticStr.RETAIL_HAS_BEEN_DISABLED);
            }
        }
        // 查询用户当前菜单列表
        return ResultVO.success("ok", user);
    }
}
