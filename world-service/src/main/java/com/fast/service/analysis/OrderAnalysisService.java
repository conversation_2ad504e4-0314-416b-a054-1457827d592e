/*
 * Powered By fast.up
 */
package com.fast.service.analysis;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.analysis.OrderAnalysisMapper;
import com.fast.po.analysis.OrderAnalysisPO;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.retail.FastRetailService;
import com.fast.service.user.FastUserService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 订单分析
 *
 * <AUTHOR>
 */
@Service
public class OrderAnalysisService extends BaseService {

    @Autowired
    private OrderAnalysisMapper orderAnalysisMapper;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastUserService userService;
    @Autowired
    private FastRetailService retailService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 订单排名分析
     */
    public ResultVO<?> getOrderRankAnalysisList(OrderAnalysisPO params, Integer queryAll) {
        // 1=订单笔数;2=订单支付人数;3=充值总金额;4=实到总金额
        List<OrderAnalysisPO> list = null;
        if (queryAll != null && queryAll == 1) {
            list = orderAnalysisMapper.queryRechargeRankAllList(params);
        } else {
            list = orderAnalysisMapper.queryRechargeRankList(params);
        }
        Set<Integer> ids = new HashSet<>();
        list.forEach(cur -> ids.add(cur.getId()));
        Map<Integer, String> nameMap = new HashMap<>();
        switch (params.getGroupByColum()) {
            case 1: {
                // 分销商
                FastRetailPO query = new FastRetailPO();
                query.setIds(StrUtil.join(ids));
                nameMap = retailService.queryRetailNameMap(query);
                break;
            }
            case 2: {
                // 优化师
                FastUserPO query = new FastUserPO();
                query.setIds(StrUtil.join(ids));
                nameMap = userService.queryUserNameMap(query);
                break;
            }
            case 3: {
                // 渠道链接
                FastLinkPO query = new FastLinkPO();
                query.setIds(StrUtil.join(ids));
                nameMap = linkService.queryLinkNameMap(query);
                break;
            }
            case 4: {
                // 短剧
                FastDramaPO query = new FastDramaPO();
                query.setIds(StrUtil.join(ids));
                nameMap = dramaService.queryDramaNameMap(query);
                break;
            }
        }
        if (params.getGroupByColum() < 5) {
            for (OrderAnalysisPO po : list) {
                po.setName(nameMap.get(po.getId()));
            }
        }
        // 倒序排序(由大->小)
        // 排序字段:1=订单笔数;2=订单支付人数;3=充值总金额;4=实到总金额
        switch (params.getSortType()) {
            case 1:
                list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeOrderCount).thenComparing(OrderAnalysisPO::getId).reversed());
                break;
            case 2:
                list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMemberCount).thenComparing(OrderAnalysisPO::getId).reversed());
                break;
            case 3:
            case 4:
                list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMoneySum).thenComparing(OrderAnalysisPO::getId).reversed());
                break;
        }
        return ResultVO.success(list);
    }

    /**
     * 导出订单分析
     * 月月想要加个导出功能，导出【分销商排名】这个图标的数据，点击导出，可导出分销商名称、充值总金额、实到金额、订单支付人数、订单支付笔数，这样的表@张亮亮 @李海龙
     */
    public ResultVO<?> exportOrderRankAnalysisList(SessionVO sessionVO, OrderAnalysisPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_ORDER_ANALYSIS_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        // 查询导出数据
        ResultVO resVO = getOrderRankAnalysisList(params, 1);
        List<OrderAnalysisPO> list = (List<OrderAnalysisPO>) resVO.getResults();
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        String groupTypeName = "";
        switch (params.getGroupByColum()) {
            case 1: {
                groupTypeName = "分销商";
                break;
            }
            case 2: {
                groupTypeName = "优化师";
                break;
            }
            case 3: {
                groupTypeName = "链接";
                break;
            }
            case 4: {
                groupTypeName = "短剧";
                break;
            }
            case 5: {
                groupTypeName = "月份";
                break;
            }
        }
        // 组装导出的excel数据
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (OrderAnalysisPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)
            row.add(cur.getName());
            CollUtil.addNoRepeat(rowHeadNames, groupTypeName + "名称");
            row.add(cur.getRechargeMoneySum());
            CollUtil.addNoRepeat(rowHeadNames, "充值总金额");
            row.add(cur.getRechargeMoneyProfitSum());
            CollUtil.addNoRepeat(rowHeadNames, "实到金额");
            row.add(cur.getRechargeMemberCount());
            CollUtil.addNoRepeat(rowHeadNames, "订单支付人数");
            row.add(cur.getRechargeOrderCount());
            CollUtil.addNoRepeat(rowHeadNames, "订单支付笔数");
            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }

        String title = "订单分析-按" + groupTypeName + "排行";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 支付趋势汇总
     */
    public ResultVO<?> getOrderSumTopAnalysisList(OrderAnalysisPO params, OrderAnalysisPO paramsLast) {
        boolean needLast = notBlank(params.getPayTimeStr());
        // dateType: 1=支付人数;2=支付订单数;3=支付金额;4=订单完成率;5=用户支付率;6=人均支付金额;7=人均支付次数;8=实到金额
        List<OrderAnalysisPO> list = new ArrayList<>();
        int data1, dataLast1 = 0;
        {
            // 支付人数
            params.setState(1);
            paramsLast.setState(1);
            int data = orderAnalysisMapper.queryRechargeMemberCount(params);
            data1 = data;
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(1);
            po.setData(data);
            if (needLast) {
                int dataLast = orderAnalysisMapper.queryRechargeMemberCount(paramsLast);
                po.setRingRate(DoubleUtil.ringRatio(data, dataLast));
                dataLast1 = dataLast;
            }
            list.add(po);
        }
        int data2, dataLast2 = 0;
        {
            // 支付订单数
            params.setState(1);
            paramsLast.setState(1);
            int data = orderAnalysisMapper.queryRechargeOrderCount(params);
            data2 = data;
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(2);
            po.setData(data);
            if (needLast) {
                int dataLast = orderAnalysisMapper.queryRechargeOrderCount(paramsLast);
                po.setRingRate(DoubleUtil.ringRatio(data, dataLast));
                dataLast2 = dataLast;
            }
            list.add(po);
        }
        BigDecimal data3, dataLast3 = BigDecimal.ZERO;
        {
            // 支付金额
            params.setState(1);
            paramsLast.setState(1);
            BigDecimal data = defaultIfNull(orderAnalysisMapper.queryRechargeMoneySum(params), BigDecimal.ZERO);
            params.setPayForm(8);
            BigDecimal dataXuni = defaultIfNull(orderAnalysisMapper.queryRechargeMoneySum(params), BigDecimal.ZERO);
            params.setPayForm(null);
            data3 = data;
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(3);
            po.setData(data);
            po.setData2(dataXuni);
            if (needLast) {
                BigDecimal dataLast = defaultIfNull(orderAnalysisMapper.queryRechargeMoneySum(paramsLast), BigDecimal.ZERO);
                po.setRingRate(DoubleUtil.ringRatio(data, dataLast));
                dataLast3 = dataLast;
            }
            list.add(po);
        }
        {
            // 支付实到金额
            params.setState(1);
            paramsLast.setState(1);
            BigDecimal data = defaultIfNull(orderAnalysisMapper.queryRechargeMoneyProfitSum(params), BigDecimal.ZERO);
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(8);
            po.setData(data);
            if (needLast) {
                BigDecimal dataLast = defaultIfNull(orderAnalysisMapper.queryRechargeMoneyProfitSum(paramsLast), BigDecimal.ZERO);
                po.setRingRate(DoubleUtil.ringRatio(data, dataLast));
            }
            list.add(po);
        }
        int data4, dataLast4 = 0;
        {
            // 订单完成率：已支付成功的订单➗总订单数（含已取消、待支付状态的）
            params.setState(null);
            paramsLast.setState(null);
            int data = orderAnalysisMapper.queryRechargeOrderCount(params);
            data4 = data;
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(4);
            BigDecimal rate = DoubleUtil.divB4Zero(data2 * 100, data);
            po.setData(rate);
            if (needLast) {
                int dataLast = orderAnalysisMapper.queryRechargeOrderCount(paramsLast);
                BigDecimal rateLast = DoubleUtil.divB4Zero(dataLast2 * 100, dataLast);
                po.setRingRate(DoubleUtil.ringRatio(rate, rateLast));
                dataLast4 = dataLast;
            }
            list.add(po);
        }
        {
            // 用户支付率：已支付成功的用户数➗总的创建订单用户数
            params.setState(null);
            paramsLast.setState(null);
            int data = orderAnalysisMapper.queryRechargeMemberCount(params);
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(5);
            BigDecimal rate = DoubleUtil.divB4Zero(data1 * 100, data);
            po.setData(rate);
            if (needLast) {
                int dataLast = orderAnalysisMapper.queryRechargeMemberCount(paramsLast);
                BigDecimal rateLast = DoubleUtil.divB4Zero(dataLast1 * 100, dataLast);
                po.setRingRate(DoubleUtil.ringRatio(rate, rateLast));
            }
            list.add(po);
        }
        {
            // 人均支付金额：支付金额➗已支付成功的用户数
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(6);
            BigDecimal data = DoubleUtil.divB4Zero(data3, data1);
            po.setData(data);
            if (needLast) {
                BigDecimal rateLast = DoubleUtil.divB4Zero(dataLast3, dataLast1);
                po.setRingRate(DoubleUtil.ringRatio(data, rateLast));
            }
            list.add(po);
        }
        {
            // 人均支付次数：支付订单数➗已支付成功的用户数
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(7);
            BigDecimal data = DoubleUtil.divB4Zero(data2, data1);
            po.setData(data);
            if (needLast) {
                BigDecimal rateLast = DoubleUtil.divB4Zero(dataLast2, dataLast1);
                po.setRingRate(DoubleUtil.ringRatio(data, rateLast));
            }
            list.add(po);
        }
        return ResultVO.success(list);
    }

    /**
     * 支付趋势线条统计图
     */
    public ResultVO<?> getOrderTrendAnalysisList(OrderAnalysisPO params) {
        List<Date> dateList = DateUtil.getBetweenDayByStartEndDay(params.getPayTimeS(), params.getPayTimeE());
        List<OrderAnalysisPO> list = new ArrayList<>();
        // dateType: 1=支付人数;2=支付订单数;3=支付金额;4=订单完成率;5=用户支付率;6=人均支付金额;7=人均支付次数;8=到账金额
        switch (params.getDataType()) {
            case 1: {
                // 支付人数
                params.setState(1);
                List<OrderAnalysisPO> listT = orderAnalysisMapper.queryRechargeMemberCountDayList(params);
                Map<String, Integer> map = new HashMap<>();
                listT.forEach(cur -> map.put(cur.getDataDay(), toInteger(cur.getData(), 0)));

                for (Date date : dateList) {
                    OrderAnalysisPO po = new OrderAnalysisPO();
                    String d = DateUtil.format09(date);
                    po.setDataDay(d);
                    po.setData(defaultIfNull(map.get(d), 0));
                    list.add(po);
                }
            }
            break;
            case 2: {
                // 支付订单数
                params.setState(1);
                List<OrderAnalysisPO> listT = orderAnalysisMapper.queryRechargeOrderCountDayList(params);
                Map<String, Integer> map = new HashMap<>();
                listT.forEach(cur -> map.put(cur.getDataDay(), toInteger(cur.getData(), 0)));

                for (Date date : dateList) {
                    OrderAnalysisPO po = new OrderAnalysisPO();
                    String d = DateUtil.format09(date);
                    po.setDataDay(d);
                    po.setData(defaultIfNull(map.get(d), 0));
                    list.add(po);
                }
            }
            break;
            case 3: {
                // 支付金额
                params.setState(1);
                List<OrderAnalysisPO> listT = orderAnalysisMapper.queryRechargeMoneySumDayList(params);
                Map<String, BigDecimal> map = new HashMap<>();
                listT.forEach(cur -> map.put(cur.getDataDay(), toBigDecimal(cur.getData(), BigDecimal.ZERO)));

                for (Date date : dateList) {
                    OrderAnalysisPO po = new OrderAnalysisPO();
                    String d = DateUtil.format09(date);
                    po.setDataDay(d);
                    po.setData(defaultIfNull(map.get(d), BigDecimal.ZERO));
                    list.add(po);
                }
            }
            break;
            case 4: {
                // 订单完成率：已支付成功的订单➗总订单数（含已取消、待支付状态的）
                params.setState(1);
                List<OrderAnalysisPO> listSuc = orderAnalysisMapper.queryRechargeOrderCountDayList(params);
                Map<String, Integer> mapSuc = new HashMap<>();
                listSuc.forEach(cur -> mapSuc.put(cur.getDataDay(), toInteger(cur.getData(), 0)));
                params.setState(null);
                List<OrderAnalysisPO> listAll = orderAnalysisMapper.queryRechargeOrderCountDayList(params);
                Map<String, Integer> mapAll = new HashMap<>();
                listAll.forEach(cur -> mapAll.put(cur.getDataDay(), toInteger(cur.getData(), 0)));

                for (Date date : dateList) {
                    OrderAnalysisPO po = new OrderAnalysisPO();
                    String d = DateUtil.format09(date);
                    po.setDataDay(d);
                    po.setData(DoubleUtil.divB4Zero(defaultIfNull(mapSuc.get(d), 0) * 100, mapAll.get(d)));
                    list.add(po);
                }
            }
            break;
            case 5: {
                // 用户支付率：已支付成功的用户数➗总的创建订单用户数
                params.setState(1);
                List<OrderAnalysisPO> listSuc = orderAnalysisMapper.queryRechargeMemberCountDayList(params);
                Map<String, Integer> mapSuc = new HashMap<>();
                listSuc.forEach(cur -> mapSuc.put(cur.getDataDay(), toInteger(cur.getData(), 0)));
                params.setState(null);
                List<OrderAnalysisPO> listAll = orderAnalysisMapper.queryRechargeMemberCountDayList(params);
                Map<String, Integer> mapAll = new HashMap<>();
                listAll.forEach(cur -> mapAll.put(cur.getDataDay(), toInteger(cur.getData(), 0)));

                for (Date date : dateList) {
                    OrderAnalysisPO po = new OrderAnalysisPO();
                    String d = DateUtil.format09(date);
                    po.setDataDay(d);
                    po.setData(DoubleUtil.divB4Zero(defaultIfNull(mapSuc.get(d), 0) * 100, mapAll.get(d)));
                    list.add(po);
                }
            }
            break;
            case 6: {
                // 人均支付金额：支付金额➗已支付成功的用户数
                params.setState(1);
                List<OrderAnalysisPO> listSuc = orderAnalysisMapper.queryRechargeMemberCountDayList(params);
                Map<String, Integer> mapSuc = new HashMap<>();
                listSuc.forEach(cur -> mapSuc.put(cur.getDataDay(), toInteger(cur.getData(), 0)));
                params.setState(1);
                List<OrderAnalysisPO> listMoney = orderAnalysisMapper.queryRechargeMoneySumDayList(params);
                Map<String, BigDecimal> mapMoney = new HashMap<>();
                listMoney.forEach(cur -> mapMoney.put(cur.getDataDay(), toBigDecimal(cur.getData(), BigDecimal.ZERO)));

                for (Date date : dateList) {
                    OrderAnalysisPO po = new OrderAnalysisPO();
                    String d = DateUtil.format09(date);
                    po.setDataDay(d);
                    po.setData(DoubleUtil.divB4Zero(defaultIfNull(mapMoney.get(d), BigDecimal.ZERO), mapSuc.get(d)));
                    list.add(po);
                }
            }
            break;
            case 7: {
                // 人均支付次数：总订单数➗已支付成功的用户数
                params.setState(1);
                List<OrderAnalysisPO> listSuc = orderAnalysisMapper.queryRechargeMemberCountDayList(params);
                Map<String, Integer> mapSuc = new HashMap<>();
                listSuc.forEach(cur -> mapSuc.put(cur.getDataDay(), toInteger(cur.getData(), 0)));
                params.setState(1);
                List<OrderAnalysisPO> listAll = orderAnalysisMapper.queryRechargeOrderCountDayList(params);
                Map<String, Integer> mapAll = new HashMap<>();
                listAll.forEach(cur -> mapAll.put(cur.getDataDay(), toInteger(cur.getData(), 0)));

                for (Date date : dateList) {
                    OrderAnalysisPO po = new OrderAnalysisPO();
                    String d = DateUtil.format09(date);
                    po.setDataDay(d);
                    po.setData(DoubleUtil.divB4Zero(defaultIfNull(mapAll.get(d), 0), mapSuc.get(d)));
                    list.add(po);
                }
            }
            break;
            case 8: {
                // 实际到账金额
                params.setState(1);
                List<OrderAnalysisPO> listT = orderAnalysisMapper.queryRechargeMoneyProfitSumDayList(params);
                Map<String, BigDecimal> map = new HashMap<>();
                listT.forEach(cur -> map.put(cur.getDataDay(), toBigDecimal(cur.getData(), BigDecimal.ZERO)));

                for (Date date : dateList) {
                    OrderAnalysisPO po = new OrderAnalysisPO();
                    String d = DateUtil.format09(date);
                    po.setDataDay(d);
                    po.setData(defaultIfNull(map.get(d), BigDecimal.ZERO));
                    list.add(po);
                }
            }
            break;
        }
        return ResultVO.success(list);
    }

    /**
     * 支付类型占比
     */
    public ResultVO<?> getOrderTypeAnalysisList(OrderAnalysisPO params) {
        List<OrderAnalysisPO> list = new ArrayList<>();
        // dateType: 1=ios支付人数占比;2=安卓支付人数占比;3=VIP充值人数占比;4=K币充值人数占比
        {
            // 安卓支付人数
            params.setState(1);
            params.setPhoneOs(1);
            int data1 = orderAnalysisMapper.queryRechargeMemberCount(params);

            // ios支付人数
            params.setState(1);
            params.setPhoneOs(2);
            int data2 = orderAnalysisMapper.queryRechargeMemberCount(params);

            int data = data1 + data2;

            if (data == 0) {
                OrderAnalysisPO po1 = new OrderAnalysisPO();
                po1.setDataType(1);
                po1.setData(0);
                list.add(po1);

                OrderAnalysisPO po2 = new OrderAnalysisPO();
                po2.setDataType(2);
                po2.setData(0);
                list.add(po2);
            } else {
                OrderAnalysisPO po1 = new OrderAnalysisPO();
                po1.setDataType(1);
                BigDecimal rate = DoubleUtil.divB4Zero(data2 * 100, data);
                po1.setData(rate);
                list.add(po1);

                OrderAnalysisPO po2 = new OrderAnalysisPO();
                po2.setDataType(2);
                po2.setData(DoubleUtil.subB(new BigDecimal(100), rate));
                list.add(po2);
            }
        }
        {
            params.setPhoneOs(null);
            // K币充值订单占比
            params.setState(1);
            params.setOrderType(1);
            int data1 = orderAnalysisMapper.queryRechargeOrderCount(params);

            // VIP充值订单占比
            params.setState(1);
            params.setOrderType(null);
            params.setOrderTypes("2,4"); // 剧卡和vip都算做是vip充值
            int data2 = orderAnalysisMapper.queryRechargeOrderCount(params);

            int data = data1 + data2;

            if (data == 0) {
                OrderAnalysisPO po3 = new OrderAnalysisPO();
                po3.setDataType(3);// 3=VIP充值人数占比
                po3.setData(0);
                list.add(po3);

                OrderAnalysisPO po4 = new OrderAnalysisPO();
                po4.setDataType(4);// 4=K币充值人数占比
                po4.setData(0);
                list.add(po4);
            } else {
                OrderAnalysisPO po3 = new OrderAnalysisPO();
                po3.setDataType(3);// 3=VIP充值人数占比
                BigDecimal rate = DoubleUtil.divB4Zero(data2 * 100, data);
                po3.setData(rate);
                list.add(po3);

                OrderAnalysisPO po4 = new OrderAnalysisPO();
                po4.setDataType(4);// 4=K币充值人数占比
                po4.setData(DoubleUtil.subB(new BigDecimal(100), rate));
                list.add(po4);
            }
        }
        ResultVO<List<OrderAnalysisPO>> resultVO = ResultVO.success(list);
        resultVO.setSummary(orderAnalysisMapper.queryRechargePhoneBrandCount(params));
        return resultVO;
    }

    /**
     * 支付时间趋势
     */
    public ResultVO<List<OrderAnalysisPO>> getOrderTimeAnalysisList(OrderAnalysisPO params) {
        List<OrderAnalysisPO> list = orderAnalysisMapper.queryRechargeTimeCount(params);
        return ResultVO.success(list == null ? new ArrayList<>() : list);
    }

    /**
     * 注册支付率
     */
    public List<OrderAnalysisPO> getRegisterPayRateAnalysisList(OrderAnalysisPO params) {
        // 查询每天的注册人数
        List<OrderAnalysisPO> memberCountList = orderAnalysisMapper.queryMemberCountDayList(params);
        params.setRechargeRateHour(params.getDataType());
        List<OrderAnalysisPO> payCountList = orderAnalysisMapper.queryMemberPayCountDayList(params);
        Map<String, Integer> payCountMap = new HashMap<>();
        payCountList.forEach(cur -> payCountMap.put(cur.getDataDay(), cur.getRechargeMemberCount()));

        for (OrderAnalysisPO data : memberCountList) {
            data.setRegisterCount(data.getRegisterCount());
            data.setRechargeMemberCount(defaultIfNull(payCountMap.get(data.getDataDay()), 0));
            data.setRechargeRate(DoubleUtil.divB4Zero(data.getRechargeMemberCount() * 100, data.getRegisterCount()));
        }
        return memberCountList;
    }

    /**
     * 注册支付率
     */
    public ResultVO<?> getRegisterPayRateAnalysisList_back(OrderAnalysisPO params) {
        // 查询每天的注册人数
        List<OrderAnalysisPO> memberCountList = orderAnalysisMapper.queryMemberCountDayList(params);
        params.setRechargeRateHour(1);
        List<OrderAnalysisPO> payCountList = orderAnalysisMapper.queryMemberPayCountDayList(params);
        Map<String, Integer> payCountMap = new HashMap<>();
        payCountList.forEach(cur -> payCountMap.put(cur.getDataDay(), cur.getRechargeMemberCount()));

        params.setRechargeRateHour(24);
        List<OrderAnalysisPO> payCountList24 = orderAnalysisMapper.queryMemberPayCountDayList(params);
        Map<String, Integer> payCountMap24 = new HashMap<>();
        payCountList24.forEach(cur -> payCountMap24.put(cur.getDataDay(), cur.getRechargeMemberCount()));

        params.setRechargeRateHour(48);
        List<OrderAnalysisPO> payCountList48 = orderAnalysisMapper.queryMemberPayCountDayList(params);
        Map<String, Integer> payCountMap48 = new HashMap<>();
        payCountList48.forEach(cur -> payCountMap48.put(cur.getDataDay(), cur.getRechargeMemberCount()));

        for (OrderAnalysisPO data : memberCountList) {
            List<OrderAnalysisPO> list = new ArrayList<>();
            OrderAnalysisPO po1 = new OrderAnalysisPO();
            po1.setDataType(1);
            po1.setRegisterCount(data.getRegisterCount());
            po1.setRechargeMemberCount(defaultIfNull(payCountMap.get(data.getDataDay()), 0));
            po1.setRechargeRate(DoubleUtil.divB4Zero(po1.getRechargeMemberCount(), po1.getRegisterCount()));

            OrderAnalysisPO po24 = new OrderAnalysisPO();
            po1.setDataType(2);
            po24.setRegisterCount(data.getRegisterCount());
            po24.setRechargeMemberCount(defaultIfNull(payCountMap24.get(data.getDataDay()), 0));
            po24.setRechargeRate(DoubleUtil.divB4Zero(po24.getRechargeMemberCount(), po24.getRegisterCount()));

            OrderAnalysisPO po48 = new OrderAnalysisPO();
            po1.setDataType(3);
            po48.setRegisterCount(data.getRegisterCount());
            po48.setRechargeMemberCount(defaultIfNull(payCountMap48.get(data.getDataDay()), 0));
            po48.setRechargeRate(DoubleUtil.divB4Zero(po48.getRechargeMemberCount(), po48.getRegisterCount()));

            list.add(po1);
            list.add(po24);
            list.add(po48);

            data.setList(list);
        }
        return ResultVO.success(memberCountList);
    }

    /**
     * 充值模版分析
     */
    public ResultVO<?> getPayTemplateAnalysisList(OrderAnalysisPO params) {
        // dateType: 1=K币充值档位;2=VIP充值档位
        params.setOrderType(params.getDataType());
        List<OrderAnalysisPO> list = orderAnalysisMapper.queryRechargeMoneyGroupList(params);
        list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeOrderCount).thenComparing(OrderAnalysisPO::getRechargeMoney).reversed());
        List<OrderAnalysisPO> list2 = new ArrayList<>();
        int rechargeOrderCount = 0;// 其他 充值人数
        for (int i = 0; i < list.size(); i++) {
            OrderAnalysisPO po = list.get(i);
            po.setName("¥" + po.getRechargeMoney());
            if (i > 7) {
                rechargeOrderCount = rechargeOrderCount + po.getRechargeOrderCount();
            } else {
                list2.add(po);
            }
        }
        if (rechargeOrderCount > 0) {
            OrderAnalysisPO po9 = new OrderAnalysisPO();
            po9.setRechargeOrderCount(rechargeOrderCount);
            po9.setName("其他");
            list2.add(po9);
        }

        return ResultVO.success(list2);
    }

    // 最大统计到5充的数据
    private static final int RECHARGE_COUNT_MAX = 5;

    /**
     * 累充分析-复充用户
     */
    public ResultVO<?> getCumulativeRechargeMemberAnalysisList(OrderAnalysisPO params) {
        int moneyType = params.getMoneySearchType() == null ? 0 : params.getMoneySearchType();
        List<OrderAnalysisPO> result = new ArrayList<>();
        List<OrderAnalysisPO> list = orderAnalysisMapper.queryRechargeCountMemberList(params);
        // 统计N充人数(共统计到5充)
        int[] rechargeCount = new int[RECHARGE_COUNT_MAX];
        BigDecimal[] rechargeMoney = new BigDecimal[RECHARGE_COUNT_MAX];
        for (OrderAnalysisPO po : list) {
            if (po.getRechargeCount() > RECHARGE_COUNT_MAX) {
                po.setRechargeCount(RECHARGE_COUNT_MAX);// 大于5充的全部算5充
            }
            List<BigDecimal> moneyAll = CollUtil.parseBigDecimalStr2List(po.getRechargeMoneyStr());
            // 注意: 以下除`default`外都不能带break
            switch (po.getRechargeCount()) {
                case 5:
                    extracted(moneyType, rechargeCount, moneyAll, 4);
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 4);
                case 4:
                    extracted(moneyType, rechargeCount, moneyAll, 3);
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 3);
                case 3:
                    extracted(moneyType, rechargeCount, moneyAll, 2);
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 2);
                case 2:
                    extracted(moneyType, rechargeCount, moneyAll, 1);
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 1);
                case 1:
                    extracted(moneyType, rechargeCount, moneyAll, 0);
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 0);
                default:
                    break;
            }
        }
        for (int i = 0; i < RECHARGE_COUNT_MAX; i++) {
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setRechargeCount(i + 1);
            po.setRechargeMemberCount(rechargeCount[i]);
            // 计算每N充的平均充值金额
            po.setRechargeMoneySum(defaultIfNull(rechargeMoney[i], BigDecimal.ZERO));
            po.setRechargeMoneyAvg(DoubleUtil.divB4Zero(po.getRechargeMoneySum(), rechargeCount[i]));
            result.add(po);
        }
        return ResultVO.success(result);
    }

    /**
     * 累加每个N充的金额(所有1充的累加一起,2充的累加一起...)
     *
     * @param rechargeMoney
     * @param moneyAll
     * @param n
     */
    private static void sumAddRechargeMoney(final BigDecimal[] rechargeMoney, final List<BigDecimal> moneyAll, int n) {
        if (n < 4) {
            rechargeMoney[n] = DoubleUtil.addB(defaultIfNull(rechargeMoney[n], BigDecimal.ZERO), moneyAll.get(n));
        } else {
            rechargeMoney[n] = DoubleUtil.addB(defaultIfNull(rechargeMoney[n], BigDecimal.ZERO), moneyAll.subList(n, moneyAll.size()));
        }
    }

    private static void extracted(final int moneyType, final int[] rechargeCount, final List<BigDecimal> moneyAll, final int i) {
        switch (moneyType) {
            case 1:
                if (moneyAll.get(i).compareTo(BigDecimalVar.BD_20) < 0) {
                    rechargeCount[i]++;
                }
                break;
            case 2:
                if (moneyAll.get(i).compareTo(BigDecimalVar.BD_20) >= 0) {
                    rechargeCount[i]++;
                }
                break;
            default:
                rechargeCount[i]++;
                break;
        }
    }

    // 定义全局变量,避免频繁创建对象
    private static final BigDecimal BigDecimal_300 = new BigDecimal("300");
    private static final BigDecimal BigDecimal_200 = new BigDecimal("200");
    private static final BigDecimal BigDecimal_100 = new BigDecimal("100");
    private static final BigDecimal BigDecimal_30 = new BigDecimal("30");

    /**
     * 累充分析-充值金额
     */
    public ResultVO<?> getCumulativeRechargeMoneyAnalysisList(OrderAnalysisPO params) {
        List<OrderAnalysisPO> result = new ArrayList<>();
        List<OrderAnalysisPO> list = orderAnalysisMapper.queryRechargeCountMoneyList(params);
        // 统计N充人数(共统计到5充)
        int[] rechargeCount = new int[RECHARGE_COUNT_MAX];
        for (OrderAnalysisPO po : list) {
            if (po.getRechargeMoney().compareTo(BigDecimal_300) > 0) {
                rechargeCount[4]++;
            } else if (po.getRechargeMoney().compareTo(BigDecimal_200) > 0) {
                rechargeCount[3]++;
            } else if (po.getRechargeMoney().compareTo(BigDecimal_100) > 0) {
                rechargeCount[2]++;
            } else if (po.getRechargeMoney().compareTo(BigDecimal_30) > 0) {
                rechargeCount[1]++;
            } else {
                rechargeCount[0]++;
            }
        }
        for (int i = 0; i < RECHARGE_COUNT_MAX; i++) {
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setRechargeCount(i + 1);
            po.setRechargeMemberCount(rechargeCount[i]);
            result.add(po);
        }
        return ResultVO.success(result);
    }

    /**
     * 回传分析-顶部汇总
     */
    public ResultVO<?> getBackSumAnalysisList(OrderAnalysisPO params, OrderAnalysisPO paramsLast) {
        boolean needLast = notBlank(params.getPayTimeStr());
        // dateType: 1=总订单数;2=回传成功数;3=回传失败数;4=手动回传成功数;5=回传率
        List<OrderAnalysisPO> list = new ArrayList<>();
        int data1, dataLast1 = 0;
        {
            // 总订单数
            params.setState(1);
            int data = orderAnalysisMapper.queryRechargeOrderCount(params);
            data1 = data;
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(1);
            po.setData(data);
            if (needLast) {
                paramsLast.setState(1);
                int dataLast = orderAnalysisMapper.queryRechargeOrderCount(paramsLast);
                po.setRingRate(DoubleUtil.ringRatio(data, dataLast));
                dataLast1 = dataLast;
            }
            list.add(po);
        }
        int data2, dataLast2 = 0;
        {
            // 回传成功数
            params.setState(1);
            params.setBackState(2);
            int data = orderAnalysisMapper.queryRechargeOrderCount(params);
            data2 = data;
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(2);
            po.setData(data);
            if (needLast) {
                paramsLast.setState(1);
                paramsLast.setBackState(2);
                int dataLast = orderAnalysisMapper.queryRechargeOrderCount(paramsLast);
                po.setRingRate(DoubleUtil.ringRatio(data, dataLast));
                dataLast2 = dataLast;
            }
            list.add(po);
        }
        {
            // 回传失败数
            params.setState(1);
            params.setBackState(3);
            int data = orderAnalysisMapper.queryRechargeOrderCount(params);
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(3);
            po.setData(data);
            if (needLast) {
                paramsLast.setState(1);
                paramsLast.setBackState(3);
                int dataLast = orderAnalysisMapper.queryRechargeOrderCount(paramsLast);
                po.setRingRate(DoubleUtil.ringRatio(data, dataLast));
            }
            list.add(po);
        }
        {
            // 手动回传成功数
            params.setState(1);
            params.setBackState(2);
            params.setBackAuto(2);
            int data = orderAnalysisMapper.queryRechargeOrderCount(params);
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(4);
            po.setData(data);
            if (needLast) {
                paramsLast.setState(1);
                paramsLast.setBackState(2);
                paramsLast.setBackAuto(2);
                int dataLast = orderAnalysisMapper.queryRechargeOrderCount(paramsLast);
                po.setRingRate(DoubleUtil.ringRatio(data, dataLast));
            }
            list.add(po);
        }
        {
            // 回传率：回传成功数➗总订单数
            BigDecimal data = DoubleUtil.divB4Zero(data2 * 100, data1);
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(5);
            po.setData(data);
            if (needLast) {
                BigDecimal dataLast = DoubleUtil.divB4Zero(dataLast2 * 100, dataLast1);
                po.setRingRate(DoubleUtil.ringRatio(data, dataLast));
            }
            list.add(po);
        }
        return ResultVO.success(list);
    }

    /**
     * 回传分析-失败占比
     */
    public ResultVO<?> getBackFailReasonAnalysisList(OrderAnalysisPO params) {
        List<OrderAnalysisPO> list = orderAnalysisMapper.queryBackFailReasonList(params);
        list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeCount).thenComparing(OrderAnalysisPO::getBackType).reversed());
        return ResultVO.success(list);
    }

    /**
     * 统计分析汇总
     */
    public ResultVO<?> getStatisticsSumList(OrderAnalysisPO params) {
        Date nowStart = DateUtil.beginOfDay();
        Date nowEnd = DateUtil.endOfDay();

        Date yesStart = DateUtil.addDays(nowStart, -1);
        Date yesEnd = DateUtil.addDays(nowEnd, -1);

        Date monthStart = DateUtil.beginOfMonth();
        // Date monthEnd = DateUtil.getMonthEndDate();

        List<OrderAnalysisPO> list = new ArrayList<>();
        // dataType:1=今日;2=昨日;3=本月
        {
            params.setPayTimeS(nowStart);
            params.setPayTimeE(nowEnd);
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(1);
            getStatisticsSum(params, po);

            list.add(po);
        }
        {
            params.setPayTimeS(yesStart);
            params.setPayTimeE(yesEnd);
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(2);
            getStatisticsSum(params, po);

            list.add(po);
        }
        {
            params.setPayTimeS(monthStart);
            params.setPayTimeE(nowEnd);
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setDataType(3);
            getStatisticsSum(params, po);

            list.add(po);
        }

        return ResultVO.success(list);
    }

    /**
     * 统计分析汇总
     *
     * @param params
     * @param po
     */
    private void getStatisticsSum(OrderAnalysisPO params, OrderAnalysisPO po) {
        params.setOrderType(1);
        po.setRechargeCoinMoneySum(orderAnalysisMapper.queryRechargeMoneySum(params));// 充值K币金额
        po.setRechargeCoinMoneyProfitSum(orderAnalysisMapper.queryRechargeMoneyProfitSum(params));// 充值K币金额

        params.setOrderType(2);
        po.setRechargeVipMoneySum(orderAnalysisMapper.queryRechargeMoneySum(params));// 充值VIP金额
        po.setRechargeVipMoneyProfitSum(orderAnalysisMapper.queryRechargeMoneyProfitSum(params));// 充值VIP金额

        po.setRechargeMoneySum(DoubleUtil.addB(po.getRechargeCoinMoneySum(), po.getRechargeVipMoneySum()));// 总充值金额
        po.setRechargeMoneyProfitSum(DoubleUtil.addB(po.getRechargeCoinMoneyProfitSum(), po.getRechargeVipMoneyProfitSum()));// 总充值金额

        params.setOrderType(null);
        po.setRechargeMoneyAddSum(orderAnalysisMapper.queryRechargeMoneyAddSum(params));// 新增用户充值金额
        po.setRechargeMoneyProfitAddSum(orderAnalysisMapper.queryRechargeMoneyProfitAddSum(params));// 新增用户充值金额
        po.setRechargeMoneyNewSum(orderAnalysisMapper.queryRechargeMoneyNewSum(params));// 注册用户充值金额
        po.setRechargeMoneyProfitNewSum(orderAnalysisMapper.queryRechargeMoneyProfitNewSum(params));// 注册用户充值金额
        po.setRechargeMemberCount(orderAnalysisMapper.queryRechargeFirstMemberCount(params));// 首充用户数
    }

    /**
     * 统计分析-按维度
     */
    public List<OrderAnalysisPO> getStatisticsByDateList(OrderAnalysisPO params, OrderAnalysisPO summary) {
        Set<Integer> ids = new HashSet<>();
        // dataType:维度 1=按月;2=按天;3=按分销商;4=按优化师;5=按短剧
        List<OrderAnalysisPO> list = orderAnalysisMapper.queryStatisticsOrderSumGroupList(params);
        if (CollUtil.isEmpty(list)) {
            return list;
        }

        for (OrderAnalysisPO po : list) {
            po.setRechargeMoneySum(DoubleUtil.addB(po.getRechargeCoinMoneySum(), po.getRechargeVipMoneySum(), po.getRechargeDramaCardMoneySum()));// 总充值金额
            po.setRechargeMoneyProfitSum(DoubleUtil.addB(po.getRechargeCoinMoneyProfitSum(), po.getRechargeVipMoneyProfitSum(), po.getRechargeDramaCardMoneyProfitSum()));// 总充值金额
            po.setBackRate(DoubleUtil.divB4Zero(po.getBackSuccessCount() * 100, po.getRechargeOrderCount()));
            po.setRechargeCoinMoneyPer(DoubleUtil.divB4Zero(po.getRechargeCoinMoneySum(), po.getRechargeCoinMemberCount()));
            po.setRechargeCoinMoneyProfitPer(DoubleUtil.divB4Zero(po.getRechargeCoinMoneyProfitSum(), po.getRechargeCoinMemberCount()));
            po.setRechargeVipMoneyPer(DoubleUtil.divB4Zero(po.getRechargeVipMoneySum(), po.getRechargeVipMemberCount()));
            po.setRechargeVipMoneyProfitPer(DoubleUtil.divB4Zero(po.getRechargeVipMoneyProfitSum(), po.getRechargeVipMemberCount()));
            if (params.getDataType() >= 3) {
                ids.add(toInteger(po.getDataDay()));
            }

            if (summary != null) {
                summary.setRechargeMoneySum(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneySum(), BigDecimal.ZERO), po.getRechargeMoneySum()));
                summary.setRechargeMoneyProfitSum(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneyProfitSum(), BigDecimal.ZERO), po.getRechargeMoneyProfitSum()));
                summary.setBackSuccessCount(defaultIfNull(summary.getBackSuccessCount(), 0) + po.getBackSuccessCount());
                // summary.setRechargeMemberCount(defaultIfNull(summary.getRechargeMemberCount(), 0) + po.getRechargeMemberCount());
                summary.setRechargeOrderCount(defaultIfNull(summary.getRechargeOrderCount(), 0) + po.getRechargeOrderCount());
                summary.setRechargeMoneyAddSum(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneyAddSum(), BigDecimal.ZERO), po.getRechargeMoneyAddSum()));
                summary.setRechargeMoneyProfitAddSum(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneyProfitAddSum(), BigDecimal.ZERO), po.getRechargeMoneyProfitAddSum()));
                summary.setRechargeMemberCountAdd(defaultIfNull(summary.getRechargeMemberCountAdd(), 0) + po.getRechargeMemberCountAdd());
                summary.setRechargeMoneyNewSum(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneyNewSum(), BigDecimal.ZERO), po.getRechargeMoneyNewSum()));
                summary.setRechargeMoneyProfitNewSum(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneyProfitNewSum(), BigDecimal.ZERO), po.getRechargeMoneyProfitNewSum()));
                summary.setRechargeMemberCountNew(defaultIfNull(summary.getRechargeMemberCountNew(), 0) + po.getRechargeMemberCountNew());
                summary.setRechargeMoneyColorSum(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneyColorSum(), BigDecimal.ZERO), po.getRechargeMoneyColorSum()));
                summary.setRechargeMoneyProfitColorSum(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneyProfitColorSum(), BigDecimal.ZERO), po.getRechargeMoneyProfitColorSum()));
                summary.setRechargeMemberCountColor(defaultIfNull(summary.getRechargeMemberCountColor(), 0) + po.getRechargeMemberCountColor());
                summary.setRechargeCoinMoneySum(DoubleUtil.addB(defaultIfNull(summary.getRechargeCoinMoneySum(), BigDecimal.ZERO), po.getRechargeCoinMoneySum()));
                summary.setRechargeCoinMoneyProfitSum(DoubleUtil.addB(defaultIfNull(summary.getRechargeCoinMoneyProfitSum(), BigDecimal.ZERO), po.getRechargeCoinMoneyProfitSum()));
                summary.setRechargeCoinMemberCount(defaultIfNull(summary.getRechargeCoinMemberCount(), 0) + po.getRechargeCoinMemberCount());
                summary.setRechargeVipMoneySum(DoubleUtil.addB(defaultIfNull(summary.getRechargeVipMoneySum(), BigDecimal.ZERO), po.getRechargeVipMoneySum()));
                summary.setRechargeVipMoneyProfitSum(DoubleUtil.addB(defaultIfNull(summary.getRechargeVipMoneyProfitSum(), BigDecimal.ZERO), po.getRechargeVipMoneyProfitSum()));
                summary.setRechargeVipMemberCount(defaultIfNull(summary.getRechargeVipMemberCount(), 0) + po.getRechargeVipMemberCount());
            }
        }

        if (summary != null) {
            params.setState(1);
            summary.setRechargeMemberCount(orderAnalysisMapper.queryRechargeMemberCount(params));
            summary.setBackRate(DoubleUtil.divB4Zero(summary.getBackSuccessCount() * 100, summary.getRechargeOrderCount()));
            summary.setRechargeCoinMoneyPer(DoubleUtil.divB4Zero(summary.getRechargeCoinMoneySum(), summary.getRechargeCoinMemberCount()));
            summary.setRechargeCoinMoneyProfitPer(DoubleUtil.divB4Zero(summary.getRechargeCoinMoneyProfitSum(), summary.getRechargeCoinMemberCount()));
            summary.setRechargeVipMoneyPer(DoubleUtil.divB4Zero(summary.getRechargeVipMoneySum(), summary.getRechargeVipMemberCount()));
            summary.setRechargeVipMoneyProfitPer(DoubleUtil.divB4Zero(summary.getRechargeVipMoneyProfitSum(), summary.getRechargeVipMemberCount()));
        }

        Map<Integer, String> nameMap = new HashMap<>();
        switch (params.getDataType()) {
            case 3: {
                FastRetailPO query = new FastRetailPO();
                query.setIds(StrUtil.join(ids));
                nameMap = retailService.queryRetailNameMap(query);
                break;
            }
            case 4: {
                FastUserPO query = new FastUserPO();
                query.setIds(StrUtil.join(ids));
                nameMap = userService.queryUserNameMap(query);
                break;
            }
            case 5: {
                FastDramaPO query = new FastDramaPO();
                query.setIds(StrUtil.join(ids));
                nameMap = dramaService.queryDramaNameMap(query);
                break;
            }
        }
        // 翻译3,4,5维度的字段名称
        if (params.getDataType() >= 3) {
            for (OrderAnalysisPO po : list) {
                po.setDataDay(nameMap.get(toInteger(po.getDataDay())));
            }
        }

        // 排序
        sortData(params, list);
        return list;
    }

    /**
     * 排序
     *
     * @param params
     * @param list
     */
    private static void sortData(OrderAnalysisPO params, List<OrderAnalysisPO> list) {
        switch (params.getSortType()) {
            case 0:// 日期
                if (params.getDataType() < 3) {
                    // 正序排序(由小->大)
                    list.sort(Comparator.comparing(OrderAnalysisPO::getDataDay));
                }
                break;
            case 1:// 充值总金额
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMoneySum).reversed().thenComparing(OrderAnalysisPO::getDataDay));
                } else {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMoneySum).thenComparing(OrderAnalysisPO::getDataDay));
                }
                break;
            case 2:// 充值总人数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMemberCount).reversed().thenComparing(OrderAnalysisPO::getDataDay));
                } else {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMemberCount).thenComparing(OrderAnalysisPO::getDataDay));
                }
                break;
            case 3:// 订单总数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeOrderCount).reversed().thenComparing(OrderAnalysisPO::getDataDay));
                } else {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeOrderCount).thenComparing(OrderAnalysisPO::getDataDay));
                }
                break;
            case 4:// 新增用户充值金额
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMoneyAddSum).reversed().thenComparing(OrderAnalysisPO::getDataDay));
                } else {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMoneyAddSum).thenComparing(OrderAnalysisPO::getDataDay));
                }
                break;
            case 5:// 注册用户充值金额
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMoneyNewSum).reversed().thenComparing(OrderAnalysisPO::getDataDay));
                } else {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMoneyNewSum).thenComparing(OrderAnalysisPO::getDataDay));
                }
                break;
            case 6:// 染色用户充值金额
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMoneyColorSum).reversed().thenComparing(OrderAnalysisPO::getDataDay));
                } else {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeMoneyColorSum).thenComparing(OrderAnalysisPO::getDataDay));
                }
                break;
            case 7:// K币充值数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeCoinMoneySum).reversed().thenComparing(OrderAnalysisPO::getDataDay));
                } else {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeCoinMoneySum).thenComparing(OrderAnalysisPO::getDataDay));
                }
                break;
            case 8:// 充值VIP金额
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeVipMoneySum).reversed().thenComparing(OrderAnalysisPO::getDataDay));
                } else {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getRechargeVipMoneySum).thenComparing(OrderAnalysisPO::getDataDay));
                }
                break;
            case 9:// 回传率
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getBackRate).reversed().thenComparing(OrderAnalysisPO::getDataDay));
                } else {
                    list.sort(Comparator.comparing(OrderAnalysisPO::getBackRate).thenComparing(OrderAnalysisPO::getDataDay));
                }
                break;
        }
    }

    /**
     * 注册支付率-分销商
     */
    public List<JSONObject> getRetailRegisterPayRateAnalysisList(OrderAnalysisPO params, List<OrderAnalysisPO> paramsList) {
        List<JSONObject> result = new ArrayList<>();

        Map<Integer, String> nameMap;
        Set<Integer> retailSet = new HashSet<>();
        for (OrderAnalysisPO po : paramsList) {
            retailSet.add(po.getRetailId());
        }
        // 查询分销商
        FastRetailPO retailQuery = new FastRetailPO();
        retailQuery.setIds(StrUtil.join(retailSet));
        nameMap = retailService.queryRetailNameMap(retailQuery);

        for (OrderAnalysisPO po : paramsList) {
            Date start = DateUtil.format09(po.getReleaseDateStr());
            po.setRegisterTimeS(start);
            po.setRegisterTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
            List<OrderAnalysisPO> resultList = getRegisterPayRateAnalysisList(po);

            // 日期自动补全
            HashMap<String, OrderAnalysisPO> dataMap = new HashMap<>();
            resultList.forEach(cur -> dataMap.put(cur.getDataDay(), cur));
            List<OrderAnalysisPO> list = new ArrayList<>();
            Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
            // 组装数据(从上线开始的每天)
            for (int i = 1; i <= params.getContrastDataDay(); i++) {
                String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
                OrderAnalysisPO data = dataMap.get(dataDay);

                if (data == null) {
                    data = new OrderAnalysisPO();
                    data.setRegisterCount(0);
                    data.setRechargeMemberCount(0);
                    data.setRechargeRate(BigDecimal.ZERO);
                }
                data.setDataDay(dataDay);

                list.add(data);
            }

            JSONObject data = new JSONObject();
            data.put("retailId", po.getRetailId());
            data.put("retailName", nameMap.get(po.getRetailId()));
            data.put("list", defaultIfNull(list, new ArrayList<>()));

            // 计算统计汇总数据
            data.put("dataSum", po.getData());

            result.add(data);
        }
        return result;
    }

    /**
     * 订单数据对比
     */
    public List<JSONObject> getOrderContrastAnalysis(OrderAnalysisPO params, List<OrderAnalysisPO> paramsList) {
        // 对比数据维度 1=支付人数;2=支付订单数;3=支付金额;4=订单完成率;5=用户支付率,6=人均支付金额;7=人均支付次数
        List<JSONObject> result = new ArrayList<>();

        Map<Integer, String> nameMap = new HashMap<>();
        if (params.getFirstType() == 0) {
            Set<Integer> dramaSet = new HashSet<>();
            for (OrderAnalysisPO po : paramsList) {
                dramaSet.add(po.getDramaId());
            }
            // 查询短剧
            FastDramaPO dramaQuery = new FastDramaPO();
            dramaQuery.setIds(StrUtil.join(dramaSet));
            nameMap = dramaService.queryDramaNameMap(dramaQuery);
        } else if (params.getFirstType() == 1) {
            Set<Integer> retailSet = new HashSet<>();
            for (OrderAnalysisPO po : paramsList) {
                retailSet.add(po.getRetailId());
            }
            // 查询分销商
            FastRetailPO retailQuery = new FastRetailPO();
            retailQuery.setIds(StrUtil.join(retailSet));
            nameMap = retailService.queryRetailNameMap(retailQuery);
        }
        for (OrderAnalysisPO po : paramsList) {
            List<OrderAnalysisPO> list;
            switch (params.getContrastDataType()) {
                case 1:// 支付人数
                    params.setState(1);
                    list = getRechargeMemberCount(params, po);
                    break;
                case 2:// 支付订单数
                    params.setState(1);
                    list = getRechargeOrderCount(params, po);
                    break;
                case 3:// 支付金额
                    params.setState(1);
                    list = getRechargeMoneySum(params, po);
                    break;
                case 4:// 订单完成率
                    params.setState(null);
                    list = getRechargeOrderRate(params, po);
                    break;
                case 5:// 用户支付率
                    params.setState(null);
                    list = getRechargePayRate(params, po);
                    break;
                case 6:// 人均支付金额
                    params.setState(null);
                    list = getRechargeMoneyPer(params, po);
                    break;
                case 7:// 人均支付次数
                    params.setState(null);
                    list = getRechargeMemberPer(params, po);
                    break;
                default:
                    throw new MyException("对比的数据维度不存在");
            }

            if (params.getFirstType() == 0) {
                JSONObject data = new JSONObject();
                data.put("dramaId", po.getDramaId());
                data.put("dramaName", nameMap.get(po.getDramaId()));
                data.put("list", defaultIfNull(list, new ArrayList<>()));

                // 计算统计汇总数据
                data.put("dataSum", po.getData());

                result.add(data);
            } else if (params.getFirstType() == 1) {
                JSONObject data = new JSONObject();
                data.put("retailId", po.getRetailId());
                data.put("retailName", nameMap.get(po.getRetailId()));
                data.put("list", defaultIfNull(list, new ArrayList<>()));

                // 计算统计汇总数据
                data.put("dataSum", po.getData());

                result.add(data);
            }
        }
        return result;
    }

    /**
     * 观看支付率对比
     */
    public List<JSONObject> getWatchPayRateContrastAnalysis(OrderAnalysisPO params, List<OrderAnalysisPO> paramsList) {
        List<JSONObject> result = new ArrayList<>();
        Set<Integer> dramaSet = new HashSet<>();
        for (OrderAnalysisPO po : paramsList) {
            dramaSet.add(po.getDramaId());
        }
        // 查询短剧
        FastDramaPO dramaQuery = new FastDramaPO();
        dramaQuery.setIds(StrUtil.join(dramaSet));
        Map<Integer, String> nameMap = dramaService.queryDramaNameMap(dramaQuery);
        for (OrderAnalysisPO po : paramsList) {
            List<OrderAnalysisPO> list = getWatchPayRateContrastAnalysisList(params, po);

            JSONObject data = new JSONObject();
            data.put("dramaId", po.getDramaId());
            data.put("dramaName", nameMap.get(po.getDramaId()));
            data.put("list", defaultIfNull(list, new ArrayList<>()));

            result.add(data);
        }
        return result;
    }

    /**
     * 观看支付率-对比
     */
    public List<OrderAnalysisPO> getWatchPayRateContrastAnalysisList(OrderAnalysisPO params, OrderAnalysisPO po) {
        List<OrderAnalysisPO> list = new ArrayList<>();
        // 查询每天的注册人数
        Date start = DateUtil.format09(po.getReleaseDateStr());
        po.setCreateTimeS(start);
        po.setCreateTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        List<OrderAnalysisPO> memberCountList = orderAnalysisMapper.queryFirstWatchMemberCountDayList(po);

        HashMap<String, Integer> dataMap = new HashMap<>();
        memberCountList.forEach(cur -> dataMap.put(cur.getDataDay(), toInteger(cur.getWatchCount(), 0)));

        po.setRechargeRateHour(params.getDataType());
        List<OrderAnalysisPO> payCountList = orderAnalysisMapper.queryFirstWatchMemberPayCountDayList(po);
        Map<String, Integer> payCountMap = new HashMap<>();
        payCountList.forEach(cur -> payCountMap.put(cur.getDataDay(), cur.getRechargeMemberCount()));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            Integer data = defaultIfNull(dataMap.get(dataDay), StaticVar.ZERO);
            Integer data2 = defaultIfNull(payCountMap.get(dataDay), 0);

            OrderAnalysisPO r = new OrderAnalysisPO();
            r.setDay(i);
            r.setDataDay(dataDay);
            r.setWatchCount(data);
            r.setRechargeMemberCount(data2);
            r.setRechargeRate(DoubleUtil.divB4Zero(data2 * 100, data));
            r.setData(data);

            list.add(r);
        }
        return list;
    }

    /**
     * 支付人数
     *
     * @param params
     * @param po
     * @return
     */
    private List<OrderAnalysisPO> getRechargeMemberCount(OrderAnalysisPO params, OrderAnalysisPO po) {
        List<OrderAnalysisPO> list = new ArrayList<>();
        OrderAnalysisPO query = new OrderAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setRetailId(po.getRetailId());
        query.setRetailIds(po.getRetailIds());
        query.setLinkIds(po.getLinkIds());
        query.setState(params.getState());
        query.setContentType(params.getContentType());

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setPayTimeS(start);
        query.setPayTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        List<OrderAnalysisPO> dataList = orderAnalysisMapper.queryRechargeMemberCountDayList(query);
        po.setData(orderAnalysisMapper.queryRechargeMemberCount(query));

        HashMap<String, Integer> dataMap = new HashMap<>();
        dataList.forEach(cur -> dataMap.put(cur.getDataDay(), toInteger(cur.getData(), 0)));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            Integer data = defaultIfNull(dataMap.get(dataDay), StaticVar.ZERO);

            OrderAnalysisPO r = new OrderAnalysisPO();
            r.setDay(i);
            r.setDataDay(dataDay);
            r.setData(data);

            list.add(r);
        }
        return list;
    }

    /**
     * 累充金额对比
     */
    public List<JSONObject> getCumulativeRechargeContrastList(OrderAnalysisPO params, List<OrderAnalysisPO> paramsList) {
        List<JSONObject> result = new ArrayList<>();
        Set<Integer> dramaSet = new HashSet<>();
        for (OrderAnalysisPO po : paramsList) {
            dramaSet.add(po.getDramaId());
        }
        // 查询短剧
        FastDramaPO dramaQuery = new FastDramaPO();
        dramaQuery.setIds(StrUtil.join(dramaSet));
        Map<Integer, String> nameMap = dramaService.queryDramaNameMap(dramaQuery);
        for (OrderAnalysisPO po : paramsList) {
            Date start = DateUtil.format09(po.getReleaseDateStr());
            po.setPayTimeS(start);
            po.setPayTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
            List<OrderAnalysisPO> list = getCumulativeRechargeContrastList(po);

            JSONObject data = new JSONObject();
            data.put("dramaId", po.getDramaId());
            data.put("dramaName", nameMap.get(po.getDramaId()));
            data.put("list", defaultIfNull(list, new ArrayList<>()));

            result.add(data);
        }
        return result;
    }

    /**
     * 累充金额对比
     */
    public List<OrderAnalysisPO> getCumulativeRechargeContrastList(OrderAnalysisPO params) {
        List<OrderAnalysisPO> result = new ArrayList<>();
        List<OrderAnalysisPO> list = orderAnalysisMapper.queryRechargeCountMoneyList(params);
        // 统计N充人数(共统计到5充)
        int[] rechargeCount = new int[RECHARGE_COUNT_MAX];
        for (OrderAnalysisPO po : list) {
            if (po.getRechargeMoney().compareTo(BigDecimal_300) > 0) {
                rechargeCount[4]++;
            } else if (po.getRechargeMoney().compareTo(BigDecimal_200) > 0) {
                rechargeCount[3]++;
            } else if (po.getRechargeMoney().compareTo(BigDecimal_100) > 0) {
                rechargeCount[2]++;
            } else if (po.getRechargeMoney().compareTo(BigDecimal_30) > 0) {
                rechargeCount[1]++;
            } else {
                rechargeCount[0]++;
            }
        }
        for (int i = 0; i < RECHARGE_COUNT_MAX; i++) {
            OrderAnalysisPO po = new OrderAnalysisPO();
            po.setRechargeCount(i + 1);
            po.setRechargeMemberCount(rechargeCount[i]);
            result.add(po);
        }
        return result;
    }

    /**
     * 支付订单数
     *
     * @param params
     * @param po
     * @return
     */
    private List<OrderAnalysisPO> getRechargeOrderCount(OrderAnalysisPO params, OrderAnalysisPO po) {
        List<OrderAnalysisPO> list = new ArrayList<>();
        OrderAnalysisPO query = new OrderAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setRetailId(po.getRetailId());
        query.setRetailIds(po.getRetailIds());
        query.setLinkIds(po.getLinkIds());
        query.setState(params.getState());
        query.setContentType(params.getContentType());

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setPayTimeS(start);
        query.setPayTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        List<OrderAnalysisPO> dataList = orderAnalysisMapper.queryRechargeOrderCountDayList(query);
        po.setData(orderAnalysisMapper.queryRechargeOrderCount(query));

        HashMap<String, Integer> dataMap = new HashMap<>();
        dataList.forEach(cur -> dataMap.put(cur.getDataDay(), toInteger(cur.getData(), 0)));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            Integer data = defaultIfNull(dataMap.get(dataDay), StaticVar.ZERO);

            OrderAnalysisPO r = new OrderAnalysisPO();
            r.setDay(i);
            r.setDataDay(dataDay);
            r.setData(data);

            list.add(r);
        }
        return list;
    }

    /**
     * 支付金额
     *
     * @param params
     * @param po
     * @return
     */
    private List<OrderAnalysisPO> getRechargeMoneySum(OrderAnalysisPO params, OrderAnalysisPO po) {
        List<OrderAnalysisPO> list = new ArrayList<>();
        OrderAnalysisPO query = new OrderAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setRetailId(po.getRetailId());
        query.setRetailIds(po.getRetailIds());
        query.setLinkIds(po.getLinkIds());
        query.setState(1);
        query.setContentType(params.getContentType());

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setPayTimeS(start);
        query.setPayTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        List<OrderAnalysisPO> dataList = orderAnalysisMapper.queryRechargeMoneySumDayList(query);
        po.setData(orderAnalysisMapper.queryRechargeMoneySum(query));

        HashMap<String, BigDecimal> dataMap = new HashMap<>();
        dataList.forEach(cur -> dataMap.put(cur.getDataDay(), toBigDecimal(cur.getData(), BigDecimal.ZERO)));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            BigDecimal data = defaultIfNull(dataMap.get(dataDay), BigDecimal.ZERO);

            OrderAnalysisPO r = new OrderAnalysisPO();
            r.setDay(i);
            r.setDataDay(dataDay);
            r.setData(data);

            list.add(r);
        }
        return list;
    }

    /**
     * 订单完成率：已支付成功的订单➗总订单数（含已取消、待支付状态的）
     *
     * @param params
     * @param po
     * @return
     */
    private List<OrderAnalysisPO> getRechargeOrderRate(OrderAnalysisPO params, OrderAnalysisPO po) {
        List<OrderAnalysisPO> list = new ArrayList<>();
        params.setState(1);
        List<OrderAnalysisPO> rechargeOrderSuccessList = getRechargeOrderCount(params, po);
        Integer suc = (Integer) po.getData();
        Map<Integer, Integer> rechargeOrderSuccessMap = new HashMap<>();
        rechargeOrderSuccessList.forEach(cur -> rechargeOrderSuccessMap.put(cur.getDay(), (Integer) cur.getData()));

        params.setState(null);
        List<OrderAnalysisPO> rechargeOrderAllList = getRechargeOrderCount(params, po);
        Integer all = (Integer) po.getData();
        Map<Integer, Integer> rechargeOrderAllMap = new HashMap<>();
        rechargeOrderAllList.forEach(cur -> rechargeOrderAllMap.put(cur.getDay(), (Integer) cur.getData()));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            Integer data1 = defaultIfNull(rechargeOrderSuccessMap.get(i), 0);
            Integer data2 = defaultIfNull(rechargeOrderAllMap.get(i), 0);

            BigDecimal data = DoubleUtil.divB4Zero(data1 * 100, data2);

            OrderAnalysisPO r = new OrderAnalysisPO();
            r.setDay(i);
            r.setDataDay(dataDay);
            r.setData(data);
            r.setRechargeOrderCount(data1);
            r.setRechargeOrderSucCount(data2);

            list.add(r);
        }
        po.setData(DoubleUtil.divB4Zero(suc * 100, all));
        return list;
    }

    /**
     * 用户支付率：已支付成功的用户数➗总的创建订单用户数
     *
     * @param params
     * @param po
     * @return
     */
    private List<OrderAnalysisPO> getRechargePayRate(OrderAnalysisPO params, OrderAnalysisPO po) {
        List<OrderAnalysisPO> list = new ArrayList<>();
        params.setState(1);
        List<OrderAnalysisPO> rechargeOrderSuccessList = getRechargeMemberCount(params, po);
        Integer suc = (Integer) po.getData();
        Map<Integer, Integer> rechargeOrderSuccessMap = new HashMap<>();
        rechargeOrderSuccessList.forEach(cur -> rechargeOrderSuccessMap.put(cur.getDay(), (Integer) cur.getData()));

        params.setState(null);
        List<OrderAnalysisPO> rechargeOrderAllList = getRechargeMemberCount(params, po);
        Integer all = (Integer) po.getData();
        Map<Integer, Integer> rechargeOrderAllMap = new HashMap<>();
        rechargeOrderAllList.forEach(cur -> rechargeOrderAllMap.put(cur.getDay(), (Integer) cur.getData()));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            Integer data1 = defaultIfNull(rechargeOrderSuccessMap.get(i), 0);
            Integer data2 = defaultIfNull(rechargeOrderAllMap.get(i), 0);

            BigDecimal data = DoubleUtil.divB4Zero(data1 * 100, data2);

            OrderAnalysisPO r = new OrderAnalysisPO();
            r.setDay(i);
            r.setDataDay(dataDay);
            r.setData(data);
            r.setRechargeOrderCount(data1);
            r.setRechargeOrderSucCount(data2);

            list.add(r);
        }
        po.setData(DoubleUtil.divB4Zero(suc * 100, all));
        return list;
    }

    /**
     * 人均支付金额：支付金额➗已支付成功的用户数
     *
     * @param params
     * @param po
     * @return
     */
    private List<OrderAnalysisPO> getRechargeMoneyPer(OrderAnalysisPO params, OrderAnalysisPO po) {
        List<OrderAnalysisPO> list = new ArrayList<>();
        params.setState(1);
        List<OrderAnalysisPO> rechargeMoneySuccessList = getRechargeMoneySum(params, po);
        BigDecimal suc = (BigDecimal) po.getData();
        Map<Integer, BigDecimal> rechargeMoneySuccessMap = new HashMap<>();
        rechargeMoneySuccessList.forEach(cur -> rechargeMoneySuccessMap.put(cur.getDay(), (BigDecimal) cur.getData()));

        params.setState(1);
        List<OrderAnalysisPO> rechargeOrderAllList = getRechargeMemberCount(params, po);
        Integer all = (Integer) po.getData();
        Map<Integer, Integer> rechargeOrderAllMap = new HashMap<>();
        rechargeOrderAllList.forEach(cur -> rechargeOrderAllMap.put(cur.getDay(), (Integer) cur.getData()));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            BigDecimal data1 = defaultIfNull(rechargeMoneySuccessMap.get(i), BigDecimal.ZERO);
            Integer data2 = defaultIfNull(rechargeOrderAllMap.get(i), 0);

            BigDecimal data = DoubleUtil.divB4Zero(data1, data2);

            OrderAnalysisPO r = new OrderAnalysisPO();
            r.setDay(i);
            r.setDataDay(dataDay);
            r.setData(data);
            r.setRechargeMoney(data1);
            r.setRechargeOrderSucCount(data2);

            list.add(r);
        }
        po.setData(DoubleUtil.divB4Zero(suc, all));
        return list;
    }

    /**
     * 人均支付次数：成功的总订单数➗已支付成功的用户数
     *
     * @param params
     * @param po
     * @return
     */
    private List<OrderAnalysisPO> getRechargeMemberPer(OrderAnalysisPO params, OrderAnalysisPO po) {
        List<OrderAnalysisPO> list = new ArrayList<>();
        params.setState(1);
        List<OrderAnalysisPO> rechargeMemberCountAllList = getRechargeOrderCount(params, po);
        Integer all = (Integer) po.getData();
        Map<Integer, Integer> rechargeMemberCountAllMap = new HashMap<>();
        rechargeMemberCountAllList.forEach(cur -> rechargeMemberCountAllMap.put(cur.getDay(), (Integer) cur.getData()));

        params.setState(1);
        List<OrderAnalysisPO> rechargeMemberCountSuccessList = getRechargeMemberCount(params, po);
        Integer suc = (Integer) po.getData();
        Map<Integer, Integer> rechargeMemberCountSuccessMap = new HashMap<>();
        rechargeMemberCountSuccessList.forEach(cur -> rechargeMemberCountSuccessMap.put(cur.getDay(), (Integer) cur.getData()));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            Integer data1 = defaultIfNull(rechargeMemberCountAllMap.get(i), 0);
            Integer data2 = defaultIfNull(rechargeMemberCountSuccessMap.get(i), 0);

            BigDecimal data = DoubleUtil.divB4Zero(data1, data2);

            OrderAnalysisPO r = new OrderAnalysisPO();
            r.setDay(i);
            r.setDataDay(dataDay);
            r.setData(data);
            r.setRechargeOrderCount(data1);
            r.setRechargeOrderSucCount(data2);

            list.add(r);
        }
        po.setData(DoubleUtil.divB4Zero(all, suc));
        return list;
    }
}
