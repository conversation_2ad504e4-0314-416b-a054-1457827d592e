/*
 * Powered By fast.up
 */
package com.fast.service.analysis;

import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticVar;
import com.fast.mapper.analysis.LinkAnalysisMapper;
import com.fast.po.analysis.LinkAnalysisPO;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.promote.FastLinkRoadPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaRetailService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.promote.FastLinkRoadService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.retail.FastRetailService;
import com.fast.service.user.FastUserService;
import com.fast.utils.*;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 投放分析
 *
 * <AUTHOR>
 */
@Service
public class LinkAnalysisService extends BaseService {

    @Autowired
    private LinkAnalysisMapper launchAnalysisMapper;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastDramaRetailService dramaRetailService;
    @Autowired
    private FastUserService userService;
    @Autowired
    private FastRetailService retailService;
    @Autowired
    private FastLinkRoadService linkRoadService;

    /**
     * ROI梯度表-整体数据汇总
     */
    public List<LinkAnalysisPO> getLinkRoiCoreAllAnalysis(LinkAnalysisPO params, LinkAnalysisPO summary) {
        // 根据params缓存查询结果--开始
        String paramKey = Md5Util.getMD5(params.toString()).substring(16);
        String listKey = "getLinkRoiCoreAllAnalysis:" + paramKey;
        String summaryKey = "getLinkRoiCoreAllAnalysisSum:" + paramKey;
        String summaryCache = RedisUtil.get(summaryKey);
        if (notBlank(summaryCache)) {
            LinkAnalysisPO cache = JsonUtil.toJavaObject(summaryCache, LinkAnalysisPO.class);
            BeanUtils.copyProperties(cache, summary);
        }
        String listCache = RedisUtil.get(listKey);
        if (notBlank(listCache)) {
            List<LinkAnalysisPO> cache = JsonUtil.toList(listCache, LinkAnalysisPO.class);
            if (CollUtil.hasContent(cache)) {
                return cache;
            }
        }
        // 根据params缓存查询结果--结束

        // 汇总全部的D0-D60
        Integer[] numD60RechSum = new Integer[61];// 每日充值人数
        BigDecimal[] moneyD60RechSum = new BigDecimal[61];// 每日充值金额
        BigDecimal[] backD60RechSum = new BigDecimal[61];// 每日roi
        BigDecimal[] moneyD60RechSumDaySum = new BigDecimal[61];// 每日充值金额累加
        BigDecimal[] backD60RechSumDaySum = new BigDecimal[61];// 每日roi累加

        List<LinkAnalysisPO> list = new ArrayList<>();
        // 查询每天(维度)的明细汇总
        Map<Object, Integer> addColorMemberSumMap = new HashMap<>();
        List<LinkAnalysisPO> listPre = launchAnalysisMapper.queryCoreAllAnalysisList(params);
        if (params.getGroupByColum() > 0) {
            for (LinkAnalysisPO cur : listPre) {
                if (cur.getId() != null && cur.getId() > 0) {
                    list.add(cur);
                }
            }
        } else {
            list = listPre;
        }
        if (params.getGroupByColum() > 0) {
            Set<Integer> ids = new HashSet<>();
            list.forEach(cur -> ids.add(cur.getId()));
            // 统计维度：0=整体;1=链接;2=分销商;3=优化师;4=短剧;5=链路
            switch (params.getGroupByColum()) {
                case 1: {
                    params.setLinkIds(StrUtil.join(ids));
                }
                break;
                case 2: {
                    params.setRetailIds(StrUtil.join(ids));
                }
                break;
                case 3: {
                    params.setAdvUserIds(StrUtil.join(ids));
                }
                break;
                case 4: {
                    params.setDramaIds(StrUtil.join(ids));
                }
                break;
                case 5: {
                    params.setRoadIds(StrUtil.join(ids));
                }
                break;
            }
            List<LinkAnalysisPO> addColorMemberSumList = launchAnalysisMapper.queryAddColorMemberSumList(params);
            addColorMemberSumList.forEach(cur -> addColorMemberSumMap.put(cur.getId(), cur.getMemberCountAddColor()));
        } else {
            List<LinkAnalysisPO> addColorMemberSumList = launchAnalysisMapper.queryAddColorMemberSumList(params);
            addColorMemberSumList.forEach(cur -> addColorMemberSumMap.put(cur.getStatisDate(), cur.getMemberCountAddColor()));
        }
        for (LinkAnalysisPO cur : list) {
            // 去除重复
            if (notBlank(cur.getLinkIds())) {
                Set<Integer> linkIds = CollUtil.parseIntStr2Set(cur.getLinkIds());
                if (CollUtil.hasContent(linkIds)) {
                    cur.setLinkIds(StrUtil.join(linkIds));
                }
            }
            String[] numD60Rech = StrUtil.splitN(cur.getNumD60Rech(), StaticVar.CROSS_HATCH);
            String[] moneyD60Rech = StrUtil.splitN(cur.getMoneyD60Rech(), StaticVar.CROSS_HATCH);
            BigDecimal rechargeMoneySumAddAll = BigDecimal.ZERO;
            for (String d60Rech : moneyD60Rech) {
                String[] moneyOne = StrUtil.split(d60Rech, StaticVar.COMMA);
                for (String s : moneyOne) {
                    if (notBlank(s)) {
                        rechargeMoneySumAddAll = DoubleUtil.addB(rechargeMoneySumAddAll, new BigDecimal(s));
                    }
                }
            }
            cur.setRechargeMoneySumAddAll(rechargeMoneySumAddAll);
            // params.setStatisDate(cur.getStatisDate());
            if (params.getGroupByColum() > 0) {
                cur.setMemberCountAddColor(defaultIfNull(addColorMemberSumMap.get(cur.getId()), 0));
            } else {
                cur.setMemberCountAddColor(defaultIfNull(addColorMemberSumMap.get(cur.getStatisDate()), 0));
            }
            // 续充率 (累计充值金额-当日充值金额)➗当日充值金额
            BigDecimal decimal = DoubleUtil.subB(defaultIfNull(cur.getRechargeMoneySumAddAll(), BigDecimal.ZERO), defaultIfNull(cur.getRechargeMoneySumAdd(), BigDecimal.ZERO)).multiply(BigDecimalVar.BD_100);
            cur.setRenewRate(DoubleUtil.divB4Zero(decimal, cur.getRechargeMoneySumAdd()));
            // 染色率
            cur.setMemberRateAddColor(DoubleUtil.divB4Zero(cur.getMemberCountAddColor() * 100, cur.getMemberCountAddAll()));
            // arpu 累计充值金额➗累计新增用户数
            cur.setArpu(DoubleUtil.divB4Zero(cur.getRechargeMoneySumAddAll(), cur.getMemberCountAddAll()));
            // arppu 累计充值金额➗累计充值用户数
            cur.setArppu(DoubleUtil.divB4Zero(cur.getRechargeMoneySumAddAll(), cur.getRechargeMemberCountAddAll()));
            // 用户成本 总成本➗新增用户数
            cur.setMemberCost(DoubleUtil.divB4Zero(cur.getAdMoneyConsume(), cur.getMemberCountAddAll()));
            // 付费用户成本 总成本➗累计充值用户数
            cur.setPayMemberCost(DoubleUtil.divB4Zero(cur.getAdMoneyConsume(), cur.getRechargeMemberCountAddAll()));
            // 回报率 累计充值金额➗该日期的总成本
            cur.setBackRate(DoubleUtil.divB4Zero(defaultIfNull(cur.getRechargeMoneySumAddAll(), BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), cur.getAdMoneyConsume()));
            // 汇总每天(维度)的D0-D60
            Integer[] numD60RechAll = new Integer[61];// 每日充值人数
            BigDecimal[] moneyD60RechAll = new BigDecimal[61];// 每日充值金额
            BigDecimal[] moneyD60RechDaySum = new BigDecimal[61];// 每日充值金额累加
            BigDecimal[] backD60RechAll = new BigDecimal[61];// 每日roi
            BigDecimal[] backD60RechDaySum = new BigDecimal[61];// 每日roi累加
            BigDecimal[] renewRateD60 = new BigDecimal[61];// 每日续充率
            int length = numD60Rech.length;
            if (moneyD60Rech.length < length) {
                length = moneyD60Rech.length;
            }
            for (int i = 0; i < length; i++) {
                String[] numOne = StrUtil.splitN(numD60Rech[i], StaticVar.COMMA);
                for (int j = 0; j < numOne.length; j++) {
                    if (notBlank(numOne[j])) {
                        numD60RechAll[j] = defaultIfNull(numD60RechAll[j], 0) + toInteger(numOne[j]);
                    }
                }
                String[] moneyOne = StrUtil.splitN(moneyD60Rech[i], StaticVar.COMMA);
                if (moneyOne != null && moneyOne.length > 0) {
                    for (int j = 0; j < moneyOne.length; j++) {
                        if (notBlank(moneyOne[j])) {
                            moneyD60RechAll[j] = defaultIfNull(moneyD60RechAll[j], BigDecimal.ZERO).add(toBigDecimal(moneyOne[j]));
                        }
                    }
                }
            }

            // 每日充值人数/金额
            for (int i = 0; i < numD60RechAll.length; i++) {
                if (notBlank(numD60RechAll[i])) {
                    numD60RechSum[i] = defaultIfNull(numD60RechSum[i], 0) + numD60RechAll[i];
                }
                if (notBlank(moneyD60RechAll[i])) {
                    moneyD60RechSum[i] = defaultIfNull(moneyD60RechSum[i], BigDecimal.ZERO).add(moneyD60RechAll[i]);
                    // 每日累加(D[n]=D[n-1]+D)
                    moneyD60RechDaySum[i] = defaultIfNull(i > 0 ? moneyD60RechDaySum[i - 1] : BigDecimal.ZERO, BigDecimal.ZERO).add(moneyD60RechAll[i]);
                    moneyD60RechSumDaySum[i] = defaultIfNull(moneyD60RechSumDaySum[i], BigDecimal.ZERO).add(moneyD60RechDaySum[i]);
                }
                if (i > 0 && moneyD60RechDaySum[i] == null && moneyD60RechDaySum[i - 1] != null) {
                    moneyD60RechSumDaySum[i] = defaultIfNull(moneyD60RechSumDaySum[i], BigDecimal.ZERO).add(moneyD60RechDaySum[i - 1]);
                }
            }

            // 每日roi
            for (int i = 0; i < backD60RechAll.length; i++) {
                backD60RechAll[i] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechAll[i], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), cur.getAdMoneyConsume());
                backD60RechDaySum[i] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechDaySum[i], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), cur.getAdMoneyConsume());
                backD60RechSumDaySum[i] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechSumDaySum[i], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), cur.getAdMoneyConsume());
            }

            // 续充率
            renewRateD60[0] = null;
            for (int i = 1; i < moneyD60RechAll.length; i++) {
                renewRateD60[i] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechAll[i], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), moneyD60RechAll[0]);
            }

            cur.setNumD60RechAll(numD60RechAll);
            cur.setMoneyD60RechAll(moneyD60RechAll);
            cur.setMoneyD60RechDaySum(moneyD60RechDaySum);
            cur.setRenewRateD60(renewRateD60);
            cur.setBackD60RechAll(backD60RechAll);
            cur.setBackD60RechDaySum(backD60RechDaySum);

            // 汇总数据
            if (summary != null) {
                summary.setAdMoneyConsume(DoubleUtil.addB(defaultIfNull(summary.getAdMoneyConsume(), BigDecimal.ZERO), cur.getAdMoneyConsume()));
                summary.setMemberCountAddAll(defaultIfNull(summary.getMemberCountAddAll(), StaticVar.ZERO) + cur.getMemberCountAddAll());
                summary.setMemberCountAddColor(defaultIfNull(summary.getMemberCountAddColor(), StaticVar.ZERO) + cur.getMemberCountAddColor());
                summary.setRechargeMemberCountAddAll(defaultIfNull(summary.getRechargeMemberCountAddAll(), StaticVar.ZERO) + cur.getRechargeMemberCountAddAll());
                summary.setRechargeMemberCountAdd(defaultIfNull(summary.getRechargeMemberCountAdd(), StaticVar.ZERO) + cur.getRechargeMemberCountAdd());
                summary.setRechargeMoneySumAddAll(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneySumAddAll(), BigDecimal.ZERO), cur.getRechargeMoneySumAddAll()));
                summary.setRechargeMoneySumAdd(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneySumAdd(), BigDecimal.ZERO), cur.getRechargeMoneySumAdd()));
            }
            // 清理垃圾数据
            cur.setNumD60Rech(null);
            cur.setMoneyD60Rech(null);
        }
        if (summary != null) {
            // arppu 累计充值金额➗累计充值用户数
            summary.setArppu(DoubleUtil.divB4Zero(summary.getRechargeMoneySumAddAll(), summary.getRechargeMemberCountAddAll()));
            // 用户成本 总成本➗新增用户数
            summary.setMemberCost(DoubleUtil.divB4Zero(summary.getAdMoneyConsume(), summary.getMemberCountAddAll()));
            // 付费用户成本 总成本➗累计付费用户数
            summary.setPayMemberCost(DoubleUtil.divB4Zero(summary.getAdMoneyConsume(), summary.getRechargeMemberCountAddAll()));
            // 回报率 累计充值金额➗该日期的总成本
            summary.setBackRate(DoubleUtil.divB4Zero(defaultIfNull(summary.getRechargeMoneySumAddAll(), BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), summary.getAdMoneyConsume()));
            summary.setMemberRateAddColor(DoubleUtil.divB4Zero(defaultIfNull(summary.getMemberCountAddColor(), 0) * 100, summary.getMemberCountAddAll()));
            summary.setMoneyD60RechAll(moneyD60RechSum);

            // 续充率 (累计充值金额-当日充值金额)➗当日充值金额
            BigDecimal decimal = DoubleUtil.subB(defaultIfNull(summary.getRechargeMoneySumAddAll(), BigDecimal.ZERO), defaultIfNull(summary.getRechargeMoneySumAdd(), BigDecimal.ZERO)).multiply(BigDecimalVar.BD_100);
            summary.setRenewRate(DoubleUtil.divB4Zero(decimal, summary.getRechargeMoneySumAdd()));

            // 去除多余的脏数据
            for (int i = 0; i < numD60RechSum.length; i++) {
                if (numD60RechSum[i] == null && moneyD60RechSumDaySum[i] != null) {
                    moneyD60RechSumDaySum[i] = null;
                    break;
                }
            }
            summary.setNumD60RechAll(numD60RechSum);
            summary.setMoneyD60RechDaySum(moneyD60RechSumDaySum);
            for (int j = 0; j < backD60RechSum.length; j++) {
                backD60RechSum[j] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechSum[j], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), summary.getAdMoneyConsume());
                backD60RechSumDaySum[j] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechSumDaySum[j], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), summary.getAdMoneyConsume());
            }
            summary.setBackD60RechAll(backD60RechSum);
            summary.setBackD60RechDaySum(backD60RechSumDaySum);
        }
        // 根据params缓存查询结果
        if (CollUtil.hasContent(list)) {
            RedisUtil.setObject(listKey, list, 60);
        }
        if (summary != null) {
            RedisUtil.setObject(summaryKey, summary, 60);
        }
        return list;
    }

    /**
     * ROI梯度表-每日数据汇总
     */
    public List<LinkAnalysisPO> getLinkRoiCorePerDayAnalysis(LinkAnalysisPO params, LinkAnalysisPO summary) {
        // 汇总全部的D0-D60
        Integer[] numD60RechSum = new Integer[61];// 每日充值人数
        BigDecimal[] moneyD60RechSum = new BigDecimal[61];// 每日充值金额
        BigDecimal[] backD60RechSum = new BigDecimal[61];// 每日roi
        BigDecimal[] moneyD60RechSumDaySum = new BigDecimal[61];// 每日充值金额累加
        BigDecimal[] backD60RechSumDaySum = new BigDecimal[61];// 每日roi累加

        // 查询每天(维度)的明细汇总
        Map<String, Integer> addColorMemberSumMap = new HashMap<>();
        List<LinkAnalysisPO> list = launchAnalysisMapper.queryCorePerDayAnalysisList(params);

        params.setGroupByColum(0);
        List<LinkAnalysisPO> addColorMemberSumList = launchAnalysisMapper.queryAddColorMemberSumList(params);
        addColorMemberSumList.forEach(cur -> addColorMemberSumMap.put(cur.getStatisDate(), cur.getMemberCountAddColor()));

        for (LinkAnalysisPO cur : list) {
            String[] numD60Rech = StrUtil.splitN(cur.getNumD60Rech(), StaticVar.CROSS_HATCH);
            String[] moneyD60Rech = StrUtil.splitN(cur.getMoneyD60Rech(), StaticVar.CROSS_HATCH);
            BigDecimal rechargeMoneySumAddAll = BigDecimal.ZERO;
            if (moneyD60Rech != null) {
                for (String d60Rech : moneyD60Rech) {
                    String[] moneyOne = StrUtil.split(d60Rech, StaticVar.COMMA);
                    for (String s : moneyOne) {
                        if (notBlank(s)) {
                            rechargeMoneySumAddAll = DoubleUtil.addB(rechargeMoneySumAddAll, new BigDecimal(s));
                        }
                    }
                }
            }
            cur.setRechargeMoneySumAddAll(rechargeMoneySumAddAll);
            cur.setRechargeMemberCountAddAll(cur.getRechargeMemberCountAddAll());
            // params.setStatisDate(cur.getStatisDate());
            cur.setMemberCountAddColor(defaultIfNull(addColorMemberSumMap.get(cur.getStatisDate()), 0));
            // cur.setMemberCountAddColor(launchAnalysisMapper.queryAddColorMemberSum(params));
            cur.setMemberRateAddColor(DoubleUtil.divB4Zero(cur.getMemberCountAddColor() * 100, cur.getMemberCountAdd()));
            // arpu 累计充值金额➗累计新增用户数
            cur.setArpu(DoubleUtil.divB4Zero(cur.getRechargeMoneySumAddAll(), cur.getMemberCountAdd()));
            // arppu 累计充值金额➗累计充值用户数
            cur.setArppu(DoubleUtil.divB4Zero(cur.getRechargeMoneySumAddAll(), cur.getRechargeMemberCountAddAll()));
            // 用户成本 当天总成本➗当天新增用户数
            cur.setMemberCost(DoubleUtil.divB4Zero(cur.getAdMoneyConsume(), cur.getMemberCountAdd()));
            // 付费用户成本 当天总成本➗当天累计付费用户数
            cur.setPayMemberCost(DoubleUtil.divB4Zero(cur.getAdMoneyConsume(), cur.getRechargeMemberCountAddAll()));
            // 回报率 累计充值金额➗该日期的总成本
            cur.setBackRate(DoubleUtil.divB4Zero(defaultIfNull(cur.getRechargeMoneySumAddAll(), BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), cur.getAdMoneyConsume()));
            // 汇总每天(维度)的D0-D60
            Integer[] numD60RechAll = new Integer[61];// 每日充值人数
            BigDecimal[] moneyD60RechAll = new BigDecimal[61];// 每日充值金额
            BigDecimal[] moneyD60RechDaySum = new BigDecimal[61];// 每日充值金额累加
            BigDecimal[] backD60RechAll = new BigDecimal[61];// 每日roi
            BigDecimal[] backD60RechDaySum = new BigDecimal[61];// 每日roi累加
            // String[] numD60Rech = StrUtil.split(cur.getNumD60Rech(), StaticVar.CROSS_HATCH);
            // String[] moneyD60Rech = StrUtil.split(cur.getMoneyD60Rech(), StaticVar.CROSS_HATCH);
            if (numD60Rech != null) {
                for (int i = 0; i < numD60Rech.length; i++) {
                    String[] numD60RechOne = StrUtil.splitN(numD60Rech[i], StaticVar.COMMA);
                    for (int j = 0; j < numD60RechOne.length; j++) {
                        if (notBlank(numD60RechOne[j])) {
                            numD60RechAll[j] = defaultIfNull(numD60RechAll[j], 0) + toInteger(numD60RechOne[j]);
                        }
                    }
                    if (moneyD60Rech != null) {
                        String[] moneyD60RechOne = StrUtil.splitN(defaultIfNull(moneyD60Rech[i], ""), StaticVar.COMMA);
                        for (int j = 0; j < moneyD60RechOne.length; j++) {
                            if (notBlank(moneyD60RechOne[j])) {
                                moneyD60RechAll[j] = defaultIfNull(moneyD60RechAll[j], BigDecimal.ZERO).add(toBigDecimal(moneyD60RechOne[j]));
                            }
                        }
                    }
                }
            }

            // 每日充值人数/金额
            for (int i = 0; i < numD60RechAll.length; i++) {
                if (notBlank(numD60RechAll[i])) {
                    numD60RechSum[i] = defaultIfNull(numD60RechSum[i], 0) + numD60RechAll[i];
                }
                if (notBlank(moneyD60RechAll[i])) {
                    moneyD60RechSum[i] = defaultIfNull(moneyD60RechSum[i], BigDecimal.ZERO).add(moneyD60RechAll[i]);
                    // 每日累加(D[n]=D[n-1]+D)
                    moneyD60RechDaySum[i] = defaultIfNull(i > 0 ? moneyD60RechDaySum[i - 1] : BigDecimal.ZERO, BigDecimal.ZERO).add(moneyD60RechAll[i]);
                    moneyD60RechSumDaySum[i] = defaultIfNull(moneyD60RechSumDaySum[i], BigDecimal.ZERO).add(moneyD60RechDaySum[i]);
                }
            }

            // 每日roi
            for (int i = 0; i < backD60RechAll.length; i++) {
                backD60RechAll[i] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechAll[i], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), cur.getAdMoneyConsume());
                backD60RechDaySum[i] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechDaySum[i], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), cur.getAdMoneyConsume());
                backD60RechSumDaySum[i] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechSumDaySum[i], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), cur.getAdMoneyConsume());
            }

            cur.setNumD60RechAll(numD60RechAll);
            cur.setMoneyD60RechAll(moneyD60RechAll);
            cur.setMoneyD60RechDaySum(moneyD60RechDaySum);
            cur.setBackD60RechAll(backD60RechAll);
            cur.setBackD60RechDaySum(backD60RechDaySum);

            // 汇总数据
            if (summary != null) {
                summary.setAdMoneyConsume(DoubleUtil.addB(defaultIfNull(summary.getAdMoneyConsume(), BigDecimal.ZERO), cur.getAdMoneyConsume()));
                summary.setMemberCountAdd(defaultIfNull(summary.getMemberCountAdd(), StaticVar.ZERO) + cur.getMemberCountAdd());
                summary.setMemberCountAddColor(defaultIfNull(summary.getMemberCountAddColor(), StaticVar.ZERO) + cur.getMemberCountAddColor());
                summary.setRechargeMemberCountAddAll(defaultIfNull(summary.getRechargeMemberCountAddAll(), StaticVar.ZERO) + cur.getRechargeMemberCountAddAll());
                summary.setRechargeMoneySumAddAll(DoubleUtil.addB(defaultIfNull(summary.getRechargeMoneySumAddAll(), BigDecimal.ZERO), cur.getRechargeMoneySumAddAll()));
            }
        }
        if (summary != null) {
            // arpu 累计充值金额➗累计新增用户数
            summary.setArpu(DoubleUtil.divB4Zero(summary.getRechargeMoneySumAddAll(), summary.getMemberCountAdd()));
            // 用户成本 当天总成本➗当天新增用户数
            summary.setMemberCost(DoubleUtil.divB4Zero(summary.getAdMoneyConsume(), summary.getMemberCountAdd()));
            // 付费用户成本 当天总成本➗当天累计付费用户数
            summary.setPayMemberCost(DoubleUtil.divB4Zero(summary.getAdMoneyConsume(), summary.getRechargeMemberCountAddAll()));
            // 回报率 累计充值金额➗该日期的总成本
            summary.setBackRate(DoubleUtil.divB4Zero(defaultIfNull(summary.getRechargeMoneySumAddAll(), BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), summary.getAdMoneyConsume()));
            summary.setMemberRateAddColor(DoubleUtil.divB4Zero(defaultIfNull(summary.getMemberCountAddColor(), 0) * 100, summary.getMemberCountAdd()));
            summary.setNumD60RechAll(numD60RechSum);
            summary.setMoneyD60RechAll(moneyD60RechSum);
            summary.setMoneyD60RechDaySum(moneyD60RechSumDaySum);
            for (int j = 0; j < backD60RechSum.length; j++) {
                backD60RechSum[j] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechSum[j], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), summary.getAdMoneyConsume());
                backD60RechSumDaySum[j] = DoubleUtil.divB4Zero(defaultIfNull(moneyD60RechSumDaySum[j], BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), summary.getAdMoneyConsume());
            }
            summary.setBackD60RechAll(backD60RechSum);
            summary.setBackD60RechDaySum(backD60RechSumDaySum);
        }
        return list;
    }

    /**
     * ROI梯度表
     */
    public List<LinkAnalysisPO> getLinkRoiCoreAnalysis(LinkAnalysisPO params, LinkAnalysisPO summary) {
        List<LinkAnalysisPO> list = getLinkRoiCoreAllAnalysis(params, summary);
        // groupByColum:统计维度：0=整体;1=链接;2=分销商;3=优化师;4=短剧;5=链路
        switch (params.getGroupByColum()) {
            case 0: {
                // 整体数据汇总
                list.sort(Comparator.comparing(LinkAnalysisPO::getStatisDate).reversed());
                if (summary != null) {
                    for (LinkAnalysisPO cur : list) {
                        summary.setValidLinkCount(defaultIfNull(summary.getValidLinkCount(), 0) + cur.getValidLinkCount());
                    }
                }
            }
            break;
            case 1: {
                // 链接数据汇总
                list.sort(Comparator.comparing(LinkAnalysisPO::getId).reversed());
                Set<Integer> ids = new HashSet<>();
                list.forEach(cur -> ids.add(cur.getId()));
                // 翻译链接名称
                {
                    FastLinkPO query = new FastLinkPO();
                    query.setIds(StrUtil.join(ids));
                    Map<Integer, FastLinkPO> nameMap = linkService.queryLinkMap(query);
                    for (LinkAnalysisPO cur : list) {
                        FastLinkPO fastLinkPO = nameMap.get(cur.getId());
                        if (fastLinkPO == null) {
                            fastLinkPO = new FastLinkPO();
                        }
                        cur.setLinkName(fastLinkPO.getLinkName());
                        cur.setLinkCreatTime(fastLinkPO.getCreateTime());
                        cur.setUserName(fastLinkPO.getUserName());
                    }
                }

                // 今日数据
                params.setLinkIds(StrUtil.join(ids));
                this.doTodayData(params, summary, list);

                // 翻译分销商名称
                {
                    ids.clear();
                    list.forEach(cur -> ids.add(cur.getRetailId()));
                    FastRetailPO query = new FastRetailPO();
                    query.setIds(StrUtil.join(ids));
                    Map<Integer, String> nameMap = retailService.queryRetailNameMap(query);
                    for (LinkAnalysisPO cur : list) {
                        cur.setRetailName(nameMap.get(cur.getRetailId()));
                    }
                }

                // 翻译优化师名称
                {
                    ids.clear();
                    list.forEach(cur -> ids.add(cur.getAdvUserId()));
                    FastUserPO query = new FastUserPO();
                    query.setIds(StrUtil.join(ids));
                    Map<Integer, String> nameMap = userService.queryUserNameMap(query);
                    for (LinkAnalysisPO cur : list) {
                        cur.setAdvUserName(nameMap.get(cur.getAdvUserId()));
                    }
                }
            }
            break;
            case 2: {
                // 分销商数据汇总
                list.sort(Comparator.comparing(LinkAnalysisPO::getId).reversed());
                Set<Integer> ids = new HashSet<>();
                list.forEach(cur -> ids.add(cur.getId()));

                FastRetailPO query = new FastRetailPO();
                query.setIds(StrUtil.join(ids));
                Map<Integer, FastRetailPO> map = retailService.queryRetailMap(query);
                for (LinkAnalysisPO cur : list) {
                    FastRetailPO retailPO = defaultIfNull(map.get(cur.getId()), new FastRetailPO());
                    cur.setRetailName(retailPO.getRetailName());
                    cur.setRetailType(retailPO.getRetailType());
                }

                // 今日数据
                params.setRetailIds(StrUtil.join(ids));
                this.doTodayData(params, summary, list);
            }
            break;
            case 3: {
                // 优化师数据汇总
                list.sort(Comparator.comparing(LinkAnalysisPO::getId).reversed());
                Set<Integer> ids = new HashSet<>();
                list.forEach(cur -> ids.add(cur.getId()));

                FastUserPO query = new FastUserPO();
                query.setIds(StrUtil.join(ids));
                Map<Integer, FastUserPO> nameMap = userService.queryUserMap(query);
                for (LinkAnalysisPO cur : list) {
                    FastUserPO po = nameMap.get(cur.getId());
                    if (po == null) {
                        po = new FastUserPO();
                    }
                    cur.setAdvUserName(po.getUserName());
                    cur.setRetailName(po.getRetailName());
                }

                // 今日数据
                params.setAdvUserIds(StrUtil.join(ids));
                this.doTodayData(params, summary, list);
            }
            break;
            case 4: {
                // 短剧数据汇总
                list.sort(Comparator.comparing(LinkAnalysisPO::getId).reversed());
                Set<Integer> ids = new HashSet<>();
                list.forEach(cur -> ids.add(cur.getId()));

                FastDramaPO query = new FastDramaPO();
                query.setIds(StrUtil.join(ids));
                Map<Integer, String> nameMap = dramaService.queryDramaNameMap(query);
                for (LinkAnalysisPO cur : list) {
                    cur.setDramaName(nameMap.get(cur.getId()));
                }

                // 今日数据
                params.setDramaIds(StrUtil.join(ids));
                this.doTodayData(params, summary, list);
            }
            break;
            case 5: {
                // 链路数据汇总
                list.sort(Comparator.comparing(LinkAnalysisPO::getId).reversed());
                Set<Integer> ids = new HashSet<>();
                list.forEach(cur -> ids.add(cur.getId()));

                FastLinkRoadPO query = new FastLinkRoadPO();
                query.setIds(StrUtil.join(ids));
                Map<Integer, String> nameMap = linkRoadService.queryLinkRoadName(query);
                for (LinkAnalysisPO cur : list) {
                    cur.setRoadName(nameMap.get(cur.getId()));
                }

                // 今日数据
                params.setRoadIds(StrUtil.join(ids));
                this.doTodayData(params, summary, list);
            }
            break;
        }
        params.setStatisDate(null);
        params.setStatisDateS(null);
        params.setStatisDateE(null);
        // 查询创建的链接数
        if (params.getGroupByColum() > 1) {
            List<LinkAnalysisPO> linkCountList = launchAnalysisMapper.queryAllLinkCount(params);
            Map<Integer, LinkAnalysisPO> linkCountMap = new HashMap<>();
            linkCountList.forEach(cur -> linkCountMap.put(cur.getId(), cur));

            for (LinkAnalysisPO cur : list) {
                LinkAnalysisPO po = defaultIfNull(linkCountMap.get(cur.getId()), new LinkAnalysisPO());
                cur.setAllLinkCount(defaultIfNull(po.getAllLinkCount(), 0));
                cur.setLinkIds(po.getLinkIds());
                if (summary != null) {
                    summary.setAllLinkCount(defaultIfNull(summary.getAllLinkCount(), 0) + cur.getAllLinkCount());
                }
            }
        }
        // 处理排序 ：0=默认;1=投放消耗;2=新增用户;3=染色用户;4=充值用户;5=用户成本;6=ARPPU;7=充值金额;8=累计ROI",
        switch (params.getSortType()) {
            case 1: {
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getAdMoneyConsume).reversed());
                } else {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getAdMoneyConsume));
                }
            }
            break;
            case 2: {
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getMemberCountAddAll).reversed());
                } else {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getMemberCountAddAll));
                }
            }
            break;
            case 3: {
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getMemberCountAddColor).reversed());
                } else {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getMemberCountAddColor));
                }
            }
            break;
            case 4: {
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getRechargeMemberCountAddAll).reversed());
                } else {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getRechargeMemberCountAddAll));
                }
            }
            break;
            case 5: {
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getMemberCost).reversed());
                } else {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getMemberCost));
                }
            }
            break;
            case 6: {
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getArppu).reversed());
                } else {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getArppu));
                }
            }
            break;
            case 7: {
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getRechargeMoneySumAddAll).reversed());
                } else {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getRechargeMoneySumAddAll));
                }
            }
            break;
            case 8: {
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getBackRate).reversed());
                } else {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getBackRate));
                }
            }
            break;
            case 9: {
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getRenewRate).reversed());
                } else {
                    list.sort(Comparator.comparing(LinkAnalysisPO::getRenewRate));
                }
            }
            break;
        }
        return list;
    }

    /**
     * 处理今日数据
     *
     * @param params
     * @param summary
     * @param list
     */
    private void doTodayData(LinkAnalysisPO params, LinkAnalysisPO summary, List<LinkAnalysisPO> list) {
        params.setStatisDate(DateUtil.getNowTime09Str());
        {
            List<LinkAnalysisPO> linkAnalysisData = launchAnalysisMapper.queryTodayDataAnalysisList(params);
            Map<Integer, LinkAnalysisPO> todayMap = new HashMap<>();
            for (LinkAnalysisPO cur : linkAnalysisData) {
                todayMap.put(cur.getId(), cur);
            }
            for (LinkAnalysisPO cur : list) {
                LinkAnalysisPO po = todayMap.get(cur.getId());
                if (po == null) {
                    cur.setAdMoneyConsumeToday(BigDecimal.ZERO);
                    cur.setMemberCountAddToday(0);
                    cur.setRechargeMemberCountAddToday(0);
                } else {
                    cur.setAdMoneyConsumeToday(defaultIfNull(po.getAdMoneyConsumeToday(), BigDecimal.ZERO));
                    cur.setMemberCountAddToday(defaultIfNull(po.getMemberCountAddToday(), 0));
                    cur.setRechargeMemberCountAddToday(defaultIfNull(po.getRechargeMemberCountAddToday(), 0));
                }
                if (summary != null) {
                    summary.setAdMoneyConsumeToday(DoubleUtil.addB(defaultIfNull(summary.getAdMoneyConsumeToday(), BigDecimal.ZERO), cur.getAdMoneyConsumeToday()));
                    summary.setMemberCountAddToday(defaultIfNull(summary.getMemberCountAddToday(), 0) + cur.getMemberCountAddToday());
                    summary.setRechargeMemberCountAddToday(defaultIfNull(summary.getRechargeMemberCountAddToday(), 0) + cur.getRechargeMemberCountAddToday());
                }
            }
        }
        {
            List<LinkAnalysisPO> colorAddData = launchAnalysisMapper.queryTodayAddColorMember(params);
            Map<Integer, Integer> todayMap = new HashMap<>();
            for (LinkAnalysisPO cur : colorAddData) {
                todayMap.put(cur.getId(), cur.getDataCount());
            }
            for (LinkAnalysisPO cur : list) {
                Integer po = todayMap.get(cur.getId());
                if (po == null) {
                    cur.setMemberCountAddColorToday(0);
                } else {
                    cur.setMemberCountAddColorToday(po);
                }
                if (summary != null) {
                    summary.setMemberCountAddColorToday(defaultIfNull(summary.getMemberCountAddColorToday(), 0) + cur.getMemberCountAddColorToday());
                }
            }
        }
    }

    /**
     * ROI梯度表-累计数据汇总
     */
    public List<LinkAnalysisPO> getLinkRoiDetailSummaryAnalysis(LinkAnalysisPO params) {
        List<LinkAnalysisPO> list = launchAnalysisMapper.queryDetailSummaryAnalysisList(params);
        for (LinkAnalysisPO cur : list) {
            params.setLinkId(cur.getId());
            cur.setMemberCountAddColor(launchAnalysisMapper.queryAddColorMemberSum(params));
            // arpu 累计充值金额➗累计新增用户数
            cur.setArpu(DoubleUtil.divB4Zero(cur.getRechargeMoneySumAddAll(), cur.getMemberCountAddAll()));
            // arppu 累计充值金额➗累计充值用户数
            cur.setArppu(DoubleUtil.divB4Zero(cur.getRechargeMoneySumAddAll(), cur.getRechargeMemberCountAddAll()));
            // 用户成本 总成本➗累计新增用户数
            cur.setMemberCost(DoubleUtil.divB4Zero(cur.getAdMoneyConsume(), cur.getMemberCountAddAll()));
            // 付费用户成本 总成本➗累计付费用户数
            cur.setPayMemberCost(DoubleUtil.divB4Zero(cur.getAdMoneyConsume(), cur.getRechargeMemberCountAddAll()));
            // 回报率 累计充值金额➗总成本
            cur.setBackRate(DoubleUtil.divB4Zero(defaultIfNull(cur.getRechargeMoneySumAddAll(), BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), cur.getAdMoneyConsume()));
        }
        // groupByColum:统计维度：0=整体;1=链接;2=分销商;3=优化师;4=短剧;5=链路
        switch (params.getGroupByColum()) {
            case 1: {
                // 链接数据汇总
            }
            break;
            case 2: {
                // 分销商数据汇总
                Set<Integer> ids = new HashSet<>();
                list.forEach(cur -> ids.add(cur.getId()));

                FastRetailPO query = new FastRetailPO();
                query.setIds(StrUtil.join(ids));
                Map<Integer, FastRetailPO> map = retailService.queryRetailMap(query);
                for (LinkAnalysisPO cur : list) {
                    FastRetailPO retailPO = defaultIfNull(map.get(cur.getId()), new FastRetailPO());
                    cur.setRetailName(retailPO.getRetailName());
                    cur.setRetailType(retailPO.getRetailType());
                }
            }
            break;
            case 3: {
                // 优化师数据汇总
                Set<Integer> ids = new HashSet<>();
                list.forEach(cur -> ids.add(cur.getId()));

                FastUserPO query = new FastUserPO();
                query.setIds(StrUtil.join(ids));
                Map<Integer, String> nameMap = userService.queryUserNameMap(query);
                for (LinkAnalysisPO cur : list) {
                    cur.setAdvUserName(nameMap.get(cur.getId()));
                }

                FastRetailPO query2 = new FastRetailPO();
                ids.clear();
                list.forEach(cur -> ids.add(cur.getRetailId()));
                query2.setIds(StrUtil.join(ids));
                Map<Integer, FastRetailPO> map = retailService.queryRetailMap(query2);
                for (LinkAnalysisPO cur : list) {
                    FastRetailPO retailPO = defaultIfNull(map.get(cur.getRetailId()), new FastRetailPO());
                    cur.setRetailName(retailPO.getRetailName());
                }
            }
            break;
            case 4: {
                // 短剧数据汇总
                Set<Integer> ids = new HashSet<>();
                list.forEach(cur -> ids.add(cur.getId()));

                FastDramaPO query = new FastDramaPO();
                query.setIds(StrUtil.join(ids));
                Map<Integer, String> nameMap = dramaService.queryDramaNameMap(query);
                for (LinkAnalysisPO cur : list) {
                    cur.setDramaGrantRetailCount(dramaRetailService.queryCountByDramaId(cur.getId()));
                    cur.setDramaName(nameMap.get(cur.getId()));
                }
            }
            break;
            case 5: {
                // 链路数据汇总
                Set<Integer> ids = new HashSet<>();
                list.forEach(cur -> ids.add(cur.getId()));

                FastLinkRoadPO query = new FastLinkRoadPO();
                query.setIds(StrUtil.join(ids));
                Map<Integer, String> nameMap = linkRoadService.queryLinkRoadName(query);
                for (LinkAnalysisPO cur : list) {
                    cur.setRoadName(nameMap.get(cur.getId()));
                }
            }
            break;
        }
        // 查询创建的链接数
        if (params.getGroupByColum() > 1) {
            List<LinkAnalysisPO> linkCountList = launchAnalysisMapper.queryAllLinkCount(params);
            Map<Integer, LinkAnalysisPO> linkCountMap = new HashMap<>();
            linkCountList.forEach(cur -> linkCountMap.put(cur.getId(), cur));

            for (LinkAnalysisPO cur : list) {
                LinkAnalysisPO po = defaultIfNull(linkCountMap.get(cur.getId()), new LinkAnalysisPO());
                cur.setAllLinkCount(defaultIfNull(po.getAllLinkCount(), 0));
            }
        }
        return list;
    }

    /**
     * ROI梯度表-今日数据汇总
     */
    public List<LinkAnalysisPO> getLinkRoiDetailTodayAnalysis(LinkAnalysisPO params) {
        params.setStatisDate(DateUtil.getNowTime09Str());
        List<LinkAnalysisPO> list = launchAnalysisMapper.queryDetailTodayAnalysisList(params);
        for (LinkAnalysisPO cur : list) {
            cur.setMemberCountAddAll(cur.getMemberCountAdd());
            // params.setLinkId(cur.getLinkId());
            cur.setMemberCountAddColor(launchAnalysisMapper.queryAddColorMemberSum(params));
            List<LinkAnalysisPO> list4 = new ArrayList<>();// 4个子列表数据
            {
                // 今日新增充值
                params.setAddState(1);
                LinkAnalysisPO po = defaultIfNull(launchAnalysisMapper.queryTodayRechargeSum(params), new LinkAnalysisPO());
                po.setGroupByColum(1);
                // 客单价 充值金额➗充值人数
                po.setMemberSingleMoney(DoubleUtil.divB4Zero(defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO), po.getRechargeMemberCount()));
                // 付费率：这里的充值人数➗今日总新增用户数
                po.setMemberPayRate(DoubleUtil.divB4Zero(defaultIfNull(po.getRechargeMemberCount(), 0) * 100, cur.getMemberCountAdd()));
                list4.add(po);
            }
            {
                // 今日新增K币充值
                params.setAddState(1);
                params.setOrderType(1);
                LinkAnalysisPO po = defaultIfNull(launchAnalysisMapper.queryTodayRechargeSum(params), new LinkAnalysisPO());
                po.setGroupByColum(2);
                // 客单价 充值金额➗充值人数
                po.setMemberSingleMoney(DoubleUtil.divB4Zero(defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO), po.getRechargeMemberCount()));
                // 付费率：这里的充值人数➗今日总新增用户数
                po.setMemberPayRate(DoubleUtil.divB4Zero(defaultIfNull(po.getRechargeMemberCount(), 0) * 100, cur.getMemberCountAdd()));
                list4.add(po);
            }
            {
                // 今日新增VIP充值
                params.setAddState(1);
                params.setOrderType(2);
                LinkAnalysisPO po = defaultIfNull(launchAnalysisMapper.queryTodayRechargeSum(params), new LinkAnalysisPO());
                po.setGroupByColum(3);
                // 客单价 充值金额➗充值人数
                po.setMemberSingleMoney(DoubleUtil.divB4Zero(defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO), po.getRechargeMemberCount()));
                // 付费率：这里的充值人数➗今日总新增用户数
                po.setMemberPayRate(DoubleUtil.divB4Zero(defaultIfNull(po.getRechargeMemberCount(), 0) * 100, cur.getMemberCountAdd()));
                list4.add(po);
            }
            {
                // 今日总用户
                params.setAddState(1);
                params.setOrderType(null);
                LinkAnalysisPO po = defaultIfNull(launchAnalysisMapper.queryTodayRechargeSum(params), new LinkAnalysisPO());
                po.setGroupByColum(4);
                // 客单价 充值金额➗充值人数
                po.setMemberSingleMoney(DoubleUtil.divB4Zero(defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO), po.getRechargeMemberCount()));
                // 付费率：这里的充值人数➗今日总用户数
                po.setMemberPayRate(DoubleUtil.divB4Zero(defaultIfNull(po.getRechargeMemberCount(), 0) * 100, cur.getMemberCountAdd()));
                list4.add(po);
            }
            cur.setRechargeMoneySumAdd(list4.get(0).getRechargeMoneySum());
            // 4个子列表数据
            cur.setList(list4);
            // arpu 累计充值金额➗累计新增用户数
            cur.setArppu(DoubleUtil.divB4Zero(defaultIfNull(cur.getRechargeMoneySumAdd(), BigDecimal.ZERO), cur.getMemberCountAdd()));
            // arppu 累计充值金额➗累计充值用户数
            cur.setArppu(DoubleUtil.divB4Zero(defaultIfNull(cur.getRechargeMoneySumAdd(), BigDecimal.ZERO), cur.getRechargeMemberCountAddAll()));
            // 用户成本 总成本➗累计新增用户数
            cur.setMemberCost(DoubleUtil.divB4Zero(cur.getAdMoneyConsume(), cur.getMemberCountAdd()));
            // 付费用户成本 总成本➗累计付费用户数
            cur.setPayMemberCost(DoubleUtil.divB4Zero(cur.getAdMoneyConsume(), cur.getRechargeMemberCountAddAll()));
            // 回报率 累计充值金额➗总成本
            cur.setBackRate(DoubleUtil.divB4Zero(defaultIfNull(cur.getRechargeMoneySumAdd(), BigDecimal.ZERO).multiply(BigDecimalVar.BD_100), cur.getAdMoneyConsume()));
        }
        return list;
    }

    /**
     * 免费渠道链接ROI梯度表
     *
     * @param params
     * @param pageVO
     * @return
     */
    public ResultVO getLinkRoiCoreAllAnalysisFree(LinkAnalysisPO params, PageVO pageVO) {
        Integer groupByColum = defaultIfNull(params.getGroupByColum(), 0);
        Integer[] numD60MemberAllSummary = new Integer[61];                     // D0-D60当日产生收益的用户数
        BigDecimal[] numD60MoneyDaySummary = new BigDecimal[61];                // D0-D60当日产生的收益金额
        BigDecimal[] numD60MoneyAllSummary = new BigDecimal[61];                // D0-D60累计产生的收益金额
        BigDecimal[] numD60ROIDaySummary = new BigDecimal[61];                  // D0-D60当日的ROI
        BigDecimal[] numD60ROIAllSummary = new BigDecimal[61];                  // D0-D60累计的ROI
        if (groupByColum == 0) {  // 整体分析
            startPage(pageVO);
            List<LinkAnalysisPO> list = launchAnalysisMapper.queryFreeLinkROISummaryAnalysis(params);
            for (LinkAnalysisPO item : list) {
                Integer[] numD60MemberAll = new Integer[61];                     // D0-D60当日产生收益的用户数
                BigDecimal[] numD60MoneyDay = new BigDecimal[61];                // D0-D60当日产生的收益金额
                BigDecimal[] numD60MoneyAll = new BigDecimal[61];                // D0-D60累计产生的收益金额
                BigDecimal[] numD60ROIDay = new BigDecimal[61];                  // D0-D60当日的ROI
                BigDecimal[] numD60ROIAll = new BigDecimal[61];                  // D0-D60累计的ROI
                if (item.getAddConvertMemberD60() != null && item.getAddConvertMemberD60().length() > 0) {
                    String[] numD60MemberAllStrs = item.getAddConvertMemberD60().split("#");
                    for (String numD60MemberAllStr : numD60MemberAllStrs) {
                        String[] numD60MemberAllD60 = numD60MemberAllStr.split(",");
                        for (int i = 0; i < numD60MemberAllD60.length; i++) {
                            if (numD60MemberAllD60[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                                numD60MemberAll[i] = defaultIfNull(numD60MemberAll[i], 0) + Integer.parseInt(numD60MemberAllD60[i]);
                            } else {
                                numD60MemberAll[i] = null;
                            }
                        }
                    }
                }
                if (item.getAddConvertMoneyDayD60() != null && item.getAddConvertMoneyDayD60().length() > 0) {
                    String[] addConvertMoneyDayStrs = item.getAddConvertMoneyDayD60().split("#");
                    for (String addConvertMoneyDayStr : addConvertMoneyDayStrs) {
                        String[] addConvertMoneyDayD60 = addConvertMoneyDayStr.split(",");
                        for (int i = 0; i < addConvertMoneyDayD60.length; i++) {
                            if (addConvertMoneyDayD60[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                                numD60MoneyDay[i] = defaultIfNull(numD60MoneyDay[i], BigDecimal.ZERO).add(new BigDecimal(addConvertMoneyDayD60[i]));
                            } else {
                                numD60MoneyDay[i] = null;
                            }
                        }
                    }
                }

                numD60MoneyAll = querySumamry(numD60MoneyDay);                   // 累计金额
                BigDecimal totalIncome = BigDecimal.ZERO;                        // 新增用户总收入

                for (int k = 0; k < numD60MoneyDay.length; k++) {
                    totalIncome = DoubleUtil.addB(totalIncome, defaultIfNull(numD60MoneyDay[k], BigDecimal.ZERO));
                }

                BigDecimal adMoneyConsume = defaultIfNull(item.getAdMoneyConsume(), BigDecimal.ZERO);
                Integer convertMemberCount = defaultIfNull(item.getConvertMemberCountRecharge(), 0) +
                        defaultIfNull(item.getConvertMemeberCountAd(), 0); // 转化用户数
                Integer addMemberCount = defaultIfNull(item.getMemberCountAdd(), 0);
                // 累计总收入金额➗累计转化用户数
                BigDecimal arppu = BigDecimal.ZERO;
                if (convertMemberCount > 0) {
                    arppu = DoubleUtil.divB(totalIncome, new BigDecimal(convertMemberCount));
                }
                // 累计总收入金额➗累计消耗
                BigDecimal returnRatio = BigDecimal.ZERO;
                if (adMoneyConsume.compareTo(BigDecimal.ZERO) > 0) {
                    returnRatio = DoubleUtil.mulB(DoubleUtil.divB(totalIncome, adMoneyConsume), new BigDecimal(100));
                }
                // 累计总收入金额➗当日新增用户数
                BigDecimal LTV60D = BigDecimal.ZERO;
                if (addMemberCount > 0) {
                    LTV60D = DoubleUtil.divB(totalIncome, new BigDecimal(addMemberCount));
                }
                // 指当日新增用户中在当日充值+在当产生广告收益的用户数
                BigDecimal convertMemberCost = BigDecimal.ZERO;      // 转化用户成本
                if (convertMemberCount > 0) {
                    convertMemberCost = DoubleUtil.divB(adMoneyConsume, convertMemberCount);
                }

                // 消耗金额➗累计新增用户数
                BigDecimal addMemberCost = BigDecimal.ZERO;         // 新增用户成本
                if (addMemberCount > 0) {
                    addMemberCost = DoubleUtil.divB(adMoneyConsume, addMemberCount);
                }
                item.setAddMemberCost(addMemberCost);
                item.setConvertMemberCost(convertMemberCost);

                item.setArppu(arppu);
                item.setReturnRatio(returnRatio);
                item.setLTV60D(LTV60D);
                item.setConvertMemberCount(convertMemberCount);
                item.setIncome(totalIncome);
                item.setIncomeAd(totalIncome);

                if (adMoneyConsume.compareTo(BigDecimal.ZERO) > 0) {
                    for (int i = 0; i < 61; i++) {
                        if (numD60MoneyDay[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                            numD60ROIDay[i] = DoubleUtil.divB(DoubleUtil.mulB(numD60MoneyDay[i], BigDecimalVar.BD_100),
                                    item.getAdMoneyConsume());
                        } else {
                            numD60ROIDay[i] = null;
                        }
                        if (numD60MoneyAll[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                            numD60ROIAll[i] = DoubleUtil.divB(DoubleUtil.mulB(numD60MoneyAll[i], BigDecimalVar.BD_100),
                                    item.getAdMoneyConsume());
                        } else {
                            numD60ROIAll[i] = null;
                        }
                    }
                } else {
                    for (int i = 0; i < 61; i++) {
                        if (checkDateExpire(item.getStatisDate(), i)) {
                            numD60ROIDay[i] = BigDecimal.ZERO;
                            numD60ROIAll[i] = BigDecimal.ZERO;
                        }
                    }
                }
                item.setNumD60MemberAll(numD60MemberAll);
                item.setNumD60MoneyDay(DoubleUtil.pareseTwoZero(numD60MoneyDay));
                item.setNumD60MoneyAll(DoubleUtil.pareseTwoZero(numD60MoneyAll));
                item.setNumD60ROIDay(numD60ROIDay);
                item.setNumD60ROIAll(numD60ROIAll);
            }
            freeLinkAnalysisOrderBy(list, params);
            LinkAnalysisPO summary = launchAnalysisMapper.queryFreeLinkROISummaryAnalysisSummary(params);
            if (summary != null) {
                if (summary.getAddConvertMemberD60() != null && summary.getAddConvertMemberD60().length() > 0) {
                    String[] numD60MemberAllStrs = summary.getAddConvertMemberD60().split("#");
                    for (String numD60MemberAllStr : numD60MemberAllStrs) {
                        String[] numD60MemberAllD60 = numD60MemberAllStr.split(",");
                        for (int i = 0; i < numD60MemberAllD60.length; i++) {
                            if (numD60MemberAllD60[i] != null && checkDateExpire(params.getStatisDateS(), i)) {
                                numD60MemberAllSummary[i] = defaultIfNull(numD60MemberAllSummary[i], 0)
                                        + Integer.parseInt(numD60MemberAllD60[i]);
                            } else {
                                numD60MemberAllSummary[i] = null;
                            }
                        }
                    }
                }
                if (summary.getAddConvertMoneyDayD60() != null && summary.getAddConvertMoneyDayD60().length() > 0) {
                    String[] addConvertMoneyDayD60Strs = summary.getAddConvertMoneyDayD60().split("#");
                    for (String addConvertMoneyDayD60Str : addConvertMoneyDayD60Strs) {
                        String[] addConvertMoneyDayD60 = addConvertMoneyDayD60Str.split(",");
                        for (int i = 0; i < addConvertMoneyDayD60.length; i++) {
                            if (addConvertMoneyDayD60[i] != null && checkDateExpire(params.getStatisDateS(), i)) {
                                numD60MoneyDaySummary[i] = defaultIfNull(numD60MoneyDaySummary[i], BigDecimal.ZERO).add(new BigDecimal(addConvertMoneyDayD60[i]));
                            } else {
                                numD60MoneyDaySummary[i] = null;
                            }
                        }
                    }
                }
                numD60MoneyAllSummary = querySumamry(numD60MoneyDaySummary);               // 累计金额
                BigDecimal totalIncomeSummary = BigDecimal.ZERO;                           // 新增用户总收入
                for (int k = 0; k < 61; k++) {
                    totalIncomeSummary = DoubleUtil.addB(totalIncomeSummary, defaultIfNull(numD60MoneyDaySummary[k], BigDecimal.ZERO));
                }
                Integer convertMemberSummary = defaultIfNull(summary.getConvertMemberCountRecharge(), 0) +
                        defaultIfNull(summary.getConvertMemeberCountAd(), 0);
                BigDecimal adMoneyConsumeSummary = defaultIfNull(summary.getAdMoneyConsume(), BigDecimal.ZERO);
                Integer addMemberCountSummary = defaultIfNull(summary.getMemberCountAdd(), 0);
                ////累计总收入金额➗累计转化用户数
                BigDecimal arppuSummary = BigDecimal.ZERO;
                if (convertMemberSummary > 0) {
                    arppuSummary = DoubleUtil.divB(totalIncomeSummary, new BigDecimal(convertMemberSummary));
                }
                // 累计总收入金额➗累计消耗
                BigDecimal returnRatioSummary = BigDecimal.ZERO;
                if (adMoneyConsumeSummary.compareTo(BigDecimal.ZERO) > 0) {
                    returnRatioSummary = DoubleUtil.mulB(DoubleUtil.divB(totalIncomeSummary, adMoneyConsumeSummary), new BigDecimal(100));
                }
                // 累计总收入金额➗当日新增用户数
                BigDecimal LTV60DSummary = BigDecimal.ZERO;
                if (addMemberCountSummary > 0) {
                    LTV60DSummary = DoubleUtil.divB(totalIncomeSummary, new BigDecimal(addMemberCountSummary));
                }
                // 指当日新增用户中在当日充值+在当产生广告收益的用户数
                BigDecimal convertMemberCostSummary = BigDecimal.ZERO;      // 转化用户成本
                if (convertMemberSummary > 0) {
                    convertMemberCostSummary = DoubleUtil.divB(adMoneyConsumeSummary, convertMemberSummary);
                }

                // 消耗金额➗累计新增用户数
                BigDecimal addMemberCostSummary = BigDecimal.ZERO;         // 新增用户成本
                if (addMemberCountSummary > 0) {
                    addMemberCostSummary = DoubleUtil.divB(adMoneyConsumeSummary, addMemberCountSummary);
                }

                summary.setAddMemberCost(addMemberCostSummary);
                summary.setConvertMemberCost(convertMemberCostSummary);

                if (adMoneyConsumeSummary.compareTo(BigDecimal.ZERO) > 0) {
                    for (int i = 0; i < 61; i++) {
                        if (numD60MoneyDaySummary[i] != null) {
                            numD60ROIDaySummary[i] = DoubleUtil.divB(DoubleUtil.mulB(defaultIfNull(numD60MoneyDaySummary[i], BigDecimal.ZERO), BigDecimalVar.BD_100),
                                    summary.getAdMoneyConsume());
                        }
                        if (numD60MoneyAllSummary[i] != null) {
                            numD60ROIAllSummary[i] = DoubleUtil.divB(DoubleUtil.mulB(defaultIfNull(numD60MoneyAllSummary[i], BigDecimal.ZERO), BigDecimalVar.BD_100),
                                    summary.getAdMoneyConsume());
                        }
                    }
                } else {
                    for (int i = 0; i < 61; i++) {
                        if (checkDateExpire(params.getStatisDateS(), i)) {
                            numD60ROIDaySummary[i] = BigDecimal.ZERO;
                            numD60ROIAllSummary[i] = BigDecimal.ZERO;
                        }
                    }
                }
                summary.setIncome(totalIncomeSummary);
                summary.setIncomeAd(totalIncomeSummary);
                summary.setConvertMemberCount(convertMemberSummary);
                summary.setArppu(arppuSummary);
                summary.setReturnRatio(returnRatioSummary);
                summary.setLTV60D(LTV60DSummary);
                summary.setNumD60MemberAll(numD60MemberAllSummary);
                summary.setNumD60MoneyDay(DoubleUtil.pareseTwoZero(numD60MoneyDaySummary));
                summary.setNumD60MoneyAll(DoubleUtil.pareseTwoZero(numD60MoneyAllSummary));
                summary.setNumD60ROIDay(numD60ROIDaySummary);
                summary.setNumD60ROIAll(numD60ROIAllSummary);
            }
            Map<String, Object> result = getPageListData(list, pageVO);
            return ResultVO.summary(result, summary);
        } else { // 单渠道分析
            startPage(pageVO);
            List<LinkAnalysisPO> list = launchAnalysisMapper.queryFreeLinkROIAnalysis(params);
            for (LinkAnalysisPO item : list) {
                String encryptionId = encode(item.getLinkId());
                BigDecimal adMoneyConsume = defaultIfNull(item.getAdMoneyConsume(), BigDecimal.ZERO);
                Integer[] numD60MemberAll = new Integer[61];                     // D0-D60当日产生收益的用户数
                BigDecimal[] numD60MoneyDay = new BigDecimal[61];                // D0-D60当日产生的收益金额
                BigDecimal[] numD60MoneyAll = new BigDecimal[61];                // D0-D60累计产生的收益金额
                BigDecimal[] numD60ROIDay = new BigDecimal[61];                  // D0-D60当日的ROI
                BigDecimal[] numD60ROIAll = new BigDecimal[61];                  // D0-D60累计的ROI
                if (item.getAddConvertMemberD60() != null && item.getAddConvertMemberD60().length() > 0) {
                    String[] numD60MemberAllStrs = item.getAddConvertMemberD60().split("#");
                    for (String numD60MemberAllStr : numD60MemberAllStrs) {
                        String[] numD60MemberAllD60 = numD60MemberAllStr.split(",");
                        for (int i = 0; i < numD60MemberAllD60.length; i++) {
                            if (numD60MemberAllD60[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                                numD60MemberAll[i] = defaultIfNull(numD60MemberAll[i], 0) + Integer.parseInt(numD60MemberAllD60[i]);
                            } else {
                                numD60MemberAll[i] = null;
                            }
                        }
                    }
                }
                if (item.getAddConvertMoneyDayD60() != null && item.getAddConvertMoneyDayD60().length() > 0) {
                    String[] addConvertMoneyDayStrs = item.getAddConvertMoneyDayD60().split("#");
                    for (String addConvertMoneyDayStr : addConvertMoneyDayStrs) {
                        String[] addConvertMoneyDayD60 = addConvertMoneyDayStr.split(",");
                        for (int i = 0; i < addConvertMoneyDayD60.length; i++) {
                            if (addConvertMoneyDayD60[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                                numD60MoneyDay[i] = defaultIfNull(numD60MoneyDay[i], BigDecimal.ZERO).add(new BigDecimal(addConvertMoneyDayD60[i]));
                            } else {
                                numD60MoneyDay[i] = null;
                            }
                        }
                    }
                }
                numD60MoneyAll = querySumamry(numD60MoneyDay);                   // 累计金额
                BigDecimal totalIncome = BigDecimal.ZERO;                        // 新增用户总收入
                for (int k = 0; k < numD60MoneyDay.length; k++) {
                    totalIncome = DoubleUtil.addB(totalIncome, defaultIfNull(numD60MoneyDay[k], BigDecimal.ZERO));
                }
                if (adMoneyConsume.compareTo(BigDecimal.ZERO) > 0) {
                    for (int i = 0; i < 61; i++) {
                        if (numD60MoneyDay[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                            numD60ROIDay[i] = DoubleUtil.divB(DoubleUtil.mulB(defaultIfNull(numD60MoneyDay[i], BigDecimal.ZERO), BigDecimalVar.BD_100),
                                    adMoneyConsume);
                            numD60ROIAll[i] = DoubleUtil.divB(DoubleUtil.mulB(defaultIfNull(numD60MoneyAll[i], BigDecimal.ZERO), BigDecimalVar.BD_100),
                                    adMoneyConsume);
                        } else {
                            numD60ROIDay[i] = null;
                            numD60ROIAll[i] = null;
                        }
                    }
                } else {
                    for (int i = 0; i < 61; i++) {
                        if (checkDateExpire(item.getStatisDate(), i)) {
                            numD60ROIDay[i] = BigDecimal.ZERO;
                            numD60ROIAll[i] = BigDecimal.ZERO;
                        }
                    }
                }
                Integer convertMemberCount = defaultIfNull(item.getConvertMemberCount(), 0); // 转化用户数
                Integer addMemberCount = defaultIfNull(item.getMemberCountAddAll(), 0);
                // 累计总收入金额➗累计转化用户数
                BigDecimal arppu = BigDecimal.ZERO;
                if (convertMemberCount > 0) {
                    arppu = DoubleUtil.divB(totalIncome, new BigDecimal(convertMemberCount));
                }
                // 累计总收入金额➗累计消耗
                BigDecimal returnRatio = BigDecimal.ZERO;
                if (adMoneyConsume.compareTo(BigDecimal.ZERO) > 0) {
                    returnRatio = DoubleUtil.mulB(DoubleUtil.divB(totalIncome, adMoneyConsume), new BigDecimal(100));
                }
                // 累计总收入金额➗当日新增用户数
                BigDecimal LTV60D = BigDecimal.ZERO;
                if (addMemberCount > 0) {
                    LTV60D = DoubleUtil.divB(totalIncome, new BigDecimal(addMemberCount));
                }
                // 指当日新增用户中在当日充值+在当产生广告收益的用户数
                BigDecimal convertMemberCost = BigDecimal.ZERO;      // 转化用户成本
                if (convertMemberCount > 0) {
                    convertMemberCost = DoubleUtil.divB(adMoneyConsume, convertMemberCount);
                }

                // 消耗金额➗累计新增用户数
                BigDecimal addMemberCost = BigDecimal.ZERO;         // 新增用户成本
                if (addMemberCount > 0) {
                    addMemberCost = DoubleUtil.divB(adMoneyConsume, addMemberCount);
                }
                item.setAddMemberCost(addMemberCost);
                item.setConvertMemberCost(convertMemberCost);

                item.setArppu(arppu);
                item.setReturnRatio(returnRatio);
                item.setLTV60D(LTV60D);
                item.setEncryptionId(encryptionId);
                item.setIncome(totalIncome);
                item.setNumD60MemberAll(numD60MemberAll);
                item.setNumD60MoneyDay(DoubleUtil.pareseTwoZero(numD60MoneyDay));
                item.setNumD60MoneyAll(DoubleUtil.pareseTwoZero(numD60MoneyAll));
                item.setNumD60ROIDay(numD60ROIDay);
                item.setNumD60ROIAll(numD60ROIAll);
            }
            freeLinkAnalysisOrderBy(list, params);
            LinkAnalysisPO summary = launchAnalysisMapper.queryFreeLinkROIAnalysisSummary(params);
            if (summary != null) {
                BigDecimal adMoneyConsume = defaultIfNull(summary.getAdMoneyConsume(), BigDecimal.ZERO);
                Integer convertMemberCount = defaultIfNull(summary.getConvertMemberCount(), 0); // 转化用户数
                Integer addMemberCount = defaultIfNull(summary.getMemberCountAddAll(), 0);

                if (summary.getAddConvertMemberD60() != null && summary.getAddConvertMemberD60().length() > 0) {
                    String[] numD60MemberAllStrs = summary.getAddConvertMemberD60().split("#");
                    for (String numD60MemberAllStr : numD60MemberAllStrs) {
                        String[] numD60MemberAllD60 = numD60MemberAllStr.split(",");
                        for (int i = 0; i < numD60MemberAllD60.length; i++) {
                            if (numD60MemberAllD60[i] != null && checkDateExpire(params.getStatisDateS(), i)) {
                                numD60MemberAllSummary[i] = defaultIfNull(numD60MemberAllSummary[i], 0) + Integer.parseInt(numD60MemberAllD60[i]);
                            } else {
                                numD60MemberAllSummary[i] = null;
                            }
                        }
                    }
                }
                if (summary.getAddConvertMoneyDayD60() != null && summary.getAddConvertMoneyDayD60().length() > 0) {
                    String[] addConvertMoneyDayStrs = summary.getAddConvertMoneyDayD60().split("#");
                    for (String addConvertMoneyDayStr : addConvertMoneyDayStrs) {
                        String[] addConvertMoneyDayD60 = addConvertMoneyDayStr.split(",");
                        for (int i = 0; i < addConvertMoneyDayD60.length; i++) {
                            if (addConvertMoneyDayD60[i] != null && checkDateExpire(params.getStatisDateS(), i)) {
                                numD60MoneyDaySummary[i] = defaultIfNull(numD60MoneyDaySummary[i], BigDecimal.ZERO).add(new BigDecimal(addConvertMoneyDayD60[i]));
                            } else {
                                numD60MoneyDaySummary[i] = null;
                            }
                        }
                    }
                }

                numD60MoneyAllSummary = querySumamry(numD60MoneyDaySummary);               // 累计金额
                BigDecimal totalIncomeSummary = BigDecimal.ZERO;                           // 新增用户总收入
                for (int k = 0; k < 61; k++) {
                    totalIncomeSummary = DoubleUtil.addB(totalIncomeSummary, defaultIfNull(numD60MoneyDaySummary[k], BigDecimal.ZERO));
                }
                // 累计总收入金额➗累计转化用户数
                BigDecimal arppu = BigDecimal.ZERO;
                if (convertMemberCount > 0) {
                    arppu = DoubleUtil.divB(totalIncomeSummary, new BigDecimal(convertMemberCount));
                }
                // 累计总收入金额➗累计消耗
                BigDecimal returnRatio = BigDecimal.ZERO;
                if (adMoneyConsume.compareTo(BigDecimal.ZERO) > 0) {
                    returnRatio = DoubleUtil.mulB(DoubleUtil.divB(totalIncomeSummary, adMoneyConsume), new BigDecimal(100));
                }
                // 累计总收入金额➗当日新增用户数
                BigDecimal LTV60D = BigDecimal.ZERO;
                if (addMemberCount > 0) {
                    LTV60D = DoubleUtil.divB(totalIncomeSummary, new BigDecimal(addMemberCount));
                }

                // 指当日新增用户中在当日充值+在当产生广告收益的用户数
                BigDecimal convertMemberCostSummary = BigDecimal.ZERO;      // 转化用户成本
                if (convertMemberCount > 0) {
                    convertMemberCostSummary = DoubleUtil.divB(adMoneyConsume, convertMemberCount);
                }

                // 消耗金额➗累计新增用户数
                BigDecimal addMemberCostSummary = BigDecimal.ZERO;         // 新增用户成本
                if (addMemberCount > 0) {
                    addMemberCostSummary = DoubleUtil.divB(adMoneyConsume, addMemberCount);
                }

                summary.setAddMemberCost(addMemberCostSummary);
                summary.setConvertMemberCost(convertMemberCostSummary);

                if (adMoneyConsume.compareTo(BigDecimal.ZERO) > 0) {
                    for (int i = 0; i < 61; i++) {
                        if (numD60MoneyDaySummary[i] != null && checkDateExpire(params.getStatisDateS(), i)) {
                            numD60ROIDaySummary[i] = DoubleUtil.divB(DoubleUtil.mulB(defaultIfNull(numD60MoneyDaySummary[i], BigDecimal.ZERO), BigDecimalVar.BD_100),
                                    adMoneyConsume);
                            numD60ROIAllSummary[i] = DoubleUtil.divB(DoubleUtil.mulB(defaultIfNull(numD60MoneyAllSummary[i], BigDecimal.ZERO), BigDecimalVar.BD_100),
                                    adMoneyConsume);
                        } else {
                            numD60ROIDaySummary[i] = null;
                            numD60ROIAllSummary[i] = null;
                        }
                    }
                } else {
                    for (int i = 0; i < 61; i++) {
                        if (checkDateExpire(params.getStatisDateS(), i)) {
                            numD60ROIDaySummary[i] = BigDecimal.ZERO;
                            numD60ROIAllSummary[i] = BigDecimal.ZERO;
                        }
                    }
                }
                summary.setIncome(totalIncomeSummary);
                summary.setIncomeAd(totalIncomeSummary);
                summary.setArppu(arppu);
                summary.setReturnRatio(returnRatio);
                summary.setLTV60D(LTV60D);
                summary.setNumD60MemberAll(numD60MemberAllSummary);
                summary.setNumD60MoneyDay(DoubleUtil.pareseTwoZero(numD60MoneyDaySummary));
                summary.setNumD60MoneyAll(DoubleUtil.pareseTwoZero(numD60MoneyAllSummary));
                summary.setNumD60ROIDay(numD60ROIDaySummary);
                summary.setNumD60ROIAll(numD60ROIAllSummary);
            }
            Map<String, Object> result = getPageListData(list, pageVO);
            return ResultVO.summary(result, summary);
        }
    }

    /**
     * 处理排序问题
     *
     * @param list
     * @param params
     */
    public void freeLinkAnalysisOrderBy(List<LinkAnalysisPO> list, LinkAnalysisPO params) {
        if (list != null && list.size() > 0) {
            switch (params.getSortType()) {
                case 1: { // 1=有效链接数
                    if (params.getSortOrder() == 1) {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getValidLinkCount).reversed());
                    } else {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getValidLinkCount));
                    }
                }
                break;
                case 2: { // 2=投放消耗
                    if (params.getSortOrder() == 1) {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getAdMoneyConsume).reversed());
                    } else {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getAdMoneyConsume));
                    }
                }
                break;
                case 3: {// 3=新增用户
                    if (params.getSortOrder() == 1) {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getMemberCountAdd).reversed());
                    } else {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getMemberCountAdd));
                    }
                }
                break;
                case 4: {// 4=染色用户
                    if (params.getSortOrder() == 1) {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getMemberCountAddColor).reversed());
                    } else {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getMemberCountAddColor));
                    }
                }
                break;
                case 5: {// 5=转化用户
                    if (params.getSortOrder() == 1) {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getConvertMemberCount).reversed());
                    } else {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getConvertMemberCount));
                    }
                }
                break;
                case 6: {// 6=用户成本
                    if (params.getSortOrder() == 1) {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getAddMemberCost).reversed());
                    } else {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getAddMemberCost));
                    }
                }
                break;
                case 7: {// 7=ARPPU
                    if (params.getSortOrder() == 1) {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getArppu).reversed());
                    } else {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getArppu));
                    }
                }
                break;
                case 8: {// 8=累计回报率
                    if (params.getSortOrder() == 1) {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getReturnRatio).reversed());
                    } else {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getReturnRatio));
                    }
                }
                break;
                case 9: {// 9=60DLTV
                    if (params.getSortOrder() == 1) {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getLTV60D).reversed());
                    } else {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getLTV60D));
                    }
                }
                case 10: {// 10=累计收入金额
                    if (params.getSortOrder() == 1) {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getIncome).reversed());
                    } else {
                        list.sort(Comparator.comparing(LinkAnalysisPO::getIncome));
                    }
                }
                break;
            }
        }
    }

    public BigDecimal[] querySumamry(BigDecimal[] a) {
        List<BigDecimal> bList = new ArrayList();
        for (int k = 0; k < a.length; k++) {
            if (k == 0) {
                bList.add(k, a[k]);
            }
            if (k >= 1) {
                if (a[k] != null) {
                    bList.add(k, defaultIfNull(bList.get(k - 1), BigDecimal.ZERO).add(defaultIfNull(a[k], BigDecimal.ZERO)));
                } else {
                    bList.add(k, null);
                }
            }
        }
        BigDecimal[] array = bList.stream().toArray(BigDecimal[]::new);
        return array;
    }

    public boolean checkDateExpire(String statisDate, Integer i) {
        Date addDate = DateUtil.addDays(DateUtil.format09(statisDate), i);
        if (addDate.before(DateUtil.endOfDay())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}
