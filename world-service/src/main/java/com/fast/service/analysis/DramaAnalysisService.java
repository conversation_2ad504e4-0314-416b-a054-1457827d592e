/*
 * Powered By fast.up
 */
package com.fast.service.analysis;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticVar;
import com.fast.mapper.analysis.DramaAnalysisMapper;
import com.fast.mapper.datalake.DataLakeOrderConsumeMapper;
import com.fast.mapper.promote.FastLinkMapper;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.po.drama.FastDramaPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.promote.FastLinkQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 短剧分析
 *
 * <AUTHOR>
 */
@Service
public class DramaAnalysisService extends BaseService {

    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private DramaAnalysisMapper dramaAnalysisMapper;
    @Autowired
    private FastLinkMapper linkMapper;
    @Autowired
    private DataLakeOrderConsumeMapper dataLakeOrderConsumeMapper;

    /**
     * 用户观看和充值分析[2024-02新增看广告分析]
     */
    public ResultVO<?> getWatchRechargeAnalysisList(DramaAnalysisPO params) {
        int allWatchMemberCount = 0;// 总观看人数
        // 查询每个剧的观看人数
        List<DramaAnalysisPO> list = dramaAnalysisMapper.queryWatchMemberCountListOrderBy(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.success(getDefaultListData());
        }
        Map<Integer, FastDramaPO> dramaMap = dramaService.queryAllDramaMap();
        for (DramaAnalysisPO cur : list) {
            FastDramaPO dramaPO = defaultIfNull(dramaMap.get(cur.getDramaId()), new FastDramaPO());
            cur.setDramaName(dramaPO.getDramaName());
            cur.setReleaseDate(dramaPO.getReleaseDate());
            allWatchMemberCount += cur.getWatchMemberCount();
        }
        // 计算观看人数占比
        for (DramaAnalysisPO cur : list) {
            cur.setWatchMemberCountRate(DoubleUtil.divB(cur.getWatchMemberCount() * 100, allWatchMemberCount));
        }

        int allRechargeMemberCount = 0;// 总充值人数
        {
            // 查询每个剧的充值人数
            List<DramaAnalysisPO> rechargeList = dramaAnalysisMapper.queryRechargeMemberCountListOrderBy(params);

            for (DramaAnalysisPO cur : rechargeList) {
                FastDramaPO dramaPO = defaultIfNull(dramaMap.get(cur.getDramaId()), new FastDramaPO());
                cur.setDramaName(dramaPO.getDramaName());
                cur.setReleaseDate(dramaPO.getReleaseDate());
                allRechargeMemberCount += cur.getRechargeMemberCount();
            }
            // 计算充值人数占比
            for (DramaAnalysisPO cur : rechargeList) {
                cur.setRechargeMemberCountRate(DoubleUtil.divB(cur.getRechargeMemberCount() * 100, allRechargeMemberCount));
            }
            Map<Integer, DramaAnalysisPO> rechargeMap = new HashMap<>();
            rechargeList.forEach(cur -> rechargeMap.put(cur.getDramaId(), cur));

            // 组装充值人数数据
            for (DramaAnalysisPO cur : list) {
                DramaAnalysisPO po = rechargeMap.get(cur.getDramaId());
                if (po == null) {
                    po = new DramaAnalysisPO();
                }
                cur.setRechargeMemberCountRate(defaultIfNull(po.getRechargeMemberCountRate(), BigDecimal.ZERO));
                cur.setRechargeMemberCount(defaultIfNull(po.getRechargeMemberCount(), StaticVar.ZERO));
            }
        }
        int allAdIncomeMemberCount = 0;// 总广告收益人数
        {
            // 查询每个剧的广告收益人数
            List<DramaAnalysisPO> adIncomeList = dramaAnalysisMapper.queryAdIncomeMemberCountListOrderBy(params);

            for (DramaAnalysisPO cur : adIncomeList) {
                FastDramaPO dramaPO = defaultIfNull(dramaMap.get(cur.getDramaId()), new FastDramaPO());
                cur.setDramaName(dramaPO.getDramaName());
                cur.setReleaseDate(dramaPO.getReleaseDate());
                allAdIncomeMemberCount += cur.getAdIncomeMemberCount();
            }
            // 计算广告收益人数占比
            for (DramaAnalysisPO cur : adIncomeList) {
                cur.setAdIncomeMemberCountRate(DoubleUtil.divB(cur.getAdIncomeMemberCount() * 100, allAdIncomeMemberCount));
            }
            Map<Integer, DramaAnalysisPO> adIncomeMap = new HashMap<>();
            adIncomeList.forEach(cur -> adIncomeMap.put(cur.getDramaId(), cur));

            // 组装数据
            for (DramaAnalysisPO cur : list) {
                DramaAnalysisPO po = adIncomeMap.get(cur.getDramaId());
                if (po == null) {
                    po = new DramaAnalysisPO();
                }
                cur.setAdIncomeMemberCountRate(defaultIfNull(po.getAdIncomeMemberCountRate(), BigDecimal.ZERO));
                cur.setAdIncomeMemberCount(defaultIfNull(po.getAdIncomeMemberCount(), StaticVar.ZERO));
            }
        }
        // 倒序排序(由大->小)
        // 排序字段:1=充值人数;2=观看人数
        if (params.getSortType() == 1) {
            list.sort(Comparator.comparing(DramaAnalysisPO::getRechargeMemberCount).thenComparing(DramaAnalysisPO::getDramaId).reversed());
        } else if (params.getSortType() == 2) {
            list.sort(Comparator.comparing(DramaAnalysisPO::getWatchMemberCount).thenComparing(DramaAnalysisPO::getDramaId).reversed());
        } else if (params.getSortType() == 3) {
            list.sort(Comparator.comparing(DramaAnalysisPO::getAdIncomeMemberCount).thenComparing(DramaAnalysisPO::getDramaId).reversed());
        }
        ResultVO<List<DramaAnalysisPO>> vo = ResultVO.success(list);
        vo.setSummary(new JSONObject()
                .fluentPut("allWatchMemberCount", allWatchMemberCount)
                .fluentPut("allRechargeMemberCount", allRechargeMemberCount)
                .fluentPut("allAdIncomeMemberCount", allAdIncomeMemberCount));
        return vo;
    }

    /**
     * 用户K币消费分析
     */
    public ResultVO<?> getCoinConsumeAnalysisList(DramaAnalysisPO params) {
        List<DramaAnalysisPO> rechargeList = dramaAnalysisMapper.queryCoinConsumeListOrderBy(params);
        if (CollUtil.isEmpty(rechargeList)) {
            return ResultVO.success(getDefaultListData());
        }
        Map<Integer, FastDramaPO> dramaMap = dramaService.queryAllDramaMap();
        for (DramaAnalysisPO cur : rechargeList) {
            FastDramaPO dramaPO = defaultIfNull(dramaMap.get(cur.getDramaId()), new FastDramaPO());
            cur.setDramaName(dramaPO.getDramaName());
            cur.setReleaseDate(dramaPO.getReleaseDate());
        }
        return ResultVO.success(rechargeList);
    }

    /**
     * 短剧数据明细列表
     */
    public List<DramaAnalysisPO> getDramaDetailList(DramaAnalysisPO params) {
        // 查询观看人数
        List<DramaAnalysisPO> list = dramaAnalysisMapper.queryWatchMemberCountList2(params);
        Map<Integer, FastDramaPO> dramaMap = dramaService.queryAllDramaMap();

        if (CollUtil.isEmpty(list)) {
            return list;
        } else {
            List<Integer> dramaIds = new ArrayList<>();
            for (DramaAnalysisPO cur : list) {
                FastDramaPO dramaPO = defaultIfNull(dramaMap.get(cur.getDramaId()), new FastDramaPO());
                cur.setDramaName(dramaPO.getDramaName());
                cur.setReleaseDate(dramaPO.getReleaseDate());
                dramaIds.add(cur.getDramaId());
            }
            params.setDramaIds(StrUtil.joinNoRepeat(dramaIds));
            // 定义需要组装的临时数据
            List<DramaAnalysisPO> dataList2, dataList3, dataList4, dataList5, dataList6;

            // 开始组装数据
            // 查询K币总消耗数
            dataList2 = dataLakeOrderConsumeMapper.queryCoinConsumeListPlus(params);

            Map<Integer, DramaAnalysisPO> map2 = new HashMap<>();
            if (CollUtil.hasContent(dataList2)) {
                for (DramaAnalysisPO cur : dataList2) {
                    map2.put(cur.getDramaId(), cur);
                }
            }
            for (DramaAnalysisPO po : list) {
                DramaAnalysisPO coinConsume = map2.get(po.getDramaId());
                if (coinConsume == null) {
                    coinConsume = new DramaAnalysisPO();
                }
                Integer data = defaultIfNull(coinConsume.getCoinConsume(), 0);
                po.setCoinConsume(data);

                data = defaultIfNull(coinConsume.getCoinGiveConsume(), 0);
                po.setCoinGiveConsume(data);
            }

            // 查询广告消耗金额
            FastLinkQueryVO query = new FastLinkQueryVO();
            query.setDramaId(params.getDramaId());
            query.setIds(params.getLinkIds());
            query.setMiniIds(params.getMiniIds());
            query.setRetailIds(params.getRetailIds());
            query.setBeginTime(DateUtil.format09(params.getCreateTimeS()));
            query.setEndTime(DateUtil.format09(params.getCreateTimeE()));
            query.setOfficialIds(params.getOfficialIds());
            query.setContentType(params.getContentType());

            List<FastLinkQueryVO> linkStatics = linkMapper.queryListDramaCost(query);

            Map<Integer, FastLinkQueryVO> mapLink = new HashMap<>();
            if (CollUtil.hasContent(linkStatics)) {
                for (FastLinkQueryVO po : linkStatics) {
                    mapLink.put(po.getDramaId(), po);
                }
            }
            for (DramaAnalysisPO po : list) {
                FastLinkQueryVO data = mapLink.get(po.getDramaId());
                if (data == null) {
                    data = new FastLinkQueryVO();
                }
                po.setAdMoneyConsume(defaultIfNull(data.getAdMoneyConsume(), BigDecimal.ZERO));
                po.setAdMoneyConsumePay(defaultIfNull(data.getAdMoneyConsumePay(), BigDecimal.ZERO));
                po.setAdMoneyConsumeFree(defaultIfNull(data.getAdMoneyConsumeFree(), BigDecimal.ZERO));
            }

            // 查询新增充值金额(付费+免费)
            params.setAddState(1);
            dataList6 = dramaAnalysisMapper.queryRechargeMoneyList(params);

            Map<Integer, DramaAnalysisPO> map6 = new HashMap<>();
            if (CollUtil.hasContent(dataList6)) {
                for (DramaAnalysisPO cur : dataList6) {
                    map6.put(cur.getDramaId(), cur);
                }
            }
            for (DramaAnalysisPO po : list) {
                DramaAnalysisPO data = map6.get(po.getDramaId());
                if (data == null) {
                    data = new DramaAnalysisPO();
                }
                po.setRechargeMoneySumAddPay(defaultIfNull(data.getRechargeMoneySumPay(), BigDecimal.ZERO));
                po.setRechargeMoneySumAddFree(defaultIfNull(data.getRechargeMoneySumFree(), BigDecimal.ZERO));
                po.setRechargeMoneyProfitSumAddPay(defaultIfNull(data.getRechargeMoneyProfitSumPay(), BigDecimal.ZERO));
                po.setRechargeMoneyProfitSumAddFree(defaultIfNull(data.getRechargeMoneyProfitSumFree(), BigDecimal.ZERO));
                // po.setRechargeMemberCountAdd(defaultIfNull(analysisPO.getRechargeMemberCount(), 0));
            }
            params.setAddState(null);

            // 查询完播人数
            params.setPlayState(null);
            if (params.getWatchTimeS() != null) {
                params.setFinishDateS(DateUtil.format06Int(params.getWatchTimeS()));
            }
            if (params.getWatchTimeE() != null) {
                params.setFinishDateE(DateUtil.format06Int(params.getWatchTimeE()));
            }
            dataList3 = dramaAnalysisMapper.queryWatchFinishMemberCountListNew(params);

            Map<Integer, Integer> map3 = new HashMap<>();
            if (CollUtil.hasContent(dataList3)) {
                for (DramaAnalysisPO cur : dataList3) {
                    map3.put(cur.getDramaId(), cur.getWatchFinishMemberCount());
                }
            }
            for (DramaAnalysisPO po : list) {
                Integer data = defaultIfNull(map3.get(po.getDramaId()), 0);
                po.setWatchFinishMemberCount(data);
            }

            params.setPlayState(null);
            // 查询全部充值金额/人数
            dataList4 = dramaAnalysisMapper.queryRechargeMoneyList(params);

            Map<Integer, DramaAnalysisPO> map4 = new HashMap<>();
            if (CollUtil.hasContent(dataList4)) {
                for (DramaAnalysisPO cur : dataList4) {
                    map4.put(cur.getDramaId(), cur);
                }
            }
            for (DramaAnalysisPO po : list) {
                DramaAnalysisPO data = map4.get(po.getDramaId());
                if (data == null) {
                    data = new DramaAnalysisPO();
                }
                po.setRechargeMoneySumPay(defaultIfNull(data.getRechargeMoneySumPay(), BigDecimal.ZERO));
                po.setRechargeMoneySumFree(defaultIfNull(data.getRechargeMoneySumFree(), BigDecimal.ZERO));
                po.setRechargeMoneyProfitSumPay(defaultIfNull(data.getRechargeMoneyProfitSumPay(), BigDecimal.ZERO));
                po.setRechargeMoneyProfitSumFree(defaultIfNull(data.getRechargeMoneyProfitSumFree(), BigDecimal.ZERO));
                // po.setRechargeMemberCount(defaultIfNull(data.getRechargeMemberCount(), 0));
            }

            // 对空数据赋值默认数据
            doNullData(list);

            // 排序
            sortDrama(params, list);
        }
        return list;
    }

    /**
     * 短剧单日明细表
     */
    public List<DramaAnalysisPO> getDramaDayDetailList(DramaAnalysisPO params) {
        // 查询观看人数
        List<DramaAnalysisPO> list = dramaAnalysisMapper.queryWatchMemberCountDayList(params);
        if (CollUtil.isEmpty(list)) {
            return list;
        } else {
            List<Integer> dramaIds = new ArrayList<>();
            for (DramaAnalysisPO po : list) {
                dramaIds.add(po.getDramaId());
            }
            params.setDramaIds(StrUtil.joinNoRepeat(dramaIds));
            // 定义需要组装的临时数据
            List<DramaAnalysisPO> dataList2, dataList3, dataList4, dataList5, dataList6;

            // 开始组装数据
            // 查询K币总消耗数
            dataList2 = dramaAnalysisMapper.queryCoinConsumeDayList(params);
            Map<String, DramaAnalysisPO> map2 = new HashMap<>();
            if (CollUtil.hasContent(dataList2)) {
                for (DramaAnalysisPO po : dataList2) {
                    map2.put(po.getDataDay(), po);
                }
            }
            for (DramaAnalysisPO po : list) {
                DramaAnalysisPO coinConsume = map2.get(po.getDataDay());
                if (coinConsume == null) {
                    coinConsume = new DramaAnalysisPO();
                }
                po.setCoinConsume(coinConsume.getCoinConsume());
                po.setCoinGiveConsume(coinConsume.getCoinGiveConsume());
            }

            // 查询广告消耗金额
            FastLinkQueryVO query = new FastLinkQueryVO();
            query.setDramaId(params.getDramaId());
            query.setIds(params.getLinkIds());
            query.setMiniIds(params.getMiniIds());
            query.setRetailIds(params.getRetailIds());
            query.setBeginTime(DateUtil.format09(params.getWatchTimeS()));
            query.setEndTime(DateUtil.format09(params.getWatchTimeE()));
            query.setOfficialIds(params.getOfficialIds());

            List<FastLinkQueryVO> linkStatics = linkMapper.queryListDramaCostDay(query);
            Map<String, FastLinkQueryVO> mapLink = new HashMap<>();
            if (CollUtil.hasContent(linkStatics)) {
                for (FastLinkQueryVO po : linkStatics) {
                    mapLink.put(po.getDataDay(), po);
                }
            }
            for (DramaAnalysisPO po : list) {
                FastLinkQueryVO data = mapLink.get(po.getDataDay());
                if (data == null) {
                    data = new FastLinkQueryVO();
                }
                po.setAdMoneyConsume(defaultIfNull(data.getAdMoneyConsume(), BigDecimal.ZERO));
                po.setAdMoneyConsumePay(defaultIfNull(data.getAdMoneyConsumePay(), BigDecimal.ZERO));
                po.setAdMoneyConsumeFree(defaultIfNull(data.getAdMoneyConsumeFree(), BigDecimal.ZERO));
            }

            // 查询新增充值金额(付费+免费)
            params.setAddState(1);
            dataList6 = dramaAnalysisMapper.queryRechargeMoneyDayListNew(params);
            Map<String, DramaAnalysisPO> map6 = new HashMap<>();
            if (CollUtil.hasContent(dataList6)) {
                for (DramaAnalysisPO po : dataList6) {
                    map6.put(po.getDataDay(), po);
                }
            }
            for (DramaAnalysisPO po : list) {
                DramaAnalysisPO data = map6.get(po.getDataDay());
                if (data == null) {
                    data = new DramaAnalysisPO();
                }
                po.setRechargeMoneySumAddPay(defaultIfNull(data.getRechargeMoneySumPay(), BigDecimal.ZERO));
                po.setRechargeMoneySumAddFree(defaultIfNull(data.getRechargeMoneySumFree(), BigDecimal.ZERO));
                po.setRechargeMoneyProfitSumAddPay(defaultIfNull(data.getRechargeMoneyProfitSumPay(), BigDecimal.ZERO));
                po.setRechargeMoneyProfitSumAddFree(defaultIfNull(data.getRechargeMoneyProfitSumFree(), BigDecimal.ZERO));
                // po.setRechargeMemberCountAdd(defaultIfNull(data.getRechargeMemberCount(), 0));
            }
            params.setAddState(null);

            // 查询完播人数
            params.setPlayState(null);
            if (params.getWatchTimeS() != null) {
                params.setFinishDateS(DateUtil.format06Int(params.getWatchTimeS()));
            }
            if (params.getWatchTimeE() != null) {
                params.setFinishDateE(DateUtil.format06Int(params.getWatchTimeE()));
            }
            dataList3 = dramaAnalysisMapper.queryWatchFinishMemberCountDayListV2(params);
            Map<String, Integer> map3 = new HashMap<>();
            if (CollUtil.hasContent(dataList3)) {
                for (DramaAnalysisPO po : dataList3) {
                    map3.put(po.getDataDay(), po.getWatchFinishMemberCount());
                }
            }
            for (DramaAnalysisPO po : list) {
                Integer data = defaultIfNull(map3.get(po.getDataDay()), 0);
                po.setWatchFinishMemberCount(data);
            }

            params.setPlayState(null);
            // 查询充值金额/人数
            dataList4 = dramaAnalysisMapper.queryRechargeMoneyDayListNew(params);
            Map<String, DramaAnalysisPO> map4 = new HashMap<>();
            if (CollUtil.hasContent(dataList4)) {
                for (DramaAnalysisPO po : dataList4) {
                    map4.put(po.getDataDay(), po);
                }
            }
            for (DramaAnalysisPO po : list) {
                DramaAnalysisPO data = map4.get(po.getDataDay());
                if (data == null) {
                    data = new DramaAnalysisPO();
                }
                po.setRechargeMoneySumPay(defaultIfNull(data.getRechargeMoneySumPay(), BigDecimal.ZERO));
                po.setRechargeMoneySumFree(defaultIfNull(data.getRechargeMoneySumFree(), BigDecimal.ZERO));
                po.setRechargeMoneyProfitSumPay(defaultIfNull(data.getRechargeMoneyProfitSumPay(), BigDecimal.ZERO));
                po.setRechargeMoneyProfitSumFree(defaultIfNull(data.getRechargeMoneyProfitSumFree(), BigDecimal.ZERO));
            }

            // 对空数据赋值默认数据
            doNullData(list);

            // 排序
            sortDay(params, list);
        }
        return list;
    }

    /**
     * 排序
     *
     * @param params
     * @param list
     */
    private static void sortDrama(DramaAnalysisPO params, List<DramaAnalysisPO> list) {
        switch (params.getSortType()) {
            case 1:// 观看人数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchMemberCount).thenComparing(DramaAnalysisPO::getDramaId).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchMemberCount).thenComparing(DramaAnalysisPO::getDramaId));
                }
                break;
            case 2:// K币总消耗数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getCoinConsume).thenComparing(DramaAnalysisPO::getDramaId).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getCoinConsume).thenComparing(DramaAnalysisPO::getDramaId));
                }
                break;
            case 3:// 完播人数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchFinishMemberCount).thenComparing(DramaAnalysisPO::getDramaId).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchFinishMemberCount).thenComparing(DramaAnalysisPO::getDramaId));
                }
                break;
            case 4:// 广告消耗
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getAdMoneyConsume).thenComparing(DramaAnalysisPO::getDramaId).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getAdMoneyConsume).thenComparing(DramaAnalysisPO::getDramaId));
                }
                break;
            case 5:// 新增ROI
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRoiAdd).thenComparing(DramaAnalysisPO::getDramaId).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRoiAdd).thenComparing(DramaAnalysisPO::getDramaId));
                }
                break;
            case 6:// 付费充值
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRechargeMoneySumPay).thenComparing(DramaAnalysisPO::getDramaId).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRechargeMoneySumPay).thenComparing(DramaAnalysisPO::getDramaId));
                }
                break;
            case 7:// 免费充值
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRechargeMoneySumFree).thenComparing(DramaAnalysisPO::getDramaId).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRechargeMoneySumFree).thenComparing(DramaAnalysisPO::getDramaId));
                }
                break;
            case 8:// 免费广告收入
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getAdIncomeMoneySum).thenComparing(DramaAnalysisPO::getDramaId).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getAdIncomeMoneySum).thenComparing(DramaAnalysisPO::getDramaId));
                }
                break;
        }
    }

    /**
     * 排序
     *
     * @param params
     * @param list
     */
    private static void sortDay(DramaAnalysisPO params, List<DramaAnalysisPO> list) {
        switch (params.getSortType()) {
            case 0:// 统计日期正序
                // 正序排序(由小->大)
                list.sort(Comparator.comparing(DramaAnalysisPO::getDataDay));
                break;
            case 1:// 观看人数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchMemberCount).thenComparing(DramaAnalysisPO::getDataDay).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchMemberCount).thenComparing(DramaAnalysisPO::getDataDay));
                }
                break;
            case 2:// K币总消耗数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getCoinConsume).thenComparing(DramaAnalysisPO::getDataDay).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getCoinConsume).thenComparing(DramaAnalysisPO::getDataDay));
                }
                break;
            case 3:// 完播人数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchFinishMemberCount).thenComparing(DramaAnalysisPO::getDataDay).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchFinishMemberCount).thenComparing(DramaAnalysisPO::getDataDay));
                }
                break;
            case 4:// 广告消耗
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getAdMoneyConsume).thenComparing(DramaAnalysisPO::getDataDay).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getAdMoneyConsume).thenComparing(DramaAnalysisPO::getDataDay));
                }
                break;
            case 5:// 新增ROI
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRoiAdd).thenComparing(DramaAnalysisPO::getDataDay).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRoiAdd).thenComparing(DramaAnalysisPO::getDataDay));
                }
                break;
            case 6:// 付费充值
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRechargeMoneySumPay).thenComparing(DramaAnalysisPO::getDataDay).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRechargeMoneySumPay).thenComparing(DramaAnalysisPO::getDataDay));
                }
                break;
            case 7:// 免费充值
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRechargeMoneySumFree).thenComparing(DramaAnalysisPO::getDataDay).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRechargeMoneySumFree).thenComparing(DramaAnalysisPO::getDataDay));
                }
                break;
            case 8:// 免费广告收入
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getAdIncomeMoneySum).thenComparing(DramaAnalysisPO::getDataDay).reversed());
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getAdIncomeMoneySum).thenComparing(DramaAnalysisPO::getDataDay));
                }
                break;
        }
    }

    /**
     * 对空数据赋值默认数据
     *
     * @param list
     */
    private static void doNullData(List<DramaAnalysisPO> list) {
        for (DramaAnalysisPO po : list) {
            if (po.getWatchMemberCount() == null) {
                po.setWatchMemberCount(0);
            }
            if (po.getCoinConsume() == null) {
                po.setCoinConsume(0);
            }
            if (po.getWatchFinishMemberCount() == null) {
                po.setWatchFinishMemberCount(0);
            }
            if (po.getRechargeMoneySumPay() == null) {
                po.setRechargeMoneySumPay(BigDecimal.ZERO);
            }
            if (po.getRechargeMoneySumFree() == null) {
                po.setRechargeMoneySumFree(BigDecimal.ZERO);
            }
            if (po.getRechargeMoneyProfitSumPay() == null) {
                po.setRechargeMoneyProfitSumPay(BigDecimal.ZERO);
            }
            if (po.getRechargeMoneyProfitSumFree() == null) {
                po.setRechargeMoneyProfitSumFree(BigDecimal.ZERO);
            }
            if (po.getRechargeMemberCount() == null) {
                po.setRechargeMemberCount(0);
            }
            if (po.getAdMoneyConsume() == null) {
                po.setAdMoneyConsume(BigDecimal.ZERO);
            }
            if (po.getRechargeMoneyProfitSumAddFree() == null) {
                po.setRechargeMoneyProfitSumAddFree(BigDecimal.ZERO);
            }
            if (po.getRechargeMoneyProfitSumAddPay() == null) {
                po.setRechargeMoneyProfitSumAddPay(BigDecimal.ZERO);
            }
            if (po.getAdIncomeMoneySumAdd() == null) {
                po.setAdIncomeMoneySumAdd(BigDecimal.ZERO);
            }
            po.setRechargeMoneyProfitSumAdd(DoubleUtil.addB(po.getRechargeMoneyProfitSumAddFree(), po.getRechargeMoneyProfitSumAddPay()));
            // 计算ROI
            /*if (po.getRoi() == null) {
                po.setRoi(DoubleUtil.divB4Zero(po.getRechargeMoneyProfitSum().multiply(BigDecimalVar.BD_100), po.getAdMoneyConsume()));
            }*/
            // 计算新增ROI
            if (po.getRoiAdd() == null) {
                po.setRoiAdd(DoubleUtil.divB4Zero(po.getRechargeMoneyProfitSumAdd().multiply(BigDecimalVar.BD_100), po.getAdMoneyConsume()));
            }
            if (po.getRoiAddPay() == null) {
                po.setRoiAddPay(DoubleUtil.divB4Zero(po.getRechargeMoneyProfitSumAddPay().multiply(BigDecimalVar.BD_100), po.getAdMoneyConsumePay()));
            }
            if (po.getRoiAddFree() == null) {
                po.setRoiAddFree(DoubleUtil.divB4Zero(DoubleUtil.addB(po.getRechargeMoneyProfitSumAddFree(), po.getAdIncomeMoneySumAdd()).multiply(BigDecimalVar.BD_100), po.getAdMoneyConsumeFree()));
            }
            // 计算完播率
            if (po.getWatchFinishRate() == null) {
                po.setWatchFinishRate(DoubleUtil.divB4Zero(po.getWatchFinishMemberCount() * 100, po.getWatchMemberCount()));
            }
            po.setEncryptionId(encode(po.getDramaId()));
        }
    }
}
