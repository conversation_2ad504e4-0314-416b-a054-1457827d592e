/*
 * Powered By fast.up
 */
package com.fast.service.analysis;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticVar;
import com.fast.mapper.analysis.DramaDetailAnalysisMapper;
import com.fast.mapper.datalake.*;
import com.fast.mapper.fee.FastFeeRuleMapper;
import com.fast.mapper.promote.FastLinkMapper;
import com.fast.mapper.retail.FastRetailMapper;
import com.fast.mapper.user.FastUserMapper;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.po.fee.FastFeeRulePO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.po.member.FastMemberUnlockDramaPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaSeriesService;
import com.fast.service.drama.FastDramaService;
import com.fast.utils.*;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.SessionVO;
import com.fast.vo.drama.FastDramaSeriesVO;
import com.fast.vo.drama.FastDramaVO;
import com.fast.vo.fee.FastFeeRuleVO;
import com.fast.vo.promote.FastLinkQueryVO;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 短剧整体分析
 *
 * <AUTHOR>
 */
@Service
public class DramaDetailAnalysisService extends BaseService {

    @Autowired
    private DramaDetailAnalysisMapper detailAnalysisMapper;
    @Autowired
    private FastLinkMapper linkMapper;
    @Autowired
    private FastRetailMapper retailMapper;
    @Autowired
    private FastUserMapper fastUserMapper;
    @Autowired
    private FastFeeRuleMapper feeRuleMapper;
    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastDramaSeriesService seriesService;
    @Autowired
    private DataLakeRecentLogMapper dataLakeRecentLogMapper;
    @Autowired
    private DataLakeUnlockDramaMapper dataLakeUnlockDramaMapper;
    @Autowired
    private DataLakeRechargeMapper dataLakeRechargeMapper;
    @Autowired
    private DataLakeOrderConsumeMapper dataLakeOrderConsumeMapper;
    @Autowired
    private DataLakeStatisLinkMapper dataLakeStatisLinkMapper;
    @Autowired
    private DataLakeMemberLinkMapper dataLakeMemberLinkMapper;

    /**
     * 核心数据概览
     */
    public List<JSONObject> getDramaDetailCoreDataAnalysisList(DramaAnalysisPO params, SessionVO sessionVO) {
        Date nowStart = DateUtil.beginOfDay();
        Date nowEnd = DateUtil.endOfDay();

        Date yesStart = DateUtil.addDays(nowStart, -1);
        Date yesEnd = DateUtil.addDays(nowEnd, -1);

        Long currentTimeMillis = 0L;

        List<JSONObject> list = new ArrayList<>();
        // 用户注册人数分析
        {
            currentTimeMillis = System.currentTimeMillis();
            JSONObject registryData = queryRegistryCountData(params);
            list.add(registryData);
            log.info("短剧核心数据分析dramaId:{},用户注册人数分析耗时:{}", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis));
        }
        // 观看人数分析
        int watchToday, watchYes, watchTotal;
        {
            currentTimeMillis = System.currentTimeMillis();
            JSONObject watchData = queryWatchCountData(params);
            log.info("短剧核心数据分析dramaId:{},用户观看数据分析耗时:{}", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis));
            list.add(watchData);

            watchToday = watchData.getIntValue("todayData");
            watchYes = watchData.getIntValue("oldData");
            watchTotal = watchData.getIntValue("totalData");
            // currentTimeMillis = System.currentTimeMillis();
            // FastMemberRecentLogPO recentQuery = new FastMemberRecentLogPO();
            // recentQuery.setCreateTimeS(nowStart);
            // recentQuery.setCreateTimeE(nowEnd);
            // recentQuery.setDramaId(params.getDramaId());
            // recentQuery.setLinkIds(params.getLinkIds());
            // recentQuery.setRetailIds(params.getRetailIds());
            // recentQuery.setAdvUserIds(params.getAdvUserIds());
            // recentQuery.setMiniIds(params.getMiniIds());
            // recentQuery.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
            // Integer todayData = dataLakeRecentLogMapper.queryWatchMemberCountByDramaId(recentQuery);
            //
            // recentQuery.setCreateTimeS(yesStart);
            // recentQuery.setCreateTimeE(yesEnd);
            // int yes = dataLakeRecentLogMapper.queryWatchMemberCountByDramaId(recentQuery);
            //
            // recentQuery.setCreateTimeS(null);
            // recentQuery.setCreateTimeE(null);
            // int totalData = dataLakeRecentLogMapper.queryWatchMemberCountByDramaId(recentQuery);
            //
            // BigDecimal dataRate;
            // if (todayData - yes == 0) {
            //     dataRate = BigDecimal.ZERO;
            // } else if (yes == 0) {
            //     dataRate = BigDecimalVar.BD_100;
            // } else {
            //     dataRate = DoubleUtil.divB((todayData - yes) * 100, yes);
            // }
            //
            // JSONObject data = new JSONObject();
            // data.fluentPut("type", 2).fluentPut("todayData", todayData).fluentPut("dataRate", dataRate).fluentPut("totalData", totalData);
            //
            // list.add(data);
            // watchToday = todayData;
            // watchYes = yes;
            // watchTotal = totalData;
            // log.info("dramaId={},观看人数分析耗时{}秒",params.getDramaId(),(System.currentTimeMillis()-currentTimeMillis)/1000);
        }
        // K币消费值分析
        {
            currentTimeMillis = System.currentTimeMillis();
            params.setCreateTimeS(nowStart);
            params.setCreateTimeE(nowEnd);
            long todayData = defaultIfNull(dataLakeOrderConsumeMapper.queryCoinConsumeList(params), 0L);

            params.setCreateTimeS(yesStart);
            params.setCreateTimeE(yesEnd);

            long yes = queryCoinConsumeListByRedis(params, StaticVar.DREAM_ANALYSIS_DATA_CONSUME_YESTERDAY + params.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(params)));

            params.setCreateTimeS(null);
            params.setCreateTimeE(yesEnd);
            long totalData = queryCoinConsumeListByRedis(params, StaticVar.DREAM_ANALYSIS_DATA_CONSUME_HISTORY + params.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(params))) + todayData;
            params.setCreateTimeE(null);

            BigDecimal dataRate;
            if (todayData - yes == 0) {
                dataRate = BigDecimal.ZERO;
            } else if (yes == 0) {
                dataRate = BigDecimalVar.BD_100;
            } else {
                dataRate = DoubleUtil.divB((todayData - yes) * 100, yes);
            }

            JSONObject data = new JSONObject();
            data.fluentPut("type", 3).fluentPut("todayData", todayData).fluentPut("dataRate", dataRate).fluentPut("totalData", totalData);

            list.add(data);
            log.info("dramaId={},K币消费值分析耗时{}秒", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis) / 1000);
            log.info("短剧核心数据分析dramaId:{},K币消费值分析耗时:{}", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis));
        }
        // 完播率
        {
            JSONObject data = queryWatchFinishCountData(params, watchToday, watchYes, watchTotal);
            list.add(data);

            // currentTimeMillis = System.currentTimeMillis();
            // FastDramaVO drama = dramaService.queryInfoByRedis(params.getDramaId());
            // if (drama == null || drama.getSeriesNumUpdate() == null) {
            //     // throw new MyException("当前短剧[" + params.getDramaId() + "]没有配置更新集数");
            //     return null;
            // }
            // params.setWatchTimeS(nowStart);
            // params.setWatchTimeE(nowEnd);
            // params.setSeriesNum(drama.getSeriesNumUpdate());//最后一集
            // params.setPlayState(1);
            // BigDecimal todayData = DoubleUtil.divB4Zero(dataLakeRecentLogMapper.queryWatchFinishMemberCountV2(params) * 100, watchToday);
            // params.setWatchTimeS(yesStart);
            // params.setWatchTimeE(yesEnd);
            // BigDecimal yes = DoubleUtil.divB4Zero(dataLakeRecentLogMapper.queryWatchFinishMemberCountV2(params) * 100, watchYes);
            // params.setWatchTimeS(null);
            // params.setWatchTimeE(null);
            // BigDecimal totalData = DoubleUtil.divB4Zero(dataLakeRecentLogMapper.queryWatchFinishMemberCountV2(params) * 100, watchTotal);
            // BigDecimal dataRate;
            // if (DoubleUtil.subB(todayData, yes).compareTo(BigDecimal.ZERO) == 0) {
            //     dataRate = BigDecimal.ZERO;
            // } else if (yes.compareTo(BigDecimal.ZERO) == 0) {
            //     dataRate = BigDecimalVar.BD_100;
            // } else {
            //     dataRate = DoubleUtil.divB(DoubleUtil.mulB(DoubleUtil.subB(todayData, yes), BigDecimalVar.BD_100), yes);
            // }
            // JSONObject data = new JSONObject();
            // data.fluentPut("type", 4).fluentPut("todayData", todayData).fluentPut("dataRate", dataRate).fluentPut("totalData", totalData);
            // list.add(data);
            // log.info("dramaId={},完播率分析耗时{}秒",params.getDramaId(),(System.currentTimeMillis()-currentTimeMillis)/1000);
            // params.setSeriesNum(null);
        }
        // 广告消耗金额分析
        BigDecimal consumeMoneyToday, consumeMoneyYes, consumeMoneyTotal;
        {
            currentTimeMillis = System.currentTimeMillis();
            FastLinkQueryVO query = new FastLinkQueryVO();
            query.setDramaId(params.getDramaId());
            query.setRetailIds(params.getRetailIds());
            query.setIds(params.getLinkIds());
            query.setMiniIds(params.getMiniIds());
            query.setAdvUserIds(params.getAdvUserIds());
            query.setBeginTime(DateUtil.format07(nowStart));
            query.setEndTime(DateUtil.format07(nowEnd));
            query.setRetailTypes("1,2"); // 仅查询自代投
            query.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
            BigDecimal todayData = defaultIfNull(dataLakeStatisLinkMapper.queryListDramaCostSum(query), BigDecimal.ZERO);

            query.setBeginTime(DateUtil.format07(yesStart));
            query.setEndTime(DateUtil.format07(yesEnd));
            BigDecimal yes = queryListDramaCostSumRedis(query, StaticVar.DREAM_ANALYSIS_DATA_COST_YESTERDAY + query.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(query)));

            params.setCreateTimeS(null);
            params.setCreateTimeE(null);
            query.setBeginTime(null);
            query.setEndTime(DateUtil.format07(yesEnd));
            BigDecimal totalData = queryListDramaCostSumRedis(query, StaticVar.DREAM_ANALYSIS_DATA_COST_HISTORY + query.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(query))).add(todayData);

            BigDecimal dataRate;
            if (DoubleUtil.subB(todayData, yes).compareTo(BigDecimal.ZERO) == 0) {
                dataRate = BigDecimal.ZERO;
            } else if (yes.compareTo(BigDecimal.ZERO) == 0) {
                dataRate = BigDecimalVar.BD_100;
            } else {
                dataRate = DoubleUtil.divB(DoubleUtil.mulB(DoubleUtil.subB(todayData, yes), BigDecimalVar.BD_100), yes);
            }

            JSONObject data = new JSONObject();
            data.fluentPut("type", 5).fluentPut("todayData", todayData).fluentPut("dataRate", dataRate).fluentPut("totalData", totalData);

            list.add(data);
            consumeMoneyToday = todayData;
            consumeMoneyYes = yes;
            consumeMoneyTotal = totalData;
            log.info("dramaId={},广告消耗金额分析耗时{}秒", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis) / 1000);
            log.info("短剧核心数据分析dramaId:{},广告消耗金额分析耗时:{}", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis));
        }
        // 新增充值金额/人数
        BigDecimal rechargeMoneyTodayAdd, rechargeMoneyYesAdd, rechargeMoneyTotalAdd;
        {
            currentTimeMillis = System.currentTimeMillis();
            // 自代投-新增充值金额
            BigDecimal usAddTodayRechargeMoney = BigDecimal.ZERO;
            BigDecimal usAddYesRechMoney = BigDecimal.ZERO;
            BigDecimal usAddTotalRechargeMoney = BigDecimal.ZERO;
            // 自代投-新增充值人数
            Integer usAddTodayRechargMemberCount = 0;
            Integer usAddYesMemberRechargCount = 0;
            Integer usAddTotalMemberRechargCount = 0;
            // 分销商-新增充值金额
            BigDecimal aloneAddTodayRechargeMoney = BigDecimal.ZERO;
            BigDecimal aloneAddYesRechargeMoney = BigDecimal.ZERO;
            BigDecimal aloneAddTotalRechargeMoney = BigDecimal.ZERO;
            // 分销商-新增充值人数
            Integer aloneAddTodayRechargMemberCount = 0;
            Integer aloneAddYesMemberRechargCount = 0;
            Integer aloneAddTotalMemberRechargCount = 0;

            DramaAnalysisPO addRechargeParam = new DramaAnalysisPO();
            addRechargeParam.setDramaId(params.getDramaId());
            addRechargeParam.setLinkIds(params.getLinkIds());
            addRechargeParam.setRetailIds(params.getRetailIds());
            addRechargeParam.setAdvUserIds(params.getAdvUserIds());
            addRechargeParam.setMiniIds(params.getMiniIds());
            addRechargeParam.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
            addRechargeParam.setAddState(1);
            addRechargeParam.setCreateTimeS(nowStart);
            addRechargeParam.setCreateTimeE(nowEnd);
            DramaAnalysisPO todayData = defaultIfNull(dataLakeRechargeMapper.queryRechargeDataListGroupByRetailType(addRechargeParam), new DramaAnalysisPO());
            usAddTodayRechargeMoney = todayData.getUsRechargeMoney();
            usAddTodayRechargMemberCount = todayData.getUsRecharegeMemberCount();
            aloneAddTodayRechargeMoney = todayData.getAloneRechargeMoney();
            aloneAddTodayRechargMemberCount = todayData.getAloneRechargeMemberCount();

            addRechargeParam.setCreateTimeS(yesStart);
            addRechargeParam.setCreateTimeE(yesEnd);
            DramaAnalysisPO yes = queryRechargeDataListGroupByRetailTypeRedis(addRechargeParam, StaticVar.DREAM_ANALYSIS_DATA_RECHARGE_YESTERDAY + addRechargeParam.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(addRechargeParam)));
            usAddYesRechMoney = yes.getUsRechargeMoney();
            usAddYesMemberRechargCount = yes.getUsRecharegeMemberCount();
            aloneAddYesRechargeMoney = yes.getAloneRechargeMoney();
            aloneAddYesMemberRechargCount = yes.getAloneRechargeMemberCount();

            addRechargeParam.setCreateTimeS(null);
            addRechargeParam.setCreateTimeE(yesEnd);
            DramaAnalysisPO totalData = queryRechargeDataListGroupByRetailTypeRedis(addRechargeParam, StaticVar.DREAM_ANALYSIS_DATA_RECHARGE_HISTORY + addRechargeParam.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(addRechargeParam)));
            usAddTotalRechargeMoney = totalData.getUsRechargeMoney().add(usAddTodayRechargeMoney);
            usAddTotalMemberRechargCount = totalData.getUsRecharegeMemberCount() + usAddTodayRechargMemberCount;
            aloneAddTotalRechargeMoney = totalData.getAloneRechargeMoney().add(aloneAddTodayRechargeMoney);
            aloneAddTotalMemberRechargCount = totalData.getAloneRechargeMemberCount() + aloneAddTodayRechargMemberCount;

            BigDecimal dataRate1; // 自代投充值金额
            if (DoubleUtil.subB(usAddTodayRechargeMoney, usAddYesRechMoney).compareTo(BigDecimal.ZERO) == 0) {
                dataRate1 = BigDecimal.ZERO;
            } else if (usAddYesRechMoney.compareTo(BigDecimal.ZERO) == 0) {
                dataRate1 = BigDecimalVar.BD_100;
            } else {
                dataRate1 = DoubleUtil.divB(DoubleUtil.mulB(DoubleUtil.subB(usAddTodayRechargeMoney, usAddYesRechMoney), BigDecimalVar.BD_100), usAddYesRechMoney);
            }
            JSONObject data1 = new JSONObject();
            data1.fluentPut("type", 16).fluentPut("todayData", usAddTodayRechargeMoney)
                    .fluentPut("dataRate", dataRate1)
                    .fluentPut("totalData", usAddTotalRechargeMoney);
            list.add(data1);

            BigDecimal dataRate2; // 分销商充值金额
            if (DoubleUtil.subB(aloneAddTodayRechargeMoney, aloneAddYesRechargeMoney).compareTo(BigDecimal.ZERO) == 0) {
                dataRate2 = BigDecimal.ZERO;
            } else if (aloneAddYesRechargeMoney.compareTo(BigDecimal.ZERO) == 0) {
                dataRate2 = BigDecimalVar.BD_100;
            } else {
                dataRate2 = DoubleUtil.divB(DoubleUtil.mulB(DoubleUtil.subB(aloneAddTodayRechargeMoney, aloneAddYesRechargeMoney),
                        BigDecimalVar.BD_100), aloneAddYesRechargeMoney);
            }
            JSONObject data2 = new JSONObject();
            data2.fluentPut("type", 24).fluentPut("todayData", aloneAddTodayRechargeMoney)
                    .fluentPut("dataRate", dataRate2)
                    .fluentPut("totalData", aloneAddTotalRechargeMoney);
            list.add(data2);

            Integer todayAddRechargeMemberCount = usAddTodayRechargMemberCount + aloneAddTodayRechargMemberCount;
            Integer yesAddRechargeMemberCount = usAddYesMemberRechargCount + aloneAddYesMemberRechargCount;
            Integer totalAddRechargeMemberCount = usAddTotalMemberRechargCount + aloneAddTotalMemberRechargCount;
            BigDecimal dataRate3; // 充值人数
            if ((todayAddRechargeMemberCount - yesAddRechargeMemberCount) == 0) {
                dataRate3 = BigDecimal.ZERO;
            } else if (yesAddRechargeMemberCount == 0) {
                dataRate3 = BigDecimalVar.BD_100;
            } else {
                dataRate3 = new BigDecimal((todayAddRechargeMemberCount - yesAddRechargeMemberCount) * 100 / yesAddRechargeMemberCount);
            }
            JSONObject data3 = new JSONObject();
            data3.fluentPut("type", 17).fluentPut("todayData", todayAddRechargeMemberCount)
                    .fluentPut("dataRate", dataRate3)
                    .fluentPut("totalData", totalAddRechargeMemberCount);
            list.add(data3);

            rechargeMoneyTodayAdd = usAddTodayRechargeMoney;
            rechargeMoneyYesAdd = usAddYesRechMoney;
            rechargeMoneyTotalAdd = usAddTotalRechargeMoney;
            log.info("dramaId={},新增充值金额/人数分析耗时{}秒", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis) / 1000);
            log.info("短剧核心数据分析dramaId:{},新增充值金额/人数分析耗时:{}", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis));
        }
        // 用户充值金额/人数分析(总)
        {
            currentTimeMillis = System.currentTimeMillis();
            // 自代投-充值金额
            BigDecimal usTodayRechargeMoney = BigDecimal.ZERO;
            BigDecimal usYesRechargeMoney = BigDecimal.ZERO;
            BigDecimal usTotalRechargeMoney = BigDecimal.ZERO;
            // 自代投-充值人数
            Integer usTodayRechargMemberCount = 0;
            Integer usYesMemberRechargCount = 0;
            Integer usTotalMemberRechargCount = 0;
            // 分销商-充值金额
            BigDecimal aloneTodayRechargeMoney = BigDecimal.ZERO;
            BigDecimal aloneYesRechargeMoney = BigDecimal.ZERO;
            BigDecimal aloneTotalRechargeMoney = BigDecimal.ZERO;
            // 分销商-充值人数
            Integer aloneTodayRechargMemberCount = 0;
            Integer aloneYesMemberRechargCount = 0;
            Integer aloneTotalMemberRechargCount = 0;

            // 总充值金额-自代投
            DramaAnalysisPO queryParam = new DramaAnalysisPO();
            queryParam.setDramaId(params.getDramaId());
            queryParam.setLinkIds(params.getLinkIds());
            queryParam.setRetailIds(params.getRetailIds());
            queryParam.setAdvUserIds(params.getAdvUserIds());
            queryParam.setMiniIds(params.getMiniIds());
            queryParam.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
            queryParam.setCreateTimeS(nowStart);
            queryParam.setCreateTimeE(nowEnd);
            DramaAnalysisPO todayData = defaultIfNull(dataLakeRechargeMapper.queryRechargeDataListGroupByRetailType(queryParam), new DramaAnalysisPO());
            usTodayRechargeMoney = todayData.getUsRechargeMoney();
            usTodayRechargMemberCount = todayData.getUsRecharegeMemberCount();
            aloneTodayRechargeMoney = todayData.getAloneRechargeMoney();
            aloneTodayRechargMemberCount = todayData.getAloneRechargeMemberCount();

            queryParam.setCreateTimeS(yesStart);
            queryParam.setCreateTimeE(yesEnd);

            DramaAnalysisPO yes = queryRechargeDataListGroupByRetailTypeRedis(queryParam, StaticVar.DREAM_ANALYSIS_DATA_RECHARGE_YESTERDAY + queryParam.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(queryParam)));
            usYesRechargeMoney = yes.getUsRechargeMoney();
            usYesMemberRechargCount = yes.getUsRecharegeMemberCount();
            aloneYesRechargeMoney = yes.getAloneRechargeMoney();
            aloneYesMemberRechargCount = yes.getAloneRechargeMemberCount();

            queryParam.setCreateTimeS(null);
            queryParam.setCreateTimeE(yesEnd);
            DramaAnalysisPO totalData = queryRechargeDataListGroupByRetailTypeRedis(queryParam, StaticVar.DREAM_ANALYSIS_DATA_RECHARGE_HISTORY + queryParam.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(queryParam)));
            usTotalRechargeMoney = totalData.getUsRechargeMoney().add(usTodayRechargeMoney);
            usTotalMemberRechargCount = totalData.getUsRecharegeMemberCount() + usTodayRechargMemberCount;
            aloneTotalRechargeMoney = totalData.getAloneRechargeMoney().add(aloneTodayRechargeMoney);
            aloneTotalMemberRechargCount = totalData.getAloneRechargeMemberCount() + aloneTodayRechargMemberCount;

            BigDecimal dataRate1; // 自代投充值金额
            if (DoubleUtil.subB(usTodayRechargeMoney, usYesRechargeMoney).compareTo(BigDecimal.ZERO) == 0) {
                dataRate1 = BigDecimal.ZERO;
            } else if (usYesRechargeMoney.compareTo(BigDecimal.ZERO) == 0) {
                dataRate1 = BigDecimalVar.BD_100;
            } else {
                dataRate1 = DoubleUtil.divB(DoubleUtil.mulB(DoubleUtil.subB(usTodayRechargeMoney, usYesRechargeMoney), BigDecimalVar.BD_100), usYesRechargeMoney);
            }
            JSONObject data1 = new JSONObject();
            data1.fluentPut("type", 6).fluentPut("todayData", usTodayRechargeMoney)
                    .fluentPut("dataRate", dataRate1)
                    .fluentPut("totalData", usTotalRechargeMoney);
            list.add(data1);

            BigDecimal dataRate2; // 分销商充值金额
            if (DoubleUtil.subB(aloneTodayRechargeMoney, aloneYesRechargeMoney).compareTo(BigDecimal.ZERO) == 0) {
                dataRate2 = BigDecimal.ZERO;
            } else if (aloneYesRechargeMoney.compareTo(BigDecimal.ZERO) == 0) {
                dataRate2 = BigDecimalVar.BD_100;
            } else {
                dataRate2 = DoubleUtil.divB(DoubleUtil.mulB(DoubleUtil.subB(aloneTodayRechargeMoney, aloneYesRechargeMoney),
                        BigDecimalVar.BD_100), aloneYesRechargeMoney);
            }
            JSONObject data2 = new JSONObject();
            data2.fluentPut("type", 23).fluentPut("todayData", aloneTodayRechargeMoney)
                    .fluentPut("dataRate", dataRate2)
                    .fluentPut("totalData", aloneTotalRechargeMoney);
            list.add(data2);

            Integer todayRechargeMemberCount = usTodayRechargMemberCount + aloneTodayRechargMemberCount;
            Integer yesRechargeMemberCount = usYesMemberRechargCount + aloneYesMemberRechargCount;
            Integer totalRechargeMemberCount = usTotalMemberRechargCount + aloneTotalMemberRechargCount;
            BigDecimal dataRate3; // 充值人数
            if ((todayRechargeMemberCount - yesRechargeMemberCount) == 0) {
                dataRate3 = BigDecimal.ZERO;
            } else if (yesRechargeMemberCount == 0) {
                dataRate3 = BigDecimalVar.BD_100;
            } else {
                dataRate3 = new BigDecimal((todayRechargeMemberCount - yesRechargeMemberCount) * 100 / yesRechargeMemberCount);
            }
            JSONObject data3 = new JSONObject();
            data3.fluentPut("type", 7).fluentPut("todayData", todayRechargeMemberCount)
                    .fluentPut("dataRate", dataRate3)
                    .fluentPut("totalData", totalRechargeMemberCount);
            list.add(data3);
            params.setAddState(null);
            log.info("dramaId={},用户充值金额/人数分析(总)耗时{}秒", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis) / 1000);
            log.info("短剧核心数据分析dramaId:{},用户充值金额/人数分析(总)分析耗时:{}", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis));
        }
        // ROI分析
        {
            currentTimeMillis = System.currentTimeMillis();
            log.info("新增充值金额rechargeMoneyTodayAdd:{},consumeMoneyToday:{}", rechargeMoneyTodayAdd, consumeMoneyToday);
            BigDecimal todayData = DoubleUtil.divB4Zero(rechargeMoneyTodayAdd.multiply(BigDecimalVar.BD_100), consumeMoneyToday);
            BigDecimal yes = DoubleUtil.divB4Zero(rechargeMoneyYesAdd.multiply(BigDecimalVar.BD_100), consumeMoneyYes);
            BigDecimal totalData = DoubleUtil.divB4Zero(rechargeMoneyTotalAdd.multiply(BigDecimalVar.BD_100), consumeMoneyTotal);
            BigDecimal dataRate;
            if (DoubleUtil.subB(todayData, yes).compareTo(BigDecimal.ZERO) == 0) {
                dataRate = BigDecimal.ZERO;
            } else if (yes.compareTo(BigDecimal.ZERO) == 0) {
                dataRate = BigDecimalVar.BD_100;
            } else {
                dataRate = DoubleUtil.divB(DoubleUtil.mulB(DoubleUtil.subB(todayData, yes), BigDecimalVar.BD_100), yes);
            }
            JSONObject data = new JSONObject();
            data.fluentPut("type", 18).fluentPut("todayData", todayData).fluentPut("dataRate", dataRate).fluentPut("totalData", totalData);
            list.add(data);
            log.info("dramaId={},ROI分析耗时{}秒", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis) / 1000);
            log.info("短剧核心数据分析dramaId:{},ROI分析耗时:{}", params.getDramaId(), (System.currentTimeMillis() - currentTimeMillis));
        }
        return list;
    }

    public DramaAnalysisPO queryRechargeDataListGroupByRetailTypeRedis(DramaAnalysisPO query, String key) {
        String cacheCount = RedisUtil.get(key);
        if (StrUtil.isBlank(cacheCount)) {
            DramaAnalysisPO dbData = defaultIfNull(dataLakeRechargeMapper.queryRechargeDataListGroupByRetailType(query), new DramaAnalysisPO());
            RedisUtil.set(key, JsonUtil.toString(dbData), 24 * 60 * 60);
            return dbData;
        }
        return JsonUtil.toJavaObject(cacheCount, DramaAnalysisPO.class);
    }

    public BigDecimal queryListDramaCostSumRedis(FastLinkQueryVO query, String key) {
        String cacheCount = RedisUtil.get(key);
        if (StrUtil.isBlank(cacheCount)) {
            BigDecimal dbData = defaultIfNull(dataLakeStatisLinkMapper.queryListDramaCostSum(query), BigDecimal.ZERO);
            RedisUtil.set(key, String.valueOf(dbData), 24 * 60 * 60);
            return dbData;
        }
        return new BigDecimal(cacheCount);
    }

    public long queryCoinConsumeListByRedis(DramaAnalysisPO query, String key) {
        String cacheCount = RedisUtil.get(key);
        if (StrUtil.isBlank(cacheCount)) {
            long dbData = defaultIfNull(dataLakeOrderConsumeMapper.queryCoinConsumeList(query), 0L);
            RedisUtil.set(key, String.valueOf(dbData), 24 * 60 * 60);
            return dbData;
        }
        return Long.parseLong(cacheCount);
    }

    private JSONObject queryWatchFinishCountData(DramaAnalysisPO params, Integer watchToday, Integer watchYes, Integer total) {
        log.info("短剧核心数据分析,完播率分析watchToday:{},watchYes:{},total:{}", watchToday, watchYes, total);
        Date nowStart = DateUtil.beginOfDay();
        Date nowEnd = DateUtil.endOfDay();
        Date yesStart = DateUtil.addDays(nowStart, -1);
        Date yesEnd = DateUtil.addDays(nowEnd, -1);
        FastMemberRecentLogPO recentQuery = new FastMemberRecentLogPO();
        recentQuery.setCreateTimeS(nowStart);
        recentQuery.setCreateTimeE(nowEnd);
        recentQuery.setDramaId(params.getDramaId());
        recentQuery.setLinkIds(params.getLinkIds());
        recentQuery.setRetailIds(params.getRetailIds());
        recentQuery.setAdvUserIds(params.getAdvUserIds());
        recentQuery.setMiniIds(params.getMiniIds());
        recentQuery.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());


        FastDramaVO drama = dramaService.queryInfoByRedis(params.getDramaId());
        if (drama == null || drama.getSeriesNumUpdate() == null) {
            // throw new MyException("当前短剧[" + params.getDramaId() + "]没有配置更新集数");
            return null;
        }
        recentQuery.setPlayState(1);
        recentQuery.setSeriesNum(drama.getSeriesNumUpdate());// 最后一集
        Integer todayFinish = dataLakeRecentLogMapper.queryWatchMemberCountByDramaId2(recentQuery);
        BigDecimal todayData = DoubleUtil.divB4Zero(todayFinish * 100, watchToday);

        recentQuery.setCreateTimeS(yesStart);
        recentQuery.setCreateTimeE(yesEnd);
        BigDecimal oldData = DoubleUtil.divB4Zero(queryWatchMemberCountByDramaIdRedis(recentQuery, StaticVar.DREAM_ANALYSIS_DATA_WATCH_YESTERDAY + recentQuery.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(recentQuery))) * 100, watchYes);

        recentQuery.setCreateTimeS(null);
        recentQuery.setCreateTimeE(yesEnd);
        BigDecimal totalData = DoubleUtil.divB4Zero((queryWatchMemberCountByDramaIdRedis(recentQuery, StaticVar.DREAM_ANALYSIS_DATA_WATCH_HISTORY + recentQuery.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(recentQuery))) + todayFinish) * 100, total);

        log.info("短剧核心数据分析,完播率分析todayData:{},oldData:{},totalData:{}", todayData, oldData, totalData);

        BigDecimal dataRate;
        if (DoubleUtil.subB(todayData, oldData).compareTo(BigDecimal.ZERO) == 0) {
            dataRate = BigDecimal.ZERO;
        } else if (oldData.compareTo(BigDecimal.ZERO) == 0) {
            dataRate = BigDecimalVar.BD_100;
        } else {
            dataRate = DoubleUtil.divB(DoubleUtil.mulB(DoubleUtil.subB(todayData, oldData), BigDecimalVar.BD_100), oldData);
        }

        JSONObject data = new JSONObject();
        data.fluentPut("type", 4).fluentPut("todayData", todayData).fluentPut("dataRate", dataRate).fluentPut("totalData", totalData);
        return data;
    }

    private JSONObject queryWatchCountData(DramaAnalysisPO params) {
        Date nowStart = DateUtil.beginOfDay();
        Date nowEnd = DateUtil.endOfDay();
        Date yesStart = DateUtil.addDays(nowStart, -1);
        Date yesEnd = DateUtil.addDays(nowEnd, -1);
        FastMemberRecentLogPO recentQuery = new FastMemberRecentLogPO();
        recentQuery.setCreateTimeS(nowStart);
        recentQuery.setCreateTimeE(nowEnd);
        recentQuery.setDramaId(params.getDramaId());
        recentQuery.setLinkIds(params.getLinkIds());
        recentQuery.setRetailIds(params.getRetailIds());
        recentQuery.setAdvUserIds(params.getAdvUserIds());
        recentQuery.setMiniIds(params.getMiniIds());
        recentQuery.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
        Integer todayData = dataLakeRecentLogMapper.queryWatchMemberCountByDramaId2(recentQuery);

        recentQuery.setCreateTimeS(yesStart);
        recentQuery.setCreateTimeE(yesEnd);
        // 昨日数据+昨日前一天数据缓存
        int oldData = queryWatchMemberCountByDramaIdRedis(recentQuery, StaticVar.DREAM_ANALYSIS_DATA_WATCH_YESTERDAY + recentQuery.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(recentQuery)));

        recentQuery.setCreateTimeS(null);
        recentQuery.setCreateTimeE(yesEnd);
        Integer totalData = queryWatchMemberCountByDramaIdRedis(recentQuery, StaticVar.DREAM_ANALYSIS_DATA_WATCH_HISTORY + recentQuery.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(recentQuery))) + todayData;

        BigDecimal dataRate;
        if (todayData - oldData == 0) {
            dataRate = BigDecimal.ZERO;
        } else if (oldData == 0) {
            dataRate = BigDecimalVar.BD_100;
        } else {
            dataRate = DoubleUtil.divB((todayData - oldData) * 100, oldData);
        }

        JSONObject data = new JSONObject();
        data.fluentPut("type", 2).fluentPut("todayData", todayData).fluentPut("dataRate", dataRate).fluentPut("totalData", totalData);
        data.put("oldData", oldData);
        return data;
    }

    public int queryWatchMemberCountByDramaIdRedis(FastMemberRecentLogPO query, String key) {
        String cacheCount = RedisUtil.get(key);
        if (StrUtil.isBlank(cacheCount)) {
            int dbData = dataLakeRecentLogMapper.queryWatchMemberCountByDramaId2(query);
            RedisUtil.set(key, String.valueOf(dbData), 24 * 60 * 60);
            return dbData;
        }
        return Integer.parseInt(cacheCount);
    }

    private JSONObject queryRegistryCountData(DramaAnalysisPO params) {
        Date nowStart = DateUtil.beginOfDay();
        Date nowEnd = DateUtil.endOfDay();
        Date yesStart = DateUtil.addDays(nowStart, -1);
        Date yesEnd = DateUtil.addDays(nowEnd, -1);
        DramaAnalysisPO query = new DramaAnalysisPO();
        query.setDramaId(params.getDramaId());
        query.setRetailIds(params.getRetailIds());
        query.setLinkIds(params.getLinkIds());
        query.setMiniIds(params.getMiniIds());
        query.setAdvUserIds(params.getAdvUserIds());
        query.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
        query.setRegisterTimeS(nowStart);
        query.setRegisterTimeE(nowEnd);
        long start = System.currentTimeMillis();
        int todayData = dataLakeMemberLinkMapper.queryRegisterMemberCountList(query);
        log.info("短剧核心数据分析当天注册人数耗时:{}", System.currentTimeMillis() - start);

        query.setRegisterTimeS(yesStart);
        query.setRegisterTimeE(yesEnd);
        start = System.currentTimeMillis();
        int oldData = queryRegistryCountDataByRedis(query, StaticVar.DREAM_ANALYSIS_DATA_REGISTER_YESTERDAY + query.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(query)));
        log.info("短剧核心数据分析昨天注册人数耗时:{}", System.currentTimeMillis() - start);

        query.setRegisterTimeS(null);
        query.setRegisterTimeE(yesEnd);
        start = System.currentTimeMillis();
        int totalData = queryRegistryCountDataByRedis(query, StaticVar.DREAM_ANALYSIS_DATA_REGISTER_HISTORY + query.getDramaId() + ":" + Md5Util.getMD5(JsonUtil.toString(query))) + todayData;
        log.info("短剧核心数据分析总注册人数耗时:{}", System.currentTimeMillis() - start);

        BigDecimal dataRate;
        if (todayData - oldData == 0) {
            dataRate = BigDecimal.ZERO;
        } else if (oldData == 0) {
            dataRate = BigDecimalVar.BD_100;
        } else {
            dataRate = DoubleUtil.divB((todayData - oldData) * 100, oldData);
        }

        JSONObject data = new JSONObject();
        data.fluentPut("type", 1).fluentPut("todayData", todayData).fluentPut("dataRate", dataRate).fluentPut("totalData", totalData);
        return data;
    }

    public int queryRegistryCountDataByRedis(DramaAnalysisPO query, String key) {
        String cacheCount = RedisUtil.get(key);
        if (StrUtil.isBlank(cacheCount)) {
            int dbData = dataLakeMemberLinkMapper.queryRegisterMemberCountList(query);
            RedisUtil.set(key, String.valueOf(dbData), 24 * 60 * 60);
            return dbData;
        }
        return Integer.parseInt(cacheCount);
    }

    // 最大统计到10充的数据
    private static final int RECHARGE_COUNT_MAX = 10;

    /**
     * 短剧付费分析
     */
    public List<DramaAnalysisPO> getDramaDetailPayAnalysisList(DramaAnalysisPO params) {
        List<DramaAnalysisPO> result = new ArrayList<>();
        List<DramaAnalysisPO> rechargeCountList = detailAnalysisMapper.queryRechargeMemberGroupList(params);

        // 统计N充人数(共统计到10充)
        int[] rechargeCount = new int[RECHARGE_COUNT_MAX];
        // 首充VIP人数
        int rechargeVipCount = 0;
        BigDecimal[] rechargeMoney = new BigDecimal[RECHARGE_COUNT_MAX];
        for (DramaAnalysisPO po : rechargeCountList) {
            if (po.getRechargeCount() > RECHARGE_COUNT_MAX) {
                po.setRechargeCount(RECHARGE_COUNT_MAX);// 大于10充的全部算10充
            }
            List<BigDecimal> moneyAll = CollUtil.parseBigDecimalStr2List(po.getRechargeMoneyStr());
            List<Integer> typeAll = CollUtil.parseIntStr2List(po.getRechargeTypeStr());
            // 注意: 以下除`default`外都不能带break
            switch (po.getRechargeCount()) {
                case 10:
                    rechargeCount[9]++;
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 9);
                case 9:
                    rechargeCount[8]++;
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 8);
                case 8:
                    rechargeCount[7]++;
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 7);
                case 7:
                    rechargeCount[6]++;
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 6);
                case 6:
                    rechargeCount[5]++;
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 5);
                case 5:
                    rechargeCount[4]++;
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 4);
                case 4:
                    rechargeCount[3]++;
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 3);
                case 3:
                    rechargeCount[2]++;
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 2);
                case 2:
                    rechargeCount[1]++;
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 1);
                case 1:
                    rechargeCount[0]++;
                    sumAddRechargeMoney(rechargeMoney, moneyAll, 0);
                    if (typeAll.get(0) == 2) {
                        rechargeVipCount++;
                    }
                default:
                    break;
            }
        }
        for (int i = 0; i < RECHARGE_COUNT_MAX; i++) {
            DramaAnalysisPO po = new DramaAnalysisPO();
            po.setRechargeCount(i + 1);
            po.setRechargeMemberCount(rechargeCount[i]);
            po.setMemberCount(po.getRechargeMemberCount());
            int count = rechargeCount[0];
            // 剔除VIP首充人数-复充率专属 1=是;0=否
            if (params.getNoFirstVipNum() != null && params.getNoFirstVipNum() == 1) {
                count -= rechargeVipCount;
            }
            if (i > 0) {
                po.setRepeatRechargeRate(DoubleUtil.divB4Zero(rechargeCount[i] * 100, count));// 复充率 N充的充值人数/首充的充值总人数
                po.setData(po.getRepeatRechargeRate());
            }
            // 计算每N充的平均充值金额
            po.setRechargeMoneySum(defaultIfNull(rechargeMoney[i], BigDecimal.ZERO));
            po.setRechargeMoneyAvg(DoubleUtil.divB4Zero(po.getRechargeMoneySum(), rechargeCount[i]));

            result.add(po);
        }
        // 计算首充的转定率
        // 观看人数
        // int todayData = detailAnalysisMapper.queryWatchMemberCountList(params);
        FastMemberRecentLogPO fastMemberRecentLogPO = new FastMemberRecentLogPO();
        fastMemberRecentLogPO.setDramaId(params.getDramaId());
        fastMemberRecentLogPO.setAdvUserIds(params.getAdvUserIds());
        fastMemberRecentLogPO.setLinkId(params.getLinkId());
        fastMemberRecentLogPO.setLinkIds(params.getLinkIds());
        fastMemberRecentLogPO.setRetailId(params.getRetailId());
        fastMemberRecentLogPO.setRetailIds(params.getRetailIds());
        fastMemberRecentLogPO.setMemberIds(params.getMemberIds());
        fastMemberRecentLogPO.setCreateTimeS(params.getCreateTimeS());
        fastMemberRecentLogPO.setCreateTimeE(params.getCreateTimeE());
        fastMemberRecentLogPO.setRegisterTimeS(params.getRegisterTimeS());
        fastMemberRecentLogPO.setRegisterTimeE(params.getRegisterTimeE());
        fastMemberRecentLogPO.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
        int todayData = dataLakeRecentLogMapper.queryWatchMemberCountByDramaId(fastMemberRecentLogPO);
        BigDecimal rate = DoubleUtil.divB4Zero(rechargeCount[0] * 100, todayData);
        DramaAnalysisPO po = result.get(0);
        po.setRepeatRechargeRate(rate);
        po.setData(rate);
        return result;
    }

    /**
     * 优化 原接口数据量过大，导致内存溢出
     *
     * @param params
     * @return
     */
    public List<DramaAnalysisPO> getDramaDetailPayAnalysisListV2(DramaAnalysisPO params) {
        List<DramaAnalysisPO> result = new ArrayList<>();
        // 统计充值次数
        List<DramaAnalysisPO> rechargeCountList = dataLakeRechargeMapper.queryListGroupByRechargeCount(params);
        // 统计N充人数
        int[] rechargeCount = new int[RECHARGE_COUNT_MAX];
        // 统计N充的充值金额
        BigDecimal[] rechargeMoney = new BigDecimal[RECHARGE_COUNT_MAX];
        // 首充为VIP的人数
        int vipRechargeCount = 0;
        for (DramaAnalysisPO po : rechargeCountList) {
            if (po.getRechargeCount() > 10) {  // 超过10次算10次以上
                po.setRechargeCount(10);
            }
            // 注意: 以下除`default`外都不能带break
            switch (po.getRechargeCount()) {
                case 10:
                    rechargeCount[9] += po.getRechargeMemberNum();  // 10充以上充值人数
                    rechargeMoney[9] = DoubleUtil.addB(defaultIfNull(rechargeMoney[9], BigDecimal.ZERO), defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO));
                case 9:
                    rechargeCount[8] += po.getRechargeMemberNum();
                    rechargeMoney[8] = DoubleUtil.addB(defaultIfNull(rechargeMoney[8], BigDecimal.ZERO), defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO));
                case 8:
                    rechargeCount[7] += po.getRechargeMemberNum();
                    rechargeMoney[7] = DoubleUtil.addB(defaultIfNull(rechargeMoney[7], BigDecimal.ZERO), defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO));
                case 7:
                    rechargeCount[6] += po.getRechargeMemberNum();
                    rechargeMoney[6] = DoubleUtil.addB(defaultIfNull(rechargeMoney[6], BigDecimal.ZERO), defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO));
                case 6:
                    rechargeCount[5] += po.getRechargeMemberNum();
                    rechargeMoney[5] = DoubleUtil.addB(defaultIfNull(rechargeMoney[5], BigDecimal.ZERO), defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO));
                case 5:
                    rechargeCount[4] += po.getRechargeMemberNum();
                    rechargeMoney[4] = DoubleUtil.addB(defaultIfNull(rechargeMoney[4], BigDecimal.ZERO), defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO));
                case 4:
                    rechargeCount[3] += po.getRechargeMemberNum();
                    rechargeMoney[3] = DoubleUtil.addB(defaultIfNull(rechargeMoney[3], BigDecimal.ZERO), defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO));
                case 3:
                    rechargeCount[2] += po.getRechargeMemberNum();
                    rechargeMoney[2] = DoubleUtil.addB(defaultIfNull(rechargeMoney[2], BigDecimal.ZERO), defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO));
                case 2:
                    rechargeCount[1] += po.getRechargeMemberNum();
                    rechargeMoney[1] = DoubleUtil.addB(defaultIfNull(rechargeMoney[1], BigDecimal.ZERO), defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO));
                case 1:
                    rechargeCount[0] += po.getRechargeMemberNum();
                    rechargeMoney[0] = DoubleUtil.addB(defaultIfNull(rechargeMoney[0], BigDecimal.ZERO), defaultIfNull(po.getRechargeMoneySum(), BigDecimal.ZERO));
                    vipRechargeCount += po.getFirstVipRechargeNum();
                default:
                    break;
            }
        }
        for (int i = 0; i < RECHARGE_COUNT_MAX; i++) {
            DramaAnalysisPO po = new DramaAnalysisPO();
            po.setRechargeCount(i + 1);
            po.setRechargeMemberCount(rechargeCount[i]);
            po.setMemberCount(po.getRechargeMemberCount());
            int count = rechargeCount[0];
            // 剔除VIP首充人数-复充率专属 1=是;0=否
            if (params.getNoFirstVipNum() != null && params.getNoFirstVipNum() == 1) {
                count -= vipRechargeCount;
            }
            if (i > 0) { // 非首充 计算复充率
                po.setRepeatRechargeRate(DoubleUtil.divB4Zero(rechargeCount[i] * 100, count));// 复充率 N充的充值人数/首充的充值总人数
                po.setData(po.getRepeatRechargeRate());
            }
            // 计算每N充的平均充值金额
            po.setRechargeMoneySum(defaultIfNull(rechargeMoney[i], BigDecimal.ZERO));
            po.setRechargeMoneyAvg(DoubleUtil.divB4Zero(po.getRechargeMoneySum(), rechargeCount[i]));
            result.add(po);
        }
        // 首充转定率
        FastMemberRecentLogPO fastMemberRecentLogPO = new FastMemberRecentLogPO();
        fastMemberRecentLogPO.setDramaId(params.getDramaId());
        fastMemberRecentLogPO.setAdvUserIds(params.getAdvUserIds());
        fastMemberRecentLogPO.setLinkId(params.getLinkId());
        fastMemberRecentLogPO.setLinkIds(params.getLinkIds());
        fastMemberRecentLogPO.setRetailId(params.getRetailId());
        fastMemberRecentLogPO.setRetailIds(params.getRetailIds());
        fastMemberRecentLogPO.setMemberIds(params.getMemberIds());
        fastMemberRecentLogPO.setCreateTimeS(params.getCreateTimeS());
        fastMemberRecentLogPO.setCreateTimeE(params.getCreateTimeE());
        fastMemberRecentLogPO.setRegisterTimeS(params.getRegisterTimeS());
        fastMemberRecentLogPO.setRegisterTimeE(params.getRegisterTimeE());
        fastMemberRecentLogPO.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
        int watchMemberNum = dataLakeRecentLogMapper.queryWatchMemberCountByDramaId(fastMemberRecentLogPO);
        BigDecimal rate = DoubleUtil.divB4Zero(rechargeCount[0] * 100, watchMemberNum);
        DramaAnalysisPO po = result.get(0);
        po.setRepeatRechargeRate(rate);
        po.setData(rate);
        return result;
    }


    /**
     * 累加每个N充的金额(所有1充的累加一起,2充的累加一起...)
     *
     * @param rechargeMoney
     * @param moneyAll
     * @param n
     */
    private static void sumAddRechargeMoney(final BigDecimal[] rechargeMoney, final List<BigDecimal> moneyAll, int n) {
        if (n < 9) {
            rechargeMoney[n] = DoubleUtil.addB(defaultIfNull(rechargeMoney[n], BigDecimal.ZERO), moneyAll.get(n));
        } else {
            rechargeMoney[n] = DoubleUtil.addB(defaultIfNull(rechargeMoney[n], BigDecimal.ZERO), moneyAll.subList(n, moneyAll.size()));
        }
    }

    /**
     * 剧集分析-留存分析
     */
    public List<DramaAnalysisPO> getDramaDetailRemainAnalysisList(DramaAnalysisPO params) {
        List<DramaAnalysisPO> result = new ArrayList<>();

        // List<DramaAnalysisPO> remainCountList = detailAnalysisMapper.queryDramaRemainGroupList(params);
        // 弃用mysql查询，使用clickhouse来查数据
        FastMemberRecentLogPO groupBySeriesNum = new FastMemberRecentLogPO();
        groupBySeriesNum.setRetailId(params.getRetailId());
        groupBySeriesNum.setDramaId(params.getDramaId());
        groupBySeriesNum.setLinkIds(params.getLinkIds());
        groupBySeriesNum.setMemberIds(params.getMemberIds());
        groupBySeriesNum.setOfficialIds(params.getOfficialIds());
        groupBySeriesNum.setCreateTimeS(params.getWatchTimeS());
        groupBySeriesNum.setCreateTimeE(params.getWatchTimeE());
        List<FastMemberRecentLogPO> remainCountList = dataLakeRecentLogMapper.queryWatchCountGroupBySeriesNum(groupBySeriesNum);

        FastDramaVO drama = dramaService.queryInfoByRedis(params.getDramaId());
        if (drama == null || drama.getSeriesNumUpdate() == null) {
            // throw new MyException("当前短剧[" + params.getDramaId() + "]没有配置更新集数");
            return null;
        }
        // 每集观看人数
        Map<Integer, Integer> watchMemberCountMap = new HashMap<>();
        // 剧集总数
        int numAll = drama.getSeriesNumUpdate();
        // 第一集观看人数
        int firstWatchMemberCount = 0;
        for (FastMemberRecentLogPO po : remainCountList) {
            watchMemberCountMap.put(po.getSeriesNum(), po.getWatchCount());
            if (po.getSeriesNum() != null && po.getSeriesNum() == 1) {
                firstWatchMemberCount = po.getWatchCount();
            }
        }
        for (int i = 1; i <= numAll; i++) {
            DramaAnalysisPO po = new DramaAnalysisPO();
            Integer watchMemberCount = defaultIfNull(watchMemberCountMap.get(i), 0);
            po.setSeriesNum(i);
            po.setWatchMemberCount(watchMemberCount);
            po.setMemberCount(watchMemberCount);
            po.setRemainRate(DoubleUtil.divB4Zero(watchMemberCount * 100, firstWatchMemberCount));
            po.setData(po.getRemainRate());
            result.add(po);
        }
        return result;
    }


    public List<DramaAnalysisPO> getDramaDetailRemainAnalysisListV2(DramaAnalysisPO params) {
        List<DramaAnalysisPO> result = new ArrayList<>();
        // List<DramaAnalysisPO> remainCountList = detailAnalysisMapper.queryDramaRemainGroupList(params);
        // 弃用mysql查询，使用clickhouse来查数据
        FastMemberRecentLogPO groupBySeriesNum = new FastMemberRecentLogPO();
        groupBySeriesNum.setRetailId(params.getRetailId());
        groupBySeriesNum.setRetailIds(params.getRetailIds());
        groupBySeriesNum.setDramaId(params.getDramaId());
        groupBySeriesNum.setAdvUserIds(params.getAdvUserIds());
        groupBySeriesNum.setMiniIds(params.getMiniIds());
        groupBySeriesNum.setFeeFlag(params.getFeeFlag());
        groupBySeriesNum.setLinkIds(params.getLinkIds());
        groupBySeriesNum.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
        groupBySeriesNum.setRegisterTimeS(params.getRegisterTimeS());
        groupBySeriesNum.setRegisterTimeE(params.getRegisterTimeE());
        groupBySeriesNum.setCreateTimeS(params.getWatchTimeS());
        groupBySeriesNum.setCreateTimeE(params.getWatchTimeE());
        List<FastMemberRecentLogPO> remainCountList = dataLakeRecentLogMapper.queryWatchCountGroupBySeriesNumV2(groupBySeriesNum);

        FastDramaVO drama = dramaService.queryInfoByRedis(params.getDramaId());
        if (drama == null || drama.getSeriesNumUpdate() == null) {
            // throw new MyException("当前短剧[" + params.getDramaId() + "]没有配置更新集数");
            return null;
        }
        // 每集观看人数
        Map<Integer, Integer> watchMemberCountMap = new HashMap<>();
        // 剧集总数
        int numAll = drama.getSeriesNumUpdate();
        // 第一集观看人数
        int firstWatchMemberCount = 0;
        for (FastMemberRecentLogPO po : remainCountList) {
            watchMemberCountMap.put(po.getSeriesNum(), po.getWatchCount());
            if (po.getSeriesNum() != null && po.getSeriesNum() == 1) {
                firstWatchMemberCount = po.getWatchCount();
            }
        }
        for (int i = 1; i <= numAll; i++) {
            DramaAnalysisPO po = new DramaAnalysisPO();
            Integer watchMemberCount = defaultIfNull(watchMemberCountMap.get(i), 0);
            po.setSeriesNum(i);
            po.setWatchMemberCount(watchMemberCount);
            po.setMemberCount(watchMemberCount);
            po.setRemainRate(DoubleUtil.divB4Zero(watchMemberCount * 100, firstWatchMemberCount));
            po.setData(po.getRemainRate());
            result.add(po);
        }
        return result;
    }


    /**
     * 剧集分析-留存分析-对比
     */
    public List<JSONObject> getDramaDetailRemainAnalysisContrast(DramaAnalysisPO params) {
        Map<Integer, String> nameMap = new HashMap<>();
        List<Integer> ids = null;
        switch (params.getContrastType()) {
            case 1: {
                ids = CollUtil.parseIntStr2List(params.getRetailIds());
                // 查询分销商
                FastRetailPO query = new FastRetailPO();
                query.setRetailIds(params.getRetailIds());
                List<FastRetailPO> list = retailMapper.querySimpleList(query);
                for (FastRetailPO po : list) {
                    nameMap.put(po.getId(), po.getRetailName());
                }
            }
            break;
            case 2: {

            }
            break;
            case 3: {
                ids = CollUtil.parseIntStr2List(params.getAdvUserIds());
                // 查询优化师
                FastUserPO query = new FastUserPO();
                query.setIds(params.getAdvUserIds());
                List<FastUserPO> list = fastUserMapper.querySimpleList(query);
                for (FastUserPO po : list) {
                    nameMap.put(po.getId(), po.getUserName());
                }
            }
            break;
            case 4: {
                ids = CollUtil.parseIntStr2List(params.getLinkIds());
                // 查询链接
                FastLinkPO query = new FastLinkPO();
                query.setIds(params.getLinkIds());
                List<FastLinkPO> list = linkMapper.querySimpleList(query);
                for (FastLinkPO po : list) {
                    nameMap.put(po.getId(), po.getLinkName());
                }
            }
            break;
        }
        if (ids == null) {
            return null;
        }

        List<JSONObject> result = new ArrayList<>();
        for (Integer id : ids) {
            switch (params.getContrastType()) {
                case 1: {
                    params.setRetailIds(null);
                    params.setRetailId(id);
                }
                break;
                case 2: {
                    params.setOfficialIds(null);
                    params.setOfficialId(id);
                }
                break;
                case 3: {
                    params.setAdvUserIds(null);
                    params.setAdvUserId(id);
                }
                break;
                case 4: {
                    params.setLinkIds(null);
                    params.setLinkId(id);
                }
                break;
            }
            JSONObject data = new JSONObject();
            data.put("id", id);
            data.put("name", nameMap.get(id));
            data.put("list", getDramaDetailRemainAnalysisListV2(params));
            result.add(data);
        }
        return result;
    }

    /**
     * 剧集分析-留存分析-对比-规则显示
     */
    public List<FastFeeRulePO> getDramaDetailRemainRuleContrast(DramaAnalysisPO params) {
        List<Integer> officialIds = null;
        switch (params.getContrastType()) {
            case 2: {
                officialIds = CollUtil.parseIntStr2List(params.getOfficialIds());
            }
            break;
            case 4: {
                // 查询链接
                FastLinkPO queryL = new FastLinkPO();
                queryL.setIds(params.getLinkIds());
                officialIds = linkMapper.queryOfficialIds(queryL);
            }
            break;
        }
        FastFeeRulePO rule = new FastFeeRulePO();
        rule.setOfficialIds(StrUtil.joinNoRepeat(officialIds));
        rule.setDramaId(params.getDramaId());
        List<FastFeeRulePO> feeRuleList = feeRuleMapper.queryFollowNumList(rule);
        for (FastFeeRulePO po : feeRuleList) {
            if (po.getFollowNum() == 0) {
                po.setFollowType(po.getFollowNumOfficial());
            }
        }
        return feeRuleList;
    }

    private static final int VALID_TIME = 3;

    /**
     * 剧集分析-跳出分析
     */
    public List<?> getDramaDetailSkipAnalysisList(DramaAnalysisPO params) {
        List<DramaAnalysisPO> result = new ArrayList<>();
        // 弃用mysql查询，改用clickhouse
        FastMemberRecentLogPO querySkip = new FastMemberRecentLogPO();
        querySkip.setDramaId(params.getDramaId());
        querySkip.setSeriesNum(params.getSeriesNum());
        querySkip.setAdvUserIds(params.getAdvUserIds());
        querySkip.setLinkIds(params.getLinkIds());
        querySkip.setRetailIds(params.getRetailIds());
        querySkip.setMiniIds(params.getMiniIds());
        querySkip.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
        querySkip.setCreateTimeS(params.getWatchTimeS());
        querySkip.setCreateTimeE(params.getWatchTimeE());
        querySkip.setRegisterTimeS(params.getRegisterTimeS());
        querySkip.setRegisterTimeE(params.getRegisterTimeE());
        List<FastMemberRecentLogPO> skipCountList = dataLakeRecentLogMapper.queryDramaSkipGroupList(querySkip);
        FastDramaSeriesVO dramaSeries = seriesService.queryInfoByRedis(params.getDramaId(), params.getSeriesNum(), params.getLanguageCode(), params.getCaptionType());
        if (dramaSeries == null) {
            return getDefaultListData();
        }
        Map<Integer, Integer> map = new HashMap<>();
        // 剧集时长
        int allTime = dramaSeries.getSeriesTime();
        for (FastMemberRecentLogPO po : skipCountList) {
            if (params.getOpenValidAnalysis() == 1 && (po.getPlaySecond() <= VALID_TIME || po.getPlaySecond() >= (allTime - VALID_TIME))) {
                map.put(po.getPlaySecond(), 0);
            } else {
                map.put(po.getPlaySecond(), po.getWatchCount());
            }
        }
        // 单集观看人数-需要去重
        FastMemberRecentLogPO querySeriesCount = new FastMemberRecentLogPO();
        querySeriesCount.setDramaId(params.getDramaId());
        querySkip.setAdvUserIds(params.getAdvUserIds());
        querySkip.setRetailIds(params.getRetailIds());
        querySkip.setMiniIds(params.getMiniIds());
        querySkip.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
        querySeriesCount.setSeriesNum(params.getSeriesNum());
        querySeriesCount.setCreateTimeS(params.getWatchTimeS());
        querySeriesCount.setCreateTimeE(params.getWatchTimeE());
        querySkip.setRegisterTimeS(params.getRegisterTimeS());
        querySkip.setRegisterTimeE(params.getRegisterTimeE());
        int watchMemberCount = dataLakeRecentLogMapper.queryWatchCountByDramaId(querySeriesCount);
        for (int i = 1; i <= allTime; i++) {
            DramaAnalysisPO po = new DramaAnalysisPO();
            Integer skipMemberCount = defaultIfNull(map.get(i), 0);
            po.setSkipTime(i);
            po.setSkipRate(DoubleUtil.divB4Zero(skipMemberCount * 100, watchMemberCount));
            po.setSkipMemberCount(skipMemberCount);
            result.add(po);
        }
        return result;
    }

    /**
     * 剧集分析-剧集数据明细
     */
    public List<DramaAnalysisPO> getDramaDetailSeriesAnalysisList(SessionVO sessionVO, DramaAnalysisPO params) {
        List<DramaAnalysisPO> result = new ArrayList<>();
        List<DramaAnalysisPO> consumeList = dataLakeOrderConsumeMapper.querySeriesCoinConsumeList(params);
        FastMemberRecentLogPO seriesCount = new FastMemberRecentLogPO();
        seriesCount.setAdvUserIds(params.getAdvUserIds());
        seriesCount.setRetailIds(params.getRetailIds());
        seriesCount.setLinkIds(params.getLinkIds());
        seriesCount.setMiniIds(params.getMiniIds());
        seriesCount.setRemoveMountLinkFlag(params.getRemoveMountLinkFlag());
        seriesCount.setDramaId(params.getDramaId());
        seriesCount.setSeriesNum(params.getSeriesNum());
        seriesCount.setOfficialIds(params.getOfficialIds());
        seriesCount.setPayResultExt(params.getPayResultExt());
        seriesCount.setLinkTimeSearch(params.getLinkTimeSearch());
        seriesCount.setLinkTimeS(params.getLinkTimeS());
        seriesCount.setLinkTimeE(params.getLinkTimeE());
        List<FastMemberRecentLogPO> seriesCountList = dataLakeRecentLogMapper.queryDramaDetailSeriesGroupList(seriesCount);
        FastMemberUnlockDramaPO seriesUnlockCount = new FastMemberUnlockDramaPO();
        seriesUnlockCount.setDramaId(params.getDramaId());
        seriesUnlockCount.setLinkTimeSearch(params.getLinkTimeSearch());
        seriesUnlockCount.setLinkTimeS(params.getLinkTimeS());
        seriesUnlockCount.setLinkTimeE(params.getLinkTimeE());
        List<FastMemberUnlockDramaPO> seriesUnlockList = dataLakeUnlockDramaMapper.queryUnlockCountGroupBySeriesNum(seriesUnlockCount);
        Map<Integer, Integer> unlockMap = Maps.newHashMap();
        for (FastMemberUnlockDramaPO unlock : seriesUnlockList) {
            unlockMap.put(unlock.getSeriesNum(), unlock.getUnlockCount());
        }
        FastFeeRuleVO fastFeeRuleVO = dramaService.getFeeRule(sessionVO.getLinkId(), sessionVO.getOfficialId(), params.getDramaId());
        Integer startNum = fastFeeRuleVO.getStartNum();

        FastDramaVO drama = dramaService.queryInfoByRedis(params.getDramaId());
        if (drama == null || drama.getSeriesNumUpdate() == null) {
            return new ArrayList<>();
        }
        // 每集统计数据
        Map<Integer, DramaAnalysisPO> dataMap = new HashMap<>();
        Map<Integer, DramaAnalysisPO> coinConsumeMap = new HashMap<>();
        // 剧集总数
        int seriesAll = drama.getSeriesNumUpdate();
        // 第一集观看人数
        int firstWatchMemberCount = 0;
        for (FastMemberRecentLogPO po : seriesCountList) {
            DramaAnalysisPO dramaAnalysisPO = new DramaAnalysisPO();
            dramaAnalysisPO.setSeriesNum(po.getSeriesNum());
            dramaAnalysisPO.setWatchMemberCount(po.getWatchCount());
            dramaAnalysisPO.setWatchFinishMemberCount(po.getWatchFinishMemberCount());
            dataMap.put(po.getSeriesNum(), dramaAnalysisPO);
            if (po.getSeriesNum() != null && po.getSeriesNum() == 1) {
                firstWatchMemberCount = po.getWatchCount();
            }
        }
        for (DramaAnalysisPO po : consumeList) {
            coinConsumeMap.put(po.getSeriesNum(), po);
        }
        for (int i = 1; i <= seriesAll; i++) {
            DramaAnalysisPO po = new DramaAnalysisPO();
            DramaAnalysisPO cur = defaultIfNull(dataMap.get(i), new DramaAnalysisPO());
            DramaAnalysisPO next = defaultIfNull(dataMap.get(i + 1), new DramaAnalysisPO());
            DramaAnalysisPO consume = defaultIfNull(coinConsumeMap.get(i), new DramaAnalysisPO());
            // 是否付费
            if (i < startNum) {
                po.setWhetherPay(0);
            } else {
                po.setWhetherPay(1);
            }
            // 当前集完播人数
            Integer watchFinishMemberCount = defaultIfNull(cur.getWatchFinishMemberCount(), 0);
            po.setWatchFinishMemberCount(watchFinishMemberCount);
            // 当前集观看人数
            Integer watchMemberCount = defaultIfNull(cur.getWatchMemberCount(), 0);
            po.setWatchMemberCount(watchMemberCount);
            Integer unlockMemberCount = defaultIfNull(unlockMap.get(i), 0);
            po.setUnlockCount(unlockMemberCount);
            // 下一集观看人数
            Integer watchMemberCountNext = defaultIfNull(next.getWatchMemberCount(), 0);
            po.setSeriesNum(i);
            // 当前K币消费数
            Integer coinConsume = defaultIfNull(consume.getCoinGiveConsume(), 0) + defaultIfNull(consume.getCoinRechargeConsume(), 0);
            po.setCoinConsume(coinConsume);
            // 当前K币消费数-赠送
            Integer coinGiveConsume = defaultIfNull(consume.getCoinGiveConsume(), 0);
            po.setCoinGiveConsume(coinGiveConsume);

            po.setRemainRate(DoubleUtil.divB4Zero(watchMemberCount * 100, firstWatchMemberCount));
            po.setSkipRate(DoubleUtil.divB4Zero((watchMemberCount - watchMemberCountNext) * 100, watchMemberCount));
            po.setWatchFinishRate(DoubleUtil.divB4Zero(po.getWatchFinishMemberCount() * 100, watchMemberCount));
            result.add(po);
        }
        // 排序
        sortDay(params, result);
        return result;
    }

    /**
     * 排序
     *
     * @param params
     * @param list
     */
    private static void sortDay(DramaAnalysisPO params, List<DramaAnalysisPO> list) {
        switch (params.getSortType()) {
            case 0:// 剧集号
                // 正序排序(由小->大)
                list.sort(Comparator.comparing(DramaAnalysisPO::getSeriesNum));
                break;
            case 1:// 观看人数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchMemberCount).reversed().thenComparing(DramaAnalysisPO::getSeriesNum));
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchMemberCount).thenComparing(DramaAnalysisPO::getSeriesNum));
                }
                break;
            case 2:// 留存率
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRemainRate).reversed().thenComparing(DramaAnalysisPO::getSeriesNum));
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getRemainRate).thenComparing(DramaAnalysisPO::getSeriesNum));
                }
                break;
            case 3:// 单集跳出率
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getSkipRate).reversed().thenComparing(DramaAnalysisPO::getSeriesNum));
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getSkipRate).thenComparing(DramaAnalysisPO::getSeriesNum));
                }
                break;
            case 4:// 单集消耗K币
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getCoinConsume).reversed().thenComparing(DramaAnalysisPO::getSeriesNum));
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getCoinConsume).thenComparing(DramaAnalysisPO::getSeriesNum));
                }
                break;
            case 5:// 单集完播人数
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchFinishMemberCount).reversed().thenComparing(DramaAnalysisPO::getSeriesNum));
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchFinishMemberCount).thenComparing(DramaAnalysisPO::getSeriesNum));
                }
                break;
            case 6:// 完播率
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchFinishRate).reversed().thenComparing(DramaAnalysisPO::getSeriesNum));
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWatchFinishRate).thenComparing(DramaAnalysisPO::getSeriesNum));
                }
                break;
            case 8:// 是否付费排名
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWhetherPay).reversed().thenComparing(DramaAnalysisPO::getSeriesNum));
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getWhetherPay).thenComparing(DramaAnalysisPO::getSeriesNum));
                }
                break;
            case 9:// 订阅人数排名
                // 倒序排序(由大->小)
                if (params.getSortOrder() == 1) {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getUnlockCount).reversed().thenComparing(DramaAnalysisPO::getSeriesNum));
                } else {
                    list.sort(Comparator.comparing(DramaAnalysisPO::getUnlockCount).thenComparing(DramaAnalysisPO::getSeriesNum));
                }
                break;
        }
    }

}
