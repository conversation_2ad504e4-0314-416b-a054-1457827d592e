/*
 * Powered By fast.up
 */
package com.fast.service.analysis;

import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.ContentTypeEnum;
import com.fast.mapper.analysis.LinkAnalysisMapper;
import com.fast.po.analysis.LinkAnalysisPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 渠道分析
 *
 * <AUTHOR>
 */
@Service
public class LinkAnalysisExportService extends BaseService {

    @Autowired
    private LinkAnalysisService dataService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private LinkAnalysisMapper launchAnalysisMapper;

    /**
     * ROI梯度表-整体数据汇总-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportLinkRoiCoreAnalysis(SessionVO sessionVO, LinkAnalysisPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_LINK_ROI + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        LinkAnalysisPO summary = new LinkAnalysisPO();
        List<LinkAnalysisPO> list = dataService.getLinkRoiCoreAnalysis(params, summary);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        list.add(0, summary);
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        int i = 0;
        for (LinkAnalysisPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)
            switch (params.getGroupByColum()) {
                case 0: {
                    // 整体
                    if (i == 0) {
                        row.add("累计");
                    } else {
                        row.add(cur.getStatisDate());
                    }
                    CollUtil.addNoRepeat(rowHeadNames, "日期");

                    row.add(cur.getValidLinkCount());
                    CollUtil.addNoRepeat(rowHeadNames, "有效链接数");

                    row.add(cur.getAdMoneyConsume());
                    CollUtil.addNoRepeat(rowHeadNames, "投放消耗");

                    row.add(cur.getMemberCountAddAll());
                    CollUtil.addNoRepeat(rowHeadNames, "新增用户");

                    row.add(cur.getMemberCountAddColor());
                    CollUtil.addNoRepeat(rowHeadNames, "染色用户");

                    row.add(cur.getRechargeMemberCountAdd());
                    CollUtil.addNoRepeat(rowHeadNames, "充值用户");
                }
                break;
                case 1: {
                    // 链接
                    if (i == 0) {
                        row.add("累计");
                    } else {
                        row.add(cur.getId());
                    }
                    CollUtil.addNoRepeat(rowHeadNames, "链接ID");

                    row.add(cur.getLinkName());
                    CollUtil.addNoRepeat(rowHeadNames, "链接名称");

                    row.add(cur.getAdvUserName());
                    CollUtil.addNoRepeat(rowHeadNames, "优化师");

                    row.add(cur.getRetailName());
                    CollUtil.addNoRepeat(rowHeadNames, "分销商");
                }
                break;
                case 2: {
                    // 分销商
                    if (i == 0) {
                        row.add("累计");
                    } else {
                        row.add(cur.getRetailName());
                    }
                    CollUtil.addNoRepeat(rowHeadNames, "分销商名称");

                    // 类别:1=自投、2=代投、3=独立分销商
                    row.add(cur.getRetailType() == 1 ? "自投" : cur.getRetailType() == 2 ? "代投" : "独立分销商");
                    CollUtil.addNoRepeat(rowHeadNames, "分销商类型");

                    row.add(cur.getAllLinkCount());
                    CollUtil.addNoRepeat(rowHeadNames, "关联链接数");
                }
                break;
                case 3: {
                    // 优化师
                    if (i == 0) {
                        row.add("累计");
                    } else {
                        row.add(cur.getAdvUserName());
                    }
                    CollUtil.addNoRepeat(rowHeadNames, "优化师名称");

                    row.add(cur.getRetailName());
                    CollUtil.addNoRepeat(rowHeadNames, "归属分销商");

                    row.add(cur.getAllLinkCount());
                    CollUtil.addNoRepeat(rowHeadNames, "关联链接数");
                }
                break;
                case 4: {
                    // 短剧
                    if (i == 0) {
                        row.add("累计");
                    } else {
                        row.add(cur.getId());
                    }
                    CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(ObjectUtils.defaultIfNull(params.getContentType(), ContentTypeEnum.DRAMA.index)) + "ID");

                    row.add(cur.getDramaName());
                    CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(ObjectUtils.defaultIfNull(params.getContentType(), ContentTypeEnum.DRAMA.index)) + "名称");

                    row.add(cur.getAllLinkCount());
                    CollUtil.addNoRepeat(rowHeadNames, "关联链接数");
                }
                break;
                case 5: {
                    // 链路
                    if (i == 0) {
                        row.add("累计");
                    } else {
                        row.add(cur.getRoadName());
                    }
                    CollUtil.addNoRepeat(rowHeadNames, "投放链路");

                    row.add(cur.getAllLinkCount());
                    CollUtil.addNoRepeat(rowHeadNames, "关联链接数");
                }
                break;
            }

            if (params.getGroupByColum() > 0) {
                row.add(cur.getAdMoneyConsumeToday());
                CollUtil.addNoRepeat(rowHeadNames, "今日-投放消耗");

                row.add(cur.getAdMoneyConsume());
                CollUtil.addNoRepeat(rowHeadNames, "累计-投放消耗");

                row.add(cur.getMemberCountAddToday());
                CollUtil.addNoRepeat(rowHeadNames, "今日-新增用户");

                row.add(cur.getMemberCountAddAll());
                CollUtil.addNoRepeat(rowHeadNames, "累计-新增用户");

                row.add(cur.getMemberCountAddColorToday());
                CollUtil.addNoRepeat(rowHeadNames, "今日-染色用户");

                row.add(cur.getMemberCountAddColor());
                CollUtil.addNoRepeat(rowHeadNames, "累计-染色用户");

                row.add(cur.getRechargeMemberCountAddToday());
                CollUtil.addNoRepeat(rowHeadNames, "今日-充值用户");

                row.add(cur.getRechargeMemberCountAdd());
                CollUtil.addNoRepeat(rowHeadNames, "累计-充值用户");
            }

            row.add(cur.getRenewRate() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "续充率");

            row.add(cur.getMemberCost());
            CollUtil.addNoRepeat(rowHeadNames, "新增用户成本");

            row.add(cur.getPayMemberCost());
            CollUtil.addNoRepeat(rowHeadNames, "充值用户成本");

            row.add(cur.getArppu());
            CollUtil.addNoRepeat(rowHeadNames, "ARPPU");

            row.add(cur.getRechargeMoneySumAddAll());
            CollUtil.addNoRepeat(rowHeadNames, "累计实到金额");

            row.add(cur.getBackRate() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "累计回报率");

            Integer[] numD60RechAll = cur.getNumD60RechAll();// D0-D60充值人数
            BigDecimal[] moneyD60RechAll = cur.getMoneyD60RechAll();// D0-D60充值金额
            BigDecimal[] renewRateD60 = cur.getRenewRateD60();// D0-D60续充率
            BigDecimal[] moneyD60RechDaySum = cur.getMoneyD60RechDaySum();// D0-D60充值金额-累计
            BigDecimal[] backD60RechAll = cur.getBackD60RechAll();// D0-D60回传率
            BigDecimal[] backD60RechDaySum = cur.getBackD60RechDaySum();// D0-D60回传率-累计
            for (int j = 0; j <= 60; j++) {
                row.add(numD60RechAll != null && numD60RechAll[j] != null ? numD60RechAll[j] : "-");
                CollUtil.addNoRepeat(rowHeadNames, "D" + j + "新增充值用户");

                row.add(moneyD60RechAll != null && moneyD60RechAll[j] != null ? moneyD60RechAll[j] : "-");
                CollUtil.addNoRepeat(rowHeadNames, "D" + j + "新增充值金额");

                row.add(moneyD60RechDaySum != null && moneyD60RechDaySum[j] != null ? moneyD60RechDaySum[j] : "-");
                CollUtil.addNoRepeat(rowHeadNames, "D" + j + "累计充值金额");

                if (j > 0) {
                    row.add(renewRateD60 != null && renewRateD60[j] != null ? renewRateD60[j] : "-");
                    CollUtil.addNoRepeat(rowHeadNames, "D" + j + "续充率");
                }

                row.add(backD60RechAll != null && backD60RechAll[j] != null ? (backD60RechAll[j] + "%") : "-");
                CollUtil.addNoRepeat(rowHeadNames, "D" + j + "新增回报率");

                row.add(backD60RechDaySum != null && backD60RechDaySum[j] != null ? (backD60RechDaySum[j] + "%") : "-");
                CollUtil.addNoRepeat(rowHeadNames, "D" + j + "累计回报率");
            }

            dataList.add(row);
            i++;
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "ROI梯度表-数据汇总";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 免费渠道导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportLinkRoiCoreAnalysisFree(SessionVO sessionVO, LinkAnalysisPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_FREE_LINK_ROI + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer groupByColum = defaultIfNull(params.getGroupByColum(), 0);
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        if (groupByColum == 0) {  // 整体分析
            List<LinkAnalysisPO> list = launchAnalysisMapper.queryFreeLinkROISummaryAnalysis(params);
            if (list == null || list.size() == 0) {
                return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
            }
            for (LinkAnalysisPO item : list) {
                Integer[] numD60MemberAll = new Integer[61];                     // D0-D60当日产生收益的用户数
                BigDecimal[] numD60MoneyDay = new BigDecimal[61];                // D0-D60当日产生的收益金额
                BigDecimal[] numD60MoneyAll = new BigDecimal[61];                // D0-D60累计产生的收益金额
                BigDecimal[] numD60ROIDay = new BigDecimal[61];                  // D0-D60当日的ROI
                BigDecimal[] numD60ROIAll = new BigDecimal[61];                  // D0-D60累计的ROI
                if (item.getAddConvertMemberD60() != null && item.getAddConvertMemberD60().length() > 0) {
                    String[] numD60MemberAllStrs = item.getAddConvertMemberD60().split("#");
                    for (String numD60MemberAllStr : numD60MemberAllStrs) {
                        String[] numD60MemberAllD60 = numD60MemberAllStr.split(",");
                        for (int i = 0; i < numD60MemberAllD60.length; i++) {
                            if (numD60MemberAllD60[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                                numD60MemberAll[i] = defaultIfNull(numD60MemberAll[i], 0) + Integer.parseInt(numD60MemberAllD60[i]);
                            } else {
                                numD60MemberAll[i] = null;
                            }
                        }
                    }
                }
                if (item.getAddConvertMoneyDayD60() != null && item.getAddConvertMoneyDayD60().length() > 0) {
                    String[] addConvertMoneyDayStrs = item.getAddConvertMoneyDayD60().split("#");
                    for (String addConvertMoneyDayStr : addConvertMoneyDayStrs) {
                        String[] addConvertMoneyDayD60 = addConvertMoneyDayStr.split(",");
                        for (int i = 0; i < addConvertMoneyDayD60.length; i++) {
                            if (addConvertMoneyDayD60[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                                numD60MoneyDay[i] = DoubleUtil.addB(defaultIfNull(numD60MoneyDay[i], BigDecimal.ZERO), new BigDecimal(addConvertMoneyDayD60[i]));
                            } else {
                                numD60MoneyDay[i] = null;
                            }
                        }
                    }
                }

                numD60MoneyAll = querySumamry(numD60MoneyDay);                   // 累计金额
                BigDecimal totalIncome = BigDecimal.ZERO;                        // 新增用户总收入

                for (int k = 0; k < numD60MoneyDay.length; k++) {
                    totalIncome = DoubleUtil.addB(totalIncome, defaultIfNull(numD60MoneyDay[k], BigDecimal.ZERO));
                }

                BigDecimal adMoneyConsume = defaultIfNull(item.getAdMoneyConsume(), BigDecimal.ZERO);
                Integer convertMemberCount = defaultIfNull(item.getConvertMemberCountRecharge(), 0) +
                        defaultIfNull(item.getConvertMemeberCountAd(), 0); // 转化用户数
                Integer addMemberCount = defaultIfNull(item.getMemberCountAdd(), 0);
                // 累计总收入金额➗累计转化用户数
                BigDecimal arppu = BigDecimal.ZERO;
                if (convertMemberCount > 0) {
                    arppu = DoubleUtil.divB(totalIncome, new BigDecimal(convertMemberCount));
                }
                // 累计总收入金额➗累计消耗
                BigDecimal returnRatio = BigDecimal.ZERO;
                if (adMoneyConsume.compareTo(BigDecimal.ZERO) > 0) {
                    returnRatio = DoubleUtil.mulB(DoubleUtil.divB(totalIncome, adMoneyConsume), new BigDecimal(100));
                }
                // 累计总收入金额➗当日新增用户数
                BigDecimal LTV60D = BigDecimal.ZERO;
                if (addMemberCount > 0) {
                    LTV60D = DoubleUtil.divB(totalIncome, new BigDecimal(addMemberCount));
                }
                // 指当日新增用户中在当日充值+在当产生广告收益的用户数
                BigDecimal convertMemberCost = BigDecimal.ZERO;      // 转化用户成本
                if (convertMemberCount > 0) {
                    convertMemberCost = DoubleUtil.divB(adMoneyConsume, convertMemberCount);
                }

                // 消耗金额➗累计新增用户数
                BigDecimal addMemberCost = BigDecimal.ZERO;         // 新增用户成本
                if (addMemberCount > 0) {
                    addMemberCost = DoubleUtil.divB(adMoneyConsume, addMemberCount);
                }
                item.setAddMemberCost(addMemberCost);
                item.setConvertMemberCost(convertMemberCost);

                item.setArppu(arppu);
                item.setReturnRatio(returnRatio);
                item.setLTV60D(LTV60D);
                item.setConvertMemberCount(convertMemberCount);
                item.setIncome(totalIncome);
                item.setIncomeAd(totalIncome);

                if (adMoneyConsume.compareTo(BigDecimal.ZERO) > 0) {
                    for (int i = 0; i < 61; i++) {
                        if (numD60MoneyDay[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                            numD60ROIDay[i] = DoubleUtil.divB(DoubleUtil.mulB(numD60MoneyDay[i], BigDecimalVar.BD_100),
                                    item.getAdMoneyConsume());
                        } else {
                            numD60ROIDay[i] = null;
                        }
                        if (numD60MoneyAll[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                            numD60ROIAll[i] = DoubleUtil.divB(DoubleUtil.mulB(numD60MoneyAll[i], BigDecimalVar.BD_100),
                                    item.getAdMoneyConsume());
                        } else {
                            numD60ROIAll[i] = null;
                        }
                    }
                } else {
                    for (int i = 0; i < 61; i++) {
                        if (checkDateExpire(item.getStatisDate(), i)) {
                            numD60ROIDay[i] = BigDecimal.ZERO;
                            numD60ROIAll[i] = BigDecimal.ZERO;
                        }
                    }
                }
                item.setNumD60MemberAll(numD60MemberAll);
                item.setNumD60MoneyDay(numD60MoneyDay);
                item.setNumD60MoneyAll(numD60MoneyAll);
                item.setNumD60ROIDay(numD60ROIDay);
                item.setNumD60ROIAll(numD60ROIAll);

                List<Object> row = new ArrayList<>();

                row.add(item.getStatisDate());
                CollUtil.addNoRepeat(rowHeadNames, "日期");

                row.add(item.getValidLinkCount());
                CollUtil.addNoRepeat(rowHeadNames, "有效链接数");

                row.add(item.getAdMoneyConsume());
                CollUtil.addNoRepeat(rowHeadNames, "投放消耗");

                row.add(item.getMemberCountAdd());
                CollUtil.addNoRepeat(rowHeadNames, "新增用户人数");

                row.add(item.getMemberCountAddColor());
                CollUtil.addNoRepeat(rowHeadNames, "染色用户人数");

                row.add(item.getConvertMemberCount());
                CollUtil.addNoRepeat(rowHeadNames, "转化用户数");

                row.add(item.getConvertMemeberCountAd());
                CollUtil.addNoRepeat(rowHeadNames, "转化用户(广告)");

                row.add(item.getConvertMemberCountRecharge());
                CollUtil.addNoRepeat(rowHeadNames, "转化用户(充值)");

                row.add(item.getAddMemberCost());
                CollUtil.addNoRepeat(rowHeadNames, "新增用户成本");

                row.add(item.getConvertMemberCost());
                CollUtil.addNoRepeat(rowHeadNames, "转化用户成本");

                row.add(item.getArppu());
                CollUtil.addNoRepeat(rowHeadNames, "arppu");

                row.add(item.getIncome());
                CollUtil.addNoRepeat(rowHeadNames, "累计收入");

                row.add(item.getIncomeAd());
                CollUtil.addNoRepeat(rowHeadNames, "累计广告收入");

                row.add(item.getIncomeRecharge());
                CollUtil.addNoRepeat(rowHeadNames, "累计充值收入");

                row.add(item.getReturnRatio());
                CollUtil.addNoRepeat(rowHeadNames, "累计回报率");

                row.add(item.getLTV60D());
                CollUtil.addNoRepeat(rowHeadNames, "60DLTV");

                for (int i = 0; i < 61; i++) {
                    String d60 = parseD60(item, i);
                    row.add(d60);
                    CollUtil.addNoRepeat(rowHeadNames, "D" + i);
                }
                dataList.add(row);
            }
        } else {
            List<LinkAnalysisPO> list = launchAnalysisMapper.queryFreeLinkROIAnalysis(params);
            if (list == null || list.size() == 0) {
                return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
            }
            for (LinkAnalysisPO item : list) {
                String encryptionId = encode(item.getLinkId());
                BigDecimal adMoneyConsume = defaultIfNull(item.getAdMoneyConsume(), BigDecimal.ZERO);
                Integer[] numD60MemberAll = new Integer[61];                     // D0-D60当日产生收益的用户数
                BigDecimal[] numD60MoneyDay = new BigDecimal[61];                // D0-D60当日产生的收益金额
                BigDecimal[] numD60MoneyAll = new BigDecimal[61];                // D0-D60累计产生的收益金额
                BigDecimal[] numD60ROIDay = new BigDecimal[61];                  // D0-D60当日的ROI
                BigDecimal[] numD60ROIAll = new BigDecimal[61];                  // D0-D60累计的ROI
                if (item.getAddConvertMemberD60() != null && item.getAddConvertMemberD60().length() > 0) {
                    String[] numD60MemberAllStrs = item.getAddConvertMemberD60().split("#");
                    for (String numD60MemberAllStr : numD60MemberAllStrs) {
                        String[] numD60MemberAllD60 = numD60MemberAllStr.split(",");
                        for (int i = 0; i < numD60MemberAllD60.length; i++) {
                            if (numD60MemberAllD60[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                                numD60MemberAll[i] = defaultIfNull(numD60MemberAll[i], 0) + Integer.parseInt(numD60MemberAllD60[i]);
                            } else {
                                numD60MemberAll[i] = null;
                            }
                        }
                    }
                }
                if (item.getAddConvertMoneyDayD60() != null && item.getAddConvertMoneyDayD60().length() > 0) {
                    String[] addConvertMoneyDayStrs = item.getAddConvertMoneyDayD60().split("#");
                    for (String addConvertMoneyDayStr : addConvertMoneyDayStrs) {
                        String[] addConvertMoneyDayD60 = addConvertMoneyDayStr.split(",");
                        for (int i = 0; i < addConvertMoneyDayD60.length; i++) {
                            if (addConvertMoneyDayD60[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                                numD60MoneyDay[i] = DoubleUtil.addB(defaultIfNull(numD60MoneyDay[i], BigDecimal.ZERO), new BigDecimal(addConvertMoneyDayD60[i]));
                            } else {
                                numD60MoneyDay[i] = null;
                            }
                        }
                    }
                }
                numD60MoneyAll = querySumamry(numD60MoneyDay);                   // 累计金额
                BigDecimal totalIncome = BigDecimal.ZERO;                        // 新增用户总收入
                for (int k = 0; k < numD60MoneyDay.length; k++) {
                    totalIncome = DoubleUtil.addB(totalIncome, defaultIfNull(numD60MoneyDay[k], BigDecimal.ZERO));
                }
                if (adMoneyConsume.compareTo(BigDecimal.ZERO) > 0) {
                    for (int i = 0; i < 61; i++) {
                        if (numD60MoneyDay[i] != null && checkDateExpire(item.getStatisDate(), i)) {
                            numD60ROIDay[i] = DoubleUtil.divB(DoubleUtil.mulB(defaultIfNull(numD60MoneyDay[i], BigDecimal.ZERO), BigDecimalVar.BD_100),
                                    adMoneyConsume);
                            numD60ROIAll[i] = DoubleUtil.divB(DoubleUtil.mulB(defaultIfNull(numD60MoneyAll[i], BigDecimal.ZERO), BigDecimalVar.BD_100),
                                    adMoneyConsume);
                        } else {
                            numD60ROIDay[i] = null;
                            numD60ROIAll[i] = null;
                        }
                    }
                } else {
                    for (int i = 0; i < 61; i++) {
                        if (checkDateExpire(item.getStatisDate(), i)) {
                            numD60ROIDay[i] = BigDecimal.ZERO;
                            numD60ROIAll[i] = BigDecimal.ZERO;
                        }
                    }
                }
                Integer convertMemberCount = defaultIfNull(item.getConvertMemberCount(), 0); // 转化用户数
                Integer addMemberCount = defaultIfNull(item.getMemberCountAddAll(), 0);
                // 累计总收入金额➗累计转化用户数
                BigDecimal arppu = BigDecimal.ZERO;
                if (convertMemberCount > 0) {
                    arppu = DoubleUtil.divB(totalIncome, new BigDecimal(convertMemberCount));
                }
                // 累计总收入金额➗累计消耗
                BigDecimal returnRatio = BigDecimal.ZERO;
                if (adMoneyConsume.compareTo(BigDecimal.ZERO) > 0) {
                    returnRatio = DoubleUtil.mulB(DoubleUtil.divB(totalIncome, adMoneyConsume), new BigDecimal(100));
                }
                // 累计总收入金额➗当日新增用户数
                BigDecimal LTV60D = BigDecimal.ZERO;
                if (addMemberCount > 0) {
                    LTV60D = DoubleUtil.divB(totalIncome, new BigDecimal(addMemberCount));
                }
                // 指当日新增用户中在当日充值+在当产生广告收益的用户数
                BigDecimal convertMemberCost = BigDecimal.ZERO;      // 转化用户成本
                if (convertMemberCount > 0) {
                    convertMemberCost = DoubleUtil.divB(adMoneyConsume, convertMemberCount);
                }

                // 消耗金额➗累计新增用户数
                BigDecimal addMemberCost = BigDecimal.ZERO;         // 新增用户成本
                if (addMemberCount > 0) {
                    addMemberCost = DoubleUtil.divB(adMoneyConsume, addMemberCount);
                }
                item.setAddMemberCost(addMemberCost);
                item.setConvertMemberCost(convertMemberCost);

                item.setArppu(arppu);
                item.setReturnRatio(returnRatio);
                item.setLTV60D(LTV60D);
                item.setEncryptionId(encryptionId);
                item.setIncome(totalIncome);
                item.setNumD60MemberAll(numD60MemberAll);
                item.setNumD60MoneyDay(numD60MoneyDay);
                item.setNumD60MoneyAll(numD60MoneyAll);
                item.setNumD60ROIDay(numD60ROIDay);
                item.setNumD60ROIAll(numD60ROIAll);

                List<Object> row = new ArrayList<>();

                row.add(item.getLinkId());
                CollUtil.addNoRepeat(rowHeadNames, "链接Id");

                row.add(item.getLinkName());
                CollUtil.addNoRepeat(rowHeadNames, "链接名称");

                row.add(item.getAdvUserName());
                CollUtil.addNoRepeat(rowHeadNames, "优化师");

                row.add(item.getRetailName());
                CollUtil.addNoRepeat(rowHeadNames, "分销商名称");

                row.add(item.getAdMoneyConsumeToday());
                CollUtil.addNoRepeat(rowHeadNames, "投放消耗-今日");

                row.add(item.getAdMoneyConsume());
                CollUtil.addNoRepeat(rowHeadNames, "投放消耗-累计");

                row.add(item.getMemberCountAddToday());
                CollUtil.addNoRepeat(rowHeadNames, "当日新增用户数");

                row.add(item.getMemberCountAddAll());
                CollUtil.addNoRepeat(rowHeadNames, "累计新增用户数");

                row.add(item.getMemberCountAddColorToday());
                CollUtil.addNoRepeat(rowHeadNames, "染色用户数-今日");

                row.add(item.getMemberCountAddColor());
                CollUtil.addNoRepeat(rowHeadNames, "染色用户数-累计");

                row.add(item.getConvertMemberCount());
                CollUtil.addNoRepeat(rowHeadNames, "转化用户数");

                row.add(item.getConvertMemberCountToday());
                CollUtil.addNoRepeat(rowHeadNames, "累计转化用户数");

                row.add(item.getAddMemberCost());
                CollUtil.addNoRepeat(rowHeadNames, "新增用户成本");

                row.add(item.getConvertMemberCost());
                CollUtil.addNoRepeat(rowHeadNames, "转化用户成本");

                row.add(item.getArppu());
                CollUtil.addNoRepeat(rowHeadNames, "ARPPU");

                row.add(item.getReturnRatio());
                CollUtil.addNoRepeat(rowHeadNames, "累计回报率");

                row.add(item.getLTV60D());
                CollUtil.addNoRepeat(rowHeadNames, "60DLTV");

                for (int i = 0; i < 61; i++) {
                    String d60 = parseD60(item, i);
                    row.add(d60);
                    CollUtil.addNoRepeat(rowHeadNames, "D" + i);
                }
                dataList.add(row);
            }
        }
        String title = "ROI梯度表-数据汇总";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * ROI梯度表-累计数据汇总-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportLinkRoiDetailSummaryAnalysis(SessionVO sessionVO, LinkAnalysisPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_LINK_ROI + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        List<LinkAnalysisPO> list = dataService.getLinkRoiDetailSummaryAnalysis(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (LinkAnalysisPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            switch (params.getGroupByColum()) {
                case 1: {
                    // 链接
                    row.add(cur.getId());
                    CollUtil.addNoRepeat(rowHeadNames, "链接ID");

                    row.add(cur.getLinkName());
                    CollUtil.addNoRepeat(rowHeadNames, "链接名称");

                    row.add(DateUtil.format07(cur.getCreateTime()));
                    CollUtil.addNoRepeat(rowHeadNames, "创建时间");
                }
                break;
                case 2: {
                    // 分销商
                    row.add(cur.getRetailName());
                    CollUtil.addNoRepeat(rowHeadNames, "分销商名称");

                    // 类别:1=自投、2=代投、3=独立分销商
                    row.add(cur.getRetailType() == 1 ? "自投" : cur.getRetailType() == 2 ? "代投" : "独立分销商");
                    CollUtil.addNoRepeat(rowHeadNames, "分销商类型");

                    row.add(cur.getAllLinkCount());
                    CollUtil.addNoRepeat(rowHeadNames, "关联链接数");
                }
                break;
                case 3: {
                    // 优化师
                    row.add(cur.getAdvUserName());
                    CollUtil.addNoRepeat(rowHeadNames, "优化师名称");

                    row.add(cur.getRetailName());
                    CollUtil.addNoRepeat(rowHeadNames, "归属分销商");

                    row.add(cur.getAllLinkCount());
                    CollUtil.addNoRepeat(rowHeadNames, "关联链接数");
                }
                break;
                case 4: {
                    // 短剧
                    row.add(cur.getId());
                    CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(ObjectUtils.defaultIfNull(params.getContentType(), ContentTypeEnum.DRAMA.index)) + "ID");

                    row.add(cur.getDramaName());
                    CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(ObjectUtils.defaultIfNull(params.getContentType(), ContentTypeEnum.DRAMA.index)) + "名称");

                    row.add(cur.getDramaGrantRetailCount());
                    CollUtil.addNoRepeat(rowHeadNames, "授权分销商数");

                    row.add(cur.getAllLinkCount());
                    CollUtil.addNoRepeat(rowHeadNames, "关联链接数");
                }
                break;
                case 5: {
                    // 链路
                    row.add(cur.getRoadName());
                    CollUtil.addNoRepeat(rowHeadNames, "投放链路");

                    row.add(cur.getAllLinkCount());
                    CollUtil.addNoRepeat(rowHeadNames, "关联链接数");
                }
                break;
            }

            if (params.getGroupByColum() > 0) {
                row.add(cur.getAdMoneyConsume());
                CollUtil.addNoRepeat(rowHeadNames, "累计消耗");

                row.add(cur.getMemberCountAddAll());
                CollUtil.addNoRepeat(rowHeadNames, "新增用户");

                row.add(cur.getMemberCountAddColor());
                CollUtil.addNoRepeat(rowHeadNames, "染色用户");

                row.add(cur.getRechargeMoneySumAddAll());
                CollUtil.addNoRepeat(rowHeadNames, "累计充值金额");

                row.add(cur.getRechargeMemberCountAddAll());
                CollUtil.addNoRepeat(rowHeadNames, "累计充值人数");

                row.add(cur.getRechargeOrderAddSumAll());
                CollUtil.addNoRepeat(rowHeadNames, "累计充值笔数");

                row.add(cur.getMemberCost());
                CollUtil.addNoRepeat(rowHeadNames, "新增用户成本");

                row.add(cur.getPayMemberCost());
                CollUtil.addNoRepeat(rowHeadNames, "充值用户成本");

                row.add(cur.getArpu());
                CollUtil.addNoRepeat(rowHeadNames, "ARPU");

                row.add(cur.getArppu());
                CollUtil.addNoRepeat(rowHeadNames, "ARPPU");

                row.add(cur.getBackRate() + "%");
                CollUtil.addNoRepeat(rowHeadNames, "累计回报率");
            }

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "ROI梯度表-累计数据汇总";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * ROI梯度表-实时监测数据（今日）-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportLinkRoiDetailTodayAnalysis(SessionVO sessionVO, LinkAnalysisPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_LINK_ROI + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        List<LinkAnalysisPO> list = dataService.getLinkRoiDetailTodayAnalysis(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (LinkAnalysisPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            if (params.getGroupByColum() > 0) {
                row.add(cur.getStatisDate());
                CollUtil.addNoRepeat(rowHeadNames, "日期");

                row.add(cur.getMemberCountAdd());
                CollUtil.addNoRepeat(rowHeadNames, "今日新增用户数");

                row.add(cur.getMemberCountAddColor());
                CollUtil.addNoRepeat(rowHeadNames, "今日染色用户数");

                row.add(cur.getAdMoneyConsume());
                CollUtil.addNoRepeat(rowHeadNames, "今日消耗");

                row.add(cur.getBackRate() + "%");
                CollUtil.addNoRepeat(rowHeadNames, "今日ROI");

                List<LinkAnalysisPO> eachList = cur.getList();
                if (CollUtil.hasContent(eachList)) {
                    String str = null;
                    for (LinkAnalysisPO po : eachList) {
                        switch (po.getGroupByColum()) {
                            case 1: {
                                str = "今日新增用户充值";
                            }
                            break;
                            case 2: {
                                str = "普通充值";
                            }
                            break;
                            case 3: {
                                str = "会员充值（VIP）";
                            }
                            break;
                            case 4: {
                                str = "今日总用户充值";
                            }
                            break;
                        }
                        row.add(Objects.isNull(cur.getRechargeMoneySum()) ? BigDecimal.ZERO : cur.getRechargeMoneySum());
                        CollUtil.addNoRepeat(rowHeadNames, str + "金额");

                        row.add(Objects.isNull(cur.getRechargeMemberCount()) ? BigDecimal.ZERO : cur.getRechargeMemberCount());
                        CollUtil.addNoRepeat(rowHeadNames, str + "人数");

                        row.add(Objects.isNull(cur.getRechargeCount()) ? BigDecimal.ZERO : cur.getRechargeCount());
                        CollUtil.addNoRepeat(rowHeadNames, str + "笔数");

                        row.add(Objects.isNull(cur.getMemberSingleMoney()) ? BigDecimal.ZERO : cur.getMemberSingleMoney());
                        CollUtil.addNoRepeat(rowHeadNames, str + "客单价");

                        row.add(Objects.isNull(cur.getMemberPayRate()) ? BigDecimal.ZERO + "%" : cur.getMemberPayRate());
                        CollUtil.addNoRepeat(rowHeadNames, str + "付费率");
                    }
                }
            }

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "ROI梯度表-实时监测数据（今日）";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * ROI梯度表-每日ROI数据-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportLinkRoiCorePerDayAnalysis(SessionVO sessionVO, LinkAnalysisPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_LINK_ROI + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        LinkAnalysisPO summary = new LinkAnalysisPO();
        List<LinkAnalysisPO> list = dataService.getLinkRoiCorePerDayAnalysis(params, summary);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        list.add(0, summary);
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        int i = 0;
        for (LinkAnalysisPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)
            if (i == 0) {
                row.add("累计");
            } else {
                row.add(cur.getStatisDate());
            }
            CollUtil.addNoRepeat(rowHeadNames, "日期");

            row.add(cur.getAdMoneyConsume());
            CollUtil.addNoRepeat(rowHeadNames, "投放消耗");

            row.add(cur.getMemberCountAdd());
            CollUtil.addNoRepeat(rowHeadNames, "新增用户");

            row.add(cur.getMemberCountAddColor());
            CollUtil.addNoRepeat(rowHeadNames, "染色用户");

            row.add(cur.getRechargeMemberCountAddAll());
            CollUtil.addNoRepeat(rowHeadNames, "充值用户");

            row.add(cur.getRechargeMoneySumAddAll());
            CollUtil.addNoRepeat(rowHeadNames, "累计充值金额");

            row.add(cur.getMemberCost());
            CollUtil.addNoRepeat(rowHeadNames, "新增用户成本");

            row.add(cur.getPayMemberCost());
            CollUtil.addNoRepeat(rowHeadNames, "充值用户成本");

            row.add(cur.getArpu());
            CollUtil.addNoRepeat(rowHeadNames, "ARPU");

            row.add(cur.getBackRate() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "累计ROI");

            Integer[] numD60RechAll = cur.getNumD60RechAll();// D0-D60充值人数
            BigDecimal[] moneyD60RechAll = cur.getMoneyD60RechAll();// D0-D60充值金额
            BigDecimal[] moneyD60RechDaySum = cur.getMoneyD60RechDaySum();// D0-D60充值金额-累计
            BigDecimal[] backD60RechAll = cur.getBackD60RechAll();// D0-D60回传率
            BigDecimal[] backD60RechDaySum = cur.getBackD60RechDaySum();// D0-D60回传率-累计
            for (int j = 0; j <= 60; j++) {
                row.add(numD60RechAll != null && numD60RechAll[j] != null ? numD60RechAll[j] : "-");
                CollUtil.addNoRepeat(rowHeadNames, "D" + j + "新增充值用户");

                row.add(moneyD60RechAll != null && moneyD60RechAll[j] != null ? moneyD60RechAll[j] : "-");
                CollUtil.addNoRepeat(rowHeadNames, "D" + j + "新增充值金额");

                row.add(moneyD60RechDaySum != null && moneyD60RechDaySum[j] != null ? moneyD60RechDaySum[j] : "-");
                CollUtil.addNoRepeat(rowHeadNames, "D" + j + "累计充值金额");

                row.add(backD60RechAll != null && backD60RechAll[j] != null ? (backD60RechAll[j] + "%") : "-");
                CollUtil.addNoRepeat(rowHeadNames, "D" + j + "新增回报率");

                row.add(backD60RechDaySum != null && backD60RechDaySum[j] != null ? (backD60RechDaySum[j] + "%") : "-");
                CollUtil.addNoRepeat(rowHeadNames, "D" + j + "累计回报率");
            }

            dataList.add(row);
            i++;
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "ROI梯度表-每日ROI数据";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    public String parseD60(LinkAnalysisPO item, Integer i) {
        String data1 = "";
        if (item.getNumD60MemberAll()[i] == null) {
            data1 = "-";
        } else {
            data1 = String.valueOf(item.getNumD60MemberAll()[i]);
        }
        String data2 = "";
        if (item.getNumD60MoneyDay()[i] == null) {
            data2 = "-";
        } else {
            data2 = "￥" + item.getNumD60MoneyDay()[i];
        }
        String data3 = "";
        if (item.getNumD60MoneyAll()[i] == null) {
            data3 = "-";
        } else {
            data3 = "￥" + item.getNumD60MoneyAll()[i];
        }
        String data4 = "";
        if (item.getNumD60ROIDay()[i] == null) {
            data4 = "-";
        } else {
            data4 = item.getNumD60ROIDay()[i] + "%";
        }
        String data5 = "";
        if (item.getNumD60ROIAll()[i] == null) {
            data5 = "-";
        } else {
            data5 = item.getNumD60ROIAll()[i] + "%";
        }
        String d60 = data1 + "\n" + data2 + "/" + data3 + "\n" + data4 + "/" + data5;
        return d60;
    }

    public static BigDecimal[] querySumamry(BigDecimal[] a) {
        List<BigDecimal> bList = new ArrayList();
        for (int k = 0; k < a.length; k++) {
            if (k == 0) {
                bList.add(k, a[k]);
            }
            if (k >= 1) {
                if (a[k] != null) {
                    bList.add(k, DoubleUtil.addB(
                            defaultIfNull(bList.get(k - 1), BigDecimal.ZERO),
                            defaultIfNull(a[k], BigDecimal.ZERO)));
                } else {
                    bList.add(k, a[k]);
                }
            }
        }
        BigDecimal[] array = bList.stream().toArray(BigDecimal[]::new);
        return array;
    }

    public boolean checkDateExpire(String statisDate, Integer i) {
        Date addDate = DateUtil.addDays(DateUtil.format09(statisDate), i);
        if (addDate.before(DateUtil.endOfDay())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
