/*
 * Powered By fast.up
 */
package com.fast.service.analysis;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.BigDecimalVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.analysis.DramaAnalysisMapper;
import com.fast.mapper.fee.FastFeeRuleMapper;
import com.fast.mapper.promote.FastLinkMapper;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.promote.FastLinkQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 短剧数据对比
 *
 * <AUTHOR>
 */
@Service
public class DramaContrastAnalysisService extends BaseService {

    @Autowired
    private DramaAnalysisMapper dramaAnalysisMapper;
    @Autowired
    private FastLinkMapper linkMapper;
    @Autowired
    private FastFeeRuleMapper feeRuleMapper;
    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private DramaDetailAnalysisService detailAnalysisService;

    /**
     * 核心数据对比
     */
    public List<JSONObject> getDramaContrastCoreAnalysis(DramaAnalysisPO params, List<DramaAnalysisPO> paramsList) {
        List<JSONObject> result = new ArrayList<>();
        List<Integer> dramaIds = new ArrayList<>();
        for (DramaAnalysisPO param : paramsList) {
            dramaIds.add(param.getDramaId());
        }
        // 查询短剧
        FastDramaPO query = new FastDramaPO();
        query.setIds(StrUtil.joinNoRepeat(dramaIds));
        Map<Integer, String> nameMap = dramaService.queryDramaNameMap(query);

        for (DramaAnalysisPO po : paramsList) {
            List<DramaAnalysisPO> list;
            switch (params.getContrastDataType()) {
                case 1:// 广告消耗金额
                    list = getAdMoneyConsume(params, po);
                    break;
                case 2:// 充值金额
                    list = getRechargeMoneySum(params, po);
                    break;
                case 3:// ROI
                    list = getRoi(params, po);
                    break;
                case 4:// K币消耗值
                    list = getCoinConsume(params, po);
                    break;
                case 5:// 充值人数
                    list = getRechargeMemberCount(params, po);
                    break;
                case 6:// 观看人数
                    list = getWatchMemberCount(params, po);
                    break;
                case 7:// 完播率
                    list = getWatchFinishRate(params, po);
                    break;
                case 8:// 留存率
                    list = getRemainRate(params, po);
                    break;
                case 9:// 复充率
                    list = getRepeatPayRate(params, po);
                    break;
                case 10:// 订阅数
                    list = getUnlockCount(params, po);
                    break;
                case 11:// 订阅金额
                    list = getUnlockMoney(params, po);
                    break;
                default:
                    throw new MyException("对比的数据维度不存在");
            }

            JSONObject data = new JSONObject();
            data.put("dramaId", po.getDramaId());
            data.put("dramaName", nameMap.get(po.getDramaId()));
            data.put("list", defaultIfNull(list, new ArrayList<>()));

            // 计算统计汇总数据
            data.put("dataSum", po.getData());

            // 选择了一个链接id
            if (notBlank(po.getLinkIds()) && StrUtil.isNum(po.getLinkIds())) {
                data.put("startNum", feeRuleMapper.getDramaStartNumByLink(po.getLinkIds()));
            }

            result.add(data);
        }
        return result;
    }

    /**
     * 订阅金额对比
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getUnlockMoney(DramaAnalysisPO params, DramaAnalysisPO po) {
        List<DramaAnalysisPO> list = new ArrayList<>();
        DramaAnalysisPO query = new DramaAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setContentType(po.getContentType());
        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setPayTimeS(start);
        query.setPayTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        List<DramaAnalysisPO> dataList = dramaAnalysisMapper.queryUnlockMoneyCountDayList(query);
        BigDecimal allData = BigDecimal.ZERO;
        HashMap<String, BigDecimal> dataMap = new HashMap<>();
        dataList.forEach(cur -> dataMap.put(cur.getDataDay(), defaultIfNull(DoubleUtil.divB(cur.getUnlockCoin(), new BigDecimal(100)), BigDecimal.ZERO)));
        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            BigDecimal data = defaultIfNull(dataMap.get(dataDay), BigDecimal.ZERO);
            DramaAnalysisPO r = new DramaAnalysisPO();
            r.setDay(i);
            r.setData(data);
            allData = DoubleUtil.addB(allData, data);
            list.add(r);
        }
        po.setData(allData);
        return list;
    }

    /**
     * 订阅人数对比
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getUnlockCount(DramaAnalysisPO params, DramaAnalysisPO po) {
        List<DramaAnalysisPO> list = new ArrayList<>();
        DramaAnalysisPO query = new DramaAnalysisPO();
        query.setDramaId(po.getDramaId());
        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setCreateTimeS(start);
        query.setCreateTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        query.setContentType(params.getContentType());
        List<DramaAnalysisPO> dataList = dramaAnalysisMapper.queryUnlockMemberCountDayList(query);
        int allData = 0;
        HashMap<String, Integer> dataMap = new HashMap<>();
        dataList.forEach(cur -> dataMap.put(cur.getDataDay(), defaultIfNull(cur.getUnlockCount(), 0)));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            Integer data = defaultIfNull(dataMap.get(dataDay), 0);
            DramaAnalysisPO r = new DramaAnalysisPO();
            r.setDay(i);
            r.setData(data);
            allData = allData + data;

            list.add(r);
        }
        po.setData(allData);
        return list;
    }

    /**
     * 完播率
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getWatchFinishRate(DramaAnalysisPO params, DramaAnalysisPO po) {
        List<DramaAnalysisPO> list = new ArrayList<>();
        DramaAnalysisPO query = new DramaAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setRetailId(po.getRetailId());
        query.setRetailIds(po.getRetailIds());
        query.setLinkIds(getLinkIds(po.getAdvUserIds(), po.getLinkIds()));

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setFinishDateS(DateUtil.format06Int(start));
        query.setFinishDateE(DateUtil.format06Int(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        List<DramaAnalysisPO> watchFinishMemberCountList = dramaAnalysisMapper.queryWatchFinishMemberCountDayListV2(query);
        int allData1 = 0;

        Map<String, Integer> watchFinishMemberCountMap = new HashMap<>();
        watchFinishMemberCountList.forEach(cur -> watchFinishMemberCountMap.put(cur.getDataDay(), defaultIfNull(cur.getWatchFinishMemberCount(), 0)));

        List<DramaAnalysisPO> watchMemberCountList = getWatchMemberCount(params, po);
        int allData2 = (Integer) po.getData();
        Map<Integer, Integer> watchMemberCountMap = new HashMap<>();
        watchMemberCountList.forEach(cur -> watchMemberCountMap.put(cur.getDay(), (Integer) cur.getData()));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            int data1 = defaultIfNull(watchFinishMemberCountMap.get(dataDay), 0);
            Integer data2 = defaultIfNull(watchMemberCountMap.get(i), 0);

            BigDecimal data = DoubleUtil.divB4Zero(data1 * 100, data2);

            DramaAnalysisPO r = new DramaAnalysisPO();
            r.setDay(i);
            r.setData(data);
            allData1 = allData1 + data1;

            list.add(r);
        }
        po.setData(DoubleUtil.divB4Zero(allData1 * 100, allData2));
        return list;
    }

    /**
     * 留存率
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getRemainRate(DramaAnalysisPO params, DramaAnalysisPO po) {
        DramaAnalysisPO query = new DramaAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setRetailId(po.getRetailId());
        query.setRetailIds(po.getRetailIds());
        query.setLinkIds(getLinkIds(po.getAdvUserIds(), po.getLinkIds()));

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setWatchTimeS(start);
        query.setWatchTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        return detailAnalysisService.getDramaDetailRemainAnalysisList(query);
    }

    /**
     * 复充率
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getRepeatPayRate(DramaAnalysisPO params, DramaAnalysisPO po) {
        DramaAnalysisPO query = new DramaAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setRetailId(po.getRetailId());
        query.setRetailIds(po.getRetailIds());
        query.setLinkIds(getLinkIds(po.getAdvUserIds(), po.getLinkIds()));

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setCreateTimeS(start);
        query.setCreateTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        return detailAnalysisService.getDramaDetailPayAnalysisList(query);
    }

    /**
     * 观看人数
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getWatchMemberCount(DramaAnalysisPO params, DramaAnalysisPO po) {
        List<DramaAnalysisPO> list = new ArrayList<>();
        DramaAnalysisPO query = new DramaAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setRetailId(po.getRetailId());
        query.setRetailIds(po.getRetailIds());
        query.setLinkIds(getLinkIds(po.getAdvUserIds(), po.getLinkIds()));

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setWatchTimeS(start);
        query.setWatchTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        List<DramaAnalysisPO> dataList = dramaAnalysisMapper.queryWatchMemberCountDayList(query);
        int allData = 0;

        HashMap<String, Integer> dataMap = new HashMap<>();
        dataList.forEach(cur -> dataMap.put(cur.getDataDay(), defaultIfNull(cur.getWatchMemberCount(), 0)));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            Integer data = defaultIfNull(dataMap.get(dataDay), 0);

            DramaAnalysisPO r = new DramaAnalysisPO();
            r.setDay(i);
            r.setData(data);
            allData = allData + data;

            list.add(r);
        }
        po.setData(allData);
        return list;
    }

    /**
     * 充值人数
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getRechargeMemberCount(DramaAnalysisPO params, DramaAnalysisPO po) {
        List<DramaAnalysisPO> list = new ArrayList<>();
        DramaAnalysisPO query = new DramaAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setRetailId(po.getRetailId());
        query.setRetailIds(po.getRetailIds());
        query.setLinkIds(getLinkIds(po.getAdvUserIds(), po.getLinkIds()));

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setPayTimeS(start);
        query.setPayTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        List<DramaAnalysisPO> dataList = dramaAnalysisMapper.queryRechargeMemberCountDayList(query);
        int allData = 0;

        HashMap<String, Integer> dataMap = new HashMap<>();
        dataList.forEach(cur -> dataMap.put(cur.getDataDay(), defaultIfNull(cur.getRechargeMemberCount(), 0)));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            Integer data = defaultIfNull(dataMap.get(dataDay), 0);

            DramaAnalysisPO r = new DramaAnalysisPO();
            r.setDay(i);
            r.setData(data);
            allData = allData + data;

            list.add(r);
        }
        po.setData(allData);
        return list;
    }

    /**
     * K币消耗值
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getCoinConsume(DramaAnalysisPO params, DramaAnalysisPO po) {
        List<DramaAnalysisPO> list = new ArrayList<>();
        DramaAnalysisPO query = new DramaAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setRetailId(po.getRetailId());
        query.setRetailIds(po.getRetailIds());
        query.setLinkIds(getLinkIds(po.getAdvUserIds(), po.getLinkIds()));

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setWatchTimeS(start);
        query.setWatchTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        List<DramaAnalysisPO> dataList = dramaAnalysisMapper.queryCoinConsumeDayList(query);
        long allData = 0;

        HashMap<String, Integer> dataMap = new HashMap<>();
        dataList.forEach(cur -> dataMap.put(cur.getDataDay(), defaultIfNull(cur.getCoinConsume(), 0)));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            Integer data = defaultIfNull(dataMap.get(dataDay), 0);

            DramaAnalysisPO r = new DramaAnalysisPO();
            r.setDay(i);
            r.setData(data);
            allData = allData + data;

            list.add(r);
        }
        po.setData(allData);
        return list;
    }

    /**
     * ROI
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getRoi(DramaAnalysisPO params, DramaAnalysisPO po) {
        List<DramaAnalysisPO> list = new ArrayList<>();
        List<DramaAnalysisPO> rechargeMoneySumList = getRechargeMoneySum(params, po);
        BigDecimal allData1 = (BigDecimal) po.getData();
        Map<Integer, BigDecimal> rechargeMoneySumMap = new HashMap<>();
        rechargeMoneySumList.forEach(cur -> rechargeMoneySumMap.put(cur.getDay(), (BigDecimal) cur.getData()));

        List<DramaAnalysisPO> adMoneyConsumeList = getAdMoneyConsume(params, po);
        BigDecimal allData2 = (BigDecimal) po.getData();
        Map<Integer, BigDecimal> adMoneyConsumeMap = new HashMap<>();
        adMoneyConsumeList.forEach(cur -> adMoneyConsumeMap.put(cur.getDay(), (BigDecimal) cur.getData()));

        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            BigDecimal data1 = defaultIfNull(rechargeMoneySumMap.get(i), BigDecimal.ZERO);
            BigDecimal data2 = defaultIfNull(adMoneyConsumeMap.get(i), BigDecimal.ZERO);

            BigDecimal data = DoubleUtil.divB4Zero(data1.multiply(BigDecimalVar.BD_100), data2);

            DramaAnalysisPO r = new DramaAnalysisPO();
            r.setDay(i);
            r.setData(data);

            list.add(r);
        }
        po.setData(DoubleUtil.divB4Zero(allData1.multiply(BigDecimalVar.BD_100), allData2));
        return list;
    }

    /**
     * 充值金额
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getRechargeMoneySum(DramaAnalysisPO params, DramaAnalysisPO po) {
        List<DramaAnalysisPO> list = new ArrayList<>();
        DramaAnalysisPO query = new DramaAnalysisPO();
        query.setDramaId(po.getDramaId());
        query.setRetailId(po.getRetailId());
        query.setRetailIds(po.getRetailIds());
        query.setLinkIds(getLinkIds(po.getAdvUserIds(), po.getLinkIds()));

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setPayTimeS(start);
        query.setPayTimeE(DateUtil.endOfDay(DateUtil.addDays(start, params.getContrastDataDay() - 1)));
        List<DramaAnalysisPO> dataList = dramaAnalysisMapper.queryRechargeMoneyDayList(query);
        BigDecimal allData = BigDecimal.ZERO;

        HashMap<String, BigDecimal> dataMap = new HashMap<>();
        dataList.forEach(cur -> dataMap.put(cur.getDataDay(), defaultIfNull(cur.getRechargeMoneySum(), BigDecimal.ZERO)));

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            BigDecimal data = defaultIfNull(dataMap.get(dataDay), BigDecimal.ZERO);

            DramaAnalysisPO r = new DramaAnalysisPO();
            r.setDay(i);
            r.setData(data);
            allData = allData.add(data);

            list.add(r);
        }
        po.setData(allData);
        return list;
    }

    /**
     * 广告消耗金额
     *
     * @param params
     * @param po
     * @return
     */
    private List<DramaAnalysisPO> getAdMoneyConsume(DramaAnalysisPO params, DramaAnalysisPO po) {
        List<DramaAnalysisPO> list = new ArrayList<>();
        FastLinkQueryVO query = new FastLinkQueryVO();
        query.setDramaId(po.getDramaId());
        query.setIds(po.getLinkIds());
        query.setMiniIds(po.getMiniIds());
        query.setRetailIds(po.getRetailIds());

        Date start = DateUtil.format09(po.getReleaseDateStr());
        query.setBeginTime(po.getReleaseDateStr());
        query.setEndTime(DateUtil.format09(DateUtil.addDays(start, params.getContrastDataDay() - 1)));

        List<FastLinkQueryVO> dataList = linkMapper.queryListDramaCostDay(query);
        HashMap<String, BigDecimal> dataMap = new HashMap<>();
        dataList.forEach(cur -> dataMap.put(cur.getDataDay(), defaultIfNull(cur.getAdMoneyConsume(), BigDecimal.ZERO)));
        BigDecimal allData = BigDecimal.ZERO;

        Date releaseDate = DateUtil.format09(po.getReleaseDateStr());
        // 组装数据(从上线开始的每天)
        for (int i = 1; i <= params.getContrastDataDay(); i++) {
            String dataDay = DateUtil.format09(DateUtil.addDays(releaseDate, i - 1));
            BigDecimal data = defaultIfNull(dataMap.get(dataDay), BigDecimal.ZERO);

            DramaAnalysisPO r = new DramaAnalysisPO();
            r.setDay(i);
            r.setData(data);
            allData = allData.add(data);
            list.add(r);
        }
        po.setData(allData);
        return list;
    }

    private String getLinkIds(String advUserIds, String linkIds) {
        if (isBlank(advUserIds) && isBlank(linkIds)) {
            return null;
        }
        // 优化师和链接id搜索
        FastLinkPO query = new FastLinkPO();
        query.setAdvUserIds(advUserIds);
        query.setIds(linkIds);
        Set<Integer> linkIdList = linkService.queryLinkIds(query);
        if (CollUtil.isEmpty(linkIdList)) {
            if (notBlank(advUserIds) || notBlank(linkIds)) {
                return "0";// 设置一个不存在的链接id
            }
            return null;
        }
        return StrUtil.join(linkIdList);
    }
}
