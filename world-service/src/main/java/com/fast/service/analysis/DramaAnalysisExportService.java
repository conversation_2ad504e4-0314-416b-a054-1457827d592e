/*
 * Powered By fast.up
 */
package com.fast.service.analysis;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.ContentTypeEnum;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.po.member.FastMemberCoinMoneyMonthPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.member.FastMemberOrderRechargeService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 短剧分析
 *
 * <AUTHOR>
 */
@Service
public class DramaAnalysisExportService extends BaseService {

    @Autowired
    private DramaAnalysisService dataService;
    @Autowired
    private FastMemberOrderRechargeService dataRechargeService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 短剧单日明细表-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportDramaDayDetailList(SessionVO sessionVO, DramaAnalysisPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_DRAMA_DAY_DETAIL + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        List<DramaAnalysisPO> list = dataService.getDramaDayDetailList(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (DramaAnalysisPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getDataDay());
            CollUtil.addNoRepeat(rowHeadNames, "日期");

            row.add(cur.getWatchMemberCount());
            CollUtil.addNoRepeat(rowHeadNames, "观看人数");

            row.add(cur.getCoinConsume());
            CollUtil.addNoRepeat(rowHeadNames, "K币消耗数");

            row.add(cur.getCoinGiveConsume());
            CollUtil.addNoRepeat(rowHeadNames, "K币消耗数-赠送");

            row.add(cur.getWatchFinishMemberCount());
            CollUtil.addNoRepeat(rowHeadNames, "完播人数");

            row.add(cur.getWatchFinishRate() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "完播率");

            row.add(cur.getAdMoneyConsume());
            CollUtil.addNoRepeat(rowHeadNames, "广告消耗");

            row.add(cur.getAdMoneyConsumePay());
            CollUtil.addNoRepeat(rowHeadNames, "广告消耗-付费");

            row.add(cur.getAdMoneyConsumeFree());
            CollUtil.addNoRepeat(rowHeadNames, "广告消耗-免费");

            row.add(cur.getRoiAdd() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "新增ROI");

            row.add(cur.getRoiAddPay() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "新增ROI-付费");

            row.add(cur.getRoiAddFree() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "新增ROI-免费");

            row.add(cur.getRechargeMoneySumPay());
            CollUtil.addNoRepeat(rowHeadNames, "付费投放充值");

            row.add(cur.getRechargeMoneyProfitSumPay());
            CollUtil.addNoRepeat(rowHeadNames, "付费投放实到");

            row.add(cur.getRechargeMoneySumAddPay());
            CollUtil.addNoRepeat(rowHeadNames, "新增付费投放充值");

            row.add(cur.getRechargeMoneyProfitSumAddPay());
            CollUtil.addNoRepeat(rowHeadNames, "新增付费投放实到");

            row.add(cur.getRechargeMoneySumFree());
            CollUtil.addNoRepeat(rowHeadNames, "免费投放充值");

            row.add(cur.getRechargeMoneyProfitSumFree());
            CollUtil.addNoRepeat(rowHeadNames, "免费投放实到");

            row.add(cur.getRechargeMoneySumAddFree());
            CollUtil.addNoRepeat(rowHeadNames, "新增免费投放充值");

            row.add(cur.getRechargeMoneyProfitSumAddFree());
            CollUtil.addNoRepeat(rowHeadNames, "新增免费投放实到");

            row.add(cur.getAdIncomeMoneySum());
            CollUtil.addNoRepeat(rowHeadNames, "免费广告总收入");

            row.add(cur.getAdIncomeMoneySumAdd());
            CollUtil.addNoRepeat(rowHeadNames, "新增免费广告收入");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "短剧单日明细表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 短剧数据明细表-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportDramaDetailList(SessionVO sessionVO, DramaAnalysisPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_DRAMA_DETAIL + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        List<DramaAnalysisPO> list = dataService.getDramaDetailList(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (DramaAnalysisPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getDramaName());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "名称");

            row.add(cur.getWatchMemberCount());
            CollUtil.addNoRepeat(rowHeadNames, "观看人数");

            row.add(cur.getCoinConsume());
            CollUtil.addNoRepeat(rowHeadNames, "K币消耗数");

            row.add(cur.getCoinGiveConsume());
            CollUtil.addNoRepeat(rowHeadNames, "K币消耗数-赠送");

            row.add(cur.getWatchFinishMemberCount());
            CollUtil.addNoRepeat(rowHeadNames, "完播人数");

            row.add(cur.getWatchFinishRate() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "完播率");

            row.add(cur.getAdMoneyConsume());
            CollUtil.addNoRepeat(rowHeadNames, "广告消耗");

            row.add(cur.getAdMoneyConsumePay());
            CollUtil.addNoRepeat(rowHeadNames, "广告消耗-付费");

            row.add(cur.getAdMoneyConsumeFree());
            CollUtil.addNoRepeat(rowHeadNames, "广告消耗-免费");

            row.add(cur.getRoiAdd() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "新增ROI");

            row.add(cur.getRoiAddPay() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "新增ROI-付费");

            row.add(cur.getRoiAddFree() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "新增ROI-免费");

            row.add(cur.getRechargeMoneySumPay());
            CollUtil.addNoRepeat(rowHeadNames, "付费投放充值");

            row.add(cur.getRechargeMoneyProfitSumPay());
            CollUtil.addNoRepeat(rowHeadNames, "付费投放实到");

            row.add(cur.getRechargeMoneySumAddPay());
            CollUtil.addNoRepeat(rowHeadNames, "新增付费投放充值");

            row.add(cur.getRechargeMoneyProfitSumAddPay());
            CollUtil.addNoRepeat(rowHeadNames, "新增付费投放实到");

            row.add(cur.getRechargeMoneySumFree());
            CollUtil.addNoRepeat(rowHeadNames, "免费投放充值");

            row.add(cur.getRechargeMoneyProfitSumFree());
            CollUtil.addNoRepeat(rowHeadNames, "免费投放实到");

            row.add(cur.getRechargeMoneySumAddFree());
            CollUtil.addNoRepeat(rowHeadNames, "新增免费投放充值");

            row.add(cur.getRechargeMoneyProfitSumAddFree());
            CollUtil.addNoRepeat(rowHeadNames, "新增免费投放实到");

            row.add(cur.getAdIncomeMoneySum());
            CollUtil.addNoRepeat(rowHeadNames, "免费广告总收入");

            row.add(cur.getAdIncomeMoneySumAdd());
            CollUtil.addNoRepeat(rowHeadNames, "新增免费广告收入");

            row.add(DateUtil.format09(cur.getReleaseDate()));
            CollUtil.addNoRepeat(rowHeadNames, "上线日期");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "短剧数据明细表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 财务核算列表-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportMemberRechargeCoinList(SessionVO sessionVO, FastMemberCoinMoneyMonthPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_RECHARGE_ORDER_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        List<FastMemberCoinMoneyMonthPO> list = dataRechargeService.getMemberRechargeCoinList2(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberCoinMoneyMonthPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getMonthStr());
            CollUtil.addNoRepeat(rowHeadNames, "月份");

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "应用名称");

            row.add(DoubleUtil.addB(cur.getMoneyRecharge(), cur.getMoneyVipRecharge()));
            CollUtil.addNoRepeat(rowHeadNames, "用户充值总金额");

            row.add(cur.getMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "用户充值K币金额");

            row.add(cur.getCoinRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "用户充值K币");

            row.add(cur.getCoinGive());
            CollUtil.addNoRepeat(rowHeadNames, "用户赠送K币");

            row.add(cur.getCoinRechargeConsume());
            CollUtil.addNoRepeat(rowHeadNames, "用户消耗充值K币");

            row.add(cur.getCoinGiveConsume());
            CollUtil.addNoRepeat(rowHeadNames, "用户消耗赠送K币");

            row.add(cur.getCoinRechargeRemain());
            CollUtil.addNoRepeat(rowHeadNames, "剩余用户消耗充值K币");

            row.add(cur.getCoinGiveRemain());
            CollUtil.addNoRepeat(rowHeadNames, "剩余用户消耗赠送K币");

            row.add(cur.getMoneyVipRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "用户充值VIP金额");

            row.add(cur.getMoneyVipConsume());
            CollUtil.addNoRepeat(rowHeadNames, "用户消耗VIP金额");

            row.add(cur.getMoneyVipRemain());
            CollUtil.addNoRepeat(rowHeadNames, "用户剩余VIP金额");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "财务核算列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }
}
