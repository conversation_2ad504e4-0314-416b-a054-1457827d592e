/*
 * Powered By fast.up
 */
package com.fast.service.analysis;

import com.fast.constant.BigDecimalVar;
import com.fast.mapper.analysis.RetailIndexMapper;
import com.fast.po.analysis.RetailIndexPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DoubleUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 分销商首页
 *
 * <AUTHOR>
 */
@Service
public class RetailIndexService extends BaseService {

    @Autowired
    private RetailIndexMapper launchAnalysisMapper;

    public ResultVO<?> getRechargeConsume(RetailIndexPO params) {
        // 采用MD5模式,防止redis的key过长
        String s = Md5Util.getMD5(params.getContentType() + "-" + params.getRetailId() + "-" + params.getOfficialIds() + "-" + params.getAdvMediaId() + "-" + params.getCreateTimeS() + "-" + params.getCreateTimeE()).substring(10);
        String key = "retail_recharge_consume" + s;
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            return JsonUtil.toJavaObject(value, ResultVO.class);
        }
        RetailIndexPO po = new RetailIndexPO();
        BigDecimal recharge = launchAnalysisMapper.getRecharge(params);
        BigDecimal rechargeP = launchAnalysisMapper.getRechargeProfit(params);
        BigDecimal consume = launchAnalysisMapper.getConsume(params);

        po.setRechargeMoneySum(defaultIfNull(recharge, BigDecimal.ZERO));
        po.setRechargeMoneyProfitSum(defaultIfNull(rechargeP, BigDecimal.ZERO));
        po.setAdMoneyConsume(defaultIfNull(consume, BigDecimal.ZERO));
        po.setRoi(DoubleUtil.divB4Zero(po.getRechargeMoneyProfitSum().multiply(BigDecimalVar.BD_100), po.getAdMoneyConsume()));

        ResultVO<RetailIndexPO> vo = ResultVO.success(po);
        RedisUtil.setObject(key, vo, 15);
        return vo;
    }

    public ResultVO<?> getCoreAnalysis(RetailIndexPO params) {
        // 采用MD5模式,防止redis的key过长
        String s = Md5Util.getMD5(params.getContentType() + params.getRetailId() + "-" + params.getOfficialIds() + "-" + params.getCreateTimeS() + "-" + params.getCreateTimeE()).substring(10);
        String key = "retail_core_data" + s;
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            return JsonUtil.toJavaObject(value, ResultVO.class);
        }
        RetailIndexPO recharge = launchAnalysisMapper.getCoreAddData(params);
        if (recharge == null) {
            recharge = new RetailIndexPO();
            recharge.setNumDay(0);
            recharge.setNumDayRech(0);
            recharge.setNumDayOrder(0);
            recharge.setMoneyDay(BigDecimal.ZERO);
            recharge.setMoneyProfitDay(BigDecimal.ZERO);
        }

        // 新用户笔单价 新用户充值金额➗新用户充值笔数
        recharge.setMemberAddPerMoney(DoubleUtil.divB4Zero(recharge.getMoneyProfitDay(), recharge.getNumDayOrder()));

        // 新用户付费率 新用户充值人数➗新增用户数
        recharge.setMemberAddPayRate(DoubleUtil.divB4Zero(recharge.getNumDayRech() * 100, recharge.getNumDay()));

        // 新用户ARPU 新用户充值金额➗新增用户数
        recharge.setMemberAddARPU(DoubleUtil.divB4Zero(recharge.getMoneyProfitDay(), recharge.getNumDay()));

        // 总充值金额和笔数
        RetailIndexPO orderMoney = launchAnalysisMapper.getRechargeOrderMoney(params);
        if (orderMoney == null) {
            orderMoney = new RetailIndexPO();
            orderMoney.setRechargeMoneySum(BigDecimal.ZERO);
            orderMoney.setRechargeMoneyProfitSum(BigDecimal.ZERO);
            orderMoney.setRechargeOrderSum(0);
        } else {
            recharge.setRechargeMoneySum(orderMoney.getRechargeMoneySum());
            recharge.setRechargeMoneyProfitSum(orderMoney.getRechargeMoneyProfitSum());
            recharge.setRechargeOrderSum(orderMoney.getRechargeOrderSum());
        }

        // K币消费数据
        RetailIndexPO coinConsume = launchAnalysisMapper.getCoinConsume(params);
        if (coinConsume == null) {
            coinConsume = new RetailIndexPO();
            coinConsume.setCoinGiveConsume(0);
            coinConsume.setCoinRechargeConsume(0);
        } else {
            recharge.setCoinGiveConsume(coinConsume.getCoinGiveConsume());
            recharge.setCoinRechargeConsume(coinConsume.getCoinRechargeConsume());
        }

        ResultVO<RetailIndexPO> vo = ResultVO.success(recharge);
        RedisUtil.setObject(key, vo, 15);
        return vo;
    }
}
