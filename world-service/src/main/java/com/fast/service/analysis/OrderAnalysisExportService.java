/*
 * Powered By fast.up
 */
package com.fast.service.analysis;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.analysis.OrderAnalysisPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单分析
 *
 * <AUTHOR>
 */
@Service
public class OrderAnalysisExportService extends BaseService {

    @Autowired
    private OrderAnalysisService dataService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 订单统计-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportStatisticsByDateList(SessionVO sessionVO, OrderAnalysisPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_ORDER_RETAIL_DETAIL + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        OrderAnalysisPO summary = new OrderAnalysisPO();
        List<OrderAnalysisPO> list = dataService.getStatisticsByDateList(params, summary);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        list.add(0, summary);
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        int i = 0;
        for (OrderAnalysisPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            if (i == 0) {
                row.add("累计");
            } else {
                row.add(cur.getDataDay());
            }
            switch (params.getDataType()) {
                case 1:
                case 2:
                    CollUtil.addNoRepeat(rowHeadNames, "日期");
                    break;
                case 3:
                    CollUtil.addNoRepeat(rowHeadNames, "分销商");
                    break;
                case 4:
                    CollUtil.addNoRepeat(rowHeadNames, "优化师");
                    break;
                case 5:
                    CollUtil.addNoRepeat(rowHeadNames, "短剧");
                    break;
                default:
                    CollUtil.addNoRepeat(rowHeadNames, "");
            }

            row.add(cur.getRechargeMoneySum());
            CollUtil.addNoRepeat(rowHeadNames, "充值总金额");

            row.add(cur.getRechargeMemberCount());
            CollUtil.addNoRepeat(rowHeadNames, "充值总人数");

            row.add(cur.getRechargeOrderCount());
            CollUtil.addNoRepeat(rowHeadNames, "订单总数");

            row.add(cur.getRechargeMoneyAddSum());
            CollUtil.addNoRepeat(rowHeadNames, "新增用户充值金额");

            row.add(cur.getRechargeMemberCountAdd());
            CollUtil.addNoRepeat(rowHeadNames, "新增用户充值人数");

            row.add(cur.getRechargeMoneyNewSum());
            CollUtil.addNoRepeat(rowHeadNames, "注册用户充值金额");

            row.add(cur.getRechargeMemberCountNew());
            CollUtil.addNoRepeat(rowHeadNames, "注册用户充值人数");

            row.add(cur.getRechargeMoneyColorSum());
            CollUtil.addNoRepeat(rowHeadNames, "染色用户充值金额");

            row.add(cur.getRechargeMemberCountColor());
            CollUtil.addNoRepeat(rowHeadNames, "染色用户充值人数");

            row.add(cur.getRechargeCoinMoneySum());
            CollUtil.addNoRepeat(rowHeadNames, "K币充值金额");

            row.add(cur.getRechargeCoinMoneyProfitSum());
            CollUtil.addNoRepeat(rowHeadNames, "K币实到金额");

            row.add(cur.getRechargeCoinMoneyPer());
            CollUtil.addNoRepeat(rowHeadNames, "K币充值人均金额");

            row.add(cur.getRechargeCoinMemberCount());
            CollUtil.addNoRepeat(rowHeadNames, "K币充值人次");

            row.add(cur.getRechargeVipMoneySum());
            CollUtil.addNoRepeat(rowHeadNames, "VIP充值金额");

            row.add(cur.getRechargeVipMoneyProfitSum());
            CollUtil.addNoRepeat(rowHeadNames, "VIP实到金额");

            row.add(cur.getRechargeVipMoneyPer());
            CollUtil.addNoRepeat(rowHeadNames, "VIP充值人均金额");

            row.add(cur.getRechargeVipMemberCount());
            CollUtil.addNoRepeat(rowHeadNames, "VIP充值人次");

            row.add(cur.getBackRate() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "回传率");

            dataList.add(row);
            i++;
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "订单统计";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }
}
