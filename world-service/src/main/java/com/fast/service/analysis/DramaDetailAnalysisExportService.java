/*
 * Powered By fast.up
 */
package com.fast.service.analysis;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 短剧整体分析
 *
 * <AUTHOR>
 */
@Service
public class DramaDetailAnalysisExportService extends BaseService {

    @Autowired
    private DramaDetailAnalysisService dataService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 剧集数据明细-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportDramaDetailSeriesAnalysisList(SessionVO sessionVO, DramaAnalysisPO params) {
        Integer cpUserType = sessionVO.getCpUserType();
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_DRAMA_DETAIL_SERIES + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        List<DramaAnalysisPO> list = dataService.getDramaDetailSeriesAnalysisList(sessionVO, params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (DramaAnalysisPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add("第" + cur.getSeriesNum() + "集");
            CollUtil.addNoRepeat(rowHeadNames, "剧集名称");

            if (cpUserType != 2) {
                row.add(cur.getWhetherPay().equals(1) ? "付费" : "免费");
                CollUtil.addNoRepeat(rowHeadNames, "是否付费");
            }


            row.add(cur.getWatchMemberCount());
            CollUtil.addNoRepeat(rowHeadNames, "观看人数");

            row.add(cur.getRemainRate() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "留存率");

            row.add(cur.getSkipRate() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "单集跳出率");

            if (cpUserType != 2) {
                row.add(cur.getUnlockCount());
                CollUtil.addNoRepeat(rowHeadNames, "订阅人数");
            }

            if (cpUserType != 2) {
                row.add(cur.getCoinConsume());
                CollUtil.addNoRepeat(rowHeadNames, "单集K币消费总数");
            }

            if (cpUserType != 2) {
                row.add(cur.getCoinGiveConsume());
                CollUtil.addNoRepeat(rowHeadNames, "单集K币消耗数-赠送");
            }
            Integer platformFlag = defaultIfNull(params.getPlatformFlag(), 0);
            if (cpUserType == 2 && platformFlag == 0) {
                row.add(cur.getCoinConsume());
                CollUtil.addNoRepeat(rowHeadNames, "单集消耗K币(仅充值币)");
            }

            row.add(cur.getWatchFinishMemberCount());
            CollUtil.addNoRepeat(rowHeadNames, "单集完播人数");

            row.add(cur.getWatchFinishRate() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "完播率");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "剧集数据明细";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }
}
