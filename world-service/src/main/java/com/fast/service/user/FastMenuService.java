/*
 * Powered By fast.up
 */
package com.fast.service.user;

import com.fast.constant.StaticStr;
import com.fast.mapper.user.FastMenuMapper;
import com.fast.po.user.FastMenuPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMenuService extends BaseService {

    @Autowired
    private FastMenuMapper fastMenuMapper;

    public List<FastMenuPO> getMenuListByUser(FastMenuPO item) {
        return fastMenuMapper.getMenuListByUser(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMenuPO queryById(FastMenuPO item) {
        return fastMenuMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMenuPO queryById(Integer id) {
        FastMenuPO itemParam = new FastMenuPO();
        itemParam.setId(id);
        return fastMenuMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMenuPO queryOne(FastMenuPO item) {
        return fastMenuMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMenuPO> queryList(FastMenuPO item) {
        return fastMenuMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO queryPageList(FastMenuPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMenuPO> list = fastMenuMapper.queryList(item);
        for (FastMenuPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        pageVO.setTotal(new PageInfo<>(list).getTotal());
        Map<String, Object> results = ResultVO.getMap();
        results.put("list", list);
        results.put("page", pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMenuPO item) {
        return fastMenuMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMenuPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastMenuMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMenuPO> list) {
        if (fastMenuMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMenuPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastMenuMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
