/*
 * Powered By fast.up
 */
package com.fast.service.user;

import com.fast.mapper.user.FastUserCardMapper;
import com.fast.po.user.FastUserCardPO;
import com.fast.service.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastUserCardService extends BaseService {

    @Autowired
    private FastUserCardMapper fastUserCardMapper;

    @Qualifier("fastUserMapper")

    public List<FastUserCardPO> queryUserId(Integer userid) {
        FastUserCardPO cardPO = fastUserCardMapper.queryByUserId(userid);
        if (cardPO == null) {
            return new ArrayList<>();
        }
        return List.of(cardPO);
    }
}
