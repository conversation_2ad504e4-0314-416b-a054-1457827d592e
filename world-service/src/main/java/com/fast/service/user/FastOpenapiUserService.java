/*
 * Powered By fast.up
 */
package com.fast.service.user;

import com.fast.constant.StaticStr;
import com.fast.mapper.user.FastOpenapiUserMapper;
import com.fast.po.user.FastOpenapiUserPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastOpenapiUserService extends BaseService {

    @Autowired
    private FastOpenapiUserMapper fastOpenapiUserMapper;

    /**
     * 通过id查询单个对象
     */
    public FastOpenapiUserPO queryById(FastOpenapiUserPO item) {
        return fastOpenapiUserMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastOpenapiUserPO queryById(Integer id) {
        return fastOpenapiUserMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastOpenapiUserPO queryOne(FastOpenapiUserPO item) {
        return fastOpenapiUserMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastOpenapiUserPO> queryList(FastOpenapiUserPO item) {
        return fastOpenapiUserMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastOpenapiUserPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastOpenapiUserPO> list = fastOpenapiUserMapper.queryList(item);
        for (FastOpenapiUserPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastOpenapiUserPO item) {
        return fastOpenapiUserMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastOpenapiUserPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastOpenapiUserMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastOpenapiUserPO> list) {
        if (fastOpenapiUserMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastOpenapiUserPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastOpenapiUserMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
