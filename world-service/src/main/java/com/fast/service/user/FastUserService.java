/*
 * Powered By fast.up
 */
package com.fast.service.user;

import com.fast.constant.RedisVar;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.user.FastRoleMapper;
import com.fast.mapper.user.FastUserMapper;
import com.fast.po.user.FastRolePO;
import com.fast.po.user.FastUserPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.user.FastUserVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastUserService extends BaseService {

    @Autowired
    private FastUserMapper fastUserMapper;
    @Autowired
    private FastRoleMapper fastRoleMapper;
    @Autowired
    private FastMiniMapper fastMiniMapper;

    /**
     * 通过id查询单个对象
     */
    public FastUserPO queryById(FastUserPO item) {
        return fastUserMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public MethodVO deleteById(FastUserPO item) {
        FastUserPO userPO = fastUserMapper.queryById(item);
        String[] roleIdArray = userPO.getRoleIds().split(",");
        for (String roleId : roleIdArray) {
            if ("1".equals(roleId)) {
                return MethodVO.error("不能删除管理员");
            }
            if ("2".equals(roleId)) {
                return MethodVO.error("不能删除管理员");
            }
        }
        item.setLoginName(userPO.getLoginName() + "#" + StrUtil.getRandomInt(3));
        if (fastUserMapper.deleteById(item) > 0) {
            return MethodVO.success();
        }
        return MethodVO.error("删除失败");
    }

    /**
     * 通过id查询单个对象
     */
    public FastUserPO queryById(Integer id) {
        FastUserPO itemParam = new FastUserPO();
        itemParam.setId(id);
        return fastUserMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastUserPO queryOne(FastUserPO item) {
        return fastUserMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastUserPO> queryList(FastUserPO item) {
        return fastUserMapper.queryList(item);
    }

    /**
     * 查询全部
     */
    public List<FastUserPO> querySimpleList(FastUserPO item) {
        return fastUserMapper.querySimpleList(item);
    }

    /**
     * 查询用户分销商
     */
    public List<Integer> queryRetailIds(FastUserPO item) {
        return fastUserMapper.queryRetailIds(item);
    }

    /**
     * 查询用户姓名
     */
    public List<FastUserPO> queryUserNames(FastUserPO item) {
        return fastUserMapper.queryUserNames(item);
    }

    /**
     * 查询用户姓名Map
     */
    public Map<Integer, String> queryUserNameMap(FastUserPO item) {
        List<FastUserPO> list = fastUserMapper.queryUserNames(item);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, String> map = new HashMap<>();
        list.forEach(cur -> map.put(cur.getId(), cur.getUserName()));
        return map;
    }

    /**
     * 查询用户Map
     */
    public Map<Integer, FastUserPO> queryUserMap(FastUserPO item) {
        List<FastUserPO> list = fastUserMapper.queryUserNames(item);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, FastUserPO> map = new HashMap<>();
        list.forEach(cur -> map.put(cur.getId(), cur));
        return map;
    }

    /**
     * 查询优化师列表-回传配置中
     */
    public List<FastUserVO> queryListUserV1(FastUserVO item) {
        return fastUserMapper.queryListUserV1(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastUserPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastUserPO> list = fastUserMapper.queryList(item);
        for (FastUserPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            // 设置角色名称
            cur.setRoleNames(getRoleNamesByIds(cur.getRetailId(), cur.getRoleIds()));
            if (StrUtil.isEmpty(cur.getOfficialIds())) {
                cur.setRelateOfficial(0);
            } else {
                cur.setRelateOfficial(1);
            }
            if (StrUtil.isEmpty(cur.getTiktokOfficialIds())) {
                cur.setRelateMini(0);
            } else {
                cur.setRelateMini(1);
            }
            // 快手绑定关系
            if (cur.getRelateMini() == 0 && StrUtil.isEmpty(cur.getKuaishouOfficialIds())) {
                cur.setRelateMini(0);
            } else {
                cur.setRelateMini(1);
            }
            // 快应用绑定关系
            if (cur.getRelateMini() == 0 && StrUtil.isEmpty(cur.getQuickOfficialIds())) {
                cur.setRelateMini(0);
            } else {
                cur.setRelateMini(1);
            }
            // APP应用绑定关系
            if (cur.getRelateMini() == 0 && StrUtil.isEmpty(cur.getAppOfficialIds())) {
                cur.setRelateMini(0);
            } else {
                cur.setRelateMini(1);
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    public ResultVO<?> getAuditUserList(FastUserPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastUserPO> list = fastUserMapper.queryList(item);
        for (FastUserPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            // 设置角色名称
            cur.setRoleNames(getRoleNamesByIds(cur.getRetailId(), cur.getRoleIds()));
            if (StrUtil.isEmpty(cur.getOfficialIds())) {
                cur.setRelateOfficial(0);
            } else {
                cur.setRelateOfficial(1);
            }
            if (StrUtil.isEmpty(cur.getTiktokOfficialIds())) {
                cur.setRelateMini(0);
            } else {
                cur.setRelateMini(1);
            }
        }
        list.remove(list.get(list.size() - 1));
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询全部优化师(分页)
     */
    public ResultVO<?> queryPageAdvUserList(FastUserPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastUserPO> list = fastUserMapper.queryAdvUserList(item);
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 根据角色id查询角色名名称
     *
     * @param retailId
     * @param ids
     * @return
     */
    public String getRoleNamesByIds(Integer retailId, String ids) {
        String key = StaticVar.ROLE_LIST + retailId;
        List<FastRolePO> roleList = RedisUtil.getList(key, FastRolePO.class);
        if (roleList == null) {
            FastRolePO roleParam = new FastRolePO();
            roleParam.setRetailId(retailId);
            roleList = fastRoleMapper.queryList(roleParam);
            if (retailId > 0) {
                roleParam.setRetailId(0);
                List<FastRolePO> roleAdminList = fastRoleMapper.queryList(roleParam);
                roleList.addAll(roleAdminList);
            }
            RedisUtil.setList(key, roleList, RedisUtil.ONE_MINUTE);
        }
        if (roleList == null || roleList.size() == 0) {
            return "";
        }
        List<Integer> roleIdList = CollUtil.parseIntStr2List(ids);
        // 处理角色
        if (roleIdList != null && roleIdList.size() > 0) {
            StringBuilder roleNameBuffer = new StringBuilder();
            for (FastRolePO role : roleList) {
                if (roleIdList.contains(role.getId())) {
                    if (roleNameBuffer.length() > 0) {
                        roleNameBuffer.append(",");
                    }
                    roleNameBuffer.append(role.getRoleName());
                }
            }
            return roleNameBuffer.toString();
        }
        return "";
    }

    /**
     * 查询总数
     */
    public int queryCount(FastUserPO item) {
        return fastUserMapper.queryCount(item);
    }

    /**
     * 查询名称是否重复
     */
    public int queryNameIsRepeat(FastUserPO item) {
        return fastUserMapper.queryNameIsRepeat(item);
    }

    /**
     * 添加分销商管理员
     *
     * @param item
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertRetailAdmin(FastUserPO item) {
        item.setAddByAdmin(1);
        return insert(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastUserPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        // 判断是否存在相同账号
        FastUserPO fastUser = new FastUserPO();
        fastUser.setLoginName(item.getLoginName());
        if (fastUserMapper.queryCount(fastUser) > 0) {
            return MethodVO.error("已经存在相同账号");
        }
        // 判断管理员账号
        if (isAdmin(item.getRoleIds())) {
            if (item.getAddByAdmin() == null || item.getAddByAdmin() != 1) {
                return MethodVO.error("管理员账号不能新增");
            }
        }
        if (fastUserMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    // 判断是否管理员
    private boolean isAdmin(String roleIds) {
        if (StrUtil.isEmpty(roleIds)) {
            return false;
        }
        String[] roleIdArray = roleIds.split(StaticVar.COMMA);
        for (String roleId : roleIdArray) {
            if ("1".equals(roleId)) {
                return true;
            }
            if ("2".equals(roleId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastUserPO> list) {
        if (fastUserMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastUserPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (isAdmin(item.getRoleIds())) {
            return MethodVO.error("不能修改为管理员");
        }
        if (fastUserMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO resetPasswords(List<Integer> idList, Integer updatorId) {
        FastUserPO item = new FastUserPO();
        for (Integer id : idList) {
            item.setId(id);
            item.setPassword(Md5Util.getMD5BySalt(StaticVar.DEFAULT_PASSWORD));
            item.setUpdatorId(updatorId);
            if (fastUserMapper.updateById(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        return MethodVO.success();
    }

    /**
     * 员工账号更新-强制重新登录
     *
     * @param timeStampStr
     * @param sysUserId
     */
    public void staffAccountChanged(long timeStampStr, int sysUserId) {
        String key = StaticVar.STAFF_ACCOUNT_CHANGED + sysUserId;
        RedisUtil.set(RedisVar.REDIS_TOKEN_DB, key, String.valueOf(timeStampStr), StaticVar.ACCESS_TOKEN_EXP + 3600);
    }

    /**
     * 分销商账号更新-强制重新登录
     *
     * @param timeStampStr
     * @param retailId
     */
    public void retailAccountChanged(long timeStampStr, int retailId) {
        String key = StaticVar.RETAIL_ACCOUNT_CHANGED + retailId;
        RedisUtil.set(RedisVar.REDIS_TOKEN_DB, key, String.valueOf(timeStampStr), StaticVar.ACCESS_TOKEN_EXP + 3600);
    }

    public int queryUserCount(Integer contentType, Integer retailId) {
        return fastUserMapper.queryUserCount(contentType, retailId);
    }

}
