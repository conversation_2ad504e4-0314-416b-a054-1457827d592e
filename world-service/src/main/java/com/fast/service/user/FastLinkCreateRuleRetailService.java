/*
 * Powered By fast.up
 */
package com.fast.service.user;

import com.fast.constant.StaticStr;
import com.fast.framework.exception.MyException;
import com.fast.mapper.user.FastLinkCreateRuleRetailMapper;
import com.fast.po.user.FastLinkCreateRuleRetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastLinkCreateRuleRetailService extends BaseService {

    @Autowired
    private FastLinkCreateRuleRetailMapper fastLinkCreateRuleRetailMapper;

    /**
     * 通过id查询单个对象
     */
    public FastLinkCreateRuleRetailPO queryById(FastLinkCreateRuleRetailPO params) {
        return fastLinkCreateRuleRetailMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLinkCreateRuleRetailPO queryById(Integer id) {
        return fastLinkCreateRuleRetailMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastLinkCreateRuleRetailPO queryOne(FastLinkCreateRuleRetailPO params) {
        return fastLinkCreateRuleRetailMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastLinkCreateRuleRetailPO> queryList(FastLinkCreateRuleRetailPO params) {
        return fastLinkCreateRuleRetailMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastLinkCreateRuleRetailPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastLinkCreateRuleRetailPO> list = fastLinkCreateRuleRetailMapper.queryList(params);
        for (FastLinkCreateRuleRetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastLinkCreateRuleRetailPO params) {
        return fastLinkCreateRuleRetailMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastLinkCreateRuleRetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);

        String[] retails = params.getRetailIds().split(",");

        // 删除旧数据
        fastLinkCreateRuleRetailMapper.removeByRetailIds(params.getRetailIds(), params.getRuleId());

        List<FastLinkCreateRuleRetailPO> saveList = new ArrayList<>();
        for (String retailId : retails) {
            FastLinkCreateRuleRetailPO fastLinkCreateRuleRetailPO = new FastLinkCreateRuleRetailPO();
            fastLinkCreateRuleRetailPO.setCreatorId(params.getCreatorId());
            fastLinkCreateRuleRetailPO.setRetailId(Integer.valueOf(retailId));
            fastLinkCreateRuleRetailPO.setRuleId(params.getRuleId());
            fastLinkCreateRuleRetailPO.setCreateTime(nowTime);
            fastLinkCreateRuleRetailPO.setUpdateTime(nowTime);
            fastLinkCreateRuleRetailPO.setUpdatorId(params.getCreatorId());
            saveList.add(fastLinkCreateRuleRetailPO);
        }
        fastLinkCreateRuleRetailMapper.insertBatch(saveList);
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastLinkCreateRuleRetailPO> list) {
        if (fastLinkCreateRuleRetailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastLinkCreateRuleRetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastLinkCreateRuleRetailMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public void deleteById(Integer id) {
        int count = fastLinkCreateRuleRetailMapper.deleteById(id);
        if (count == 0) {
            throw new MyException("移除失败!");
        }
    }
}
