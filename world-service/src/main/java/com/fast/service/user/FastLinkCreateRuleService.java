/*
 * Powered By fast.up
 */
package com.fast.service.user;

import com.fast.constant.StaticStr;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.mapper.user.FastLinkCreateRuleMapper;
import com.fast.mapper.user.FastLinkCreateRuleRetailMapper;
import com.fast.mapper.user.FastUserRecentLogMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.user.FastLinkCreateRulePO;
import com.fast.po.user.FastLinkCreateRuleRetailPO;
import com.fast.po.user.FastUserRecentLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastLinkCreateRuleService extends BaseService {

    @Autowired
    private FastLinkCreateRuleMapper fastLinkCreateRuleMapper;

    @Autowired
    private FastLinkCreateRuleRetailMapper fastLinkCreateRuleRetailMapper;

    @Autowired
    private FastUserRecentLogMapper fastUserRecentLogMapper;

    @Autowired
    private FastDramaMapper fastDramaMapper;

    /**
     * 通过id查询单个对象
     */
    public FastLinkCreateRulePO queryById(FastLinkCreateRulePO params) {
        return fastLinkCreateRuleMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLinkCreateRulePO queryById(Integer id) {
        return fastLinkCreateRuleMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastLinkCreateRulePO queryOne(FastLinkCreateRulePO params) {
        return fastLinkCreateRuleMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastLinkCreateRulePO> queryList(FastLinkCreateRulePO params) {
        return fastLinkCreateRuleMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastLinkCreateRulePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastLinkCreateRulePO> list = fastLinkCreateRuleMapper.queryList(params);
        for (FastLinkCreateRulePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastLinkCreateRulePO params) {
        return fastLinkCreateRuleMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastLinkCreateRulePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastLinkCreateRuleMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastLinkCreateRulePO> list) {
        if (fastLinkCreateRuleMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastLinkCreateRulePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastLinkCreateRuleMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public FastLinkCreateRulePO getFastLinkCreateRule() {
        FastLinkCreateRulePO fastLinkCreateRulePO = fastLinkCreateRuleMapper.queryOne(new FastLinkCreateRulePO());
        if (Objects.isNull(fastLinkCreateRulePO)) {
            // 初始化新增一条
            fastLinkCreateRulePO = new FastLinkCreateRulePO();
            fastLinkCreateRulePO.setCreatorId(0);
            fastLinkCreateRulePO.setUpdatorId(0);
            fastLinkCreateRulePO.setCreateTime(new Date());
            fastLinkCreateRulePO.setUpdateTime(new Date());
            fastLinkCreateRulePO.setSeriesPlayTimeRatio(BigDecimal.ZERO);
            fastLinkCreateRulePO.setSeriesRatio(BigDecimal.ZERO);
            fastLinkCreateRulePO.setRuleName("默认规则");
            fastLinkCreateRuleMapper.insertSelective(fastLinkCreateRulePO);
        }

        fastLinkCreateRulePO.setEncryptionId(encode(fastLinkCreateRulePO.getId()));
        return fastLinkCreateRulePO;
    }

    public List<FastLinkCreateRulePO> verifyUser(FastLinkCreateRulePO params) {
        List<FastLinkCreateRulePO> resultRuleList = new ArrayList<>();

        // 获取分销商是否绑定规则
        FastLinkCreateRuleRetailPO where = new FastLinkCreateRuleRetailPO();
        where.setRetailId(params.getRetailId());
        List<FastLinkCreateRuleRetailPO> ruleList = fastLinkCreateRuleRetailMapper.queryList(where);

        if (CollUtil.isEmpty(ruleList)) {
            log.info("获取链接校验未配置规则,校验成功!");
            return resultRuleList;
        }
        // 获取规则信息
        Set<Integer> collect = ruleList.stream().map(FastLinkCreateRuleRetailPO::getRuleId).collect(Collectors.toSet());

        String ruleIds = StrUtil.join(collect);

        FastLinkCreateRulePO ruleWhere = new FastLinkCreateRulePO();
        ruleWhere.setRuleIds(ruleIds);
        List<FastLinkCreateRulePO> linkRuleList = fastLinkCreateRuleMapper.queryList(ruleWhere);

        if (CollUtil.isEmpty(linkRuleList)) {
            // 未配置规则
            return resultRuleList;
        }

        FastUserRecentLogPO fastUserRecentLogPO = new FastUserRecentLogPO();
        fastUserRecentLogPO.setUserId(params.getUserId());
        fastUserRecentLogPO.setDramaId(params.getDramaId());
        // 获取用户观看记录
        List<FastUserRecentLogPO> userRecentLogPOS = fastUserRecentLogMapper.queryList(fastUserRecentLogPO);

        // 校验用户观看记录
        for (FastLinkCreateRulePO linkCreateRulePO : linkRuleList) {
            FastLinkCreateRulePO result = new FastLinkCreateRulePO();
            FastDramaPO fastDramaPO = fastDramaMapper.queryById(params.getDramaId());
            Integer seriesNumAll = fastDramaPO.getSeriesNumAll();
            BigDecimal seriesRatio = linkCreateRulePO.getSeriesRatio();
            int needCount = BigDecimal.valueOf(seriesNumAll).multiply(seriesRatio).setScale(0, RoundingMode.UP).intValue();
            result.setSeriesCount(needCount);
            result.setSeriesPlayTimeRatio(linkCreateRulePO.getSeriesPlayTimeRatio());
            if (needCount == 0) {
                // 无需观看
                continue;
            }
            // 过滤出单集符合条件的观看记录列表
            // 单集播放比例
            BigDecimal seriesPlayTimeRatio = linkCreateRulePO.getSeriesPlayTimeRatio();

            List<FastUserRecentLogPO> filterList = userRecentLogPOS.stream().filter(item -> {
                BigDecimal playRatio = item.getPlayRatio();
                return playRatio.compareTo(seriesPlayTimeRatio) > 0;
            }).collect(Collectors.toList());

            if (CollUtil.isEmpty(filterList)) {
                resultRuleList.add(result);
                continue;
            }
            filterList.sort(Comparator.comparing(FastUserRecentLogPO::getSeriesNum));

            // 总集数
            // 占比
            // 连续 从第一集开始
            if (filterList.size() < needCount) {
                resultRuleList.add(result);
                continue;
            }
            FastUserRecentLogPO first = filterList.get(0);
            FastUserRecentLogPO last = filterList.get(needCount - 1);
            if ((first.getSeriesNum() != 1) || (last.getSeriesNum() != needCount)) {
                // 未通过
                resultRuleList.add(result);
                continue;
            }
        }

        return resultRuleList;
    }
}
