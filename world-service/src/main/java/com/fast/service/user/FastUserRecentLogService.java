/*
 * Powered By fast.up
 */
package com.fast.service.user;

import com.fast.constant.StaticStr;
import com.fast.framework.exception.MyException;
import com.fast.mapper.drama.FastDramaSeriesMapper;
import com.fast.mapper.user.FastUserRecentLogMapper;
import com.fast.po.drama.FastDramaSeriesPO;
import com.fast.po.user.FastUserRecentLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastUserRecentLogService extends BaseService {

    @Autowired
    private FastUserRecentLogMapper fastUserRecentLogMapper;

    @Autowired
    private FastDramaSeriesMapper fastDramaSeriesMapper;

    /**
     * 通过id查询单个对象
     */
    public FastUserRecentLogPO queryById(FastUserRecentLogPO params) {
        return fastUserRecentLogMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastUserRecentLogPO queryById(Integer id) {
        return fastUserRecentLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastUserRecentLogPO queryOne(FastUserRecentLogPO params) {
        return fastUserRecentLogMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastUserRecentLogPO> queryList(FastUserRecentLogPO params) {
        return fastUserRecentLogMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastUserRecentLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastUserRecentLogPO> list = fastUserRecentLogMapper.queryList(params);
        for (FastUserRecentLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastUserRecentLogPO params) {
        return fastUserRecentLogMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastUserRecentLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastUserRecentLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastUserRecentLogPO> list) {
        if (fastUserRecentLogMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastUserRecentLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastUserRecentLogMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public void insertOrUpdate(FastUserRecentLogPO params) {
        // 查询之前有没有播放记录
        FastUserRecentLogPO query = new FastUserRecentLogPO();
        query.setDramaId(params.getDramaId());
        query.setUserId(params.getUserId());
        query.setSeriesNum(params.getSeriesNum());
        FastUserRecentLogPO existsRecord = fastUserRecentLogMapper.queryOne(query);
        // 查询剧集最大时长
        FastDramaSeriesPO fastDramaSeriesPO = new FastDramaSeriesPO();
        fastDramaSeriesPO.setDelFlag(0);
        fastDramaSeriesPO.setDramaId(params.getDramaId());
        fastDramaSeriesPO.setSeriesNum(params.getSeriesNum());
        FastDramaSeriesPO seriesPO = fastDramaSeriesMapper.queryOne(fastDramaSeriesPO);
        if (Objects.isNull(seriesPO)) {
            throw new MyException("剧集不存在!");
        }
        if (Objects.nonNull(existsRecord)) {
            params.setPlaySecond(existsRecord.getPlaySecond().compareTo(params.getPlaySecond()) > 0 ? existsRecord.getPlaySecond() : params.getPlaySecond());
        }
        if (params.getPlaySecond().compareTo(BigDecimal.valueOf(seriesPO.getSeriesTime())) > 0) {
            params.setPlaySecond(BigDecimal.valueOf(seriesPO.getSeriesTime()));
        }
        params.setCreateTime(new Date());
        params.setUpdateTime(new Date());
        int updateCount = fastUserRecentLogMapper.insertOrUpdate(params);
    }
}
