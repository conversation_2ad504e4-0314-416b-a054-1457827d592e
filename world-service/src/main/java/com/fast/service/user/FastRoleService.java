/*
 * Powered By fast.up
 */
package com.fast.service.user;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.user.FastMenuMapper;
import com.fast.mapper.user.FastRoleMapper;
import com.fast.mapper.user.FastRoleMenuMapper;
import com.fast.po.user.FastMenuPO;
import com.fast.po.user.FastRoleContentPO;
import com.fast.po.user.FastRoleMenuPO;
import com.fast.po.user.FastRolePO;
import com.fast.service.base.BaseService;
import com.fast.service.system.LoginService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastRoleService extends BaseService {

    @Autowired
    private FastRoleMapper fastRoleMapper;
    @Autowired
    private FastRoleMenuMapper fastRoleMenuMapper;
    @Autowired
    private FastMenuMapper fastMenuMapper;
    @Autowired
    private LoginService loginService;
    @Autowired
    private FastRoleContentService fastRoleContentService;

    /**
     * 通过id查询单个对象
     */
    public ResultVO<?> queryById(SessionVO sessionVO, FastRolePO item) {
        FastRolePO rolePO = fastRoleMapper.queryById(item);
        // 查询全部菜单
        FastMenuPO menuParam = new FastMenuPO();
        Integer cpUserType = sessionVO.getCpUserType();
        if (cpUserType == 1) {
            menuParam.setRoleIds("6"); // 组织用户
        } else if (cpUserType == 2) {  // CP用户
            if (sessionVO.getCpType() == 1) {
                menuParam.setRoleIds("7"); // 制片
            } else if (sessionVO.getCpType() == 2) {
                menuParam.setRoleIds("8"); // 编剧
            } else if (sessionVO.getCpType() >= 3) {
                menuParam.setRoleIds("9"); // 制作团队
            }
        } else if (cpUserType == 3) {
            menuParam.setRoleIds("2"); // 分销商
        } else if (cpUserType == 4) {
            if (sessionVO.getContentType() == 3) {
                menuParam.setRoleIds("1,11"); // 平台用户
            } else {
                menuParam.setRoleIds("1,11"); // 平台用户
            }
        }
        if (item.getContentType() != null) {
            menuParam.setContentType(item.getContentType());
        }
        List<FastMenuPO> allMenuList = fastMenuMapper.getMenuListByUser(menuParam);
        allMenuList = loginService.getMenuTreeList(allMenuList, 2);
        // 查询当前角色菜单
        menuParam.setRoleIds(item.getId().toString());
        List<FastMenuPO> roleMenuList = fastMenuMapper.getMenuListByUser(menuParam);
        Map<String, Object> results = ResultVO.getMap();
        results.put("rolePO", rolePO);
        results.put("roleMenuList", roleMenuList);
        results.put("allMenuList", allMenuList);
        return ResultVO.success(results);
    }

    /**
     * 通过id查询单个对象
     */
    public FastRolePO queryById(Integer id) {
        FastRolePO itemParam = new FastRolePO();
        itemParam.setId(id);
        return fastRoleMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastRolePO queryOne(FastRolePO item) {
        return fastRoleMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastRolePO> queryList(FastRolePO item) {
        List<FastRolePO> list = fastRoleMapper.queryList(item);
        if (list != null && list.size() > 0) {
            for (FastRolePO role : list) {
                role.setEncryptionId(encode(role.getId()));
            }
        }
        return list;
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastRolePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastRolePO> list = fastRoleMapper.queryList(item);
        for (FastRolePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (notBlank(cur.getContentTypes())) {
                List<JSONObject> contentTypeStr = new ArrayList<>();
                List<Integer> contentTypes = CollUtil.parseIntStr2List(cur.getContentTypes());
                for (Integer contentType : contentTypes) {
                    switch (contentType) {
                        case 1: {
                            contentTypeStr.add(new JSONObject().fluentPut("name", "短剧").fluentPut("contentType", 1));
                        }
                        break;
                        case 4: {
                            contentTypeStr.add(new JSONObject().fluentPut("name", "漫剧").fluentPut("contentType", 4));
                        }
                        break;
                        case 2: {
                            contentTypeStr.add(new JSONObject().fluentPut("name", "漫画").fluentPut("contentType", 2));
                        }
                        break;
                        case 3: {
                            contentTypeStr.add(new JSONObject().fluentPut("name", "小说").fluentPut("contentType", 3));
                        }
                        break;
                        case 99: {
                            contentTypeStr.add(new JSONObject().fluentPut("name", "总管理").fluentPut("contentType", 99));
                        }
                        break;
                    }
                }
                cur.setContentTypeStr(contentTypeStr);
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastRolePO item) {
        return fastRoleMapper.queryCount(item);
    }

    /**
     * 查询名称是否重复
     */
    public int queryNameIsRepeat(FastRolePO item) {
        return fastRoleMapper.queryNameIsRepeat(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(SessionVO sessionVO, FastRolePO item) {
        Integer cpUserType = sessionVO.getCpUserType();
        if (cpUserType == 1) {
            item.setOrgId(sessionVO.getOrgId());
        } else if (cpUserType == 2) {
            item.setRetailId(sessionVO.getRetailId());
        }
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastRoleMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        if (cpUserType == 1) { // 组织用户
            FastRoleContentPO insert = new FastRoleContentPO();
            insert.setRoleId(item.getId());
            insert.setContentType(1);
            insert.setCreatorId(sessionVO.getUserId());
            insert.setCreateTime(DateUtil.getNowDate());
            fastRoleContentService.insert(insert);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastRolePO> list) {
        if (fastRoleMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastRolePO item) {
        FastRolePO rolePO = fastRoleMapper.queryById(item.getId());
        if (rolePO != null && StaticVar.FINDER_ROLE_NAME.equals(rolePO.getRoleName())) {
            return MethodVO.error(String.format("%s,不可修改", StaticVar.FINDER_ROLE_NAME));
        }
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastRoleMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateRoleMenu(FastRolePO item) {
        // 删除关联的菜单
        fastRoleMapper.deleteMenuRelByContentType(item.getId(), item.getContentType());
        // 新增菜单
        if (notBlank(item.getMenuIds())) {
            String[] menuIds = item.getMenuIds().split(StaticVar.COMMA);
            for (String menuIdStr : menuIds) {
                Integer menuId = Integer.valueOf(menuIdStr);
                FastRoleMenuPO rm = new FastRoleMenuPO();
                rm.setMenuId(menuId);
                rm.setRoleId(item.getId());
                rm.setContentType(item.getContentType());
                if (fastRoleMenuMapper.insert(rm) == 0) {
                    transactionRollBack();
                    throw new MyException("修改失败");
                }
            }
        }
        return MethodVO.success();
    }

    /**
     * 批量更新角色信息
     *
     * @param item
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateBatchRoleMenu(FastRolePO item) {
        List<FastRolePO> list = item.getList();
        for (FastRolePO params : list) {
            Integer id = decodeInt(params.getEncryptionId());
            if (id == null) {
                throw new MyException(StaticStr.INVALID_PARAM);
            }
            if (StrUtil.isEmpty(params.getMenuIds())) {
                // throw new MyException("菜单不能为空");
            }
            if (params.getContentType() == null) {
                throw new MyException("内容类型不能为空");
            }
            params.setId(id);
            updateRoleMenu(params);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO deleteRole(FastRolePO item) {
        FastRolePO rolePO = fastRoleMapper.queryById(item.getId());
        if (rolePO != null && StaticVar.FINDER_ROLE_NAME.equals(rolePO.getRoleName())) {
            return MethodVO.error(String.format("%S,不可删除", StaticVar.FINDER_ROLE_NAME));
        }
        // 删除关联的菜单
        fastRoleMapper.deleteById(item);
        fastRoleMapper.deleteMenuRelByContentType(item.getId(), null);

        return MethodVO.success();
    }
}
