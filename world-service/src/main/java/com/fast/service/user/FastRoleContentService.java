/*
 * Powered By fast.up
 */
package com.fast.service.user;

import com.fast.constant.StaticStr;
import com.fast.mapper.user.FastRoleContentMapper;
import com.fast.mapper.user.FastRoleMenuMapper;
import com.fast.po.user.FastRoleContentPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class FastRoleContentService extends BaseService {

    @Autowired
    private FastRoleContentMapper fastRoleContentMapper;
    @Autowired
    private FastRoleMenuMapper fastRoleMenuMapper;

    /**
     * 更新角色的设置
     *
     * @param item
     * @return
     */
    @Transactional
    public MethodVO updateRoleContent(FastRoleContentPO item) {
        // 查询旧的角色的内容权限
        FastRoleContentPO rcParam = new FastRoleContentPO();
        rcParam.setRoleId(item.getRoleId());
        Set<Integer> newList = CollUtil.parseIntStr2Set(item.getContentTypeStr());
        List<FastRoleContentPO> oldList = fastRoleContentMapper.queryList(rcParam);
        Set<Integer> oldSet = new HashSet<>();
        for (FastRoleContentPO po : oldList) {
            oldSet.add(po.getContentType());
        }
        // 待删除内容权限，同时删除关联的菜单
        Set<Integer> toDelSet = new HashSet<>();
        for (FastRoleContentPO rc : oldList) {
            if (!newList.contains(rc.getContentType())) {
                toDelSet.add(rc.getContentType());
            }
        }
        // 待新增的, newList里面存在, 但是oldSet不存在
        Set<Integer> toAddSet = new HashSet<>();
        newList.forEach(cur -> {
            if (!oldSet.contains(cur)) {
                toAddSet.add(cur);
            }
        });
        // 删除关联权限
        for (Integer conType : toDelSet) {
            // 删除关联内容权限
            if (fastRoleContentMapper.deleteByContentType(item.getRoleId(), conType) == 0) {
                transactionRollBack();
                return MethodVO.error("设置失败");
            }
            // 删除关联菜单
            fastRoleMenuMapper.deleteByContentType(item.getRoleId(), conType);
        }
        Date nowTime = DateUtil.getNowDate();
        if (CollUtil.hasContent(toAddSet)) {
            for (Integer conType : toAddSet) {
                FastRoleContentPO rcPO = new FastRoleContentPO();
                rcPO.setRoleId(item.getRoleId());
                rcPO.setContentType(conType);
                rcPO.setCreatorId(item.getCreatorId());
                rcPO.setCreateTime(nowTime);
                if (fastRoleContentMapper.insertSelective(rcPO) == 0) {
                    transactionRollBack();
                    return MethodVO.error("设置失败");
                }
            }
        }
        return MethodVO.success("设置成功");
    }

    /**
     * 通过id查询单个对象
     */
    public FastRoleContentPO queryById(FastRoleContentPO item) {
        return fastRoleContentMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastRoleContentPO queryById(Integer id) {
        return fastRoleContentMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastRoleContentPO queryOne(FastRoleContentPO item) {
        return fastRoleContentMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastRoleContentPO> queryList(FastRoleContentPO item) {
        return fastRoleContentMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastRoleContentPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastRoleContentPO> list = fastRoleContentMapper.queryList(item);
        for (FastRoleContentPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastRoleContentPO item) {
        return fastRoleContentMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastRoleContentPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastRoleContentMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastRoleContentPO> list) {
        if (fastRoleContentMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastRoleContentPO item) {
        if (fastRoleContentMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
