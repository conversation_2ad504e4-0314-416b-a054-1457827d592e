/*
 * Powered By fast.up
 */
package com.fast.service.group;

import com.fast.constant.StaticStr;
import com.fast.mapper.group.FastMemberGroupAreaMapper;
import com.fast.po.group.FastMemberGroupAreaPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberGroupAreaService extends BaseService {

    @Autowired
    private FastMemberGroupAreaMapper fastMemberGroupAreaMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberGroupAreaPO queryById(FastMemberGroupAreaPO params) {
        return fastMemberGroupAreaMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberGroupAreaPO queryById(Integer id) {
        return fastMemberGroupAreaMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberGroupAreaPO queryOne(FastMemberGroupAreaPO params) {
        return fastMemberGroupAreaMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberGroupAreaPO> queryList(FastMemberGroupAreaPO params) {
        return fastMemberGroupAreaMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberGroupAreaPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberGroupAreaPO> list = fastMemberGroupAreaMapper.queryList(params);
        for (FastMemberGroupAreaPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberGroupAreaPO params) {
        return fastMemberGroupAreaMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberGroupAreaPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberGroupAreaMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberGroupAreaPO> list) {
        if (fastMemberGroupAreaMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberGroupAreaPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberGroupAreaMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
