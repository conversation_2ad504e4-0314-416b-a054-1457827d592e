/*
 * Powered By fast.up
 */
package com.fast.service.group;

import com.fast.constant.StaticStr;
import com.fast.mapper.group.FastJobPointerMapper;
import com.fast.po.group.FastJobPointerPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastJobPointerService extends BaseService {

    @Autowired
    private FastJobPointerMapper fastJobPointerMapper;

    // 查询指针
    public Long queryPointerByCode(String jobCode) {
        FastJobPointerPO params = new FastJobPointerPO();
        params.setJobCode(jobCode);
        FastJobPointerPO item = fastJobPointerMapper.queryOne(params);
        if (item == null) {
            item = new FastJobPointerPO();
            item.setJobCode(jobCode);
            item.setPointer(0L);
            Date timeNow = DateUtil.getNowDate();
            item.setCreateTime(timeNow);
            item.setUpdateTime(timeNow);
            fastJobPointerMapper.insertSelective(item);
        }
        return item.getPointer();
    }

    // 向上更新指针
    public int updatePointerUpByCode(String jobCode, Long pointer) {
        FastJobPointerPO params = new FastJobPointerPO();
        params.setJobCode(jobCode);
        params.setPointer(pointer);
        return fastJobPointerMapper.updatePointerUpByCode(params);
    }


    public FastJobPointerPO queryById(FastJobPointerPO params) {
        return fastJobPointerMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastJobPointerPO queryById(Integer id) {
        return fastJobPointerMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastJobPointerPO queryOne(FastJobPointerPO params) {
        return fastJobPointerMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastJobPointerPO> queryList(FastJobPointerPO params) {
        return fastJobPointerMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastJobPointerPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastJobPointerPO> list = fastJobPointerMapper.queryList(params);
        for (FastJobPointerPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastJobPointerPO params) {
        return fastJobPointerMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastJobPointerPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastJobPointerMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastJobPointerPO> list) {
        if (fastJobPointerMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastJobPointerPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastJobPointerMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
