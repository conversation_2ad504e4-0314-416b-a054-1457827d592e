/*
 * Powered By fast.up
 */
package com.fast.service.group;

import com.fast.constant.StaticStr;
import com.fast.mapper.group.FastMemberGroupMapper;
import com.fast.po.group.FastMemberGroupPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberGroupService extends BaseService {

    @Autowired
    private FastMemberGroupMapper fastMemberGroupMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberGroupPO queryById(FastMemberGroupPO params) {
        return fastMemberGroupMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberGroupPO queryById(Integer id) {
        return fastMemberGroupMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberGroupPO queryOne(FastMemberGroupPO params) {
        return fastMemberGroupMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberGroupPO> queryList(FastMemberGroupPO params) {
        return fastMemberGroupMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberGroupPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberGroupPO> list = fastMemberGroupMapper.queryList(params);
        for (FastMemberGroupPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberGroupPO params) {
        return fastMemberGroupMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberGroupPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberGroupMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberGroupPO> list) {
        if (fastMemberGroupMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberGroupPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberGroupMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
