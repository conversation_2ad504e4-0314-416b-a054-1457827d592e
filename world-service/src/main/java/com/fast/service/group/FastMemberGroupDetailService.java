/*
 * Powered By fast.up
 */
package com.fast.service.group;

import com.fast.constant.StaticStr;
import com.fast.mapper.group.FastMemberGroupDetailMapper;
import com.fast.po.group.FastMemberGroupDetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberGroupDetailService extends BaseService {

    @Autowired
    private FastMemberGroupDetailMapper fastMemberGroupDetailMapper;
    @Autowired
    private FastJobPointerService fastJobPointerService;

    /**
     * 重置用户当日数据
     */
    public void resetMemberDayDataJob() {


    }

    /**
     * 更新人群分组订单类型
     * orderType 1=充值K币;2=充值VIP;3=赠送K币;4=剧卡充值
     */
    @Async
    public void updateMemberGroupRechargeType(Long memberId, Integer orderType) {
        FastMemberGroupDetailPO gdParam = new FastMemberGroupDetailPO();
        gdParam.setId(memberId);
        if (orderType != null && orderType == 1) {
            gdParam.setRechargeCoinFlag(1); // K币
        } else if (orderType != null && orderType == 2) {
            gdParam.setRechargeVipFlag(1);  // vip
        } else if (orderType != null && orderType == 4) {
            gdParam.setRechargeCardFlag(1); // 剧卡
        }
        gdParam.setUpdateTime(DateUtil.getNowDate());
        fastMemberGroupDetailMapper.updateById(gdParam);
    }

    /**
     * 添加人群分组信息
     */
    @Async
    public void addMemberGroupDetail(FastMemberGroupDetailPO gdPO) {
        Date nowTime = DateUtil.getNowDate();
        gdPO.setCreateTime(nowTime);
        gdPO.setUpdateTime(nowTime);
        fastMemberGroupDetailMapper.insertSelective(gdPO);
    }

    @Async
    public void updateMemberGroupDetail(FastMemberGroupDetailPO gdPO) {
        gdPO.setUpdateTime(DateUtil.getNowDate());
        fastMemberGroupDetailMapper.updateById(gdPO);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberGroupDetailPO queryById(FastMemberGroupDetailPO params) {
        return fastMemberGroupDetailMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberGroupDetailPO queryById(Integer id) {
        return fastMemberGroupDetailMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberGroupDetailPO queryOne(FastMemberGroupDetailPO params) {
        return fastMemberGroupDetailMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberGroupDetailPO> queryList(FastMemberGroupDetailPO params) {
        return fastMemberGroupDetailMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberGroupDetailPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberGroupDetailPO> list = fastMemberGroupDetailMapper.queryList(params);
        for (FastMemberGroupDetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberGroupDetailPO params) {
        return fastMemberGroupDetailMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberGroupDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberGroupDetailMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberGroupDetailPO> list) {
        if (fastMemberGroupDetailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberGroupDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberGroupDetailMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
