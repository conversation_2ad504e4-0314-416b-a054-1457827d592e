/*
 * Powered By fast.up
 */
package com.fast.service.group;

import com.fast.constant.StaticStr;
import com.fast.mapper.group.FastMemberGroupRegionMapper;
import com.fast.po.group.FastMemberGroupRegionPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberGroupRegionService extends BaseService {

    @Autowired
    private FastMemberGroupRegionMapper fastMemberGroupRegionMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberGroupRegionPO queryById(FastMemberGroupRegionPO params) {
        return fastMemberGroupRegionMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberGroupRegionPO queryById(Integer id) {
        return fastMemberGroupRegionMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberGroupRegionPO queryOne(FastMemberGroupRegionPO params) {
        return fastMemberGroupRegionMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberGroupRegionPO> queryList(FastMemberGroupRegionPO params) {
        return fastMemberGroupRegionMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberGroupRegionPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberGroupRegionPO> list = fastMemberGroupRegionMapper.queryList(params);
        for (FastMemberGroupRegionPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberGroupRegionPO params) {
        return fastMemberGroupRegionMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberGroupRegionPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberGroupRegionMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberGroupRegionPO> list) {
        if (fastMemberGroupRegionMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberGroupRegionPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberGroupRegionMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
