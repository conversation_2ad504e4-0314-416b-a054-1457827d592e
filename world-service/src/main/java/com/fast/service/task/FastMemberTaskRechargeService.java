/*
 * Powered By fast.up
 */
package com.fast.service.task;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.task.FastMemberTaskActionMapper;
import com.fast.mapper.task.FastMemberTaskRechargeMapper;
import com.fast.po.member.FastMemberAccountPO;
import com.fast.po.task.FastMemberTaskActionPO;
import com.fast.po.task.FastMemberTaskRechargePO;
import com.fast.po.task.FastSettingTaskCoinPO;
import com.fast.po.task.FastSettingTaskPO;
import com.fast.service.base.BaseService;
import com.fast.service.member.FastMemberAccountService;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberTaskRechargeService extends BaseService {

    @Autowired
    private FastMemberTaskRechargeMapper fastMemberTaskRechargeMapper;
    @Autowired
    private FastMemberTaskRechargeService fastMemberTaskRechargeService;
    @Autowired
    private FastMemberTaskActionService fastMemberTaskActionService;
    @Autowired
    private FastMemberTaskActionMapper fastMemberTaskActionMapper;
    @Autowired
    private FastSettingTaskService fastSettingTaskService;
    @Autowired
    private FastMemberAccountService fastMemberAccountService;

    // 过期K币生成记录
    public void synExpireCoinJob() {
        int count = 0; // 每次最多执行50个循环
        boolean loop = true;
        Integer countAll = 0;
        Integer countSuccess = 0;
        while (loop && count < 50) {
            FastMemberTaskRechargePO trParam = new FastMemberTaskRechargePO();
            List<FastMemberTaskRechargePO> trList = fastMemberTaskRechargeMapper.queryExpireList(trParam);
            if (trList.size() == 0) {
                loop = false;
            }
            countAll += trList.size();
            Date nowTime = DateUtil.getNowDate();
            for (FastMemberTaskRechargePO trPO : trList) {
                // 添加记录
                FastMemberTaskRechargePO taskPO = new FastMemberTaskRechargePO();
                taskPO.setActionId(trPO.getActionId());
                taskPO.setCoinAll(0);
                taskPO.setCoinRemain(0);
                taskPO.setCoinCost(trPO.getCoinRemain());
                taskPO.setCreateTime(nowTime);
                taskPO.setUpdateTime(nowTime);
                taskPO.setEffectTime(trPO.getExpireTime());
                taskPO.setExpireTime(trPO.getExpireTime());
                taskPO.setMemberId(trPO.getMemberId());
                taskPO.setRetailId(trPO.getRetailId());
                taskPO.setLinkId(trPO.getLinkId());
                taskPO.setMiniId(trPO.getMiniId());
                taskPO.setOfficialId(trPO.getOfficialId());
                taskPO.setRemark("过期");
                taskPO.setDataType(2);
                if (fastMemberTaskRechargeService.addMemberTaskRecharge(null, taskPO)) {
                    FastMemberTaskRechargePO trUpdate = new FastMemberTaskRechargePO();
                    trUpdate.setId(trPO.getId());
                    trUpdate.setExpireHandle(1);
                    trUpdate.setUpdateTime(nowTime);
                    fastMemberTaskRechargeMapper.updateById(trUpdate);
                    countSuccess++;
                }
            }
            count++;
        }
        log.info("福利任务过期:总" + countAll + "条，成功" + countSuccess + "条");
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberTaskRechargePO queryById(FastMemberTaskRechargePO params) {
        return fastMemberTaskRechargeMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberTaskRechargePO queryById(Integer id) {
        return fastMemberTaskRechargeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberTaskRechargePO queryOne(FastMemberTaskRechargePO params) {
        return fastMemberTaskRechargeMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberTaskRechargePO> queryList(FastMemberTaskRechargePO params) {
        return fastMemberTaskRechargeMapper.queryList(params);
    }


    /**
     * 添加任务K币唯一入口
     */
    public boolean addMemberTaskRecharge(SessionVO sessionVO, FastMemberTaskRechargePO trPO) {
        Date nowDate = DateUtil.getNowDate();
        trPO.setCoinRemain(trPO.getCoinAll());
        if (sessionVO != null) {
            trPO.setRetailId(sessionVO.getRetailId());
            trPO.setMiniId(sessionVO.getMiniId());
            trPO.setOfficialId(sessionVO.getOfficialId());
            trPO.setLinkId(sessionVO.getLinkId());
            trPO.setMemberId(sessionVO.getMemberId());
        }
        trPO.setCreateTime(nowDate);
        trPO.setEffectTime(nowDate);// 起效时间
        if (trPO.getCoinCost() != null && trPO.getCoinCost() > 0) {
            trPO.setExpireTime(nowDate);// 过期时间
        } else {
            trPO.setExpireTime(DateUtil.addDays(nowDate, 5));// 过期时间
        }
        if (fastMemberTaskRechargeMapper.insertSelective(trPO) == 0) {
            transactionRollBack();
            return false;
        }

        // 增加账户余额
        FastMemberAccountPO item = new FastMemberAccountPO();
        item.setMemberId(trPO.getMemberId());
        item.setCoinTaskAll(trPO.getCoinAll());
        item.setCoinAll(trPO.getCoinAll());
        item.setCoinTaskRemain(trPO.getCoinAll());
        item.setCoinRemain(trPO.getCoinAll());
        item.setRemark(trPO.getRemark());
        MethodVO methodVO = fastMemberAccountService.updatePlusById(item);
        if (methodVO.getCode() != 0) {
            transactionRollBack();
            return false;
        }

        String keyTask = StaticVar.MEMBER_TASK_COIN + trPO.getMemberId();
        RedisUtil.del(keyTask);
        return true;
    }

    // 查询用户的福利任务K币
    public FastMemberTaskRechargePO queryMemberTaskRecharge(Long memberId) {
        FastMemberTaskRechargePO trPO = null;
        if (fastMemberTaskActionService.judgeMemberTask(memberId)) {
            FastMemberTaskRechargePO trParam = new FastMemberTaskRechargePO();
            trParam.setMemberId(memberId);
            trPO = fastMemberTaskRechargeMapper.queryMemberCoin(trParam);
        } else {
            trPO = new FastMemberTaskRechargePO();
            trPO.setCoinAll(0);
            trPO.setCoinCost(0);
            trPO.setCoinRemain(0);
        }
        return trPO;
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberTaskRechargePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberTaskRechargePO> list = fastMemberTaskRechargeMapper.queryList(params);
        for (FastMemberTaskRechargePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        // 查询汇总信息
        FastMemberTaskRechargePO trPO = queryMemberTaskRecharge(params.getMemberId());
        Integer coinUsed = 0;
        Integer coinExpire = 0;
        Integer coinRemain = 0;
        Integer coinAll = 0;
        if (trPO != null) {
            coinUsed = trPO.getCoinUsed();
            coinExpire = trPO.getCoinExpire();
            coinRemain = trPO.getCoinRemain();
            coinAll = trPO.getCoinAll();
        }
        results.put("coinUsed", coinUsed);
        results.put("coinExpire", coinExpire);
        results.put("coinRemain", coinRemain);
        results.put("coinAll", coinAll);
        return ResultVO.success(results);
    }

    /**
     * 按会员分组后分页查询
     */
    public ResultVO<?> querySummaryPageList(FastMemberTaskRechargePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberTaskRechargePO> list = fastMemberTaskRechargeMapper.queryMemberSummaryList(params);
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
//    public int queryCount(FastMemberTaskRechargePO params){
//        return fastMemberTaskRechargeMapper.queryCount(params);
//    }

    /**
     * 新增
     */
//    @Transactional(rollbackFor = Exception.class)
//    public MethodVO insert(FastMemberTaskRechargePO params)  {
//    	Date nowTime = DateUtil.getNowDate();
//        params.setCreateTime(nowTime);
//        if (fastMemberTaskRechargeMapper.insertSelective(params) == 0) {
//            transactionRollBack();
//            return MethodVO.error(StaticStr.ERROR_ADD);
//        }
//        return MethodVO.success();
//    }

    /**
     * 批量新增
     */
//    @Transactional(rollbackFor = Exception.class)
//    public MethodVO insertBatch(List<FastMemberTaskRechargePO> list) {
//        if(fastMemberTaskRechargeMapper.insertBatch(list) > 0) {
//            return MethodVO.success();
//        }else {
//            return MethodVO.error(StaticStr.ERROR_ADD);
//        }
//    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberTaskRechargePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberTaskRechargeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 领取奖励
     */
    public MethodVO getPrize(SessionVO sessionVO, FastMemberTaskActionPO params) {
        // 查询待完成任务
        FastMemberTaskActionPO taPO = fastMemberTaskActionService.queryById(params.getId());
        if (taPO == null) {
            return MethodVO.error("没有任务记录");
        }
        if (!params.getMemberId().equals(taPO.getMemberId())) {
            return MethodVO.error("任务记录不存在");
        }
        if (taPO.getAwardState() == 1) {
            return MethodVO.error("已经领取");
        }

        FastSettingTaskPO taskPO = fastSettingTaskService.getSettingTaskRedis(sessionVO.getMiniId());
        if (taskPO == null) {
            return MethodVO.error("未配置福利规则");
        }
        Map<String, Integer> coinMap = new HashMap<>();
        List<FastSettingTaskCoinPO> coinList = taskPO.getCoinList();
        for (FastSettingTaskCoinPO coin : coinList) {
            coinMap.put(coin.getTaskType() + "_" + coin.getMarkIdx(), coin.getCoin());
        }

        Integer coinAll = coinMap.get(taPO.getTaskType() + "_" + taPO.getMarkIdx());
        if (coinAll == null) {
            return MethodVO.error("未配置币");
        }

        FastMemberTaskRechargePO trPO = new FastMemberTaskRechargePO();
        String remark = fastMemberTaskActionService.getRemark(taPO.getTaskType(), taPO.getMarkIdx());
        log.info("任务领奖remark:{}", remark);
        trPO.setCoinAll(coinAll);
        trPO.setRemark(remark);
        trPO.setActionId(taPO.getId());
        if (addMemberTaskRecharge(sessionVO, trPO)) {
            taPO.setAwardState(1);
            taPO.setAwardTime(DateUtil.getNowDate());
            fastMemberTaskActionMapper.updateById(taPO);
            return MethodVO.success("领取成功");
        }
        return MethodVO.error("领取失败");
    }

}










