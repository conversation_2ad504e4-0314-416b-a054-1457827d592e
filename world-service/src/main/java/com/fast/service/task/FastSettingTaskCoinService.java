/*
 * Powered By fast.up
 */
package com.fast.service.task;

import com.fast.constant.StaticStr;
import com.fast.mapper.task.FastSettingTaskCoinMapper;
import com.fast.po.task.FastSettingTaskCoinPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastSettingTaskCoinService extends BaseService {

    @Autowired
    private FastSettingTaskCoinMapper fastSettingTaskCoinMapper;

    /**
     * 通过id查询单个对象
     */
    public FastSettingTaskCoinPO queryById(FastSettingTaskCoinPO params) {
        return fastSettingTaskCoinMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSettingTaskCoinPO queryById(Integer id) {
        return fastSettingTaskCoinMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingTaskCoinPO queryOne(FastSettingTaskCoinPO params) {
        return fastSettingTaskCoinMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastSettingTaskCoinPO> queryList(FastSettingTaskCoinPO params) {
        return fastSettingTaskCoinMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingTaskCoinPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastSettingTaskCoinPO> list = fastSettingTaskCoinMapper.queryList(params);
        for (FastSettingTaskCoinPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingTaskCoinPO params) {
        return fastSettingTaskCoinMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSettingTaskCoinPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastSettingTaskCoinMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastSettingTaskCoinPO> list) {
        if (fastSettingTaskCoinMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSettingTaskCoinPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastSettingTaskCoinMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
