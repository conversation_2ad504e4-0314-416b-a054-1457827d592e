/*
 * Powered By fast.up
 */
package com.fast.service.task;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.mapper.member.FastMemberAddictionMapper;
import com.fast.mapper.member.FastMemberRecentLogMapper;
import com.fast.mapper.task.FastMemberTaskActionMapper;
import com.fast.mapper.task.FastMemberTaskRechargeMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.member.FastMemberAddictionPO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.po.task.FastMemberTaskActionPO;
import com.fast.po.task.FastMemberTaskRechargePO;
import com.fast.po.task.FastSettingTaskCoinPO;
import com.fast.po.task.FastSettingTaskPO;
import com.fast.service.base.BaseService;
import com.fast.service.member.FastMemberAccountService;
import com.fast.service.member.FastMemberEnterFromService;
import com.fast.service.member.FastMemberMessagePushService;
import com.fast.service.member.FastMemberService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.playlist.FastMiniPlaylistDramaService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.member.MemberAccountVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberTaskActionService extends BaseService {

    @Autowired
    private FastMemberTaskActionMapper fastMemberTaskActionMapper;
    @Autowired
    private FastMemberTaskRechargeMapper fastMemberTaskRechargeMapper;
    @Autowired
    private FastSettingTaskService fastSettingTaskService;
    @Autowired
    private FastMemberTaskRechargeService fastMemberTaskRechargeService;
    @Autowired
    private FastMemberRecentLogMapper fastMemberRecentLogMapper;
    @Autowired
    private FastMemberAddictionMapper fastMemberAddictionMapper;
    @Autowired
    private FastMemberAccountService accountService;
    @Autowired
    private FastDramaMapper fastDramaMapper;
    @Autowired
    private FastMemberEnterFromService memberEnterFromService;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMemberService fastMemberService;
    @Autowired
    private FastMemberMessagePushService fastMemberMessagePushService;
    @Autowired
    private FastMiniPlaylistDramaService fastMiniPlaylistDramaService;

    // 判断会员是否福利任务用户
    public boolean judgeMemberTask(Long memberId) {
        String keyTask = StaticVar.MEMBER_TASK_COIN + memberId;
        String resTask = RedisUtil.get(keyTask);
        if (StrUtil.isNotEmpty(resTask)) {
            return !StaticVar.EMPTY_FLAG.equals(resTask);
        }
        // 查询数据库
        FastMemberTaskActionPO trParam = new FastMemberTaskActionPO();
        trParam.setMemberId(memberId);
        FastMemberTaskActionPO trPO = fastMemberTaskActionMapper.queryOne(trParam);
        if (trPO == null) {
            RedisUtil.set(keyTask, StaticVar.EMPTY_FLAG, 60 * 60);
            return false;
        } else {
            RedisUtil.set(keyTask, "exist", 60 * 60 * 24 * 10);
            return true;
        }
    }

    /**
     * 添邀请记录
     */
    public ResultVO addInviteLog(SessionVO sessionVO) {
        FastSettingTaskPO taskPO = fastSettingTaskService.getSettingTaskRedis(sessionVO.getMiniId());
        if (taskPO == null) {
            return ResultVO.error(StaticStr.RULE_NOT_CONFIGURED);
        }
        FastMemberTaskActionPO taParam = new FastMemberTaskActionPO();
        Integer circleType = taskPO.getCircleUsual();
        Date todayStart = DateUtil.beginOfDay();// 当天开始时间
        Date weekStart = DateUtil.beginOfWeek();
        Date monthStart = DateUtil.beginOfMonth();
        if (circleType == 1) { // 天
            taParam.setCreateTime(todayStart);
        } else if (circleType == 2) { // 周
            taParam.setCreateTime(weekStart);
        } else if (circleType == 3) {
            taParam.setCreateTime(monthStart);
        } else {
            return ResultVO.error(StaticStr.CYCLE_NOT_CONFIGURED);
        }
        taParam.setMemberId(sessionVO.getMemberId());
        taParam.setTaskType(4);// 日常任务
        taParam.setMarkIdx(3);// 邀请好友
        List<FastMemberTaskActionPO> taList = fastMemberTaskActionMapper.querySignList(taParam);
        if (taList.size() > 0) {
            FastMemberTaskActionPO taPO = taList.get(0);
            Integer processNum = taPO.getProcessNum();
            processNum = processNum == null ? 1 : processNum + 1;
            taPO.setProcessNum(processNum);
            taPO.setUpdateTime(DateUtil.getNowDate());
            if (processNum >= 5) {
                taPO.setState(1);
            }
            fastMemberTaskActionMapper.updateById(taPO);
        }
        return ResultVO.success();
    }

    // 添加会员行为
    public ResultVO addTaskAction(SessionVO sessionVO, FastMemberTaskActionPO actionPO) {
        FastSettingTaskPO taskPO = fastSettingTaskService.getSettingTaskRedis(sessionVO.getMiniId());
        if (taskPO == null) {
            return ResultVO.error(StaticStr.RULE_NOT_CONFIGURED);
        }
        Map<String, Integer> coinMap = new HashMap<>(); // 任务对应币的集合
        List<FastSettingTaskCoinPO> coinList = taskPO.getCoinList();// 任务对应币的列表
        for (FastSettingTaskCoinPO coin : coinList) {
            coinMap.put(coin.getTaskType() + "_" + coin.getMarkIdx(), coin.getCoin());
        }
        Date nowDate = DateUtil.getNowDate();
        Date todayStart = DateUtil.beginOfDay();// 当天开始时间
        Date todayEnd = DateUtil.endOfDay();// 当天结束时间
        Date yesterdayStart = DateUtil.format07(DateUtil.format09(DateUtil.getYesterdayDate()) + " 00:00:00");
        Date weekStart = DateUtil.beginOfWeek();
        Date monthStart = DateUtil.beginOfMonth();
        if (actionPO.getTaskType() == 1) {
            // 签到，缓存验证是否当天已经签到
            String key = StaticVar.MINI_TASK_SIGN + sessionVO.getMemberId();
            String res = RedisUtil.get(key);
            if (StrUtil.isNotEmpty(res)) {
                return ResultVO.error(StaticStr.SIGNED_IN_TODAY);
            }
            if (actionPO.getMarkIdx() == null) {
                return ResultVO.error(StaticStr.SIGN_DAY_NUM_CANNOT_BE_EMPTY);
            }
            Long leftSeconds = (todayEnd.getTime() - nowDate.getTime()) / 1000;// 今天剩余秒数
            // 数据库查询验证当天是否签到
            FastMemberTaskActionPO acParam1 = new FastMemberTaskActionPO();
            acParam1.setCreateTime(yesterdayStart);
            acParam1.setMemberId(sessionVO.getMemberId());
            acParam1.setTaskType(1);
            List<FastMemberTaskActionPO> actionList = fastMemberTaskActionMapper.querySignList(acParam1);
            Integer signDay = 1;// 第几天签到
            if (actionList.size() > 0) {
                FastMemberTaskActionPO lastAction = actionList.get(0);
                if (lastAction.getActionTime().after(todayStart)) {
                    RedisUtil.set(key, "ok", leftSeconds.intValue());
                    return ResultVO.error(StaticStr.SIGNED_IN_TODAY);
                }
                if (lastAction.getActionTime().after(yesterdayStart)) {
                    // 昨天签到
                    signDay = lastAction.getMarkIdx() + 1;
                    signDay = signDay > 7 ? 1 : signDay;
                }
            }
            if (signDay != actionPO.getMarkIdx()) {
                return ResultVO.error(StaticStr.SIGN_FAILED);
            }
            // 保存操作记录
            actionPO.setActionTime(nowDate);
            actionPO.setAwardState(1);
            actionPO.setAwardTime(nowDate);
            actionPO.setCreateTime(nowDate);
            actionPO.setState(1);// 完成
            actionPO.setMemberId(sessionVO.getMemberId());
            if (fastMemberTaskActionMapper.insertSelective(actionPO) > 0) {
                Integer coinAll = coinMap.get("1_" + signDay);
                FastMemberTaskRechargePO trPO = new FastMemberTaskRechargePO();
                trPO.setActionId(actionPO.getId());
                // 仅支持2倍3倍
                if (actionPO.getMoreTimesFlag() != null && actionPO.getMoreTimesFlag() > 1 && actionPO.getMoreTimesFlag() < 4) {
                    trPO.setCoinAll(coinAll * actionPO.getMoreTimesFlag());
                } else {
                    trPO.setCoinAll(coinAll);
                }
                trPO.setRemark(StaticStr.SEVEN_DAY_SIGN);
                // 发起加币
                if (fastMemberTaskRechargeService.addMemberTaskRecharge(sessionVO, trPO)) {
                    RedisUtil.set(key, "ok", leftSeconds.intValue());
                    return ResultVO.success();
                }
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
        } else if (actionPO.getTaskType() == 2) {
            // 看视频
            Integer circleType = taskPO.getCircleAdv();
            // 判断周期内是否完成看视频
            String key = StaticVar.MINI_TASK_ADV + sessionVO.getMemberId();
            String res = RedisUtil.get(key);
            if (StrUtil.isNotEmpty(res)) {
                return ResultVO.error(StaticStr.DUPLICATE_TASK_COMPLETE_NOT_ALLOWED);
            }
            // 数据库验证是否看完
            FastMemberTaskActionPO acParam1 = new FastMemberTaskActionPO();
            Date endDate = null;
            if (circleType == 1) { // 天
                acParam1.setCreateTime(todayStart);
                endDate = DateUtil.endOfDay();
            } else if (circleType == 2) { // 周
                acParam1.setCreateTime(weekStart);
                endDate = DateUtil.endOfWeek();
            } else if (circleType == 3) {
                acParam1.setCreateTime(monthStart);
                endDate = DateUtil.endOfMonth();
            } else {
                return ResultVO.error(StaticStr.CYCLE_NOT_CONFIGURED);
            }
            acParam1.setMemberId(sessionVO.getMemberId());
            acParam1.setTaskType(2);
            List<FastMemberTaskActionPO> actionList = fastMemberTaskActionMapper.querySignList(acParam1);
            if (actionList.size() >= 3) {
                Long leftSeconds = (endDate.getTime() - nowDate.getTime()) / 1000;
                RedisUtil.set(key, "ok", leftSeconds.intValue());
                return ResultVO.error(StaticStr.DUPLICATE_TASK_COMPLETE_NOT_ALLOWED);
            }
            if (actionList.size() == 0) {
                actionPO.setMarkIdx(1);
            } else if (actionList.size() == 1) {
                actionPO.setMarkIdx(2);
            } else {
                actionPO.setMarkIdx(3);
            }
            // 保存操作记录
            actionPO.setActionTime(nowDate);
            actionPO.setAwardState(1);
            actionPO.setAwardTime(nowDate);
            actionPO.setCreateTime(nowDate);
            actionPO.setState(1);// 完成
            actionPO.setMemberId(sessionVO.getMemberId());
            if (fastMemberTaskActionMapper.insertSelective(actionPO) > 0) {
                Integer coinAll = coinMap.get("2_" + actionPO.getMarkIdx());
                if (coinAll == null) {
                    return ResultVO.error(StaticStr.COIN_NOT_CONFIGURED);
                }
                FastMemberTaskRechargePO trPO = new FastMemberTaskRechargePO();
                trPO.setActionId(actionPO.getId());
                trPO.setCoinAll(coinAll);
                trPO.setRemark("看广告领奖励");
                // 发起加币
                if (fastMemberTaskRechargeService.addMemberTaskRecharge(sessionVO, trPO)) {
                    return ResultVO.success();
                }
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
        } else if (actionPO.getTaskType() == 3) {
            // 新手任务，新增和完成
            String key = StaticVar.MINI_TASK_NEW + "_" + actionPO.getMarkIdx() + "_" + sessionVO.getMemberId();
            if (actionPO.getState() == 0) {
                // 新增
                String res = RedisUtil.get(key);
                if (StrUtil.isNotEmpty(res)) {
                    return ResultVO.error(StaticStr.TASK_EXIST);
                }
            }
            // 数据库验证数据是否已经存在
            FastMemberTaskActionPO acParam1 = new FastMemberTaskActionPO();
            acParam1.setMemberId(sessionVO.getMemberId());
            acParam1.setMarkIdx(actionPO.getMarkIdx());// 区分
            acParam1.setTaskType(3);
            FastMemberTaskActionPO actionExist = fastMemberTaskActionMapper.queryOne(acParam1);
            if (actionPO.getState() == 0 && actionExist != null) {
                RedisUtil.set(key, "ok", 60 * 60 * 24 * 30);
                return ResultVO.error(StaticStr.TASK_EXIST);
            }

            FastMiniVO fastMiniVO = fastMiniService.queryInfoByRedis(sessionVO.getMiniId());
            // 微小福利任务加桌和从桌面进入判断  新手任务默认是直接完成的微小需要单独判断
            Integer actionState = null;
            if ((Arrays.asList(1, 2).contains(actionPO.getMarkIdx())) && fastMiniVO.getType() == 1) {
                boolean desktopFlag = memberEnterFromService.queryMemberFromDesktopByRedis(sessionVO.getMemberId());
                actionState = desktopFlag ? 1 : 0;
                log.info("判断微小加桌福利任务actionState:{}", actionState);
            }
            // 微小关注公众号新手任务
            if (fastMiniVO.getType() == 1 && actionPO.getMarkIdx() == 4) {
                String memberFollowState = fastMemberService.getMemberFollowState(sessionVO.getMemberId());
                actionState = "1".equals(memberFollowState) ? 1 : 0;
                // 之前已完成过的不进行更改
                if (Objects.nonNull(actionExist) && actionExist.getState() == 1) {
                    actionState = 1;
                }
            }

            if (actionExist == null) {
                // 新增
                actionPO.setAwardState(0);
                actionPO.setCreateTime(nowDate);
                actionPO.setState(Objects.isNull(actionState) ? actionPO.getState() : actionState);// 完成
                actionPO.setMemberId(sessionVO.getMemberId());
                if (fastMemberTaskActionMapper.insertSelective(actionPO) == 0) {
                    return ResultVO.error(StaticStr.ADD_FAILED);
                }
            } else {
                // 更新 新手任务除了微小加桌直接完成
                actionExist.setState(Objects.isNull(actionState) ? 1 : actionState);
                actionExist.setUpdateTime(nowDate);
                fastMemberTaskActionMapper.updateById(actionExist);
            }
        } else if (actionPO.getTaskType() == 4) {
            // 日常
            String key = StaticVar.MINI_TASK_USUAL + "_" + actionPO.getMarkIdx() + "_" + sessionVO.getMemberId();
            String res = RedisUtil.get(key);
            if (StrUtil.isNotEmpty(res) && actionPO.getState() == 0) {
                return ResultVO.error(StaticStr.TASK_EXIST);
            }
            // 数据库验证数据是否已经存在
            FastMemberTaskActionPO acParam1 = new FastMemberTaskActionPO();
            acParam1.setMemberId(sessionVO.getMemberId());
            acParam1.setMarkIdx(actionPO.getMarkIdx());// 区分
            acParam1.setTaskType(4);
            Date endDate = null;
            Integer circleType = taskPO.getCircleUsual();
            if (circleType == 1) { // 天
                acParam1.setCreateTime(todayStart);
                endDate = DateUtil.endOfDay();
            } else if (circleType == 2) { // 周
                acParam1.setCreateTime(weekStart);
                endDate = DateUtil.endOfWeek();
            } else if (circleType == 3) {
                acParam1.setCreateTime(monthStart);
                endDate = DateUtil.endOfMonth();
            } else {
                return ResultVO.error(StaticStr.CYCLE_NOT_CONFIGURED);
            }
            List<FastMemberTaskActionPO> actionList = fastMemberTaskActionMapper.querySignList(acParam1);
            // 只取第一条
            if (!actionList.isEmpty() && actionPO.getState() == 0) {
                long leftSeconds = (endDate.getTime() - nowDate.getTime()) / 1000;
                RedisUtil.set(key, "ok", (int) leftSeconds);
                return ResultVO.error(StaticStr.TASK_EXIST);
            }

            if (actionList.isEmpty()) {
                // 保存操作记录
                actionPO.setAwardState(0);
                actionPO.setCreateTime(nowDate);
                if (actionPO.getMarkIdx() == 2 || actionPO.getMarkIdx() == 3) {
                    actionPO.setTargetNum(5);
                }
                if (actionPO.getMarkIdx() == 1) {
                    // 剧场逛逛直接完成
                    actionPO.setState(1);
                    actionPO.setActionTime(nowDate);
                }
                actionPO.setMemberId(sessionVO.getMemberId());
                if (fastMemberTaskActionMapper.insertSelective(actionPO) == 0) {
                    return ResultVO.error(StaticStr.ADD_FAILED);
                }
            } else {
                FastMemberTaskActionPO actionExist = actionList.get(0);
                actionExist.setState(1);
                actionExist.setActionTime(nowDate);
                actionExist.setUpdateTime(nowDate);
                fastMemberTaskActionMapper.updateById(actionExist);
            }
        } else if (actionPO.getTaskType() == 5) {
            // 成就
            String key = StaticVar.MINI_TASK_PRIDE + "_" + actionPO.getMarkIdx() + "_" + sessionVO.getMemberId();
            String res = RedisUtil.get(key);
            if (StrUtil.isNotEmpty(res) && actionPO.getState() == 0) {
                return ResultVO.error(StaticStr.TASK_EXIST);
            }
            // 数据库验证数据是否已经存在
            FastMemberTaskActionPO acParam1 = new FastMemberTaskActionPO();
            acParam1.setMemberId(sessionVO.getMemberId());
            acParam1.setMarkIdx(actionPO.getMarkIdx());// 区分
            acParam1.setTaskType(5);
            FastMemberTaskActionPO actionExist = fastMemberTaskActionMapper.queryOne(acParam1);
            if (actionExist != null && actionPO.getState() == 0) {
                RedisUtil.set(key, "ok", 60 * 60 * 24 * 30);
                return ResultVO.error(StaticStr.TASK_EXIST);
            }
            if (actionExist == null) {
                // 保存操作记录
                actionPO.setAwardState(0);
                actionPO.setCreateTime(nowDate);
                actionPO.setMemberId(sessionVO.getMemberId());
                if (fastMemberTaskActionMapper.insertSelective(actionPO) == 0) {
                    return ResultVO.error(StaticStr.ADD_FAILED);
                }
            } else {
                actionExist.setState(1);
                actionExist.setActionTime(nowDate);
                actionExist.setUpdateTime(nowDate);
                fastMemberTaskActionMapper.updateById(actionExist);
            }
        }
        return ResultVO.success();
    }

    /**
     * 查询用户福利任务情况
     */
    public ResultVO getMemberTaskInfo(SessionVO sessionVO) {
        Integer miniId = sessionVO.getMiniId();
        Long memberId = sessionVO.getMemberId();
        Map<String, Object> results = new HashMap<>();
        // app
        FastMiniVO fastMiniVO = fastMiniService.queryInfoByRedis(sessionVO.getMiniId());
        if (fastMiniVO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }
        // 任务配置
        FastSettingTaskPO taskPO = fastSettingTaskService.getSettingTaskRedis(miniId);
        if (taskPO == null) {
            return ResultVO.error(StaticStr.TASK_CONFIG_IS_EMPTY);
        }
        results.put("taskSetting", taskPO);
        Date nowDate = DateUtil.getNowDate();
        Date todayStart = DateUtil.beginOfDay();
        Date weekStart = DateUtil.beginOfWeek();
        Date monthStart = DateUtil.beginOfMonth();
        Date yesterdayStart = DateUtil.addDays(todayStart, -1);
        // 统一key
        Map<String, FastMemberTaskActionPO> map = new HashMap<>();
        // 查询7天签到情况(查询近1天+今天=2天)
        if (taskPO.getSwitchSign() != null && taskPO.getSwitchSign() == 1) {
            FastMemberTaskActionPO acParam1 = new FastMemberTaskActionPO();
            acParam1.setCreateTime(DateUtil.addDays(todayStart, -1));
            acParam1.setMemberId(memberId);
            acParam1.setTaskType(1);
            List<FastMemberTaskActionPO> actionList = fastMemberTaskActionMapper.querySignList(acParam1);
            int signDay = 1; // 签到天数
            int signToday = 0;// 今日签到0未签1已签
            if (CollUtil.isNotEmpty(actionList)) {
                if (actionList.get(0).getActionTime().after(todayStart)) {
                    signToday = 1;
                    signDay = actionList.get(0).getMarkIdx();
                } else if (actionList.get(0).getActionTime().after(yesterdayStart)) {
                    signDay = actionList.get(0).getMarkIdx() + 1; // 昨日签到+1
                }
                actionList.forEach(ta -> map.put(ta.getTaskType() + "_" + ta.getMarkIdx(), ta));
            }
            results.put("signDay", signDay);
            results.put("signToday", signToday);
            // 获取用户次日签到订阅状态
            results.put("signWarnState", fastMemberMessagePushService.getMemberSignWarnState(memberId));
        }
        // 查询看视频
        if (taskPO.getSwitchAdv() != null && taskPO.getSwitchAdv() == 1) {
            FastMemberTaskActionPO acParam1 = new FastMemberTaskActionPO();
            acParam1.setMemberId(memberId);
            acParam1.setTaskType(2);
            if (taskPO.getCircleAdv() == 1) {
                acParam1.setCreateTime(todayStart);// 天
            } else if (taskPO.getCircleAdv() == 2) {
                acParam1.setCreateTime(weekStart);// 周
            } else if (taskPO.getCircleAdv() == 3) {
                acParam1.setCreateTime(monthStart);// 月
            }
            List<FastMemberTaskActionPO> actionList = fastMemberTaskActionMapper.querySignList(acParam1);
            if (CollUtil.isNotEmpty(actionList)) {
                actionList.forEach(ta -> map.put(ta.getTaskType() + "_" + ta.getMarkIdx(), ta));
            }
            results.put("advCount", actionList == null ? 0 : Math.min(actionList.size(), 3));// 已看广告数量
        }
        // 新手任务
        if (taskPO.getSwitchNew() != null && taskPO.getSwitchNew() == 1) {
            FastMemberTaskActionPO addicTaskPO = new FastMemberTaskActionPO();
            addicTaskPO.setTaskType(3); // 新手任务
            addicTaskPO.setMarkIdx(3);    // 追剧	
            addicTaskPO.setState(0);
            addTaskAction(sessionVO, addicTaskPO);
            addicTaskPO.setMarkIdx(5);
            addTaskAction(sessionVO, addicTaskPO);
            // if (fastMiniVO.getType() == 1) {
            //     // 新手任务加桌和桌面进入 微小内部已经判断是否完成逻辑
            //     addicTaskPO.setState(1);
            //     addicTaskPO.setMarkIdx(1);
            //     addTaskAction(sessionVO, addicTaskPO);
            //     addicTaskPO.setMarkIdx(2);
            //     addTaskAction(sessionVO, addicTaskPO);
            //     // 微小关注公众号新手任务
            //     addicTaskPO.setMarkIdx(4);
            //     addTaskAction(sessionVO, addicTaskPO);
            // }

            FastMemberTaskActionPO acParam1 = new FastMemberTaskActionPO();
            acParam1.setMemberId(memberId);
            acParam1.setTaskType(3);
            List<FastMemberTaskActionPO> actionList = fastMemberTaskActionMapper.querySignList(acParam1);
            if (CollUtil.isNotEmpty(actionList)) {
                for (FastMemberTaskActionPO ta : actionList) {
                    if (ta.getMarkIdx() == 3 && ta.getState() == 0) {
                        // 登录方式：0、设备ID登录；1、账号密码；2、手机登录；3、邮箱登录；4、google授权登录
                        Integer loginType = sessionVO.getLoginType();
                        if (loginType != null && loginType > 0) {
                            ta.setState(1);
                            ta.setActionTime(nowDate);
                            ta.setUpdateTime(nowDate);
                            fastMemberTaskActionMapper.updateById(ta);
                        }
                    } else if (ta.getMarkIdx() == 5 && ta.getState() == 0) {
                        // 查询是否开启手机消息通知

                    }
                    map.put(ta.getTaskType() + "_" + ta.getMarkIdx(), ta);
                }
            }
            results.put("newList", actionList);// 新手任务完成情况
        }
        // 日常任务
        if (taskPO.getSwitchUsual() != null && taskPO.getSwitchUsual() == 1) {
            // 查询开启的日常任务
            Date actionStart = null;
            if (taskPO.getCircleUsual() == 1) {
                actionStart = todayStart;// 天
            } else if (taskPO.getCircleUsual() == 2) {
                actionStart = weekStart;// 周
            } else if (taskPO.getCircleUsual() == 3) {
                actionStart = monthStart;// 月
            }
            // 去剧场看一看
            FastMemberTaskActionPO recTaskPO = new FastMemberTaskActionPO();
            recTaskPO.setTaskType(4); // 日常任务
            recTaskPO.setMarkIdx(1);    // 追剧	
            recTaskPO.setState(0);
            addTaskAction(sessionVO, recTaskPO);
            // 周期内完成的添加关注数量
            FastMemberTaskActionPO addicTaskPO = new FastMemberTaskActionPO();
            addicTaskPO.setTaskType(4); // 日常任务
            addicTaskPO.setMarkIdx(2);    // 追剧	
            addicTaskPO.setState(0);
            addTaskAction(sessionVO, addicTaskPO);
            // 周期内完成的观看新剧
            FastMemberTaskActionPO dramaTaskPO = new FastMemberTaskActionPO();
            dramaTaskPO.setTaskType(4); // 日常任务
            dramaTaskPO.setMarkIdx(4);    // 追剧	
            dramaTaskPO.setState(0);
            addTaskAction(sessionVO, dramaTaskPO);
            // 去剧单看一部剧
            FastMemberTaskActionPO playlistTaskPO = new FastMemberTaskActionPO();
            playlistTaskPO.setTaskType(4); // 日常任务
            playlistTaskPO.setMarkIdx(5);    // 剧单	
            playlistTaskPO.setState(0);
            addTaskAction(sessionVO, playlistTaskPO);

            // 查询点击去完成的
            FastMemberTaskActionPO acParam1 = new FastMemberTaskActionPO();
            acParam1.setCreateTime(actionStart);// 天
            acParam1.setMemberId(memberId);
            acParam1.setTaskType(4);
            List<FastMemberTaskActionPO> actionList = fastMemberTaskActionMapper.querySignList(acParam1);
            if (CollUtil.isNotEmpty(actionList)) {
                for (FastMemberTaskActionPO ta : actionList) {
                    if (ta.getMarkIdx() == 1 && ta.getState() == 0) {

                    } else if (ta.getMarkIdx() == 2 && ta.getState() == 0) {
                        // 验证添加追剧完成情况
                        FastMemberAddictionPO maParam = new FastMemberAddictionPO();
                        maParam.setMemberId(memberId);
                        maParam.setCreateTime(ta.getCreateTime());
                        Integer countAdd = fastMemberAddictionMapper.queryTaskCount(maParam);
                        countAdd = countAdd > 5 ? 5 : countAdd;
                        if (!ta.getProcessNum().equals(countAdd)) {
                            ta.setProcessNum(countAdd);
                            if (countAdd == 5) {
                                ta.setState(1);
                                ta.setActionTime(nowDate);
                                ta.setUpdateTime(nowDate);
                            }
                            // 数据库更新
                            fastMemberTaskActionMapper.updateById(ta);
                        }
                    } else if (ta.getMarkIdx() == 4 && ta.getState() == 0) {
                        // 验证看新剧完成情况
                        Date beginDate = ta.getCreateTime();// 去完成发起时间
                        // 验证是否完成，查询用户全部阅读记录
                        FastMemberRecentLogPO mrParam = new FastMemberRecentLogPO();
                        mrParam.setLimitExport(2000);
                        mrParam.setMemberId(memberId);
                        List<FastMemberRecentLogPO> mrList = fastMemberRecentLogMapper.query4TaskList(mrParam);
                        Map<Integer, Integer> dramaSecondMap = new HashMap<>();
                        Set<Integer> beforMap = new HashSet<>();
                        Set<Integer> afterMap = new HashSet<>();
                        for (FastMemberRecentLogPO item : mrList) {
                            if (beginDate.before(item.getCreateTime())) {
                                afterMap.add(item.getDramaId());
                            } else {
                                beforMap.add(item.getDramaId());
                            }
                            Integer seconds = dramaSecondMap.get(item.getDramaId());
                            seconds = seconds == null ? 0 : seconds;
                            seconds += item.getPlaySecond();
                            dramaSecondMap.put(item.getDramaId(), seconds);
                        }
                        for (Integer dramaId : afterMap) {
                            if (!beforMap.contains(dramaId)) {
                                // 新剧
                                if (dramaSecondMap.get(dramaId) > 180) {
                                    ta.setState(1);
                                    ta.setActionTime(nowDate);
                                    ta.setUpdateTime(nowDate);
                                    // 更新数据库
                                    fastMemberTaskActionMapper.updateById(ta);
                                }
                            }
                        }
                    } else if (ta.getMarkIdx() == 5 && ta.getState() == 0) {
                        // 验证是否完成，查询用户领了任务那一刻开始，到今天晚上0点全部观看记录
                        FastMemberRecentLogPO mrParam = new FastMemberRecentLogPO();
                        mrParam.setMiniId(miniId);
                        mrParam.setMemberId(memberId);
                        mrParam.setCreateTimeS(ta.getCreateTime());
                        mrParam.setCreateTimeE(DateUtil.endOfDay(ta.getCreateTime()));
                        List<FastMemberRecentLogPO> mrList = fastMemberRecentLogMapper.query4TaskList(mrParam);
                        // 没看过就不管
                        if (CollUtil.isNotEmpty(mrList)) {
                            // 获取应用下配置的所有漫剧ID
                            List<Integer> ids = fastMiniPlaylistDramaService.getDramaIdsByMiniId(miniId);
                            // 逐个比对，观看记录表中只要有一部剧在剧单配置表中，那就说明完成了任务
                            if (mrList.stream().anyMatch(po -> ids.contains(po.getDramaId()))) {
                                ta.setState(1);
                                ta.setActionTime(nowDate);
                                ta.setUpdateTime(nowDate);
                                // 更新数据库
                                fastMemberTaskActionMapper.updateById(ta);
                            }
                        }
                    }
                    map.put(ta.getTaskType() + "_" + ta.getMarkIdx(), ta);
                }
            }
            results.put("usualList", actionList);// 日常任务完成情况
        }
        // 成就任务
        if (taskPO.getSwitchPride() != null && taskPO.getSwitchPride() == 1) {
            // 完成的观看整部剧
            FastMemberTaskActionPO dramaAllTaskPO = new FastMemberTaskActionPO();
            dramaAllTaskPO.setTaskType(5); // 成就任务
            dramaAllTaskPO.setMarkIdx(1);
            dramaAllTaskPO.setState(0);
            addTaskAction(sessionVO, dramaAllTaskPO);
            // 累计看完多部剧
            FastMemberTaskActionPO drama2TaskPO = new FastMemberTaskActionPO();
            drama2TaskPO.setTaskType(5);// 成就任务
            drama2TaskPO.setMarkIdx(2);
            drama2TaskPO.setState(0);
            addTaskAction(sessionVO, drama2TaskPO);

            FastMemberTaskActionPO acParam1 = new FastMemberTaskActionPO();
            acParam1.setMemberId(memberId);
            acParam1.setTaskType(5);
            List<FastMemberTaskActionPO> actionList = fastMemberTaskActionMapper.querySignList(acParam1);

            Map<Integer, Integer> dramaSeriesCountMap; // 所有剧的集数
            List<FastDramaPO> dramaList; // 已经看完的剧
            
            int readAll = 0;// 看完整部剧
            int getFlag = 0;// 是否已经查询
            if (CollUtil.isNotEmpty(actionList)) {
                for (FastMemberTaskActionPO ta : actionList) {
                    if ((ta.getMarkIdx() == 1 || ta.getMarkIdx() == 2) && ta.getState() == 0) {
                        if (getFlag == 0) {
                            // 看完整部剧
                            dramaSeriesCountMap = getDramaSeriesCountMap();
                            // 查询已经看完的剧
                            dramaList = fastDramaMapper.queryTaskList(memberId);
                            for (FastDramaPO drama : dramaList) {
                                // 看过的最大剧集数
                                Integer seriesNumAll = drama.getSeriesNumAll();
                                // 短剧的总集数
                                Integer dramaSeriesCount = dramaSeriesCountMap.get(drama.getId());
                                // 相等，说明看完了
                                if (seriesNumAll.equals(dramaSeriesCount)) {
                                    readAll = readAll + 1;
                                }
                            }
                            getFlag = 1;
                        }
                        if (ta.getMarkIdx() == 1 && readAll > 0) {
                            ta.setState(1);
                            ta.setActionTime(nowDate);
                            ta.setUpdateTime(nowDate);
                            fastMemberTaskActionMapper.updateById(ta);
                        }
                        if (ta.getMarkIdx() == 2 && readAll >= 5) {
                            ta.setState(1);
                            ta.setActionTime(nowDate);
                            ta.setUpdateTime(nowDate);
                            ta.setProcessNum(5);
                            fastMemberTaskActionMapper.updateById(ta);
                        }
                        if (ta.getMarkIdx() == 2) {
                            ta.setProcessNum(Math.min(readAll, 5));
                        }
                    }
                    map.put(ta.getTaskType() + "_" + ta.getMarkIdx(), ta);
                }
            }
            results.put("prideList", actionList);// 成就任务完成情况
        }
        // 查询总可用币
        // FastMemberTaskRechargePO trParam = new FastMemberTaskRechargePO();
        // trParam.setMemberId(memberId);
        // FastMemberTaskRechargePO trPO = fastMemberTaskRechargeMapper.queryMemberCoin(trParam);
        // if (trPO != null) {
        //     results.put("coinRemain", trPO.getCoinRemain());
        // }
        MemberAccountVO accountVO = accountService.queryInfoByRedis(sessionVO.getMemberId());
        if (accountVO != null) {
            results.put("coinRemain", accountVO.getCoinTaskRemain());
        }
        // 将state补到coinList
        for (FastSettingTaskCoinPO po : taskPO.getCoinList()) {
            FastMemberTaskActionPO action = map.get(po.getTaskType() + "_" + po.getMarkIdx());
            po.setActionId(action == null ? 0 : action.getId());
            po.setState(action == null ? 0 : action.getState());
            po.setAwardState(action == null ? 0 : action.getAwardState());
        }
        return ResultVO.success(results);
    }

    // 查询剧的集数
    private Map<Integer, Integer> getDramaSeriesCountMap() {
        Map<Integer, Integer> seriesCountMap = new HashMap<>();
        FastDramaPO dramaParam = new FastDramaPO();
        dramaParam.setDelFlag(0);
        List<FastDramaPO> dramaList = fastDramaMapper.querySimpleList(dramaParam);
        for (FastDramaPO drama : dramaList) {
            seriesCountMap.put(drama.getId(), drama.getSeriesNumAll());
        }
        return seriesCountMap;
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberTaskActionPO queryById(FastMemberTaskActionPO params) {
        return fastMemberTaskActionMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberTaskActionPO queryById(Long id) {
        return fastMemberTaskActionMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberTaskActionPO queryOne(FastMemberTaskActionPO params) {
        return fastMemberTaskActionMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberTaskActionPO> queryList(FastMemberTaskActionPO params) {
        return fastMemberTaskActionMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberTaskActionPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberTaskActionPO> list = fastMemberTaskActionMapper.queryList(params);
        for (FastMemberTaskActionPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberTaskActionPO params) {
        return fastMemberTaskActionMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberTaskActionPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberTaskActionMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberTaskActionPO> list) {
        if (fastMemberTaskActionMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberTaskActionPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberTaskActionMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public String getRemark(Integer taskType, Integer markIdx) {
        String remark = "";
        if (taskType == 1 && markIdx == 1) {
            remark = StaticStr.SIGN_IN_ON_DAY_1;
        } else if (taskType == 1 && markIdx == 2) {
            remark = StaticStr.SIGN_IN_ON_DAY_2;
        } else if (taskType == 1 && markIdx == 3) {
            remark = StaticStr.SIGN_IN_ON_DAY_3;
        } else if (taskType == 1 && markIdx == 4) {
            remark = StaticStr.SIGN_IN_ON_DAY_4;
        } else if (taskType == 1 && markIdx == 5) {
            remark = StaticStr.SIGN_IN_ON_DAY_5;
        } else if (taskType == 1 && markIdx == 6) {
            remark = StaticStr.SIGN_IN_ON_DAY_6;
        } else if (taskType == 1 && markIdx == 7) {
            remark = StaticStr.SIGN_IN_ON_DAY_7;
        } else if (taskType == 2) {
            remark = StaticStr.WATCH_ADS;
        } else if (taskType == 3 && markIdx == 1) {
            remark = StaticStr.SAVE_DESKTOP;
        } else if (taskType == 3 && markIdx == 2) {
            remark = StaticStr.DESKTOP_LAUNCH;
        } else if (taskType == 3 && markIdx == 3) {
            remark = StaticStr.SECURE_LOGIN;
        } else if (taskType == 3 && markIdx == 4) {
            remark = StaticStr.FOLLOW_OFFICIAL_ACCOUNT;
        } else if (taskType == 4 && markIdx == 1) {
            remark = StaticStr.GO_TO_THEATER;
        } else if (taskType == 4 && markIdx == 2) {
            remark = StaticStr.FOLLOW_DRAMA;
        } else if (taskType == 4 && markIdx == 3) {
            remark = StaticStr.INVITE_FRIENDS;
        } else if (taskType == 4 && markIdx == 4) {
            remark = StaticStr.WATCH_NEW_DRAMA;
        } else if (taskType == 4 && markIdx == 5) {
            remark = StaticStr.GO_TO_TOPIC;
        } else if (taskType == 5 && markIdx == 1) {
            remark = StaticStr.WATCH_WHOLE_DRAMA;
        } else if (taskType == 5 && markIdx == 2) {
            remark = StaticStr.WATCHED_MULTIPLE_DRAMAS;
        }
        return remark;
    }
}
