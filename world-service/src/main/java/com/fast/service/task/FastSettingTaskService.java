/*
 * Powered By fast.up
 */
package com.fast.service.task;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.task.FastSettingTaskCoinMapper;
import com.fast.mapper.task.FastSettingTaskMapper;
import com.fast.po.task.FastSettingTaskCoinPO;
import com.fast.po.task.FastSettingTaskPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastSettingTaskService extends BaseService {

    @Autowired
    private FastSettingTaskMapper fastSettingTaskMapper;
    @Autowired
    private FastSettingTaskCoinMapper fastSettingTaskCoinMapper;

    /**
     * 查询小程序福利任务配置（包含金币配置）带缓存
     */
    public FastSettingTaskPO getSettingTaskRedis(Integer miniId) {
        String key = StaticVar.MINI_TASK_DETAIL + miniId;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            // 为空，从数据库查询
            FastSettingTaskPO taskPO = queryDetailByMiniId(miniId);
            if (taskPO == null) {
                res = StaticVar.EMPTY_FLAG;
                RedisUtil.set(key, res, 60 * 60);
            } else {
                res = JsonUtil.toString(taskPO);
                RedisUtil.set(key, res, 60 * 60 * 2);
            }
        }
        if (StaticVar.EMPTY_FLAG.equals(res)) {
            return null;
        } else {
            return JsonUtil.toJavaObject(res, FastSettingTaskPO.class);
        }
    }

    /**
     * 查询小程序福利任务配置（包含金币配置）
     */
    public FastSettingTaskPO queryDetailByMiniId(Integer miniId) {
        FastSettingTaskPO taskPO = fastSettingTaskMapper.queryByMiniId(miniId);
        // 查询关联币
        if (taskPO != null) {
            FastSettingTaskCoinPO coinParam = new FastSettingTaskCoinPO();
            coinParam.setMiniId(taskPO.getMiniId());
            List<FastSettingTaskCoinPO> coinList = fastSettingTaskCoinMapper.queryList(coinParam);
            taskPO.setCoinList(coinList);
        }
        return taskPO;
    }


    /**
     * 通过id查询单个对象
     */
    public FastSettingTaskPO queryById(FastSettingTaskPO params) {
        return fastSettingTaskMapper.queryById(params);
    }

    /**
     * 查询小程序福利任务开关配置信息
     */
    public FastSettingTaskPO queryByMiniIdRedis(Integer miniId) {
        String key = StaticVar.MINI_TASK_ONE + miniId;
        String res = RedisUtil.get(key);
        if (StrUtil.isNotEmpty(res)) {
            return JsonUtil.toJavaObject(res, FastSettingTaskPO.class);
        }
        FastSettingTaskPO taskPO = fastSettingTaskMapper.queryByMiniId(miniId);
        RedisUtil.set(key, JsonUtil.toString(taskPO), 60 * 60 * 2);
        return taskPO;
    }


    /**
     * 通过id查询单个对象
     */
    public FastSettingTaskPO queryById(Integer id) {
        return fastSettingTaskMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSettingTaskPO queryOne(FastSettingTaskPO params) {
        return fastSettingTaskMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastSettingTaskPO> queryList(FastSettingTaskPO params) {
        return fastSettingTaskMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSettingTaskPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastSettingTaskPO> list = fastSettingTaskMapper.queryList(params);
        for (FastSettingTaskPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSettingTaskPO params) {
        return fastSettingTaskMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(SessionVO sessionVO, FastSettingTaskPO taskPO) {
        Date nowTime = DateUtil.getNowDate();
        taskPO.setCreateTime(nowTime);
        taskPO.setCreatorId(sessionVO.getUserId());
        taskPO.setUpdatorId(sessionVO.getUserId());
        taskPO.setUpdateTime(nowTime);
        if (fastSettingTaskMapper.insertSelective(taskPO) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        // 插入关联的K币表
        for (FastSettingTaskCoinPO coinPO : taskPO.getCoinList()) {
            coinPO.setCreatorId(sessionVO.getUserId());
            coinPO.setCreateTime(nowTime);
            coinPO.setUpdatorId(sessionVO.getUserId());
            coinPO.setUpdateTime(nowTime);
            if (fastSettingTaskCoinMapper.insertSelective(coinPO) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastSettingTaskPO> list) {
        if (fastSettingTaskMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(SessionVO sessionVO, FastSettingTaskPO taskPO) {
        Date nowTime = DateUtil.getNowDate();
        taskPO.setUpdatorId(sessionVO.getUserId());
        taskPO.setUpdateTime(nowTime);
        if (fastSettingTaskMapper.updateByMiniId(taskPO) == 0) {
            // 不存在，新增一条
            taskPO.setCreateTime(nowTime);
            taskPO.setCreatorId(sessionVO.getUserId());
            fastSettingTaskMapper.insertSelective(taskPO);
        }
        // 插入关联的K币表
        for (FastSettingTaskCoinPO coinPO : taskPO.getCoinList()) {
            if (coinPO.getId() == null) {
                // 新增
                coinPO.setMiniId(taskPO.getMiniId());
                coinPO.setCreatorId(sessionVO.getUserId());
                coinPO.setCreateTime(nowTime);
                coinPO.setUpdatorId(sessionVO.getUserId());
                coinPO.setUpdateTime(nowTime);
                // 签到任务，默认开启
                if (coinPO.getTaskType() == 1) {
                    coinPO.setSwitchOn(1);
                }
                if (fastSettingTaskCoinMapper.insertSelective(coinPO) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            } else {
                coinPO.setUpdatorId(sessionVO.getUserId());
                coinPO.setUpdateTime(nowTime);
                // 是复制小程序
                if (!Objects.equals(coinPO.getMiniId(), taskPO.getMiniId())) {
                    coinPO.setMiniId(taskPO.getMiniId());
                    FastSettingTaskCoinPO queryCount = new FastSettingTaskCoinPO();
                    queryCount.setMiniId(taskPO.getMiniId());
                    queryCount.setMarkIdx(coinPO.getMarkIdx());
                    queryCount.setTaskType(coinPO.getTaskType());
                    int existsCount = fastSettingTaskCoinMapper.queryCount(queryCount);
                    if (existsCount > 0) {
                        if (fastSettingTaskCoinMapper.updateByMiniIdAndType(coinPO) == 0) {
                            transactionRollBack();
                            return MethodVO.error(StaticStr.ADD_FAILED);
                        }
                    } else {
                        // 新增
                        coinPO.setMiniId(taskPO.getMiniId());
                        coinPO.setCreatorId(sessionVO.getUserId());
                        coinPO.setCreateTime(nowTime);
                        coinPO.setUpdatorId(sessionVO.getUserId());
                        coinPO.setUpdateTime(nowTime);
                        if (fastSettingTaskCoinMapper.insertSelective(coinPO) == 0) {
                            transactionRollBack();
                            return MethodVO.error(StaticStr.ADD_FAILED);
                        }
                    }
                    continue;
                }
                if (fastSettingTaskCoinMapper.updateById(coinPO) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            }
        }
        // 清除缓存
        String key = StaticVar.MINI_TASK_DETAIL + taskPO.getMiniId();
        RedisUtil.del(key);
        String key2 = StaticVar.MINI_TASK_ONE + taskPO.getMiniId();
        RedisUtil.del(key);
        RedisUtil.del(key2);
        return MethodVO.success();
    }
}
