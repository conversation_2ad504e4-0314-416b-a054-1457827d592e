/*
 * Powered By fast.up
 */
package com.fast.service.flock;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.flock.FastFlockMapper;
import com.fast.po.flock.FastFlockPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastFlockService extends BaseService {

    @Autowired
    private FastFlockMapper fastFlockMapper;


    public ResultVO getFlockPass(Integer miniId, Long memberId) {
        List<FastFlockPO> flockList = fastFlockMapper.getFlockPass(miniId);
        Integer showTimes = Integer.MAX_VALUE;
        Integer checkFlockId = 0;
        String flockPass = "";
        String checkKey = "";
        for (FastFlockPO flock : flockList) {
            String key = StaticVar.FLOCK_MEMBER + memberId + "_" + flock.getId();
            String alreadyShowTimesStr = RedisUtil.get(key);
            if (StrUtil.isEmpty(alreadyShowTimesStr)) {
                alreadyShowTimesStr = "0";
            }
            Integer alreadyShowTimes = Integer.valueOf(alreadyShowTimesStr);
            if (alreadyShowTimes < showTimes) {
                // 就这个了
                showTimes = alreadyShowTimes;
                checkFlockId = flock.getId();
                flockPass = flock.getFlockPass();
                checkKey = key;
            }
        }
        if (StrUtil.isNotEmpty(flockPass)) {
            // 更新缓存
            RedisUtil.set(checkKey, String.valueOf(showTimes + 1));
            // 更新曝光次数
            FastFlockPO flockPO = new FastFlockPO();
            flockPO.setId(checkFlockId);
            fastFlockMapper.updateShowTimesById(flockPO);
        }
        Map<String, Object> results = ResultVO.getMap();
        results.put("flockPass", flockPass);
        return ResultVO.success(results);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFlockPO queryById(FastFlockPO item) {
        return fastFlockMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFlockPO queryById(Integer id) {
        return fastFlockMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFlockPO queryOne(FastFlockPO item) {
        return fastFlockMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFlockPO> queryList(FastFlockPO item) {
        return fastFlockMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFlockPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFlockPO> list = fastFlockMapper.queryList(item);
        for (FastFlockPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFlockPO item) {
        return fastFlockMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFlockPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastFlockMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFlockPO> list) {
        if (fastFlockMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFlockPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastFlockMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
