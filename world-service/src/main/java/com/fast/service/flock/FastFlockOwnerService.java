/*
 * Powered By fast.up
 */
package com.fast.service.flock;

import com.fast.constant.StaticStr;
import com.fast.mapper.flock.FastFlockOwnerMapper;
import com.fast.po.flock.FastFlockOwnerPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastFlockOwnerService extends BaseService {

    @Autowired
    private FastFlockOwnerMapper fastFlockOwnerMapper;

    /**
     * 通过id查询单个对象
     */
    public FastFlockOwnerPO queryById(FastFlockOwnerPO item) {
        return fastFlockOwnerMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFlockOwnerPO queryById(Integer id) {
        return fastFlockOwnerMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFlockOwnerPO queryOne(FastFlockOwnerPO item) {
        return fastFlockOwnerMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFlockOwnerPO> queryList(FastFlockOwnerPO item) {
        return fastFlockOwnerMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFlockOwnerPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFlockOwnerPO> list = fastFlockOwnerMapper.queryList(item);
        for (FastFlockOwnerPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFlockOwnerPO item) {
        return fastFlockOwnerMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFlockOwnerPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastFlockOwnerMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFlockOwnerPO> list) {
        if (fastFlockOwnerMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFlockOwnerPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastFlockOwnerMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
