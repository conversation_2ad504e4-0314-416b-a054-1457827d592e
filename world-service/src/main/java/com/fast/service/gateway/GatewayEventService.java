/*
 * Powered By fast.up
 */
package com.fast.service.gateway;

import com.fast.utils.DateUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fast.mapper.gateway.GatewayEventMapper;
import com.fast.po.gateway.GatewayEventPO;
import com.fast.vo.ResultVO;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.service.gateway.GatewayEventService;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.constant.StaticStr;

/**
 * <AUTHOR>
 */
@Service
public class GatewayEventService extends BaseService {

    @Autowired
    private GatewayEventMapper gatewayEventMapper;

    /**
     * 通过id查询单个对象
     */
    public GatewayEventPO queryById(GatewayEventPO params) {
        return gatewayEventMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public GatewayEventPO queryById(Integer id) {
        return gatewayEventMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public GatewayEventPO queryOne(GatewayEventPO params) {
        return gatewayEventMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<GatewayEventPO> queryList(GatewayEventPO params) {
        return gatewayEventMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(GatewayEventPO params, PageVO pageVO) {
        startPage(pageVO);
        List<GatewayEventPO> list = gatewayEventMapper.queryList(params);
        for (GatewayEventPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
		return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(GatewayEventPO params){
        Integer count = gatewayEventMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(GatewayEventPO params)  {
    	Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (gatewayEventMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<GatewayEventPO> list) {
        if(gatewayEventMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        }else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(GatewayEventPO params)  {
		Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (gatewayEventMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(GatewayEventPO params)  {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        GatewayEventPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        gatewayEventMapper.deleteById(po.getId());
        return MethodVO.success();
    }
    
}
