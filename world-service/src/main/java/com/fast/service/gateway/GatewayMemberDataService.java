/*
 * Powered By fast.up
 */
package com.fast.service.gateway;

import com.fast.utils.DateUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fast.mapper.gateway.GatewayMemberDataMapper;
import com.fast.po.gateway.GatewayMemberDataPO;
import com.fast.vo.ResultVO;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.service.gateway.GatewayMemberDataService;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.constant.StaticStr;

/**
 * <AUTHOR>
 */
@Service
public class GatewayMemberDataService extends BaseService {

    @Autowired
    private GatewayMemberDataMapper gatewayMemberDataMapper;

    /**
     * 通过id查询单个对象
     */
    public GatewayMemberDataPO queryById(GatewayMemberDataPO params) {
        return gatewayMemberDataMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public GatewayMemberDataPO queryById(Integer id) {
        return gatewayMemberDataMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public GatewayMemberDataPO queryOne(GatewayMemberDataPO params) {
        return gatewayMemberDataMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<GatewayMemberDataPO> queryList(GatewayMemberDataPO params) {
        return gatewayMemberDataMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(GatewayMemberDataPO params, PageVO pageVO) {
        startPage(pageVO);
        List<GatewayMemberDataPO> list = gatewayMemberDataMapper.queryList(params);
        for (GatewayMemberDataPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
		return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(GatewayMemberDataPO params){
        Integer count = gatewayMemberDataMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(GatewayMemberDataPO params)  {
    	Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (gatewayMemberDataMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<GatewayMemberDataPO> list) {
        if(gatewayMemberDataMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        }else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(GatewayMemberDataPO params)  {
		Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (gatewayMemberDataMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(GatewayMemberDataPO params)  {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        GatewayMemberDataPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        gatewayMemberDataMapper.deleteById(po.getId());
        return MethodVO.success();
    }
    
}
