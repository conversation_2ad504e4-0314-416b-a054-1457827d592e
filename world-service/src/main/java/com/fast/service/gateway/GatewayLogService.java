/*
 * Powered By fast.up
 */
package com.fast.service.gateway;

import com.fast.utils.DateUtil;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fast.mapper.gateway.GatewayLogMapper;
import com.fast.po.gateway.GatewayLogPO;
import com.fast.vo.ResultVO;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.service.gateway.GatewayLogService;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.constant.StaticStr;

/**
 * <AUTHOR>
 */
@Service
public class GatewayLogService extends BaseService {

    @Autowired
    private GatewayLogMapper gatewayLogMapper;

    /**
     * 通过id查询单个对象
     */
    public GatewayLogPO queryById(GatewayLogPO params) {
        return gatewayLogMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public GatewayLogPO queryById(Long id) {
        return gatewayLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public GatewayLogPO queryOne(GatewayLogPO params) {
        return gatewayLogMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<GatewayLogPO> queryList(GatewayLogPO params) {
        return gatewayLogMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(GatewayLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<GatewayLogPO> list = gatewayLogMapper.queryList(params);
        for (GatewayLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
		return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(GatewayLogPO params){
        Integer count = gatewayLogMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(GatewayLogPO params)  {
    	Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (gatewayLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<GatewayLogPO> list) {
        if(gatewayLogMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        }else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(GatewayLogPO params)  {
		Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (gatewayLogMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(GatewayLogPO params)  {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        GatewayLogPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        gatewayLogMapper.deleteById(po.getId());
        return MethodVO.success();
    }
    
}
