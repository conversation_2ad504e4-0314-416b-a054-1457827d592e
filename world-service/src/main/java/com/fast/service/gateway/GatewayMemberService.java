/*
 * Powered By fast.up
 */
package com.fast.service.gateway;

import com.fast.mapper.gateway.GatewayMemberDataMapper;
import com.fast.po.gateway.GatewayMemberDataPO;
import com.fast.utils.DateUtil;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.gateway.LoadingPageEntity;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fast.mapper.gateway.GatewayMemberMapper;
import com.fast.po.gateway.GatewayMemberPO;
import com.fast.vo.ResultVO;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.service.gateway.GatewayMemberService;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.constant.StaticStr;

/**
 * <AUTHOR>
 */
@Service
public class GatewayMemberService extends BaseService {

    @Autowired
    private GatewayMemberMapper gatewayMemberMapper;
    @Autowired
    private GatewayMemberDataMapper gatewayMemberDataMapper;

    /**
     * 通过id查询单个对象
     */
    public GatewayMemberPO queryById(GatewayMemberPO params) {
        return gatewayMemberMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public GatewayMemberPO queryById(Long id) {
        return gatewayMemberMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public GatewayMemberPO queryOne(GatewayMemberPO params) {
        return gatewayMemberMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<GatewayMemberPO> queryList(GatewayMemberPO params) {
        return gatewayMemberMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(GatewayMemberPO params, PageVO pageVO) {
        startPage(pageVO);
        List<GatewayMemberPO> list = gatewayMemberMapper.queryList(params);
        for (GatewayMemberPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
		return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public Integer queryCount(GatewayMemberPO params){
        Integer count = gatewayMemberMapper.queryCount(params);
        return count == null ? 0 : count;
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(GatewayMemberPO params)  {
    	Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (gatewayMemberMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO setGatewayMember(GatewayMemberPO params, LoadingPageEntity gatewayData)  {
        String gatewayDataStr;
        GatewayMemberPO gatewayMemberPO = new GatewayMemberPO();
        gatewayMemberPO.setExternalId(params.getExternalId());
        GatewayMemberPO gatewayMember = gatewayMemberMapper.queryOne(gatewayMemberPO);
        if (gatewayMember != null) {
            gatewayData.setGatewayMemberId(gatewayMember.getId()); // 设置网关用户id
            gatewayDataStr = toJSONString(gatewayData);

            params.setId(gatewayMember.getId());
            if (gatewayMemberMapper.updateById(params) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
            GatewayMemberDataPO gatewayMemberDataPO = new GatewayMemberDataPO();
            gatewayMemberDataPO.setGatewayMemberId(params.getId());
            GatewayMemberDataPO gatewayMemberData = gatewayMemberDataMapper.queryOne(gatewayMemberDataPO);
            if (gatewayMemberData != null) {
                gatewayMemberDataPO.setId(gatewayMemberData.getId());
                gatewayMemberDataPO.setData(gatewayDataStr);
                if (gatewayMemberDataMapper.updateById(gatewayMemberDataPO) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            } else {
                gatewayMemberDataPO.setData(gatewayDataStr);
                if (gatewayMemberDataMapper.insertSelective(gatewayMemberDataPO) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            }
        } else {
            Date nowTime = DateUtil.getNowDate();
            params.setCreateTime(nowTime);
            params.setUpdateTime(nowTime);
            if (gatewayMemberMapper.insertSelective(params) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
            gatewayData.setGatewayMemberId(params.getId()); // 设置网关用户id
            gatewayDataStr = toJSONString(gatewayData);
            GatewayMemberDataPO gatewayMemberDataPO = new GatewayMemberDataPO();
            gatewayMemberDataPO.setGatewayMemberId(params.getId());
            gatewayMemberDataPO.setData(gatewayDataStr);
            gatewayMemberDataPO.setCreateTime(nowTime);
            gatewayMemberDataPO.setUpdateTime(nowTime);
            if (gatewayMemberDataMapper.insertSelective(gatewayMemberDataPO) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
        }


        Map<String, String> data = new HashMap<>();
        data.put("client_ip_address", params.getIp());
        data.put("w2a_data_encrypt", "w2a:" + StrUtil.charCodeAt(gatewayDataStr));
        // log.info(StrUtil.fromCharCode(StrUtil.charCodeAt(JsonUtil.toString(params))));
        return MethodVO.success(data);
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<GatewayMemberPO> list) {
        if(gatewayMemberMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        }else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(GatewayMemberPO params)  {
		Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (gatewayMemberMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(GatewayMemberPO params)  {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        GatewayMemberPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        gatewayMemberMapper.deleteById(po.getId());
        return MethodVO.success();
    }
    
}
