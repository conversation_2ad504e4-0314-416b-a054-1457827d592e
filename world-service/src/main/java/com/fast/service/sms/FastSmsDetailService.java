/*
 * Powered By fast.up
 */
package com.fast.service.sms;

import com.fast.constant.StaticStr;
import com.fast.mapper.sms.FastSmsDetailMapper;
import com.fast.po.sms.FastSmsDetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastSmsDetailService extends BaseService {

    @Autowired
    private FastSmsDetailMapper fastSmsDetailMapper;

    public int insertOneSms(Integer channel, Integer state, String phone, String content, String ip, String ua) {
        FastSmsDetailPO sdPO = new FastSmsDetailPO();
        sdPO.setChannel(channel);
        sdPO.setState(state);
        sdPO.setPhone(phone);
        sdPO.setContent(content);
        Date timeNow = DateUtil.getNowDate();
        sdPO.setCreateTime(timeNow);
        sdPO.setUpdateTime(timeNow);
        sdPO.setIp(ip);
        sdPO.setUa(ua);
        return fastSmsDetailMapper.insertSelective(sdPO);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSmsDetailPO queryById(FastSmsDetailPO params) {
        return fastSmsDetailMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastSmsDetailPO queryById(Integer id) {
        return fastSmsDetailMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastSmsDetailPO queryOne(FastSmsDetailPO params) {
        return fastSmsDetailMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastSmsDetailPO> queryList(FastSmsDetailPO params) {
        return fastSmsDetailMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastSmsDetailPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastSmsDetailPO> list = fastSmsDetailMapper.queryList(params);
        for (FastSmsDetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastSmsDetailPO params) {
        return fastSmsDetailMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastSmsDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastSmsDetailMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastSmsDetailPO> list) {
        if (fastSmsDetailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastSmsDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastSmsDetailMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
