/*
 * Powered By fast.up
 */
package com.fast.service.playlist;

import com.fast.constant.StaticStr;
import com.fast.enums.LanguageEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.playlist.FastMiniPlaylistMapper;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.playlist.FastMiniPlaylistDramaPO;
import com.fast.po.playlist.FastMiniPlaylistI18nPO;
import com.fast.po.playlist.FastMiniPlaylistPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.thread.FastUserContext;
import com.fast.utils.thread.LanguageContext;
import com.fast.utils.thread.SysTypeContext;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniPlaylistService extends BaseService {

    @Autowired
    private FastMiniPlaylistMapper fastMiniPlaylistMapper;

    @Autowired
    private FastMiniPlaylistDramaService fastMiniPlaylistDramaService;

    @Autowired
    private FastMiniPlaylistI18nService fastMiniPlaylistI18nService;

    @Autowired
    private FastDramaService fastDramaService;

    /**
     * 通过id查询单个对象
     */
    public FastMiniPlaylistPO queryById(FastMiniPlaylistPO params) {
        return fastMiniPlaylistMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniPlaylistPO queryById(Integer id, boolean i18n) {
        FastMiniPlaylistPO po = fastMiniPlaylistMapper.queryById(id);
        if (po == null) {
            return null;
        }
        if (i18n) {
            fillI18n(po);
        }
        return po;
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniPlaylistPO queryOne(FastMiniPlaylistPO params) {
        return fastMiniPlaylistMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniPlaylistPO> queryList(FastMiniPlaylistPO params) {
        return fastMiniPlaylistMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniPlaylistPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniPlaylistPO> list = params.getMiniId() != null ? queryListByMiniId(params.getMiniId(), params.getContVersionId(), true) : fastMiniPlaylistMapper.queryList(params);
        for (FastMiniPlaylistPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            // 国际化
            fillI18n(cur);
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniPlaylistPO params) {
        return fastMiniPlaylistMapper.queryCount(params);
    }

    public List<FastMiniPlaylistPO> queryListByMiniId(Integer miniId, Integer contVersionId, boolean injectDrama) {
        if (miniId == null || contVersionId == null) {
            return new ArrayList<>();
        }
        FastMiniPlaylistPO params = new FastMiniPlaylistPO();
        params.setMiniId(miniId);
        params.setContVersionId(contVersionId);
        if (!injectDrama) {
            return queryList(params);
        }
        List<FastMiniPlaylistPO> playlistPOS = queryList(params);
        if (CollUtil.isEmpty(playlistPOS)) {
            return new ArrayList<>();
        }
        List<Integer> plids = playlistPOS.stream().map(FastMiniPlaylistPO::getId).collect(Collectors.toList());
        Map<Integer, List<FastMiniPlaylistDramaPO>> map = fastMiniPlaylistDramaService.queryByPlaylistIds(plids);
        playlistPOS.forEach(po -> {
            List<FastMiniPlaylistDramaPO> list = map.get(po.getId());
            po.setDramaCount(list.size());
            // 加载短剧数据
            if (CollUtil.isNotEmpty(list)) {
                List<Integer> ids = list.stream().map(FastMiniPlaylistDramaPO::getDramaId).collect(Collectors.toList());
                po.setDramaList(fastDramaService.queryInfoByRedis(StringUtils.join(ids, ",")));
            }
            // i18n
            fillI18n(po);
        });
        return playlistPOS;
    }

    public List<Integer> queryIdsByMiniId(Integer miniId, Integer contVersionId) {
        List<FastMiniPlaylistPO> list = queryListByMiniId(miniId, contVersionId, false);
        return list.stream().map(FastMiniPlaylistPO::getId).collect(Collectors.toList());
    }

    public int queryCount(Integer miniId) {
        if (miniId == null) {
            return 0;
        }
        FastMiniPlaylistPO params = new FastMiniPlaylistPO();
        params.setMiniId(miniId);
        return queryCount(params);
    }

    public FastMiniPlaylistPO getDetail(Integer id) {
        FastMiniPlaylistPO fastMiniPlaylist = queryById(id, true);
        if (fastMiniPlaylist == null) {
            return null;
        }
        List<Integer> ids = fastMiniPlaylistDramaService.getDramaIdsByPlaylistId(id);
        // fastMiniPlaylist.setDramaIdList(ids);
        // fastMiniPlaylist.setDramaIds(StringUtils.join(ids, ","));
        if (CollUtil.isNotEmpty(ids)) {
            fastMiniPlaylist.setDramaList(fastDramaService.queryInfoByRedis(StringUtils.join(ids, ",")));
        }
        return fastMiniPlaylist;
    }

    public void fillI18n(FastMiniPlaylistPO item) {
        if (item == null || item.getId() == null) {
            return;
        }
        Map<String, FastMiniPlaylistI18nPO> map = fastMiniPlaylistI18nService.getMapByIdForLang(item.getId());
        if (CollUtil.isEmpty(map)) {
            return;
        }
        if (SysTypeContext.isBackend()) {
            Map<String, String> titleMap = new LinkedHashMap<>();
            // Map<String, String> subTitleMap = new LinkedHashMap<>();
            // Map<String, String> remarkMap = new LinkedHashMap<>();
            // Map<String, String> coverMap = new LinkedHashMap<>();
            map.forEach((k, v) -> {
                titleMap.put(k, v.getTitle());
                // subTitleMap.put(k, v.getSubTitle());
                // remarkMap.put(k, v.getRemark());
                // coverMap.put(k, v.getCover());
            });
            item.setTitles(titleMap);
            // item.setSubTitles(subTitleMap);
            // item.setRemarks(remarkMap);
            // item.setCovers(coverMap);
        }
        if (SysTypeContext.isApp()) {
            String languageType = LanguageContext.getLanguageType();
            FastMiniPlaylistI18nPO i18nPO = map.get(languageType);
            if (i18nPO == null) {
                return;
            }
            item.setTitle(i18nPO.getTitle());
            // item.setSubTitle(i18nPO.getSubTitle());
            // item.setRemark(i18nPO.getRemark());
        }
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniPlaylistPO item) {
        if (CollUtil.isEmpty(item.getI18ns())) {
            return MethodVO.error("国际化内容不能为空");
        }
        // 写一份默认国际化内容
        FastMiniPlaylistI18nPO i18nPO = takeDefaultContent(item);
        item.setTitle(i18nPO.getTitle());
        item.setSubTitle(i18nPO.getSubTitle());
        item.setCover(i18nPO.getCover());
        item.setRemark(i18nPO.getRemark());

        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastMiniPlaylistMapper.insertSelective(item) > 0) {
            syncPlaylistDramas(item, false);
            // 国际化
            item.getI18ns().forEach(i18n -> {
                i18n.setPlaylistId(item.getId());
                i18n.setMiniId(item.getMiniId());
                i18n.setCreateTime(nowTime);
                i18n.setCreatorId(FastUserContext.getUserId());
            });
            fastMiniPlaylistI18nService.insertBatch(item.getI18ns());
        }
        return MethodVO.success();
    }

    private FastMiniPlaylistI18nPO takeDefaultContent(FastMiniPlaylistPO item) {
        FastMiniPlaylistI18nPO defaulted = null;
        for (FastMiniPlaylistI18nPO content : item.getI18ns()) {
            if (Objects.equals(content.getLanguageCode(), LanguageEnum.ENGLISH.getCode())) {
                defaulted = content;
                break;
            }
        }
        if (defaulted == null) {
            throw new MyException("英文是默认兜底版，不允许为空");
        }
        return defaulted;
    }

    /**
     * 同步剧单下的短剧，保存一份关系数据
     *
     * @param params   剧单入参
     * @param isUpdate 是否修改（true/false）
     */
    private void syncPlaylistDramas(FastMiniPlaylistPO params, boolean isUpdate) {
        if (StringUtils.isBlank(params.getDramaIds())) {
            log.info("剧单 {} 没有关联短剧，关系数据不予同步", params.getId());
            return;
        }
        if (isUpdate) {
            fastMiniPlaylistDramaService.deleteByPlaylistId(params.getId());
        }

        String[] split = StringUtils.split(params.getDramaIds(), ",");
        for (int i = 0; i < split.length; i++) {
            FastMiniPlaylistDramaPO playlistDramaPO = new FastMiniPlaylistDramaPO();
            playlistDramaPO.setType(params.getType());
            playlistDramaPO.setMiniId(params.getMiniId());
            playlistDramaPO.setPlaylistId(params.getId());
            playlistDramaPO.setDramaId(Integer.valueOf(split[i]));
            playlistDramaPO.setSeq(i + 1);
            playlistDramaPO.setCreateTime(DateUtil.getNowDate());
            playlistDramaPO.setUpdateTime(DateUtil.getNowDate());
            fastMiniPlaylistDramaService.insert(playlistDramaPO);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniPlaylistPO item) {
        if (item.getId() == null) {
            return MethodVO.error(StaticStr.INVALID_PARAM);
        }
        if (CollUtil.isEmpty(item.getI18ns())) {
            return MethodVO.error("国际化内容不能为空");
        }
        if (item.getSeq() == null) {
            Integer maxSeq = fastMiniPlaylistMapper.getMaxSeqByMiniId(item.getMiniId());
            item.setSeq(maxSeq == null ? 1 : maxSeq + 1);
        }
        // 写一份默认国际化内容
        FastMiniPlaylistI18nPO i18nPO = takeDefaultContent(item);
        item.setTitle(i18nPO.getTitle());
        item.setSubTitle(i18nPO.getSubTitle());
        item.setCover(i18nPO.getCover());
        item.setRemark(i18nPO.getRemark());

        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        item.setUpdatorId(FastUserContext.getUserId());
        if (fastMiniPlaylistMapper.updateById(item) > 0) {
            syncPlaylistDramas(item, true);
            // 国际化
            fastMiniPlaylistI18nService.deleteByPlaylistId(item.getId());
            item.getI18ns().forEach(i18n -> {
                i18n.setPlaylistId(item.getId());
                i18n.setMiniId(item.getMiniId());
                i18n.setCreateTime(nowTime);
                i18n.setCreatorId(FastUserContext.getUserId());
            });
            fastMiniPlaylistI18nService.insertBatch(item.getI18ns());
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(Integer id) {
        if (id == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        int row = fastMiniPlaylistMapper.deleteById(id);
        if (row > 0) {
            fastMiniPlaylistDramaService.deleteByPlaylistId(id);
            fastMiniPlaylistI18nService.deleteByPlaylistId(id);
        }
        return row;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateBatch(FastMiniPO miniPO, List<FastMiniPlaylistPO> playlists, Integer cvid) {
        if (miniPO == null || CollUtil.isEmpty(playlists)) {
            log.info("应用或剧单为空，不进行保存");
            return;
        }
        List<Integer> oldIds = queryIdsByMiniId(miniPO.getId(), miniPO.getContVersionId());
        if (CollUtil.isNotEmpty(oldIds)) {
            oldIds.forEach(this::delete);
        }
        for (int i = 0; i < playlists.size(); i++) {
            FastMiniPlaylistPO playlist = playlists.get(i);
            playlist.setContVersionId(cvid);
            playlist.setMiniId(miniPO.getId());
            playlist.setType(miniPO.getType());
            // playlist.setSeq(i + 1 + maxSeq);
            playlist.setSeq(i + 1);
            insert(playlist);
        }
    }
}
