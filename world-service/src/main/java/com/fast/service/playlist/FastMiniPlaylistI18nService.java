/*
 * Powered By fast.up
 */
package com.fast.service.playlist;

import com.fast.constant.StaticStr;
import com.fast.mapper.playlist.FastMiniPlaylistI18nMapper;
import com.fast.po.playlist.FastMiniPlaylistI18nPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniPlaylistI18nService extends BaseService {

    @Autowired
    private FastMiniPlaylistI18nMapper fastMiniPlaylistI18nMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniPlaylistI18nPO queryById(FastMiniPlaylistI18nPO params) {
        return fastMiniPlaylistI18nMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniPlaylistI18nPO queryById(Integer id) {
        return fastMiniPlaylistI18nMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniPlaylistI18nPO queryOne(FastMiniPlaylistI18nPO params) {
        return fastMiniPlaylistI18nMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniPlaylistI18nPO> queryList(FastMiniPlaylistI18nPO params) {
        return fastMiniPlaylistI18nMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniPlaylistI18nPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniPlaylistI18nPO> list = fastMiniPlaylistI18nMapper.queryList(params);
        for (FastMiniPlaylistI18nPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniPlaylistI18nPO params) {
        return fastMiniPlaylistI18nMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniPlaylistI18nPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMiniPlaylistI18nMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniPlaylistI18nPO> list) {
        if (fastMiniPlaylistI18nMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniPlaylistI18nPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniPlaylistI18nMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastMiniPlaylistI18nPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.INVALID_PARAM);
        }
        FastMiniPlaylistI18nPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        fastMiniPlaylistI18nMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public Map<String, FastMiniPlaylistI18nPO> getMapByIdForLang(Integer id) {
        if (id == null) {
            return null;
        }

        FastMiniPlaylistI18nPO po = new FastMiniPlaylistI18nPO();
        po.setPlaylistId(id);
        List<FastMiniPlaylistI18nPO> list = queryList(po);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().collect(Collectors.toMap(FastMiniPlaylistI18nPO::getLanguageCode, FastMiniPlaylistI18nPO -> FastMiniPlaylistI18nPO, (a, b) -> b, LinkedHashMap::new));
    }

    public int deleteByPlaylistId(Integer playlistId) {
        if (playlistId == null) {
            return 0;
        }
        return fastMiniPlaylistI18nMapper.deleteByPlaylistId(playlistId);
    }
}
