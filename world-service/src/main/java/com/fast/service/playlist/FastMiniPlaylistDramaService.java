/*
 * Powered By fast.up
 */
package com.fast.service.playlist;

import com.fast.constant.StaticStr;
import com.fast.framework.exception.MyException;
import com.fast.mapper.playlist.FastMiniPlaylistDramaMapper;
import com.fast.po.playlist.FastMiniPlaylistDramaPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniPlaylistDramaService extends BaseService {

    @Autowired
    private FastMiniPlaylistDramaMapper fastMiniPlaylistDramaMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniPlaylistDramaPO queryById(FastMiniPlaylistDramaPO params) {
        return fastMiniPlaylistDramaMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniPlaylistDramaPO queryById(Integer id) {
        return fastMiniPlaylistDramaMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniPlaylistDramaPO queryOne(FastMiniPlaylistDramaPO params) {
        return fastMiniPlaylistDramaMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniPlaylistDramaPO> queryList(FastMiniPlaylistDramaPO params) {
        return fastMiniPlaylistDramaMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniPlaylistDramaPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniPlaylistDramaPO> list = fastMiniPlaylistDramaMapper.queryList(params);
        for (FastMiniPlaylistDramaPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniPlaylistDramaPO params) {
        return fastMiniPlaylistDramaMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniPlaylistDramaPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (fastMiniPlaylistDramaMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniPlaylistDramaPO params) {
        params.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniPlaylistDramaMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public int deleteByPlaylistId(Integer playlistId) {
        if (playlistId == null || playlistId <= 0) {
            throw new MyException("invalid playlistId");
        }
        FastMiniPlaylistDramaPO playlistDramaPO = new FastMiniPlaylistDramaPO();
        playlistDramaPO.setPlaylistId(playlistId);
        return fastMiniPlaylistDramaMapper.deleteBy(playlistDramaPO);
    }

    public List<FastMiniPlaylistDramaPO> queryByPlaylistId(Integer playlistId) {
        if (playlistId == null || playlistId <= 0) {
            return new ArrayList<>();
        }
        FastMiniPlaylistDramaPO playlistDramaPO = new FastMiniPlaylistDramaPO();
        playlistDramaPO.setPlaylistId(playlistId);
        return fastMiniPlaylistDramaMapper.queryList(playlistDramaPO);
    }

    public List<Integer> getDramaIdsByPlaylistId(Integer playlistId) {
        if (playlistId == null || playlistId <= 0) {
            return new ArrayList<>();
        }
        List<FastMiniPlaylistDramaPO> list = queryByPlaylistId(playlistId);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(FastMiniPlaylistDramaPO::getDramaId).collect(Collectors.toList());
    }

    public List<FastMiniPlaylistDramaPO> queryByMiniId(Integer miniId) {
        if (miniId == null || miniId <= 0) {
            return new ArrayList<>();
        }
        FastMiniPlaylistDramaPO playlistDramaPO = new FastMiniPlaylistDramaPO();
        playlistDramaPO.setMiniId(miniId);
        return fastMiniPlaylistDramaMapper.queryList(playlistDramaPO);
    }

    public List<Integer> getDramaIdsByMiniId(Integer miniId) {
        if (miniId == null || miniId <= 0) {
            return new ArrayList<>();
        }
        List<FastMiniPlaylistDramaPO> list = queryByMiniId(miniId);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(FastMiniPlaylistDramaPO::getDramaId).collect(Collectors.toList());
    }

    public Map<Integer, List<FastMiniPlaylistDramaPO>> queryByPlaylistIds(Collection<Integer> playlistIds) {
        if (CollUtil.isEmpty(playlistIds)) {
            return new HashMap<>();
        }
        FastMiniPlaylistDramaPO playlistDramaPO = new FastMiniPlaylistDramaPO();
        playlistDramaPO.setPlaylistIds(playlistIds);
        List<FastMiniPlaylistDramaPO> playlistDramaPOS = queryList(playlistDramaPO);
        if (CollUtil.isEmpty(playlistDramaPOS)) {
            return new HashMap<>();
        }
        Map<Integer, List<FastMiniPlaylistDramaPO>> map = new HashMap<>();
        playlistDramaPOS.forEach(cur -> {
            List<FastMiniPlaylistDramaPO> list = map.getOrDefault(cur.getPlaylistId(), new ArrayList<>());
            list.add(cur);
            map.put(cur.getPlaylistId(), list);
        });
        return map;
    }

    public int deleteByMiniId(Integer id) {
        if (id == null) {
            return 0;
        }
        return fastMiniPlaylistDramaMapper.deleteByMiniId(id);
    }
}
