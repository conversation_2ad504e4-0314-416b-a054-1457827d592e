/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniContVersionMapper;
import com.fast.po.mini.FastMiniContVersionPO;
import com.fast.service.base.BaseService;
import com.fast.service.language.FastLanguageService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniContVersionService extends BaseService {

    @Autowired
    private FastMiniContVersionMapper fastMiniContVersionMapper;

    @Autowired
    private FastLanguageService fastLanguageService;

    /**
     * 通过id查询单个对象
     */
    public FastMiniContVersionPO queryById(FastMiniContVersionPO params) {
        FastMiniContVersionPO po = fastMiniContVersionMapper.queryById(params);
        if (po != null) {
            po.setEncryptionId(encode(po.getId()));
            po.setLanguages(fastLanguageService.queryCodeMaps(po.getLanguageCodes()));
        }
        return po;
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniContVersionPO queryById(Integer id) {
        return fastMiniContVersionMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniContVersionPO queryOne(FastMiniContVersionPO params) {
        return fastMiniContVersionMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniContVersionPO> queryList(FastMiniContVersionPO params) {
        return fastMiniContVersionMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniContVersionPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniContVersionPO> list = fastMiniContVersionMapper.queryList(params);
        for (FastMiniContVersionPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setLanguages(fastLanguageService.queryCodeMaps(cur.getLanguageCodes()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniContVersionPO params) {
        return fastMiniContVersionMapper.queryCount(params);
    }

    public FastMiniContVersionPO queryDefaulted(Integer miniId) {
        FastMiniContVersionPO po = new FastMiniContVersionPO();
        po.setMiniId(miniId);
        po.setDefaulted(1);
        return queryOne(po);
    }

    public FastMiniContVersionPO queryByLangCodeNotDefaulted(Integer miniId, String langCode) {
        FastMiniContVersionPO po = new FastMiniContVersionPO();
        // po.setDefaulted(0);
        po.setMiniId(miniId);
        po.setLanguageCodes(langCode);
        return fastMiniContVersionMapper.queryByLangCodeNotDefaulted(po);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniContVersionPO params) {
        if (Objects.equals(params.getDefaulted(), 1) && queryDefaulted(params.getMiniId()) != null) {
            return MethodVO.error("该应用的通用版本已存在");
        }
        String languageCodes = params.getLanguageCodes();
        if (StrUtil.isBlank(languageCodes)) {
            return MethodVO.error("语言编码不能为空");
        }
        String[] langs = StrUtil.splitN(languageCodes, ",");
        for (String lang : langs) {
            if (queryByLangCodeNotDefaulted(params.getMiniId(), lang) != null) {
                return MethodVO.error("一种语言只能绑定一个版本");
            }
        }
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMiniContVersionMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniContVersionPO> list) {
        if (fastMiniContVersionMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniContVersionPO params) {
        if (Objects.equals(params.getDefaulted(), 1)) {
            FastMiniContVersionPO po = queryDefaulted(params.getMiniId());
            if (po != null && !po.getId().equals(params.getId())) {
                return MethodVO.error("该应用的通用版本已存在");
            }
        }
        String languageCodes = params.getLanguageCodes();
        if (StrUtil.isBlank(languageCodes)) {
            return MethodVO.error("语言编码不能为空");
        }
        String[] langs = StrUtil.splitN(languageCodes, ",");
        for (String lang : langs) {
            FastMiniContVersionPO po = queryByLangCodeNotDefaulted(params.getMiniId(), lang);
            if (po != null && !po.getId().equals(params.getId())) {
                return MethodVO.error("一种语言只能绑定一个版本");
            }
        }
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniContVersionMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastMiniContVersionPO params) {
        FastMiniContVersionPO exist = queryById(params.getId());
        if (Objects.equals(exist.getDefaulted(), 1)) {
            return MethodVO.error("通用版本不能删除");
        }
        fastMiniContVersionMapper.deleteById(params.getId());
        return MethodVO.success();
    }

    public List<FastMiniContVersionPO> getByMiniId(Integer miniId) {
        if (miniId == null) {
            return null;
        }
        return fastMiniContVersionMapper.getByMiniId(miniId);
    }

    public List<Integer> getIdByMiniId(Integer miniId) {
        if (miniId == null) {
            return null;
        }
        List<FastMiniContVersionPO> list = fastMiniContVersionMapper.getByMiniId(miniId);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().map(FastMiniContVersionPO::getId).collect(Collectors.toList());
    }
}
