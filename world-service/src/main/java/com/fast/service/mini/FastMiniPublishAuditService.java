/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniPublishAuditMapper;
import com.fast.po.mini.FastMiniPublishAuditPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniPublishAuditService extends BaseService {

    @Autowired
    private FastMiniPublishAuditMapper fastMiniPublishAuditMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniPublishAuditPO queryById(FastMiniPublishAuditPO item) {
        return fastMiniPublishAuditMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniPublishAuditPO queryById(Integer id) {
        FastMiniPublishAuditPO itemParam = new FastMiniPublishAuditPO();
        itemParam.setId(id);
        return fastMiniPublishAuditMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniPublishAuditPO queryOne(FastMiniPublishAuditPO item) {
        return fastMiniPublishAuditMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniPublishAuditPO> queryList(FastMiniPublishAuditPO item) {
        return fastMiniPublishAuditMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniPublishAuditPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniPublishAuditPO> list = fastMiniPublishAuditMapper.queryList(item);
        for (FastMiniPublishAuditPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniPublishAuditPO item) {
        return fastMiniPublishAuditMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniPublishAuditPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastMiniPublishAuditMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniPublishAuditPO> list) {
        if (fastMiniPublishAuditMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniPublishAuditPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniPublishAuditMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
