/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniHisMapper;
import com.fast.po.mini.FastMiniHisPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniHisService extends BaseService {

    @Autowired
    private FastMiniHisMapper fastMiniHisMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniHisPO queryById(FastMiniHisPO params) {
        return fastMiniHisMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniHisPO queryById(Integer id) {
        return fastMiniHisMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniHisPO queryOne(FastMiniHisPO params) {
        return fastMiniHisMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniHisPO> queryList(FastMiniHisPO params) {
        return fastMiniHisMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniHisPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniHisPO> list = fastMiniHisMapper.queryList(params);
        for (FastMiniHisPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniHisPO params) {
        return fastMiniHisMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniHisPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMiniHisMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniHisPO> list) {
        if (fastMiniHisMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniHisPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniHisMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
