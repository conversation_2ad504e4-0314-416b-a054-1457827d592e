/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniDivideMapper;
import com.fast.po.mini.FastMiniDividePO;
import com.fast.service.base.BaseService;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniDivideService extends BaseService {

    @Autowired
    private FastMiniDivideMapper fastMiniDivideMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniDividePO queryById(FastMiniDividePO item) {
        return fastMiniDivideMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniDividePO queryById(Integer id) {
        return fastMiniDivideMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniDividePO queryOne(FastMiniDividePO item) {
        return fastMiniDivideMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniDividePO> queryList(FastMiniDividePO item) {
        return fastMiniDivideMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniDividePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniDividePO> list = fastMiniDivideMapper.queryList(item);
        for (FastMiniDividePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniDividePO item) {
        return fastMiniDivideMapper.queryCount(item);
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniDividePO> list) {
        if (fastMiniDivideMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

}
