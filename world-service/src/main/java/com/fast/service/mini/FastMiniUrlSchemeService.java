/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.mini.FastMiniUrlSchemeMapper;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.mini.FastMiniUrlSchemePO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniUrlSchemeService extends BaseService {

    @Autowired
    private FastMiniUrlSchemeMapper urlSchemeMapper;
    @Autowired
    private FastMiniMapper fastMiniMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniUrlSchemePO queryById(FastMiniUrlSchemePO item) {
        return urlSchemeMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniUrlSchemePO queryById(Integer id) {
        return urlSchemeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniUrlSchemePO queryOne(FastMiniUrlSchemePO item) {
        return urlSchemeMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniUrlSchemePO> queryList(FastMiniUrlSchemePO item) {
        return urlSchemeMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniUrlSchemePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniUrlSchemePO> list = urlSchemeMapper.queryList(item);
        for (FastMiniUrlSchemePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniUrlSchemePO item) {
        return urlSchemeMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoInsert() {
        FastMiniPO queryMini = new FastMiniPO();
        List<FastMiniPO> miniList = fastMiniMapper.queryList(queryMini);
        if (CollUtil.isEmpty(miniList)) {
            return;
        }
        Date date = DateUtil.getYesterdayDate();
        int countDate = DateUtil.format06Int(date);
        for (FastMiniPO mini : miniList) {
            String key = String.format(StaticVar.WECHAT_MINI_URL_SCHEME, mini.getAppId(), countDate);
            FastMiniUrlSchemePO item = new FastMiniUrlSchemePO();
            item.setUrlMakeCount(toInteger(RedisUtil.get(key), 0));
            item.setMiniId(mini.getId());
            item.setCountDate(countDate);
            if (urlSchemeMapper.insertSelective(item) == 0) {
                throw new MyException("统计失败");
            }
        }
    }
}
