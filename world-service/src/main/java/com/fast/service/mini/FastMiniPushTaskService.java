/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniPushTaskMapper;
import com.fast.po.mini.FastMiniPushTaskPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniPushTaskService extends BaseService {

    @Autowired
    private FastMiniPushTaskMapper fastMiniPushTaskMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniPushTaskPO queryById(FastMiniPushTaskPO item) {
        return fastMiniPushTaskMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniPushTaskPO queryById(Integer id) {
        return fastMiniPushTaskMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniPushTaskPO queryOne(FastMiniPushTaskPO item) {
        return fastMiniPushTaskMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniPushTaskPO> queryList(FastMiniPushTaskPO item) {
        return fastMiniPushTaskMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniPushTaskPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniPushTaskPO> list = fastMiniPushTaskMapper.queryList(item);
        for (FastMiniPushTaskPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getReceiveCounts() == 0) {
                cur.setClickRate(BigDecimal.valueOf(0.00D));
            } else {
                Double rate = Double.valueOf(cur.getClickCounts() * 100) / cur.getReceiveCounts();
                cur.setClickRate(BigDecimal.valueOf(rate).setScale(2, RoundingMode.HALF_UP));
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniPushTaskPO item) {
        return fastMiniPushTaskMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniPushTaskPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastMiniPushTaskMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniPushTaskPO> list) {
        if (fastMiniPushTaskMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniPushTaskPO item) {
        FastMiniPushTaskPO ptPO = fastMiniPushTaskMapper.queryById(item.getId());
        if (ptPO.getState() != 1) {
            return MethodVO.error("已发送，不能修改");
        }
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMiniPushTaskMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Async
    public MethodVO updateState(FastMiniPushTaskPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMiniPushTaskMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
