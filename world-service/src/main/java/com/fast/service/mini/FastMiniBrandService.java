/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniBrandMapper;
import com.fast.po.mini.FastMiniBrandPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniBrandService extends BaseService {

    @Autowired
    private FastMiniBrandMapper fastMiniBrandMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniBrandPO queryById(FastMiniBrandPO params) {
        return fastMiniBrandMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniBrandPO queryById(Integer id) {
        return fastMiniBrandMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniBrandPO queryOne(FastMiniBrandPO params) {
        return fastMiniBrandMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniBrandPO> queryList(FastMiniBrandPO params) {
        return fastMiniBrandMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniBrandPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniBrandPO> list = fastMiniBrandMapper.queryList(params);
        for (FastMiniBrandPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniBrandPO params) {
        return fastMiniBrandMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniBrandPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMiniBrandMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniBrandPO> list) {
        if (fastMiniBrandMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniBrandPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniBrandMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
