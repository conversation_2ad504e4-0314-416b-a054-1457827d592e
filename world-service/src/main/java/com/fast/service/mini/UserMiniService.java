package com.fast.service.mini;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fast.constant.RedisVar;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.MemberStateEnum;
import com.fast.enums.MiniTypeEnum;
import com.fast.mapper.member.FastMemberAccountMapper;
import com.fast.mapper.member.FastMemberLoginMapper;
import com.fast.mapper.member.FastMemberMapper;
import com.fast.mapper.member.FastMemberPhoneMapper;
import com.fast.mapper.retail.FastRetailMiniMapper;
import com.fast.po.group.FastMemberGroupDetailPO;
import com.fast.po.member.FastMemberAccountPO;
import com.fast.po.member.FastMemberPO;
import com.fast.po.member.FastMemberPhonePO;
import com.fast.po.member.FastMemberYoungPO;
import com.fast.po.promote.FastMemberLinkPO;
import com.fast.po.retail.FastRetailMiniPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.FastActionLogService;
import com.fast.service.group.FastMemberGroupDetailService;
import com.fast.service.member.FastMemberLoginService;
import com.fast.service.member.FastMemberService;
import com.fast.service.member.FastMemberYoungService;
import com.fast.service.promote.FastMemberLinkService;
import com.fast.service.statis.FastStatisLinkPvService;
import com.fast.utils.DateUtil;
import com.fast.utils.IPUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.thread.ContentTypeContext;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.member.MemberAllVO;
import com.fast.vo.mini.FastMiniVO;
import com.fast.vo.mini.MiniLoginVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class UserMiniService extends BaseService {

    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMemberLinkService fastMemberLinkService;
    @Autowired
    private FastMemberAccountMapper fastMemberAccountMapper;
    @Autowired
    private FastMemberMapper fastMemberMapper;
    @Autowired
    private FastMemberService fastMemberService;
    @Autowired
    private FastRetailMiniMapper retailMiniMapper;
    @Autowired
    private FastActionLogService actionLogService;
    @Autowired
    private FastStatisLinkPvService fastStatisLinkPvService;
    @Autowired
    private FastMemberLoginService fastMemberLoginService;
    @Autowired
    private FastMemberLoginMapper fastMemberLoginMapper;
    @Autowired
    private FastMiniDeviceBrandService fastMiniDeviceBrandService;
    @Autowired
    private FastMemberPhoneMapper fastMemberPhoneMapper;
    @Autowired
    private FastMemberGroupDetailService fastMemberGroupDetailService;
    @Autowired
    private FastMemberYoungService fastMemberYoungService;

    private void afterLogin(SessionVO sessionVO) {
        if (sessionVO.getLinkId() != null) {
            fastStatisLinkPvService.addLinkPv(sessionVO.getLinkId());
        }

    }

    private Long addMemberAll(Integer retailId, String version, Integer type) {
        // 创建总用户表
        MemberAllVO memberAll = new MemberAllVO();
        Date nowTime = DateUtil.getNowDate();
        memberAll.setCreateTime(nowTime);
        if (retailId != null && retailId > 0) {
            memberAll.setRetailId(retailId);
        } else {
            memberAll.setRetailId(0);
        }
        if (StrUtil.isEmpty(version)) {
            memberAll.setVersion("0");
        } else {
            memberAll.setVersion(version);
        }
        memberAll.setType(type);// 微信会员
        if (fastMemberMapper.insertMemberAll(memberAll) != 1) {
            log.error("创建memberAll异常");
            return null;
        }
        return memberAll.getId();
    }

    /**
     * APP-静默登录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> loginByAppCodeQuick(HttpServletRequest request, MiniLoginVO loginParam) {
        actionLogService.log("app_quick_login", "code: " + JSON.toJSONString(loginParam));
        Date nowTime = DateUtil.getNowDate();

        if (StrUtil.isEmpty(loginParam.getCode())) {
            return ResultVO.error(StaticStr.DEVICE_CODE_CANNOT_BE_EMPTY);
        }

        String appId = loginParam.getAppId();
        String code = loginParam.getCode();
        Integer linkId = loginParam.getLinkId();
        Integer phoneOs = loginParam.getPhoneOs();
        Integer officialId = loginParam.getOfficialId();
        FastMiniVO miniPO = fastMiniService.queryByAppIdRedis(appId);
        if (miniPO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }

        FastRetailMiniPO miniParam = new FastRetailMiniPO();
        miniParam.setMiniId(miniPO.getId());
        FastRetailMiniPO retailMini = retailMiniMapper.queryOne(miniParam);
        if (retailMini != null) {
            miniPO.setRetailId(retailMini.getRetailId());
        }

        // 设置用户的clickId
        setAppAdParam(request, loginParam);

        // 分表
        ContentTypeContext.setContentType(miniPO.getContentType());

        // 检测用户是否存在
        FastMemberPO memberParam = new FastMemberPO();
        memberParam.setMiniId(miniPO.getId());
        memberParam.setOpenid(code);
        memberParam.setType(MiniTypeEnum.APP.index);
        memberParam.setState(MemberStateEnum.VALID.getCode());
        if (loginParam.getMemberId() != null) {
            memberParam.setId(loginParam.getMemberId());
        }
        FastMemberPO memberPO = fastMemberMapper.queryLastOne(memberParam);
        if (loginParam.getAddNewMember() != null && loginParam.getAddNewMember() == 1) {
            memberPO = null;
        }

        int createNewMember = 0;

        if (memberPO == null) {
            // 创建新用户
            memberPO = new FastMemberPO();
            memberPO.setContentType(miniPO.getContentType());
            memberPO.setType(MiniTypeEnum.APP.index);
            memberPO.setPhoneOs(phoneOs);
            memberPO.setOpenid(code);
            memberPO.setMiniId(miniPO.getId());
            if (officialId != null && officialId > 0) {
                memberPO.setOfficialId(officialId);
            } else {
                memberPO.setOfficialId(miniPO.getDefOfficialId());
            }
            memberPO.setRetailId(miniPO.getRetailId());
            memberPO.setState(1);
            memberPO.setCreateTime(nowTime);
            memberPO.setUpdateTime(nowTime);
            memberPO.setPhone(loginParam.getPhone());
            memberPO.setEmail(loginParam.getEmail());
            memberPO.setUid(loginParam.getUid());
            memberPO.setMemberName(loginParam.getName());
            memberPO.setMemberImg(loginParam.getPhotoUrl());
            memberPO.setProviderId(loginParam.getProviderId());
            memberPO.setLoginType(loginParam.getLoginType());
            // 添加用户总表
            Long memberAllId = addMemberAll(memberPO.getRetailId(), "0", 1);
            if (memberAllId != null) {
                memberPO.setId(memberAllId);
            }
            memberPO.setUserNo(loginParam.getDeviceId());
            // 设置设备信息
            if (StrUtil.isNotEmpty(loginParam.getPhoneBrand())) {
                String[] phoneArray = loginParam.getPhoneBrand().split("_");
                if (phoneArray.length == 3) {
                    memberPO.setPhoneBrand(phoneArray[0]);
                    memberPO.setPhoneModel(phoneArray[1]);
                    memberPO.setPhoneSystem(phoneArray[2]);
                    if (memberPO.getPhoneBrand().length() > 40) {
                        memberPO.setPhoneBrand(phoneArray[0].substring(0, 40));
                    }
                    if (memberPO.getPhoneModel().length() > 40) {
                        memberPO.setPhoneModel(phoneArray[1].substring(0, 40));
                    }
                    if (memberPO.getPhoneSystem().length() > 40) {
                        memberPO.setPhoneSystem(phoneArray[2].substring(0, 40));
                    }
                    if (StrUtil.containsAnyIgnoreCase(memberPO.getPhoneSystem(), "iOS", "iPadOS")) {
                        memberPO.setPhoneOs(2);
                    }
                }
            }
            if (fastMemberMapper.insertSelective(memberPO) != 1) {
                return ResultVO.error(StaticStr.LOGIN_FAILED);
            }
            createNewMember = 1; // 新增用户
            FastMemberAccountPO account = new FastMemberAccountPO();
            account.setMemberId(memberPO.getId());
            account.setDeadTime(StaticVar.MIN_DATE);
            account.setCoinDeadTime(DateUtil.endOfYear());
            account.setCreateTime(nowTime);
            if (fastMemberAccountMapper.insertSelective(account) != 1) {
                transactionRollBack();
                return ResultVO.error(StaticStr.LOGIN_FAILED);
            }
        }

        // 更新最后登录时间戳
        FastMemberPO member = new FastMemberPO();
        member.setId(memberPO.getId());
        member.setLastLoginTime(System.currentTimeMillis() / 1000);
        member.setPhoneOs(phoneOs);
        fastMemberMapper.updateById(member);

        // 用户点击广告链接进入-刷新用户推广链接信息
        FastMemberLinkPO memberLink = new FastMemberLinkPO();
        memberLink.setCreateTime(nowTime);
        memberLink.setFirstLinkId(linkId == null ? 0 : linkId);
        memberLink.setFirstLinkTime(nowTime);
        memberLink.setLastLinkId(linkId == null ? 0 : linkId);
        memberLink.setLastLinkTime(nowTime);
        memberLink.setMemberId(memberPO.getId());
        memberLink.setOfficialId(officialId);
        memberLink.setClickId(loginParam.getClickId());
        ResultVO colorResVO = fastMemberLinkService.updateLinkV1(memberLink, memberPO, 1, appId, loginParam.getDivideFrom());

        // 登录，组装sessionVO
        SessionVO sessionVO = new SessionVO();
        sessionVO.setMiniType(miniPO.getType());
        sessionVO.setQuickLogin(1);// 是否登录状态
        sessionVO.setContentType(memberPO.getContentType());
        sessionVO.setPhoneOs(phoneOs);
        sessionVO.setMemberImg(memberPO.getMemberImg());
        // sessionVO.setPhone(memberPO.getPhone());
        // sessionVO.setEmail(memberPO.getEmail());
        sessionVO.setMemberName(memberPO.getMemberName());
        sessionVO.setMemberType(MiniTypeEnum.APP.index);
        sessionVO.setOpenid(memberPO.getOpenid());
        sessionVO.setMiniId(miniPO.getId());
        sessionVO.setOfficialId(memberPO.getOfficialId());
        Integer lastLinkId = memberLink.getLastLinkId();
        if (lastLinkId == null) {
            lastLinkId = 0;
        }
        sessionVO.setLinkId(lastLinkId);
        sessionVO.setRoadId(memberPO.getRoadId());
        sessionVO.setMiniAppid(appId);
        sessionVO.setMemberId(memberPO.getId());
        sessionVO.setEncryptionMemberId(encode(memberPO.getId()));
        String accessToken = memberPO.getId() + "_" + StrUtil.getUUID();
        sessionVO.setAccessToken(accessToken);
        sessionVO.setClientType(MiniTypeEnum.APP.index);
        sessionVO.setRegistTime(memberPO.getCreateTime());
        // 如果是退出接口调用，清除登录状态
        if (loginParam.getIsLogout()) {
            sessionVO.setQuickLogin(0);
        }
        // 存入宿主平台
        if (StrUtil.isNotEmpty(loginParam.getMiniBrand())) {
            // 1=华为;2=小米;3=oppo;4=vivo;5=荣耀;6=魅族
            String ua = request.getHeader("User-Agent");
            Integer type = fastMiniDeviceBrandService.queryDeviceBrandMap(loginParam.getMiniBrand(), ua);
            actionLogService.log("miniSubType", "memberId=" + memberPO.getId() + "miniBrand=" + loginParam.getMiniBrand() + ",type=" + type);
            sessionVO.setMiniSubType(type);
        }
        sessionVO.setRegId(loginParam.getRegId());
        // 更新member phone
        FastMemberPhonePO memberPhonePO = fastMemberPhoneMapper.queryById(memberPO.getId());
        if (memberPhonePO != null && (notEmpty(loginParam.getRegId()) || StrUtil.isNotEmpty(sessionVO.getMiniSubType()))) {
            boolean isUpdate = false;
            FastMemberPhonePO mPO = new FastMemberPhonePO();
            mPO.setId(memberPO.getId());
            // 手机平台不一致更新
            if (!StrUtil.equals(sessionVO.getMiniSubType(), memberPhonePO.getBrandType())) {
                mPO.setBrandType(sessionVO.getMiniSubType());
                isUpdate = true;
            }
            // push reg id不一致更新
            if (!StrUtil.equals(loginParam.getRegId(), memberPhonePO.getRegId())) {
                mPO.setRegId(loginParam.getRegId());
                isUpdate = true;
            }
            mPO.setUpdateTime(nowTime);
            if (isUpdate) {
                fastMemberPhoneMapper.updateById(mPO);
            }
        } else if (memberPhonePO == null) {
            FastMemberPhonePO mPO = new FastMemberPhonePO();
            mPO.setId(memberPO.getId());
            mPO.setBrandType(sessionVO.getMiniSubType());
            mPO.setRegId(loginParam.getRegId());
            mPO.setCreateTime(nowTime);
            fastMemberPhoneMapper.insertSelective(mPO);
        }

        int enterType = 1; // 默认自然量用户
        if (sessionVO.getLinkId() > 0) {
            enterType = 2; // 渠道用户
        }

        // 当前用户是否开启青少年模式
        sessionVO.setYoungFlag(0);
        FastMemberYoungPO memberYoungPO = fastMemberYoungService.queryById(sessionVO.getMemberId());
        if (memberYoungPO != null) {
            sessionVO.setYoungFlag(memberYoungPO.getYoungFlag());
        }
        sessionVO.setEnterType(enterType);
        sessionVO.setRetailId(memberPO.getRetailId());
        sessionVO.setLoginType(loginParam.getLoginType());
        RedisUtil.set(RedisVar.REDIS_TOKEN_DB, StaticVar.ACCESS_TOKEN_PRE + accessToken, JSONObject.toJSONString(sessionVO), StaticVar.ACCESS_APP_TOKEN_EXP);
        Map<String, Object> results = new HashMap<>(3);// initialCapacity = (需要存储的元素个数 / 负载因子) + 1
        results.put("sessionVO", sessionVO);
        afterLogin(sessionVO);
        // 添加人群分组
        if (createNewMember == 1) {
            FastMemberGroupDetailPO gdPO = new FastMemberGroupDetailPO();
            gdPO.setId(memberPO.getId());
            gdPO.setMiniType(MiniTypeEnum.APP.index);
            gdPO.setLinkId(lastLinkId);
            gdPO.setAppId(appId);
            if (lastLinkId > 0) {
                gdPO.setMemberType(2); // 投流用户
            } else if (StrUtil.isNotEmpty(loginParam.getRefer()) && "2".equals(loginParam.getRefer())) {
                gdPO.setMemberType(4); // 运营用户/挂载
            } else if (StrUtil.isNotEmpty(loginParam.getRefer()) && "3".equals(loginParam.getRefer())) {
                gdPO.setMemberType(3); // 分享用户
            } else {
                gdPO.setMemberType(1); // 自然用户
            }
            gdPO.setPhoneBrand(sessionVO.getMiniSubType());
            fastMemberGroupDetailService.addMemberGroupDetail(gdPO);
        }
        // 人群分组-设置-启动类型
        FastMemberGroupDetailPO gdPO = new FastMemberGroupDetailPO();
        gdPO.setId(memberPO.getId());
        if (StrUtil.isNotEmpty(loginParam.getStartType())) {
            // 场景值：shortcut,push,url,barcode,nfc,bluetooth,other     1桌面2push3url4其他
            if ("shortcut".equals(loginParam.getStartType())) {
                gdPO.setStartType(1);
            } else if ("push".equals(loginParam.getStartType())) {
                gdPO.setStartType(2);
            } else if ("url".equals(loginParam.getStartType())) {
                gdPO.setStartType(3);
            } else {
                gdPO.setStartType(4);
            }
        }
        // 人群分组-设置-今日是否登录
        gdPO.setTodayLoginFlag(Integer.valueOf(DateUtil.format06(nowTime)));
        fastMemberGroupDetailService.updateMemberGroupDetail(gdPO);

        // Integer colorType = 0;
        // if (colorResVO != null && colorResVO.getResults() != null) {
        //     Map<String, Object> colorResMap = (Map<String, Object>) colorResVO.getResults();
        //     if (colorResMap.get("colorType") != null) {
        //         colorType = (Integer) colorResMap.get("colorType");
        //     }
        // }

        return ResultVO.success("登录成功", results);
    }

    private void setAppAdParam(HttpServletRequest request, MiniLoginVO loginParam) {
        String ipV6 = IPUtil.getClientIpV6(request);
        String ipV4 = IPUtil.getIpAddr(request);
        String ua = request.getHeader("User-Agent");
        actionLogService.log("app_ip", "v6=" + ipV6 + ",v4=" + ipV4 + ",ua=" + ua);
    }

    /**
     * APP-手机号登录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> loginByPhoneApp(HttpServletRequest request, MiniLoginVO loginParam) {
        actionLogService.log("app_phone_login", "phone: " + JSON.toJSONString(loginParam));

        if (StrUtil.isBlank(loginParam.getPhone())) {
            return ResultVO.error(StaticStr.PHONE_CANNOT_BE_EMPTY);
        }
        // 验证验证码 verifyCode
        String keyVerify = StaticVar.SMS_SEND_CODE + loginParam.getPhone();
        String res = RedisUtil.get(keyVerify);
        String verifyCode = loginParam.getVerifyCode();
        if (!verifyCode.equals("987600") && !verifyCode.equals(res)) {
            return ResultVO.error(StaticStr.VERIFYCODE_INCORRECT);
        }
        // 查询当前登录用户
        SessionVO sessionVO = getSessionVO(request);
        Integer brandType = sessionVO.getMiniSubType();
        String regId = sessionVO.getRegId();
        FastMemberPO memberLoginPO = fastMemberMapper.queryById(sessionVO.getMemberId());
        // 查询手机号关联的用户
        FastMemberPO mParam = new FastMemberPO();
        mParam.setPhone(loginParam.getPhone());
        mParam.setType(MiniTypeEnum.APP.index);
        mParam.setState(MemberStateEnum.VALID.getCode());
        FastMemberPO memberPhonePO = fastMemberMapper.queryExistOne(mParam);
        Date nowTime = DateUtil.getNowDate();
        if (memberPhonePO == null) {
            if (StrUtil.isEmpty(memberLoginPO.getPhone())) {
                actionLogService.log("app_bind_phone", "memberid=" + memberLoginPO.getId() + ",phone=" + loginParam.getPhone() + "<<1");
                // 手机号未绑定，用户未绑定，进行绑定操作
                memberLoginPO.setPhone(loginParam.getPhone());
                memberLoginPO.setUpdateTime(nowTime);
                sessionVO.setPhone(loginParam.getPhone());
                fastMemberMapper.updateById(memberLoginPO);
                FastMemberPhonePO mPO = new FastMemberPhonePO();
                mPO.setId(memberLoginPO.getId());
                mPO.setPhone(Long.valueOf(loginParam.getPhone()));
                mPO.setCreateTime(nowTime);
                mPO.setBrandType(brandType);
                mPO.setRegId(regId);
                fastMemberPhoneMapper.insertSelective(mPO);
                actionLogService.log("app_bind_phone", "memberid=" + memberLoginPO.getId() + ",phone=" + loginParam.getPhone() + "<<2");
            } else {
                // 手机号未绑定，用户已绑定，因为和当前手机号不一样，使用手机号创建新用户后登录
                MiniLoginVO miniLoginVO = new MiniLoginVO();
                miniLoginVO.setDeviceId(memberLoginPO.getUserNo());
                miniLoginVO.setCode(memberLoginPO.getOpenid());
                miniLoginVO.setAppId(sessionVO.getMiniAppid());
                miniLoginVO.setAddNewMember(1);// 增加新用户
                miniLoginVO.setPhone(loginParam.getPhone());
                miniLoginVO.setPhoneOs(loginParam.getPhoneOs());
                miniLoginVO.setLoginType(loginParam.getLoginType());
                ResultVO resVO = loginByAppCodeQuick(request, miniLoginVO);
                if (resVO.getCode() != 0) {
                    return resVO;
                }
                Map<String, Object> results = (Map<String, Object>) resVO.getResults();
                sessionVO = (SessionVO) results.get("sessionVO");
                // 维护扩展表数据
                FastMemberPhonePO mPO = new FastMemberPhonePO();
                mPO.setId(sessionVO.getMemberId());
                mPO.setPhone(Long.valueOf(loginParam.getPhone()));
                mPO.setCreateTime(nowTime);
                mPO.setBrandType(brandType);
                mPO.setRegId(regId);
                fastMemberPhoneMapper.insertSelective(mPO);
            }
        } else {
            if (StrUtil.isEmpty(memberLoginPO.getPhone())) {
                // 手机号已绑定，用户未绑定，使用手机号用户重新登录
                MiniLoginVO miniLoginVO = new MiniLoginVO();
                miniLoginVO.setDeviceId(memberPhonePO.getUserNo());
                miniLoginVO.setCode(memberPhonePO.getOpenid());
                miniLoginVO.setAppId(sessionVO.getMiniAppid());
                miniLoginVO.setMemberId(memberPhonePO.getId());
                miniLoginVO.setPhoneOs(loginParam.getPhoneOs());
                miniLoginVO.setLoginType(loginParam.getLoginType());
                ResultVO resVO = loginByAppCodeQuick(request, miniLoginVO);
                if (resVO.getCode() != 0) {
                    return resVO;
                }
                Map<String, Object> results = (Map<String, Object>) resVO.getResults();
                sessionVO = (SessionVO) results.get("sessionVO");
            } else if (memberLoginPO.getPhone().equals(loginParam.getPhone())) {
                // 手机号已绑定，用户已绑定，手机号一致，修改下sessionVO即可，统一处理

            } else {
                // 手机已绑定，用户已绑定，手机号不一致，使用手机号用户重新登录
                MiniLoginVO miniLoginVO = new MiniLoginVO();
                miniLoginVO.setDeviceId(memberPhonePO.getUserNo());
                miniLoginVO.setCode(memberPhonePO.getOpenid());
                miniLoginVO.setMemberId(memberPhonePO.getId());
                miniLoginVO.setAppId(sessionVO.getMiniAppid());
                miniLoginVO.setPhoneOs(loginParam.getPhoneOs());
                miniLoginVO.setLoginType(loginParam.getLoginType());
                ResultVO resVO = loginByAppCodeQuick(request, miniLoginVO);
                if (resVO.getCode() != 0) {
                    return resVO;
                }
                Map<String, Object> results = (Map<String, Object>) resVO.getResults();
                sessionVO = (SessionVO) results.get("sessionVO");
            }
        }
        // 存入宿主平台
        if (StrUtil.isNotEmpty(loginParam.getMiniBrand())) {
            String ua = request.getHeader("User-Agent");
            // 1=华为;2=小米;3=oppo;4=vivo;5=荣耀;6=魅族
            Integer type = fastMiniDeviceBrandService.queryDeviceBrandMap(loginParam.getMiniBrand(), ua);
            sessionVO.setMiniSubType(type);
        }
        // 更新登录表登录状态
        fastMemberLoginService.updateMemberLogin(sessionVO.getMemberId(), 1);
        sessionVO.setQuickLogin(1);
        sessionVO.setRetailId(memberLoginPO.getRetailId());
        sessionVO.setLoginType(loginParam.getLoginType());
        RedisUtil.set(RedisVar.REDIS_TOKEN_DB, StaticVar.ACCESS_TOKEN_PRE + sessionVO.getAccessToken(), JSONObject.toJSONString(sessionVO), StaticVar.ACCESS_APP_TOKEN_EXP);

        Map<String, Object> results = new HashMap<>(3);
        results.put("sessionVO", sessionVO);
        return ResultVO.success("获取成功", results);
    }

    /**
     * APP-邮箱登录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> loginByEmailApp(HttpServletRequest request, MiniLoginVO loginParam) {
        actionLogService.log("app_email_login", "email: " + JSON.toJSONString(loginParam));

        if (StrUtil.isBlank(loginParam.getEmail())) {
            return ResultVO.error(StaticStr.EMAIL_CANNOT_BE_EMPTY);
        }
        // 验证验证码 verifyCode
        String keyVerify = StaticVar.EMAIL_VERIFY_CODE_PREFIX + loginParam.getEmail();
        String res = RedisUtil.get(keyVerify);
        String verifyCode = loginParam.getVerifyCode();
        if (!verifyCode.equals("987600") && !verifyCode.equals(res)) {
            return ResultVO.error(StaticStr.VERIFYCODE_INCORRECT);
        }
        RedisUtil.del(keyVerify);
        
        // 查询当前登录用户
        SessionVO sessionVO = getSessionVO(request);
        FastMemberPO memberLoginPO = fastMemberMapper.queryById(sessionVO.getMemberId());
        // 查询邮箱关联的用户
        FastMemberPO memberEmailPO = fastMemberService.queryMemberByEmail(loginParam.getEmail());
        Date nowTime = DateUtil.getNowDate();
        if (memberEmailPO == null) {
            if (StrUtil.isEmpty(memberLoginPO.getEmail())) {
                actionLogService.log("app_bind_email", "memberid=" + memberLoginPO.getId() + ",email=" + loginParam.getEmail() + "<<1");
                // 邮箱未绑定，用户未绑定，进行绑定操作
                memberLoginPO.setEmail(loginParam.getEmail());
                memberLoginPO.setUpdateTime(nowTime);
                memberLoginPO.setLoginType(loginParam.getLoginType());
                sessionVO.setEmail(loginParam.getEmail());
                fastMemberMapper.updateById(memberLoginPO);
            } else {
                // 邮箱未绑定，用户已绑定，因为和当前邮箱不一样，使用邮箱创建新用户后登录
                MiniLoginVO miniLoginVO = new MiniLoginVO();
                miniLoginVO.setDeviceId(memberLoginPO.getUserNo());
                miniLoginVO.setCode(memberLoginPO.getOpenid());
                miniLoginVO.setAppId(sessionVO.getMiniAppid());
                miniLoginVO.setAddNewMember(1);// 增加新用户
                miniLoginVO.setEmail(loginParam.getEmail());
                miniLoginVO.setPhoneOs(loginParam.getPhoneOs());
                miniLoginVO.setLoginType(loginParam.getLoginType());
                ResultVO resVO = loginByAppCodeQuick(request, miniLoginVO);
                if (resVO.getCode() != 0) {
                    return resVO;
                }
                Map<String, Object> results = (Map<String, Object>) resVO.getResults();
                sessionVO = (SessionVO) results.get("sessionVO");
            }
        } else {
            if (StrUtil.isEmpty(memberLoginPO.getEmail())) {
                // 邮箱已绑定，用户未绑定，使用邮箱用户重新登录
                MiniLoginVO miniLoginVO = new MiniLoginVO();
                miniLoginVO.setDeviceId(memberEmailPO.getUserNo());
                miniLoginVO.setCode(memberEmailPO.getOpenid());
                miniLoginVO.setAppId(sessionVO.getMiniAppid());
                miniLoginVO.setMemberId(memberEmailPO.getId());
                miniLoginVO.setPhoneOs(loginParam.getPhoneOs());
                miniLoginVO.setLoginType(loginParam.getLoginType());
                ResultVO resVO = loginByAppCodeQuick(request, miniLoginVO);
                if (resVO.getCode() != 0) {
                    return resVO;
                }
                Map<String, Object> results = (Map<String, Object>) resVO.getResults();
                sessionVO = (SessionVO) results.get("sessionVO");
            } else if (memberLoginPO.getEmail().equals(loginParam.getEmail())) {
                // 邮箱已绑定，用户已绑定，邮箱一致，修改下sessionVO即可，统一处理

            } else {
                // 邮箱已绑定，用户已绑定，邮箱不一致，使用邮箱用户重新登录
                MiniLoginVO miniLoginVO = new MiniLoginVO();
                miniLoginVO.setDeviceId(memberEmailPO.getUserNo());
                miniLoginVO.setCode(memberEmailPO.getOpenid());
                miniLoginVO.setMemberId(memberEmailPO.getId());
                miniLoginVO.setAppId(sessionVO.getMiniAppid());
                miniLoginVO.setPhoneOs(loginParam.getPhoneOs());
                miniLoginVO.setLoginType(loginParam.getLoginType());
                ResultVO resVO = loginByAppCodeQuick(request, miniLoginVO);
                if (resVO.getCode() != 0) {
                    return resVO;
                }
                Map<String, Object> results = (Map<String, Object>) resVO.getResults();
                sessionVO = (SessionVO) results.get("sessionVO");
            }
        }
        // 存入宿主平台
        if (StrUtil.isNotEmpty(loginParam.getMiniBrand())) {
            String ua = request.getHeader("User-Agent");
            // 1=华为;2=小米;3=oppo;4=vivo;5=荣耀;6=魅族
            Integer type = fastMiniDeviceBrandService.queryDeviceBrandMap(loginParam.getMiniBrand(), ua);
            sessionVO.setMiniSubType(type);
        }
        // 更新登录表登录状态
        fastMemberLoginService.updateMemberLogin(sessionVO.getMemberId(), 1);
        sessionVO.setQuickLogin(1);
        sessionVO.setRetailId(memberLoginPO.getRetailId());
        sessionVO.setEmail(memberLoginPO.getEmail());
        sessionVO.setLoginType(loginParam.getLoginType());
        RedisUtil.set(RedisVar.REDIS_TOKEN_DB, StaticVar.ACCESS_TOKEN_PRE + sessionVO.getAccessToken(), JSONObject.toJSONString(sessionVO), StaticVar.ACCESS_APP_TOKEN_EXP);

        Map<String, Object> results = new HashMap<>(3);
        results.put("sessionVO", sessionVO);
        return ResultVO.success("Get ahead", results);
    }

    /**
     * APP-三方授权登录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> loginByThirdApp(HttpServletRequest request, MiniLoginVO loginParam) {
        actionLogService.log("app_third_login", "uid: " + JSON.toJSONString(loginParam));

        if (StrUtil.isBlank(loginParam.getUid())) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }

        // 查询当前登录用户
        SessionVO sessionVO = getSessionVO(request);
        FastMemberPO memberLoginPO = fastMemberMapper.queryById(sessionVO.getMemberId());
        // 查询uid关联的用户
        FastMemberPO memberUidPO = fastMemberService.queryMemberByUid(loginParam.getUid());
        Date nowTime = DateUtil.getNowDate();
        if (memberUidPO == null) {
            if (StrUtil.isEmpty(memberLoginPO.getUid())) {
                actionLogService.log("app_bind_uid", "memberid=" + memberLoginPO.getId() + ",uid=" + loginParam.getUid() + "<<1");
                // uid未绑定，用户未绑定，进行绑定操作
                memberLoginPO.setUid(loginParam.getUid());
                memberLoginPO.setEmail(loginParam.getEmail());
                memberLoginPO.setLoginType(loginParam.getLoginType());
                memberLoginPO.setMemberName(loginParam.getName());
                memberLoginPO.setMemberImg(loginParam.getPhotoUrl());
                memberLoginPO.setProviderId(loginParam.getProviderId());
                memberLoginPO.setUpdateTime(nowTime);
                fastMemberMapper.updateById(memberLoginPO);

                sessionVO.setUid(loginParam.getUid());
                sessionVO.setEmail(loginParam.getEmail());
                sessionVO.setMemberName(loginParam.getName());
                sessionVO.setMemberImg(loginParam.getPhotoUrl());
                sessionVO.setProviderId(loginParam.getProviderId());

                // 纯新用户，添加个标识缓存
                String keyNew = StaticVar.MEMBER_PURE_NEW + memberLoginPO.getId();
                RedisUtil.set(keyNew, StaticVar.PURE_NEW, 20);
            } else {
                // uid未绑定，用户已绑定，因为和当前uid不一样，使用uid创建新用户后登录
                MiniLoginVO miniLoginVO = new MiniLoginVO();
                miniLoginVO.setDeviceId(memberLoginPO.getUserNo());
                miniLoginVO.setCode(memberLoginPO.getOpenid());
                miniLoginVO.setAppId(sessionVO.getMiniAppid());
                miniLoginVO.setAddNewMember(1);// 增加新用户
                miniLoginVO.setUid(loginParam.getUid());
                miniLoginVO.setEmail(loginParam.getEmail());
                miniLoginVO.setName(loginParam.getName());
                miniLoginVO.setPhotoUrl(loginParam.getPhotoUrl());
                miniLoginVO.setProviderId(loginParam.getProviderId());
                miniLoginVO.setPhoneOs(loginParam.getPhoneOs());
                miniLoginVO.setLoginType(loginParam.getLoginType());
                ResultVO resVO = loginByAppCodeQuick(request, miniLoginVO);
                if (resVO.getCode() != 0) {
                    return resVO;
                }
                Map<String, Object> results = (Map<String, Object>) resVO.getResults();
                sessionVO = (SessionVO) results.get("sessionVO");

                // 纯新用户，添加个标识缓存
                String keyNew = StaticVar.MEMBER_PURE_NEW + sessionVO.getMemberId();
                RedisUtil.set(keyNew, StaticVar.PURE_NEW, 20);
            }
        } else {
            if (StrUtil.isEmpty(memberLoginPO.getUid())) {
                // uid已绑定，用户未绑定，使用uid用户重新登录
                MiniLoginVO miniLoginVO = new MiniLoginVO();
                miniLoginVO.setDeviceId(memberUidPO.getUserNo());
                miniLoginVO.setCode(memberUidPO.getOpenid());
                miniLoginVO.setAppId(sessionVO.getMiniAppid());
                miniLoginVO.setMemberId(memberUidPO.getId());
                miniLoginVO.setPhoneOs(loginParam.getPhoneOs());
                miniLoginVO.setLoginType(loginParam.getLoginType());
                ResultVO resVO = loginByAppCodeQuick(request, miniLoginVO);
                if (resVO.getCode() != 0) {
                    return resVO;
                }
                Map<String, Object> results = (Map<String, Object>) resVO.getResults();
                sessionVO = (SessionVO) results.get("sessionVO");
            } else if (memberLoginPO.getUid().equals(loginParam.getUid())) {
                // uid已绑定，用户已绑定，uid一致，修改下sessionVO即可，统一处理

            } else {
                // uid已绑定，用户已绑定，uid不一致，使用uid用户重新登录
                MiniLoginVO miniLoginVO = new MiniLoginVO();
                miniLoginVO.setDeviceId(memberUidPO.getUserNo());
                miniLoginVO.setCode(memberUidPO.getOpenid());
                miniLoginVO.setMemberId(memberUidPO.getId());
                miniLoginVO.setAppId(sessionVO.getMiniAppid());
                miniLoginVO.setPhoneOs(loginParam.getPhoneOs());
                miniLoginVO.setLoginType(loginParam.getLoginType());
                ResultVO resVO = loginByAppCodeQuick(request, miniLoginVO);
                if (resVO.getCode() != 0) {
                    return resVO;
                }
                Map<String, Object> results = (Map<String, Object>) resVO.getResults();
                sessionVO = (SessionVO) results.get("sessionVO");
            }
        }
        // 存入宿主平台
        if (StrUtil.isNotEmpty(loginParam.getMiniBrand())) {
            String ua = request.getHeader("User-Agent");
            // 1=华为;2=小米;3=oppo;4=vivo;5=荣耀;6=魅族
            Integer type = fastMiniDeviceBrandService.queryDeviceBrandMap(loginParam.getMiniBrand(), ua);
            sessionVO.setMiniSubType(type);
        }
        // 更新登录表登录状态
        fastMemberLoginService.updateMemberLogin(sessionVO.getMemberId(), 1);
        sessionVO.setQuickLogin(1);
        sessionVO.setRetailId(memberLoginPO.getRetailId());
        sessionVO.setUid(memberLoginPO.getUid());
        sessionVO.setEmail(memberLoginPO.getEmail());
        sessionVO.setMemberName(memberLoginPO.getMemberName());
        sessionVO.setMemberImg(memberLoginPO.getMemberImg());
        sessionVO.setProviderId(memberLoginPO.getProviderId());
        sessionVO.setLoginType(loginParam.getLoginType());
        RedisUtil.set(RedisVar.REDIS_TOKEN_DB, StaticVar.ACCESS_TOKEN_PRE + sessionVO.getAccessToken(), JSONObject.toJSONString(sessionVO), StaticVar.ACCESS_APP_TOKEN_EXP);

        Map<String, Object> results = new HashMap<>(3);
        results.put("sessionVO", sessionVO);
        return ResultVO.success("Get ahead", results);
    }

    /**
     * APP-退出登录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> loginOutApp(HttpServletRequest request, MiniLoginVO loginVO) {
        SessionVO sessionVO = getSessionVO(request);
        if (sessionVO == null || sessionVO.getMemberId() == null) {
            return ResultVO.error(StaticStr.LOGOUT_FAILED);
        }
        fastMemberLoginService.updateMemberLogin(sessionVO.getMemberId(), 0);
        // 清除token
        RedisUtil.del(RedisVar.REDIS_TOKEN_DB, StaticVar.ACCESS_TOKEN_PRE + sessionVO.getAccessToken());

        // 清除登录状态缓存
        String keyQuickLogin = StaticVar.APP_LOGIN_EMAIL + sessionVO.getMemberId();
        RedisUtil.del(keyQuickLogin);

        // 调用code登录
        loginVO.setIsLogout(true);
        return loginByAppCodeQuick(request, loginVO);
    }

    /**
     * 快应用-注销登录
     */
    public ResultVO<?> loginDestoryApp(SessionVO sessionVO) {
        // 软删除用户
        fastMemberMapper.delMemberById(sessionVO.getMemberId());
        // 删除默认的游客登录token


        return ResultVO.success();
    }

}
