/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.mini.*;
import com.fast.mapper.setting.FastSettingCommonMapper;
import com.fast.mapper.setting.FastSettingSystemMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.mini.*;
import com.fast.po.playlist.FastMiniPlaylistPO;
import com.fast.po.setting.FastSettingCommonPO;
import com.fast.po.setting.FastSettingSystemPO;
import com.fast.po.task.FastSettingTaskPO;
import com.fast.service.aliyun.AliCdnService;
import com.fast.service.base.BaseService;
import com.fast.service.common.FastCornerService;
import com.fast.service.common.FastTagService;
import com.fast.service.common.FastVideoService;
import com.fast.service.drama.FastDramaRetailService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.playlist.FastMiniPlaylistService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.task.FastSettingTaskService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.SessionVO;
import com.fast.vo.common.FastCornerVO;
import com.fast.vo.common.FastTagVO;
import com.fast.vo.drama.FastDramaVO;
import com.fast.vo.drama.MiniDramaVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniSettingService extends BaseService {

    @Autowired
    private FastDramaService fastDramaService;
    @Autowired
    private FastTagService fastTagService;
    @Autowired
    private FastCornerService fastCornerService;
    @Autowired
    private FastDramaRetailService dramaRetailService;
    @Autowired
    private FastMiniBannerMapper fastMiniBannerMapper;
    @Autowired
    private FastMiniAccessMapper fastMiniAccessMapper;
    @Autowired
    private FastMiniRecommendMapper miniRecommendMapper;
    @Autowired
    private FastMiniRecommendModularMapper recommendModularMapper;
    @Autowired
    private FastMiniVideoModularMapper videoModularMapper;
    @Autowired
    private FastVideoService videoService;
    @Autowired
    private FastMiniBottomMapper fastMiniBottomMapper;
    @Autowired
    private FastMiniSettingMapper fastMiniSettingMapper;
    @Autowired
    private FastMiniSettingGroupMapper settingGroupMapper;
    @Autowired
    private FastMiniWallpaperMapper fastMiniWallpaperMapper;
    @Autowired
    private FastMiniIntroductionMapper fastMiniIntroductionMapper;
    @Autowired
    private FastMiniMapper fastMiniMapper;
    @Autowired
    private FastSettingCommonMapper settingCommonMapper;
    @Autowired
    private FastSettingTaskService fastSettingTaskService;
    @Autowired
    private FastSettingSystemMapper fastSettingSystemMapper;
    @Autowired
    private AliCdnService aliCdnService;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastMiniPlaylistService fastMiniPlaylistService;

    /**
     * 查询小程序配置信息-member专用
     */
    public FastMiniSettingPO queryInfoByRedisMember(FastMiniSettingPO params, FastMiniMemberVersionPO versionPO, SessionVO sessionVO) {
        FastMiniSettingPO miniSettingPO = queryInfoByRedis(params);
        if (params.getType() == 5 || params.getType() == 7) {
            resetQuick(miniSettingPO, sessionVO);
        }
        if (versionPO != null && versionPO.getVersion() != null) {
            if (miniSettingPO.getSettingGroup() != null) {
                FastMiniSettingGroupPO settingGroup = miniSettingPO.getSettingGroup();
                if (settingGroup.getOpenState() == 1) {
                    int count = 0;// 匹配计数
                    if (versionPO.getRechargeCount() >= settingGroup.getRechargeCount()) {
                        count++;
                    }
                    if (versionPO.getVipOrderDay() >= settingGroup.getVipOrderDay()) {
                        count++;
                    }
                    if (versionPO.getFinishDramaNum() >= settingGroup.getFinishDramaNum()) {
                        count++;
                    }
                    // 切换到用户组所在的配置信息
                    if ((settingGroup.getOpenMemberGroup() == 3 && count == 3)
                            || (settingGroup.getOpenMemberGroup() == 2 && count >= 2)
                            || (settingGroup.getOpenMemberGroup() == 1 && count >= 1)) {
                        miniSettingPO.setRecommend(miniSettingPO.getRecommendGroup());
                        miniSettingPO.setRecommendModularList(miniSettingPO.getRecommendModularListGroup());
                    }
                }
            }
            if (miniSettingPO.getSettingGroupBanner() != null) {
                FastMiniSettingGroupPO settingGroup = miniSettingPO.getSettingGroupBanner();
                if (settingGroup.getOpenState() == 1) {
                    int count = 0;// 匹配计数
                    if (versionPO.getRechargeCount() >= settingGroup.getRechargeCount()) {
                        count++;
                    }
                    if (versionPO.getVipOrderDay() >= settingGroup.getVipOrderDay()) {
                        count++;
                    }
                    if (versionPO.getFinishDramaNum() >= settingGroup.getFinishDramaNum()) {
                        count++;
                    }
                    // 切换到用户组所在的配置信息
                    if ((settingGroup.getOpenMemberGroup() == 3 && count == 3)
                            || (settingGroup.getOpenMemberGroup() == 2 && count >= 2)
                            || (settingGroup.getOpenMemberGroup() == 1 && count >= 1)) {
                        miniSettingPO.setBannerList(miniSettingPO.getBannerListGroup());
                    }
                }
            }
        }
        return miniSettingPO;
    }

    public void resetQuick(FastMiniSettingPO miniSettingPO, SessionVO sessionVO) {
        // 快应用
        Integer miniSubType = sessionVO.getMiniSubType();
        miniSubType = miniSubType == null ? 1 : miniSubType; // 1=华为;2=小米;3=oppo;4=vivo;5=荣耀;6=魅族
        Integer enterType = sessionVO.getEnterType(); // 1自然2渠道
        enterType = enterType == null ? 2 : enterType;
        List<FastMiniRecommendPO> recTheaterList = miniSettingPO.getRecommendTheaterList();
        List<FastMiniRecommendPO> recSuperList = miniSettingPO.getRecommendSuperList();

        Map<String, FastMiniRecommendPO> theaterMap = new HashMap<>();
        Map<String, FastMiniRecommendPO> superMap = new HashMap<>();
        for (FastMiniRecommendPO item : recTheaterList) {
            theaterMap.put(item.getTabOne() + "_" + item.getTabTwo(), item);
        }
        for (FastMiniRecommendPO item : recSuperList) {
            superMap.put(item.getTabOne() + "_" + item.getTabTwo(), item);
        }
        Integer tabOne = 1;// 默认1联盟2华为
        Integer tabTwo = 2;// 默认1渠道2自然
        if (miniSubType == 1 && enterType == 1) {
            // 华为-自然
            tabOne = 2;
            tabTwo = 2;
        } else if (miniSubType == 1 && enterType == 2) {
            // 华为-渠道
            tabOne = 2;
            tabTwo = 1;
        } else if (miniSubType > 1 && enterType == 1) {
            // 联盟-自然
            tabOne = 1;
            tabTwo = 2;
        } else if (miniSubType > 1 && enterType == 1) {
            // 联盟-渠道
            tabOne = 1;
            tabTwo = 1;
        }
        String tabKey = tabOne + "_" + tabTwo;
        if (theaterMap.containsKey(tabKey)) {
            miniSettingPO.setRecommend(theaterMap.get(tabKey));
        }
        if (superMap.containsKey(tabKey)) {
            miniSettingPO.setRecommendSuper(superMap.get(tabKey));
        }
    }

    /**
     * 查询小程序扩展配置信息
     */
    public FastMiniSettingPO querySettingByRedis(Integer miniId, Integer cvid) {
        String key = StaticVar.MINI_CONTENT_SETTING_ONLY + miniId + ":" + cvid;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            // 查询数据库
            FastMiniSettingPO setParam = new FastMiniSettingPO();
            setParam.setMiniId(miniId);
            FastMiniSettingPO setPO = fastMiniSettingMapper.querySimpleByMiniId(setParam);
            if (setPO != null) {
                res = JsonUtil.toString(setPO);
            } else {
                res = StaticVar.EMPTY_FLAG;
            }
            RedisUtil.set(key, res, 60 * 60 * 12);
        }
        if (StrUtil.isNotEmpty(res) && !StaticVar.EMPTY_FLAG.equals(res)) {
            FastMiniSettingPO msPO = JsonUtil.toJavaObject(res, FastMiniSettingPO.class);
            return msPO;
        }
        return null;
    }

    /**
     * 播放页快速查询配置信息
     */
    public FastMiniSettingPO queryInfoByRedisSimple(FastMiniSettingPO params) {
        String key = StaticVar.MINI_CONTENT_SETTING_SIMPLE + params.getAppId() + ":" + params.getContVersionId();
        String res = RedisUtil.get(key);
        FastMiniSettingPO settingPO = null;
        if (StrUtil.isEmpty(res)) {
            // 查询小程序
            FastMiniVO miniPO = fastMiniService.queryByAppIdRedis(params.getAppId());
            // 查询配置
            FastMiniSettingPO settingParam = new FastMiniSettingPO();
            settingParam.setMiniId(miniPO.getId());
            settingPO = fastMiniSettingMapper.querySimpleByMiniId(settingParam);
            // 查询小程序福利任务配置
            FastSettingTaskPO taskPO = fastSettingTaskService.getSettingTaskRedis(miniPO.getId());
            settingPO.setTaskPO(taskPO);
            // 加桌
            settingPO.setDesktopFlag(miniPO.getDesktopFlag());
            // 是否支持倍速
            settingPO.setAccelerateFlag(miniPO.getAccelerateFlag());
            // 缓存起来
            RedisUtil.set(key, JsonUtil.toString(settingPO), 60 * 20);
        } else {
            settingPO = JsonUtil.toJavaObject(res, FastMiniSettingPO.class);
        }

        return settingPO;
    }

    /**
     * 查询小程序配置信息
     */
    public FastMiniSettingPO queryInfoByRedis(FastMiniSettingPO params) {
        // log.info("查询小程序配置信息:{}", JSON.toJSONString(params));
        
        if (params.getMiniId() == null || params.getContVersionId() == null) {
            return null;
        }
        String key = StaticVar.MINI_CONTENT_SETTING + params.getMiniId() + ":" + params.getContVersionId();
        String value = RedisUtil.get(key);
        // log.info("查询小程序配置信息缓存，key: {}, value: {}", key, value);
        if (notEmpty(value)) {
            FastMiniSettingPO po = JsonUtil.toJavaObject(value, FastMiniSettingPO.class);
            List<FastMiniAccessPO> accessList = po.getAccessList();
            for (FastMiniAccessPO cur : accessList) {
                // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                if (cur.getShowDrama() == 2) {
                    List<FastTagVO> tagList = fastTagService.queryInfoByRedis(cur.getTagIds());
                    cur.setTagList(tagList);
                }
                if (cur.getShowDrama() == 3) {
                    List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(cur.getCornerIds());
                    cur.setCornerList(cornerList);
                }
                List<FastDramaVO> dramaList = getDramaListNoHideRedis(cur.getDramaIds(), params.getRetailId());
                cur.setDramaList(dramaList);
                if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                    // 升级接口
                    cur.setDramaList(null);
                    List<Integer> dramaIdList = new ArrayList<>();
                    for (FastDramaVO drama : dramaList) {
                        dramaIdList.add(drama.getId());
                    }
                    cur.setDramaIdList(dramaIdList);
                }
            }
            // 推荐区
            {
                FastMiniRecommendPO recommendPO = po.getRecommend();
                if (recommendPO != null && recommendPO.getContVersionId() == params.getContVersionId()) {
                    List<FastDramaVO> dramaList = getDramaListNoHideRedis(recommendPO.getDramaIds(), params.getRetailId());
                    recommendPO.setDramaList(dramaList);
                    if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                        // 升级接口
                        recommendPO.setDramaList(null);
                        List<Integer> dramaIdList = new ArrayList<>();
                        for (FastDramaVO drama : dramaList) {
                            dramaIdList.add(drama.getId());
                        }
                        recommendPO.setDramaIdList(dramaIdList);
                    }

                    // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                    if (recommendPO.getShowDrama() == 2) {
                        List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recommendPO.getTagIds());
                        recommendPO.setTagList(tagList);
                    }
                    if (recommendPO.getShowDrama() == 3) {
                        List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recommendPO.getCornerIds());
                        recommendPO.setCornerList(cornerList);
                    }
                }
            }
            {
                FastMiniRecommendPO recommendGroup = po.getRecommendGroup();
                if (recommendGroup != null && recommendGroup.getContVersionId() == params.getContVersionId()) {
                    List<FastDramaVO> dramaList = getDramaListNoHideRedis(recommendGroup.getDramaIds(), params.getRetailId());
                    recommendGroup.setDramaList(dramaList);
                    if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                        // 升级接口==推荐组
                        recommendGroup.setDramaList(null);
                        List<Integer> dramaIdList = new ArrayList<>();
                        for (FastDramaVO drama : dramaList) {
                            dramaIdList.add(drama.getId());
                        }
                        recommendGroup.setDramaIdList(dramaIdList);
                    }
                    // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                    if (recommendGroup.getShowDrama() == 2) {
                        List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recommendGroup.getTagIds());
                        recommendGroup.setTagList(tagList);
                    }
                    if (recommendGroup.getShowDrama() == 3) {
                        List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recommendGroup.getCornerIds());
                        recommendGroup.setCornerList(cornerList);
                    }
                }
            }
            {
                if (po.getType() == 1) {
                    // 底导航推荐区
                    FastMiniRecommendPO recommendGroup = po.getRecommendBottom();
                    if (recommendGroup != null && recommendGroup.getContVersionId() == params.getContVersionId()) {
                        List<FastDramaVO> dramaList = getDramaListNoHideRedis(recommendGroup.getDramaIds(), params.getRetailId());
                        recommendGroup.setDramaList(dramaList);
                        if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                            // 升级接口==推荐组
                            recommendGroup.setDramaList(null);
                            List<Integer> dramaIdList = new ArrayList<>();
                            for (FastDramaVO drama : dramaList) {
                                dramaIdList.add(drama.getId());
                            }
                            recommendGroup.setDramaIdList(dramaIdList);
                        }
                        // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                        if (recommendGroup.getShowDrama() == 2) {
                            List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recommendGroup.getTagIds());
                            recommendGroup.setTagList(tagList);
                        }
                        if (recommendGroup.getShowDrama() == 3) {
                            List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recommendGroup.getCornerIds());
                            recommendGroup.setCornerList(cornerList);
                        }
                    } else {
                        log.info("底导航推荐区缓存为空返回默认空数据");
                        // 内容推荐-返回默认值
                        FastMiniRecommendPO recommendPO = new FastMiniRecommendPO();
                        recommendPO.setGroupType(7);
                        recommendPO.setMiniId(params.getMiniId());
                        recommendPO.setCreatorId(params.getCreatorId());
                        recommendPO.setCreateTime(DateUtil.getNowDate());
                        recommendPO.setShowDrama(1);
                        po.setRecommendBottom(recommendPO);
                    }
                }
            }
            {
                // 快应用-v1
                FastMiniRecommendPO recommendSuper = po.getRecommendSuper();
                // log.info("recommendSuper:{}", JSON.toJSONString(recommendSuper));
                if (recommendSuper != null && recommendSuper.getContVersionId() == params.getContVersionId()) {
                    List<FastDramaVO> dramaList = getDramaListNoHideRedis(recommendSuper.getDramaIds(), params.getRetailId());
                    // log.info("dramaList:{}", JSON.toJSONString(dramaList));
                    recommendSuper.setDramaList(dramaList);
                    if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                        // 升级接口
                        recommendSuper.setDramaList(null);
                        List<Integer> dramaIdList = new ArrayList<>();
                        for (FastDramaVO drama : dramaList) {
                            dramaIdList.add(drama.getId());
                        }
                        recommendSuper.setDramaIdList(dramaIdList);
                    }
                    // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                    if (recommendSuper.getShowDrama() == 2) {
                        List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recommendSuper.getTagIds());
                        recommendSuper.setTagList(tagList);
                    }
                    if (recommendSuper.getShowDrama() == 3) {
                        List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recommendSuper.getCornerIds());
                        recommendSuper.setCornerList(cornerList);
                    }
                }

                // 快应用-v1-青少年模式
                FastMiniRecommendPO recommendYoung = po.getRecommendYoung();
                if (recommendYoung != null && recommendYoung.getContVersionId() == params.getContVersionId()) {
                    List<FastDramaVO> dramaList = getDramaListNoHideRedis(recommendYoung.getDramaIds(), params.getRetailId());
                    recommendYoung.setDramaList(dramaList);
                    if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                        // 升级接口
                        recommendYoung.setDramaList(null);
                        List<Integer> dramaIdList = new ArrayList<>();
                        for (FastDramaVO drama : dramaList) {
                            dramaIdList.add(drama.getId());
                        }
                        recommendYoung.setDramaIdList(dramaIdList);
                    }
                    // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                    if (recommendYoung.getShowDrama() == 2) {
                        List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recommendYoung.getTagIds());
                        recommendYoung.setTagList(tagList);
                    }
                    if (recommendYoung.getShowDrama() == 3) {
                        List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recommendYoung.getCornerIds());
                        recommendYoung.setCornerList(cornerList);
                    }
                }

                // 快应用-v2-多tab剧列表-小剧场
                List<FastMiniRecommendPO> recTheaterList = po.getRecommendTheaterList(); // 剧场推荐列表
                if (recTheaterList != null && recTheaterList.size() > 0) {
                    for (FastMiniRecommendPO recItem : recTheaterList) {
                        if (recItem != null && recItem.getContVersionId() == params.getContVersionId()) {
                            List<FastDramaVO> dramaList = getDramaListNoHideRedis(recItem.getDramaIds(), params.getRetailId());
                            recItem.setDramaList(dramaList);
                            if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                                // 升级接口
                                recItem.setDramaList(null);
                                List<Integer> dramaIdList = new ArrayList<>();
                                for (FastDramaVO drama : dramaList) {
                                    dramaIdList.add(drama.getId());
                                }
                                recItem.setDramaIdList(dramaIdList);
                            }
                            // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                            if (recItem.getShowDrama() == 2) {
                                List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recItem.getTagIds());
                                recItem.setTagList(tagList);
                            }
                            if (recItem.getShowDrama() == 3) {
                                List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recItem.getCornerIds());
                                recItem.setCornerList(cornerList);
                            }
                        }
                    }
                }

                // 快应用-v2-多tab剧列表-精选
                List<FastMiniRecommendPO> recSuperList = po.getRecommendSuperList(); // 剧场推荐列表
                if (recSuperList != null && recSuperList.size() > 0) {
                    for (FastMiniRecommendPO recItem : recSuperList) {
                        if (recItem != null && recItem.getContVersionId() == params.getContVersionId()) {
                            List<FastDramaVO> dramaList = getDramaListNoHideRedis(recItem.getDramaIds(), params.getRetailId());
                            recItem.setDramaList(dramaList);
                            if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                                // 升级接口
                                recItem.setDramaList(null);
                                List<Integer> dramaIdList = new ArrayList<>();
                                for (FastDramaVO drama : dramaList) {
                                    dramaIdList.add(drama.getId());
                                }
                                recItem.setDramaIdList(dramaIdList);
                            }
                            // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                            if (recItem.getShowDrama() == 2) {
                                List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recItem.getTagIds());
                                recItem.setTagList(tagList);
                            }
                            if (recItem.getShowDrama() == 3) {
                                List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recItem.getCornerIds());
                                recItem.setCornerList(cornerList);
                            }
                        }
                    }
                }
            }
            // 推荐区-模块
            {
                List<FastMiniRecommendModularPO> recommendModularList = po.getRecommendModularList();
                if (CollUtil.hasContent(recommendModularList)) {
                    for (FastMiniRecommendModularPO modular : recommendModularList) {
                        modular.setDramaList(getDramaListNoHideRedis(modular.getDramaIds(), params.getRetailId()));

                        // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                        if (modular.getShowDrama() == 2) {
                            List<FastTagVO> tagList = fastTagService.queryInfoByRedis(modular.getTagIds());
                            modular.setTagList(tagList);
                        }
                        if (modular.getShowDrama() == 3) {
                            List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(modular.getCornerIds());
                            modular.setCornerList(cornerList);
                        }
                    }
                } else {
                    po.setRecommendModularList(new ArrayList<>());
                }
            }
            // 推荐区-模块
            {
                List<FastMiniRecommendModularPO> recommendModularListGroup = po.getRecommendModularListGroup();
                if (CollUtil.hasContent(recommendModularListGroup)) {
                    for (FastMiniRecommendModularPO modular : recommendModularListGroup) {
                        modular.setDramaList(getDramaListNoHideRedis(modular.getDramaIds(), params.getRetailId()));

                        // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                        if (modular.getShowDrama() == 2) {
                            List<FastTagVO> tagList = fastTagService.queryInfoByRedis(modular.getTagIds());
                            modular.setTagList(tagList);
                        }
                        if (modular.getShowDrama() == 3) {
                            List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(modular.getCornerIds());
                            modular.setCornerList(cornerList);
                        }
                    }
                } else {
                    po.setRecommendModularListGroup(new ArrayList<>());
                }
            }
            // 推荐区-模块-发现
            {
                List<FastMiniVideoModularPO> videoModularList = po.getVideoModularList();
                if (CollUtil.hasContent(videoModularList)) {
                    for (FastMiniVideoModularPO video : videoModularList) {
                        video.setVideoList(videoService.queryInfoByRedis(video.getVideoIds()));
                    }
                } else {
                    po.setVideoModularList(new ArrayList<>());
                }
            }
            // 剧单配置
            List<FastMiniPlaylistPO> playlistList = po.getPlaylists();
            if (!CollUtil.hasContent(playlistList)) {
                po.setPlaylists(new ArrayList<>());
            } else {
                playlistList.forEach(playlist -> fastMiniPlaylistService.fillI18n(playlist));
            }
            // 查询小程序福利任务配置
            FastSettingTaskPO taskPO = fastSettingTaskService.getSettingTaskRedis(po.getMiniId());
            po.setTaskPO(taskPO);

            // 增加全剧列表
            if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                Map<Integer, MiniDramaVO> retailDramaMap = fastDramaService.getRetailDramaAllMap(params.getRetailId());
                po.setRetailDramaMap(retailDramaMap);
            }
            // 微小给历史数据增加福利配置回显
            if (CollUtil.isNotEmpty(po.getBottomList()) && po.getType() == 1) {
                boolean present = po.getBottomList().stream().anyMatch(item -> item.getType() == 6);
                if (!present) {
                    FastMiniBottomPO mbPO = new FastMiniBottomPO();
                    mbPO.setContentType(po.getContentType());
                    mbPO.setCreatorId(params.getCreatorId());
                    mbPO.setCreateTime(DateUtil.getNowDate());
                    mbPO.setMiniId(params.getMiniId());
                    mbPO.setSequence(35);
                    mbPO.setType(6);
                    mbPO.setName("福利");
                    mbPO.setOpenState(0);// 默认关闭
                    po.getBottomList().add(mbPO);
                }
                // 增加推荐按钮回显
                boolean recommendPresent = po.getBottomList().stream().anyMatch(item -> item.getType() == 7);
                if (!recommendPresent) {
                    FastMiniBottomPO mbPO = buildRecommendBottom(po.getContentType(), params.getCreatorId(), params.getMiniId(), 37, DateUtil.getNowDate());
                    po.getBottomList().add(mbPO);
                }
            }
            return po;
        }
        FastMiniPO miniParam = new FastMiniPO();
        miniParam.setId(params.getMiniId());
        FastMiniPO miniPO = fastMiniMapper.queryById(miniParam);
        // 通过数据库查询
        Date nowDate = DateUtil.getNowDate();
        FastMiniSettingPO po = new FastMiniSettingPO();
        po.setMiniId(params.getMiniId());
        po.setEncryptionId(encode(params.getMiniId()));
        po = fastMiniSettingMapper.queryByMiniId(po);
        if (miniPO != null && po != null) {
            po.setType(miniPO.getType());
            po.setContentType(miniPO.getContentType());
            po.setMiniName(miniPO.getMiniName()); // 小程序名称(协议展示用)
            po.setPrincipalName(miniPO.getPrincipalName()); // 主体名称(协议展示用)
            po.setAccelerateFlag(miniPO.getAccelerateFlag()); // 倍速播放0否1是
        }
        if (po == null) {
            po = new FastMiniSettingPO();
            po.setCreatorId(params.getCreatorId());
            po.setCreateTime(nowDate);
            po.setMiniId(params.getMiniId());
            po.setCoinName("K币");
            po.setVisitorName("VIP");
            fastMiniSettingMapper.insertSelective(po);

            po = fastMiniSettingMapper.queryByMiniId(po);
        }
        if (po == null) {
            throw new MyException("初始化数据失败");
        }
        po.setType(miniPO.getType());
        po.setContentType(miniPO.getContentType());
        if (miniPO.getType() == 5 && StrUtil.isEmpty(po.getPayTypeSet())) {
            // 初始化支付配置
            FastSettingSystemPO ssParam = new FastSettingSystemPO();
            ssParam.setCode("pay_type_set");
            FastSettingSystemPO ssPO = fastSettingSystemMapper.queryOne(ssParam);
            if (ssPO != null) {
                po.setPayTypeSet(ssPO.getContent());
            }
        }
        if (miniPO.getType() == 7 && StrUtil.isEmpty(po.getPayTypeSet())) {
            // 初始化支付配置
            FastSettingSystemPO ssParam = new FastSettingSystemPO();
            ssParam.setCode("app_pay_types");
            FastSettingSystemPO ssPO = fastSettingSystemMapper.queryOne(ssParam);
            if (ssPO != null) {
                po.setPayTypeSet(ssPO.getContent());
            }
        }

        // 处理用户组设置初始值-推荐
        if (po.getSettingGroup() == null) {
            FastMiniSettingGroupPO query = new FastMiniSettingGroupPO();
            query.setMiniId(po.getMiniId());
            query.setGroupType(1);// 推荐
            FastMiniSettingGroupPO groupPO = settingGroupMapper.queryOne(query);
            if (groupPO == null) {
                groupPO = new FastMiniSettingGroupPO();
                groupPO.setGroupType(1);// 推荐
                groupPO.setMiniId(po.getMiniId());
                groupPO.setCreateTime(DateUtil.getNowDate());
                groupPO.setCreatorId(po.getCreatorId());

                settingGroupMapper.insertSelective(groupPO);

                groupPO = settingGroupMapper.queryOne(query);
            }
            po.setSettingGroup(groupPO);
        }

        // 处理用户组设置初始值-banner
        if (po.getSettingGroupBanner() == null) {
            FastMiniSettingGroupPO query = new FastMiniSettingGroupPO();
            query.setMiniId(po.getMiniId());
            query.setGroupType(2);// banner
            FastMiniSettingGroupPO groupPO = settingGroupMapper.queryOne(query);
            if (groupPO == null) {
                groupPO = new FastMiniSettingGroupPO();
                groupPO.setGroupType(2);// banner
                groupPO.setMiniId(po.getMiniId());
                groupPO.setCreateTime(DateUtil.getNowDate());
                groupPO.setCreatorId(po.getCreatorId());

                settingGroupMapper.insertSelective(groupPO);

                groupPO = settingGroupMapper.queryOne(query);
            }
            po.setSettingGroupBanner(groupPO);
        }

        // banner-默认-全局
        {
            FastMiniBannerPO banner = new FastMiniBannerPO();
            banner.setMiniId(params.getMiniId());
            banner.setType(1);
            banner.setGroupType(1);
            List<FastMiniBannerPO> bannerList = fastMiniBannerMapper.queryList(banner);
            po.setMiniId(params.getMiniId());
            po.setBannerList(bannerList);
            for (FastMiniBannerPO cur : bannerList) {
                if (cur.getJumpType() == 1) {
                    FastDramaVO dramaVO = fastDramaService.queryInfoByRedis(toInteger(cur.getJumpContent()));
                    cur.setDramaVO(dramaVO);
                }
            }
            if (CollUtil.isEmpty(bannerList)) {
                po.setBannerList(new ArrayList<>());
            }
        }

        // banner-默认-用户组
        {
            FastMiniBannerPO banner = new FastMiniBannerPO();
            banner.setMiniId(params.getMiniId());
            banner.setType(1);
            banner.setGroupType(2);
            List<FastMiniBannerPO> bannerList = fastMiniBannerMapper.queryList(banner);
            po.setMiniId(params.getMiniId());
            po.setBannerListGroup(bannerList);
            for (FastMiniBannerPO cur : bannerList) {
                if (cur.getJumpType() == 1) {
                    FastDramaVO dramaVO = fastDramaService.queryInfoByRedis(toInteger(cur.getJumpContent()));
                    cur.setDramaVO(dramaVO);
                }
            }
            if (CollUtil.isEmpty(bannerList)) {
                po.setBannerListGroup(new ArrayList<>());
            }
        }

        // banner-发现
        {
            FastMiniBannerPO banner = new FastMiniBannerPO();
            banner.setMiniId(params.getMiniId());
            banner.setType(2);
            List<FastMiniBannerPO> bannerFindList = fastMiniBannerMapper.queryList(banner);
            po.setMiniId(params.getMiniId());
            po.setBannerFindList(bannerFindList);
            for (FastMiniBannerPO cur : bannerFindList) {
                if (cur.getJumpType() == 1) {
                    FastDramaVO dramaVO = fastDramaService.queryInfoByRedis(toInteger(cur.getJumpContent()));
                    cur.setDramaVO(dramaVO);
                }
            }
            if (CollUtil.isEmpty(bannerFindList)) {
                po.setBannerFindList(new ArrayList<>());
            }
        }

        FastDramaPO dramaPO = new FastDramaPO();
        dramaPO.setContentType(po.getContentType());
        dramaPO.setDelFlag(StaticVar.NO);
        List<Integer> allIdList = fastDramaService.queryAllIds(dramaPO);
        String allIds = StrUtil.join(allIdList);

        // 功能入口
        FastMiniAccessPO access = new FastMiniAccessPO();
        access.setMiniId(params.getMiniId());
        List<FastMiniAccessPO> accessList = fastMiniAccessMapper.queryShowList(access);
        if (CollUtil.isEmpty(accessList)) {
            // 初始化数据
            access.setMiniId(0);
            accessList = fastMiniAccessMapper.queryList(access);
            if (CollUtil.hasContent(accessList)) {
                for (FastMiniAccessPO cur : accessList) {
                    cur.setCreatorId(params.getCreatorId());
                    cur.setCreateTime(nowDate);
                    cur.setMiniId(params.getMiniId());
                    cur.setShowDrama(1);

                    if (cur.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                        cur.setDramaIds(allIds);
                    }
                    cur.setDramaList(getDramaListNoHideRedis(cur.getDramaIds(), params.getRetailId()));
                }
                fastMiniAccessMapper.insertBatch(accessList);
            }
        } else {
            for (FastMiniAccessPO cur : accessList) {
                // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                if (cur.getShowDrama() == 2) {
                    List<FastTagVO> tagList = fastTagService.queryInfoByRedis(cur.getTagIds());
                    cur.setTagList(tagList);
                }
                if (cur.getShowDrama() == 3) {
                    List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(cur.getCornerIds());
                    cur.setCornerList(cornerList);
                }
                List<FastDramaVO> dramaList = getDramaListNoHideRedis(cur.getDramaIds(), params.getRetailId());
                cur.setDramaList(dramaList);
                if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                    // 升级接口
                    cur.setDramaList(null);
                    List<Integer> dramaIdList = new ArrayList<>();
                    for (FastDramaVO drama : dramaList) {
                        dramaIdList.add(drama.getId());
                    }
                    cur.setDramaIdList(dramaIdList);
                }
            }
        }
        po.setAccessList(accessList);
        // 推荐区
        if (po.getContentType() == 1 || po.getContentType() == 4) {
            {
                // 内容推荐-全局
                FastMiniRecommendPO recommendQuery = new FastMiniRecommendPO();
                recommendQuery.setMiniId(params.getMiniId());
                recommendQuery.setGroupType(1);
                recommendQuery.setContVersionId(params.getContVersionId());
                FastMiniRecommendPO recommendPO = miniRecommendMapper.queryOne(recommendQuery);
                if (recommendPO == null) {
                    recommendPO = new FastMiniRecommendPO();
                    recommendPO.setGroupType(1);
                    recommendPO.setMiniId(params.getMiniId());
                    recommendPO.setCreatorId(params.getCreatorId());
                    recommendPO.setCreateTime(nowDate);
                    recommendPO.setShowDrama(1);
                    recommendPO.setContVersionId(params.getContVersionId());

                    if (recommendPO.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                        recommendPO.setDramaIds(allIds);
                    }
                    miniRecommendMapper.insertSelective(recommendPO);

                    recommendPO = miniRecommendMapper.queryOne(recommendQuery);
                }
                if (recommendPO == null) {
                    throw new MyException("初始化数据失败");
                }
                List<FastDramaVO> dramaList = getDramaListNoHideRedis(recommendPO.getDramaIds(), params.getRetailId());
                recommendPO.setDramaList(dramaList);
                if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                    // 升级接口==推荐1
                    recommendPO.setDramaList(null);
                    List<Integer> dramaIdList = new ArrayList<>();
                    for (FastDramaVO drama : dramaList) {
                        dramaIdList.add(drama.getId());
                    }
                    recommendPO.setDramaIdList(dramaIdList);
                }
                // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                if (recommendPO.getShowDrama() == 2) {
                    List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recommendPO.getTagIds());
                    recommendPO.setTagList(tagList);
                }
                if (recommendPO.getShowDrama() == 3) {
                    List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recommendPO.getCornerIds());
                    recommendPO.setCornerList(cornerList);
                }
                po.setRecommend(recommendPO);
            }
            {
                // 内容推荐-底导航
                if (po.getType() == 1) {
                    // log.info("查询小程序配置信息缓存为空,初始化底导航推荐区");
                    FastMiniRecommendPO recommendQuery = new FastMiniRecommendPO();
                    recommendQuery.setMiniId(params.getMiniId());
                    recommendQuery.setGroupType(7);
                    recommendQuery.setContVersionId(params.getContVersionId());
                    FastMiniRecommendPO recommendPO = miniRecommendMapper.queryOne(recommendQuery);
                    if (recommendPO == null) {
                        recommendPO = new FastMiniRecommendPO();
                        recommendPO.setGroupType(7);
                        recommendPO.setMiniId(params.getMiniId());
                        recommendPO.setCreatorId(params.getCreatorId());
                        recommendPO.setCreateTime(nowDate);
                        recommendPO.setShowDrama(1);
                        if (recommendPO.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                            recommendPO.setDramaIds(allIds);
                        }
                        recommendPO.setContVersionId(params.getContVersionId());
                        miniRecommendMapper.insertSelective(recommendPO);
                        recommendPO = miniRecommendMapper.queryOne(recommendQuery);
                    }
                    if (recommendPO == null) {
                        throw new MyException("初始化数据失败");
                    }
                    List<FastDramaVO> dramaList = getDramaListNoHideRedis(recommendPO.getDramaIds(), params.getRetailId());
                    recommendPO.setDramaList(dramaList);
                    if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                        // 升级接口==推荐1
                        recommendPO.setDramaList(null);
                        List<Integer> dramaIdList = new ArrayList<>();
                        for (FastDramaVO drama : dramaList) {
                            dramaIdList.add(drama.getId());
                        }
                        recommendPO.setDramaIdList(dramaIdList);
                    }
                    // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                    if (recommendPO.getShowDrama() == 2) {
                        List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recommendPO.getTagIds());
                        recommendPO.setTagList(tagList);
                    }
                    if (recommendPO.getShowDrama() == 3) {
                        List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recommendPO.getCornerIds());
                        recommendPO.setCornerList(cornerList);
                    }
                    po.setRecommendBottom(recommendPO);
                }
            }
            {
                // 内容推荐-分用户组
                FastMiniRecommendPO recommendQuery = new FastMiniRecommendPO();
                recommendQuery.setMiniId(params.getMiniId());
                recommendQuery.setGroupType(2);
                recommendQuery.setContVersionId(params.getContVersionId());
                FastMiniRecommendPO recommendGroup = miniRecommendMapper.queryOne(recommendQuery);
                if (recommendGroup == null) {
                    recommendGroup = new FastMiniRecommendPO();
                    recommendGroup.setGroupType(2);
                    recommendGroup.setMiniId(params.getMiniId());
                    recommendGroup.setCreatorId(params.getCreatorId());
                    recommendGroup.setCreateTime(nowDate);
                    recommendGroup.setShowDrama(1);

                    if (recommendGroup.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                        recommendGroup.setDramaIds(allIds);
                    }
                    recommendGroup.setContVersionId(params.getContVersionId());
                    miniRecommendMapper.insertSelective(recommendGroup);

                    recommendGroup = miniRecommendMapper.queryOne(recommendQuery);
                }
                if (recommendGroup == null) {
                    throw new MyException("初始化数据失败");
                }
//                recommendGroup.setDramaList(getDramaListNoHideRedis(recommendGroup.getDramaIds(), params.getRetailId()));
                List<FastDramaVO> dramaList = getDramaListNoHideRedis(recommendGroup.getDramaIds(), params.getRetailId());
                recommendGroup.setDramaList(dramaList);
                if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                    // 升级接口==推荐组
                    recommendGroup.setDramaList(null);
                    List<Integer> dramaIdList = new ArrayList<>();
                    for (FastDramaVO drama : dramaList) {
                        dramaIdList.add(drama.getId());
                    }
                    recommendGroup.setDramaIdList(dramaIdList);
                }
                // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                if (recommendGroup.getShowDrama() == 2) {
                    List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recommendGroup.getTagIds());
                    recommendGroup.setTagList(tagList);
                }
                if (recommendGroup.getShowDrama() == 3) {
                    List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recommendGroup.getCornerIds());
                    recommendGroup.setCornerList(cornerList);
                }
                po.setRecommendGroup(recommendGroup);
            }

            {
                // 内容推荐-全局
                FastMiniRecommendPO recommendQuery = new FastMiniRecommendPO();
                recommendQuery.setMiniId(params.getMiniId());
                recommendQuery.setGroupType(3);
                recommendQuery.setContVersionId(params.getContVersionId());
                FastMiniRecommendPO recommendSuper = miniRecommendMapper.queryOne(recommendQuery);
                // 快应用精品推荐
                if ((miniPO.getType() == 5 || miniPO.getType() == 7) && recommendSuper == null) {
                    recommendSuper = new FastMiniRecommendPO();
                    recommendSuper.setGroupType(3);
                    recommendSuper.setMiniId(params.getMiniId());
                    recommendSuper.setCreatorId(params.getCreatorId());
                    recommendSuper.setCreateTime(nowDate);
                    recommendSuper.setShowDrama(1);

                    if (recommendSuper.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                        recommendSuper.setDramaIds(allIds);
                    }
                    recommendSuper.setContVersionId(params.getContVersionId());
                    miniRecommendMapper.insertSelective(recommendSuper);
                    recommendSuper = miniRecommendMapper.queryOne(recommendQuery);
                }
                if (recommendSuper != null) {
                    List<FastDramaVO> dramaList = getDramaListNoHideRedis(recommendSuper.getDramaIds(), params.getRetailId());
                    recommendSuper.setDramaList(dramaList);
                    if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                        // 升级接口
                        recommendSuper.setDramaList(null);
                        List<Integer> dramaIdList = new ArrayList<>();
                        for (FastDramaVO drama : dramaList) {
                            dramaIdList.add(drama.getId());
                        }
                        recommendSuper.setDramaIdList(dramaIdList);
                    }
                    // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                    if (recommendSuper.getShowDrama() == 2) {
                        List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recommendSuper.getTagIds());
                        recommendSuper.setTagList(tagList);
                    }
                    if (recommendSuper.getShowDrama() == 3) {
                        List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recommendSuper.getCornerIds());
                        recommendSuper.setCornerList(cornerList);
                    }
                    po.setRecommendSuper(recommendSuper);
                }
                if (miniPO.getType() == 5 || miniPO.getType() == 7) {
                    // 快应用v2-小剧场列表
                    FastMiniRecommendPO recTheaterQuery = new FastMiniRecommendPO();
                    recTheaterQuery.setMiniId(params.getMiniId());
                    recTheaterQuery.setGroupType(4);
                    recTheaterQuery.setContVersionId(params.getContVersionId());
                    List<FastMiniRecommendPO> recTheaterList = miniRecommendMapper.queryList(recTheaterQuery);
                    if (recTheaterList == null || recTheaterList.size() == 0) {
                        insertRecommond(4, 2, 2, params.getMiniId(), params.getCreatorId(), allIds, params.getContVersionId());    // 华为-自然
                        insertRecommond(4, 2, 1, params.getMiniId(), params.getCreatorId(), allIds, params.getContVersionId());    // 华为-渠道
                        insertRecommond(4, 1, 2, params.getMiniId(), params.getCreatorId(), allIds, params.getContVersionId());    // 联盟-自然
                        insertRecommond(4, 1, 1, params.getMiniId(), params.getCreatorId(), allIds, params.getContVersionId());    // 联盟-渠道
                        recTheaterList = miniRecommendMapper.queryList(recTheaterQuery);
                    }
                    for (FastMiniRecommendPO recItem : recTheaterList) {
                        List<FastDramaVO> dramaList = getDramaListNoHideRedis(recItem.getDramaIds(), params.getRetailId());
                        recItem.setDramaList(dramaList);
                        if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                            // 升级接口
                            recItem.setDramaList(null);
                            List<Integer> dramaIdList = new ArrayList<>();
                            for (FastDramaVO drama : dramaList) {
                                dramaIdList.add(drama.getId());
                            }
                            recItem.setDramaIdList(dramaIdList);
                        }
                        // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                        if (recItem.getShowDrama() == 2) {
                            List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recItem.getTagIds());
                            recItem.setTagList(tagList);
                        }
                        if (recItem.getShowDrama() == 3) {
                            List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recItem.getCornerIds());
                            recItem.setCornerList(cornerList);
                        }
                    }
                    po.setRecommendTheaterList(recTheaterList);

                    // 快应用v2-精选列表
                    FastMiniRecommendPO recSuperQuery = new FastMiniRecommendPO();
                    recSuperQuery.setMiniId(params.getMiniId());
                    recSuperQuery.setGroupType(5);
                    recSuperQuery.setContVersionId(params.getContVersionId());
                    List<FastMiniRecommendPO> recSuperList = miniRecommendMapper.queryList(recSuperQuery);
                    if (recSuperList == null || recSuperList.size() == 0) {
                        insertRecommond(5, 2, 2, params.getMiniId(), params.getCreatorId(), allIds, params.getContVersionId());  // 华为-自然
                        insertRecommond(5, 2, 1, params.getMiniId(), params.getCreatorId(), allIds, params.getContVersionId());  // 华为-渠道
                        insertRecommond(5, 1, 2, params.getMiniId(), params.getCreatorId(), allIds, params.getContVersionId());  // 联盟-自然
                        insertRecommond(5, 1, 1, params.getMiniId(), params.getCreatorId(), allIds, params.getContVersionId());  // 联盟-渠道
                        recSuperList = miniRecommendMapper.queryList(recSuperQuery);
                    }
                    for (FastMiniRecommendPO recItem : recSuperList) {
                        List<FastDramaVO> dramaList = getDramaListNoHideRedis(recItem.getDramaIds(), params.getRetailId());
                        recItem.setDramaList(dramaList);
                        if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                            // 升级接口
                            recItem.setDramaList(null);
                            List<Integer> dramaIdList = new ArrayList<>();
                            for (FastDramaVO drama : dramaList) {
                                dramaIdList.add(drama.getId());
                            }
                            recItem.setDramaIdList(dramaIdList);
                        }
                        // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                        if (recItem.getShowDrama() == 2) {
                            List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recItem.getTagIds());
                            recItem.setTagList(tagList);
                        }
                        if (recItem.getShowDrama() == 3) {
                            List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recItem.getCornerIds());
                            recItem.setCornerList(cornerList);
                        }
                    }
                    po.setRecommendSuperList(recSuperList);

                    // 内容推荐-青少年模式
                    FastMiniRecommendPO recommendYoungQuery = new FastMiniRecommendPO();
                    recommendYoungQuery.setMiniId(params.getMiniId());
                    recommendYoungQuery.setGroupType(6);
                    recommendYoungQuery.setContVersionId(params.getContVersionId());
                    FastMiniRecommendPO recommendYoungPO = miniRecommendMapper.queryOne(recommendYoungQuery);
                    if (recommendYoungPO == null) {
                        recommendYoungPO = new FastMiniRecommendPO();
                        recommendYoungPO.setGroupType(6);
                        recommendYoungPO.setMiniId(params.getMiniId());
                        recommendYoungPO.setCreatorId(params.getCreatorId());
                        recommendYoungPO.setCreateTime(nowDate);
                        recommendYoungPO.setShowDrama(1);

                        if (recommendYoungPO.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                            recommendYoungPO.setDramaIds(allIds);
                        }
                        recommendYoungPO.setContVersionId(params.getContVersionId());
                        miniRecommendMapper.insertSelective(recommendYoungPO);

                        recommendYoungPO = miniRecommendMapper.queryOne(recommendQuery);
                    }
                    if (recommendYoungPO == null) {
                        throw new MyException("初始化数据失败");
                    }
                    List<FastDramaVO> dramaList = getDramaListNoHideRedis(recommendYoungPO.getDramaIds(), params.getRetailId());
                    recommendYoungPO.setDramaList(dramaList);
                    if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
                        // 升级接口==推荐1
                        recommendYoungPO.setDramaList(null);
                        List<Integer> dramaIdList = new ArrayList<>();
                        for (FastDramaVO drama : dramaList) {
                            dramaIdList.add(drama.getId());
                        }
                        recommendYoungPO.setDramaIdList(dramaIdList);
                    }
                    // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                    if (recommendYoungPO.getShowDrama() == 2) {
                        List<FastTagVO> tagList = fastTagService.queryInfoByRedis(recommendYoungPO.getTagIds());
                        recommendYoungPO.setTagList(tagList);
                    }
                    if (recommendYoungPO.getShowDrama() == 3) {
                        List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(recommendYoungPO.getCornerIds());
                        recommendYoungPO.setCornerList(cornerList);
                    }
                    po.setRecommendYoung(recommendYoungPO);
                }

            }
        }
        // 推荐区-模块
        if (po.getContentType() == 2) {
            {
                FastMiniRecommendModularPO recommendQuery = new FastMiniRecommendModularPO();
                recommendQuery.setMiniId(params.getMiniId());
                recommendQuery.setGroupType(1);
                List<FastMiniRecommendModularPO> modularList = recommendModularMapper.queryList(recommendQuery);
                if (CollUtil.hasContent(modularList)) {
                    for (FastMiniRecommendModularPO modular : modularList) {
                        modular.setDramaList(getDramaListNoHideRedis(modular.getDramaIds(), params.getRetailId()));

                        // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                        if (modular.getShowDrama() == 2) {
                            List<FastTagVO> tagList = fastTagService.queryInfoByRedis(modular.getTagIds());
                            modular.setTagList(tagList);
                        }
                        if (modular.getShowDrama() == 3) {
                            List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(modular.getCornerIds());
                            modular.setCornerList(cornerList);
                        }
                    }
                }

                po.setRecommendModularList(modularList);
            }
            {
                FastMiniRecommendModularPO recommendQuery = new FastMiniRecommendModularPO();
                recommendQuery.setMiniId(params.getMiniId());
                recommendQuery.setGroupType(2);
                List<FastMiniRecommendModularPO> modularList = recommendModularMapper.queryList(recommendQuery);
                if (CollUtil.hasContent(modularList)) {
                    for (FastMiniRecommendModularPO modular : modularList) {
                        modular.setDramaList(getDramaListNoHideRedis(modular.getDramaIds(), params.getRetailId()));

                        // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                        if (modular.getShowDrama() == 2) {
                            List<FastTagVO> tagList = fastTagService.queryInfoByRedis(modular.getTagIds());
                            modular.setTagList(tagList);
                        }
                        if (modular.getShowDrama() == 3) {
                            List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(modular.getCornerIds());
                            modular.setCornerList(cornerList);
                        }
                    }
                }

                po.setRecommendModularListGroup(modularList);
            }
            {
                FastMiniVideoModularPO videoModularPO = new FastMiniVideoModularPO();
                videoModularPO.setMiniId(params.getMiniId());
                List<FastMiniVideoModularPO> videoModularList = videoModularMapper.queryList(videoModularPO);
                if (CollUtil.hasContent(videoModularList)) {
                    for (FastMiniVideoModularPO video : videoModularList) {
                        video.setVideoList(videoService.queryInfoByRedis(video.getVideoIds()));
                    }
                }

                po.setVideoModularList(videoModularList);
            }
        }
        // 推荐区-模块
        if (po.getContentType() == 3) {
            {
                FastMiniRecommendModularPO recommendQuery = new FastMiniRecommendModularPO();
                recommendQuery.setMiniId(params.getMiniId());
                recommendQuery.setGroupType(1);
                List<FastMiniRecommendModularPO> modularList = recommendModularMapper.queryList(recommendQuery);
                if (CollUtil.hasContent(modularList)) {
                    for (FastMiniRecommendModularPO modular : modularList) {
                        modular.setDramaList(getDramaListNoHideRedis(modular.getDramaIds(), params.getRetailId()));

                        // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                        if (modular.getShowDrama() == 2) {
                            List<FastTagVO> tagList = fastTagService.queryInfoByRedis(modular.getTagIds());
                            modular.setTagList(tagList);
                        }
                        if (modular.getShowDrama() == 3) {
                            List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(modular.getCornerIds());
                            modular.setCornerList(cornerList);
                        }
                    }
                }

                po.setRecommendModularList(modularList);
            }
            {
                FastMiniRecommendModularPO recommendQuery = new FastMiniRecommendModularPO();
                recommendQuery.setMiniId(params.getMiniId());
                recommendQuery.setGroupType(2);
                List<FastMiniRecommendModularPO> modularList = recommendModularMapper.queryList(recommendQuery);
                if (CollUtil.hasContent(modularList)) {
                    for (FastMiniRecommendModularPO modular : modularList) {
                        modular.setDramaList(getDramaListNoHideRedis(modular.getDramaIds(), params.getRetailId()));

                        // 展示剧目:1=全部;2=标签;3=角标;0=自定义选择
                        if (modular.getShowDrama() == 2) {
                            List<FastTagVO> tagList = fastTagService.queryInfoByRedis(modular.getTagIds());
                            modular.setTagList(tagList);
                        }
                        if (modular.getShowDrama() == 3) {
                            List<FastCornerVO> cornerList = fastCornerService.queryInfoByRedis(modular.getCornerIds());
                            modular.setCornerList(cornerList);
                        }
                    }
                }

                po.setRecommendModularListGroup(modularList);
            }
            {
                FastMiniVideoModularPO videoModularPO = new FastMiniVideoModularPO();
                videoModularPO.setMiniId(params.getMiniId());
                List<FastMiniVideoModularPO> videoModularList = videoModularMapper.queryList(videoModularPO);
                if (CollUtil.hasContent(videoModularList)) {
                    for (FastMiniVideoModularPO video : videoModularList) {
                        video.setVideoList(videoService.queryInfoByRedis(video.getVideoIds()));
                    }
                }

                po.setVideoModularList(videoModularList);
            }
        }
        // 底部导航
        FastMiniBottomPO bottom = new FastMiniBottomPO();
        bottom.setMiniId(params.getMiniId());
        List<FastMiniBottomPO> bottomList = fastMiniBottomMapper.queryList(bottom);
        if (CollUtil.isEmpty(bottomList)) {
            // 初始化数据
            bottom.setMiniId(0);
            bottom.setContentType(po.getContentType());
            bottom.setMiniType(0);
            if (miniPO.getType() == 5) {
                // 快应用
                bottom.setMiniId(-1); // 和线上数据并行兼容，线上是0，如果也为0，导致线上的数据变多
                bottom.setMiniType(5);
            }
            if (miniPO.getType() == 7) {
                // APP应用
                bottom.setMiniId(-2); // 和线上数据并行兼容，线上是0，如果也为0，导致线上的数据变多
                bottom.setMiniType(7);
            }
            bottomList = fastMiniBottomMapper.queryList(bottom);
            if (CollUtil.hasContent(bottomList)) {
                int i = 1;
                for (FastMiniBottomPO cur : bottomList) {
                    cur.setContentType(po.getContentType());
                    cur.setCreatorId(params.getCreatorId());
                    cur.setCreateTime(nowDate);
                    cur.setMiniId(params.getMiniId());
                    cur.setSequence(i);
                    i++;
                    fastMiniBottomMapper.insertSelective(cur);
                }
                if (miniPO.getType() == 2 || miniPO.getType() == 1) {
                    // 抖音小程序，增加定制底部导航
                    FastMiniBottomPO mbPO = new FastMiniBottomPO();
                    mbPO.setContentType(po.getContentType());
                    mbPO.setCreatorId(params.getCreatorId());
                    mbPO.setCreateTime(nowDate);
                    mbPO.setMiniId(params.getMiniId());
                    mbPO.setSequence(35);
                    mbPO.setType(6);
                    mbPO.setName("福利");
                    mbPO.setOpenState(0);// 默认关闭    
                    fastMiniBottomMapper.insertSelective(mbPO);
                    bottomList.add(mbPO);
                }
            }
        }
        po.setBottomList(bottomList);
        // 壁纸
        FastMiniWallpaperPO wp = new FastMiniWallpaperPO();
        wp.setMiniId(params.getMiniId());
        List<FastMiniWallpaperPO> wpList = fastMiniWallpaperMapper.queryMiniWallpeperList(wp);
        po.setWallpaperList(wpList);
        // 剧情介绍
        FastMiniIntroductionPO introParam = new FastMiniIntroductionPO();
        introParam.setMiniId(params.getMiniId());
        introParam.setRetailId(params.getRetailId());
        List<FastMiniIntroductionPO> introList = fastMiniIntroductionMapper.queryAllList(introParam);
        for (FastMiniIntroductionPO curItem : introList) {
            curItem.setDramaCover(aliCdnService.getImgFullUrl(curItem.getDramaCover()));
        }
        po.setIntroductionList(introList);
        // 增加设置
        FastSettingCommonPO cParam = new FastSettingCommonPO();
        cParam.setAppType(1);
        if (params.getType() != null && params.getType() == 5) {
            cParam.setAppType(5);
        }
        FastSettingCommonPO cPO = settingCommonMapper.queryOne(cParam);
        if (cPO != null) {
            po.setCostPopFlag(cPO.getCostPopFlag());
            po.setRechargeCheckFlag(cPO.getRechargeCheckFlag());
        } else {
            // 默认勾选上
            po.setCostPopFlag(1);
            po.setRechargeCheckFlag(1);
        }
        // 小程序福利任务设置
        FastSettingTaskPO taskPO = fastSettingTaskService.getSettingTaskRedis(po.getMiniId());
        po.setTaskPO(taskPO);

        // 增加全剧列表
        if (params.getPlusFlag() != null && params.getPlusFlag() == 1) {
            Map<Integer, MiniDramaVO> retailDramaMap = fastDramaService.getRetailDramaAllMap(params.getRetailId());
            po.setRetailDramaMap(retailDramaMap);
        }

        // 剧单列表
        po.setPlaylists(fastMiniPlaylistService.queryListByMiniId(params.getMiniId(), params.getContVersionId(), true));

        actionLogService.log("mini_get_setting", "mini_id=" + miniPO.getId() + ",key=" + key);
        RedisUtil.set(key, JsonUtil.toString(po), RedisUtil.TIME_5D);

        return po;
    }

    public void insertRecommond(Integer groupType, Integer tabOne, Integer tabTwo, Integer miniId, Integer creatorId, String allIds, Integer contVersionId) {
        FastMiniRecommendPO rPO = new FastMiniRecommendPO();
        rPO.setGroupType(groupType);
        rPO.setMiniId(miniId);
        rPO.setCreatorId(creatorId);
        rPO.setCreateTime(DateUtil.getNowDate());
        rPO.setTabOne(tabOne);
        rPO.setTabTwo(tabTwo);
        rPO.setShowDrama(1);
        if (rPO.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
            rPO.setDramaIds(allIds);
        }
        rPO.setContVersionId(contVersionId);
        miniRecommendMapper.insertSelective(rPO);
    }

    /**
     * 根据短剧id获取短剧列表-去除下架的和隐藏的-去除没有授权的
     *
     * @param dramaIds 短剧id
     * @param retailId 分销商id
     * @return
     */
    private List<FastDramaVO> getDramaListNoHideRedis(String dramaIds, Integer retailId) {
        if (StrUtil.isEmpty(dramaIds)) {
            return new ArrayList<>();
        }
        String key = StaticVar.RETAIL_REC_DRAMA_CLIENT + retailId + "_" + Md5Util.getMD5(dramaIds);
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            List<FastDramaVO> dramaList = getDramaListNoHideOrg(dramaIds, retailId);
            res = JsonUtil.toString(dramaList);
            RedisUtil.set(key, res, 60 * 5);
        }
        List<FastDramaVO> vos = JsonUtil.toList(res, FastDramaVO.class);
        if (CollUtil.isEmpty(vos)) {
            List<FastDramaVO> dramaList = getDramaListNoHideOrg(dramaIds, retailId);
            res = JsonUtil.toString(dramaList);
            RedisUtil.set(key, res, 60 * 5);
        }
        if (CollUtil.isNotEmpty(vos)) {
            vos.forEach(vo -> {
                if (notEmpty(vo.getTagIds())) {
                    vo.setTagNameList(fastTagService.queryInfoByRedis(vo.getTagIds()));
                }
                if (vo.getCornerId() != null) {
                    vo.setCorner(fastCornerService.queryInfoByRedis(vo.getCornerId()));
                }
                // i18n
                fastDramaService.fillI18n(vo);
            });
        }
        return vos;
    }

    /**
     * 根据短剧id获取短剧列表-去除下架的和隐藏的-去除没有授权的
     *
     * @param dramaIds 短剧id
     * @param retailId 分销商id
     * @return
     */
    private List<FastDramaVO> getDramaListNoHideOrg(String dramaIds, Integer retailId) {
        // dramaIds去重
        if (dramaIds != null && dramaIds.length() > 100) {
            dramaIds = StrUtil.duplicateRemove(dramaIds);
        }
        List<FastDramaVO> dramaList = fastDramaService.queryInfoByRedis(dramaIds);
        // 查询分销商已授权的短剧id
        List<Integer> dramaIdHas = dramaRetailService.queryInfoByRedis(retailId);
        if (CollUtil.isEmpty(dramaIdHas)) {
            if (retailId != null && retailId > 0) {
                return new ArrayList<>();
            }
        }
        if (CollUtil.hasContent(dramaList)) {
            for (int i = dramaList.size() - 1; i >= 0; i--) {
                FastDramaVO vo = dramaList.get(i);
                if (vo.getOpenState() == 0 || vo.getShelfState() == 0) {
                    dramaList.remove(i);
                } else if (retailId != null && retailId > 0) {
                    if (!dramaIdHas.contains(vo.getId())) {
                        dramaList.remove(i);
                    }
                }
                // i18n
                fastDramaService.fillI18n(vo);
            }
        }
        return dramaList;
    }

    /**
     * 根据小程序id获取在小程序配置的短剧id
     *
     * @param miniId 小程序id
     * @return
     */
    public List<Integer> getMiniSettingDramaIds(Integer miniId, Integer cvid) {
        List<Integer> dramaIds = new ArrayList<>();
        String key = StaticVar.MINI_SETTING_DRAMA_IDS + miniId + ":" + cvid;
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            dramaIds = CollUtil.parseIntStr2List(value);
        } else {
            {
                // 推荐区
                FastMiniRecommendPO recommendQuery = new FastMiniRecommendPO();
                recommendQuery.setMiniId(miniId);
                FastMiniRecommendPO recommendPO = miniRecommendMapper.queryOne(recommendQuery);
                if (recommendPO != null && notEmpty(recommendPO.getDramaIds())) {
                    List<Integer> list = CollUtil.parseIntStr2List(recommendPO.getDramaIds());
                    if (list != null) {
                        dramaIds.addAll(list);
                    }
                }
            }
            {
                // 功能入口
                FastMiniAccessPO accessQuery = new FastMiniAccessPO();
                accessQuery.setMiniId(miniId);
                List<FastMiniAccessPO> accessList = fastMiniAccessMapper.queryList(accessQuery);
                if (CollUtil.hasContent(accessList)) {
                    for (FastMiniAccessPO cur : accessList) {
                        if (notEmpty(cur.getDramaIds())) {
                            List<Integer> list = CollUtil.parseIntStr2List(cur.getDramaIds());
                            if (list != null) {
                                dramaIds.addAll(list);
                            }
                        }
                    }
                }
            }
            if (CollUtil.isEmpty(dramaIds)) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                RedisUtil.set(key, StrUtil.join(dramaIds), RedisUtil.TIME_2D);
            }
        }
        return dramaIds;
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateVirtualPay(FastMiniSettingPO params) {
        Date nowDate = DateUtil.getNowDate();
        params.setUpdateTime(nowDate);
        if (fastMiniSettingMapper.updateByMiniId(params) == 0) {
            transactionRollBack();
            throw new MyException(StaticStr.UPDATE_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(SessionVO sessionVO, FastMiniSettingPO params, Integer notUpdateIos) {
        Date nowDate = DateUtil.getNowDate();
        params.setUpdateTime(nowDate);
        if (notUpdateIos != null && notUpdateIos == 1) {
            // 不更新ios设置相关
            params.setOpenIosCharge1(null);
            params.setOpenIosCharge2(null);
            params.setOpenIosCharge3(null);
            params.setOpenIosTime1(null);
            params.setOpenIosTime2(null);
            params.setOpenIosTime3(null);
        }
        if (fastMiniSettingMapper.updateByMiniId(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }

        // settingGroup
        FastMiniSettingGroupPO settingGroup = params.getSettingGroup();
        if (settingGroup == null) {
            throw new MyException("settingGroup not null");
        }
        settingGroup.setGroupType(1);
        settingGroup.setMiniId(params.getMiniId());
        if (settingGroupMapper.updateByMiniId(settingGroup) == 0) {
            transactionRollBack("更新失败1");
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }

        // settingGroupBanner
        FastMiniSettingGroupPO settingGroupBanner = params.getSettingGroupBanner();
        if (settingGroupBanner == null) {
            throw new MyException("settingGroup not null");
        }
        settingGroupBanner.setGroupType(2);
        settingGroupBanner.setMiniId(params.getMiniId());
        if (settingGroupMapper.updateByMiniId(settingGroupBanner) == 0) {
            transactionRollBack("更新失败2");
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }

        FastMiniPO miniParam = new FastMiniPO();
        miniParam.setId(params.getMiniId());
        FastMiniPO miniPO = fastMiniMapper.queryById(miniParam);

        // banner-默认-全局
        {
            FastMiniBannerPO banner = new FastMiniBannerPO();
            banner.setMiniId(params.getMiniId());
            banner.setType(1);
            banner.setGroupType(1);
            fastMiniBannerMapper.deleteByMiniId(banner);
            List<FastMiniBannerPO> bannerList = params.getBannerList();
            if (CollUtil.hasContent(bannerList)) {
                if (bannerList.size() > 5) {
                    transactionRollBack("banner不能超过5张图");
                    throw new MyException("banner不能超过5张图");
                }
                for (FastMiniBannerPO cur : bannerList) {
                    cur.setCreatorId(params.getCreatorId());
                    cur.setCreateTime(nowDate);
                    cur.setMiniId(params.getMiniId());
                    cur.setDelFlag(StaticVar.NO);
                    cur.setType(1);
                    cur.setGroupType(1);// 全局

                    fastMiniBannerMapper.insertSelective(cur);
                }
            }
        }
        // banner-默认-用户组
        {
            FastMiniBannerPO banner = new FastMiniBannerPO();
            banner.setMiniId(params.getMiniId());
            banner.setType(1);
            banner.setGroupType(2);
            fastMiniBannerMapper.deleteByMiniId(banner);
            List<FastMiniBannerPO> bannerList = params.getBannerListGroup();
            if (CollUtil.hasContent(bannerList)) {
                if (bannerList.size() > 5) {
                    throw new MyException("banner不能超过5张图");
                }
                for (FastMiniBannerPO cur : bannerList) {
                    cur.setCreatorId(params.getCreatorId());
                    cur.setCreateTime(nowDate);
                    cur.setMiniId(params.getMiniId());
                    cur.setDelFlag(StaticVar.NO);
                    cur.setType(1);
                    cur.setGroupType(2);// 用户组

                    fastMiniBannerMapper.insertSelective(cur);
                }
            }
        }

        // banner-发现
        {
            FastMiniBannerPO banner = new FastMiniBannerPO();
            banner.setMiniId(params.getMiniId());
            banner.setType(2);
            fastMiniBannerMapper.deleteByMiniId(banner);
            List<FastMiniBannerPO> bannerList = params.getBannerFindList();
            if (CollUtil.hasContent(bannerList)) {
                if (bannerList.size() > 5) {
                    throw new MyException("banner不能超过5张图");
                }
                for (FastMiniBannerPO cur : bannerList) {
                    cur.setCreatorId(params.getCreatorId());
                    cur.setCreateTime(nowDate);
                    cur.setMiniId(params.getMiniId());
                    cur.setDelFlag(StaticVar.NO);
                    cur.setType(2);

                    fastMiniBannerMapper.insertSelective(cur);
                }
            }
        }

        FastDramaPO dramaPO = new FastDramaPO();
        dramaPO.setDelFlag(StaticVar.NO);
        dramaPO.setShelfState(1);
        dramaPO.setContentType(miniPO.getContentType());
        List<Integer> allIdList = fastDramaService.queryAllIds(dramaPO);
        String allIds = StrUtil.join(allIdList);

        // 功能入口
        FastMiniAccessPO access = new FastMiniAccessPO();
        access.setMiniId(params.getMiniId());
        fastMiniAccessMapper.deleteByMiniId(access);
        List<FastMiniAccessPO> accessList = params.getAccessList();
        // if (CollUtil.isEmpty(accessList) || accessList.size() != 3) {
        //     throw new MyException("accessList数据不合法");
        // }
        if (CollUtil.hasContent(accessList)) {
            for (FastMiniAccessPO cur : accessList) {
                cur.setCreatorId(params.getCreatorId());
                cur.setCreateTime(nowDate);
                cur.setMiniId(params.getMiniId());
                cur.setDelFlag(StaticVar.NO);
                cur.setOpenState(StaticVar.YES);

                if (isBlank(cur.getDramaIds()) && cur.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                    cur.setDramaIds(allIds);
                }
                fastMiniAccessMapper.insertSelective(cur);
            }
        }

        // 推荐区（短剧）
        if (miniPO.getContentType() == 1 || miniPO.getContentType() == 4) {

            {    // 推荐-全局默认推荐
                FastMiniRecommendPO recommend = params.getRecommend();
                if (recommend == null) {
                    recommend = new FastMiniRecommendPO();
                    recommend.setGroupType(1);
                    recommend.setMiniId(params.getMiniId());
                    recommend.setCreatorId(params.getCreatorId());
                    recommend.setCreateTime(nowDate);
                    recommend.setContVersionId(params.getContVersionId());
                    miniRecommendMapper.insertSelective(recommend);

                    FastMiniRecommendPO recommendQuery = new FastMiniRecommendPO();
                    recommendQuery.setMiniId(params.getMiniId());
                    recommendQuery.setGroupType(1);
                    recommend = miniRecommendMapper.queryOne(recommendQuery);
                }
                if (recommend == null) {
                    throw new MyException("初始化数据失败");
                }
                recommend.setGroupType(1);
                recommend.setShowDrama(params.getRecommend().getShowDrama());
                recommend.setMiniId(params.getMiniId());
                recommend.setUpdateTime(nowDate);
                recommend.setUpdatorId(params.getUpdatorId());
                recommend.setCornerIds(params.getRecommend().getCornerIds());
                if (isBlank(recommend.getDramaIds()) && Objects.nonNull(params.getRecommend().getShowDrama()) && params.getRecommend().getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                    recommend.setDramaIds(allIds);
                }
                // dramaIds去重
                String dramasIds = recommend.getDramaIds();
                if (StrUtil.isNotEmpty(dramasIds) && dramasIds.length() > 50) {
                    dramasIds = StrUtil.duplicateRemove(dramasIds);
                    recommend.setDramaIds(dramasIds);
                }
                recommend.setContVersionId(params.getContVersionId());
                if (miniRecommendMapper.updateByMiniId(recommend) == 0) {
                    transactionRollBack("更新失败3");
                    return MethodVO.error(StaticStr.UPDATE_FAILED);
                }
            }
            {
                // 短剧推荐-用户分组推荐
                FastMiniRecommendPO recommendGroup = params.getRecommendGroup();
                if (recommendGroup == null) {
                    recommendGroup = new FastMiniRecommendPO();
                    recommendGroup.setGroupType(2);
                    recommendGroup.setMiniId(params.getMiniId());
                    recommendGroup.setCreatorId(params.getCreatorId());
                    recommendGroup.setCreateTime(nowDate);
                    recommendGroup.setContVersionId(params.getContVersionId());
                    miniRecommendMapper.insertSelective(recommendGroup);

                    FastMiniRecommendPO recommendQuery = new FastMiniRecommendPO();
                    recommendQuery.setMiniId(params.getMiniId());
                    recommendQuery.setGroupType(2);
                    recommendGroup = miniRecommendMapper.queryOne(recommendQuery);
                }
                if (recommendGroup == null) {
                    throw new MyException("初始化数据失败");
                }
                recommendGroup.setGroupType(2);
                recommendGroup.setShowDrama(params.getRecommendGroup().getShowDrama());
                recommendGroup.setMiniId(params.getMiniId());
                recommendGroup.setUpdateTime(nowDate);
                recommendGroup.setUpdatorId(params.getUpdatorId());

                if (isBlank(recommendGroup.getDramaIds()) && Objects.nonNull(params.getRecommendGroup().getShowDrama()) && params.getRecommendGroup().getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                    recommendGroup.setDramaIds(allIds);
                }
                // dramaIds去重
                String dramasIds = recommendGroup.getDramaIds();
                if (StrUtil.isNotEmpty(dramasIds) && dramasIds.length() > 50) {
                    dramasIds = StrUtil.duplicateRemove(dramasIds);
                    recommendGroup.setDramaIds(dramasIds);
                }
                recommendGroup.setContVersionId(params.getContVersionId());
                if (miniRecommendMapper.updateByMiniId(recommendGroup) == 0) {
                    transactionRollBack("更新失败4");
                    return MethodVO.error(StaticStr.UPDATE_FAILED);
                }
            }
            {
                // 短剧推荐-底导航推荐区
                FastMiniRecommendPO recommendBottom = params.getRecommendBottom();
                if (recommendBottom == null) {
                    recommendBottom = new FastMiniRecommendPO();
                    recommendBottom.setGroupType(7);
                    recommendBottom.setMiniId(params.getMiniId());
                    recommendBottom.setCreatorId(params.getCreatorId());
                    recommendBottom.setCreateTime(nowDate);
                    recommendBottom.setShowDrama(1);
                    recommendBottom.setContVersionId(params.getContVersionId());
                    miniRecommendMapper.insertSelective(recommendBottom);

                    FastMiniRecommendPO recommendQuery = new FastMiniRecommendPO();
                    recommendQuery.setMiniId(params.getMiniId());
                    recommendQuery.setGroupType(7);
                    recommendBottom = miniRecommendMapper.queryOne(recommendQuery);
                }
                if (recommendBottom == null) {
                    throw new MyException("初始化数据失败");
                }
                recommendBottom.setGroupType(7);
                recommendBottom.setShowDrama(recommendBottom.getShowDrama());
                recommendBottom.setMiniId(params.getMiniId());
                recommendBottom.setUpdateTime(nowDate);
                recommendBottom.setUpdatorId(params.getUpdatorId());

                if (isBlank(recommendBottom.getDramaIds()) && recommendBottom.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                    recommendBottom.setDramaIds(allIds);
                }
                // dramaIds去重
                String dramasIds = recommendBottom.getDramaIds();
                if (StrUtil.isNotEmpty(dramasIds) && dramasIds.length() > 50) {
                    dramasIds = StrUtil.duplicateRemove(dramasIds);
                    recommendBottom.setDramaIds(dramasIds);
                }
                recommendBottom.setContVersionId(params.getContVersionId());
                if (miniRecommendMapper.updateByMiniId(recommendBottom) == 0) {
                    transactionRollBack("更新失败5");
                    return MethodVO.error(StaticStr.UPDATE_FAILED);
                }
            }
            {
                // 短剧推荐-精选推荐（快应用第一版）
                if (params.getRecommendSuper() != null) {
                    FastMiniRecommendPO recommendSupper = params.getRecommendSuper();
                    if (recommendSupper == null) {
                        recommendSupper = new FastMiniRecommendPO();
                        recommendSupper.setGroupType(3);
                        recommendSupper.setMiniId(params.getMiniId());
                        recommendSupper.setCreatorId(params.getCreatorId());
                        recommendSupper.setCreateTime(nowDate);
                        recommendSupper.setContVersionId(params.getContVersionId());
                        miniRecommendMapper.insertSelective(recommendSupper);

                        FastMiniRecommendPO recommendQuery = new FastMiniRecommendPO();
                        recommendQuery.setMiniId(params.getMiniId());
                        recommendQuery.setGroupType(3);
                        recommendSupper = miniRecommendMapper.queryOne(recommendQuery);
                    }
                    if (recommendSupper == null) {
                        throw new MyException("初始化数据失败");
                    }
                    recommendSupper.setGroupType(3);
                    recommendSupper.setShowDrama(params.getRecommendSuper().getShowDrama());
                    recommendSupper.setMiniId(params.getMiniId());
                    recommendSupper.setUpdateTime(nowDate);
                    recommendSupper.setUpdatorId(params.getUpdatorId());

                    if (isBlank(recommendSupper.getDramaIds()) && params.getRecommendSuper().getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                        recommendSupper.setDramaIds(allIds);
                    }
                    // dramaIds去重
                    String dramasIds = recommendSupper.getDramaIds();
                    if (StrUtil.isNotEmpty(dramasIds) && dramasIds.length() > 50) {
                        dramasIds = StrUtil.duplicateRemove(dramasIds);
                        recommendSupper.setDramaIds(dramasIds);
                    }
                    recommendSupper.setContVersionId(params.getContVersionId());
                    if (miniRecommendMapper.updateByMiniId(recommendSupper) == 0) {
                        transactionRollBack("更新失败6");
                        return MethodVO.error(StaticStr.UPDATE_FAILED);
                    }
                }
            }
            {
                // 青少年模式-推荐（快应用第一版）
                FastMiniRecommendPO recommendYoung = params.getRecommendYoung();
                if (recommendYoung != null) {
//            		if (recommendYoung == null) {
//            			recommendYoung = new FastMiniRecommendPO();
//            			recommendYoung.setGroupType(6);
//            			recommendYoung.setMiniId(params.getMiniId());
//            			recommendYoung.setCreatorId(params.getCreatorId());
//            			recommendYoung.setCreateTime(nowDate);
//            			recommendYoung.setContVersionId(params.getContVersionId());
//            			miniRecommendMapper.insertSelective(recommendYoung);
//            			
//            			FastMiniRecommendPO recommendQuery = new FastMiniRecommendPO();
//            			recommendQuery.setMiniId(params.getMiniId());
//            			recommendQuery.setGroupType(6);
//            			recommendYoung = miniRecommendMapper.queryOne(recommendQuery);
//            		}
//            		if (recommendYoung == null) {
//            			throw new MyException("初始化数据失败");
//            		}
                    recommendYoung.setGroupType(6); // 多tab精选
                    recommendYoung.setShowDrama(params.getRecommendYoung().getShowDrama());
                    recommendYoung.setMiniId(params.getMiniId());
                    recommendYoung.setUpdateTime(nowDate);
                    recommendYoung.setUpdatorId(params.getUpdatorId());
                    recommendYoung.setCornerIds(params.getRecommendYoung().getCornerIds());
                    recommendYoung.setDramaIds(params.getRecommendYoung().getDramaIds());
                    if (isBlank(recommendYoung.getDramaIds()) && recommendYoung.getShowDrama() != null && recommendYoung.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                        recommendYoung.setDramaIds(allIds);
                    }
                    // dramaIds去重
                    String dramasIds = recommendYoung.getDramaIds();
                    if (StrUtil.isNotEmpty(dramasIds) && dramasIds.length() > 50) {
                        dramasIds = StrUtil.duplicateRemove(dramasIds);
                        recommendYoung.setDramaIds(dramasIds);
                    }
                    recommendYoung.setContVersionId(params.getContVersionId());
                    // 更新
                    if (miniRecommendMapper.updateByMiniId(recommendYoung) == 0) {
                        transactionRollBack("更新失败7");
                        return MethodVO.error(StaticStr.UPDATE_FAILED);
                    }
                }
            }
            {
                // 快应用，小剧场多tab设置推荐剧列表
                if (params.getRecommendTheaterList() != null && params.getRecommendTheaterList().size() > 0) {
                    for (FastMiniRecommendPO recItem : params.getRecommendTheaterList()) {
                        recItem.setGroupType(4);// 多tab小剧场
//	                	recItem.setShowDrama(recItem.getShowDrama());
                        recItem.setMiniId(params.getMiniId());
                        recItem.setUpdateTime(nowDate);
                        recItem.setUpdatorId(params.getUpdatorId());

                        if (isBlank(recItem.getDramaIds()) && recItem.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                            recItem.setDramaIds(allIds);
                        }
                        // dramaIds去重
                        String dramasIds = recItem.getDramaIds();
                        if (StrUtil.isNotEmpty(dramasIds) && dramasIds.length() > 50) {
                            dramasIds = StrUtil.duplicateRemove(dramasIds);
                            recItem.setDramaIds(dramasIds);
                        }
                        recItem.setContVersionId(params.getContVersionId());

                        if (recItem.getId() == null) {
                            // 新增
                            recItem.setCreatorId(params.getUpdatorId());
                            recItem.setCreateTime(nowDate);
                            miniRecommendMapper.insertSelective(recItem);
                        } else {
                            // 更新
                            if (miniRecommendMapper.updateByMiniId(recItem) == 0) {
                                transactionRollBack("更新失败8");
                                return MethodVO.error(StaticStr.UPDATE_FAILED);
                            }
                        }
                    }
                }
                // 快应用，精选多tab设置推荐剧列表
                if (params.getRecommendSuperList() != null && params.getRecommendSuperList().size() > 0) {
                    for (FastMiniRecommendPO recItem : params.getRecommendSuperList()) {
                        recItem.setGroupType(5); // 多tab精选
                        recItem.setMiniId(params.getMiniId());
                        recItem.setUpdateTime(nowDate);
                        recItem.setUpdatorId(params.getUpdatorId());
                        if (isBlank(recItem.getDramaIds()) && recItem.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                            recItem.setDramaIds(allIds);
                        }
                        // dramaIds去重
                        String dramasIds = recItem.getDramaIds();
                        if (StrUtil.isNotEmpty(dramasIds) && dramasIds.length() > 50) {
                            dramasIds = StrUtil.duplicateRemove(dramasIds);
                            recItem.setDramaIds(dramasIds);
                        }
                        recItem.setContVersionId(params.getContVersionId());
                        if (recItem.getId() == null) {
                            // 新增
                            recItem.setCreatorId(params.getUpdatorId());
                            recItem.setCreateTime(nowDate);
                            miniRecommendMapper.insertSelective(recItem);
                        } else {
                            // 更新
                            if (miniRecommendMapper.updateByMiniId(recItem) == 0) {
                                transactionRollBack("更新失败9");
                                return MethodVO.error(StaticStr.UPDATE_FAILED);
                            }
                        }
                    }
                }
            }
        }

        // 推荐区-模块（漫画）
        if (miniPO.getContentType() == 2) {
            {
                FastMiniRecommendModularPO modular = new FastMiniRecommendModularPO();
                modular.setMiniId(params.getMiniId());
                modular.setGroupType(1);
                recommendModularMapper.deleteByMiniId(modular);
                List<FastMiniRecommendModularPO> recommendModularList = params.getRecommendModularList();

                if (CollUtil.hasContent(recommendModularList)) {
                    for (FastMiniRecommendModularPO cur : recommendModularList) {
                        cur.setGroupType(1);
                        cur.setCreatorId(params.getCreatorId());
                        cur.setCreateTime(nowDate);
                        cur.setMiniId(params.getMiniId());
                        cur.setDelFlag(StaticVar.NO);
                        cur.setOpenState(StaticVar.YES);

                        if (isBlank(cur.getDramaIds()) && cur.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                            cur.setDramaIds(allIds);
                        }
                        recommendModularMapper.insertSelective(cur);
                    }
                }
            }
            {
                FastMiniRecommendModularPO modular = new FastMiniRecommendModularPO();
                modular.setMiniId(params.getMiniId());
                modular.setGroupType(2);
                recommendModularMapper.deleteByMiniId(modular);
                List<FastMiniRecommendModularPO> recommendModularListGroup = params.getRecommendModularListGroup();

                if (CollUtil.hasContent(recommendModularListGroup)) {
                    for (FastMiniRecommendModularPO cur : recommendModularListGroup) {
                        cur.setGroupType(2);
                        cur.setCreatorId(params.getCreatorId());
                        cur.setCreateTime(nowDate);
                        cur.setMiniId(params.getMiniId());
                        cur.setDelFlag(StaticVar.NO);
                        cur.setOpenState(StaticVar.YES);

                        if (isBlank(cur.getDramaIds()) && cur.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                            cur.setDramaIds(allIds);
                        }
                        recommendModularMapper.insertSelective(cur);
                    }
                }
            }
            {
                FastMiniVideoModularPO modular = new FastMiniVideoModularPO();
                modular.setMiniId(params.getMiniId());
                videoModularMapper.deleteByMiniId(modular);
                List<FastMiniVideoModularPO> videoModularList = params.getVideoModularList();

                if (CollUtil.hasContent(videoModularList)) {
                    for (FastMiniVideoModularPO cur : videoModularList) {
                        cur.setCreatorId(params.getCreatorId());
                        cur.setCreateTime(nowDate);
                        cur.setMiniId(params.getMiniId());
                        cur.setDelFlag(StaticVar.NO);

                        videoModularMapper.insertSelective(cur);
                    }
                }
            }
        }

        // 推荐区-模块（小说）
        if (miniPO.getContentType() == 3) {
            {
                FastMiniRecommendModularPO modular = new FastMiniRecommendModularPO();
                modular.setMiniId(params.getMiniId());
                modular.setGroupType(1);
                recommendModularMapper.deleteByMiniId(modular);
                List<FastMiniRecommendModularPO> recommendModularList = params.getRecommendModularList();

                if (CollUtil.hasContent(recommendModularList)) {
                    for (FastMiniRecommendModularPO cur : recommendModularList) {
                        cur.setGroupType(1);
                        cur.setCreatorId(params.getCreatorId());
                        cur.setCreateTime(nowDate);
                        cur.setMiniId(params.getMiniId());
                        cur.setDelFlag(StaticVar.NO);
                        cur.setOpenState(StaticVar.YES);

                        if (isBlank(cur.getDramaIds()) && cur.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                            cur.setDramaIds(allIds);
                        }
                        recommendModularMapper.insertSelective(cur);
                    }
                }
            }
            {
                FastMiniRecommendModularPO modular = new FastMiniRecommendModularPO();
                modular.setMiniId(params.getMiniId());
                modular.setGroupType(2);
                recommendModularMapper.deleteByMiniId(modular);
                List<FastMiniRecommendModularPO> recommendModularListGroup = params.getRecommendModularListGroup();

                if (CollUtil.hasContent(recommendModularListGroup)) {
                    for (FastMiniRecommendModularPO cur : recommendModularListGroup) {
                        cur.setGroupType(2);
                        cur.setCreatorId(params.getCreatorId());
                        cur.setCreateTime(nowDate);
                        cur.setMiniId(params.getMiniId());
                        cur.setDelFlag(StaticVar.NO);
                        cur.setOpenState(StaticVar.YES);

                        if (isBlank(cur.getDramaIds()) && cur.getShowDrama() == 1 && StrUtil.notEmpty(allIds)) {
                            cur.setDramaIds(allIds);
                        }
                        recommendModularMapper.insertSelective(cur);
                    }
                }
            }
            {
                FastMiniVideoModularPO modular = new FastMiniVideoModularPO();
                modular.setMiniId(params.getMiniId());
                videoModularMapper.deleteByMiniId(modular);
                List<FastMiniVideoModularPO> videoModularList = params.getVideoModularList();

                if (CollUtil.hasContent(videoModularList)) {
                    for (FastMiniVideoModularPO cur : videoModularList) {
                        cur.setCreatorId(params.getCreatorId());
                        cur.setCreateTime(nowDate);
                        cur.setMiniId(params.getMiniId());
                        cur.setDelFlag(StaticVar.NO);

                        videoModularMapper.insertSelective(cur);
                    }
                }
            }
        }

        // 底部导航
        FastMiniBottomPO bottom = new FastMiniBottomPO();
        bottom.setMiniId(params.getMiniId());
        fastMiniBottomMapper.deleteByMiniId(bottom);
        List<FastMiniBottomPO> bottomList = params.getBottomList();
        if (CollUtil.isEmpty(bottomList) || bottomList.size() != 5) {
            // throw new MyException("bottomList数据不合法");
        }
        if (CollUtil.hasContent(bottomList)) {
            boolean addTiktokBottomFlag = true;
            // 微小抖音小程/快应用序添加菜单
            if (Arrays.asList(1, 2, 5).contains(miniPO.getType())) {
                for (FastMiniBottomPO mbItem : bottomList) {
                    // 福利中心
                    if (mbItem.getType() == 6) {
                        addTiktokBottomFlag = false;
                        break;
                    }
                }
            }
            boolean addRecommendBottomFlag = true;
            if (Objects.equals(1, miniPO.getType())) {
                addRecommendBottomFlag = bottomList.stream().noneMatch(item -> item.getType() == 7);
            }
            int i = 1;
            for (FastMiniBottomPO cur : bottomList) {
                // 快应用不需要壁纸、介绍
                if ((miniPO.getType() == 5 || miniPO.getType() == 7) && Arrays.asList(4, 5).contains(cur.getType())) {
                    continue;
                }
                cur.setCreatorId(params.getCreatorId());
                cur.setCreateTime(nowDate);
                cur.setMiniId(params.getMiniId());
                cur.setDelFlag(StaticVar.NO);
                cur.setSequence(i);
                // 微小增加推荐导航
                if (i == 2 && Objects.equals(1, miniPO.getType()) && addRecommendBottomFlag) {
                    // 微小增加推荐按钮
                    i++;
                    FastMiniBottomPO mbPO = buildRecommendBottom(cur.getContentType(), cur.getCreatorId(), params.getMiniId(), i, nowDate);
                    fastMiniBottomMapper.insertSelective(mbPO);
                }
                if (i == 3 && Arrays.asList(1, 2, 5).contains(miniPO.getType()) && addTiktokBottomFlag) {
                    i++;
                    // 微小抖音小程序，增加定制底部导航
                    FastMiniBottomPO mbPO = new FastMiniBottomPO();
                    mbPO.setContentType(cur.getContentType());
                    mbPO.setCreatorId(cur.getCreatorId());
                    mbPO.setCreateTime(nowDate);
                    mbPO.setMiniId(params.getMiniId());
                    mbPO.setSequence(i);
                    mbPO.setType(6);
                    mbPO.setName("福利");
                    mbPO.setOpenState(0);// 默认关闭
                    fastMiniBottomMapper.insertSelective(mbPO);
                }
                i++;
                fastMiniBottomMapper.insertSelective(cur);
            }
        }
        // 壁纸
        // FastMiniWallpaperPO wallpaper = new FastMiniWallpaperPO();
        // wallpaper.setMiniId(item.getMiniId());
        fastMiniWallpaperMapper.deleteByMiniId(params.getMiniId());
        List<FastMiniWallpaperPO> wallpaperList = params.getWallpaperList();
        if (wallpaperList != null && wallpaperList.size() > 0) {
            for (FastMiniWallpaperPO wall : wallpaperList) {
                wall.setCreatorId(params.getCreatorId());
                wall.setMiniId(params.getMiniId());
            }
            fastMiniWallpaperMapper.insertBatch(wallpaperList);
        }
        // 剧情介绍
        // FastMiniIntroductionPO introduction = new FastMiniIntroductionPO();
        // introduction.setMiniId(item.getMiniId());
        fastMiniIntroductionMapper.deleteByMiniId(params.getMiniId());
        List<FastMiniIntroductionPO> introductionList = params.getIntroductionList();
        if (introductionList != null && introductionList.size() > 0) {
            for (FastMiniIntroductionPO intro : introductionList) {
                intro.setCreatorId(params.getCreatorId());
                intro.setMiniId(params.getMiniId());
            }
            fastMiniIntroductionMapper.insertBatch(introductionList);
        }
        // 小程序福利任务设置
        if (params.getTaskPO() != null) {
            fastSettingTaskService.update(sessionVO, params.getTaskPO());
        }

        // 绑定剧单
        List<FastMiniPlaylistPO> playlists = params.getPlaylists();
        if (CollUtil.isNotEmpty(playlists)) {
            if (playlists.size() > 10) {
                throw new MyException("剧单不能超过10个");
            }
            fastMiniPlaylistService.saveOrUpdateBatch(miniPO, playlists, params.getContVersionId());
        }

        return MethodVO.success();
    }

    public FastMiniBottomPO buildRecommendBottom(Integer contentType, Integer creatorId, Integer miniId, Integer seq, Date createTime) {
        FastMiniBottomPO mbPO = new FastMiniBottomPO();
        mbPO.setContentType(contentType);
        mbPO.setCreatorId(creatorId);
        mbPO.setCreateTime(createTime);
        mbPO.setMiniId(miniId);
        mbPO.setSequence(seq);
        mbPO.setType(7);
        mbPO.setName("推荐");
        mbPO.setOpenState(0);// 默认关闭
        return mbPO;
    }


    /**
     * 兑换名称设置
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateCoinName(FastMiniSettingPO item) {
        Date nowDate = DateUtil.getNowDate();
        FastMiniSettingPO update = new FastMiniSettingPO();
        update.setUpdateTime(nowDate);
        update.setUpdatorId(item.getUpdatorId());
        update.setCoinName(item.getCoinName());
        update.setVisitorName(item.getVisitorName());
        update.setMiniId(item.getMiniId());
        if (fastMiniSettingMapper.updateByMiniId(item) == 0) {
            update.setCreateTime(nowDate);
            update.setCreatorId(item.getCreatorId());
            if (fastMiniSettingMapper.insertSelective(update) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        return MethodVO.success();
    }

    /**
     * ios充值开关设置
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateIosChargeOpen(FastMiniSettingPO item) {
        Date nowDate = DateUtil.getNowDate();
        FastMiniSettingPO update = new FastMiniSettingPO();
        update.setUpdateTime(nowDate);
        update.setUpdatorId(item.getUpdatorId());
        update.setOpenIosCharge1(item.getOpenIosCharge());
        update.setOpenIosTime1(item.getOpenIosTime());
        update.setOpenIosCharge2(item.getOpenIosCharge());
        update.setOpenIosTime2(item.getOpenIosTime());
        update.setOpenIosCharge3(item.getOpenIosCharge());
        update.setOpenIosTime3(item.getOpenIosTime());
        update.setMiniId(item.getMiniId());
        update.setIosRechargeType(item.getIosRechargeType());
        if (fastMiniSettingMapper.updateByMiniId(item) == 0) {
            update.setCreateTime(nowDate);
            update.setCreatorId(item.getCreatorId());
            if (fastMiniSettingMapper.insertSelective(update) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateClipboard(FastMiniSettingPO item) {
        Date nowDate = DateUtil.getNowDate();
        item.setUpdateTime(nowDate);
        if (fastMiniSettingMapper.updateByMiniId(item) == 0) {
            // 没有就新增一个
            FastMiniSettingPO update = new FastMiniSettingPO();
            update.setUpdateTime(nowDate);
            update.setUpdatorId(item.getUpdatorId());
            update.setOpenIosCharge1(item.getOpenIosCharge());
            update.setOpenIosTime1(item.getOpenIosTime());
            update.setOpenIosCharge2(item.getOpenIosCharge());
            update.setOpenIosTime2(item.getOpenIosTime());
            update.setOpenIosCharge3(item.getOpenIosCharge());
            update.setOpenIosTime3(item.getOpenIosTime());
            update.setMiniId(item.getMiniId());
            update.setCreateTime(nowDate);
            update.setCreatorId(item.getCreatorId());
            update.setClipboardOn(item.getClipboardOn());
            update.setContinuePushFlag(item.getContinuePushFlag());
            if (fastMiniSettingMapper.insertSelective(update) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        String key = StaticVar.MINI_CONTENT_SETTING + item.getMiniId() + ":" + item.getContVersionId();
        RedisUtil.del(key);// 清缓存
        FastMiniPO miniPO = fastMiniService.queryById(item.getMiniId());
        if (miniPO != null) {
            String key2 = StaticVar.MINI_CONTENT_SETTING_SIMPLE + item.getAppId() + ":" + item.getContVersionId();
            RedisUtil.del(key2);// 清缓存
        }
        return MethodVO.success();
    }

    /**
     * 根据小程序id，查询配置
     */
    public FastMiniSettingPO getMiniSettingRedis(Integer miniId, Integer cvid) {
        String key = StaticVar.MINI_SETTING_SIMPLE + miniId + ":" + cvid;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            FastMiniSettingPO msParam = new FastMiniSettingPO();
            msParam.setMiniId(miniId);
            FastMiniSettingPO msPO = fastMiniSettingMapper.queryByMiniId(msParam);
            if (msPO == null) {
                res = StaticVar.EMPTY_FLAG;
            } else {
                res = JsonUtil.toString(msPO);
            }
            RedisUtil.set(key, res, 60 * 30);
        }
        if (StaticVar.EMPTY_FLAG.equals(res)) {
            return null;
        }
        return JsonUtil.toJavaObject(res, FastMiniSettingPO.class);
    }

    /**
     * 查询小程序配置
     */
    public FastMiniSettingPO getMiniSetting(FastMiniSettingPO item) {
        FastMiniSettingPO query = new FastMiniSettingPO();
        query.setMiniId(item.getMiniId());
        FastMiniSettingPO po = fastMiniSettingMapper.queryOne(query);
        if (po == null) {
            Date nowDate = DateUtil.getNowDate();
            item.setCreateTime(nowDate);
            item.setCreatorId(item.getCreatorId());
            if (fastMiniSettingMapper.insertSelective(item) == 0) {
                transactionRollBack();
                throw new MyException(StaticStr.UPDATE_FAILED);
            }
            po = fastMiniSettingMapper.queryOne(item);
        }
        return po;
    }

}
