/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniDeviceBrandLogMapper;
import com.fast.po.mini.FastMiniDeviceBrandLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniDeviceBrandLogService extends BaseService {

    @Autowired
    private FastMiniDeviceBrandLogMapper fastMiniDeviceBrandLogMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniDeviceBrandLogPO queryById(FastMiniDeviceBrandLogPO params) {
        return fastMiniDeviceBrandLogMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniDeviceBrandLogPO queryById(Integer id) {
        return fastMiniDeviceBrandLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniDeviceBrandLogPO queryOne(FastMiniDeviceBrandLogPO params) {
        return fastMiniDeviceBrandLogMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniDeviceBrandLogPO> queryList(FastMiniDeviceBrandLogPO params) {
        return fastMiniDeviceBrandLogMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniDeviceBrandLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniDeviceBrandLogPO> list = fastMiniDeviceBrandLogMapper.queryList(params);
        for (FastMiniDeviceBrandLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniDeviceBrandLogPO params) {
        return fastMiniDeviceBrandLogMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniDeviceBrandLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMiniDeviceBrandLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniDeviceBrandLogPO> list) {
        if (fastMiniDeviceBrandLogMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniDeviceBrandLogPO params) {
        if (fastMiniDeviceBrandLogMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
