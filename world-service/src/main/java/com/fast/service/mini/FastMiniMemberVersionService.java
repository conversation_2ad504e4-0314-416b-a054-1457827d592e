/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberAccountMapper;
import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.mapper.member.FastMemberRecentDramaFinishMapper;
import com.fast.mapper.mini.FastMiniMemberVersionMapper;
import com.fast.po.member.FastMemberAccountPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.member.FastMemberRecentDramaFinishPO;
import com.fast.po.mini.FastMiniMemberVersionPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 用户在小程序的配置版本
 *
 * <AUTHOR>
 */
@Service
public class FastMiniMemberVersionService extends BaseService {

    @Autowired
    private FastMiniMemberVersionMapper memberVersionMapper;
    @Autowired
    private FastMemberAccountMapper accountMapper;
    @Autowired
    private FastMemberOrderRechargeMapper orderRechargeMapper;
    @Autowired
    private FastMemberRecentDramaFinishMapper dramaFinishMapper;

    /**
     * 查询缓存信息
     */
    public FastMiniMemberVersionPO queryInfoByRedis(Long memberId) {
        if (memberId == null) {
            return null;
        }
        FastMiniMemberVersionPO po;
        String key = StaticVar.MINI_MEMBER_VERSION_ID + memberId;
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            po = JsonUtil.toJavaObject(value, FastMiniMemberVersionPO.class);
        } else {
            po = memberVersionMapper.queryById(memberId);
            if (po == null) {
                FastMiniMemberVersionPO version = new FastMiniMemberVersionPO();
                version.setMemberId(memberId);
                getData(memberId, version);

                // 数据新增
                memberVersionMapper.insertSelective(version);
                po = memberVersionMapper.queryById(memberId);
                if (po == null) {
                    RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_10D);
                    return null;
                }
            } else {
                getData(memberId, po);
                memberVersionMapper.updateById(po);
                po.setVersion(po.getVersion() + 1);
            }
            RedisUtil.set(key, JsonUtil.toString(po), RedisUtil.TIME_10D);
        }
        return po;
    }

    /**
     * 获取业务数据
     *
     * @param memberId
     * @param version
     */
    private void getData(Long memberId, FastMiniMemberVersionPO version) {
        // 1.查询充值次数
        FastMemberAccountPO account = accountMapper.queryById(memberId);
        if (account != null) {
            version.setRechargeCount(account.getRechargeCount());
        }
        // 2.查询充值vip最大天数
        FastMemberOrderRechargePO rechargeQ = new FastMemberOrderRechargePO();
        rechargeQ.setMemberId(memberId);
        rechargeQ.setState(1);
        rechargeQ.setOrderType(2);
        List<FastMemberOrderRechargePO> rechargeList = orderRechargeMapper.queryListSimple4VipTime(rechargeQ);
        if (CollUtil.hasContent(rechargeList)) {
            int maxDay = 0;
            for (FastMemberOrderRechargePO recharge : rechargeList) {
                Date startTime = recharge.getPayTime();
                Date endTime = recharge.getPayTime();
                // 单位:1=周;2=月;3=年;4=日
                switch (recharge.getValidUnit()) {
                    case 1: {
                        endTime = DateUtil.addWeeks(recharge.getPayTime(), recharge.getValidDate());
                        break;
                    }
                    case 2: {
                        endTime = DateUtil.addMonths(recharge.getPayTime(), recharge.getValidDate());
                        break;
                    }
                    case 3: {
                        endTime = DateUtil.addYears(recharge.getPayTime(), recharge.getValidDate());
                        break;
                    }
                }
                int day = DateUtil.daysBetweenUp(startTime, endTime);
                maxDay = Math.max(maxDay, day);
            }
            version.setVipOrderDay(maxDay);
        }
        // 3.查询完播剧数量
        FastMemberRecentDramaFinishPO dramaFinishQ = new FastMemberRecentDramaFinishPO();
        dramaFinishQ.setMemberId(memberId);
        version.setFinishDramaNum(dramaFinishMapper.queryCount(dramaFinishQ));
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniMemberVersionPO queryById(FastMiniMemberVersionPO item) {
        return memberVersionMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniMemberVersionPO queryById(Long id) {
        return memberVersionMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniMemberVersionPO queryOne(FastMiniMemberVersionPO item) {
        return memberVersionMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniMemberVersionPO> queryList(FastMiniMemberVersionPO item) {
        return memberVersionMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniMemberVersionPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniMemberVersionPO> list = memberVersionMapper.queryList(item);
        for (FastMiniMemberVersionPO cur : list) {
            cur.setEncryptionId(encode(cur.getMemberId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniMemberVersionPO item) {
        return memberVersionMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniMemberVersionPO item) {
        if (memberVersionMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniMemberVersionPO item) {
        if (memberVersionMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
