/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniBannerMapper;
import com.fast.po.mini.FastMiniBannerPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniBannerService extends BaseService {

    @Autowired
    private FastMiniBannerMapper fastMiniBannerMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniBannerPO queryById(FastMiniBannerPO item) {
        return fastMiniBannerMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniBannerPO queryById(Integer id) {
        FastMiniBannerPO itemParam = new FastMiniBannerPO();
        itemParam.setId(id);
        return fastMiniBannerMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniBannerPO queryOne(FastMiniBannerPO item) {
        return fastMiniBannerMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniBannerPO> queryList(FastMiniBannerPO item) {
        return fastMiniBannerMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniBannerPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniBannerPO> list = fastMiniBannerMapper.queryList(item);
        for (FastMiniBannerPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniBannerPO item) {
        return fastMiniBannerMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniBannerPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastMiniBannerMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniBannerPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniBannerMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
