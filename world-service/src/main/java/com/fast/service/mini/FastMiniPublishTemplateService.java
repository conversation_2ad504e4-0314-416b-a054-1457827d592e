/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.mapper.mini.FastMiniPublishTemplateMapper;
import com.fast.po.mini.FastMiniPublishTemplatePO;
import com.fast.service.base.BaseService;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniPublishTemplateService extends BaseService {

    @Autowired
    private FastMiniPublishTemplateMapper fastMiniPublishTemplateMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniPublishTemplatePO queryById(FastMiniPublishTemplatePO item) {
        return fastMiniPublishTemplateMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniPublishTemplatePO queryById(Integer id) {
        return fastMiniPublishTemplateMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniPublishTemplatePO queryOne(FastMiniPublishTemplatePO item) {
        return fastMiniPublishTemplateMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniPublishTemplatePO> queryList(FastMiniPublishTemplatePO item) {
        return fastMiniPublishTemplateMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniPublishTemplatePO item) {
        List<FastMiniPublishTemplatePO> list = fastMiniPublishTemplateMapper.queryList(item);
        for (FastMiniPublishTemplatePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(list);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniPublishTemplatePO item) {
        return fastMiniPublishTemplateMapper.queryCount(item);
    }


}
