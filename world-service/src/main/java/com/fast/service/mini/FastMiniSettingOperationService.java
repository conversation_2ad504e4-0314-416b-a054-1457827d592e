/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniSettingOperationMapper;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.mini.FastMiniSettingOperationPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniSettingOperationService extends BaseService {

    @Autowired
    private FastMiniSettingOperationMapper fastMiniSettingOperationMapper;
    @Autowired
    private FastMiniService fastMiniService;

    /**
     * 通过id查询单个对象
     */
    public FastMiniSettingOperationPO queryById(FastMiniSettingOperationPO params) {
        return fastMiniSettingOperationMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniSettingOperationPO queryById(Integer id) {
        return fastMiniSettingOperationMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniSettingOperationPO queryOne(FastMiniSettingOperationPO params) {
        return fastMiniSettingOperationMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniSettingOperationPO> queryList(FastMiniSettingOperationPO params) {
        return fastMiniSettingOperationMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniSettingOperationPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniSettingOperationPO> list = fastMiniSettingOperationMapper.queryList(params);
        for (FastMiniSettingOperationPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniSettingOperationPO params) {
        return fastMiniSettingOperationMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniSettingOperationPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMiniSettingOperationMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniSettingOperationPO> list) {
        if (fastMiniSettingOperationMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniSettingOperationPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniSettingOperationMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 运营能力-获得加桌设置
     *
     * @param params 小程序id
     */
    public ResultVO<?> getDeskConfig(FastMiniVO params) {
        FastMiniSettingOperationPO fastMiniSettingOperation = new FastMiniSettingOperationPO();
        fastMiniSettingOperation.setMiniId(params.getId());
        fastMiniSettingOperation = fastMiniSettingOperationMapper.queryOne(fastMiniSettingOperation);
        if (fastMiniSettingOperation == null) {
            fastMiniSettingOperation = new FastMiniSettingOperationPO();
            fastMiniSettingOperation.setDesktopType(3);
            fastMiniSettingOperation.setStartSeriesNum(1);
            return ResultVO.success(fastMiniSettingOperation);
        }
        return ResultVO.success(fastMiniSettingOperation);
    }

    /**
     * 运营能力-保存加桌设置
     *
     * @param params 小程序id,加桌方式,加桌剧集,弹窗弹出频率,弹窗标题文案,弹窗描述文案,弹窗按钮文案
     */
    public ResultVO<?> saveDeskConfig(FastMiniSettingOperationPO params) {
        FastMiniVO miniVO = fastMiniService.queryInfoByRedis(params.getMiniId());
        if (miniVO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniSettingOperationMapper.updateByMiniId(params) == 0) {
            params.setId(null);
            params.setCreatorId(params.getUpdatorId());
            params.setCreateTime(nowTime);
            if (fastMiniSettingOperationMapper.insertSelective(params) == 0) {
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
        }
        return ResultVO.success();
    }

    public ResultVO<?> getMiniDeskConfig(FastMiniPO params) {
        FastMiniVO miniVO = fastMiniService.queryByAppIdRedis(params.getAppId());
        if (miniVO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }
        return getDeskConfig(miniVO);
    }

}
