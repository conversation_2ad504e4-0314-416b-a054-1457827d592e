/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniIntroductionMapper;
import com.fast.po.mini.FastMiniIntroductionPO;
import com.fast.service.base.BaseService;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniIntroductionService extends BaseService {

    @Autowired
    private FastMiniIntroductionMapper fastMiniIntroductionMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniIntroductionPO queryById(FastMiniIntroductionPO item) {
        return fastMiniIntroductionMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniIntroductionPO queryById(Integer id) {
        return fastMiniIntroductionMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniIntroductionPO queryOne(FastMiniIntroductionPO item) {
        return fastMiniIntroductionMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniIntroductionPO> queryList(FastMiniIntroductionPO item) {
        return fastMiniIntroductionMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniIntroductionPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniIntroductionPO> list = fastMiniIntroductionMapper.queryList(item);
        for (FastMiniIntroductionPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniIntroductionPO item) {
        return fastMiniIntroductionMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniIntroductionPO item) {
        if (fastMiniIntroductionMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniIntroductionPO> list) {
        if (fastMiniIntroductionMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniIntroductionPO item) {
        if (fastMiniIntroductionMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
