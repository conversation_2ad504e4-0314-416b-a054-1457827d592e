/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.framework.exception.MyException;
import com.fast.mapper.mini.FastMiniVersionMapper;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.mini.FastMiniVersionPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniVersionService extends BaseService {

    @Autowired
    private FastMiniVersionMapper fastMiniVersionMapper;

    @Autowired
    private FastMiniService fastMiniService;

    /**
     * 通过id查询单个对象
     */
    public FastMiniVersionPO queryById(FastMiniVersionPO params) {
        return fastMiniVersionMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniVersionPO queryById(Integer id) {
        return fastMiniVersionMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniVersionPO queryOne(FastMiniVersionPO params) {
        return fastMiniVersionMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniVersionPO> queryList(FastMiniVersionPO params) {
        return fastMiniVersionMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniVersionPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniVersionPO> list = fastMiniVersionMapper.queryList(params);
        for (FastMiniVersionPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniVersionPO params) {
        return fastMiniVersionMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniVersionPO params) {
        if (params.getMiniId() == null) {
            return MethodVO.error("应用不能为空");
        }
        FastMiniPO fastMiniPO = fastMiniService.queryById(params.getMiniId());
        if (fastMiniPO == null) {
            return MethodVO.error("应用不存在");
        }
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (fastMiniVersionMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniVersionPO params) {
        if (params.getMiniId() == null) {
            return MethodVO.error("应用不能为空");
        }
        FastMiniPO fastMiniPO = fastMiniService.queryById(params.getMiniId());
        if (fastMiniPO == null) {
            return MethodVO.error("应用不存在");
        }
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniVersionMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(Integer id) {
        if (id == null) {
            throw new MyException(StaticStr.INVALID_PARAM);
        }
        return fastMiniVersionMapper.deleteById(id);
    }
}
