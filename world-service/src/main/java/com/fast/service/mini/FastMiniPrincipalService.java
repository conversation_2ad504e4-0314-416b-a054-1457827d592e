/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.mini.FastMiniPrincipalMapper;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.mini.FastMiniPrincipalPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniPrincipalService extends BaseService {

    @Autowired
    private FastMiniPrincipalMapper fastMiniPrincipalMapper;
    @Autowired
    private FastMiniMapper fastMiniMapper;
    @Autowired
    private FastMiniContVersionService fastMiniContVersionService;

    /**
     * 通过id查询单个对象
     */
    public FastMiniPrincipalPO queryById(FastMiniPrincipalPO params) {
        return fastMiniPrincipalMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniPrincipalPO queryById(Integer id) {
        return fastMiniPrincipalMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniPrincipalPO queryOne(FastMiniPrincipalPO params) {
        return fastMiniPrincipalMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniPrincipalPO> queryList(FastMiniPrincipalPO params) {
        return fastMiniPrincipalMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniPrincipalPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniPrincipalPO> list = fastMiniPrincipalMapper.queryList(params);
        for (FastMiniPrincipalPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniPrincipalPO params) {
        return fastMiniPrincipalMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniPrincipalPO params) {
        // 名称是否已经存在
        FastMiniPrincipalPO fastMiniPrincipalPO = new FastMiniPrincipalPO();
        fastMiniPrincipalPO.setPrincipalName(params.getPrincipalName());
        FastMiniPrincipalPO existsPrincipal = fastMiniPrincipalMapper.queryOne(fastMiniPrincipalPO);
        if (Objects.nonNull(existsPrincipal)) {
            params.setSignFlag(1);
            params.setId(existsPrincipal.getId());
            update(params);
            return MethodVO.success();
        }
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMiniPrincipalMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniPrincipalPO> list) {
        if (fastMiniPrincipalMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniPrincipalPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniPrincipalMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        // 更新微小缓存
        FastMiniPO mParam = new FastMiniPO();
        mParam.setPrincipalName(params.getPrincipalName());
        mParam.setType(1);
        List<FastMiniPO> miniList = fastMiniMapper.queryList(mParam);
        for (FastMiniPO item : miniList) {
            // String key = StaticVar.MINI_CONTENT_SETTING + item.getId();
            // RedisUtil.del(key);  // 删除缓存
            // 多版本兼容
            List<Integer> cvids = fastMiniContVersionService.getIdByMiniId(item.getId());
            if (CollUtil.isNotEmpty(cvids)) {
                for (Integer cvid : cvids) {
                    String key = StaticVar.MINI_CONTENT_SETTING + item.getId() + ":" + cvid;
                    RedisUtil.del(key);  // 删除缓存
                }
            }
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateCp(FastMiniPrincipalPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniPrincipalMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
