/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.mini.FastMiniDeviceBrandLogMapper;
import com.fast.mapper.mini.FastMiniDeviceBrandMapper;
import com.fast.po.mini.FastMiniDeviceBrandLogPO;
import com.fast.po.mini.FastMiniDeviceBrandPO;
import com.fast.service.base.BaseService;
import com.fast.service.feishu.FeiShuService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniDeviceBrandService extends BaseService {

    @Autowired
    private FastMiniDeviceBrandMapper fastMiniDeviceBrandMapper;
    @Autowired
    private FastMiniDeviceBrandLogMapper fastMiniDeviceBrandLogMapper;
    @Autowired
    private FeiShuService feiShuService;

    /**
     * 通过id查询单个对象
     */
    public FastMiniDeviceBrandPO queryById(FastMiniDeviceBrandPO params) {
        return fastMiniDeviceBrandMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniDeviceBrandPO queryById(Integer id) {
        return fastMiniDeviceBrandMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniDeviceBrandPO queryOne(FastMiniDeviceBrandPO params) {
        return fastMiniDeviceBrandMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniDeviceBrandPO> queryList(FastMiniDeviceBrandPO params) {
        return fastMiniDeviceBrandMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniDeviceBrandPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniDeviceBrandPO> list = fastMiniDeviceBrandMapper.queryList(params);
        for (FastMiniDeviceBrandPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniDeviceBrandPO params) {
        return fastMiniDeviceBrandMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniDeviceBrandPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMiniDeviceBrandMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniDeviceBrandPO> list) {
        if (fastMiniDeviceBrandMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniDeviceBrandPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniDeviceBrandMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 查询设备品牌映射值
     *
     * @param brandStr 品牌字符串1=华为;2=小米;3=oppo;4=vivo;5=荣耀;6=魅族
     * @param ua       用户ua
     */
    public Integer queryDeviceBrandMap(String brandStr, String ua) {
        String key = "mini_device_brand:" + brandStr;
        int type = StaticVar.ZERO;

        // 推送飞书消息，防止重复推送5分钟
        final JedisLock lock = new JedisLock(key + "lock", 100, 5_000);
        try {
            String typeStr = RedisUtil.get(key);
            // 缓存中不存在就去数据库查询
            if (StrUtil.isEmpty(typeStr)) {
                FastMiniDeviceBrandPO params = new FastMiniDeviceBrandPO();
                params.setBrandStr(brandStr);
                FastMiniDeviceBrandPO brand = queryOne(params);
                // 存在则存入缓存
                if (brand != null) {
                    RedisUtil.set(key, brand.getType().toString());
                    typeStr = brand.getType().toString();
                }
                // 不存在则返回0
                else {
                    // 写入日志
                    FastMiniDeviceBrandLogPO logPO = new FastMiniDeviceBrandLogPO();
                    logPO.setBrandStr(brandStr);
                    logPO.setRemark(ua);
                    logPO.setCreateTime(DateUtil.getNowDate());
                    fastMiniDeviceBrandLogMapper.insertSelective(logPO);

                    // 获取锁
                    if (lock.lock()) {
                        log.info("====命中并获取到锁，key={}", key + "lock");
                        feiShuService.sendTextMessage(String.format("日志id：%s", logPO.getId()), null);
                    }
                    RedisUtil.set("mini_device_brand_log:" + brandStr, "1", 300);
                    return type;
                }
            }

            type = Integer.parseInt(typeStr);
        } catch (Exception e) {
            log.error("key{} ====解析失败或加锁失败 lock exception", key + "lock", e);
        } finally {
            lock.release();
        }

        return type;
    }
}
