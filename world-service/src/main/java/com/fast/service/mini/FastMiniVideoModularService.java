/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniVideoModularMapper;
import com.fast.po.mini.FastMiniVideoModularPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniVideoModularService extends BaseService {

    @Autowired
    private FastMiniVideoModularMapper fastMiniVideoModularMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniVideoModularPO queryById(FastMiniVideoModularPO item) {
        return fastMiniVideoModularMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniVideoModularPO queryById(Integer id) {
        return fastMiniVideoModularMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniVideoModularPO queryOne(FastMiniVideoModularPO item) {
        return fastMiniVideoModularMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniVideoModularPO> queryList(FastMiniVideoModularPO item) {
        return fastMiniVideoModularMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniVideoModularPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniVideoModularPO> list = fastMiniVideoModularMapper.queryList(item);
        for (FastMiniVideoModularPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniVideoModularPO item) {
        return fastMiniVideoModularMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniVideoModularPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMiniVideoModularMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniVideoModularPO> list) {
        if (fastMiniVideoModularMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniVideoModularPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMiniVideoModularMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
