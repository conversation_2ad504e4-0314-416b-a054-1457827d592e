/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniWallpaperMapper;
import com.fast.po.mini.FastMiniWallpaperPO;
import com.fast.service.base.BaseService;
import com.fast.service.cache.CacheResetService;
import com.fast.service.oss.OssService;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniWallpaperService extends BaseService {

    @Autowired
    private FastMiniWallpaperMapper fastMiniWallpaperMapper;
    @Autowired
    private CacheResetService cacheResetService;
    @Autowired
    private OssService ossService;

    /**
     * 通过id查询单个对象
     */
    public FastMiniWallpaperPO queryById(FastMiniWallpaperPO item) {
        return fastMiniWallpaperMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniWallpaperPO queryById(Integer id) {
        FastMiniWallpaperPO itemParam = new FastMiniWallpaperPO();
        itemParam.setId(id);
        return fastMiniWallpaperMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniWallpaperPO queryOne(FastMiniWallpaperPO item) {
        return fastMiniWallpaperMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public ResultVO queryList(FastMiniWallpaperPO item) {
        List<FastMiniWallpaperPO> paperList = fastMiniWallpaperMapper.queryList(item);
        paperList.forEach(tempItem -> {
            tempItem.setUrl(ossService.replaceOssDomain(false, tempItem.getUrl()));
        });
        return ResultVO.success("ok", paperList);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniWallpaperPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniWallpaperPO> list = fastMiniWallpaperMapper.queryList(item);
        for (FastMiniWallpaperPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setUrl(ossService.replaceOssDomain(false, cur.getUrl()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniWallpaperPO item) {
        return fastMiniWallpaperMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniWallpaperPO item) {
        if (fastMiniWallpaperMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniWallpaperPO> list) {
        if (fastMiniWallpaperMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniWallpaperPO item) {
        if (fastMiniWallpaperMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        // 清除缓存
        cacheResetService.resetCacheMiniSetOnly(null);
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO deleteBatch(List<Integer> idList) {
        StringBuffer idsBuffer = new StringBuffer();
        for (Integer id : idList) {
            if (idsBuffer.length() > 0) {
                idsBuffer.append(",");
            }
            idsBuffer.append(id);
        }
        if (fastMiniWallpaperMapper.deleteBatch(idsBuffer.toString()) == 0) {
            transactionRollBack();
            return MethodVO.error("删除错误");
        }
        // 清除缓存
        cacheResetService.resetCacheMiniSetOnly(null);
        return MethodVO.success();
    }
}
