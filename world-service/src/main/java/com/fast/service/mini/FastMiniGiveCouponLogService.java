/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniGiveCouponLogMapper;
import com.fast.po.mini.FastMiniGiveCouponLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniGiveCouponLogService extends BaseService {

    @Autowired
    private FastMiniGiveCouponLogMapper fastMiniGiveCouponLogMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniGiveCouponLogPO queryById(FastMiniGiveCouponLogPO params) {
        return fastMiniGiveCouponLogMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniGiveCouponLogPO queryById(Integer id) {
        return fastMiniGiveCouponLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniGiveCouponLogPO queryOne(FastMiniGiveCouponLogPO params) {
        return fastMiniGiveCouponLogMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniGiveCouponLogPO> queryList(FastMiniGiveCouponLogPO params) {
        return fastMiniGiveCouponLogMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniGiveCouponLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniGiveCouponLogPO> list = fastMiniGiveCouponLogMapper.queryList(params);
        for (FastMiniGiveCouponLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniGiveCouponLogPO params) {
        return fastMiniGiveCouponLogMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniGiveCouponLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMiniGiveCouponLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniGiveCouponLogPO> list) {
        if (fastMiniGiveCouponLogMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniGiveCouponLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMiniGiveCouponLogMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 查询当前活动的统计
     *
     * @return
     */
    public JSONObject queryTodayStatis() {
        Map<String, Object> dataMap = fastMiniGiveCouponLogMapper.querySumStatis();
        JSONObject data = new JSONObject(dataMap);
        BigDecimal consumeRatio = BigDecimal.ZERO;
        Integer receivedNum = data.getInteger("receivedNum");    // 下发数量
        Integer consumedNum = data.getInteger("consumedNum");    // 核销数量
        if (receivedNum != 0) {
            consumeRatio = DoubleUtil.divB(DoubleUtil.mulB(new BigDecimal(consumedNum), BigDecimalVar.BD_100), new BigDecimal(receivedNum));
        }
        data.put("consumeRatio", consumeRatio);
        return data;
    }
}
