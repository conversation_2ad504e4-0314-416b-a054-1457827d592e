/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.alibaba.nacos.common.utils.ConcurrentHashSet;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.constant.StaticYml;
import com.fast.framework.exception.MyException;
import com.fast.mapper.common.FastDomainMapper;
import com.fast.mapper.fee.FastFeeModelMapper;
import com.fast.mapper.mini.FastMiniHisMapper;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.mini.FastMiniSettingMapper;
import com.fast.mapper.retail.FastRetailMiniMapper;
import com.fast.mapper.subscribe.FastMiniSubscribeTemplateMapper;
import com.fast.mapper.upay.UpayTtMerchantMapper;
import com.fast.po.common.FastDomainPO;
import com.fast.po.mini.FastMiniHisPO;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.mini.FastMiniSettingPO;
import com.fast.po.retail.FastRetailMiniPO;
import com.fast.po.subscribe.FastMiniSubscribeTemplatePO;
import com.fast.po.upay.UpayTtMerchantPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.mini.FastMiniVO;
import com.fast.vo.mini.MiniTypeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniService extends BaseService {

    @Autowired
    private FastMiniMapper fastMiniMapper;
    @Autowired
    private FastMiniSettingMapper miniSettingMapper;
    @Autowired
    private FastDomainMapper fastDomainMapper;
    @Autowired
    private FastRetailMiniMapper retailMiniMapper;
    @Autowired
    private FastFeeModelMapper fastFeeModelMapper;
    @Autowired
    private UpayTtMerchantMapper upayTtMerchantMapper;
    @Autowired
    private FastMiniSubscribeTemplateMapper fastMiniSubscribeTemplateMapper;
    @Autowired
    private FastMiniHisMapper fastMiniHisMapper;
    @Lazy
    @Autowired
    private FastMiniSettingService fastMiniSettingService;
    @Autowired
    private FastMiniContVersionService fastMiniContVersionService;

    private static final Set<Integer> threeMiniMap = new ConcurrentHashSet<>(); // 支持三件套的小程序
    private static Long threeMiniLastUpdateTime = 0L; // 上次刷新时间

    /**
     * 判断抖音小程序是否三件套升级
     */
    public boolean judgeThreeMini(Integer miniId) {
        // 10分钟，就开始刷新map
        if (DateUtil.getTimeNowUnix() - threeMiniLastUpdateTime > 30) {
            threeMiniLastUpdateTime = DateUtil.getTimeNowUnix();
            // 查询
            FastMiniPO mParam = new FastMiniPO();
            mParam.setThreeFlag(1);
            List<FastMiniPO> mList = fastMiniMapper.querySimpleList(mParam);
            threeMiniMap.clear();
            for (FastMiniPO mini : mList) {
                threeMiniMap.add(mini.getId());
            }
        }
        return threeMiniMap.contains(miniId);
    }

    // 获取H5站的兜底落地页域名
    public String getGroundNet(Integer contentType) {
        if (contentType == null) {
            return "";
        }
        if (contentType == 1) {
            // 短剧
            return StaticYml.PROMOTE_H5_GROUND_DRAMA_NET;
        } else if (contentType == 2) {
            // 漫画
            return StaticYml.PROMOTE_H5_GROUND_CARTOON_NET;
        } else if (contentType == 3) {
            // 小说
            return StaticYml.PROMOTE_H5_GROUND_NOVEL_NET;
        }
        return "";
    }

    /**
     * 根据appId查询小程序
     */
    public FastMiniVO queryByAppIdRedis(String appId) {
        if (isBlank(appId)) {
            return null;
        }
        FastMiniVO vo = new FastMiniVO();
        FastMiniPO item = new FastMiniPO();
        item.setAppId(appId);
        item.setState(StaticVar.YES);
        String key = StaticVar.MINI_INFO_APPID + item.getAppId();
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            vo = JsonUtil.toJavaObject(value, FastMiniVO.class);
        } else {
            FastMiniPO po = fastMiniMapper.queryOne(item);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_1D);
                return null;
            } else {
                BeanUtils.copyProperties(po, vo);
                RedisUtil.set(key, JsonUtil.toString(vo), RedisUtil.TIME_1D);
            }
        }
        return vo;
    }

    /**
     * 查询小程序信息
     */
    public FastMiniVO queryInfoByRedis(FastMiniPO item) {
        if (item.getId() == null) {
            return null;
        }
        FastMiniVO fastMiniVO = new FastMiniVO();
        String key = StaticVar.MINI_INFO_ID + item.getId();
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            fastMiniVO = JsonUtil.toJavaObject(value, FastMiniVO.class);
        } else {
            item.setState(StaticVar.YES);
            FastMiniPO fastMiniPO = fastMiniMapper.queryOne(item);
            if (fastMiniPO == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_1D);
                return null;
            } else {
                BeanUtils.copyProperties(fastMiniPO, fastMiniVO);
                fastMiniVO.setEncryptionId(encode(fastMiniPO.getId()));
                RedisUtil.set(key, JsonUtil.toString(fastMiniVO), RedisUtil.TIME_1D);
            }
        }
        return fastMiniVO;
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniVO queryInfoByRedis(Integer id) {
        FastMiniPO itemParam = new FastMiniPO();
        itemParam.setId(id);
        return queryInfoByRedis(itemParam);
    }

    /**
     * 通过ids查询多个对象
     */
    public List<FastMiniVO> queryInfoByRedis(String ids) {
        List<FastMiniVO> result = new ArrayList<>();
        if (notBlank(ids)) {
            FastMiniPO itemParam = new FastMiniPO();
            List<Integer> idList = CollUtil.parseIntStr2List(ids);
            for (Integer id : idList) {
                itemParam.setId(id);
                FastMiniVO vo = queryInfoByRedis(itemParam);
                if (vo != null) {
                    result.add(vo);
                }
            }
        }
        return result;
    }

    public FastMiniPO queryById(FastMiniPO item) {
        return fastMiniMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniPO queryById(Integer id) {
        return fastMiniMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniPO queryOne(FastMiniPO item) {
        return fastMiniMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniPO> queryList(FastMiniPO item) {
        return fastMiniMapper.queryList(item);
    }

    /**
     * 查询全部
     */
    public Set<Integer> queryMiniIds(String ids) {
        return fastMiniMapper.queryMiniIds(ids);
    }

    private static final List<MiniTypeVO> TYPE_LIST = new ArrayList<>();

    static {
        // TYPE_LIST.add(new MiniTypeVO(1, "微信小程序"));
        // TYPE_LIST.add(new MiniTypeVO(2, "抖音小程序"));
        // TYPE_LIST.add(new MiniTypeVO(6, "支付宝小程序"));
        // TYPE_LIST.add(new MiniTypeVO(5, "快应用"));
        // TYPE_LIST.add(new MiniTypeVO(4, "快手小程序"));
        // TYPE_LIST.add(new MiniTypeVO(3, "H5网站"));
        TYPE_LIST.add(new MiniTypeVO(7, "APP应用"));
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniPO item, PageVO pageVO) {
        Set<Integer> ids = new HashSet<>();
        if (item.getHadSetting() != null && item.getHadSetting() == 1) {
            FastMiniSettingPO settingPO = new FastMiniSettingPO();
            ids = miniSettingMapper.queryMiniIds(settingPO);
        }
        if (item.getHadFee() != null && item.getHadFee() == 1) {
            Set<Integer> miniIds = fastFeeModelMapper.queryMiniIds();
            if (ids.size() > 0 && miniIds.size() > 0) {
                // 取交集
                for (Integer id : ids) {
                    if (!miniIds.contains(id)) {
                        ids.remove(id);
                    }
                }
            } else {
                ids = miniIds;
            }
        }
        if (ids.size() > 0) {
            item.setIds(StrUtil.join(ids));
        }

        // 若是存在分销商id，则查询分销商应用
        if (biggerZero(item.getRetailId())) {
            String miniIds = retailMiniMapper.queryMiniIdsByRetailId(item.getRetailId());
            if (isBlank(miniIds)) {
                Map<String, Object> results = getPageListData(new ArrayList<>(), pageVO);
                results.put("typeList", TYPE_LIST);
                return ResultVO.success(results);
            }
            item.setIds(miniIds);
            item.setRetailId(null);
        }

        startPage(pageVO);
        List<FastMiniPO> list = fastMiniMapper.queryList(item);
        for (FastMiniPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        results.put("typeList", TYPE_LIST);
        return ResultVO.success(results);
    }

    // 分页查询小程序关联商户号
    public ResultVO<?> queryMerchantPageList(FastMiniPO item, PageVO pageVO) {

        startPage(pageVO);
        List<FastMiniPO> list = fastMiniMapper.queryMerchantList(item);
        for (FastMiniPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询全部名称
     */
    public Map<Integer, String> queryMiniName2Map(FastMiniPO item) {
        List<FastMiniPO> list = fastMiniMapper.queryMiniName(item);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, String> map = new HashMap<>();
        list.forEach(cur -> map.put(cur.getId(), cur.getMiniName() + "[" + (cur.getType() == 1 ? "微信" : cur.getType() == 2 ? "抖音" : "H5") + "]"));
        return map;
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniPO item) {
        return fastMiniMapper.queryCount(item);
    }

    public int queryRetailCount(FastMiniPO item) {
        return fastMiniMapper.queryRetailCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (item.getDefOfficialId() == null) {
            item.setDefOfficialId(0);
        }
        if (fastMiniMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertMiniH5(FastMiniPO miniPO) {
        FastDomainPO domainQ = new FastDomainPO();
        domainQ.setDomainName(miniPO.getAppId());
        domainQ.setDelFlag(0);
        FastDomainPO domainPO = fastDomainMapper.queryOne(domainQ);

        FastMiniPO countQ = new FastMiniPO();
        countQ.setAppId(miniPO.getAppId());
        if (fastMiniMapper.queryCount(countQ) > 0) {
            throw new MyException("域名不能重复使用");
        }
        Date nowTime = DateUtil.getNowDate();
        miniPO.setCreateTime(nowTime);
        if (miniPO.getDefOfficialId() == null) {
            miniPO.setDefOfficialId(0);
        }
        miniPO.setDomainId(domainPO.getId());
        miniPO.setPrincipalName(domainPO.getPrincipalName());
        if (fastMiniMapper.insertSelective(miniPO) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        // 维护域名信息
        domainQ.setMiniId(miniPO.getId());
        domainQ.setId(domainPO.getId());
        fastDomainMapper.updateById(domainQ);
        domainQ.setId(null);
        domainQ.setPid(domainPO.getId());
        fastDomainMapper.updateByPid(domainQ);
        // 维护分销商应用
        FastRetailMiniPO retailMini = new FastRetailMiniPO();
        retailMini.setMiniId(miniPO.getId());
        retailMini.setRetailId(domainPO.getRetailId());
        retailMiniMapper.insertSelective(retailMini);
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateMiniH5(FastMiniPO item) {
        FastMiniPO update = new FastMiniPO();
        update.setId(item.getId());
        update.setMiniName(item.getMiniName());
        update.setUpdatorId(item.getUpdatorId());
        update.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniMapper.updateById(update) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateMiniMerchant(FastMiniPO item) {
        FastMiniPO miniPO = fastMiniMapper.queryById(item.getId());
        // 更新商户号表
        Date timeNow = DateUtil.getNowDate();
        UpayTtMerchantPO tmPO = new UpayTtMerchantPO();
        tmPO.setMiniId(miniPO.getId());
        tmPO.setAppId(miniPO.getAppId());
        tmPO.setCreateTime(timeNow);
        tmPO.setUpdateTime(timeNow);
        tmPO.setCreatorId(item.getUpdatorId());
        tmPO.setUpdatorId(item.getUpdatorId());
        tmPO.setMerchantNum(item.getMerchantNum());
        tmPO.setState(1);
        if (upayTtMerchantMapper.insertSelective(tmPO) > 0) {
            // 清除缓存
            String key = StaticVar.TT_MERCHANT + miniPO.getAppId();
            RedisUtil.del(key);
            return MethodVO.success("操作成功");
        }
        return MethodVO.error("操作失败");
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniPO params) {
        params.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateWithLock(FastMiniPO params) {
        params.setUpdateTime(DateUtil.getNowDate());
        String key = StaticVar.LOCK_MINI_UP + params.getId();
        String res = RedisUtil.get(key);
        if (StrUtil.isNotEmpty(res)) {
            // actionLogService.log("update_log","更新中,miniId="+params.getId());
            return MethodVO.error("更新中");
        }
        RedisUtil.set(key, "update", 10); // 默认放置10秒
        if (fastMiniMapper.updateById(params) == 0) {
            transactionRollBack();
            RedisUtil.del(key);
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        RedisUtil.del(key);
        // actionLogService.log("update_log","成功更新一次,miniId="+params.getId());
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDefaultOfficialId(FastMiniPO params) {
        params.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniMapper.updateDefaultOfficialId(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        FastMiniPO miniPO = fastMiniMapper.queryById(params);
        if (miniPO != null) {
            // 清下缓存
            String key = StaticVar.MINI_INFO_APPID + miniPO.getAppId();
            RedisUtil.del(key);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateFeeFlag(List<Integer> idList, FastMiniPO params) {
        for (Integer id : idList) {
            params.setId(id);
            params.setUpdateTime(DateUtil.getNowDate());
            FastMiniPO miniDBPO = fastMiniMapper.queryById(id);
            // 查询数据库里的feeFlag，比较是否变化
            if (miniDBPO.getFeeFlag() != null && !miniDBPO.getFeeFlag().equals(params.getFeeFlag())) {
                // 查询正在生效的，设置为无效
                FastMiniHisPO mhParam = new FastMiniHisPO();
                mhParam.setValidFlag(1);
                mhParam.setMiniId(miniDBPO.getId());
                FastMiniHisPO mhPO = fastMiniHisMapper.queryOne(mhParam);
                Date timeNow = DateUtil.getNowDate();
                if (mhPO == null) {
                    mhPO = new FastMiniHisPO();
                    mhPO.setCreateTime(timeNow);
                    mhPO.setCreatorId(params.getUpdatorId());
                    mhPO.setType(1);
                    mhPO.setFeeFlag(miniDBPO.getFeeFlag());
                    mhPO.setValidFlag(0);
                    mhPO.setValidStart(miniDBPO.getCreateTime());
                    mhPO.setMiniId(miniDBPO.getId());
                    mhPO.setValidEnd(timeNow);
                    fastMiniHisMapper.insertSelective(mhPO);
                } else {
                    mhPO.setValidEnd(timeNow);
                    mhPO.setUpdatorId(params.getUpdatorId());
                    mhPO.setValidFlag(0);
                    fastMiniHisMapper.updateById(mhPO);
                }
                // 生成新记录
                FastMiniHisPO mhNewPO = new FastMiniHisPO();
                mhNewPO.setCreateTime(timeNow);
                mhNewPO.setCreatorId(params.getUpdatorId());
                mhNewPO.setType(1);
                mhNewPO.setFeeFlag(params.getFeeFlag());
                mhNewPO.setValidFlag(1);
                mhNewPO.setValidStart(timeNow);
                mhNewPO.setMiniId(miniDBPO.getId());
                fastMiniHisMapper.insertSelective(mhNewPO);
            }
            if (fastMiniMapper.updateFeeFlag(params) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        return MethodVO.success();
    }

    public ResultVO<?> updateMiniName(FastMiniPO params) {
        // 查询原小程序
        FastMiniPO miniPO = fastMiniMapper.queryById(params.getId());
        if (miniPO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }
        if ("未命名".equals(miniPO.getMiniName()) || miniPO.getMiniName().contains("名称过期")) {
            // 可以修改
        } else {
            return ResultVO.error(StaticStr.ILLEGAL_PARAM);
        }
        params.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniMapper.updateMiniName(params) == 0) {
            transactionRollBack();
            return ResultVO.error(StaticStr.UPDATE_FAILED);
        }
        return ResultVO.success();
    }

    public ResultVO<?> updateTag(FastMiniPO params) {
        FastMiniPO update = new FastMiniPO();
        update.setAppId(params.getAppId());
        update.setTag(params.getTag());
        update.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniMapper.updateTagByAppId(params) == 0) {
            transactionRollBack();
            return ResultVO.error(StaticStr.UPDATE_FAILED);
        }
        return ResultVO.success();
    }

    /**
     * 获得小程序功能配置
     *
     * @param params 小程序id
     */
    public ResultVO<?> getAppConf(FastMiniPO params) {
        // 获得小程序
        FastMiniPO miniPO = fastMiniMapper.queryById(params.getId());
        if (miniPO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }

        // 查询消息模板
        List<FastMiniSubscribeTemplatePO> templateList = fastMiniSubscribeTemplateMapper.queryListByMiniId(miniPO.getId());
        templateList.forEach(t -> {
            if (t.getType() == 1) {
                miniPO.setDramaUpdateTemplateId(t.getWechatTemplateId());
            } else if (t.getType() == 2) {
                miniPO.setHotDramaTemplateId(t.getWechatTemplateId());
            } else if (t.getType() == 3) {
                miniPO.setSignWarnTemplateId(t.getWechatTemplateId());
            }
        });

        // 查询设置
        FastMiniSettingPO msPO = fastMiniSettingService.querySettingByRedis(params.getId(), params.getContVersionId());
        if (msPO != null) {
            miniPO.setWechatAdqDataId(msPO.getWechatAdqDataId());
            miniPO.setWechatAdqSecret(msPO.getWechatAdqSecret());
        }
        return ResultVO.success(miniPO);
    }

    /**
     * 更新小程序功能配置
     *
     * @param params 小程序id
     */
    public ResultVO<?> appConf(FastMiniPO params) {
        // 获得小程序
        FastMiniPO miniPO = fastMiniMapper.queryById(params.getId());
        if (miniPO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }
        miniPO.setFeeFlag(params.getFeeFlag());
        miniPO.setForbidBackFlag(params.getForbidBackFlag());
        // 抖小额外配置
        if (miniPO.getType() == 2) {
            miniPO.setAuthStatus(params.getAuthStatus());
            miniPO.setAccelerateFlag(params.getAccelerateFlag());
            miniPO.setDesktopFlag(params.getDesktopFlag());
        }

        // 更新小程序配置
        miniPO.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniMapper.updateById(miniPO) == 0) {
            transactionRollBack();
            return ResultVO.error(StaticStr.UPDATE_FAILED);
        }
        // 更新小程序配置表
        boolean updateSetting = StrUtil.isNotEmpty(params.getWechatAdqDataId());
        if (StrUtil.isNotEmpty(params.getWechatAdqSecret())) {
            updateSetting = true;
        }
        if (updateSetting) {
            FastMiniSettingPO msParam = new FastMiniSettingPO();
            msParam.setMiniId(params.getId());
            msParam.setWechatAdqDataId(params.getWechatAdqDataId());
            msParam.setWechatAdqSecret(params.getWechatAdqSecret());
            if (miniSettingMapper.updateByMiniId(msParam) > 0) {
                // // 清除缓存
                // String key = StaticVar.MINI_CONTENT_SETTING_ONLY + params.getId();
                // RedisUtil.del(key);
                // 多版本兼容
                List<Integer> cvids = fastMiniContVersionService.getIdByMiniId(params.getId());
                if (CollUtil.isNotEmpty(cvids)) {
                    for (Integer cvid : cvids) {
                        RedisUtil.del(StaticVar.MINI_CONTENT_SETTING_ONLY + params.getId() + ":" + cvid);
                    }
                }
            }
        }
        // 删除缓存
        String key = StaticVar.MINI_INFO_APPID + miniPO.getAppId();
        RedisUtil.del(key);
        RedisUtil.del(StaticVar.MINI_INFO_ID + miniPO.getId());

        // 消息模板配置
        updateTemplate(miniPO.getId(), 1, params.getDramaUpdateTemplateId()); // 短剧更新提醒
        updateTemplate(miniPO.getId(), 2, params.getHotDramaTemplateId()); // 热门短剧提醒
        updateTemplate(miniPO.getId(), 3, params.getSignWarnTemplateId()); // 签到订阅提醒

        return ResultVO.success();
    }

    /**
     * 更新小程消息模板配置
     *
     * @param miniId     小程序id
     * @param type       模板类型 1：追剧更新提醒 2：热剧推荐 3：签到提醒
     * @param templateId 模板id
     */
    private void updateTemplate(Integer miniId, Integer type, String templateId) {
        FastMiniSubscribeTemplatePO template = new FastMiniSubscribeTemplatePO();
        template.setMiniId(miniId);
        template.setType(type);
        template.setWechatTemplateId(templateId);
        if (fastMiniSubscribeTemplateMapper.updateByMiniId(template) == 0) {
            // 更新失败说明没有配置，新增
            template.setRemark(type == 1 ? "追剧更新提醒" : "热剧推荐");
            template.setRemark(type == 3 ? "签到提醒" : template.getRemark());
            if (fastMiniSubscribeTemplateMapper.insertSelective(template) == 0) {
                transactionRollBack();
                throw new MyException("更新消息模板配置失败");
            }
        }
    }

    @Async
    public void clearAppCache() {
        List<FastMiniPO> list = fastMiniMapper.queryList(new FastMiniPO());
        list.forEach(mini -> {
            RedisUtil.del(StaticVar.MINI_INFO_APPID + mini.getAppId());
            RedisUtil.del(StaticVar.MINI_INFO_ID + mini.getId());
        });
    }

    /**
     * 新增APP
     *
     * @param params APP信息
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertApp(FastMiniPO params) {
        FastMiniPO fastMiniPO = fastMiniMapper.queryByAppId(params.getAppId());
        if (fastMiniPO != null) {
            return MethodVO.error("包名已存在");
        }

        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (params.getDefOfficialId() == null) {
            params.setDefOfficialId(0);
        }

        if (fastMiniMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        return MethodVO.success();
    }

}
