/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniRecommendMapper;
import com.fast.po.mini.FastMiniRecommendPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniRecommendService extends BaseService {

    @Autowired
    private FastMiniRecommendMapper fastMiniRecommendMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniRecommendPO queryById(FastMiniRecommendPO item) {
        return fastMiniRecommendMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniRecommendPO queryById(Integer id) {
        FastMiniRecommendPO itemParam = new FastMiniRecommendPO();
        itemParam.setId(id);
        return fastMiniRecommendMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniRecommendPO queryOne(FastMiniRecommendPO item) {
        return fastMiniRecommendMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniRecommendPO> queryList(FastMiniRecommendPO item) {
        return fastMiniRecommendMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniRecommendPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniRecommendPO> list = fastMiniRecommendMapper.queryList(item);
        for (FastMiniRecommendPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniRecommendPO item) {
        return fastMiniRecommendMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniRecommendPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastMiniRecommendMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniRecommendPO> list) {
        if (fastMiniRecommendMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniRecommendPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniRecommendMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
