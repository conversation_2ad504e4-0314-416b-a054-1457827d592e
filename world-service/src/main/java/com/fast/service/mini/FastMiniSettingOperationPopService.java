/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniSettingOperationPopMapper;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.mini.FastMiniSettingOperationPopPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniSettingOperationPopService extends BaseService {

    @Autowired
    private FastMiniSettingOperationPopMapper fastMiniSettingOperationPopMapper;
    @Autowired
    private FastMiniService fastMiniService;

    /**
     * 通过id查询单个对象
     */
    public FastMiniSettingOperationPopPO queryById(FastMiniSettingOperationPopPO params) {
        return fastMiniSettingOperationPopMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniSettingOperationPopPO queryById(Integer id) {
        return fastMiniSettingOperationPopMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniSettingOperationPopPO queryOne(FastMiniSettingOperationPopPO params) {
        return fastMiniSettingOperationPopMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniSettingOperationPopPO> queryList(FastMiniSettingOperationPopPO params) {
        return fastMiniSettingOperationPopMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniSettingOperationPopPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniSettingOperationPopPO> list = fastMiniSettingOperationPopMapper.queryList(params);
        for (FastMiniSettingOperationPopPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniSettingOperationPopPO params) {
        return fastMiniSettingOperationPopMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniSettingOperationPopPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMiniSettingOperationPopMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniSettingOperationPopPO> list) {
        if (fastMiniSettingOperationPopMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniSettingOperationPopPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniSettingOperationPopMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 运营能力-获得浮窗配置
     *
     * @param params 参数
     * @param pageVO 分页
     */
    public ResultVO<?> getList(FastMiniSettingOperationPopPO params, PageVO pageVO) {
        FastMiniVO miniVO = fastMiniService.queryInfoByRedis(params.getMiniId());
        if (miniVO == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        FastMiniSettingOperationPopPO query = new FastMiniSettingOperationPopPO();
        query.setMiniId(params.getMiniId());
        startPage(pageVO);
        List<FastMiniSettingOperationPopPO> list = fastMiniSettingOperationPopMapper.queryList(query);
        for (FastMiniSettingOperationPopPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 运营能力-新增或更新浮窗
     *
     * @param params 参数
     */
    public ResultVO<?> addOrEdit(FastMiniSettingOperationPopPO params) {
        FastMiniVO miniVO = fastMiniService.queryInfoByRedis(params.getMiniId());
        if (miniVO == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);

        // 存在id则更新
        if (biggerZero(params.getId())) {
            if (fastMiniSettingOperationPopMapper.queryCountByMiniIdPopTypePopPage(params) > 0) {
                return ResultVO.error(StaticStr.RECORD_EXIST);
            }
            if (fastMiniSettingOperationPopMapper.updateById(params) == 0) {
                return ResultVO.error(StaticStr.UPDATE_FAILED);
            }
        } else {
            params.setId(null);
            params.setCreatorId(params.getUpdatorId());
            params.setCreateTime(nowTime);
            if (fastMiniSettingOperationPopMapper.queryCountByMiniIdPopTypePopPage(params) > 0) {
                return ResultVO.error(StaticStr.RECORD_EXIST);
            }
            if (fastMiniSettingOperationPopMapper.insertSelective(params) == 0) {
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
        }
        return ResultVO.success();
    }

    /**
     * 运营能力-复制浮窗配置
     *
     * @param params 参数
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> copy(FastMiniSettingOperationPopPO params) {
        FastMiniVO miniVO = fastMiniService.queryInfoByRedis(params.getMiniId());
        if (miniVO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }
        FastMiniVO copyMiniVO = fastMiniService.queryInfoByRedis(params.getCopyMiniId());
        if (copyMiniVO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }
        Date nowTime = DateUtil.getNowDate();

        // 删除原有配置
        fastMiniSettingOperationPopMapper.deleteByMiniId(params.getMiniId());

        FastMiniSettingOperationPopPO query = new FastMiniSettingOperationPopPO();
        query.setMiniId(copyMiniVO.getId());
        List<FastMiniSettingOperationPopPO> list = fastMiniSettingOperationPopMapper.queryList(query);
        for (FastMiniSettingOperationPopPO po : list) {
            po.setId(null);
            po.setMiniId(miniVO.getId());
            po.setCreatorId(params.getUpdatorId());
            po.setUpdatorId(params.getUpdatorId());
            po.setCreateTime(nowTime);
            po.setUpdateTime(nowTime);
            if (fastMiniSettingOperationPopMapper.insertSelective(po) == 0) {
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
        }
        return ResultVO.success();
    }

    /**
     * 运营能力-删除浮窗配置
     *
     * @param params 参数
     */
    public ResultVO<?> getMiniPopConfig(FastMiniPO params) {
        FastMiniVO miniVO = fastMiniService.queryByAppIdRedis(params.getAppId());
        if (miniVO == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        FastMiniSettingOperationPopPO query = new FastMiniSettingOperationPopPO();
        query.setMiniId(miniVO.getId());
        List<FastMiniSettingOperationPopPO> list = fastMiniSettingOperationPopMapper.queryList(query);
        for (FastMiniSettingOperationPopPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(list);
    }

    /**
     * 运营能力-删除浮窗配置
     *
     * @param params 参数
     */
    public ResultVO<?> delete(FastMiniSettingOperationPopPO params) {
        if (isEmpty(params.getEncryptionId()) || params.getId() == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer miniId = decodeInt(params.getEncryptionId());
        if (miniId == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        params.setMiniId(miniId);
        if (fastMiniSettingOperationPopMapper.deleteById(params) == 0) {
            return ResultVO.error(StaticStr.DELETE_FAILED);
        }
        return ResultVO.success();
    }
}
