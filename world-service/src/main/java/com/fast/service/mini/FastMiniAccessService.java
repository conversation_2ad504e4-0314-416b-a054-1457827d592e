/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniAccessMapper;
import com.fast.po.mini.FastMiniAccessPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniAccessService extends BaseService {

    @Autowired
    private FastMiniAccessMapper fastMiniAccessMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniAccessPO queryById(FastMiniAccessPO item) {
        return fastMiniAccessMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniAccessPO queryById(Integer id) {
        FastMiniAccessPO itemParam = new FastMiniAccessPO();
        itemParam.setId(id);
        return fastMiniAccessMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniAccessPO queryOne(FastMiniAccessPO item) {
        return fastMiniAccessMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniAccessPO> queryList(FastMiniAccessPO item) {
        return fastMiniAccessMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniAccessPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniAccessPO> list = fastMiniAccessMapper.queryList(item);
        for (FastMiniAccessPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniAccessPO item) {
        return fastMiniAccessMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniAccessPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastMiniAccessMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniAccessPO> list) {
        if (fastMiniAccessMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniAccessPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniAccessMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
