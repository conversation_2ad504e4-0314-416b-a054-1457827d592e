/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.mapper.mini.FastMiniApiLogMapper;
import com.fast.po.mini.FastMiniApiLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 抖音接口调用日志
 *
 * <AUTHOR>
 */
@Service
public class FastMiniApiLogService extends BaseService {

    @Autowired
    private FastMiniApiLogMapper fastMiniApiLogMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniApiLogPO queryById(FastMiniApiLogPO params) {
        return fastMiniApiLogMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniApiLogPO queryById(Integer id) {
        return fastMiniApiLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniApiLogPO queryOne(FastMiniApiLogPO params) {
        return fastMiniApiLogMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniApiLogPO> queryList(FastMiniApiLogPO params) {
        return fastMiniApiLogMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniApiLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniApiLogPO> list = fastMiniApiLogMapper.queryList(params);
        for (FastMiniApiLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniApiLogPO params) {
        return fastMiniApiLogMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void insert(FastMiniApiLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setCreateDate(nowTime);
        if (fastMiniApiLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
        }
    }
}
