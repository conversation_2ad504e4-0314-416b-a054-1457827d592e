/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniRecommendModularMapper;
import com.fast.po.mini.FastMiniRecommendModularPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniRecommendModularService extends BaseService {

    @Autowired
    private FastMiniRecommendModularMapper fastMiniRecommendModularMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniRecommendModularPO queryById(FastMiniRecommendModularPO item) {
        return fastMiniRecommendModularMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniRecommendModularPO queryById(Integer id) {
        return fastMiniRecommendModularMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniRecommendModularPO queryOne(FastMiniRecommendModularPO item) {
        return fastMiniRecommendModularMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniRecommendModularPO> queryList(FastMiniRecommendModularPO item) {
        return fastMiniRecommendModularMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniRecommendModularPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniRecommendModularPO> list = fastMiniRecommendModularMapper.queryList(item);
        for (FastMiniRecommendModularPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniRecommendModularPO item) {
        return fastMiniRecommendModularMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniRecommendModularPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMiniRecommendModularMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniRecommendModularPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMiniRecommendModularMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
