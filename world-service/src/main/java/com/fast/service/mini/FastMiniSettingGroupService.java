/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniSettingGroupMapper;
import com.fast.po.mini.FastMiniSettingGroupPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 小程序配置-用户群
 *
 * <AUTHOR>
 */
@Service
public class FastMiniSettingGroupService extends BaseService {

    @Autowired
    private FastMiniSettingGroupMapper fastMiniSettingGroupMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniSettingGroupPO queryById(FastMiniSettingGroupPO item) {
        return fastMiniSettingGroupMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniSettingGroupPO queryById(Integer id) {
        return fastMiniSettingGroupMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniSettingGroupPO queryOne(FastMiniSettingGroupPO item) {
        return fastMiniSettingGroupMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniSettingGroupPO> queryList(FastMiniSettingGroupPO item) {
        return fastMiniSettingGroupMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniSettingGroupPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniSettingGroupPO> list = fastMiniSettingGroupMapper.queryList(item);
        for (FastMiniSettingGroupPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniSettingGroupPO item) {
        return fastMiniSettingGroupMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniSettingGroupPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMiniSettingGroupMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniSettingGroupPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMiniSettingGroupMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
