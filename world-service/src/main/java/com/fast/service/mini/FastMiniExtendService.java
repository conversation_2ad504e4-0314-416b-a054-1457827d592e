/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.mini.FastMiniExtendMapper;
import com.fast.po.mini.FastMiniExtendPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniExtendService extends BaseService {

    @Autowired
    private FastMiniExtendMapper fastMiniExtendMapper;


    public FastMiniExtendPO queryByCodeRedis(Integer miniId, String code) {
        String key = StaticVar.MINI_EXTEND + miniId + "_" + code;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            FastMiniExtendPO meParam = new FastMiniExtendPO();
            meParam.setCode(code);
            meParam.setMiniId(miniId);
            FastMiniExtendPO mePO = fastMiniExtendMapper.queryOne(meParam);
            if (mePO != null) {
                res = JsonUtil.toString(mePO);
            } else {
                res = StaticVar.EMPTY_FLAG;
            }
            RedisUtil.set(key, res, 60 * 10);
        }
        if (StaticVar.EMPTY_FLAG.equals(res)) {
            return null;
        }
        return JsonUtil.toJavaObject(res, FastMiniExtendPO.class);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniExtendPO queryById(FastMiniExtendPO params) {
        return fastMiniExtendMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniExtendPO queryById(Integer id) {
        return fastMiniExtendMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniExtendPO queryOne(FastMiniExtendPO params) {
        return fastMiniExtendMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMiniExtendPO> queryList(FastMiniExtendPO params) {
        return fastMiniExtendMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniExtendPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniExtendPO> list = fastMiniExtendMapper.queryList(params);
        for (FastMiniExtendPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniExtendPO params) {
        return fastMiniExtendMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniExtendPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMiniExtendMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniExtendPO> list) {
        if (fastMiniExtendMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniExtendPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMiniExtendMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
