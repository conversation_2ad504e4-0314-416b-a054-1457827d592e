/*
 * Powered By fast.up
 */
package com.fast.service.mini;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniBottomMapper;
import com.fast.po.mini.FastMiniBottomPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniBottomService extends BaseService {

    @Autowired
    private FastMiniBottomMapper fastMiniBottomMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniBottomPO queryById(FastMiniBottomPO item) {
        return fastMiniBottomMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniBottomPO queryById(Integer id) {
        FastMiniBottomPO itemParam = new FastMiniBottomPO();
        itemParam.setId(id);
        return fastMiniBottomMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniBottomPO queryOne(FastMiniBottomPO item) {
        return fastMiniBottomMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniBottomPO> queryList(FastMiniBottomPO item) {
        return fastMiniBottomMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniBottomPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniBottomPO> list = fastMiniBottomMapper.queryList(item);
        for (FastMiniBottomPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniBottomPO item) {
        return fastMiniBottomMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniBottomPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastMiniBottomMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniBottomPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniBottomMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
