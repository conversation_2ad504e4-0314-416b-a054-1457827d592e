/*
 * Powered By fast.up
 */
package com.fast.service.clean;

import com.fast.constant.StaticVar;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.po.member.FastMemberRecentSeriesDayPO;
import com.fast.po.member.FastMemberRecentSeriesNumPO;
import com.fast.service.base.BaseService;
import com.fast.service.member.FastMemberRecentLogService;
import com.fast.service.member.FastMemberRecentSeriesDayService;
import com.fast.service.member.FastMemberRecentSeriesNumService;
import com.fast.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastRecentLogDataCleanService extends BaseService {

    @Autowired
    private FastMemberRecentLogService recentLogService;
    @Autowired
    private FastMemberRecentSeriesDayService recentSeriesDayService;
    @Autowired
    private FastMemberRecentSeriesNumService recentSeriesNumService;

    /**
     * 观看记录数据每人每剧每天去重
     * 历史数据处理
     */
    public void recentLogRemoveRepeatByDay() {
        long maxId = toLong(recentSeriesDayService.queryMaxId(), 0L);
        FastMemberRecentLogPO params = new FastMemberRecentLogPO();
        params.setLimitExport(StaticVar.DATA_2K);
        List<FastMemberRecentLogPO> list = new ArrayList<>(StaticVar.DATA_2K);
        do {
            list.clear();
            params.setMinId(++maxId);
            list.addAll(recentLogService.querySlaveList(params));
            if (list.size() == 0) {
                return;
            }
            maxId = list.get(list.size() - 1).getId();
            for (FastMemberRecentLogPO recentLog : list) {
                FastMemberRecentSeriesDayPO update = new FastMemberRecentSeriesDayPO();
                update.setId(recentLog.getId());
                if (recentLog.getPlayState() == 1) {
                    update.setPlayState(recentLog.getPlayState());
                }
                update.setLinkId(recentLog.getLinkId());
                update.setMiniId(recentLog.getMiniId());
                update.setRetailId(recentLog.getRetailId());
                update.setOfficialId(recentLog.getOfficialId());
                update.setDramaId(recentLog.getDramaId());
                update.setSeriesNum(recentLog.getSeriesNum());
                update.setMemberId(recentLog.getMemberId());
                update.setCreateDate(DateUtil.format06Int(recentLog.getCreateTime()));
                update.setPlaySecond(recentLog.getPlaySecond());
                if (recentSeriesDayService.updateSeriesFinish(update) == 0) {
                    try {
                        FastMemberRecentSeriesDayPO count = new FastMemberRecentSeriesDayPO();
                        count.setDramaId(update.getDramaId());
                        count.setSeriesNum(update.getSeriesNum());
                        count.setMemberId(update.getMemberId());
                        count.setCreateDate(update.getCreateDate());
                        if (recentSeriesDayService.queryCount(count) == 0) {
                            recentSeriesDayService.insertSelective(update);
                        }
                    } catch (Exception e) {
                        log.error("观看记录数据每人每剧每集去重, 插入失败:{}", e.getMessage());
                    }
                }
            }
        } while (list.size() == StaticVar.DATA_2K);
    }

    /**
     * 观看记录数据每人每剧每集去重
     * 历史数据处理
     */
    public void recentLogRemoveRepeatBySeriesNum() {
        long maxId = toLong(recentSeriesNumService.queryMaxId(), 0L);
        FastMemberRecentLogPO params = new FastMemberRecentLogPO();
        params.setLimitExport(StaticVar.DATA_2K);
        List<FastMemberRecentLogPO> list = new ArrayList<>(StaticVar.DATA_2K);
        do {
            list.clear();
            params.setMinId(++maxId);
            list.addAll(recentLogService.querySlaveList(params));
            if (list.size() == 0) {
                return;
            }
            maxId = list.get(list.size() - 1).getId();
            for (FastMemberRecentLogPO recentLog : list) {
                FastMemberRecentSeriesNumPO update = new FastMemberRecentSeriesNumPO();
                update.setId(recentLog.getId());
                if (recentLog.getPlayState() == 1) {
                    update.setPlayState(recentLog.getPlayState());
                }
                update.setDramaId(recentLog.getDramaId());
                update.setSeriesNum(recentLog.getSeriesNum());
                update.setMemberId(recentLog.getMemberId());
                update.setPlaySecond(recentLog.getPlaySecond());
                if (recentSeriesNumService.updateSeriesFinish(update) == 0) {
                    try {
                        FastMemberRecentSeriesNumPO count = new FastMemberRecentSeriesNumPO();
                        count.setDramaId(update.getDramaId());
                        count.setSeriesNum(update.getSeriesNum());
                        count.setMemberId(update.getMemberId());
                        if (recentSeriesNumService.queryCount(count) == 0) {
                            recentSeriesNumService.insertSelective(update);
                        }
                    } catch (Exception e) {
                        log.error("观看记录数据每人每剧每集去重, 插入失败:{}", e.getMessage());
                    }
                }
            }
        } while (list.size() == StaticVar.DATA_2K);
    }
}
