/*
 * Powered By fast.up
 */
package com.fast.service.back;

import com.fast.constant.StaticStr;
import com.fast.mapper.back.FastBackDeductionDetailMapper;
import com.fast.po.back.FastBackDeductionDetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastBackDeductionDetailService extends BaseService {

    @Autowired
    private FastBackDeductionDetailMapper fastBackDeductionDetailMapper;

    /**
     * 通过id查询单个对象
     */
    public FastBackDeductionDetailPO queryById(FastBackDeductionDetailPO params) {
        return fastBackDeductionDetailMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastBackDeductionDetailPO queryById(Integer id) {
        return fastBackDeductionDetailMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastBackDeductionDetailPO queryOne(FastBackDeductionDetailPO params) {
        return fastBackDeductionDetailMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastBackDeductionDetailPO> queryList(FastBackDeductionDetailPO params) {
        return fastBackDeductionDetailMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastBackDeductionDetailPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastBackDeductionDetailPO> list = fastBackDeductionDetailMapper.queryList(params);
        for (FastBackDeductionDetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastBackDeductionDetailPO params) {
        return fastBackDeductionDetailMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastBackDeductionDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastBackDeductionDetailMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastBackDeductionDetailPO> list) {
        if (fastBackDeductionDetailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastBackDeductionDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastBackDeductionDetailMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
