/*
 * Powered By fast.up
 */
package com.fast.service.back;

import com.fast.constant.StaticStr;
import com.fast.mapper.back.FastBackDeductionMapper;
import com.fast.po.back.FastBackDeductionPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastBackDeductionService extends BaseService {

    @Autowired
    private FastBackDeductionMapper fastBackDeductionMapper;

    /**
     * 通过id查询单个对象
     */
    public FastBackDeductionPO queryById(FastBackDeductionPO params) {
        return fastBackDeductionMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastBackDeductionPO queryById(Integer id) {
        return fastBackDeductionMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastBackDeductionPO queryOne(FastBackDeductionPO params) {
        return fastBackDeductionMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastBackDeductionPO> queryList(FastBackDeductionPO params) {
        return fastBackDeductionMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastBackDeductionPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastBackDeductionPO> list = fastBackDeductionMapper.queryList(params);
        for (FastBackDeductionPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastBackDeductionPO params) {
        return fastBackDeductionMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastBackDeductionPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastBackDeductionMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastBackDeductionPO> list) {
        if (fastBackDeductionMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastBackDeductionPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastBackDeductionMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
