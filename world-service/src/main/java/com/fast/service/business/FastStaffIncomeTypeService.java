/*
 * Powered By fast.up
 */
package com.fast.service.business;

import com.fast.constant.StaticStr;
import com.fast.mapper.business.FastStaffIncomeTypeMapper;
import com.fast.po.business.FastStaffIncomeTypePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastStaffIncomeTypeService extends BaseService {

    @Autowired
    private FastStaffIncomeTypeMapper fastStaffIncomeTypeMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStaffIncomeTypePO queryById(FastStaffIncomeTypePO params) {
        return fastStaffIncomeTypeMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStaffIncomeTypePO queryById(Integer id) {
        return fastStaffIncomeTypeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStaffIncomeTypePO queryOne(FastStaffIncomeTypePO params) {
        return fastStaffIncomeTypeMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffIncomeTypePO> queryList(FastStaffIncomeTypePO params) {
        return fastStaffIncomeTypeMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStaffIncomeTypePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStaffIncomeTypePO> list = fastStaffIncomeTypeMapper.queryList(params);
        for (FastStaffIncomeTypePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStaffIncomeTypePO params) {
        return fastStaffIncomeTypeMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStaffIncomeTypePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStaffIncomeTypeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStaffIncomeTypePO> list) {
        if (fastStaffIncomeTypeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStaffIncomeTypePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStaffIncomeTypeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastStaffIncomeTypePO params) {
        if (fastStaffIncomeTypeMapper.deleteById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
