/*
 * Powered By fast.up
 */
package com.fast.service.business;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.audit.AuditTypeEnum;
import com.fast.mapper.audit.FastAuditFlowMapper;
import com.fast.mapper.business.FastIncomeManualEntryMapper;
import com.fast.po.audit.FastAuditFlowPO;
import com.fast.po.audit.FastAuditProcessPO;
import com.fast.po.business.FastIncomeManualEntryPO;
import com.fast.service.audit.FastAuditProcessService;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastIncomeManualEntryService extends BaseService {

    @Autowired
    private FastIncomeManualEntryMapper incomeManualEntryMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    @Autowired
    private FastAuditProcessService fastAuditProcessService;

    @Autowired
    private FastAuditFlowMapper fastAuditFlowMapper;

    /**
     * 通过id查询单个对象
     */
    public FastIncomeManualEntryPO queryById(FastIncomeManualEntryPO params) {
        return incomeManualEntryMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastIncomeManualEntryPO queryById(Integer id) {
        return incomeManualEntryMapper.queryById(id);
    }

    /**
     * 通过id查询单个对象
     */
    public FastIncomeManualEntryPO queryJoinTypeById(FastIncomeManualEntryPO params) {
        return incomeManualEntryMapper.queryJoinTypeById(params);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastIncomeManualEntryPO queryOne(FastIncomeManualEntryPO params) {
        return incomeManualEntryMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastIncomeManualEntryPO> queryList(FastIncomeManualEntryPO params) {
        return incomeManualEntryMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastIncomeManualEntryPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastIncomeManualEntryPO> list = incomeManualEntryMapper.queryListJoinType(params);
        for (FastIncomeManualEntryPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getTimeType() == 3) {
                if (Objects.nonNull(cur.getAuditState()) && cur.getAuditState() != 2) {
                    if (cur.getAuditState() == 1) {
                        // 待审核
                        cur.setState(2);
                    } else {
                        // 审核不通过
                        cur.setState(3);
                    }
                }
                cur.setEffectDateStr(DateUtil.format10(DateUtil.format05(cur.getEffectDate().toString())));
            } else {
                cur.setEffectDateStr(DateUtil.format09(DateUtil.format06(cur.getEffectDate().toString())));
            }
        }
        FastIncomeManualEntryPO summary = incomeManualEntryMapper.querySumIncome(params);
        return ResultVO.summary(getPageListData(list, pageVO), summary);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> excelExport(FastIncomeManualEntryPO params, SessionVO sessionVO, String key) {
        List<FastIncomeManualEntryPO> list = incomeManualEntryMapper.queryListJoinType(params);
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastIncomeManualEntryPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)
            row.add(cur.getTitle());
            CollUtil.addNoRepeat(rowHeadNames, "收入项目");

            row.add(cur.getIncome());
            CollUtil.addNoRepeat(rowHeadNames, "收入总金额");

            if (cur.getTimeType() == 3) {
                row.add(DateUtil.format10(DateUtil.format05(cur.getEffectDate().toString())));
            } else {
                row.add(DateUtil.format09(DateUtil.format06(cur.getEffectDate().toString())));
            }
            CollUtil.addNoRepeat(rowHeadNames, "纳入收入日期");

            if (cur.getTimeType() == 3) {
                row.add(cur.getEffectMonthStr());
                CollUtil.addNoRepeat(rowHeadNames, "纳入CP结算期");
            }

            if (cur.getDramaId() != null && cur.getDramaId() > 0) {
                row.add(cur.getDramaId() + "-" + cur.getDramaName());
            } else {
                row.add("-");
            }
            CollUtil.addNoRepeat(rowHeadNames, "收入指定短剧");

            row.add(biggerZero(cur.getDramaId()) ? cur.getDramaId() : "-");
            CollUtil.addNoRepeat(rowHeadNames, "短剧ID");

            if (cur.getTimeType() == 3) {
                row.add(cur.getIsRenovate() == 1 ? "是" : "否");
                CollUtil.addNoRepeat(rowHeadNames, "是否翻新收入");
            }

            row.add(cur.getCreatorName());
            CollUtil.addNoRepeat(rowHeadNames, "录入人");

            if (cur.getTimeType() == 3) {
                row.add(cur.getJoinCpSettle() == 1 ? "是" : "否");
                CollUtil.addNoRepeat(rowHeadNames, "是否已参与CP结算");
            }

            row.add(DateUtil.format07(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "录入时间");

            dataList.add(row);
        }
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "收入管理-列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 查询总数
     */
    public int queryCount(FastIncomeManualEntryPO params) {
        return incomeManualEntryMapper.queryCount(params);
    }

    public List<FastIncomeManualEntryPO> queryIncome(FastIncomeManualEntryPO params) {
        return incomeManualEntryMapper.queryIncome(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastIncomeManualEntryPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (incomeManualEntryMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastIncomeManualEntryPO> list) {
        if (incomeManualEntryMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastIncomeManualEntryPO params) {
        // Date nowTime = DateUtil.getNowDate();
        // params.setUpdateTime(nowTime);
        if (incomeManualEntryMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateStateByCreateTime(FastIncomeManualEntryPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (incomeManualEntryMapper.updateStateByCreateTime(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastIncomeManualEntryPO params) {
        if (incomeManualEntryMapper.deleteById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 每个月 16 号产生上月结算单
     */
    public void incomeManualEntryJob2Settle() {
        Date nowDate = DateUtil.getNowDate();
        FastIncomeManualEntryPO query = new FastIncomeManualEntryPO();
        query.setState(1);
        query.setEffectMonth(DateUtil.format05Int(DateUtil.getLastMonthStartDay()));
        query.setJoinCpSettle(0);
        List<FastIncomeManualEntryPO> list = incomeManualEntryMapper.queryList(query);
        for (FastIncomeManualEntryPO cur : list) {
            cur.setUpdateTime(nowDate);
            cur.setJoinCpSettle(1);
            incomeManualEntryMapper.updateJoinCpSettleById(cur);
        }
    }

    public ResultVO getMonthList(SessionVO sessionVO, FastIncomeManualEntryPO params, PageVO pageVO) {
        if (Objects.nonNull(pageVO)) {
            startPage(pageVO);
        }
        List<FastIncomeManualEntryPO> monthList = monthList(params);
        FastAuditFlowPO fastAuditFlowPO = new FastAuditFlowPO();
        fastAuditFlowPO.setUserId(sessionVO.getUserId());
        fastAuditFlowPO.setAuditType(AuditTypeEnum.MONTH_INCOME.getType());
        fastAuditFlowPO.setDelFlag(0);
        int i = fastAuditFlowMapper.queryCount(fastAuditFlowPO);

        Map<String, Object> pageListData = getPageListData(monthList, pageVO);
        pageListData.put("auditUserFlag", i > 0);

        FastIncomeManualEntryPO summary = incomeManualEntryMapper.querySumIncomeMonth(params);
        pageListData.put("summary", summary);
        return ResultVO.success(pageListData);
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO batchAuditUpdateStateByDate(FastIncomeManualEntryPO update) {
        List<FastIncomeManualEntryPO> monthList = incomeManualEntryMapper.queryMonthList(update);
        if (CollUtil.isEmpty(monthList)) {
            return MethodVO.error("没有需要审批的记录!");
        }
        for (FastIncomeManualEntryPO fastIncomeManualEntryPO : monthList) {
            FastAuditProcessPO fastAuditProcessPO = new FastAuditProcessPO();
            fastAuditProcessPO.setCreatorId(update.getCreatorId());
            fastAuditProcessPO.setId(fastIncomeManualEntryPO.getAuditProcessId());
            MethodVO audit = fastAuditProcessService.audit(fastAuditProcessPO);
            if (audit.getCode() != 0) {
                transactionRollBack();
                return audit;
            }
        }
        return MethodVO.success();
    }

    public List<FastIncomeManualEntryPO> monthList(FastIncomeManualEntryPO params) {
        List<FastIncomeManualEntryPO> monthList = incomeManualEntryMapper.queryMonthList(params);
        for (FastIncomeManualEntryPO fastIncomeManualEntryPO : monthList) {

            if (Objects.isNull(fastIncomeManualEntryPO.getAuditState()) || fastIncomeManualEntryPO.getAuditState() == 2) {
                // 审核通过
                if (fastIncomeManualEntryPO.getStates().contains("1") && !fastIncomeManualEntryPO.getStates().contains("0")) {
                    // 全部为已入账
                    fastIncomeManualEntryPO.setState(1);
                } else {
                    // 待入账
                    fastIncomeManualEntryPO.setState(0);
                }
            } else {
                if (fastIncomeManualEntryPO.getAuditState() == 1) {
                    // 待审核
                    fastIncomeManualEntryPO.setState(2);
                } else {
                    // 审核不通过
                    fastIncomeManualEntryPO.setState(3);
                }
            }

        }
        return monthList;
    }

    public void updateGroupId() {
        FastIncomeManualEntryPO fastIncomeManualEntryPO = new FastIncomeManualEntryPO();
        fastIncomeManualEntryPO.setTimeType(3);
        List<FastIncomeManualEntryPO> fastIncomeManualEntryPOS = incomeManualEntryMapper.queryList(fastIncomeManualEntryPO);
        for (FastIncomeManualEntryPO po : fastIncomeManualEntryPOS) {
            FastIncomeManualEntryPO updatePo = new FastIncomeManualEntryPO();
            System.out.println(po.getIncomeTypeId() + po.getEffectMonth() + po.getEffectDate() + po.getPrincipalName() + po.getThirdAccountName() + po.getIncomeChannel());
            updatePo.setMonthGroupId(Md5Util.getMD5("" + po.getIncomeTypeId() + po.getEffectMonth() + po.getEffectDate() + po.getPrincipalName() + ObjectUtils.defaultIfNull(po.getThirdAccountName(), "") + po.getIncomeChannel()));
            updatePo.setId(po.getId());
            incomeManualEntryMapper.updateById(updatePo);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO monthBatchConfirm(SessionVO sessionVO, FastIncomeManualEntryPO monthParam) {
        monthParam.setState(0);
        List<FastIncomeManualEntryPO> fastIncomeManualEntryPOS = incomeManualEntryMapper.queryListJoinType(monthParam);

        List<FastIncomeManualEntryPO> updateList = fastIncomeManualEntryPOS.stream().filter(item -> {
            return Objects.isNull(item.getAuditState()) || item.getAuditState() == 2;
        }).collect(Collectors.toList());
        if (CollUtil.isEmpty(fastIncomeManualEntryPOS)) {
            return MethodVO.error("没有可入账的数据!");
        }

        for (FastIncomeManualEntryPO fastIncomeManualEntryPO : updateList) {
            if (Objects.nonNull(fastIncomeManualEntryPO.getAuditState()) && fastIncomeManualEntryPO.getAuditState() != 2) {
                // 未审核通过
                continue;
            }
            FastIncomeManualEntryPO updatePo = new FastIncomeManualEntryPO();
            updatePo.setStatementRatio(monthParam.getStatementRatio());
            updatePo.setIncome(fastIncomeManualEntryPO.getSourceIncome().multiply(updatePo.getStatementRatio())
                    .setScale(2, RoundingMode.HALF_UP));
            updatePo.setState(1);
            updatePo.setId(fastIncomeManualEntryPO.getId());
            updatePo.setAuditUser(sessionVO.getUserName());
            updatePo.setAuditUserId(sessionVO.getUserId());
            updatePo.setAuditTime(DateUtil.getNowDate());
            updatePo.setUpdateTime(DateUtil.getNowDate());
            int i = incomeManualEntryMapper.updateById(updatePo);
            if (i == 0) {
                transactionRollBack();
                return MethodVO.error("入账失败!");
            }
        }
        return MethodVO.success();

    }

    public ResultVO<?> exportMonthList(SessionVO sessionVO, FastIncomeManualEntryPO params) {
        String key = StaticVar.EXPORT_RATE + sessionVO.getUserId();
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            return ResultVO.error(StaticStr.CURRENTLY_HAVE_TASKS_EXPORTING_3_10);
        } else {
            RedisUtil.set(key, "1", RedisUtil.TIME_10M);
        }
        List<FastIncomeManualEntryPO> list = monthList(params);
        if (CollUtil.isEmpty(list)) {
            RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastIncomeManualEntryPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getTitle());
            CollUtil.addNoRepeat(rowHeadNames, "收入项目");

            row.add(cur.getThirdPlatformName());
            CollUtil.addNoRepeat(rowHeadNames, "分发平台");

            row.add(cur.getPrincipalName());
            CollUtil.addNoRepeat(rowHeadNames, "分发平台主体");

            row.add(cur.getThirdAccountName());
            CollUtil.addNoRepeat(rowHeadNames, "分发平台账号");

            row.add(cur.getEffectDateStr());
            CollUtil.addNoRepeat(rowHeadNames, "纳入收入日期（入账期）");

            row.add(cur.getEffectMonthStr());
            CollUtil.addNoRepeat(rowHeadNames, "纳入CP结算期");


            row.add(cur.getSourceIncome());
            CollUtil.addNoRepeat(rowHeadNames, "收入总金额");

            row.add(cur.getState() == 0 ? "待入账" : (cur.getState() == 1 ? "已入账" : cur.getState() == 2 ? "审核中" : "已驳回"));
            CollUtil.addNoRepeat(rowHeadNames, "状态");

            dataList.add(row);
        }
        log.info(toJSONString(dataList));
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "收入管理按月入账列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }
}
