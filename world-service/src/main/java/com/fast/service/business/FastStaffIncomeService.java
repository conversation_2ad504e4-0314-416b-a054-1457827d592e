/*
 * Powered By fast.up
 */
package com.fast.service.business;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.business.FastStaffIncomeDetailMapper;
import com.fast.mapper.business.FastStaffIncomeMapper;
import com.fast.mapper.business.FastStaffIncomeTypeMapper;
import com.fast.mapper.business.FastStaffMapper;
import com.fast.po.business.*;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastStaffIncomeService extends BaseService {

    @Autowired
    private FastStaffIncomeMapper fastStaffIncomeMapper;
    @Autowired
    private FastStaffIncomeDetailMapper fastStaffIncomeDetailMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastStaffIncomeAuditService fastStaffIncomeAuditService;
    @Autowired
    private FastStaffMapper fastStaffMapper;
    @Autowired
    private FastStaffIncomeTypeMapper fastStaffIncomeTypeMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStaffIncomePO queryById(FastStaffIncomePO params) {
        return fastStaffIncomeMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStaffIncomePO queryById(Integer id) {
        return fastStaffIncomeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStaffIncomePO queryOne(FastStaffIncomePO params) {
        return fastStaffIncomeMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffIncomePO> queryList(FastStaffIncomePO params) {
        return fastStaffIncomeMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStaffIncomePO params, PageVO pageVO) {
        if (!StrUtil.isBlank(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format10(params.getMonthStr())));
        }
        if (StrUtil.isBlank(params.getRealName())) {
            params.setRealName(null);
        }
        if (StrUtil.isBlank(params.getRealNames())) {
            params.setRealNames(null);
        }
        if (StrUtil.isBlank(params.getIncomeTypes())) {
            params.setIncomeTypes(null);
        }
        if (StrUtil.isBlank(params.getCreatorIds())) {
            params.setCreatorIds(null);
        }
        FastStaffIncomeAuditPO fastStaffIncomeAudit = fastStaffIncomeAuditService.queryById(1);
        String auditUser = fastStaffIncomeAudit != null ? fastStaffIncomeAudit.getAuditUser() : "";
        startPage(pageVO);
        List<FastStaffIncomePO> list = fastStaffIncomeMapper.queryList(params);
        for (FastStaffIncomePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setMonthStr(DateUtil.format10(DateUtil.format05(cur.getMonth().toString())));
            if (cur.getState() == 0) {
                cur.setAuditUser(auditUser);
            }
        }

        ResultVO<?> data = ResultVO.success(getPageListData(list, pageVO));
        FastStaffIncomePO total = fastStaffIncomeMapper.queryListTotal(params);
        data.setSummary(total);
        return data;
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> exportQueryList(SessionVO sessionVO, FastStaffIncomePO params) {
        if (!StrUtil.isBlank(params.getMonthStr())) {
            params.setMonth(DateUtil.format05Int(DateUtil.format10(params.getMonthStr())));
        } else {
            params.setMonth(DateUtil.format05Int(DateUtil.addMonths(new Date(), -1)));
        }
        if (StrUtil.isBlank(params.getRealName())) {
            params.setRealName(null);
        }
        if (StrUtil.isBlank(params.getRealNames())) {
            params.setRealNames(null);
        }
        if (StrUtil.isBlank(params.getIncomeTypes())) {
            params.setIncomeTypes(null);
        }
        if (StrUtil.isBlank(params.getCreatorIds())) {
            params.setCreatorIds(null);
        }
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_RECHARGE_ORDER_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        if (params.getMonthStr() != null) {
            params.setMonth(DateUtil.format05Int(DateUtil.format10(params.getMonthStr())));
        }

        List<FastStaffIncomePO> list = fastStaffIncomeMapper.queryList(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastStaffIncomePO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getRealName());
            CollUtil.addNoRepeat(rowHeadNames, "支出成员");

            row.add(cur.getTitle());
            CollUtil.addNoRepeat(rowHeadNames, "支出项目");

            row.add(cur.getIncome());
            CollUtil.addNoRepeat(rowHeadNames, "支出总金额");

            row.add(DateUtil.format10(DateUtil.format05(cur.getMonth().toString())));
            CollUtil.addNoRepeat(rowHeadNames, "纳入支出周期");

            row.add(cur.getState() == 0 ? "待审核" : (cur.getState() == 2 ? "不通过" : "已录入"));
            CollUtil.addNoRepeat(rowHeadNames, "状态");

            row.add(cur.getCreatorName());
            CollUtil.addNoRepeat(rowHeadNames, "录入人");

            row.add(DateUtil.format07(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "录入时间");

            row.add(cur.getRemark());
            CollUtil.addNoRepeat(rowHeadNames, "备注");

            dataList.add(row);
        }
        log.info(toJSONString(dataList));
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "人工支出列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStaffIncomePO params) {
        return fastStaffIncomeMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStaffIncomePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStaffIncomeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertDetail(FastStaffIncomePO params) {
        Date nowTime = DateUtil.getNowDate();
        StringBuffer title = new StringBuffer();
        BigDecimal income = BigDecimal.ZERO;
        params.setCreateTime(nowTime);
        if (params.getDetail().size() > 0) {
            for (FastStaffIncomeDetailPO detail : params.getDetail()) {
                detail.setMonth(params.getMonth());
                detail.setUserId(params.getUserId());
                detail.setState(params.getState());
                detail.setCreateTime(nowTime);
                if (!StrUtil.isEmpty(title)) {
                    title.append("、");
                }
                title.append(detail.getTitle());
                income = income.add(detail.getIncome());
            }
        }
        if (income.longValue() <= 0L) {
            return MethodVO.error("支出项金额不能为空");
        }
        params.setTitle(title.toString());
        params.setIncome(income);
        if (params.getIsAudit() == 0) { // 如果不需要审核，则设置为审核状态state=1
            params.setState(1);
        }
        if (fastStaffIncomeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        if (params.getDetail().size() > 0) {
            for (FastStaffIncomeDetailPO detail : params.getDetail()) {
                detail.setStaffIncomeId(params.getId());
                if (fastStaffIncomeDetailMapper.insertSelective(detail) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            }
        }
        return MethodVO.success();
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatchDetail(List<FastStaffIncomePO> datas) {
        Date nowTime = DateUtil.getNowDate();
        for (FastStaffIncomePO params : datas) {
            StringBuffer title = new StringBuffer();
            BigDecimal income = BigDecimal.ZERO;
            params.setCreateTime(nowTime);
            if (params.getDetail().size() > 0) {
                for (FastStaffIncomeDetailPO detail : params.getDetail()) {
                    detail.setMonth(params.getMonth());
                    detail.setUserId(params.getUserId());
                    detail.setState(params.getState());
                    detail.setCreateTime(nowTime);
                    if (!StrUtil.isEmpty(title)) {
                        title.append("、");
                    }
                    title.append(detail.getTitle());
                    income = income.add(detail.getIncome());
                }
            }
            if (income.longValue() <= 0L) {
                continue;
            }
            params.setTitle(title.toString());
            params.setIncome(income);
            if (params.getIsAudit() == 0) { // 如果不需要审核，则设置为审核状态state=1
                params.setState(1);
            }
            if (fastStaffIncomeMapper.insertSelective(params) == 0) {
                transactionRollBack();
                return false;
            }
            if (params.getDetail().size() > 0) {
                for (FastStaffIncomeDetailPO detail : params.getDetail()) {
                    detail.setStaffIncomeId(params.getId());
                    if (fastStaffIncomeDetailMapper.insertSelective(detail) == 0) {
                        transactionRollBack();
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStaffIncomePO> list) {
        if (fastStaffIncomeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStaffIncomePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStaffIncomeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDetail(FastStaffIncomePO params) {
        Date nowTime = DateUtil.getNowDate();
        StringBuffer title = new StringBuffer();
        BigDecimal income = BigDecimal.ZERO;
        params.setUpdateTime(nowTime);
        if (params.getDetail() != null && params.getDetail().size() > 0) {
            for (FastStaffIncomeDetailPO detail : params.getDetail()) {
                if (params.getMonth() != null) {
                    detail.setMonth(params.getMonth());
                }
                if (params.getUserId() != null) {
                    detail.setUserId(params.getUserId());
                }
                if (params.getState() != null) {
                    detail.setState(params.getState());
                }
                detail.setUpdateTime(nowTime);
                if (!StrUtil.isEmpty(title)) {
                    title.append("、");
                }
                title.append(detail.getTitle());
                income = income.add(detail.getIncome());
            }
        }
        if (income.longValue() > 0L) {
            params.setIncome(income);
        }
        if (!StrUtil.isEmpty(title)) {
            params.setTitle(title.toString());
        }
        if (params.getIsAudit() != null && params.getIsAudit() == 0) {
            // 如果不需要审核，则设置为审核状态state=1
            params.setState(1);
        } else {
            params.setState(0);
        }
        if (fastStaffIncomeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        if (params.getDetail() != null && params.getDetail().size() > 0) {
            for (FastStaffIncomeDetailPO detail : params.getDetail()) {
                if (fastStaffIncomeDetailMapper.updateById(detail) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            }
        }
        return MethodVO.success();
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateAuditState(FastStaffIncomePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setAuditTime(nowTime);
        params.setUpdateTime(nowTime);

        if (fastStaffIncomeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        FastStaffIncomeDetailPO detailPO = new FastStaffIncomeDetailPO();
        detailPO.setStaffIncomeId(params.getId());
        detailPO.setState(params.getState());
        if (fastStaffIncomeDetailMapper.updateByStaffIncomeId(detailPO) == 0) {
            transactionRollBack();
            return MethodVO.error("更新失败");
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastStaffIncomePO params) {
        if (fastStaffIncomeMapper.deleteById(params) == 0) {
            transactionRollBack();
            return MethodVO.error("删除失败");
        }
        FastStaffIncomeDetailPO detailPO = new FastStaffIncomeDetailPO();
        detailPO.setStaffIncomeId(params.getId());
        if (fastStaffIncomeDetailMapper.deleteById(detailPO) == 0) {
            transactionRollBack();
            return MethodVO.error("删除失败");
        }
        return MethodVO.success();
    }

    /**
     * 根据导入数据同步用户支出
     */
//    @Async
    public void syncStaffIncome(SessionVO sessionVO, Integer month, Date nowDate, Integer type) {
        FastStaffIncomeTypePO typePo = new FastStaffIncomeTypePO();
        typePo.setSort("asc");
        List<FastStaffIncomeTypePO> staffIncomeType = fastStaffIncomeTypeMapper.queryList(typePo);
        if (staffIncomeType == null || staffIncomeType.isEmpty() || month == null) {
            return;
        }

        FastStaffIncomeAuditPO fastStaffIncomeAudit = fastStaffIncomeAuditService.queryById(1);
        if (fastStaffIncomeAudit == null) {
            fastStaffIncomeAudit.setIsAudit(1);
        }
        FastStaffPO fastStaffPO = new FastStaffPO();
        fastStaffPO.setMonth(month);
        if (type == 1) {
            fastStaffPO.setUpdateTime(nowDate);
        }
        List<FastStaffPO> staff = fastStaffMapper.queryStaffSalaryList(fastStaffPO);
        for (FastStaffPO cur : staff) {
            FastStaffIncomePO staffIncome = new FastStaffIncomePO();

            staffIncome.setMonth(month);
            staffIncome.setRealName(cur.getRealName());
            FastStaffIncomePO staffIncome1 = queryOne(staffIncome);

            staffIncome.setUserId(cur.getUserId());
            staffIncome.setState(fastStaffIncomeAudit.getIsAudit() == 1 ? 0 : 1);
            staffIncome.setIsAudit(fastStaffIncomeAudit.getIsAudit());
            staffIncome.setAuditUser("");
            if (staffIncome1 != null) {
                staffIncome.setCreatorId(staffIncome1.getCreatorId());
                staffIncome.setCreatorName(staffIncome1.getCreatorName());
                staffIncome.setUpdatorId(sessionVO.getUserId());
            } else {
                staffIncome.setCreatorId(sessionVO.getUserId());
                staffIncome.setCreatorName(sessionVO.getUserName());
            }

            List<FastStaffIncomeDetailPO> staffIncomeDetailList = new ArrayList<>();
            for (FastStaffIncomeTypePO staffIncomeTypePO : staffIncomeType) {
                FastStaffIncomeDetailPO staffIncomeDetail = new FastStaffIncomeDetailPO();
                log.info(staffIncomeTypePO.getFieldName());
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "salary")) {
                    staffIncomeDetail.setIncome(cur.getSalary());
                }
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "salary_other")) {
                    staffIncomeDetail.setIncome(cur.getSalaryOther());
                }
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "manage_cost")) {
                    staffIncomeDetail.setIncome(cur.getManageCost());
                }
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "operation_cost")) {
                    staffIncomeDetail.setIncome(cur.getOperationCost());
                }
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "technical_cost")) {
                    staffIncomeDetail.setIncome(cur.getTechnicalCost());
                }
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "material_cost")) {
                    staffIncomeDetail.setIncome(cur.getMaterialCost());
                }
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "external_technical_cost")) {
                    staffIncomeDetail.setIncome(cur.getExternalTechnicalCost());
                }
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "external_material_cost")) {
                    staffIncomeDetail.setIncome(cur.getExternalMaterialCost());
                }
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "reimbursement_cost")) {
                    staffIncomeDetail.setIncome(cur.getReimbursementCost());
                }
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "company_manage_cost")) {
                    staffIncomeDetail.setIncome(cur.getCompanyManageCost());
                }
                if (Objects.equals(staffIncomeTypePO.getFieldName(), "company_administrative_cost")) {
                    staffIncomeDetail.setIncome(cur.getCompanyAdministrativeCost());
                }
                if (staffIncomeDetail.getIncome() == null) {
                    continue;
                }
                staffIncomeDetail.setTitle(staffIncomeTypePO.getTitle());
                staffIncomeDetail.setIncomeType(staffIncomeTypePO.getId());
                log.info(toJSONString(staffIncomeDetail));
                staffIncomeDetailList.add(staffIncomeDetail);
            }
            staffIncome.setDetail(staffIncomeDetailList);

            MethodVO methodVO = insertDetail(staffIncome);
            log.info(toJSONString(methodVO));
            if (staffIncome1 != null) {
                delete(staffIncome1);
            }
        }
    }
}
