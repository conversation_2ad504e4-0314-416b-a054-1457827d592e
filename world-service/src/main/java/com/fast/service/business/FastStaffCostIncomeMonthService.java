/*
 * Powered By fast.up
 */
package com.fast.service.business;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.business.FastIncomeManualEntryMapper;
import com.fast.mapper.business.FastStaffCostIncomeMonthMapper;
import com.fast.po.business.FastIncomeManualEntryPO;
import com.fast.po.business.FastStaffCostIncomeMonthPO;
import com.fast.po.setting.FastSettingMediaProfitPO;
import com.fast.po.setting.FastSettingMediaSettlementPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.setting.FastSettingMediaProfitService;
import com.fast.service.setting.FastSettingMediaSettlementService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.github.pagehelper.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastStaffCostIncomeMonthService extends BaseService {

    @Autowired
    private FastStaffCostIncomeMonthMapper fastStaffCostIncomeMonthMapper;
    @Autowired
    private FastSettingMediaSettlementService fastSettingMediaSettlementService;
    @Autowired
    private FastSettingMediaProfitService fastSettingMediaProfitService;
    @Autowired
    private FastIncomeManualEntryMapper fastIncomeManualEntryMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 通过id查询单个对象
     */
    public FastStaffCostIncomeMonthPO queryById(FastStaffCostIncomeMonthPO params) {
        return fastStaffCostIncomeMonthMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStaffCostIncomeMonthPO queryById(Integer id) {
        return fastStaffCostIncomeMonthMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStaffCostIncomeMonthPO queryOne(FastStaffCostIncomeMonthPO params) {
        return fastStaffCostIncomeMonthMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffCostIncomeMonthPO> queryList(FastStaffCostIncomeMonthPO params) {
        return fastStaffCostIncomeMonthMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStaffCostIncomeMonthPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStaffCostIncomeMonthPO> list = fastStaffCostIncomeMonthMapper.queryList(params);
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStaffCostIncomeMonthPO params) {
        return fastStaffCostIncomeMonthMapper.queryCount(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffCostIncomeMonthPO> mediaRechargeData(FastStaffCostIncomeMonthPO params) {
        return fastStaffCostIncomeMonthMapper.mediaRechargeData(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffCostIncomeMonthPO> mediaRechargeOtherData(FastStaffCostIncomeMonthPO params) {
        return fastStaffCostIncomeMonthMapper.mediaRechargeOtherData(params);
    }

    /**
     * 获取组用户列表
     */
    public List<FastStaffCostIncomeMonthPO> launchGroupUserList(FastStaffCostIncomeMonthPO params) {
        return fastStaffCostIncomeMonthMapper.launchGroupUserList(params);
    }

    /**
     * 查询全部
     */
    public Map<Integer, FastStaffCostIncomeMonthPO> launchGroupWeekDataList(FastStaffCostIncomeMonthPO params) {
        List<FastStaffCostIncomeMonthPO> weekList = fastStaffCostIncomeMonthMapper.launchGroupWeekDataList(params);
        Map<Integer, FastStaffCostIncomeMonthPO> weekMap = new HashMap<>();
        for (FastStaffCostIncomeMonthPO val : weekList) {
            if (val.getUserGroupId() == 0 || val.getUserGroupId() == null) {
                continue;
            }
            weekMap.put(val.getUserGroupId(), val);
        }
        return weekMap;
    }

    /**
     * 查询全部
     */
    public List<FastStaffCostIncomeMonthPO> launchGroupDataList(FastStaffCostIncomeMonthPO params) {
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthMapper.launchGroupDataList(params);

        int nowDay = DateUtil.getCurrentDay();
        int endDay = DateUtil.getCurrentDay(DateUtil.getLastMonthEndDay());

        List<FastStaffCostIncomeMonthPO> data = new ArrayList<>();
        for (FastStaffCostIncomeMonthPO val : dataList) {
            if (val.getUserGroupId() == null) {
                continue;
            }
            if (val.getUserGroupId() == 0) {
                val.setUserGroupName("其他分组");
            }
            BigDecimal estimateStaffIncome = DoubleUtil.divB4Zero(val.getStaffIncomePreMonth(), BigDecimal.valueOf(endDay)).multiply(BigDecimal.valueOf(nowDay));
            val.setEstimateStaffIncome(estimateStaffIncome);
            BigDecimal statementMoney = val.getStatementMoney() == null ? BigDecimal.ZERO : val.getStatementMoney(); // 结算金额
            BigDecimal totalProfit = statementMoney.subtract(val.getStaffIncome() == null ? BigDecimal.ZERO : val.getStaffIncome()); // 总利润
            val.setEstimateCompanyProfit(DoubleUtil.mulB(totalProfit.subtract(estimateStaffIncome), BigDecimal.valueOf(0.49)));
            val.setEstimateStaffProfit(DoubleUtil.mulB(totalProfit.subtract(estimateStaffIncome), BigDecimal.valueOf(0.51)));
            if (val.getStaffIncome().compareTo(BigDecimal.ZERO) > 0) {
                val.setTotalProfit(DoubleUtil.subB(totalProfit, val.getStaffIncome()));
                val.setCompanyProfit(DoubleUtil.mulB(totalProfit.subtract(val.getStaffIncome()), BigDecimal.valueOf(0.49)));
                val.setStaffProfit(DoubleUtil.mulB(totalProfit.subtract(val.getStaffIncome()), BigDecimal.valueOf(0.51)));
            } else {
                val.setTotalProfit(BigDecimal.ZERO);
                val.setCompanyProfit(BigDecimal.ZERO);
                val.setStaffProfit(BigDecimal.ZERO);
            }
            data.add(val);
        }
        return data;
    }

    public ResultVO<?> exportGroupList(SessionVO sessionVO, List<FastStaffCostIncomeMonthPO> list) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_RECHARGE_ORDER_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }

        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastStaffCostIncomeMonthPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getUserGroupName());
            CollUtil.addNoRepeat(rowHeadNames, "投放组");

            row.add(cur.getWeekMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "周充值金额");

            row.add(cur.getWeekMoneyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "周实到金额");

            row.add(cur.getWeekCashCost());
            CollUtil.addNoRepeat(rowHeadNames, "周现金消耗");

            row.add(cur.getMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "月充值金额");

            row.add(cur.getMoneyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月实到金额");

            row.add(cur.getCashCost());
            CollUtil.addNoRepeat(rowHeadNames, "月现金消耗");

            row.add(cur.getEstimateStaffIncome());
            CollUtil.addNoRepeat(rowHeadNames, "月预估成本");

            row.add(cur.getEstimateStaffProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月组利润预估");

            row.add(cur.getEstimateCompanyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月公司利润预估");

            row.add(cur.getStaffProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月组实际利润");

            row.add(cur.getCompanyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月公司实际利润");

            row.add(cur.getUserGroupNums());
            CollUtil.addNoRepeat(rowHeadNames, "组成员人数");

            dataList.add(row);
        }
        log.info(toJSONString(dataList));
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "投放经营数据-自代投";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 查询全部
     */
    public Map<Integer, FastStaffCostIncomeMonthPO> launchGroupMediaWeekDataList(FastStaffCostIncomeMonthPO params) {
        List<FastStaffCostIncomeMonthPO> weekList = fastStaffCostIncomeMonthMapper.launchGroupMediaWeekDataList(params);
        Map<Integer, FastStaffCostIncomeMonthPO> weekMap = new HashMap<>();
        for (FastStaffCostIncomeMonthPO val : weekList) {
            weekMap.put(val.getMediaType(), val);
        }
        return weekMap;
    }

    /**
     * 查询全部
     */
    public List<FastStaffCostIncomeMonthPO> launchGroupMediaDataList(FastStaffCostIncomeMonthPO params) {
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthMapper.launchGroupMediaDataList(params);

        int nowDay = DateUtil.getCurrentDay();
        int endDay = DateUtil.getCurrentDay(DateUtil.getLastMonthEndDay());

        List<FastStaffCostIncomeMonthPO> data = new ArrayList<>();
        BigDecimal totalEstimateCompanyProfit = BigDecimal.ZERO;
        BigDecimal totalEstimateStaffProfit = BigDecimal.ZERO;
        BigDecimal totalTotalProfit = BigDecimal.ZERO;
        for (FastStaffCostIncomeMonthPO val : dataList) {
            BigDecimal estimateStaffIncome = DoubleUtil.divB4Zero(val.getStaffIncomePreMonth(), BigDecimal.valueOf(endDay)).multiply(BigDecimal.valueOf(nowDay));
            val.setEstimateStaffIncome(estimateStaffIncome);
            BigDecimal statementMoney = defaultIfNull(val.getStatementMoney(), BigDecimal.ZERO); // 结算金额
            BigDecimal totalProfit = DoubleUtil.subB(statementMoney, val.getStaffIncome()); // 总利润
            val.setEstimateCompanyProfit(DoubleUtil.mulB(DoubleUtil.subB(totalProfit, estimateStaffIncome), BigDecimal.valueOf(0.49)));
            val.setEstimateStaffProfit(DoubleUtil.mulB(DoubleUtil.subB(totalProfit, estimateStaffIncome), BigDecimal.valueOf(0.51)));
            if (val.getStaffIncome().compareTo(BigDecimal.ZERO) > 0) {
                val.setTotalProfit(totalProfit);
            } else {
                val.setTotalProfit(BigDecimal.ZERO);
                val.setCompanyProfit(BigDecimal.ZERO);
                val.setStaffProfit(BigDecimal.ZERO);
            }
            if (val.getEstimateCompanyProfit() == null) {
                val.setEstimateCompanyProfit(BigDecimal.ZERO);
            }
            if (val.getEstimateStaffProfit() == null) {
                val.setEstimateStaffProfit(BigDecimal.ZERO);
            }
            totalEstimateCompanyProfit = totalEstimateCompanyProfit.add(val.getEstimateCompanyProfit());
            totalEstimateStaffProfit = totalEstimateStaffProfit.add(val.getEstimateStaffProfit());
            totalTotalProfit = totalTotalProfit.add(val.getTotalProfit());
            data.add(val);
        }

        if (params.getUserGroupId() == null || params.getUserGroupId() == 0) {
            FastStaffCostIncomeMonthPO dataTotal = fastStaffCostIncomeMonthMapper.launchGroupMediaDataTotal(params);
            dataTotal.setEstimateCompanyProfit(totalEstimateCompanyProfit);
            dataTotal.setEstimateStaffProfit(totalEstimateStaffProfit);
            dataTotal.setTotalProfit(totalTotalProfit);
            if (dataTotal.getStaffIncome() != null && dataTotal.getStaffIncome().compareTo(BigDecimal.ZERO) <= 0) {
                dataTotal.setCompanyProfit(BigDecimal.ZERO);
                dataTotal.setStaffProfit(BigDecimal.ZERO);
            }
            data.add(0, dataTotal);
        }
        return data;
    }

    public ResultVO<?> exportGroupMediaList(SessionVO sessionVO, List<FastStaffCostIncomeMonthPO> list) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_RECHARGE_ORDER_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }

        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastStaffCostIncomeMonthPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)
            String MediaName = "";
            if (cur.getMediaType() == 1) {
                MediaName = "巨量";
            } else if (cur.getMediaType() == 2) {
                MediaName = "ADQ";
            } else if (cur.getMediaType() == 4) {
                MediaName = "百度";
            } else if (cur.getMediaType() == 5) {
                MediaName = "快手 常规";
            } else if (cur.getMediaType() == 501) {
                MediaName = "快手native";
            } else if (cur.getMediaType() == -1) {
                MediaName = "累计";
            } else {
                continue;
            }
            row.add(MediaName);
            CollUtil.addNoRepeat(rowHeadNames, "媒体");

            row.add(cur.getWeekMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "周充值金额");

            row.add(cur.getWeekMoneyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "周实到金额");

            row.add(cur.getWeekCashCost());
            CollUtil.addNoRepeat(rowHeadNames, "周现金消耗");

            row.add(cur.getMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "月充值金额");

            row.add(cur.getMoneyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月实到金额");

            row.add(cur.getCashCost());
            CollUtil.addNoRepeat(rowHeadNames, "月现金消耗");

            row.add(cur.getEstimateStaffProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月组利润预估");

            row.add(cur.getEstimateCompanyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月公司利润预估");

            row.add(cur.getStaffProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月组实际利润");

            row.add(cur.getCompanyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月公司实际利润");

            dataList.add(row);
        }
        log.info(toJSONString(dataList));
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "投放经营数据-自代投媒体数据";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 查询全部
     */
    public List<FastStaffCostIncomeMonthPO> launchGroupMediaMonthDataList(FastStaffCostIncomeMonthPO params) {
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthMapper.launchGroupMediaMonthDataList(params);

        List<FastStaffCostIncomeMonthPO> data = new ArrayList<>();
        FastStaffCostIncomeMonthPO dataTotal = fastStaffCostIncomeMonthMapper.launchGroupMediaMonthDataTotal(params);
        if (dataTotal.getStaffIncome() == null || dataTotal.getStaffIncome().compareTo(BigDecimal.ZERO) <= 0) {
            dataTotal.setTotalProfit(BigDecimal.ZERO);
            dataTotal.setCompanyProfit(BigDecimal.ZERO);
            dataTotal.setStaffProfit(BigDecimal.ZERO);
        }
        data.add(dataTotal);
        for (FastStaffCostIncomeMonthPO val : dataList) {
            if (val.getStaffIncome().compareTo(BigDecimal.ZERO) <= 0) {
                val.setTotalProfit(BigDecimal.ZERO);
                val.setCompanyProfit(BigDecimal.ZERO);
                val.setStaffProfit(BigDecimal.ZERO);
            }
            data.add(val);
        }
        return data;
    }


    public ResultVO<?> exportGroupMediaMonthData(SessionVO sessionVO, List<FastStaffCostIncomeMonthPO> list, String title) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_RECHARGE_ORDER_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }

        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastStaffCostIncomeMonthPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)
            row.add(cur.getMonthStr());
            CollUtil.addNoRepeat(rowHeadNames, "月份");

            row.add(cur.getMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "月充值金额");

            row.add(cur.getMoneyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月实到金额");

            row.add(cur.getCashCost());
            CollUtil.addNoRepeat(rowHeadNames, "月现金消耗");

            row.add(cur.getStaffProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月组实际利润");

            row.add(cur.getCompanyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月公司实际利润");

            row.add(cur.getUserName());
            CollUtil.addNoRepeat(rowHeadNames, "充值最高贡献成员");

            dataList.add(row);
        }
        log.info(toJSONString(dataList));
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        if (StringUtil.isEmpty(title)) {
            title = "自代投媒体数据-月数据";
        }
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 查询组-用户-月数据
     */
    public List<FastStaffCostIncomeMonthPO> launchGroupStaffMonthDataList(FastStaffCostIncomeMonthPO params) {
        List<FastStaffCostIncomeMonthPO> dataList = fastStaffCostIncomeMonthMapper.launchGroupStaffMonthDataList(params);

        List<FastStaffCostIncomeMonthPO> data = new ArrayList<>();
        FastStaffCostIncomeMonthPO dataTotal = fastStaffCostIncomeMonthMapper.launchGroupStaffMonthDataTotal(params);
        data.add(dataTotal);
        for (FastStaffCostIncomeMonthPO val : dataList) {
            if (val.getStaffIncome().compareTo(BigDecimal.ZERO) <= 0) {
                val.setTotalProfit(BigDecimal.ZERO);
                val.setCompanyProfit(BigDecimal.ZERO);
                val.setStaffProfit(BigDecimal.ZERO);
            }
            data.add(val);
        }
        return data;
    }


    public ResultVO<?> exportGroupStaffMonthData(SessionVO sessionVO, List<FastStaffCostIncomeMonthPO> list) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_RECHARGE_ORDER_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }

        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastStaffCostIncomeMonthPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)
            row.add(cur.getUserName());
            CollUtil.addNoRepeat(rowHeadNames, "成员姓名");

            row.add(cur.getUserGroupName());
            CollUtil.addNoRepeat(rowHeadNames, "所属投放组");

            row.add(cur.getMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "月总充值");
            row.add(cur.getVirtualRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "月虚拟支付");
            row.add(cur.getNormalRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "月普通支付");

            row.add(cur.getMoneyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月实到充值");
            row.add(cur.getVirtualProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月实到虚拟支付");
            row.add(cur.getNormalProfit());
            CollUtil.addNoRepeat(rowHeadNames, "月实到普通支付");

            row.add(cur.getCashCost());
            CollUtil.addNoRepeat(rowHeadNames, "月现金消耗");
            row.add(cur.getCashCostBack());
            CollUtil.addNoRepeat(rowHeadNames, "月现金返点后消耗");
            row.add(cur.getTotalProfit());
            CollUtil.addNoRepeat(rowHeadNames, "结算金额");
            row.add(cur.getStaffIncome());
            CollUtil.addNoRepeat(rowHeadNames, "本月已录入成本");

            row.add(cur.getStaffProfit());
            CollUtil.addNoRepeat(rowHeadNames, "本月个人净利润（*51%后）");
            row.add(cur.getCompanyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "本月公司净利润（*49%后）");

            dataList.add(row);
        }
        log.info(toJSONString(dataList));
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "投放经营数据-投放组-成员数据";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }


    /** 分销数据-开始 **/

    /**
     * 查询组-用户-月数据
     */
    public FastStaffCostIncomeMonthPO launchRetailMonthTotal(FastStaffCostIncomeMonthPO params) {
        return fastStaffCostIncomeMonthMapper.launchRetailMonthTotal(params);
    }

    /**
     * 查询组-用户-月数据
     */
    public List<FastStaffCostIncomeMonthPO> launchRetailMonthList(FastStaffCostIncomeMonthPO params) {
        return fastStaffCostIncomeMonthMapper.launchRetailMonthList(params);
    }

    /**
     * 查询全部
     */
    public Map<Integer, FastStaffCostIncomeMonthPO> launchRetailWeekList(FastStaffCostIncomeMonthPO params) {
        List<FastStaffCostIncomeMonthPO> weekList = fastStaffCostIncomeMonthMapper.launchRetailWeekList(params);
        Map<Integer, FastStaffCostIncomeMonthPO> weekMap = new HashMap<>();
        for (FastStaffCostIncomeMonthPO val : weekList) {
            weekMap.put(val.getRetailId(), val);
        }
        return weekMap;
    }

    public ResultVO<?> exportRetailMonthData(SessionVO sessionVO, List<FastStaffCostIncomeMonthPO> list, Integer type) {
        String key = StaticVar.EXPORT_RECHARGE_ORDER_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }

        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastStaffCostIncomeMonthPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)
            if (type == 3) {
                String MediaName;
                if (cur.getMediaType() == null) {
                    MediaName = "累计";
                } else if (cur.getMediaType() == 1) {
                    MediaName = "巨量";
                } else if (cur.getMediaType() == 2) {
                    MediaName = "ADQ";
                } else if (cur.getMediaType() == 4) {
                    MediaName = "百度";
                } else if (cur.getMediaType() == 5) {
                    MediaName = "快手";
                } else if (cur.getMediaType() == 0) {
                    MediaName = "自然量";
                } else {
                    continue;
                }
                row.add(MediaName);
                CollUtil.addNoRepeat(rowHeadNames, "媒体");
                row.add(cur.getMoneyRecharge());
                CollUtil.addNoRepeat(rowHeadNames, "月总充值");
                row.add(cur.getVirtualRecharge());
                CollUtil.addNoRepeat(rowHeadNames, "月虚拟充值");
                row.add(cur.getNormalRecharge());
                CollUtil.addNoRepeat(rowHeadNames, "月普通充值");
                row.add(cur.getMoneyProfit());
                CollUtil.addNoRepeat(rowHeadNames, "月实到总充值");
                row.add(cur.getVirtualProfit());
                CollUtil.addNoRepeat(rowHeadNames, "月虚拟实到充值");
                row.add(cur.getNormalProfit());
                CollUtil.addNoRepeat(rowHeadNames, "月普通实到充值");
                row.add(cur.getMoneyRefund());
                CollUtil.addNoRepeat(rowHeadNames, "总退款");
                row.add(cur.getVirtualRefund());
                CollUtil.addNoRepeat(rowHeadNames, "月虚拟退款");
                row.add(cur.getNormalRefund());
                CollUtil.addNoRepeat(rowHeadNames, "月普通退款");
                row.add(cur.getRetailProfit());
                CollUtil.addNoRepeat(rowHeadNames, "分成金额");
                row.add(cur.getCompanyProfit());
                CollUtil.addNoRepeat(rowHeadNames, "本月公司净利润");
            } else {
                if (type == 2) {
                    row.add(cur.getMonth());
                    CollUtil.addNoRepeat(rowHeadNames, "月份");
                }
                if (type == 1) {
                    row.add(cur.getWeekMoneyRecharge());
                    CollUtil.addNoRepeat(rowHeadNames, "周总充值");
                    row.add(cur.getWeekMoneyProfit());
                    CollUtil.addNoRepeat(rowHeadNames, "周总实到");
                    row.add(cur.getWeekMoneyProfitRatio());
                    CollUtil.addNoRepeat(rowHeadNames, "周充值环比");
                }
                row.add(cur.getMoneyRecharge());
                CollUtil.addNoRepeat(rowHeadNames, "月充值");
                row.add(cur.getMoneyProfit());
                CollUtil.addNoRepeat(rowHeadNames, "月实到充值");
                if (type == 1) {
                    row.add(cur.getMoneyProfitRatio());
                    CollUtil.addNoRepeat(rowHeadNames, "月充值环比");
                }
                row.add(cur.getMoneyRefund());
                CollUtil.addNoRepeat(rowHeadNames, "本月退款");
                row.add(cur.getRetailProfit());
                CollUtil.addNoRepeat(rowHeadNames, "本月分销商分成");
                if (type == 1) {
                    row.add(cur.getRetailProfitRatio());
                    CollUtil.addNoRepeat(rowHeadNames, "本月分销商分成环比");
                }

                row.add(cur.getProcessCost());
                CollUtil.addNoRepeat(rowHeadNames, "本月投放过程成本");
                row.add(cur.getStaffIncome());
                CollUtil.addNoRepeat(rowHeadNames, "本月公司净利润");
                if (type == 1) {
                    row.add(cur.getCompanyProfitRatio());
                    CollUtil.addNoRepeat(rowHeadNames, "本月公司净利润环比");
                }
            }
            dataList.add(row);
        }
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "投放经营数据-分销-数据";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /** --- 分销数据-结束 --- **/


    /**
     * 查询全部
     */
    public Map<String, Object> mediaRechargeOverview(FastStaffCostIncomeMonthPO params) {
        return fastStaffCostIncomeMonthMapper.mediaRechargeOverview(params);
    }

    public List<Map<String, Object>> mediaRechargeMonthList(FastStaffCostIncomeMonthPO params) {
        List<Map<String, Object>> monthList = fastStaffCostIncomeMonthMapper.mediaRechargeMonthList(params);
        FastIncomeManualEntryPO manualEntryPO = new FastIncomeManualEntryPO();
        manualEntryPO.setState(1);
        manualEntryPO.setIncomeTypeIds("1,2,3");
        manualEntryPO.setEffectDateStart(DateUtil.format06(DateUtil.beginOfYear(params.getYear())));
        manualEntryPO.setEffectDateEnd(DateUtil.format06(DateUtil.endOfYear(params.getYear())));
        Map<Integer, FastIncomeManualEntryPO> manualEntry = fastIncomeManualEntryMapper.queryIncomeMonth(manualEntryPO);
        for (Map cur : monthList) {
            if (manualEntry.containsKey(cur.getOrDefault("month", 0))) {
                FastIncomeManualEntryPO manualEntryCur = manualEntry.get(cur.get("month"));
                cur.put("douyinOriginProfit", manualEntryCur.getDouyinOriginProfit());
                cur.put("kuaishouOriginProfit", manualEntryCur.getKuaishouOriginProfit());
                cur.put("kuaishouJuxingProfit", manualEntryCur.getKuaishouJuxingProfit());
            } else {
                cur.put("douyinOriginProfit", 0);
                cur.put("kuaishouOriginProfit", 0);
                cur.put("kuaishouJuxingProfit", 0);
            }
        }
        return monthList;
    }

    /**
     * 查询全部
     */
    public Map<String, Object> mediaRechargeMonthTotal(FastStaffCostIncomeMonthPO params) {
        Map<String, Object> monthTotal = fastStaffCostIncomeMonthMapper.mediaRechargeMonthTotal(params);
        if (monthTotal == null) {
            return monthTotal;
        }
        FastIncomeManualEntryPO manualEntryPO = new FastIncomeManualEntryPO();
        manualEntryPO.setState(1);
        manualEntryPO.setIncomeTypeIds("1,2,3");
        manualEntryPO.setEffectDateStart(DateUtil.format06(DateUtil.beginOfYear(params.getYear())));
        manualEntryPO.setEffectDateEnd(DateUtil.format06(DateUtil.endOfYear(params.getYear())));
        FastIncomeManualEntryPO manualEntry = fastIncomeManualEntryMapper.queryIncomeMonthTotal(manualEntryPO);
        if (manualEntry != null) {
            monthTotal.put("douyinOriginProfit", manualEntry.getDouyinOriginProfit());
            monthTotal.put("kuaishouOriginProfit", manualEntry.getKuaishouOriginProfit());
            monthTotal.put("kuaishouJuxingProfit", manualEntry.getKuaishouJuxingProfit());
        } else {
            monthTotal.put("douyinOriginProfit", 0);
            monthTotal.put("kuaishouOriginProfit", 0);
            monthTotal.put("kuaishouJuxingProfit", 0);
        }
        return monthTotal;
    }

    /**
     * 批量新增
     */
    public void insertUpdateBatch(Date startDate, Date endDate) {
        fastStaffCostIncomeMonthMapper.insertUpdateBatch(startDate, endDate);
    }

    /**
     * 获取总利润
     *
     * @return
     */
    public BigDecimal totalProfit(BigDecimal moneyRecharge, BigDecimal cashCostBack,
                                  BigDecimal virtualRecharge, BigDecimal normalRecharge) {
        BigDecimal virtual = DoubleUtil.mulB(DoubleUtil.divB4Zero(virtualRecharge, moneyRecharge), BigDecimal.valueOf(0.165));
        BigDecimal normal = DoubleUtil.mulB(DoubleUtil.divB4Zero(normalRecharge, moneyRecharge), BigDecimal.valueOf(0.01));
        BigDecimal normal1 = DoubleUtil.mulB(DoubleUtil.divB4Zero(normalRecharge, moneyRecharge), DoubleUtil.mulB(0.99, 0.15));

        BigDecimal companyProfit = DoubleUtil.mulB(moneyRecharge, DoubleUtil.addB(DoubleUtil.addB(virtual, normal), normal1));

        BigDecimal totalProfit = DoubleUtil.subB(moneyRecharge, cashCostBack);
        return DoubleUtil.subB(totalProfit, companyProfit);
    }

    /**
     * 设置媒体结算比例
     */
    public void setMediaSettlement() {
        Integer nowMonth = DateUtil.format05Int(new Date());
        FastSettingMediaSettlementPO po = new FastSettingMediaSettlementPO();
        po.setState(1);
        List<FastSettingMediaSettlementPO> list = fastSettingMediaSettlementService.queryList(po);
        for (FastSettingMediaSettlementPO val : list) {
            FastSettingMediaSettlementPO po0 = new FastSettingMediaSettlementPO();
            po0.setEffectMonth(nowMonth);
            po0.setMediaType(val.getMediaType());
            po0.setState(0);
            if (fastSettingMediaSettlementService.queryOne(po0) == null) {
                val.setEffectMonth(nowMonth);
                val.setState(0);
                fastSettingMediaSettlementService.insert(val);
            }
        }
    }

    /**
     * 设置利润分成比例
     */
    public void setMediaProfit() {
        Integer nowMonth = DateUtil.format05Int(new Date());
        FastSettingMediaProfitPO po = new FastSettingMediaProfitPO();
        po.setState(1);
        List<FastSettingMediaProfitPO> list = fastSettingMediaProfitService.queryList(po);
        for (FastSettingMediaProfitPO val : list) {
            FastSettingMediaProfitPO po0 = new FastSettingMediaProfitPO();
            po0.setEffectMonth(nowMonth);
            po0.setMediaType(val.getMediaType());
            po0.setState(0);
            if (fastSettingMediaProfitService.queryOne(po0) == null) {
                val.setEffectMonth(nowMonth);
                val.setState(0);
                fastSettingMediaProfitService.insert(val);
            }
        }
    }

}
