/*
 * Powered By fast.up
 */
package com.fast.service.business;

import com.fast.constant.StaticStr;
import com.fast.mapper.business.FastStaffMapper;
import com.fast.mapper.business.FastStaffSalaryMapper;
import com.fast.po.business.FastStaffPO;
import com.fast.po.business.FastStaffSalaryPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastStaffSalaryService extends BaseService {

    @Autowired
    private FastStaffSalaryMapper fastStaffSalaryMapper;
    @Autowired
    private FastStaffMapper fastStaffMapper;
    @Autowired
    private FastStaffService fastStaffService;

    /**
     * 通过id查询单个对象
     */
    public FastStaffSalaryPO queryById(FastStaffSalaryPO params) {
        return fastStaffSalaryMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStaffSalaryPO queryById(Integer id) {
        return fastStaffSalaryMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStaffSalaryPO queryOne(FastStaffSalaryPO params) {
        return fastStaffSalaryMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffSalaryPO> queryList(FastStaffSalaryPO params) {
        return fastStaffSalaryMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStaffSalaryPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStaffSalaryPO> list = fastStaffSalaryMapper.queryList(params);
        for (FastStaffSalaryPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setMonthStr(DateUtil.format10(DateUtil.format05(cur.getMonth().toString())));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStaffSalaryPO params) {
        return fastStaffSalaryMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStaffSalaryPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStaffSalaryMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStaffSalaryPO> list) {
        if (fastStaffSalaryMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertUpdateBatch(List<FastStaffSalaryPO> list) {
        if (fastStaffSalaryMapper.insertUpdateBatch(list) > 0) {
            return MethodVO.success("");
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStaffSalaryPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStaffSalaryMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public MethodVO importStaffSalary(List<List<Object>> data, Date nowDate, SessionVO sessionVO) {
        if (data.isEmpty() || data.size() == 1) {
            return MethodVO.error("数据为空!");
        }
        List<FastStaffPO> staffList = fastStaffService.queryList(null);
        Map<String, Integer> staffMap = staffList.stream().collect(Collectors.toMap(FastStaffPO::getRealName, FastStaffPO::getId));
        List<FastStaffSalaryPO> fastStaffSalary = new ArrayList<>();
        String msg = "";
        for (List<Object> val : data) {
            String monthStr = val.get(0).toString();
            if (StrUtil.isEmpty(monthStr) || Objects.equals(monthStr, "月份")) {
                continue;
            }
            String realName = val.get(1).toString();
            Integer staffId = staffMap.getOrDefault(realName, 0);
            if (staffId == null || staffId == 0) {
                if (StrUtil.isNotEmpty(msg)) {
                    msg += "、";
                }
                msg += realName;
                continue;
//                return MethodVO.error("支出人“" + realName + "”未添加，添加后重新导入!");
//                FastStaffPO staffPo = new FastStaffPO();
//                staffPo.setRealName(realName);
//                staffPo.setState(1);
//                staffPo.setRemark("");
//                staffPo.setCreateTime(nowDate);
//                staffPo.setUpdateTime(nowDate);
//                if (fastStaffMapper.insertSelective(staffPo) > 0) {
//                    staffId = staffPo.getId();
//                } else {
//                    return MethodVO.error("数据错误!");
//                }
            }
            FastStaffSalaryPO po = new FastStaffSalaryPO();
            po.setMonth(DateUtil.format05Int(DateUtil.format10(monthStr)));
            po.setRealName(realName);
            po.setStaffId(staffId);
            po.setSalary(BigDecimal.valueOf(Double.parseDouble(val.get(2).toString())));
            po.setSalaryOther(BigDecimal.valueOf(Double.parseDouble(val.get(3).toString())));
            po.setCreatorId(sessionVO.getUserId());
            po.setUpdatorId(sessionVO.getUserId());
            po.setRemark("");
            po.setCreateTime(nowDate);
            po.setUpdateTime(nowDate);
            fastStaffSalary.add(po);
        }
        if (!fastStaffSalary.isEmpty()) {
            MethodVO methodVO = insertUpdateBatch(fastStaffSalary);
            if (StrUtil.isNotEmpty(msg)) {
                methodVO.setMessage("支出人“" + msg + "”未添加，请添加后重新导入");
            }
            return methodVO;
        } else {
            return MethodVO.error("数据错误");
        }
    }
}
