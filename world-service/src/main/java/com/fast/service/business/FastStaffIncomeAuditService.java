/*
 * Powered By fast.up
 */
package com.fast.service.business;

import com.fast.constant.StaticStr;
import com.fast.mapper.business.FastStaffIncomeAuditMapper;
import com.fast.mapper.business.FastStaffIncomeMapper;
import com.fast.po.business.FastStaffIncomeAuditPO;
import com.fast.po.business.FastStaffIncomePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastStaffIncomeAuditService extends BaseService {

    @Autowired
    private FastStaffIncomeAuditMapper fastStaffIncomeAuditMapper;
    @Autowired
    private FastStaffIncomeMapper fastStaffIncomeMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStaffIncomeAuditPO queryById(FastStaffIncomeAuditPO params) {
        return fastStaffIncomeAuditMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStaffIncomeAuditPO queryById(Integer id) {
        return fastStaffIncomeAuditMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStaffIncomeAuditPO queryOne(FastStaffIncomeAuditPO params) {
        return fastStaffIncomeAuditMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffIncomeAuditPO> queryList(FastStaffIncomeAuditPO params) {
        return fastStaffIncomeAuditMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStaffIncomeAuditPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStaffIncomeAuditPO> list = fastStaffIncomeAuditMapper.queryList(params);
        for (FastStaffIncomeAuditPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStaffIncomeAuditPO params) {
        return fastStaffIncomeAuditMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStaffIncomeAuditPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStaffIncomeAuditMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStaffIncomeAuditPO> list) {
        if (fastStaffIncomeAuditMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStaffIncomeAuditPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStaffIncomeAuditMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }

        FastStaffIncomePO incomePO = new FastStaffIncomePO();
        incomePO.setState(0);
        if (fastStaffIncomeMapper.queryOne(incomePO) != null) {
            incomePO.setIsAudit(params.getIsAudit());
            if (fastStaffIncomeMapper.updateByAudit(incomePO) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        return MethodVO.success();
    }
}
