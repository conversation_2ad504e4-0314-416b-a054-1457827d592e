/*
 * Powered By fast.up
 */
package com.fast.service.business;

import com.fast.constant.StaticStr;
import com.fast.mapper.business.FastStaffGroupAmortizationMapper;
import com.fast.po.business.FastStaffGroupAmortizationPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastStaffGroupAmortizationService extends BaseService {

    @Autowired
    private FastStaffGroupAmortizationMapper fastStaffGroupAmortizationMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStaffGroupAmortizationPO queryById(FastStaffGroupAmortizationPO params) {
        return fastStaffGroupAmortizationMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStaffGroupAmortizationPO queryById(Integer id) {
        return fastStaffGroupAmortizationMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStaffGroupAmortizationPO queryOne(FastStaffGroupAmortizationPO params) {
        return fastStaffGroupAmortizationMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffGroupAmortizationPO> queryList(FastStaffGroupAmortizationPO params) {
        return fastStaffGroupAmortizationMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStaffGroupAmortizationPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStaffGroupAmortizationPO> list = fastStaffGroupAmortizationMapper.queryList(params);
        for (FastStaffGroupAmortizationPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setMonthStr(DateUtil.format10(DateUtil.format05(cur.getMonth().toString())));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStaffGroupAmortizationPO params) {
        return fastStaffGroupAmortizationMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStaffGroupAmortizationPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStaffGroupAmortizationMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStaffGroupAmortizationPO> list) {
        if (fastStaffGroupAmortizationMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertUpdateBatch(List<FastStaffGroupAmortizationPO> list) {
        if (fastStaffGroupAmortizationMapper.insertUpdateBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStaffGroupAmortizationPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStaffGroupAmortizationMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public MethodVO importSalaryGroupAmortization(List<List<Object>> data, SessionVO sessionVO) {
        List<FastStaffGroupAmortizationPO> insertData = new ArrayList<>();
        Date nowDate = DateUtil.getNowDate();
        Integer month = null;
        for (List<Object> val : data) {
            String monthStr = val.get(0).toString();
            if (StrUtil.isEmpty(monthStr) || Objects.equals(monthStr, "月份")) {
                continue;
            }
            month = DateUtil.format05Int(DateUtil.format10(monthStr));
            String groupName = val.get(1).toString();
            FastStaffGroupAmortizationPO po = new FastStaffGroupAmortizationPO();
            po.setMonth(month);
            po.setGroupId(0);
            po.setGroupName(groupName);
            po.setManageCost(BigDecimal.valueOf(Double.parseDouble(val.get(2).toString())));
            po.setOperationCost(BigDecimal.valueOf(Double.parseDouble(val.get(3).toString())));
            po.setTechnicalCost(BigDecimal.valueOf(Double.parseDouble(val.get(4).toString())));
            po.setMaterialCost(BigDecimal.valueOf(Double.parseDouble(val.get(5).toString())));
            po.setExternalTechnicalCost(BigDecimal.valueOf(Double.parseDouble(val.get(6).toString())));
            po.setExternalMaterialCost(BigDecimal.valueOf(Double.parseDouble(val.get(7).toString())));
            po.setReimbursementCost(BigDecimal.valueOf(Double.parseDouble(val.get(8).toString())));
            po.setCompanyManageCost(BigDecimal.valueOf(Double.parseDouble(val.get(9).toString())));
            po.setCompanyAdministrativeCost(BigDecimal.valueOf(Double.parseDouble(val.get(10).toString())));
            po.setCreatorId(sessionVO.getUserId());
            po.setUpdatorId(sessionVO.getUserId());
            po.setRemark("");
            po.setCreateTime(nowDate);
            po.setUpdateTime(nowDate);
            insertData.add(po);
        }
        if (!insertData.isEmpty()) {
            return insertUpdateBatch(insertData);
        } else {
            return MethodVO.error("数据错误");
        }
    }
}
