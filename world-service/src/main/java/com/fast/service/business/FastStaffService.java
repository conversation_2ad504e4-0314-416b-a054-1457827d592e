/*
 * Powered By fast.up
 */
package com.fast.service.business;

import com.fast.constant.StaticStr;
import com.fast.mapper.business.FastStaffMapper;
import com.fast.po.business.FastStaffPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastStaffService extends BaseService {

    @Autowired
    private FastStaffMapper fastStaffMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStaffPO queryById(FastStaffPO params) {
        return fastStaffMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStaffPO queryById(Integer id) {
        return fastStaffMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStaffPO queryOne(FastStaffPO params) {
        return fastStaffMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffPO> queryList(FastStaffPO params) {
        return fastStaffMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStaffPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStaffPO> list = fastStaffMapper.queryUserList(params);
        for (FastStaffPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStaffPO params) {
        return fastStaffMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStaffPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStaffMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStaffPO> list) {
        if (fastStaffMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStaffPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStaffMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastStaffPO params) {
        if (fastStaffMapper.deleteById(params) == 0) {
            transactionRollBack();
            return MethodVO.error("删除失败");
        }
        return MethodVO.success();
    }
}
