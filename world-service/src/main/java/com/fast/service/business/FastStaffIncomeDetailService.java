/*
 * Powered By fast.up
 */
package com.fast.service.business;

import com.fast.constant.StaticStr;
import com.fast.mapper.business.FastStaffIncomeDetailMapper;
import com.fast.po.business.FastStaffIncomeDetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastStaffIncomeDetailService extends BaseService {

    @Autowired
    private FastStaffIncomeDetailMapper fastStaffIncomeDetailMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStaffIncomeDetailPO queryById(FastStaffIncomeDetailPO params) {
        return fastStaffIncomeDetailMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStaffIncomeDetailPO queryById(Integer id) {
        return fastStaffIncomeDetailMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStaffIncomeDetailPO queryOne(FastStaffIncomeDetailPO params) {
        return fastStaffIncomeDetailMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffIncomeDetailPO> queryList(FastStaffIncomeDetailPO params) {
        return fastStaffIncomeDetailMapper.queryList(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffIncomeDetailPO> queryIncomeList(FastStaffIncomeDetailPO params) {
        return fastStaffIncomeDetailMapper.queryIncomeList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStaffIncomeDetailPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStaffIncomeDetailPO> list = fastStaffIncomeDetailMapper.queryList(params);
        for (FastStaffIncomeDetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStaffIncomeDetailPO params) {
        return fastStaffIncomeDetailMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStaffIncomeDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStaffIncomeDetailMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStaffIncomeDetailPO> list) {
        if (fastStaffIncomeDetailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStaffIncomeDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStaffIncomeDetailMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
