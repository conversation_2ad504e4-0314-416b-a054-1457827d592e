/*
 * Powered By fast.up
 */
package com.fast.service.business;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.business.FastIncomeManualEntryMapper;
import com.fast.mapper.business.FastStaffCostIncomeDayMapper;
import com.fast.mapper.setting.FastSettingMediaBackMapper;
import com.fast.po.business.FastIncomeManualEntryPO;
import com.fast.po.business.FastStaffCostIncomeDayPO;
import com.fast.po.setting.FastSettingMediaBackPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.setting.FastMemberOrderSettingService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastStaffCostIncomeDayService extends BaseService {

    @Autowired
    private FastStaffCostIncomeDayMapper fastStaffCostIncomeDayMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastSettingMediaBackMapper settingMediaBackMapper;
    @Autowired
    private FastMemberOrderSettingService orderSettingService;
    @Autowired
    private FastIncomeManualEntryService fastIncomeManualEntryService;
    @Autowired
    private FastIncomeManualEntryMapper fastIncomeManualEntryMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStaffCostIncomeDayPO queryById(FastStaffCostIncomeDayPO params) {
        return fastStaffCostIncomeDayMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStaffCostIncomeDayPO queryById(Integer id) {
        return fastStaffCostIncomeDayMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStaffCostIncomeDayPO queryOne(FastStaffCostIncomeDayPO params) {
        return fastStaffCostIncomeDayMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffCostIncomeDayPO> queryList(FastStaffCostIncomeDayPO params) {
        return fastStaffCostIncomeDayMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStaffCostIncomeDayPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStaffCostIncomeDayPO> list = fastStaffCostIncomeDayMapper.queryList(params);
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStaffCostIncomeDayPO params) {
        return fastStaffCostIncomeDayMapper.queryCount(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> exportRechargeData(SessionVO sessionVO, Map<String, Object> data, String type) {
        String keyRate = StaticVar.EXPORT_RATE + sessionVO.getUserId();
        String valueRate = RedisUtil.get(keyRate);
        if (notBlank(valueRate)) {
            return ResultVO.error(StaticStr.CURRENTLY_HAVE_TASKS_EXPORTING_3_10);
        } else {
            RedisUtil.set(keyRate, "1", RedisUtil.TIME_10M);
        }

        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_RECHARGE_ORDER_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getUserId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }

        if (CollUtil.isEmpty(data)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        List<Object> row = new ArrayList<>();// 导出的数据(一行)
        if (Objects.equals(type, "month")) {
            row.add(data.get("monthStr"));
            CollUtil.addNoRepeat(rowHeadNames, "月份");
        } else {
            row.add(data.get("date"));
            CollUtil.addNoRepeat(rowHeadNames, "日期");
        }

        row.add(data.get("moneyProfit"));
        CollUtil.addNoRepeat(rowHeadNames, "平台充值总金额");
        row.add(data.get("moneyRecharge"));
        CollUtil.addNoRepeat(rowHeadNames, "平台实到总金额");
        row.add(data.get("usProfit"));
        CollUtil.addNoRepeat(rowHeadNames, "自投充值");
        row.add(data.get("agentProfit"));
        CollUtil.addNoRepeat(rowHeadNames, "代投充值");
        row.add(data.get("aloneProfit"));
        CollUtil.addNoRepeat(rowHeadNames, "分销充值");
        row.add(data.get("nativeProfit"));
        CollUtil.addNoRepeat(rowHeadNames, "native充值");
        row.add(data.get("kuaishouMountProfit"));
        CollUtil.addNoRepeat(rowHeadNames, "快手挂载充值");
        row.add(data.get("douyinMountProfit"));
        CollUtil.addNoRepeat(rowHeadNames, "抖音挂载充值");
        row.add(data.get("douyinOriginProfit"));
        CollUtil.addNoRepeat(rowHeadNames, "抖音原生收入");
        row.add(data.get("kuaishouOriginProfit"));
        CollUtil.addNoRepeat(rowHeadNames, "快手原生收入");
        row.add(data.get("kuaishouJuxingProfit"));
        CollUtil.addNoRepeat(rowHeadNames, "快手聚星收入");

        dataList.add(row);

        log.info(toJSONString(dataList));
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title;
        if (Objects.equals(type, "today")) {
            title = "今日总数据";
        } else if (Objects.equals(type, "yesterday")) {
            title = "昨日总数据";
        } else {
            title = "本月总数据";
        }

        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);

        RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
        return ResultVO.success();
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStaffCostIncomeDayPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStaffCostIncomeDayMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStaffCostIncomeDayPO> list) {
        if (fastStaffCostIncomeDayMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStaffCostIncomeDayPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStaffCostIncomeDayMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 查询全部
     */
    public List<FastStaffCostIncomeDayPO> mediaRechargeData(FastStaffCostIncomeDayPO params) {
        return fastStaffCostIncomeDayMapper.mediaRechargeData(params);
    }

    /**
     * 查询全部
     */
    public List<FastStaffCostIncomeDayPO> mediaRechargeOtherData(FastStaffCostIncomeDayPO params) {
        return fastStaffCostIncomeDayMapper.mediaRechargeOtherData(params);
    }

    /**
     * 查询全部
     */
    public Map<String, Object> mediaRechargeOverview(FastStaffCostIncomeDayPO params) {
        Map<String, Object> overviewData = fastStaffCostIncomeDayMapper.mediaRechargeOverviewData(params);
        FastIncomeManualEntryPO manualEntryPO = new FastIncomeManualEntryPO();
        manualEntryPO.setEffectDate(Integer.valueOf(params.getDateStr().replaceAll("-", "")));
        manualEntryPO.setState(1);
        manualEntryPO.setIncomeTypeIds("1,2,3");
        List<FastIncomeManualEntryPO> manualEntry = fastIncomeManualEntryService.queryIncome(manualEntryPO);
        if (overviewData == null) {
            overviewData = new HashMap<>();
        }
        overviewData.put("douyinOriginProfit", 0);
        overviewData.put("kuaishouOriginProfit", 0);
        overviewData.put("kuaishouJuxingProfit", 0);
        for (FastIncomeManualEntryPO cur : manualEntry) {
            if (cur.getIncomeTypeId() == 1) {
                overviewData.put("douyinOriginProfit", cur.getIncome());
            }
            if (cur.getIncomeTypeId() == 2) {
                overviewData.put("kuaishouOriginProfit", cur.getIncome());
            }
            if (cur.getIncomeTypeId() == 3) {
                overviewData.put("kuaishouJuxingProfit", cur.getIncome());
            }
        }
        return overviewData;
    }

    /**
     * 查询全部
     */
    public Map<String, Object> mediaRechargeOverviewMonth(FastStaffCostIncomeDayPO params) {
        Map<String, Object> overviewData = fastStaffCostIncomeDayMapper.mediaRechargeOverviewDataMonth(params);
        String monthStart = params.getMonth() + "-01";
        FastIncomeManualEntryPO manualEntryPO = new FastIncomeManualEntryPO();
        manualEntryPO.setEffectDateStart(DateUtil.getStartDateOfMonth(DateUtil.format09(monthStart)).replaceAll("-", ""));
        manualEntryPO.setEffectDateEnd(DateUtil.getEndDateOfMonth(DateUtil.format09(monthStart)).replaceAll("-", ""));
        manualEntryPO.setState(1);
        manualEntryPO.setIncomeTypeIds("1,2,3");
        FastIncomeManualEntryPO manualEntry = fastIncomeManualEntryMapper.queryIncomeMonthTotal(manualEntryPO);
        if (overviewData == null) {
            overviewData = new HashMap<>();
        }
        if (manualEntry != null) {
            overviewData.put("douyinOriginProfit", manualEntry.getDouyinOriginProfit());
            overviewData.put("kuaishouOriginProfit", manualEntry.getKuaishouOriginProfit());
            overviewData.put("kuaishouJuxingProfit", manualEntry.getKuaishouJuxingProfit());
        } else {
            overviewData.put("douyinOriginProfit", 0);
            overviewData.put("kuaishouOriginProfit", 0);
            overviewData.put("kuaishouJuxingProfit", 0);
        }

        return overviewData;
    }

    /**
     * 查询全部
     */
    public void mediaFund(FastStaffCostIncomeDayPO params) {
        Map<Integer, FastStaffCostIncomeDayPO> dataMap = new HashMap<>();
        List<FastSettingMediaBackPO> mediaBackList = settingMediaBackMapper.queryEffectGroupByMediaType(new Date());
        Map<Integer, BigDecimal> mediaBackMap = new HashMap<>();
        for (FastSettingMediaBackPO val : mediaBackList) {
            if (val.getMediaType() == 1) {
                mediaBackMap.put(1, val.getBackRatio());
            } else if (val.getMediaType() == 2) {
                mediaBackMap.put(2, val.getBackRatio());
            } else if (val.getMediaType() == 3) {
                mediaBackMap.put(5, val.getBackRatio());
            } else if (val.getMediaType() == 4) {
                mediaBackMap.put(501, val.getBackRatio());
            } else if (val.getMediaType() == 5) {
                mediaBackMap.put(4, val.getBackRatio());
            } else if (val.getMediaType() == 6) {
                mediaBackMap.put(102, val.getBackRatio());
            } else if (val.getMediaType() == 7) {
                mediaBackMap.put(502, val.getBackRatio());
            }
        }
        params.setPayTimeStart(params.getStatDate());
        params.setPayTimeEnd(DateUtil.endOfDay(params.getStatDate()));
        List<FastStaffCostIncomeDayPO> rechargeList = fastStaffCostIncomeDayMapper.queryRechargeList(params);
        for (FastStaffCostIncomeDayPO val : rechargeList) {
            dataMap.put(val.getUserId(), val);
        }

        List<FastStaffCostIncomeDayPO> dataList = new ArrayList<>();
        for (FastStaffCostIncomeDayPO val : dataMap.values()) {
            val.setBackRatio(mediaBackMap.getOrDefault(val.getMediaType(), BigDecimal.ZERO));
            val.setDate(params.getStatDate());
            val.setCreateTime(new Date());
            val.setUpdateTime(new Date());
            dataList.add(val);
        }

        FastStaffCostIncomeDayPO kuaishouRechargeList = fastStaffCostIncomeDayMapper.queryKuaishouRechargeList(params);
        if (kuaishouRechargeList != null) {
            kuaishouRechargeList.setBackRatio(mediaBackMap.getOrDefault(502, BigDecimal.ZERO));
            kuaishouRechargeList.setDate(params.getStatDate());
            kuaishouRechargeList.setCreateTime(new Date());
            kuaishouRechargeList.setUpdateTime(new Date());
            log.info(toJSONString(kuaishouRechargeList));
            dataList.add(kuaishouRechargeList);
        }

        FastStaffCostIncomeDayPO douyinRechargeList = fastStaffCostIncomeDayMapper.queryXingtuRechargeList(params);
        if (douyinRechargeList != null) {
            douyinRechargeList.setBackRatio(mediaBackMap.getOrDefault(102, BigDecimal.ZERO));
            douyinRechargeList.setDate(params.getStatDate());
            douyinRechargeList.setCreateTime(new Date());
            douyinRechargeList.setUpdateTime(new Date());
            log.info(toJSONString(douyinRechargeList));
            dataList.add(douyinRechargeList);
        }
        for (FastStaffCostIncomeDayPO fastStaffCostIncomeDayPO : dataList) {
            fastStaffCostIncomeDayPO.setContentType(params.getContentType());
        }

        fastStaffCostIncomeDayMapper.insertUpdateBatch(dataList);
    }

}
