/*
 * Powered By fast.up
 */
package com.fast.service.upay;

import com.fast.constant.StaticStr;
import com.fast.mapper.upay.UpayOrderNotifyMapper;
import com.fast.po.upay.UpayOrderNotify;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class UpayOrderNotifyService extends BaseService {

    @Autowired
    private UpayOrderNotifyMapper upayOrderNotifyMapper;

    /**
     * 通过id查询单个对象
     */
    public UpayOrderNotify queryById(UpayOrderNotify item) {
        return upayOrderNotifyMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public UpayOrderNotify queryById(Long id) {
        UpayOrderNotify itemParam = new UpayOrderNotify();
        itemParam.setId(id);
        return upayOrderNotifyMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public UpayOrderNotify queryOne(UpayOrderNotify item) {
        return upayOrderNotifyMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<UpayOrderNotify> queryList(UpayOrderNotify item) {
        return upayOrderNotifyMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(UpayOrderNotify item, PageVO pageVO) {
        startPage(pageVO);
        List<UpayOrderNotify> list = upayOrderNotifyMapper.queryList(item);
        for (UpayOrderNotify cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(UpayOrderNotify item) {
        return upayOrderNotifyMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(UpayOrderNotify item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (upayOrderNotifyMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<UpayOrderNotify> list) {
        if (upayOrderNotifyMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(UpayOrderNotify item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (upayOrderNotifyMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
