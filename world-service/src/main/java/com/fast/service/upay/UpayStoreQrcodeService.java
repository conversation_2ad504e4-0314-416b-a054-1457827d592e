/*
 * Powered By fast.up
 */
package com.fast.service.upay;

import com.fast.constant.StaticStr;
import com.fast.mapper.upay.UpayStoreQrcodeMapper;
import com.fast.po.upay.UpayStoreQrcode;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class UpayStoreQrcodeService extends BaseService {

    @Autowired
    private UpayStoreQrcodeMapper upayStoreQrcodeMapper;

    /**
     * 通过id查询单个对象
     */
    public UpayStoreQrcode queryById(UpayStoreQrcode item) {
        return upayStoreQrcodeMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public UpayStoreQrcode queryById(Integer id) {
        UpayStoreQrcode itemParam = new UpayStoreQrcode();
        itemParam.setId(id);
        return upayStoreQrcodeMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public UpayStoreQrcode queryOne(UpayStoreQrcode item) {
        return upayStoreQrcodeMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<UpayStoreQrcode> queryList(UpayStoreQrcode item) {
        return upayStoreQrcodeMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(UpayStoreQrcode item, PageVO pageVO) {
        startPage(pageVO);
        List<UpayStoreQrcode> list = upayStoreQrcodeMapper.queryList(item);
        for (UpayStoreQrcode cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(UpayStoreQrcode item) {
        return upayStoreQrcodeMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(UpayStoreQrcode item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (upayStoreQrcodeMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<UpayStoreQrcode> list) {
        if (upayStoreQrcodeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(UpayStoreQrcode item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (upayStoreQrcodeMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
