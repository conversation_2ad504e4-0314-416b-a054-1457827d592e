/*
 * Powered By www.cardmee.net
 */
package com.fast.service.upay;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.mapper.upay.UpayOrderLogMapper;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.upay.UpayOrderLog;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.GooglePlayUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.google.api.services.androidpublisher.model.BuyerAddress;
import com.google.api.services.androidpublisher.model.LineItem;
import com.google.api.services.androidpublisher.model.Money;
import com.google.api.services.androidpublisher.model.Order;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class UpayOrderLogService extends BaseService {

    @Autowired
    private UpayOrderLogMapper upayOrderLogMapper;

    @Autowired
    private GooglePlayUtil googlePlayUtil;

    @Autowired
    private FastMemberOrderRechargeMapper fastMemberOrderRechargeMapper;

    /**
     * 通过id查询单个对象
     */
    public UpayOrderLog queryById(UpayOrderLog item) {
        return upayOrderLogMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public UpayOrderLog queryById(Long id) {
        UpayOrderLog itemParam = new UpayOrderLog();
        itemParam.setId(id);
        return upayOrderLogMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public UpayOrderLog queryOne(UpayOrderLog item) {
        return upayOrderLogMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<UpayOrderLog> queryList(UpayOrderLog item) {
        return upayOrderLogMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(UpayOrderLog item, PageVO pageVO) {
        startPage(pageVO);
        List<UpayOrderLog> list = upayOrderLogMapper.queryList(item);
        for (UpayOrderLog cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(UpayOrderLog item) {
        return upayOrderLogMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(UpayOrderLog item) {
        Date nowTime = DateUtil.getNowDate();
        if (upayOrderLogMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<UpayOrderLog> list) {
        if (upayOrderLogMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(UpayOrderLog item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (upayOrderLogMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public UpayOrderLog queryByTermOrdId(String termOrdId) {
        return queryByTermOrdId(Long.parseLong(termOrdId));
    }

    public UpayOrderLog queryByTermOrdId(Long termOrdId) {
        if (StrUtil.isBlank(termOrdId)) {
            return null;
        }
        UpayOrderLog item = new UpayOrderLog();
        item.setTermOrdId(termOrdId);
        return queryOne(item);
    }

    @Transactional(rollbackFor = Exception.class)
    public void fillGoogleProductInfo(UpayOrderLog upayOrderLog) {
        try {
            Order gpOrder = googlePlayUtil.getOrder(upayOrderLog.getOutTransId());
            if (gpOrder == null) {
                log.info("根据google订单号 {} 未能查询到订单数据, payLogId: {}", upayOrderLog.getOutTransId(), upayOrderLog.getId());
                return;
            }
            UpayOrderLog orderLogUpdate = new UpayOrderLog();
            orderLogUpdate.setId(upayOrderLog.getId());
            // 地区
            BuyerAddress buyerAddress = gpOrder.getBuyerAddress();
            String buyerCountry = buyerAddress.getBuyerCountry();
            String buyerState = buyerAddress.getBuyerState();
            if (!StrUtil.isBlank(buyerState)) {
                orderLogUpdate.setRegion(buyerState);
            } else {
                orderLogUpdate.setRegion(buyerCountry);
            }
            // 商品标题
            List<LineItem> lineItems = gpOrder.getLineItems();
            if (CollUtil.isNotEmpty(lineItems)) {
                orderLogUpdate.setGoodsDesc(lineItems.get(0).getProductTitle());
            }
            // 实到金额，这里的数据会有延时
            orderLogUpdate.setRealAmt(convertRealAmt(gpOrder.getDeveloperRevenueInBuyerCurrency()));
            // 购买令牌，兜底
            if (StrUtil.isBlank(upayOrderLog.getGpToken())) {
                orderLogUpdate.setGpToken(gpOrder.getPurchaseToken());
            }
            // gp pay time
            if (!StrUtil.isBlank(gpOrder.getLastEventTime())) {
                orderLogUpdate.setEventTime(DateUtil.getTimestamp(gpOrder.getLastEventTime()));
            }
            orderLogUpdate.setUpdateTime(DateUtil.getNowDate());
            upayOrderLogMapper.updateById(orderLogUpdate);
            // 回填订单数据
            FastMemberOrderRechargePO rechargePO = fastMemberOrderRechargeMapper.queryByPayLogId(upayOrderLog.getId());
            if (rechargePO != null) {
                FastMemberOrderRechargePO rechargeUpdate = new FastMemberOrderRechargePO();
                rechargeUpdate.setId(rechargePO.getId());
                rechargeUpdate.setProductName(orderLogUpdate.getGoodsDesc());
                rechargeUpdate.setMoneyProfit(orderLogUpdate.getRealAmt());
                fastMemberOrderRechargeMapper.updateById(rechargeUpdate);
            }
        } catch (Exception e) {
            log.error("填充支付单 {} 中的谷歌订单信息失败: {}", upayOrderLog.getId(), ExceptionUtils.getStackTrace(e));
        }
    }

    private BigDecimal convertRealAmt(Money developerRevenueInBuyerCurrency) {
        if (developerRevenueInBuyerCurrency == null) {
            log.info("当前还未返回developerRevenueInBuyerCurrency字段，可能还在计算手续费中...");
            return BigDecimal.ZERO;
        }
        // String currencyCode = developerRevenueInBuyerCurrency.getCurrencyCode();
        Long units = developerRevenueInBuyerCurrency.getUnits();
        if (units == null) {
            units = 0L;
        }
        Integer nanos = developerRevenueInBuyerCurrency.getNanos();
        if (nanos == null) {
            nanos = 0;
        }
        // 将units转换为BigDecimal
        BigDecimal unitsPart = BigDecimal.valueOf(units);
        // 将nanos转换为小数部分：nanos / 10^9
        BigDecimal nanosPart = BigDecimal.valueOf(nanos).divide(BigDecimal.valueOf(1_000_000_000L), 9, RoundingMode.UNNECESSARY);
        // 相加得到最终金额
        return unitsPart.add(nanosPart);
    }

}
