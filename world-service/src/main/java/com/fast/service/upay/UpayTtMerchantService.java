/*
 * Powered By fast.up
 */
package com.fast.service.upay;

import com.fast.constant.StaticStr;
import com.fast.mapper.upay.UpayTtMerchantMapper;
import com.fast.po.upay.UpayTtMerchantPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class UpayTtMerchantService extends BaseService {

    @Autowired
    private UpayTtMerchantMapper upayTtMerchantMapper;

    /**
     * 通过id查询单个对象
     */
    public UpayTtMerchantPO queryById(UpayTtMerchantPO params) {
        return upayTtMerchantMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public UpayTtMerchantPO queryById(Integer id) {
        return upayTtMerchantMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public UpayTtMerchantPO queryOne(UpayTtMerchantPO params) {
        return upayTtMerchantMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<UpayTtMerchantPO> queryList(UpayTtMerchantPO params) {
        return upayTtMerchantMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(UpayTtMerchantPO params, PageVO pageVO) {
        startPage(pageVO);
        List<UpayTtMerchantPO> list = upayTtMerchantMapper.queryList(params);
        for (UpayTtMerchantPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(UpayTtMerchantPO params) {
        return upayTtMerchantMapper.queryCount(params);
    }


    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(UpayTtMerchantPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (upayTtMerchantMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
