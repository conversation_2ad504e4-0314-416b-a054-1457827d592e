package com.fast.service.upay;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticVar;
import com.fast.constant.StaticVarField;
import com.fast.enums.upay.UpayChannelEnum;
import com.fast.enums.upay.UpayOrderStatusEnum;
import com.fast.enums.upay.UpayOrderTypeEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.upay.UpayOrderLogMapper;
import com.fast.po.upay.UpayOrderLog;
import com.fast.service.base.BaseService;
import com.fast.service.mini.FastMiniService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.SignUtil;
import com.fast.utils.upay.PayUtil;
import com.fast.utils.upay.PostUtil;
import com.fast.utils.upay.UnicodeUtils;
import com.fast.utils.xml.XmlUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.mini.FastMiniVO;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.net.ssl.SSLContext;
import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * UnionPay
 *
 * <AUTHOR>
 */
@Service
public class UpayRefundService extends BaseService {

    @Autowired
    private UpayOrderLogMapper upayOrderLogMapper;
    @Autowired
    private FastMiniService miniService;

    /**
     * 发起退款-普通型
     *
     * @param resultData
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void doRefund(ResultVO<Map<String, Object>> resultData, UpayOrderLog param) throws IOException {
        Date nowTime = DateUtil.getNowDate();
        // 1.查询原始支付订单
        UpayOrderLog query = new UpayOrderLog();
        query.setRetailId(param.getRetailId());
        query.setMiniId(param.getMiniId());
        query.setId(param.getId());
        // 原始订单号
        query.setTermOrdId(param.getOrgTermOrdId());
        query.setOutTransId(param.getOutTransId());
        query.setOrderType(UpayOrderTypeEnum.PAY.idnex);// 消费
        query.setState(UpayOrderStatusEnum.SUCCESS.index);// 支付成功
        UpayOrderLog orgOrderLogDB = upayOrderLogMapper.queryOne(query);
        if (orgOrderLogDB == null || orgOrderLogDB.getId() == null || orgOrderLogDB.getId() <= 0) {
            resultData.setMessage(StaticMsg.MSG_PAY_ERROR_118);
            resultData.setCode(501);
            throw new MyException(StaticMsg.MSG_PAY_ERROR_118);
        }
        if (isBlank(param.getNonceStr())) {
            param.setNonceStr(StrUtil.getRandomStr(30));
        }
        // 默认全退款
        param.setOrdAmt(orgOrderLogDB.getOrdAmt());
        // 退款沿用之前支付通道（原始通道返还）
        param.setPayChannel(orgOrderLogDB.getPayChannel());
        param.setHfMemberId(orgOrderLogDB.getHfMemberId());
        param.setOrgTermOrdId(orgOrderLogDB.getTermOrdId());
        param.setRetailId(orgOrderLogDB.getRetailId());
        param.setMiniId(orgOrderLogDB.getMiniId());
        param.setOfficialId(orgOrderLogDB.getOfficialId());
        // 2.退款开始
        // JsonData ---- start -----
        JSONObject json = new JSONObject();
        if (isBlank(param.getTermOrdId())) {
            // 短剧退款订单号
            param.setTermOrdId(StrUtil.getRandomOrderId());
        }
        json.put(StaticVarField.FH_TERM_ORD_ID, toString(param.getTermOrdId()));
        json.put(StaticVarField.FH_MEMBER_ID, toString(orgOrderLogDB.getHfMemberId()));
        json.put(StaticVarField.FH_ORD_ID, orgOrderLogDB.getOrdId());
        json.put(StaticVarField.FH_ORG_TERM_ORD_ID, toString(param.getOrgTermOrdId()));
        json.put(StaticVarField.FH_ORD_AMT, toMoney(param.getOrdAmt()));
        json.put(StaticVarField.FH_API_VERSION, StaticVar.API_VERSION3_2);
        json.put(StaticVarField.FH_MER_PRIV, PayUtil.getMerPriv());
        json.put(StaticVarField.FH_TRANS_DATE, orgOrderLogDB.getTransDate());
        // JsonData ---- end -----

        // 将商家的回调地址缓存至redis
        PayUtil.cacheNotifyUrl(toString(param.getMiniId()), param.getTermOrdId(), param.getNotifyUrl());

        // 组装订单信息
        param.setOrderType(UpayOrderTypeEnum.REF.idnex);// 退款
        param.setState(UpayOrderStatusEnum.WAITING.index);// 处理中
        param.setPreOrderId(orgOrderLogDB.getId());// 关联的父级订单主键id
        param.setQrCodeId(orgOrderLogDB.getQrCodeId());// 二维码id
        param.setPayChannelType(orgOrderLogDB.getPayChannelType());// 1支付宝,2微信
        param.setPayType(orgOrderLogDB.getPayType());
        param.setPayTypeDetail(orgOrderLogDB.getPayTypeDetail());
        param.setHfMemberId(orgOrderLogDB.getHfMemberId());
        param.setUpdateTime(nowTime);

        if (orgOrderLogDB.getPayChannel() != null && orgOrderLogDB.getPayChannel() == UpayChannelEnum.ORIG.index) {
            // 走原生支付通道(仅支持微信)
            Map<String, Object> data = XmlUtil.parseXmlToMap(this.doRefundWx(fillRefundParams(param)));
            if (data == null || data.size() == 0) {
                throw new MyException("原生微信退款发起失败");
            }
            resultData.setResults(data);

            // 组装订单信息
            param.setOutTransId(toString(data.get(StaticVarField.F_TRANSACTION_ID)));// 微信订单号
            param.setTransTime(nowTime);// 交易时间
            param.setTransDate(DateUtil.format06Int(nowTime));
        } else {
            String resultStr = PostUtil.sendPost(StaticVar.E1106, json.toString());

            JSONObject resultJson = JSONObject.parseObject(resultStr);
            Integer respCode = resultJson.getInteger(StaticVarField.FH_RESP_CODE);
            // 判断成功-处理中
            if (respCode == null) {
                log.error("退款发起失败:{}", resultStr);
                throw new MyException("退款发起失败:" + resultStr);
            } else if (respCode == 320) {
                log.error("退款发起失败:{}", resultStr);
                resultJson.put("respDesc", StaticMsg.MSG_PAY_ERROR_144);
                throw new MyException(StaticMsg.MSG_PAY_ERROR_144);
            } else {
                log.error("退款发起失败:{}", resultStr);
                throw new MyException(resultJson.getString("respDesc"));
            }
        }
        // 保存订单信息
        if (upayOrderLogMapper.insertSelective(param) == 0) {
            log.error(StaticMsg.MSG_PAY_ERROR_131);
            throw new MyException(StaticMsg.MSG_PAY_ERROR_131);
        }
        if (param.getRechargeOrderId() != null && param.getRechargeOrderId() > 0 && upayOrderLogMapper.updateRechargePayLogId(param) == 0) {
            resultData.setCode(500);
            throw new MyException(StaticMsg.MSG_PAY_ERROR_131);
        }
    }

    private static final String[] SP = new String[]{"TLSv1.2"};

    /**
     * 执行申请退款请求(微信)
     *
     * @param params 请求参数
     * @return 返回结果
     * @throws IOException e
     */
    public String doRefundWx(Map<String, String> params) throws IOException {
        String requestXml = XmlUtil.parseMapToXml(params);
        StringBuilder result = new StringBuilder();
        FileInputStream instream = null;
        CloseableHttpClient httpclient = null;
        CloseableHttpResponse response = null;
        try {
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            // 放退款证书的路径
            instream = new FileInputStream(StaticVar.WECHAT_PAY_CRET_P12);
            keyStore.load(instream, params.get(StaticVarField.F_MCH_ID).toCharArray());
            SSLContext sc = SSLContexts.custom().loadKeyMaterial(keyStore, params.get(StaticVarField.F_MCH_ID).toCharArray()).build();
            SSLConnectionSocketFactory ssl = new SSLConnectionSocketFactory(sc, SP, null, SSLConnectionSocketFactory.getDefaultHostnameVerifier());
            httpclient = HttpClients.custom().setSSLSocketFactory(ssl).build();
            // 退款接口
            HttpPost post = new HttpPost(StaticVar.WECHAT_REFUND_URL);
            StringEntity reqEntity = new StringEntity(requestXml);
            // 设置类型
            reqEntity.setContentType("application/x-www-form-urlencoded");
            post.setEntity(reqEntity);
            response = httpclient.execute(post);
            HttpEntity entity = response.getEntity();
            if (entity == null) {
                log.error("DoRefund failed, cause HttpEntity is null");
                return null;
            }
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent(), StandardCharsets.UTF_8));
            String text;
            while ((text = bufferedReader.readLine()) != null) {
                result.append(text);
            }
            EntityUtils.consume(entity);
            return result.toString();
        } catch (Exception e) {
            log.error("DoRefund error", e);
        } finally {
            if (response != null) {
                response.close();
            }
            if (httpclient != null) {
                httpclient.close();
            }
            if (instream != null) {
                instream.close();
            }
        }
        return null;
    }

    /**
     * 将退款参数转为Map
     *
     * @param param 订单退款参数
     * @return 参数集合
     */
    private TreeMap<String, String> fillRefundParams(UpayOrderLog param) {
        FastMiniVO miniVO = miniService.queryInfoByRedis(param.getMiniId());
        TreeMap<String, String> map = new TreeMap<>();
        // 短剧支付appid
        map.put(StaticVarField.F_APPID, miniVO.getAppId());
        // 短剧服务商id
        // map.put(StaticVarField.F_MCH_ID, StaticVar.WECHAT_MCH_ID);
        // 机构子商户id
        map.put(StaticVarField.F_MCH_ID, param.getHfMemberId());
        map.put(StaticVarField.F_NONCE_STR, param.getNonceStr());
        if (notBlank(param.getOutTransId())) {
            map.put(StaticVarField.F_TRANSACTION_ID, param.getOutTransId());
        }
        // 原始订单号
        map.put(StaticVarField.F_OUT_TRADE_NO, toString(param.getOrgTermOrdId()));
        // 退款订单号
        map.put(StaticVarField.F_OUT_REFUND_NO, toString(param.getTermOrdId()));
        map.put(StaticVarField.F_TOTAL_FEE, toWxPayMoney(param.getOrdAmt()));
        map.put(StaticVarField.F_REFUND_FEE, toWxPayMoney(param.getOrdAmt()));
        // map.put(StaticVarField.F_OP_USER_ID, StaticVar.WECHAT_MCH_ID);
        map.put(StaticVarField.F_REFUND_FEE_TYPE, StaticVar.CNY);
        if (notBlank(param.getGoodsDesc())) {
            map.put(StaticVarField.F_REFUND_DESC, UnicodeUtils.toEncodedUnicode(param.getGoodsDesc()));
        }
        if (notBlank(param.getRefundAccount())) {
            map.put(StaticVarField.F_REFUND_ACCOUNT, param.getRefundAccount());
        }
        map.put(StaticVarField.F_NOTIFY_URL, StaticVar.WX_NOTIFY_REF_URL);
        map.put(StaticVarField.F_SIGN_TYPE, StaticVar.MD5);
        map.put(StaticVarField.F_SIGN, SignUtil.createSignWx(map));
        return map;
    }


}
