/*
 * Powered By fast.up
 */
package com.fast.service.upay;

import com.fast.constant.StaticVar;
import com.fast.mapper.upay.UpayStoreAccountFunMapper;
import com.fast.po.upay.UpayStoreAccountFun;
import com.fast.service.base.BaseService;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class UpayStoreAccountFunService extends BaseService {

    @Autowired
    private UpayStoreAccountFunMapper accountFunMapper;

    /**
     * 通过id查询单个对象
     */
    public UpayStoreAccountFun queryById(UpayStoreAccountFun item) {
        return accountFunMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public UpayStoreAccountFun queryById(Integer id) {
        UpayStoreAccountFun itemParam = new UpayStoreAccountFun();
        itemParam.setId(id);
        return accountFunMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public UpayStoreAccountFun queryOne(UpayStoreAccountFun item) {
        return accountFunMapper.queryOne(item);
    }

    /**
     * 通过sid查询单个对象
     */
    public UpayStoreAccountFun queryOne(Integer sid) {
        UpayStoreAccountFun entity = new UpayStoreAccountFun();
        entity.setMiniId(sid);
        return accountFunMapper.queryOne(entity);
    }

    /**
     * 查询全部
     */
    public List<UpayStoreAccountFun> queryList(UpayStoreAccountFun item) {
        return accountFunMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(UpayStoreAccountFun item, PageVO pageVO) {
        startPage(pageVO);
        List<UpayStoreAccountFun> list = accountFunMapper.queryList(item);
        for (UpayStoreAccountFun cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(UpayStoreAccountFun item) {
        return accountFunMapper.queryCount(item);
    }

    /**
     * 查询总数
     */
    public void changeSubMchId(Integer miniId, Integer mchId) {
        accountFunMapper.updateMchIdByMiniId(miniId, mchId);
        RedisUtil.del(StaticVar.PAY_CHANNEL_MINI_ID + miniId);
    }
}
