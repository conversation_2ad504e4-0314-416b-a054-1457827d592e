package com.fast.service.upay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticVar;
import com.fast.constant.StaticVarField;
import com.fast.enums.upay.*;
import com.fast.framework.exception.MyException;
import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.mapper.upay.UpayOrderLogMapper;
import com.fast.mapper.upay.UpayOrderNotifyMapper;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.upay.UpayOrderLog;
import com.fast.po.upay.UpayOrderNotify;
import com.fast.service.base.BaseService;
import com.fast.service.common.FastActionLogService;
import com.fast.service.member.FastMemberOrderRechargeService;
import com.fast.utils.*;
import com.fast.utils.encryption.SignUtil;
import com.fast.utils.http.HttpUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.upay.PayUtil;
import com.fast.utils.upay.PostUtil;
import com.fast.utils.xml.XmlUtil;
import com.fast.vo.ResultVO;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.*;

/**
 * UpayPayService
 *
 * <AUTHOR>
 */
@Service
public class UpayPayService extends BaseService {

    private static final int SLEEP_TIME = 1000;
    // 支付码长度
    private static final int TIME_EXPIRED_MIN = 30;

    @Autowired
    private UpayOrderLogMapper upayOrderLogMapper;
    @Autowired
    private UpayOrderNotifyMapper upayOrderNotifyMapper;
    @Autowired
    private FastMemberOrderRechargeMapper rechargeMapper;
    @Lazy
    @Autowired
    private FastMemberOrderRechargeService rechargeService;
    @Lazy
    @Autowired
    private UpayCloseTransService upayCloseTransService;
    @Lazy
    @Autowired
    private FastActionLogService logService;

    /**
     * 查询单个订单
     *
     * @param upayOrderLog
     * @return
     */
    public UpayOrderLog queryOne(UpayOrderLog upayOrderLog) {
        return upayOrderLogMapper.queryOne(upayOrderLog);
    }

    /**
     * 查询单个订单
     *
     * @param termOrdId
     * @return
     */
    public UpayOrderLog queryOne(Long termOrdId) {
        if (isBlank(termOrdId)) {
            return null;
        }
        UpayOrderLog upayOrderLog = new UpayOrderLog();
        upayOrderLog.setTermOrdId(termOrdId);
        return upayOrderLogMapper.queryOne(upayOrderLog);
    }

    /**
     * 交易下单-预支付
     *
     * @param resultData
     * @param params
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void prepay(HttpServletRequest request, ResultVO<JSONObject> resultData, UpayOrderLog params, String key) throws Exception {
        Date nowTime = DateUtil.getNowDate();
        // JsonData ---- start -----
        JSONObject json = new JSONObject();

        if (notBlank(key)) {
            String prePayInfo = RedisUtil.get(key);
            if (notBlank(prePayInfo)) {
                resultData.setResults(JsonUtil.toJSONObject(prePayInfo));
                return;
            } else {
                // 关闭旧的订单
                UpayOrderLog close = new UpayOrderLog();
                close.setRetailId(params.getRetailId());
                close.setMiniId(params.getMiniId());
                close.setId(params.getPayLogId());
                upayCloseTransService.closeTrans(resultData, close);
                // 生成新订单号
                params.setTermOrdId(StrUtil.getRandomOrderId());
            }
        } else {
            // 生成新订单号
            params.setTermOrdId(StrUtil.getRandomOrderId());
        }
        if (params.getPayPlatform() != null && params.getPayPlatform() == StaticVar.PAY_WX_MINI) {
            json.put(StaticVarField.FH_APP_ID, params.getAppId());
        }
        json.put(StaticVarField.FH_CLIENT_IP, ComUtil.getRemoteIP(request));
        json.put(StaticVarField.FH_TERM_ORD_ID, toString(params.getTermOrdId()));
        json.put(StaticVarField.FH_MEMBER_ID, toString(params.getHfMemberId()));
        json.put(StaticVarField.FH_ORD_AMT, toMoney(params.getOrdAmt()));
        json.put(StaticVarField.FH_GOODS_DESC, URLEncoder.encode(params.getGoodsDesc(), StandardCharsets.UTF_8));
        json.put(StaticVarField.FH_PAY_CHANNEL_TYPE, params.getPayChannelTypeStr());
        if (params.getPayChannelType() == UpayChannelTypeEnum.ALI.index) {
            json.put(StaticVarField.FH_BUYER_ID, toString(params.getBuyerId()));
        } else {
            json.put(StaticVarField.FH_OPEN_ID, params.getOpenId());
        }
        json.put(StaticVarField.FH_API_VERSION, StaticVar.API_VERSION3_2);
        json.put(StaticVarField.FH_MER_PRIV, PayUtil.getMerPriv(params.getAttach()));
        // 值为1时，该笔交易为延迟交易。
        if (params.getIsDelayAcct() != null && params.getIsDelayAcct() == 1) {
            json.put(StaticVarField.FH_IS_DELAY_ACCT, params.getIsDelayAcct());
            params.setIsConfirm(UpayConfirmEnum.WAIT.index);
        }
        // 订单过期时间-30分钟以后不能支付
        Date timeExpire = DateUtil.addMinutes(nowTime, TIME_EXPIRED_MIN);
        json.put(StaticVarField.FH_TIME_EXPIRE, DateUtil.format11(timeExpire));
        params.setTimeExpire(timeExpire);
        // 商户在微信入驻的渠道号
        json.put(StaticVarField.FH_PAY_CHANNEL_ID, StaticVar.PAY_CHANNEL_ID_UP);
        // 线上小程序
        json.put(StaticVarField.FH_PAY_SCENE, "01");
        // JsonData ---- end -----

        String result = "";

        if (upayOrderLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            resultData.setCode(500);
            resultData.setMessage(StaticMsg.MSG_PAY_ERROR_131);
            log.error(StaticMsg.MSG_PAY_ERROR_131);
            return;
        }
        if (biggerZero(params.getRechargeOrderId()) && upayOrderLogMapper.updateRechargePayLogId(params) == 0) {
            transactionRollBack();
            resultData.setCode(500);
            resultData.setMessage(StaticMsg.MSG_PAY_ERROR_131);
            log.error(StaticMsg.MSG_PAY_ERROR_131);
            return;
        }

        if (isBlank(key)) {
            key = StaticVar.U_PRE_PAY + params.getId();
        }
        RedisUtil.set(key, result, 120);

        // 将商家的回调地址缓存至redis
        PayUtil.cacheNotifyUrl(toString(params.getMiniId()), params.getTermOrdId(), params.getNotifyUrl());
    }

    /**
     * 回调支付结果给商家
     *
     * @param notifyUrl
     * @param resultMap
     * @return
     */
    public boolean sendNotify(String notifyUrl, Map<String, Object> resultMap, String sid, Long outTradeNo) {
        resultMap.put(StaticVarField.F_MINI_ID, sid);
        resultMap.put(StaticVarField.F_SIGN, SignUtil.createSignWx(resultMap));
        // 回调支付结果给商家
        int notifyCount = 0;
        do {
            Map<String, Object> result = null;
            try {
                Thread.sleep(5 * notifyCount * 1000L + 1);

                result = XmlUtil.parseXmlToMap(PostUtil.post(notifyUrl, XmlUtil.parseMapToXml(resultMap)));
                if (CollUtil.hasContent(result) && StaticVar.OK.equalsIgnoreCase(toString(result.get(StaticVarField.F_RETURN_MSG)))) {
                    PayUtil.cleanNotifyUrl(sid, outTradeNo);
                    return true;
                }
                log.info("url:{};param:{};result:{}", notifyUrl, resultMap, result);
                resultMap.put(StaticVarField.F_RESULT, JsonUtil.toString(result));
            } catch (Exception e) {
                log.error(StaticMsg.MSG_PAY_ERROR_142, result);
            }
            notifyCount++;
        } while (notifyCount <= StaticVar.NOTIFY_MAX_COUNT);
        log.error(StaticMsg.MSG_PAY_ERROR_140, notifyUrl, resultMap);
        return false;
    }

    /**
     * 回调支付结果给商家
     *
     * @param notifyUrl
     * @param orderLog
     */
    public void sendNotify(String notifyUrl, UpayOrderLog orderLog, TreeMap<String, Object> resultMap) {
        TreeMap<String, Object> params;
        if (resultMap == null) {
            params = fillResultParams(orderLog);
        } else {
            params = resultMap;
        }
        boolean result = sendNotify(notifyUrl, params, orderLog.getMiniId().toString(), orderLog.getTermOrdId());

        int state = 1;
        if (result) {
            state = 2;
        }
        UpayOrderNotify query = new UpayOrderNotify();
        query.setOrderId(orderLog.getId());
        UpayOrderNotify orderNotify = upayOrderNotifyMapper.queryOne(query);
        if (orderNotify == null) {
            orderNotify = new UpayOrderNotify();
            orderNotify.setMiniId(orderLog.getMiniId());
            orderNotify.setOrderId(orderLog.getId());
            orderNotify.setState(state);
            orderNotify.setNotifyCount(1);
            orderNotify.setVersion(1);
            orderNotify.setTermOrdId(orderLog.getTermOrdId());
            orderNotify.setNotifyUrl(notifyUrl);
            if (!result) {
                orderNotify.setNotifyError(toString(params.get(StaticVarField.F_RESULT)));
                params.remove(StaticVarField.F_RESULT);
            }
            orderNotify.setNotifyData(JsonUtil.toString(params));
            orderNotify.setCreateTime(DateUtil.getNowDate());
            orderNotify.setUpdateTime(orderNotify.getCreateTime());

            upayOrderNotifyMapper.insertSelective(orderNotify);
        } else {
            if (!result) {
                orderNotify.setNotifyError(toString(params.get(StaticVarField.F_RESULT)));
                params.remove(StaticVarField.F_RESULT);
            }
            orderNotify.setState(state);
            orderNotify.setUpdateTime(DateUtil.getNowDate());
            upayOrderNotifyMapper.updateVersion(orderNotify);
        }
    }

    /**
     * 组装参数-支付结果(数据结构等同于微信支付的结果)
     *
     * @param orderLog
     * @return
     */
    public TreeMap<String, Object> fillResultParams(UpayOrderLog orderLog) {
        // 生成签名的map
        TreeMap<String, Object> resultMap = new TreeMap<>();
        resultMap.put(StaticVarField.F_MINI_ID, toString(orderLog.getMiniId()));
        resultMap.put(StaticVarField.F_SUB_MCH_ID, toString(orderLog.getHfMemberId()));
        if (orderLog.getOrderType() == UpayOrderTypeEnum.REF.idnex) {
            // 原始订单号
            resultMap.put(StaticVarField.F_OUT_TRADE_NO, toString(orderLog.getOrgTermOrdId()));
            // 微信原始订单id
            resultMap.put(StaticVarField.F_TRANSACTION_ID, toString(orderLog.getOutTransId()));

            // 退款订单号
            resultMap.put(StaticVarField.F_OUT_REFUND_NO, toString(orderLog.getTermOrdId()));
            // 退款订单id
            if (notBlank(orderLog.getOutTransId())) {
                resultMap.put(StaticVarField.F_REFUND_ID, toString(orderLog.getOutTransId()));
            } else if (notBlank(orderLog.getOrdId())) {
                resultMap.put(StaticVarField.F_REFUND_ID, toString(orderLog.getOrdId()));
            }

            resultMap.put(StaticVarField.F_REFUND_STATUS, StaticVar.SUCCESS);
            resultMap.put(StaticVarField.F_SUCCESS_TIME, DateUtil.format07(orderLog.getUpdateTime()));
        } else {
            // 原始订单号
            resultMap.put(StaticVarField.F_OUT_TRADE_NO, toString(orderLog.getTermOrdId()));
            // 原始订单id
            resultMap.put(StaticVarField.F_TRANSACTION_ID, toString(orderLog.getOutTransId()));
        }
        resultMap.put(StaticVarField.F_TOTAL_FEE, toString(DoubleUtil.mulB(BigDecimalVar.BD_100, orderLog.getOrdAmt()).intValue()));
        resultMap.put(StaticVarField.F_RETURN_CODE, StaticVar.SUCCESS);
        resultMap.put(StaticVarField.F_RESULT_CODE, StaticVar.SUCCESS);
        resultMap.put(StaticVarField.F_TRADE_STATE, StaticVar.SUCCESS);
        resultMap.put(StaticVarField.F_ATTACH, toString(orderLog.getAttach()));
        resultMap.put(StaticVarField.F_TIME_END, toString(orderLog.getEndTime()));

        if (orderLog.getState() != null) {
            if (orderLog.getState() == 1) {
                resultMap.put(StaticVarField.F_RESULT_CODE, StaticVar.INIT);
                resultMap.put(StaticVarField.F_TRADE_STATE, StaticVar.INIT);
            } else if (orderLog.getState() > 2) {
                resultMap.put(StaticVarField.F_RESULT_CODE, StaticVar.FAIL);
                resultMap.put(StaticVarField.F_TRADE_STATE, StaticVar.FAIL);
            }
        }

        resultMap.put(StaticVarField.F_SIGN, SignUtil.createSignWx(resultMap));

        return resultMap;
    }

    /**
     * 单个订单查询
     *
     * @param entity
     */
    @Transactional(rollbackFor = Exception.class)
    public UpayOrderLog queryOrder(UpayOrderLog entity) {
        UpayOrderLog queryParam = new UpayOrderLog();
        ResultVO<UpayOrderLog> vo = queryPre(entity, queryParam);
        if (vo == null) {
            return null;
        }
        UpayOrderLog update = vo.getResults();
        if (vo.isSuccess()) {
            return update;
        }
        try {
            Thread.sleep(SLEEP_TIME);
        } catch (InterruptedException e) {
            MyException.print(log, e);
        }

        return update;
    }

    /**
     * 查询订单
     *
     * @param entity
     * @param query
     * @return
     */
    public ResultVO<UpayOrderLog> queryPre(UpayOrderLog entity, UpayOrderLog query) {
        query.setTermOrdId(entity.getTermOrdId());
        query.setMiniId(entity.getMiniId());
        query.setHfMemberId(entity.getHfMemberId());
        query.setId(entity.getPayLogId());
        query.setOpenId(entity.getOpenId());

        UpayOrderLog update = upayOrderLogMapper.queryOne(query);
        if (update == null) {
            return null;
        }
        // 支付(退款)/失败(成功)/关单成功->直接返回
        if (update.getState() == UpayOrderStatusEnum.SUCCESS.index
                || update.getState() == UpayOrderStatusEnum.FAIL.index || update.getState() == UpayOrderStatusEnum.CLOSE.index) {
            update.setPayTimeStr(DateUtil.format07(update.getEndTime()));
            return ResultVO.success(update);
        }
        return ResultVO.error(update);
    }

    /**
     * 自动对账-获取数据
     *
     * @param orderType
     * @return
     */
    public List<UpayOrderLog> autoVerifyGetList(int orderType) {
        Date now = DateUtil.getNowDate();
        UpayOrderLog query = new UpayOrderLog();
        query.setOrderType(orderType);// 消费or退款
        query.setState(UpayOrderStatusEnum.WAITING.index);// 处理中
        query.setTransTime(DateUtil.addHours(now, -1));// 交易时间
        query.setTransTime2(DateUtil.addMinutes(now, -2));// 交易时间

        query.setTransDate(DateUtil.format06Int(query.getTransTime()));// 交易时间
        query.setTransDate2(DateUtil.format06Int(query.getTransTime2()));// 交易时间

        return upayOrderLogMapper.queryListSlave(query);
    }

    /**
     * 自动取消-取消
     *
     * @param orderType
     * @return
     */
    public List<UpayOrderLog> autoCancelGetList(int orderType) {
        Date now = DateUtil.getNowDate();
        UpayOrderLog query = new UpayOrderLog();
        query.setOrderType(orderType);// 消费or退款
        query.setState(UpayOrderStatusEnum.WAITING.index);// 处理中
        query.setTransTime(DateUtil.addHours(now, -2));// 交易时间
        query.setTransTime2(DateUtil.addMinutes(now, -10));// 交易时间

        query.setTransDate(DateUtil.format06Int(query.getTransTime()));// 交易时间
        query.setTransDate2(DateUtil.format06Int(query.getTransTime2()));// 交易时间

        return upayOrderLogMapper.queryListSlave(query);
    }

    /**
     * 自动对账
     *
     * @throws ParseException
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoVerify(UpayOrderLog entity) {
        try {
            this.queryOrder(entity);
        } catch (Exception e) {
            transactionRollBack();
            MyException.print(log, e);
        }
    }

    /**
     * 自动取消
     *
     * @throws ParseException
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoCancel(UpayOrderLog params) {
        try {
            ResultVO<?> resultData = new ResultVO<>();
            // 关闭旧的订单
            UpayOrderLog close = new UpayOrderLog();
            close.setRetailId(params.getRetailId());
            close.setMiniId(params.getMiniId());
            close.setId(params.getId());
            upayCloseTransService.closeTrans(resultData, close);
            if (resultData.isSuccess()) {
                FastMemberOrderRechargePO po = new FastMemberOrderRechargePO();
                po.setPayLogId(params.getId());
                po.setUpdateTime(DateUtil.getNowDate());
                po.setPayDate(params.getTransDate());
                po.setState(2);
                rechargeMapper.updateClose(po);
            }
        } catch (Exception e) {
            transactionRollBack();
            MyException.print(log, e);
        }
    }

    /**
     * 自动取消
     *
     * @throws ParseException
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoCancel(FastMemberOrderRechargePO params) {
        try {
            ResultVO<?> resultData = new ResultVO<>();
            // 关闭旧的订单
            UpayOrderLog close = new UpayOrderLog();
            close.setRetailId(params.getRetailId());
            close.setMiniId(params.getMiniId());
            close.setId(params.getPayLogId());
            if (params.getPayLogId() > 0) {
                upayCloseTransService.closeTrans(resultData, close);
                if (resultData.isSuccess()) {
                    closeOrder(params, close);
                }
            } else {
                closeOrder(params, close);
            }
        } catch (Exception e) {
            transactionRollBack();
            MyException.print(log, e);
        }
    }

    /**
     * 自动取消-订单
     *
     * @throws ParseException
     */
    private void closeOrder(FastMemberOrderRechargePO params, UpayOrderLog close) {
        FastMemberOrderRechargePO recharge = new FastMemberOrderRechargePO();
        recharge.setPayLogId(close.getId());
        recharge.setId(params.getId());
        recharge.setUpdateTime(DateUtil.getNowDate());
        recharge.setPayDate(DateUtil.format06Int(params.getCreateTime()));
        recharge.setState(2);
        rechargeMapper.updateClose(recharge);
    }

    /**
     * 自动回调-获取数据
     *
     * @param page
     * @return
     */
    public List<UpayOrderNotify> autoNotifyGetList(int page) {
        Date now = DateUtil.getNowDate();
        UpayOrderNotify query = new UpayOrderNotify();
        query.setNotifyCount(255);
        query.setState(UpayOrderStatusEnum.WAITING.index);// 处理中
        query.setTransTime(DateUtil.addHours(now, -24));// 交易时间
        query.setTransTime2(DateUtil.addMinutes(now, -2));// 交易时间
        // query.setPage(page);
        // query.setLimit(StaticVar.LIMIT_2K);

        return upayOrderNotifyMapper.queryList(query);
    }

    /**
     * 自动回调
     *
     * @throws ParseException
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void autoNotify(UpayOrderNotify entity) {
        try {
            UpayOrderLog query = new UpayOrderLog();
            query.setId(entity.getOrderId());
            UpayOrderLog orderLog = upayOrderLogMapper.queryById(query);
            TreeMap<String, Object> resultMap = new TreeMap<>(JsonUtil.jsonToMap(entity.getNotifyData()));
            sendNotify(entity.getNotifyUrl(), orderLog, resultMap);
        } catch (Exception e) {
            transactionRollBack();
            MyException.print(log, e);
        }
    }

    public void aliPayCallback(JSONObject paymentInfo) {
        log.info("进入支付宝callback:{}", paymentInfo.toJSONString());
        logService.log("alipay_callback", "通用支付回调参数" + paymentInfo.toJSONString());
        String tradeStatus = paymentInfo.getString("trade_status");
        if (!"TRADE_SUCCESS".equalsIgnoreCase(tradeStatus)) {
            log.info("支付宝支付回调,交易未成功,不处理");
            return;
        }

        Long termOrdId = toLong(paymentInfo.getString("out_trade_no"));// 自己平台的订单id
        UpayOrderLog upayOrderLog = new UpayOrderLog();
        upayOrderLog.setTermOrdId(termOrdId);// 商户订单号
        UpayOrderLog update = upayOrderLogMapper.queryOne(upayOrderLog);
        if (Objects.isNull(update)) {
            log.error("支付宝回调订单号不存在");
            throw new MyException("回调失败");
        }

        logService.log("alipay_callback", "update:" + JSONObject.toJSONString(update));
        // 处理成功-直接返回
        if (update.getState() == UpayOrderStatusEnum.SUCCESS.index) {
            log.info("支付宝回调订单已经交易成功直接返回termOrdId:{}", termOrdId);
            return;
        }
        Date nowTime = DateUtil.getNowDate();
        // 如果支付成功,则更新支付状态
        Date payTime = DateUtil.format07(paymentInfo.getString("gmt_payment"));
        update.setOutTransId(paymentInfo.getString("trade_no"));// 微信支付宝的订单号
        update.setEndTime(payTime);// 用户支付成功/支付取消时间戳，单位为毫秒
        update.setUpdateTime(nowTime);
        // 支付宝支付
        update.setPayType(UpayTypeEnum.ALI.index);
        update.setState(UpayOrderStatusEnum.SUCCESS.index);// 成功
        update.setPayForm(UpayFormEnum.ALI_APP_MIN_ONLINE.index);
        // 数据写入到数据库
        logService.log("alipay_callback", "UpayOrderLog支付成功进行更新数据:" + JSONObject.toJSONString(update));
        if (upayOrderLogMapper.updateWithVersion(update) == 0) {
            transactionRollBack();
            log.error(StaticMsg.MSG_PAY_ERROR_131);
        }
        // 处理业务数据
        logService.log("alipay_callback", "UpayOrderLogId=" + update.getId() + "支付成功，处理回调逻辑");
        rechargeService.rechargeSuccess(update);
    }

    /**
     * 获取应用级AccessToken.
     *
     * @return 返回应用级的AccessToken
     * @throws Exception 方法执行中抛出的异常
     */
    public static String getAppAT(String clientSecret, String clientId) {
        String key = StaticVar.HUA_WEI_AT_TOKEN + clientId;
        String cache = RedisUtil.get(key);
        if (StrUtil.isNotBlank(cache)) {
            return cache;
        }
        // 获取accessToken
        String grant_type = "client_credentials";
        // String msgBody = MessageFormat.format("grant_type={0}&client_secret={1}&client_id={2}", grant_type,URLEncoder.encode(clientSecret, "UTF-8"), clientId);

        Map<String, String> params = new HashMap<>();
        params.put("grant_type", grant_type);
        params.put("client_secret", clientSecret);
        params.put("client_id", clientId);

        String response = HttpUtil.post(StaticVar.HUA_WEI_TOKEN_URL, params);
        // String response = httpPost(StaticVar.HUA_WEI_TOKEN_URL, "application/x-www-form-urlencoded; charset=UTF-8", msgBody, 5000, 5000, null);
        JSONObject obj = JSONObject.parseObject(response);
        String accessToken = obj.getString("access_token");
        Long expiresIn = obj.getLong("expires_in");
        expiresIn = Math.min(expiresIn, 1800L);
        if (StrUtil.isNotBlank(accessToken)) {
            RedisUtil.set(key, accessToken, expiresIn.intValue());
        }
        return accessToken;
    }


    /**
     * oppo 支付请求头
     */
    private static Map<String, String> headers() {
        // x-secret-id 开放平台上的 appKey
        String secretId = "95a8e424df3340dbb1b44fc316d4bc33";
        // x-secret-key 开放平台上的 appSecret
        String secretKey = "7a9803dc472444229ff6e58b84f7790b";
        // x-random 随机字符串（32 位以内随机字符串）
        String random = UUID.randomUUID().toString().replace("-", "");
        // x-time 时间戳，单位：13 位 ms,有效期 30s
        String time = String.valueOf(System.currentTimeMillis());

        Map<String, String> headers = new HashMap<>();
        headers.put("x-secret-id", secretId);
        headers.put("x-random", random);
        headers.put("x-time", time);
        headers.put("x-secret-key", secretKey);

        // x-sign 统一认证签名参数
        String sign = getSign(headers);
        headers.put("x-sign", sign);
        return headers;
    }

    private static String getSign(Map<String, String> params) {
        String fullStr = getSortedParamStr(params);
        return DigestUtils.md5Hex(fullStr);
    }

    private static String getSortedParamStr(Map<String, String> params) {
        Set<String> sortedParams = new TreeSet<>(params.keySet());
        StringBuilder strB = new StringBuilder();
        // 排除sign和空值参数
        for (String key : sortedParams) {
            if ("sign".equalsIgnoreCase(key)) {
                continue;
            }
            String value = params.get(key);
            if (StrUtil.isNotEmpty(value)) {
                strB.append(key).append(value);
            }
        }
        return strB.toString();
    }

    public String oppoPayCallBack(Map<String, String> headers, Map<String, Object> params) {
        // 获得签名
        String remoteSign = headers.get("x-sign");
        headers.remove("x-sign");
        // 加入参数
        headers.put("x-secret-key", "7a9803dc472444229ff6e58b84f7790b");
        headers.put("body", JSON.toJSONString(params));
        String sign = getSign(headers);
        // 对比签名
        if (!remoteSign.equals(sign)) {
            log.error("oppo支付回调签名不一致");
            return "fail";
        }

        return "success";
    }
}
