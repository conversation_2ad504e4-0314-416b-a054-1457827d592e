package com.fast.service.upay.google;

import com.alibaba.fastjson.JSON;
import com.fast.constant.StaticStr;
import com.fast.enums.upay.UpayOrderStatusEnum;
import com.fast.enums.upay.google.ConsumptionStateEnum;
import com.fast.enums.upay.google.PurchaseStateEnum;
import com.fast.enums.upay.google.SubscriptionAckStateEnum;
import com.fast.framework.exception.MyException;
import com.fast.po.upay.UpayOrderLog;
import com.fast.service.member.FastMemberOrderRechargeService;
import com.fast.service.upay.UpayOrderLogService;
import com.fast.utils.DateUtil;
import com.fast.utils.GooglePlayUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.upay.google.GooglePayVerifyDTO;
import com.google.api.services.androidpublisher.model.ProductPurchase;
import com.google.api.services.androidpublisher.model.SubscriptionPurchaseV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * Google Pay 支付服务，负责处理与 Google Play 结算库相关的后端操作
 * Created by Song on 2025/05/09.
 */
@Service
@Slf4j
public class GooglePayService {

    @Autowired
    private FastMemberOrderRechargeService fastMemberOrderRechargeService;

    @Autowired
    private UpayOrderLogService upayOrderLogService;

    @Autowired
    private GooglePlayUtil googlePlayUtil;

    /**
     * 验证一次性商品购买，安卓端完成支付后调用此接口验证购买
     */
    @Transactional(rollbackFor = Exception.class)
    public void verifyProductPurchase(GooglePayVerifyDTO dto) {
        log.info("开始验证Google Pay一次性商品购买: {}", JSON.toJSONString(dto));

        if (StrUtil.isBlank(dto.getProductId())) {
            throw new MyException("商品ID不能为空");
        }
        Date nowDate = DateUtil.getNowDate();

        ProductPurchase purchase = googlePlayUtil.getProductPurchase(dto.getProductId(), dto.getPurchaseToken());
        if (purchase == null) {
            throw new MyException("Google Pay 订单查询失败");
        }
        // 购买状态
        int purchaseState = purchase.getPurchaseState();
        // 消耗状态
        int consumptionState = purchase.getConsumptionState();

        if (purchaseState != PurchaseStateEnum.PURCHASED.getCode() || consumptionState != ConsumptionStateEnum.CONSUMED.getCode()) {
            throw new MyException("购买未完成，状态码: " + purchaseState);
        }
        // 处理订单逻辑
        rechargeSuccess(dto, nowDate);
    }

    /**
     * 验证订阅商品购买，安卓端完成订阅支付后调用此接口验证订阅
     */
    @Transactional(rollbackFor = Exception.class)
    public void verifySubscriptionPurchase(GooglePayVerifyDTO dto) {
        log.info("开始验证Google Pay订阅购买: {}", JSON.toJSONString(dto));

        if (StrUtil.isBlank(dto.getSubscriptionId())) {
            throw new MyException("订阅ID不能为空");
        }
        Date nowDate = DateUtil.getNowDate();

        SubscriptionPurchaseV2 purchase = googlePlayUtil.getSubscriptionPurchaseV2(dto.getPurchaseToken());
        if (purchase == null) {
            throw new MyException("Google Pay 订单查询失败");
        }
        // 验证订阅状态
        String acknowledgementState = purchase.getAcknowledgementState();
        if (!StrUtil.equals(acknowledgementState, SubscriptionAckStateEnum.ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED.getCode())) {
            throw new MyException("订阅支付未完成，状态码: " + acknowledgementState);
        }
        // 处理订单逻辑
        rechargeSuccess(dto, nowDate);
    }

    /**
     * 支付成功之后的业务逻辑处理
     */
    private void rechargeSuccess(GooglePayVerifyDTO dto, Date time) {
        UpayOrderLog upayOrderLog = upayOrderLogService.queryByTermOrdId(dto.getTermOrdId());
        if (upayOrderLog == null) {
            log.error("未找到支付单, termOrdId: {}", dto.getTermOrdId());
            throw new MyException("支付单不存在");
        }
        if (upayOrderLog.getState() == UpayOrderStatusEnum.SUCCESS.index) {
            log.info("订单已处理成功，不用重复处理，本次不加K币, termOrdId: {}, id: {}", dto.getTermOrdId(), upayOrderLog.getId());
            throw new MyException(StaticStr.ERROR_OPERATION_REPEAT);
        }
        upayOrderLog.setState(UpayOrderStatusEnum.SUCCESS.index);
        upayOrderLog.setOutTransId(dto.getOrderId());
        upayOrderLog.setPurchaseTime(dto.getPurchaseTime());
        upayOrderLog.setGpToken(dto.getPurchaseToken());
        upayOrderLog.setEndTime(time);
        upayOrderLog.setUpdateTime(time);
        upayOrderLogService.update(upayOrderLog);
        log.info("支付单状态更新完毕，开始处理业务逻辑, termOrdId: {}, id: {}", upayOrderLog.getTermOrdId(), upayOrderLog.getId());

        fastMemberOrderRechargeService.rechargeSuccess(upayOrderLog);
    }

}