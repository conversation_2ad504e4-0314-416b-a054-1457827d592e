/*
 * Powered By fast.up
 */
package com.fast.service.upay;

import com.fast.constant.StaticStr;
import com.fast.mapper.upay.UpayStoreAccountFunAppMapper;
import com.fast.po.upay.UpayStoreAccountFunAppPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class UpayStoreAccountFunAppService extends BaseService {

    @Autowired
    private UpayStoreAccountFunAppMapper upayStoreAccountFunAppMapper;

    /**
     * 通过id查询单个对象
     */
    public UpayStoreAccountFunAppPO queryById(UpayStoreAccountFunAppPO params) {
        return upayStoreAccountFunAppMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public UpayStoreAccountFunAppPO queryById(Integer id) {
        return upayStoreAccountFunAppMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public UpayStoreAccountFunAppPO queryOne(UpayStoreAccountFunAppPO params) {
        return upayStoreAccountFunAppMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<UpayStoreAccountFunAppPO> queryList(UpayStoreAccountFunAppPO params) {
        return upayStoreAccountFunAppMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(UpayStoreAccountFunAppPO params, PageVO pageVO) {
        startPage(pageVO);
        List<UpayStoreAccountFunAppPO> list = upayStoreAccountFunAppMapper.queryList(params);
        for (UpayStoreAccountFunAppPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(UpayStoreAccountFunAppPO params) {
        return upayStoreAccountFunAppMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(UpayStoreAccountFunAppPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (upayStoreAccountFunAppMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<UpayStoreAccountFunAppPO> list) {
        if (upayStoreAccountFunAppMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(UpayStoreAccountFunAppPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (upayStoreAccountFunAppMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
