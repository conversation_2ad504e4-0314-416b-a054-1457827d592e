package com.fast.service.upay;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticVar;
import com.fast.constant.StaticVarField;
import com.fast.enums.upay.UpayChannelEnum;
import com.fast.enums.upay.UpayOrderStatusEnum;
import com.fast.enums.upay.UpayOrderTypeEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.mapper.upay.UpayOrderLogMapper;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.upay.UpayOrderLog;
import com.fast.po.upay.UpayStoreChannel;
import com.fast.service.base.BaseService;
import com.fast.service.mini.FastMiniService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.SignUtil;
import com.fast.utils.upay.PostUtil;
import com.fast.utils.xml.XmlUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * 关单
 *
 * <AUTHOR>
 */
@Service
public class UpayCloseTransService extends BaseService {

    @Autowired
    private UpayOrderLogMapper upayOrderLogMapper;
    @Autowired
    private FastMemberOrderRechargeMapper fastMemberOrderRechargeMapper;
    @Autowired
    private UpayPayService upayPayService;
    @Autowired
    private UpayStoreChannelService channelService;
    @Autowired
    private FastMiniService miniService;

    /**
     * 发起关单
     *
     * @param resultData
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void close(ResultVO<?> resultData, UpayOrderLog param) {
        try {
            if (param.getId() > 0) {
                closeTrans(resultData, param);
            }
            closeRechargeOrder(param.getRechargeOrderId());
        } catch (Exception e) {
            log.error("关单失败:", e);
            throw new MyException("关单失败:" + e.getMessage());
        }
    }

    /**
     * 订单关单
     *
     * @param rechargeOrderId
     */
    @Transactional(rollbackFor = Exception.class)
    public void closeRechargeOrder(Long rechargeOrderId) {
        Date now = DateUtil.getNowDate();
        FastMemberOrderRechargePO update = new FastMemberOrderRechargePO();
        update.setId(rechargeOrderId);
        update.setUpdateTime(now);
        update.setState(2);
        if (fastMemberOrderRechargeMapper.updateById(update) == 0) {
            throw new MyException("订单关单失败:更新失败");
        }
    }

    /**
     * 发起关单
     *
     * @param resultData
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void closeTrans(ResultVO<?> resultData, UpayOrderLog param) {
        if (param == null || param.getId() <= 0) {
            throw new MyException("关单参数不完整");
        }
        if (param.getId() == null && param.getPayLogId() == null) {
            throw new MyException("关单参数不完整");
        }
        // 1.查询原始支付订单
        UpayOrderLog queryParam = new UpayOrderLog();
        queryParam.setRetailId(param.getRetailId());
        queryParam.setMiniId(param.getMiniId());
        queryParam.setId(param.getId() == null ? param.getPayLogId() : param.getId());
        queryParam.setTermOrdId(toLong(param.getOut_trade_no()));
        queryParam.setOrderType(UpayOrderTypeEnum.PAY.idnex);// 消费

        UpayOrderLog update = upayOrderLogMapper.queryOne(queryParam);
        if (update == null || update.getId() == null || update.getId() <= 0) {
            resultData.setMessage(StaticMsg.MSG_PAY_ERROR_118);
            resultData.setCode(501);
            return;
        }
        param.setHfMemberId(update.getHfMemberId());

        if (update.getState() == UpayOrderStatusEnum.CLOSE.index) {
            return;// 订单已关闭
        }

        if (update.getPayChannel() == UpayChannelEnum.ORIG.index) {
            UpayStoreChannel channel = channelService.getChannel(param.getMiniId());
            // 走原生支付通道(仅支持微信)
            this.closeWx(resultData, update, channel);
        } else if (update.getPayChannel() == UpayChannelEnum.HUI_FU.index) {
            // 汇付关单开始
            closeHf(resultData, update);
        } else if (update.getPayChannel() == UpayChannelEnum.KH_MINI.index) {
            // 快手关单开始
            // closeKH(resultData, update);
        }
    }

    /**
     * 汇付关单
     *
     * @param resultData
     * @param update
     */
    private void closeHf(ResultVO<?> resultData, UpayOrderLog update) {
        // JsonData ---- start -----
        JSONObject json = new JSONObject();
        json.put(StaticVarField.FH_MEMBER_ID, toString(update.getHfMemberId()));
        json.put(StaticVarField.FH_TERM_ORD_ID, toString(update.getTermOrdId()));
        json.put(StaticVarField.FH_TRANS_DATE, update.getTransDate());
        json.put(StaticVarField.FH_API_VERSION, StaticVar.API_VERSION3_2);

        String jsonData = json.toString();
        // JsonData ---- end -----

        String resp = PostUtil.sendPost(StaticVar.CLOSE_TRANS, jsonData);

        JSONObject resultJson = JSONObject.parseObject(resp);
        Integer respCode = resultJson.getInteger(StaticVarField.FH_RESP_CODE);
        // 判断成功-处理中
        if (respCode == null) {
            log.error("汇付关单:{}", resp);
            throw new MyException("关单返回信息错误");
        } else if (respCode == 13) {
            // 原订单不存在
            resultData.setCode(501);
            log.error("汇付关单:{}", resp);
            throw new MyException("关单返回信息错误:原订单不存在");
        } else if (respCode == 198) {
            // {"respDesc":"不允许关闭一分钟以内的订单","respCode":"000198"}
            // {"respDesc":"原订单已为终态,请发起查询交易获取","respCode":"000198"}
            // if (resp.contains("原订单已为终态")) {
            //     UpayOrderLog orderLog = upayPayService.queryHF(update);
            //     if (orderLog != null && (orderLog.getState() == UpayOrderStatusEnum.FAIL.index || orderLog.getState() == UpayOrderStatusEnum.CLOSE.index)) {
            //         return;
            //     }
            // }
            resultData.setCode(501);
            log.error("汇付关单:{}", resp);
            throw new MyException("关单返回信息错误:不允许关闭一分钟以内的订单");
        } else {
            resultData.setCode(501);
            log.error("汇付关单:{}", resp);
            throw new MyException("关单返回信息错误:" + resp);
        }
    }

    /**
     * 微信订单关闭
     *
     * @param resultData
     * @param update
     */
    public void closeWx(ResultVO<?> resultData, UpayOrderLog update, UpayStoreChannel channel) {
        update.setWxKey(channel.getWxKey());
        update.setSubMchId(channel.getSubMchId());
        if (update.getPayForm() == 4) {
            update.setSubAppid(StaticVar.WECHAT_PAY_APP_ID);
        } else {
            FastMiniVO miniVO = miniService.queryInfoByRedis(channel.getMiniId());
            update.setSubAppid(miniVO.getAppId());
        }
        // 填充参数
        TreeMap<String, Object> param = fillCloseParams(update);
        // 关闭订单
        String queryRes = PostUtil.post(StaticVar.WECHAT_CLOSE_ORDER_URL, XmlUtil.parseMapToXml(param));
        Map<String, Object> resultMap = XmlUtil.parseXmlToMap(queryRes);
        if (resultMap == null || resultMap.size() == 0) {
            resultData.setCode(500);
            resultData.setMessage("关单返回信息错误");
            throw new MyException("关单返回信息错误");
        }
        log.info("closeWx >> params: {}, res: {}", param, resultMap);
        if (StaticVar.SUCCESS.equals(resultMap.get("result_code"))) {
            update.setState(UpayOrderStatusEnum.CLOSE.index);
            update.setUpdateTime(DateUtil.getNowDate());
            if (upayOrderLogMapper.updateWithVersion(update) == 0) {
                transactionRollBack();
                log.error("微信关单:{}", StaticMsg.MSG_PAY_ERROR_131);
                throw new MyException("关单返回信息错误.");
            }
        } else if ("ORDERPAID".equals(resultMap.get("err_code"))) {
            if (upayOrderLogMapper.updateWithVersion(update) == 0) {
                log.error("微信关单失败:该订单已支付");
                resultData.setCode(500);
                resultData.setMessage("微信关单失败:该订单已支付");
            }
        } else {
            log.error("关单返回信息错误 >> params: {}, res: {}", param, resultMap);
            throw new MyException("关单返回信息错误");
        }
    }

    /**
     * 将参数转为Map
     *
     * @param entity 关闭订单参数
     * @return 参数集合
     */
    private TreeMap<String, Object> fillCloseParams(UpayOrderLog entity) {
        TreeMap<String, Object> param = new TreeMap<>();
        // 短剧支付端公众号appid
        param.put(StaticVarField.F_APPID, entity.getSubAppid());
        // 短剧服务商id
        param.put(StaticVarField.F_MCH_ID, entity.getSubMchId());
        // 机构子商户id
        // param.put(StaticVarField.F_SUB_MCH_ID, entity.getHfMemberId());
        param.put(StaticVarField.F_OUT_TRADE_NO, entity.getTermOrdId());
        param.put(StaticVarField.F_NONCE_STR, StrUtil.getRandomOrderIdStr());
        param.put(StaticVarField.F_SIGN_TYPE, StaticVar.MD5);
        param.put(StaticVarField.F_SIGN, SignUtil.createSignWx(param, entity.getWxKey()));
        return param;
    }

}
