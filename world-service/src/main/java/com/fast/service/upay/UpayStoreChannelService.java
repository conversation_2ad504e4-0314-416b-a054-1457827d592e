/*
 * Powered By fast.up
 */
package com.fast.service.upay;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.upay.UpayChannelEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.upay.UpayStoreChannelMapper;
import com.fast.po.upay.UpayStoreAccountFun;
import com.fast.po.upay.UpayStoreChannel;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class UpayStoreChannelService extends BaseService {

    @Autowired
    private UpayStoreChannelMapper upayStoreChannelMapper;
    @Autowired
    private UpayStoreAccountFunService upayStoreAccountFunService;

    /**
     * 通过id查询单个对象
     */
    public UpayStoreChannel queryById(UpayStoreChannel item) {
        return upayStoreChannelMapper.queryById(item);
    }

    /**
     * 通过sid查询单个对象
     */
    public UpayStoreChannel queryChannel(Integer miniId) {
        UpayStoreChannel entity = new UpayStoreChannel();
        entity.setMiniId(miniId);
        UpayStoreChannel channel = upayStoreChannelMapper.queryOne(entity);
        if (channel == null) {
            channel = new UpayStoreChannel();
            channel.setMiniId(miniId);
            channel.setSysUserId(0);
            channel.setCreateTime(DateUtil.getNowDate());

            insert(channel);

            channel = upayStoreChannelMapper.queryOne(entity);
        }
        return channel;
    }

    /**
     * 通过id查询单个对象
     */
    public UpayStoreChannel queryById(Integer id) {
        UpayStoreChannel itemParam = new UpayStoreChannel();
        itemParam.setId(id);
        return upayStoreChannelMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public UpayStoreChannel queryOne(UpayStoreChannel item) {
        return upayStoreChannelMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<UpayStoreChannel> queryList(UpayStoreChannel item) {
        return upayStoreChannelMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(UpayStoreChannel item, PageVO pageVO) {
        startPage(pageVO);
        List<UpayStoreChannel> list = upayStoreChannelMapper.queryList(item);
        for (UpayStoreChannel cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(UpayStoreChannel item) {
        return upayStoreChannelMapper.queryCount(item);
    }

    /**
     * 获取支付通道信息
     *
     * @param miniId
     * @return
     */
    public UpayStoreChannel getChannel(Integer miniId) {
        if (miniId == null) {
            throw new MyException("miniId不能为空");
        }
        UpayStoreChannel channel;
        String key = StaticVar.PAY_CHANNEL_MINI_ID + miniId;
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            channel = JsonUtil.toJavaObject(value, UpayStoreChannel.class);
        } else {
            channel = this.queryChannel(miniId);
        }
        if (channel == null) {
            throw new MyException(StaticMsg.MSG_PAY_ERROR_130);
        }

        if (channel.getChannel() == UpayChannelEnum.ORIG.index) {
            if (channel.getSubMchId() == null) {
                throw new MyException(StaticMsg.MSG_PAY_ERROR_130);
            }
            // 走原生支付通道(仅支持微信)
            channel.setHfMemberId(channel.getSubMchId().toString());
        } else {
            UpayStoreAccountFun accountFun = upayStoreAccountFunService.queryOne(miniId);
            if (accountFun == null || accountFun.getMemberId() == null) {
                throw new MyException(StaticMsg.MSG_PAY_ERROR_130);
            }
            channel.setHfMemberId(accountFun.getMemberId());
        }

        RedisUtil.set(key, JsonUtil.toString(channel), RedisUtil.TIME_45M);
        return channel;
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(UpayStoreChannel item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (upayStoreChannelMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<UpayStoreChannel> list) {
        if (upayStoreChannelMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(UpayStoreChannel item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (upayStoreChannelMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
