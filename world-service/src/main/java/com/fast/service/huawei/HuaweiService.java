package com.fast.service.huawei;

import com.alibaba.fastjson.JSONObject;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.upay.UpayOrderLog;
import com.fast.service.base.BaseService;
import com.fast.vo.ResultVO;
import com.fast.vo.huawei.HuaweiPayWithPriceParams;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

@Service
public class HuaweiService extends BaseService {
    public ResultVO<JSONObject> huaweiPay(HttpServletRequest request, UpayOrderLog params, String app) {
        JSONObject jsonObject = new JSONObject();


        FastMemberOrderRechargePO fastMemberOrderRechargePO = new FastMemberOrderRechargePO();

        String applicationID = "";
        String publicKey = "";
        String productId = fastMemberOrderRechargePO.getMaterialId();
        String priceType = ""; // 0 : 消耗型商品 1 : 非消耗型商品

        String developerPayload = ""; // 可选参数 商户自定义参数
        String amount = params.getOrdAmt().toString(); // 支付金额
        String country = "CN"; // 国家代码
        String currency = "CNY";
        String productName = "K币";
        String reservedInfor = ""; // json格式 拓展字段可选
        /**
         * ● 0：自有应用，无渠道
         * ● 1：应用市场渠道
         * ● 2：预装渠道
         * ● 3：游戏中心
         */
        String sdkChannel = "0";
        String serviceCatalog = "X5";


        HuaweiPayWithPriceParams huaweiPayWithPriceParams = new HuaweiPayWithPriceParams();
        huaweiPayWithPriceParams.setAmount(amount);
        huaweiPayWithPriceParams.setPriceType(priceType);
        huaweiPayWithPriceParams.setApplicationID(applicationID);
        huaweiPayWithPriceParams.setPublicKey(publicKey);
        huaweiPayWithPriceParams.setProductId(productId);
        huaweiPayWithPriceParams.setDeveloperPayload(developerPayload);
        huaweiPayWithPriceParams.setCountry(country);
        huaweiPayWithPriceParams.setCurrency(currency);
        huaweiPayWithPriceParams.setProductName(productName);
        huaweiPayWithPriceParams.setReservedInfor(reservedInfor);
        huaweiPayWithPriceParams.setSdkChannel(sdkChannel);
        huaweiPayWithPriceParams.setServiceCatalog(serviceCatalog);


        jsonObject.put("purchaseIntentWithPriceReq", huaweiPayWithPriceParams);
        return ResultVO.success(jsonObject);
    }
}
