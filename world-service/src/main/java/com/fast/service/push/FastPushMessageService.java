/*
 * Powered By fast.up
 */
package com.fast.service.push;

import com.fast.constant.StaticStr;
import com.fast.mapper.push.FastPushInteractHisMapper;
import com.fast.mapper.push.FastPushMessageItemMapper;
import com.fast.mapper.push.FastPushMessageMapper;
import com.fast.po.push.FastPushMessageItemPO;
import com.fast.po.push.FastPushMessagePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastPushMessageService extends BaseService {

    @Autowired
    private FastPushMessageMapper fastPushMessageMapper;
    @Autowired
    private FastPushMessageItemMapper fastPushMessageItemMapper;
    @Autowired
    private FastPushInteractHisMapper fastPushInteractHisMapper;


    /**
     * 通过id查询单个对象
     */
    public FastPushMessagePO queryById(FastPushMessagePO item) {
        return fastPushMessageMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastPushMessagePO queryById(Integer id) {
        FastPushMessagePO itemParam = new FastPushMessagePO();
        itemParam.setId(id);
        return fastPushMessageMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastPushMessagePO queryOne(FastPushMessagePO item) {
        return fastPushMessageMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastPushMessagePO> queryList(FastPushMessagePO item) {
        return fastPushMessageMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastPushMessagePO item, PageVO pageVO) {
        if (item.getType() == 6) {
            Date nowDate = DateUtil.getNowDate();
            int count = fastPushMessageMapper.queryCount(item);
            if (count == 0) {
                item.setType(6);
                item.setTaskName("继续观看提醒");
                item.setMessageType(2);
                item.setSequence(2);
                item.setScheduleTime(nowDate);
                item.setCreateTime(nowDate);
                item.setCreatorId(item.getSysUserId());
                fastPushMessageMapper.insertSelective(item);
            }
        }
        startPage(pageVO);
        List<FastPushMessagePO> list = fastPushMessageMapper.queryList(item);
        for (FastPushMessagePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastPushMessagePO item) {
        return fastPushMessageMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastPushMessagePO item) {
        Date nowTime = DateUtil.getNowDate();
        if (item.getId() != null && item.getId() > 0) {
            if (StrUtil.isNotEmpty(item.getScheduleTimeStr())) {
                item.setScheduleTime(DateUtil.format07(item.getScheduleTimeStr()));
            }
            // 编辑
            item.setUpdateTime(nowTime);
            if (fastPushMessageMapper.updateById(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
            // 删除自动回复链接单元
            FastPushMessageItemPO itemParam = new FastPushMessageItemPO();
            itemParam.setMessageId(item.getId());
            List<FastPushMessageItemPO> itemList = fastPushMessageItemMapper.queryList(itemParam);
            for (FastPushMessageItemPO itemObj : itemList) {
                if (itemObj.getAutoItemId() != null && itemObj.getAutoItemId() > 0) {
                    if (fastPushMessageItemMapper.deleteById(itemObj.getAutoItemId()) == 0) {
                        transactionRollBack();
                        return MethodVO.error("删除失败");
                    }
                }
            }
            // 删除关联的链接单元
            if (fastPushMessageItemMapper.deleteByMessageId(item.getId()) == 0) {
                transactionRollBack();
                return MethodVO.error("删除失败");
            }
        } else {
            if (StrUtil.isNotEmpty(item.getScheduleTimeStr())) {
                item.setScheduleTime(DateUtil.format07(item.getScheduleTimeStr()));
            }
            // 新增
            if (item.getScheduleTime() == null) {
                item.setScheduleTime(DateUtil.getNowDate());
            }
            if (StrUtil.isEmpty(item.getKeywords())) {
                item.setKeywords("");
            }
            item.setCreateTime(nowTime);
            item.setUpdatorId(item.getCreatorId());
            item.setUpdateTime(nowTime);
            // 添加消息主体
            if (fastPushMessageMapper.insertSelective(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
        }
        // 添加消息链接单元
        List<FastPushMessageItemPO> messageItemList = item.getItemList();
        for (FastPushMessageItemPO messageItem : messageItemList) {
            messageItem.setMessageId(item.getId());
            messageItem.setOfficialId(item.getOfficialId());
            messageItem.setCreatorId(item.getUpdatorId());
            messageItem.setUpdatorId(item.getUpdatorId());
            messageItem.setCreateTime(nowTime);
            messageItem.setUpdateTime(nowTime);
            if (messageItem.getAutoItem() != null) {
                FastPushMessageItemPO autoItem = messageItem.getAutoItem();
                autoItem.setOfficialId(item.getOfficialId());
                autoItem.setCreatorId(item.getUpdatorId());
                autoItem.setUpdatorId(item.getUpdatorId());
                autoItem.setCreateTime(nowTime);
                autoItem.setUpdateTime(nowTime);
                autoItem.setMessageId(0);
                if (fastPushMessageItemMapper.insertSelective(autoItem) == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
                messageItem.setAutoItemId(autoItem.getId());
            }
            if (fastPushMessageItemMapper.insertSelective(messageItem) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastPushMessagePO> list) {
        if (fastPushMessageMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastPushMessagePO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastPushMessageMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastPushMessagePO item) {
        if (fastPushMessageMapper.delete(item) == 0) {
            transactionRollBack();
            return MethodVO.error("删除失败");
        }
        // 删除自动回复链接单元
        FastPushMessageItemPO itemParam = new FastPushMessageItemPO();
        itemParam.setMessageId(item.getId());
        List<FastPushMessageItemPO> itemList = fastPushMessageItemMapper.queryList(itemParam);
        for (FastPushMessageItemPO itemObj : itemList) {
            if (itemObj.getAutoItemId() != null && itemObj.getAutoItemId() > 0) {
                if (fastPushMessageItemMapper.deleteById(itemObj.getAutoItemId()) == 0) {
                    transactionRollBack();
                    return MethodVO.error("删除失败");
                }
            }
        }
        // 删除关联的链接单元
        if (fastPushMessageItemMapper.deleteByMessageId(item.getId()) == 0) {
            transactionRollBack();
            return MethodVO.error("删除失败");
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> getDetail(FastPushMessagePO param) {
        // 查询消息
        FastPushMessagePO message = fastPushMessageMapper.queryById(param);
        // 查询链接单元
        FastPushMessageItemPO itemParam = new FastPushMessageItemPO();
        itemParam.setMessageId(param.getId());
        List<FastPushMessageItemPO> messageItemList = fastPushMessageItemMapper.queryList(itemParam);
        for (FastPushMessageItemPO item : messageItemList) {
            if (item.getAutoItemId() != null) {
                FastPushMessageItemPO mip = new FastPushMessageItemPO();
                mip.setId(item.getAutoItemId());
                FastPushMessageItemPO itemPO = fastPushMessageItemMapper.queryById(mip);
                item.setAutoItem(itemPO);
            }
        }
        Map<String, Object> results = ResultVO.getMap();
        results.put("message", message);
        results.put("messageItemList", messageItemList);
        return ResultVO.success("ok", results);
    }

    /**
     * 查询消息列表
     *
     * @param param2
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<FastPushMessagePO> getMessageList(FastPushMessagePO param2) {
        // 查询消息
        List<FastPushMessagePO> messageList = fastPushMessageMapper.queryList(param2);
        for (FastPushMessagePO message : messageList) {
            // 查询链接单元
            FastPushMessageItemPO itemParam = new FastPushMessageItemPO();
            itemParam.setMessageId(message.getId());
            List<FastPushMessageItemPO> messageItemList = fastPushMessageItemMapper.queryList(itemParam);
            for (FastPushMessageItemPO item : messageItemList) {
                if (item.getAutoItemId() != null) {
                    FastPushMessageItemPO mip = new FastPushMessageItemPO();
                    mip.setId(item.getAutoItemId());
                    FastPushMessageItemPO itemPO = fastPushMessageItemMapper.queryById(mip);
                    item.setAutoItem(itemPO);
                }
            }
            message.setItemList(messageItemList);
        }
        return messageList;
    }

    // 查询符合条件的单个消息
    public FastPushMessagePO queryOneMessage(FastPushMessagePO param2) {
        // 查询消息
        List<FastPushMessagePO> messageList = fastPushMessageMapper.queryList(param2);
        if (messageList != null && messageList.size() > 0) {
            FastPushMessagePO message = messageList.get(0);
            // 查询链接单元
            FastPushMessageItemPO itemParam = new FastPushMessageItemPO();
            itemParam.setMessageId(message.getId());
            List<FastPushMessageItemPO> messageItemList = fastPushMessageItemMapper.queryList(itemParam);
            for (FastPushMessageItemPO item : messageItemList) {
                if (item.getAutoItemId() != null) {
                    FastPushMessageItemPO mip = new FastPushMessageItemPO();
                    mip.setId(item.getAutoItemId());
                    FastPushMessageItemPO itemPO = fastPushMessageItemMapper.queryById(mip);
                    item.setAutoItem(itemPO);
                }
            }
            message.setItemList(messageItemList);
            return message;
        }
        return null;
    }


}
