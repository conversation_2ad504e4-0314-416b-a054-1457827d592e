/*
 * Powered By fast.up
 */
package com.fast.service.push;

import com.fast.constant.StaticStr;
import com.fast.mapper.push.FastPushMessageItemMapper;
import com.fast.po.push.FastPushMessageItemPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastPushMessageItemService extends BaseService {

    @Autowired
    private FastPushMessageItemMapper fastPushMessageItemMapper;

    /**
     * 通过id查询单个对象
     */
    public FastPushMessageItemPO queryById(FastPushMessageItemPO item) {
        return fastPushMessageItemMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastPushMessageItemPO queryById(Integer id) {
        FastPushMessageItemPO itemParam = new FastPushMessageItemPO();
        itemParam.setId(id);
        return fastPushMessageItemMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastPushMessageItemPO queryOne(FastPushMessageItemPO item) {
        return fastPushMessageItemMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastPushMessageItemPO> queryList(FastPushMessageItemPO item) {
        return fastPushMessageItemMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastPushMessageItemPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastPushMessageItemPO> list = fastPushMessageItemMapper.queryList(item);
        for (FastPushMessageItemPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastPushMessageItemPO item) {
        return fastPushMessageItemMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastPushMessageItemPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastPushMessageItemMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastPushMessageItemPO> list) {
        if (fastPushMessageItemMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastPushMessageItemPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastPushMessageItemMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }


}
