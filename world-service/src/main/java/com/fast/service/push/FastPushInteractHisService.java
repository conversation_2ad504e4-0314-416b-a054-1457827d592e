/*
 * Powered By fast.up
 */
package com.fast.service.push;

import com.fast.constant.StaticStr;
import com.fast.mapper.push.FastPushInteractHisMapper;
import com.fast.po.push.FastPushInteractHisPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastPushInteractHisService extends BaseService {

    @Autowired
    private FastPushInteractHisMapper fastPushInteractHisMapper;

    /**
     * 通过id查询单个对象
     */
    public FastPushInteractHisPO queryById(FastPushInteractHisPO item) {
        return fastPushInteractHisMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastPushInteractHisPO queryById(Integer id) {
        FastPushInteractHisPO itemParam = new FastPushInteractHisPO();
        itemParam.setId(id);
        return fastPushInteractHisMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastPushInteractHisPO queryOne(FastPushInteractHisPO item) {
        return fastPushInteractHisMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastPushInteractHisPO> queryList(FastPushInteractHisPO item) {
        return fastPushInteractHisMapper.queryList(item);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastPushInteractHisPO item) {
        return fastPushInteractHisMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastPushInteractHisPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastPushInteractHisMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastPushInteractHisPO> list) {
        if (fastPushInteractHisMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastPushInteractHisPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastPushInteractHisMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
