/*
 * Powered By fast.up
 */
package com.fast.service.help;

import com.fast.constant.StaticStr;
import com.fast.mapper.help.FastHelpQuestionMapper;
import com.fast.mapper.help.FastHelpQuestionTypeMapper;
import com.fast.po.help.FastHelpQuestionPO;
import com.fast.po.help.FastHelpQuestionTypePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Service
public class FastHelpQuestionService extends BaseService {

    @Autowired
    private FastHelpQuestionMapper fastHelpQuestionMapper;
    @Autowired
    private FastHelpQuestionTypeMapper fastHelpQuestionTypeMapper;

    /**
     * 通过id查询单个对象
     */
    public FastHelpQuestionPO queryById(FastHelpQuestionPO params) {
        return fastHelpQuestionMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastHelpQuestionPO queryById(Integer id) {
        return fastHelpQuestionMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastHelpQuestionPO queryOne(FastHelpQuestionPO params) {
        return fastHelpQuestionMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastHelpQuestionPO> queryList(FastHelpQuestionPO params) {
        return fastHelpQuestionMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastHelpQuestionPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastHelpQuestionPO> list = fastHelpQuestionMapper.queryList(params);
        for (FastHelpQuestionPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastHelpQuestionPO params) {
        return fastHelpQuestionMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastHelpQuestionPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setSequence(fastHelpQuestionMapper.queryMaxSequence());
        if (fastHelpQuestionMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastHelpQuestionPO> list) {
        if (fastHelpQuestionMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastHelpQuestionPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        params.setSequence(null);
        if (fastHelpQuestionMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastHelpQuestionPO params) {
        if (fastHelpQuestionMapper.deleteById(params) == 0) {
            transactionRollBack();
            return MethodVO.error("删除失败");
        }
        return MethodVO.success();
    }

    /**
     * 是否解决
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> clickValid(FastHelpQuestionPO params, SessionVO sessionVO) {
        FastHelpQuestionPO fastHelpQuestion = queryById(params);
        if (fastHelpQuestion == null) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        if (params.getClickType() == 1) {
            fastHelpQuestion.setValidClickNum(fastHelpQuestion.getValidClickNum() + 1);
        } else {
            fastHelpQuestion.setInvalidClickNum(fastHelpQuestion.getInvalidClickNum() + 1);
        }
        fastHelpQuestion.setUpdateTime(DateUtil.getNowDate());
        fastHelpQuestion.setUpdatorId(sessionVO.getUserId());
        update(fastHelpQuestion);
        return ResultVO.success();
    }

    /**
     * C端查询帮助中心问题列表
     */
    public ResultVO<?> queryTreeList() {
        List<FastHelpQuestionTypePO> parentList = fastHelpQuestionTypeMapper.queryList(new FastHelpQuestionTypePO());
        List<FastHelpQuestionPO> list = fastHelpQuestionMapper.queryList(new FastHelpQuestionPO());
        parentList.forEach(parent -> {
            parent.setEncryptionId(encode(parent.getId()));
            list.forEach(cur -> {
                cur.setEncryptionId(encode(cur.getId()));
                // 问题类型ID与问题ID相等
                if (parent.getId().equals(cur.getTypeId())) {
                    if (parent.getQuestionList() == null) {
                        parent.setQuestionList(new ArrayList<>());
                    }
                    parent.getQuestionList().add(cur);
                }
            });
        });
        return ResultVO.success(parentList);
    }

    /**
     * 修改排序
     */
    public MethodVO updateSequence(FastHelpQuestionPO params) {
        FastHelpQuestionPO po = fastHelpQuestionMapper.queryById(params.getId());
        if (po == null) {
            return MethodVO.error(StaticStr.INVALID_PARAM);
        }
        FastHelpQuestionPO query = new FastHelpQuestionPO();
        query.setTypeId(po.getTypeId());
        List<FastHelpQuestionPO> list = fastHelpQuestionMapper.queryList(query);
        // 重新排序
        AtomicInteger index = new AtomicInteger(params.getSequence());
        list.forEach(cur -> {
            cur.setUpdateTime(DateUtil.getNowDate());
            cur.setUpdatorId(params.getUpdatorId());
            if (cur.getId().equals(params.getId())) {
                cur.setSequence(params.getSequence());
            } else if (cur.getSequence() >= params.getSequence()) {
                cur.setSequence(index.incrementAndGet());
            }
        });

        if (fastHelpQuestionMapper.updateSequence(list) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 查询单个详情
     */
    public ResultVO<?> getAnswer(FastHelpQuestionPO params) {
        return ResultVO.success(fastHelpQuestionMapper.queryById(params));
    }
}
