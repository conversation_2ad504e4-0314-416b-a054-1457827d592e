/*
 * Powered By fast.up
 */
package com.fast.service.help;

import com.fast.constant.StaticStr;
import com.fast.mapper.help.FastHelpComplainMapper;
import com.fast.po.help.FastHelpComplainPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastHelpComplainService extends BaseService {

    @Autowired
    private FastHelpComplainMapper fastHelpComplainMapper;

    /**
     * 通过id查询单个对象
     */
    public FastHelpComplainPO queryById(FastHelpComplainPO params) {
        return fastHelpComplainMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastHelpComplainPO queryById(Integer id) {
        return fastHelpComplainMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastHelpComplainPO queryOne(FastHelpComplainPO params) {
        return fastHelpComplainMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastHelpComplainPO> queryList(FastHelpComplainPO params) {
        return fastHelpComplainMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastHelpComplainPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastHelpComplainPO> list = fastHelpComplainMapper.queryList(params);
        for (FastHelpComplainPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastHelpComplainPO params) {
        return fastHelpComplainMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastHelpComplainPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastHelpComplainMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastHelpComplainPO> list) {
        if (fastHelpComplainMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastHelpComplainPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastHelpComplainMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 查询当前用户反馈列表
     */
    public ResultVO<?> queryMiniPageList(FastHelpComplainPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastHelpComplainPO> list = fastHelpComplainMapper.queryMiniList(params);
        list.forEach(cur -> cur.setEncryptionId(encode(cur.getId())));
        return ResultVO.success(getPageListData(list, pageVO));
    }
}
