/*
 * Powered By fast.up
 */
package com.fast.service.help;

import com.fast.constant.StaticStr;
import com.fast.mapper.help.FastHelpQuestionMapper;
import com.fast.mapper.help.FastHelpQuestionTypeMapper;
import com.fast.po.help.FastHelpQuestionPO;
import com.fast.po.help.FastHelpQuestionTypePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Service
public class FastHelpQuestionTypeService extends BaseService {

    @Autowired
    private FastHelpQuestionTypeMapper fastHelpQuestionTypeMapper;
    @Autowired
    private FastHelpQuestionMapper fastHelpQuestionMapper;

    /**
     * 通过id查询单个对象
     */
    public FastHelpQuestionTypePO queryById(FastHelpQuestionTypePO params) {
        return fastHelpQuestionTypeMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastHelpQuestionTypePO queryById(Integer id) {
        return fastHelpQuestionTypeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastHelpQuestionTypePO queryOne(FastHelpQuestionTypePO params) {
        return fastHelpQuestionTypeMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastHelpQuestionTypePO> queryList(FastHelpQuestionTypePO params) {
        List<FastHelpQuestionTypePO> list = fastHelpQuestionTypeMapper.queryList(params);
        list.forEach(cur -> cur.setEncryptionId(encode(cur.getId())));
        return list;
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastHelpQuestionTypePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastHelpQuestionTypePO> list = fastHelpQuestionTypeMapper.queryList(params);
        for (FastHelpQuestionTypePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastHelpQuestionTypePO params) {
        return fastHelpQuestionTypeMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastHelpQuestionTypePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setSequence(fastHelpQuestionTypeMapper.queryMaxSequence());
        if (fastHelpQuestionTypeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastHelpQuestionTypePO> list) {
        if (fastHelpQuestionTypeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastHelpQuestionTypePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        params.setSequence(null);
        if (fastHelpQuestionTypeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public MethodVO deleteById(FastHelpQuestionTypePO params) {
        if (fastHelpQuestionTypeMapper.deleteById(params) == 0) {
            transactionRollBack();
            return MethodVO.error("删除失败");
        }
        // 删除问题
        FastHelpQuestionPO d = new FastHelpQuestionPO();
        d.setTypeId(params.getId());
        fastHelpQuestionMapper.deleteById(d);
        return MethodVO.success();
    }

    /**
     * 修改排序
     */
    public MethodVO updateSequence(FastHelpQuestionTypePO params) {
        List<FastHelpQuestionTypePO> list = fastHelpQuestionTypeMapper.queryList(new FastHelpQuestionTypePO());
        // 重新排序
        AtomicInteger index = new AtomicInteger(params.getSequence());
        list.forEach(cur -> {
            cur.setUpdatorId(params.getUpdatorId());
            cur.setUpdateTime(DateUtil.getNowDate());
            if (cur.getId().equals(params.getId())) {
                cur.setSequence(params.getSequence());
            } else if (cur.getSequence() >= params.getSequence()) {
                cur.setSequence(index.incrementAndGet());
            }
        });

        if (fastHelpQuestionTypeMapper.updateSequence(list) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

}
