/*
 * Powered By fast.up
 */
package com.fast.service.language;

import com.fast.constant.StaticStr;
import com.fast.enums.YesOrNoEnum;
import com.fast.mapper.language.FastLanguageMapper;
import com.fast.po.language.FastLanguagePO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastLanguageService extends BaseService {

    @Autowired
    private FastLanguageMapper fastLanguageMapper;

    /**
     * 通过id查询单个对象
     */
    public FastLanguagePO queryById(FastLanguagePO params) {
        return fastLanguageMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLanguagePO queryById(Integer id) {
        return fastLanguageMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastLanguagePO queryOne(FastLanguagePO params) {
        return fastLanguageMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastLanguagePO> queryList(FastLanguagePO params) {
        return fastLanguageMapper.queryList(params);
    }
    
    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastLanguagePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastLanguagePO> list = fastLanguageMapper.queryList(params);
        for (FastLanguagePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastLanguagePO params) {
        if (params.getShow() == null) {
            params.setShow(1);
        }
        return fastLanguageMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastLanguagePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastLanguageMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastLanguagePO> list) {
        if (fastLanguageMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastLanguagePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastLanguageMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastLanguagePO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.INVALID_PARAM);
        }
        fastLanguageMapper.deleteById(params.getId());
        return MethodVO.success();
    }

    public List<FastLanguagePO> allList() {
        FastLanguagePO params = new FastLanguagePO();
        params.setShow(YesOrNoEnum.YES.getCode());
        List<FastLanguagePO> list = queryList(params);
        return CollUtil.isEmpty(list) ? new ArrayList<>() : list;
    }

    public List<String> allCodes() {
        return allList().stream().map(FastLanguagePO::getCode).collect(Collectors.toList());
    }

    public List<String> allNames() {
        return allList().stream().map(FastLanguagePO::getName).collect(Collectors.toList());
    }

    public Map<String, String> allCodeMap() {
        return allList().stream().collect(Collectors.toMap(FastLanguagePO::getCode, FastLanguagePO::getName, (a, b) -> b));
    }

    public List<Map<String, String>> allCodeMapList() {
        List<Map<String, String>> list = new ArrayList<>();
        for (FastLanguagePO fastLanguagePO : allList()) {
            Map<String, String> map = new HashMap<>();
            map.put("code", fastLanguagePO.getCode());
            map.put("name", fastLanguagePO.getName());
            list.add(map);
        }
        return list;
    }

    public List<Map<String, String>> queryCodeMaps(String codes) {
        if (StrUtil.isBlank(codes)) {
            return null;
        }
        String[] langCodes = StrUtil.split(codes, ",");
        List<String> list = new ArrayList<>();
        Collections.addAll(list, langCodes);
        FastLanguagePO po = new FastLanguagePO();
        po.setCodes(list);
        List<FastLanguagePO> poList = queryList(po);
        if (CollUtil.isEmpty(poList)) {
            return null;
        }
        return poList.stream().map(fastLanguagePO -> {
            Map<String, String> map = new HashMap<>();
            map.put("code", fastLanguagePO.getCode());
            map.put("name", fastLanguagePO.getName());
            return map;
        }).collect(Collectors.toList());
    }
}
