/*
 * Powered By fast.up
 */
package com.fast.service.i18n;

import com.fast.constant.StaticStr;
import com.fast.enums.LanguageEnum;
import com.fast.mapper.i18n.FastApptextMapper;
import com.fast.po.i18n.FastApptextI18nPO;
import com.fast.po.i18n.FastApptextPO;
import com.fast.service.base.BaseService;
import com.fast.service.language.FastLanguageService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.thread.FastUserContext;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastApptextService extends BaseService {

    @Autowired
    private FastApptextMapper fastApptextMapper;

    @Autowired
    private FastApptextI18nService fastApptextI18nService;

    @Autowired
    private FastLanguageService fastLanguageService;

    /**
     * 通过id查询单个对象
     */
    public FastApptextPO queryById(FastApptextPO params) {
        return fastApptextMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastApptextPO queryById(Integer id) {
        return fastApptextMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastApptextPO queryOne(FastApptextPO params) {
        return fastApptextMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastApptextPO> queryList(FastApptextPO params) {
        return fastApptextMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastApptextPO params, PageVO pageVO) {
        startPage(pageVO);
        // 国际化之后的模糊查询兼容，因为涉及到跨表、跨字段，所以用join兼容一下
        List<FastApptextPO> list;
        if (!StrUtil.isBlank(params.getKeyword())) {
            list = fastApptextMapper.queryListWithI18n(params);
        } else {
            list = fastApptextMapper.queryList(params);
        }
        for (FastApptextPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setTexts(fastApptextI18nService.getMapByCodeForLang(cur.getCode()));
        }
        return ResultVO.summary(getPageListData(list, pageVO), fastLanguageService.allCodeMapList());
    }

    /**
     * 查询总数
     */
    public int queryCount(FastApptextPO params) {
        return fastApptextMapper.queryCount(params);
    }

    public FastApptextPO queryByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        FastApptextPO params = new FastApptextPO();
        params.setCode(code);
        return fastApptextMapper.queryOne(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastApptextPO params) {
        if (queryByCode(params.getCode()) != null) {
            return MethodVO.error("字段名已存在");
        }
        List<FastApptextI18nPO> i18ns = params.getI18ns();
        if (CollUtil.isEmpty(i18ns)) {
            return MethodVO.error("内容不能为空");
        }
        params.setText(takeScContent(i18ns));

        Date nowDate = DateUtil.getNowDate();
        params.setCreateTime(nowDate);
        if (fastApptextMapper.insertSelective(params) == 0) {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        i18ns.forEach(i18n -> {
            if (StrUtil.isBlank(i18n.getText())) {
                return;
            }
            i18n.setCode(params.getCode());
            i18n.setCreateTime(nowDate);
            i18n.setCreatorId(FastUserContext.getUserId());
        });
        MethodVO methodVO = fastApptextI18nService.insertBatch(i18ns);
        if (methodVO.getCode() != 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    private String takeScContent(List<FastApptextI18nPO> i18ns) {
        String defaulted = null;
        for (FastApptextI18nPO content : i18ns) {
            if (Objects.equals(content.getLanguageCode(), LanguageEnum.ENGLISH.getCode())) {
                defaulted = content.getText();
                break;
            }
        }
        if (defaulted == null) {
            throw new RuntimeException("英文是默认兜底版，不允许为空");
        }
        return defaulted;
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastApptextPO> list) {
        if (fastApptextMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastApptextPO params) {
        FastApptextPO exist = queryById(params.getId());
        if (exist == null) {
            return MethodVO.error("数据不存在");
        }
        FastApptextPO codeExist = queryByCode(params.getCode());
        if (codeExist != null && !Objects.equals(codeExist.getId(), params.getId())) {
            return MethodVO.error("字段名已存在");
        }
        List<FastApptextI18nPO> i18ns = params.getI18ns();
        if (CollUtil.isEmpty(i18ns)) {
            return MethodVO.error("内容不能为空");
        }
        params.setText(takeScContent(i18ns));

        Date nowDate = DateUtil.getNowDate();
        params.setUpdateTime(nowDate);
        if (fastApptextMapper.updateById(params) == 0) {
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }

        fastApptextI18nService.deleteByCode(exist.getCode());

        i18ns.forEach(i18n -> {
            if (StrUtil.isBlank(i18n.getText())) {
                return;
            }
            i18n.setCode(params.getCode());
            i18n.setCreateTime(nowDate);
            i18n.setCreatorId(FastUserContext.getUserId());
        });
        MethodVO methodVO = fastApptextI18nService.insertBatch(i18ns);
        if (methodVO.getCode() != 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastApptextPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.INVALID_PARAM);
        }
        FastApptextPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        int row = fastApptextMapper.deleteById(po.getId());
        if (row > 0) {
            fastApptextI18nService.deleteByCode(po.getCode());
        }
        return MethodVO.success();
    }
}
