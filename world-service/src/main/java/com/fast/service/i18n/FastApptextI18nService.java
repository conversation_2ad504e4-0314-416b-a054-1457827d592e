/*
 * Powered By fast.up
 */
package com.fast.service.i18n;

import com.fast.constant.StaticStr;
import com.fast.mapper.i18n.FastApptextI18nMapper;
import com.fast.po.i18n.FastApptextI18nPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastApptextI18nService extends BaseService {

    @Autowired
    private FastApptextI18nMapper fastApptextI18nMapper;

    /**
     * 通过id查询单个对象
     */
    public FastApptextI18nPO queryById(FastApptextI18nPO params) {
        return fastApptextI18nMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastApptextI18nPO queryById(Integer id) {
        return fastApptextI18nMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastApptextI18nPO queryOne(FastApptextI18nPO params) {
        return fastApptextI18nMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastApptextI18nPO> queryList(FastApptextI18nPO params) {
        return fastApptextI18nMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastApptextI18nPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastApptextI18nPO> list = fastApptextI18nMapper.queryList(params);
        for (FastApptextI18nPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastApptextI18nPO params) {
        return fastApptextI18nMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastApptextI18nPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastApptextI18nMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastApptextI18nPO> list) {
        if (fastApptextI18nMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastApptextI18nPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastApptextI18nMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return 0;
        }
        return fastApptextI18nMapper.deleteByCode(code);
    }

    public List<FastApptextI18nPO> queryByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return Lists.newArrayList();
        }
        FastApptextI18nPO po = new FastApptextI18nPO();
        po.setCode(code);
        return queryList(po);
    }

    public Map<String, String> getMapByCodeForLang(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        FastApptextI18nPO po = new FastApptextI18nPO();
        po.setCode(code);
        List<FastApptextI18nPO> list = queryList(po);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().collect(Collectors.toMap(FastApptextI18nPO::getLanguageCode, FastApptextI18nPO::getText, (a, b) -> b, LinkedHashMap::new));
    }
}
