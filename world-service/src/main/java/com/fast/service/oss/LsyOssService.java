package com.fast.service.oss;

import com.alibaba.fastjson.JSONObject;
import com.fast.framework.config.LsyTosConfig;
import com.fast.po.oss.LsyCallbackDTO;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Base64;
import com.fast.utils.file.FileUploadUtil;
import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.model.object.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 连山云OSS
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "fast.deploy_platform", havingValue = "2")
public class LsyOssService implements OssCommService {

    public static final String CONTENT_TYPE = "application/json";

    @Autowired
    private LsyTosConfig lsyTosConfig;

    @Autowired
    @Qualifier("tosv2")
    private TOSV2 tos;

    @Autowired
    @Qualifier("contTosv2")
    private TOSV2 contTos;

    @Bean
    public TOSV2 tosv2() {
        return new TOSV2ClientBuilder().build(lsyTosConfig.getRegion(), lsyTosConfig.getEndpoint(), lsyTosConfig.getAccessKey(), lsyTosConfig.getSecretKey());
    }

    @Bean
    public TOSV2 contTosv2() {
        return new TOSV2ClientBuilder().build(lsyTosConfig.getCont().getRegion(), lsyTosConfig.getCont().getEndpoint(), lsyTosConfig.getAccessKey(), lsyTosConfig.getSecretKey());
    }

    @Override
    public String uploadFile(boolean isCont, Integer retailId, File file, String fileKey) {
        return uploadFile(isCont, fileKey, file);
    }

    @Override
    public String uploadFile(boolean isCont, String fileKey, File file) {
        long start = System.currentTimeMillis();

        PutObjectInput putObjectInput;
        try (FileInputStream inputStream = new FileInputStream(file)) {
            putObjectInput = new PutObjectInput()
                    .setBucket(isCont ? lsyTosConfig.getCont().getBucketName() : lsyTosConfig.getBucketName())
                    .setKey(fileKey)
                    .setContent(inputStream)
                    .setContentLength(file.length());
            PutObjectOutput output = isCont ? contTos.putObject(putObjectInput) : tos.putObject(putObjectInput);
            log.info("连山云上传文件成功，耗时: {}s， output: {}", (System.currentTimeMillis() - start) / 1000f, JSONObject.toJSONString(output));
            if (output != null && output.getRequestInfo() != null && output.getRequestInfo().getStatusCode() == 200) {
                return getHost(isCont) + fileKey;
            }
        } catch (Exception e) {
            log.error("连山云上传文件失败 fileKey: {}", fileKey, e);
        }
        return null;
    }

    @Override
    public String uploadFile(boolean isCont, String fileKey, InputStream inputStream) {
        long start = System.currentTimeMillis();

        PutObjectInput putObjectInput = new PutObjectInput()
                .setBucket(isCont ? lsyTosConfig.getCont().getBucketName() : lsyTosConfig.getBucketName())
                .setKey(fileKey)
                .setContent(inputStream);
        PutObjectOutput output = isCont ? contTos.putObject(putObjectInput) : tos.putObject(putObjectInput);
        log.info("连山云上传文件成功，耗时: {}s， output: {}", (System.currentTimeMillis() - start) / 1000f, JSONObject.toJSONString(output));
        if (output != null && output.getRequestInfo() != null && output.getRequestInfo().getStatusCode() == 200) {
            return getHost(isCont) + fileKey;
        }
        return null;
    }

    @Override
    public String getDomain(boolean isCont) {
        return isCont ? lsyTosConfig.getCont().getBucketName() + "." + lsyTosConfig.getCont().getEndpoint() : lsyTosConfig.getBucketName() + "." + lsyTosConfig.getEndpoint();
    }

    @Override
    public String getHost(boolean isCont) {
        return isCont ? lsyTosConfig.getCont().getHost() : lsyTosConfig.getHost();
    }

    @Override
    public JSONObject getTempUploadParams(boolean isCont, Integer retailId, Integer fileType, String fileName, String fileSuffix, Integer bizId, String lang, long expireTime, TimeUnit timeUnit) {
        String dir = FileUploadUtil.generateDir(retailId, fileType);
        // 字幕文件特殊处理，直接使用原始文件名
        String objectKey;
        if (!isCont && fileType == 5) {
            objectKey = dir + "/" + bizId + "/" + lang + "/" + fileName;
        } else {
            objectKey = FileUploadUtil.generateFile(dir) + (fileSuffix.contains(".") ? fileSuffix : "." + fileSuffix);
        }
        long expires = timeUnit.toSeconds(expireTime);

        LsyCallbackDTO lsyCallbackDTO = new LsyCallbackDTO();
        lsyCallbackDTO.setCallbackUrl(lsyTosConfig.getCallbackUrl());
        lsyCallbackDTO.setCallbackHost(lsyTosConfig.getCallbackHost());
        JSONObject callbackBody = new JSONObject();
        callbackBody.put("originalKey", "${object}");
        callbackBody.put("retailId", retailId);
        callbackBody.put("fileSize", "${size}");
        callbackBody.put("fileType", fileType);
        callbackBody.put("ytCont", isCont);
        String replace = callbackBody.toJSONString();
        lsyCallbackDTO.setCallbackBody(replace);
        lsyCallbackDTO.setCallbackBodyType(CONTENT_TYPE);

        String callbackStr = StrUtil.replace(JsonUtil.toString(lsyCallbackDTO), "\\\"${size}\\\"", "${size}");
        callbackStr = StrUtil.replace(callbackStr, "\\\"${object}\\\"", "${object}");
        String callback = new String(Base64.encode(callbackStr.getBytes()));
        List<PostSignatureCondition> conditions = new ArrayList<>();
        conditions.add(new PostSignatureCondition("success_action_status", "201"));
        conditions.add(new PostSignatureCondition("x-tos-callback", callback));
        PreSignedPostSignatureInput input = new PreSignedPostSignatureInput()
                .setBucket(isCont ? lsyTosConfig.getCont().getBucketName() : lsyTosConfig.getBucketName())
                .setConditions(conditions)
                .setKey(objectKey).setExpires(expires);
        PreSignedPostSignatureOutput output = isCont ? contTos.preSignedPostSignature(input) : tos.preSignedPostSignature(input);

        JSONObject jsonObject = new JSONObject();

        jsonObject.put("credential", output.getCredential());
        jsonObject.put("signature", output.getSignature());
        jsonObject.put("policy", output.getPolicy());
        jsonObject.put("date", output.getDate());
        jsonObject.put("algorithm", output.getAlgorithm());
        jsonObject.put("key", objectKey);
        jsonObject.put("host", getHost(isCont));
        jsonObject.put("wholeUrl", getWholeUrl(isCont, objectKey));
        jsonObject.put("callback", callback);
        return jsonObject;
    }

    @Override
    public String fetchUrl(boolean isCont, String fileKey, String url) {
        try {
            FetchObjectInput fetchObjectInput = new FetchObjectInput()
                    .setBucket(isCont ? lsyTosConfig.getCont().getBucketName() : lsyTosConfig.getBucketName())
                    .setKey(fileKey)
                    .setUrl(url);
            FetchObjectOutput output = isCont ? contTos.fetchObject(fetchObjectInput) : tos.fetchObject(fetchObjectInput);
            log.info("连山云抓取文件成功 output: {}", JSONObject.toJSONString(output));
            if (output != null && output.getRequestInfo() != null && output.getRequestInfo().getStatusCode() == 200) {
                return getHost(isCont) + fileKey;
            }
        } catch (Exception e) {
            log.info("连山云抓取文件失败 fileKey: {}", fileKey);
        }
        return null;
    }

    public void downloadFile(boolean isCont, String fileKey, String filePath) {
        downloadFile(isCont, fileKey, isCont ? lsyTosConfig.getCont().getBucketName() : lsyTosConfig.getBucketName(), filePath);
    }

    public void downloadFile(boolean isCont, String fileKey, String bucketName, String filePath) {
        GetObjectV2Input input = new GetObjectV2Input().setBucket(bucketName).setKey(fileKey);
        // 以下代码展示如何将数据下载到本地文件
        File file = new File(filePath);
        if (file.getParentFile() != null && !file.getParentFile().exists()) {
            // 此处判断文件路径的父文件夹是否存在，不存在则创建父文件夹
            // 如果父文件夹不存在且不创建，直接写入会报 FileNotFoundException
            file.getParentFile().mkdirs();
        }
        try (GetObjectV2Output output = isCont ? contTos.getObject(input) : tos.getObject(input);
             FileOutputStream fos = new FileOutputStream(file)) {
            log.info("begin to read content in object to file.");
            if (output.getContent() != null) {
                byte[] buffer = new byte[4096];
                int length;
                while ((length = output.getContent().read(buffer)) != -1) {
                    fos.write(buffer, 0, length);
                }
            }
            fos.flush();
        } catch (Exception e) {
            log.error("连山云下载文件出错fileKey:{}", fileKey, e);
        }
    }
}