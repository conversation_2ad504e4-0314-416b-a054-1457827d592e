package com.fast.service.oss;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticYml;
import com.fast.mapper.oss.OssFileMapper;
import com.fast.po.oss.OssFile;
import com.fast.utils.CollUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.file.FileUploadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class OssService {

    @Autowired
    private OssCommService ossCommService;
    @Autowired
    private OssFileMapper ossFileMapper;

    public String getOssHost(boolean isCont) {
        return ossCommService.getHost(isCont);
    }

    /**
     * 上传文件
     *
     * @param file 上传的文件
     * @return
     */
    public String uploadFile(boolean isCont, Integer retailId, Integer fileType, File file) {
        return uploadFile(isCont, retailId, fileType, file, "");
    }

    /**
     * 上传文件
     *
     * @param file     上传的文件
     * @param fileType 文件类型，按下标取值：["other", "image", "voice", "video", "doc", "caption"]
     * @return
     */
    public String uploadFile(boolean isCont, Integer retailId, Integer fileType, File file, String prefix) {
        String fileKey = FileUploadUtil.generateFileName(retailId, fileType, file, prefix);
        log.info("开始上传文件: {}, isCont: {}, 生成fileKey: {}", file.getAbsolutePath(), isCont, fileKey);
        // 调用三方oss上传文件
        String url = ossCommService.uploadFile(isCont, fileKey, file);
        if (url != null) {
            // 记录oss文件信息
            OssFile ossFile = new OssFile();
            ossFile.setRetailId(retailId);
            ossFile.setOriginalKey(fileKey);
            ossFile.setFileSize(file.length() / 1024);
            ossFileMapper.insertSelective(ossFile);
        }
        return url;
    }

    /**
     * 上传文件
     *
     * @param file 上传的文件
     * @return
     */
    public String uploadFileByName(boolean isCont, Integer retailId, Integer fileType, File file, String fileName) {
        String fileKey = FileUploadUtil.generateFileNameNoRandom(retailId, fileType, file, fileName);
        // 调用三方oss上传文件
        String url = ossCommService.uploadFile(isCont, fileKey, file);
        if (url != null) {
            // 记录oss文件信息
            OssFile ossFile = new OssFile();
            ossFile.setRetailId(retailId);
            ossFile.setOriginalKey(fileKey);
            ossFile.setFileSize(file.length() / 1024);
            ossFileMapper.insertSelective(ossFile);
        }
        return url;
    }

    /**
     * 上传文件
     *
     * @param bytes 上传的文件
     * @return
     */
    public String uploadFile(boolean isCont, Integer retailId, Integer fileType, byte[] bytes, String suffix) {
        String fileKey = FileUploadUtil.generateFileName(retailId, fileType, suffix);
        // 调用三方oss上传文件
        String url = ossCommService.uploadFile(isCont, fileKey, new ByteArrayInputStream(bytes));
        if (url != null) {
            // 记录oss文件信息
            OssFile ossFile = new OssFile();
            ossFile.setRetailId(retailId);
            ossFile.setOriginalKey(fileKey);
            ossFile.setFileSize((long) bytes.length);
            ossFileMapper.insertSelective(ossFile);
        }
        return url;
    }


    /**
     * 上传文件
     *
     * @param multipartFile 上传的文件
     * @return
     */
    public String uploadFile(boolean isCont, Integer retailId, Integer fileType, MultipartFile multipartFile) {
        return uploadFile(isCont, retailId, fileType, multipartFile, "");
    }

    /**
     * 上传文件
     *
     * @param multipartFile 上传的文件
     * @return
     */
    public String uploadFile(boolean isCont, Integer retailId, Integer fileType, MultipartFile multipartFile, String prefix) {
        String fileKey = FileUploadUtil.generateFileName(retailId, fileType, multipartFile, prefix);
        // 调用三方oss上传文件
        try {
            String url = ossCommService.uploadFile(isCont, fileKey, multipartFile.getInputStream());
            if (url != null) {
                // 记录oss文件信息
                OssFile ossFile = new OssFile();
                ossFile.setRetailId(retailId);
                ossFile.setOriginalKey(fileKey);
                ossFile.setFileSize(multipartFile.getSize() / 1024);
                ossFileMapper.insertSelective(ossFile);
            }
            return url;
        } catch (Exception e) {
            log.error("上传文件失败: {}", ExceptionUtils.getStackTrace(e));
            return null;
        }
    }


    public String getWholeUrl(boolean isCont, String fileKey) {
        return ossCommService.getWholeUrl(isCont, fileKey);
    }

    public String getHost(boolean isCont) {
        return ossCommService.getHost(isCont);
    }

    public JSONObject getTempUploadParams(boolean isCont, Integer retailId, Integer fileType, String fileName, String fileSuffix, Integer bizId, String lang, long expireTime, TimeUnit timeUnit) {
        return ossCommService.getTempUploadParams(isCont, retailId, fileType, fileName, fileSuffix, bizId, lang, expireTime, timeUnit);
    }

    public String fetchUrl(boolean isCont, Integer retailId, Integer fileType, String prefix, String url) {
        String suffix = StrUtil.substringAfterLast(url, ".");
        String fileKey = FileUploadUtil.generateFileName(retailId, fileType, prefix, suffix);
        return ossCommService.fetchUrl(isCont, fileKey, url);
    }

    public String replaceOssDomain(boolean isCont, String url) {
        if (StrUtil.isBlank(url) || CollUtil.isEmpty(StaticYml.OSS_REPLACE_URLS)) {
            return url;
        }
        for (String ossReplaceUrl : StaticYml.OSS_REPLACE_URLS) {
            String replace = url.replace(ossReplaceUrl, "");
            if (!StrUtil.equals(replace, url)) {
                // 替换成功后返回 未成功返回原地址
                url = getHost(isCont) + replace;
                break;
            }
        }
        return url;
    }
}
