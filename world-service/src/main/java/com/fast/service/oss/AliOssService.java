package com.fast.service.oss;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.*;
import com.fast.framework.config.AliYunOssConfig;
import com.fast.po.oss.UploadParamsDTO;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.file.FileUploadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 阿里云OSS
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "fast.deploy_platform", havingValue = "1")
public class AliOssService implements OssCommService {

    // 回调body
    private static final String CALLBACK_BODY = "bucket=${bucket}&objectKey=${object}&etag=${etag}&size=${size}&mimeType=${mimeType}&height=${imageInfo.height}&width=${imageInfo.width}&format=${imageInfo.format}";
    // 回调body类型
    private static final String CALLBACK_BODY_TYPE = "application/x-www-form-urlencoded";

    @Autowired
    private OSSClient ossClient;
    @Autowired
    private AliYunOssConfig ossConfig;

    /**
     * 上传文件
     *
     * @param file 上传的文件
     * @return
     */
    @Override
    public String uploadFile(boolean isCont, Integer retailId, File file, String fileKey) {
        if (StrUtil.isEmpty(fileKey)) {
            return fileKey;
        }
        PutObjectResult result = ossClient.putObject(ossConfig.getBucketName(), fileKey, file);
        if (result != null) {
            log.info("上传成功:{}", fileKey);
        }
        return fileKey;
    }

    @Override
    public String uploadFile(boolean isCont, String fileKey, File file) {
        PutObjectResult result = ossClient.putObject(ossConfig.getBucketName(), fileKey, file);
        return getHost(isCont) + fileKey;
    }

    @Override
    public String uploadFile(boolean isCont, String fileKey, InputStream inputStream) {
        PutObjectResult result = ossClient.putObject(ossConfig.getBucketName(), fileKey, inputStream);
        return getHost(isCont) + fileKey;
    }

    @Override
    public String fetchUrl(boolean isCont, String fileKey, String url) {
        return getHost(isCont) + fileKey;
    }

    @Override
    public String getDomain(boolean isCont) {
        return ossConfig.getBucketName() + "." + ossConfig.getEndpoint();
    }

    @Override
    public String getHost(boolean isCont) {
        return ossConfig.getHost();
    }

    @Override
    public JSONObject getTempUploadParams(boolean isCont, Integer retailId, Integer fileType, String fileName, String fileSuffix, Integer bizId, String lang, long expireTime, TimeUnit timeUnit) {
        // 生成目录(文件夹)
        String dir = FileUploadUtil.generateDir(retailId, fileType);
        // callbackUrl 上传回调服务器的URL
        String callbackUrl = this.ossConfig.getCallbackUrl();
        UploadParamsDTO uploadParamsDTO = doGetUploadParams(dir, callbackUrl, expireTime, timeUnit);
        return JsonUtil.toJSONObject(uploadParamsDTO);
    }

    /**
     * 组成临时上传参数
     *
     * @param dir         目标文件夹
     * @param callbackUrl 回调地址
     * @param expireTime  有效时间
     * @param timeUnit    时间单位
     * @return 上传参数
     */
    private UploadParamsDTO doGetUploadParams(String dir, String callbackUrl, long expireTime, TimeUnit timeUnit) {
        String host = this.ossConfig.getHost();
        long expireMillis = System.currentTimeMillis() + timeUnit.toMillis(expireTime);
        Date expiration = new Date(expireMillis);
        PolicyConditions policy = new PolicyConditions();
        policy.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
        policy.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, dir + "/");
        String postPolicy = this.ossClient.generatePostPolicy(expiration, policy);
        byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
        String encodedPolicy = BinaryUtil.toBase64String(binaryData);
        String postSignature = this.ossClient.calculatePostSignature(postPolicy);
        // 参数对象
        UploadParamsDTO paramsDTO = new UploadParamsDTO();
        paramsDTO.setAccessid(this.ossConfig.getAccessKey());
        paramsDTO.setPolicy(encodedPolicy);
        paramsDTO.setFilePrefix(FileUploadUtil.generateFile(dir)); // TODO 字幕文件兼容
        paramsDTO.setSignature(postSignature);
        paramsDTO.setHost(host);
        paramsDTO.setExpire(expireMillis / 1000);
        JSONObject callback = new JSONObject();
        callback.put("callbackUrl", callbackUrl);
        callback.put("callbackBody", CALLBACK_BODY);
        callback.put("callbackBodyType", CALLBACK_BODY_TYPE);
        String callbackBody = BinaryUtil.toBase64String(callback.toString().getBytes());
        paramsDTO.setCallback(callbackBody);
        return paramsDTO;
    }

    /**
     * 创建Bucket
     */
    @PostConstruct
    public void createBucket() {
        // 创建bucket
        if (ossClient.doesBucketExist(ossConfig.getBucketName())) {
            return;
        }
        CreateBucketRequest createBucketRequest = new CreateBucketRequest(ossConfig.getBucketName());
        createBucketRequest.setCannedACL(CannedAccessControlList.PublicRead);
        ossClient.createBucket(createBucketRequest);
    }
}
