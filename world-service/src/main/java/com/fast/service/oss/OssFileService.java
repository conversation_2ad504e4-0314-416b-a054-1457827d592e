/*
 * Powered By www.cardmee.net
 */
package com.fast.service.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.*;
import com.fast.constant.StaticVar;
import com.fast.framework.config.AliYunOssConfig;
import com.fast.framework.exception.MyException;
import com.fast.mapper.oss.OssFileMapper;
import com.fast.po.oss.OssFile;
import com.fast.po.oss.OssFileInfo;
import com.fast.po.oss.OssFileTypeEnum;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.net.URL;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class OssFileService extends BaseService {

    // 常规
    private static final String OSS_API = "oss-api/brandOssFile/wap/getUrlSimple?v=";
    private static final int DATA_1024 = 1024;
    private static final String PICTURE = "jpg,jpeg,png,gif";
    private static final String VOICE = "aac,ac3,acm,amr,ape,caf,flac,m4a,mp3,ra,wav,wma,aiff";
    private static final String VIDEO = "mp4,wmv,flv,avi,mpeg,ogg,rmvb,mov";
    @Autowired
    private OssFileMapper ossFileMapper;
    @Autowired(required = false)
    private AliYunOssConfig ossConfig;

    /**
     * 通过id查询单个对象
     */
    public OssFile queryById(OssFile entity) {
        return ossFileMapper.queryById(entity);
    }

    /**
     * 通过id查询单个对象
     */
    public OssFile queryById(Integer id) {
        OssFile entity = new OssFile();
        entity.setId(id);
        return ossFileMapper.queryById(entity);
    }

    /**
     * 通过条件查询单个对象
     */
    public OssFile queryOne(OssFile entity) {
        return ossFileMapper.queryOne(entity);
    }

    /**
     * 查询总数
     */
    public int queryAllCount(OssFile entity) {
        return ossFileMapper.queryAllCount(entity);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public int insert(OssFile entity) {
        if (ossFileMapper.insertSelective(entity) == 0) {
            transactionRollBack();
            return 0;
        }
        return 1;
    }

    /**
     * 新增(内部)
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOssFileByCallBack(OssFileInfo info) {
        if (isBlank(info.getObjectKey())) {
            throw new MyException("Object不能为空");
        }
        if (isBlank(info.getWholeUrl())) {
            throw new MyException("WholeUrl不能为空");
        }
        if (info.getSize() == null || info.getSize() < 0) {
            throw new MyException("Size不能为空");
        }
        String retailId = info.getObjectKey().substring(0, info.getObjectKey().indexOf("/"));
        if (StrUtil.isEmpty(retailId) || "default".equals(retailId)) {
            throw new MyException("retailId不能为空");
        }
        OssFile entity = new OssFile();
        entity.setRetailId(toInteger(retailId));
        entity.setOriginalKey(info.getObjectKey());

        int count = queryAllCount(entity);
        if (count == 1) {
            throw new MyException("不能重复添加");
        }

        // 文件大小，不足1KB按照1KB计算
        if (info.getSize() % DATA_1024 > 0) {
            entity.setFileSize((info.getSize() / DATA_1024) + 1);
        } else {
            entity.setFileSize(info.getSize() / DATA_1024);
        }
        if (isBlank(info.getFormat())) {
            info.setFormat(info.getObjectKey().substring(info.getObjectKey().lastIndexOf(".") + 1));
        }
        // 文件类型
        if (PICTURE.contains(info.getFormat().toLowerCase())) {
            entity.setFileType(1);
        } else if (VOICE.contains(info.getFormat().toLowerCase())) {
            entity.setFileType(2);
        } else if (VIDEO.contains(info.getFormat().toLowerCase())) {
            entity.setFileType(3);
        } else {
            entity.setFileType(0);
        }
        entity.setCreateTime(DateUtil.getNowDate());
        entity.setUpdateTime(entity.getCreateTime());
        if (ossFileMapper.insertSelective(entity) == 0) {
            throw new MyException("新增异常");
        }
        ResultVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertBatch(List<OssFile> list) {
        return ossFileMapper.insertBatch(list);
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public int update(OssFile entity) {
        entity.setUpdateTime(DateUtil.getNowDate());
        if (ossFileMapper.updateById(entity) == 0) {
            transactionRollBack();
            return 0;
        }
        return 1;
    }

    /**
     * 更新-VisitNum
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateVisitNum(OssFile entity) {
        entity.setUpdateTime(DateUtil.getNowDate());
        if (ossFileMapper.updateVisitNum(entity) == 0) {
            transactionRollBack();
            return 0;
        }
        return 1;
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public int delete(OssFile entity) {
        entity.setUpdateTime(DateUtil.getNowDate());
        if (ossFileMapper.delete(entity) == 0) {
            transactionRollBack();
            return 0;
        }
        return 1;
    }

    /**
     * SDK上传文件
     *
     * @param key  key
     * @param file file
     * @return 上传结果
     */
    public PutObjectResult uploadFileSS(String key, File file) {
        DefaultCredentialProvider provider = new DefaultCredentialProvider(ossConfig.getAccessKey(), ossConfig.getSecretKey());
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(ossConfig.getEndpoint(), provider);
        // 创建PutObjectRequest对象。
        PutObjectRequest putObjectRequest = new PutObjectRequest(ossConfig.getBucketName(), key, file);
        // 上传文件
        PutObjectResult r = ossClient.putObject(putObjectRequest);
        // 关闭ossClient
        ossClient.shutdown();
        return r;
    }

    /**
     * 获取临时链接地址
     *
     * @param key key
     * @return 临时链接
     */
    public String queryUrl(String key) {
        return queryUrl(key, 60);
    }

    /**
     * 获取临时链接地址
     *
     * @param key key
     * @return 临时链接
     */
    public String queryUrl(String key, int minute) {
        DefaultCredentialProvider provider = new DefaultCredentialProvider(ossConfig.getAccessKey(), ossConfig.getSecretKey());
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(ossConfig.getEndpoint(), provider);

        // 设置URL过期时间
        Date expiration = new Date(System.currentTimeMillis() + minute * 60 * 1000L);
        URL url = ossClient.generatePresignedUrl(ossConfig.getBucketName(), key, expiration);
        // 关闭OSSClient。
        ossClient.shutdown();
        return url.toString();
    }

    /**
     * 获取文件元信息
     *
     * @param key key
     * @return 文件元信息
     */
    public SimplifiedObjectMeta getObjectMeta(String key) {
        DefaultCredentialProvider provider = new DefaultCredentialProvider(ossConfig.getAccessKey(), ossConfig.getSecretKey());
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(ossConfig.getEndpoint(), provider);

        // 获取文件的部分元信息。
        SimplifiedObjectMeta objectMeta = ossClient.getSimplifiedObjectMeta(ossConfig.getBucketName(), key);
        log.info("key:{}; 元信息:{}", key, JsonUtil.toString(objectMeta));
        // 关闭OSSClient。
        ossClient.shutdown();
        return objectMeta;
    }

    /**
     * 设置文件访问权限
     *
     * @param key       key
     * @param cannedACL CannedAccessControlList
     */
    public void setObjectAclAccess(String key, CannedAccessControlList cannedACL) {
        DefaultCredentialProvider provider = new DefaultCredentialProvider(ossConfig.getAccessKey(), ossConfig.getSecretKey());
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(ossConfig.getEndpoint(), provider);

        // 创建SetObjectAclRequest对象
        SetObjectAclRequest setObjectAclRequest = new SetObjectAclRequest(ossConfig.getBucketName(), key, cannedACL);
        // 设置指定版本文件的权限。
        ossClient.setObjectAcl(setObjectAclRequest);
        // 关闭OSSClient。
        ossClient.shutdown();
    }

    /**
     * 设置文件访问权限-私有
     *
     * @param key key
     */
    public void setObjectAclPrivate(String key) {
        setObjectAclAccess(key, CannedAccessControlList.Private);
    }

    /**
     * 设置文件访问权限-公共读
     *
     * @param key key
     */
    public void setObjectAclPublicRead(String key) {
        setObjectAclAccess(key, CannedAccessControlList.PublicRead);
    }

    /**
     * 获取oss文件信息
     *
     * @param data https://camee-media-files.oss-cn-shanghai.aliyuncs.com/0/2021/01/image/000012d4-4a0e-439a-9380-5406b64ea31c.jpg
     * @return
     */
    public OssFile getBrandOssFileByData(String data) {
        if (data.startsWith(ossConfig.getHost())) {
            String key = data.replace(ossConfig.getHost(), "");
            OssFile query = new OssFile();
            query.setOriginalKey(key);
            return queryOne(query);
        }
        return null;
    }

    /**
     * 获取临时地址
     *
     * @param data https://camee-media-files.oss-cn-shanghai.aliyuncs.com/0/2021/01/image/000012d4-4a0e-439a-9380-5406b64ea31c.jpg
     * @return
     */
    public String getTempDataSimple(String data) {
        return getTempDataSimple(data, false);
    }

    /**
     * 获取临时地址
     *
     * @param data      https://camee-media-files.oss-cn-shanghai.aliyuncs.com/0/2021/01/image/000012d4-4a0e-439a-9380-5406b64ea31c.jpg
     * @param needCount 是否统计访问次数
     * @return
     */
    public String getTempDataSimple(String data, boolean needCount) {
        return getTempDataSimple(data, needCount, null);
    }

    /**
     * 获取临时地址
     *
     * @param data            https://camee-media-files.oss-cn-shanghai.aliyuncs.com/0/2021/01/image/000012d4-4a0e-439a-9380-5406b64ea31c.jpg
     * @param needCount       是否统计访问次数
     * @param ossFileTypeEnum oss文件类型（定义特殊操作）
     * @return
     */
    public String getTempDataSimple(String data, boolean needCount, OssFileTypeEnum ossFileTypeEnum) {
        if (data.startsWith(ossConfig.getHost())) {
            String key = data.replace(ossConfig.getHost(), "");
            OssFile query = new OssFile();
            query.setOriginalKey(key);
            OssFile ossFile = queryOne(query);
            if (ossFile != null) {
                String v = ossFile.getId() + StaticVar.CROSS_HATCH_2 + System.currentTimeMillis();
                if (needCount) {
                    v += StaticVar.CROSS_HATCH_2 + "1";
                } else {
                    v += StaticVar.CROSS_HATCH_2 + "0";
                }
                if (ossFileTypeEnum != null) {
                    v += StaticVar.CROSS_HATCH_2 + ossFileTypeEnum.index;
                } else {
                    v += StaticVar.CROSS_HATCH_2 + "0";
                }
                v = encode(v);
                setObjectAclPrivate(key);
                return OSS_API + v;
            }
        }
        return data;
    }

    /**
     * 获取OSS地址
     *
     * @param id
     * @return
     */
    public String getUrlSimple(Integer id) {
        OssFile ossFile = queryById(id);
        if (ossFile != null) {
            return getUrlSimple(ossFile.getOriginalKey());
        }
        return null;
    }

    /**
     * 获取OSS地址
     *
     * @param key https://s.cardmee.net/oss-api/
     * @return
     */
    public String getUrlSimple(String key) {
        setObjectAclPrivate(key);
        return queryUrl(key);
    }

    /**
     * 获取OSS地址
     *
     * @param id
     * @param c
     * @param index
     * @return
     */
    public String getOssUrl(HttpServletRequest request, Integer id, Integer c, Integer index) {
        OssFile ossFile = queryById(id);
        if (ossFile == null) {
            return null;
        }
        if (index != null && index > 0) {
            OssFileTypeEnum typeEnum = OssFileTypeEnum.get(index);
        }
        return getUrlSimple(ossFile.getOriginalKey());
    }

}
