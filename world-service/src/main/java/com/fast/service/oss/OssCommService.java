package com.fast.service.oss;

import com.alibaba.fastjson.JSONObject;

import java.io.File;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

public interface OssCommService {

    String uploadFile(boolean isCont, Integer retailId, File file, String fileKey);

    String uploadFile(boolean isCont, String fileKey, File file);

    String uploadFile(boolean isCont, String fileKey, InputStream inputStream);

    default String getWholeUrl(boolean isCont, String fileKey) {
        int lastIndexOf = fileKey.lastIndexOf("/");
        String name = lastIndexOf != -1 ? fileKey.substring(lastIndexOf + 1) : fileKey;
        String path = lastIndexOf != -1 ? fileKey.substring(0, lastIndexOf + 1) : fileKey;
        if (lastIndexOf != -1) {
            return getHost(isCont) + path + URLEncoder.encode(name, StandardCharsets.UTF_8);
        } else {
            return getHost(isCont) + fileKey;
        }
    }

    JSONObject getTempUploadParams(boolean isCont, Integer retailId, Integer fileType, String fileName, String fileSuffix, Integer bizId, String lang, long expireTime, TimeUnit timeUnit);

    String getDomain(boolean isCont);

    String getHost(boolean isCont);

    String fetchUrl(boolean isCont, String fileKey, String url);
}
