/*
 * Powered By fast.up
 */
package com.fast.service.star;

import com.fast.constant.StaticStr;
import com.fast.mapper.star.FastStarMapper;
import com.fast.po.star.FastStarPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastStarService extends BaseService {

    @Autowired
    private FastStarMapper fastStarMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStarPO queryById(FastStarPO params) {
        return fastStarMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStarPO queryById(Integer id) {
        return fastStarMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStarPO queryOne(FastStarPO params) {
        return fastStarMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStarPO> queryList(FastStarPO params) {
        return fastStarMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStarPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStarPO> list = fastStarMapper.queryList(params);
        for (FastStarPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setBindStarName(StrUtil.isBlank(cur.getStarName()) ? 0 : 1);
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStarPO params) {
        return fastStarMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStarPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStarMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStarPO> list) {
        if (fastStarMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStarPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStarMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public ResultVO<?> querySelectList(FastStarPO params) {
        List<FastStarPO> list = fastStarMapper.querySelectNameList(params);
        return ResultVO.success(list);
    }

    public ResultVO<?> getSelectIdList(FastStarPO params) {
        List<FastStarPO> list = fastStarMapper.querySelectNameList(params);
        return ResultVO.success(list);
    }
}
