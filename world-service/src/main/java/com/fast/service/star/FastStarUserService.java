/*
 * Powered By fast.up
 */
package com.fast.service.star;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.star.FastStarUserMapper;
import com.fast.po.star.FastStarUserPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastStarUserService extends BaseService {

    @Autowired
    private FastStarUserMapper fastStarUserMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStarUserPO queryById(FastStarUserPO params) {
        return fastStarUserMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStarUserPO queryById(Integer id) {
        return fastStarUserMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStarUserPO queryOne(FastStarUserPO params) {
        return fastStarUserMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStarUserPO> queryList(FastStarUserPO params) {
        return fastStarUserMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStarUserPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStarUserPO> list = fastStarUserMapper.queryList(params);
        for (FastStarUserPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            // 判断是否授权
            if (List.of(0, 2).contains(cur.getAuthSate())) {
                cur.setAuthUrl(String.format("https://open.oceanengine.com/audit/oauth.html?app_id=%s&state=%s&material_auth=1&rid=9sfbmo156r5",
                        StaticVar.OCEAN_ENGINE_OPEN_APP_ID,
                        cur.getId()
                ));
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStarUserPO params) {
        return fastStarUserMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStarUserPO params) {
        FastStarUserPO query = new FastStarUserPO();
        query.setAccountName(params.getAccountName());
        FastStarUserPO userPO = fastStarUserMapper.queryOne(query);
        if (userPO != null) {
            return MethodVO.error(StaticStr.RECORD_EXIST);
        }
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setAuthSate(0);
        if (fastStarUserMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStarUserPO> list) {
        list.forEach(item -> item.setAuthSate(0));
        if (fastStarUserMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStarUserPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStarUserMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public ResultVO<?> getFastStarUserListForSelect(FastStarUserPO params) {
        return ResultVO.success(fastStarUserMapper.getFastStarUserListForSelect(params));
    }


    public ResultVO<?> getPrincipalNameList(FastStarUserPO params) {
        return ResultVO.success(fastStarUserMapper.getPrincipalNameList(params));
    }
}
