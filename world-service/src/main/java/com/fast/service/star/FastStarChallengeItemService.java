/*
 * Powered By fast.up
 */
package com.fast.service.star;

import com.fast.constant.StaticStr;
import com.fast.mapper.star.FastStarChallengeItemMapper;
import com.fast.po.star.FastStarChallengeItemPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastStarChallengeItemService extends BaseService {

    @Autowired
    private FastStarChallengeItemMapper fastStarChallengeItemMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStarChallengeItemPO queryById(FastStarChallengeItemPO params) {
        return fastStarChallengeItemMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStarChallengeItemPO queryById(Integer id) {
        return fastStarChallengeItemMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStarChallengeItemPO queryOne(FastStarChallengeItemPO params) {
        return fastStarChallengeItemMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStarChallengeItemPO> queryList(FastStarChallengeItemPO params) {
        return fastStarChallengeItemMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStarChallengeItemPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStarChallengeItemPO> list = fastStarChallengeItemMapper.queryList(params);
        for (FastStarChallengeItemPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStarChallengeItemPO params) {
        return fastStarChallengeItemMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStarChallengeItemPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStarChallengeItemMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStarChallengeItemPO> list) {
        if (fastStarChallengeItemMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStarChallengeItemPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStarChallengeItemMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
