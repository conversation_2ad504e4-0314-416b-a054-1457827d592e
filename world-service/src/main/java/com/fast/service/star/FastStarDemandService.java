/*
 * Powered By fast.up
 */
package com.fast.service.star;

import com.fast.constant.StaticStr;
import com.fast.mapper.star.FastStarDemandMapper;
import com.fast.po.star.FastStarDemandPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastStarDemandService extends BaseService {

    @Autowired
    private FastStarDemandMapper fastStarDemandMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStarDemandPO queryById(FastStarDemandPO params) {
        return fastStarDemandMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStarDemandPO queryById(Integer id) {
        return fastStarDemandMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStarDemandPO queryOne(FastStarDemandPO params) {
        return fastStarDemandMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStarDemandPO> queryList(FastStarDemandPO params) {
        return fastStarDemandMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStarDemandPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStarDemandPO> list = fastStarDemandMapper.queryList(params);
        for (FastStarDemandPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStarDemandPO params) {
        return fastStarDemandMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStarDemandPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStarDemandMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStarDemandPO> list) {
        if (fastStarDemandMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStarDemandPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStarDemandMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
