/*
 * Powered By fast.up
 */
package com.fast.service.star;

import com.fast.constant.StaticStr;
import com.fast.mapper.star.FastStarChallengeMapper;
import com.fast.po.star.FastStarChallengePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastStarChallengeService extends BaseService {

    @Autowired
    private FastStarChallengeMapper fastStarChallengeMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStarChallengePO queryById(FastStarChallengePO params) {
        return fastStarChallengeMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStarChallengePO queryById(Integer id) {
        return fastStarChallengeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStarChallengePO queryOne(FastStarChallengePO params) {
        return fastStarChallengeMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStarChallengePO> queryList(FastStarChallengePO params) {
        return fastStarChallengeMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStarChallengePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStarChallengePO> list = fastStarChallengeMapper.queryList(params);
        for (FastStarChallengePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStarChallengePO params) {
        return fastStarChallengeMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStarChallengePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastStarChallengeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStarChallengePO> list) {
        if (fastStarChallengeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStarChallengePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastStarChallengeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
