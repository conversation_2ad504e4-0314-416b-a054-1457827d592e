/*
 * Powered By fast.up
 */
package com.fast.service.log;

import com.fast.constant.StaticStr;
import com.fast.mapper.log.FastLogVisitMapper;
import com.fast.po.log.FastLogVisitPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastLogVisitService extends BaseService {

    @Autowired
    private FastLogVisitMapper fastLogVisitMapper;

    /**
     * 通过id查询单个对象
     */
    public FastLogVisitPO queryById(FastLogVisitPO params) {
        return fastLogVisitMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLogVisitPO queryById(Integer id) {
        return fastLogVisitMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastLogVisitPO queryOne(FastLogVisitPO params) {
        return fastLogVisitMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastLogVisitPO> queryList(FastLogVisitPO params) {
        return fastLogVisitMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastLogVisitPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastLogVisitPO> list = fastLogVisitMapper.queryList(params);
        for (FastLogVisitPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastLogVisitPO params) {
        return fastLogVisitMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastLogVisitPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastLogVisitMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastLogVisitPO> list) {
        if (fastLogVisitMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastLogVisitPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastLogVisitMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
