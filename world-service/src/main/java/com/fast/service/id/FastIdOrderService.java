/*
 * Powered By fast.up
 */
package com.fast.service.id;

import com.fast.constant.StaticVar;
import com.fast.mapper.id.FastIdOrderMapper;
import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.po.id.FastIdOrderPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class FastIdOrderService extends BaseService {

    @Autowired
    private FastIdOrderMapper fastIdOrderMapper;
    @Autowired
    private FastMemberOrderRechargeMapper fastMemberOrderRechargeMapper;

    // 获取统一的订单id
    public Long getOrderId() {
        String key = StaticVar.ID_ORDER_NOT_EXIST;
        String res = RedisUtil.get(key);
        Long maxOrderId = 0L;
        if (StrUtil.isNotEmpty(res) && "yes".equals(res)) {
            maxOrderId = fastMemberOrderRechargeMapper.queryMaxId();
            if (maxOrderId == null) {
                maxOrderId = 1L;
            }
            maxOrderId = maxOrderId + 30;
            actionLogService.log("order_id_gen", "新产生订单id:" + maxOrderId);
        }
        // 新增一条总表id
        FastIdOrderPO idOrder = new FastIdOrderPO();
        if (maxOrderId > 0) {
            idOrder.setId(maxOrderId);
        }
        idOrder.setCreateTime(DateUtil.getNowDate());
        fastIdOrderMapper.insertSelective(idOrder);
        actionLogService.log("order_id_gen", "返回产生订单id:" + idOrder.getId());
        return idOrder.getId();
    }

}
