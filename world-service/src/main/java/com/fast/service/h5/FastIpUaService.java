/*
 * Powered By fast.up
 */
package com.fast.service.h5;

import com.fast.constant.StaticStr;
import com.fast.mapper.h5.FastIpUaMapper;
import com.fast.po.h5.FastIpUaPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.weibo.WeiboVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastIpUaService extends BaseService {

    @Autowired
    private FastIpUaMapper fastIpUaMapper;

    public MethodVO insertWeiboIpUa(WeiboVO params) {

        FastIpUaPO iuPO = new FastIpUaPO();
//    	iuPO.setIp(params.getIp());
        iuPO.setIp(params.getIpv4());
        iuPO.setUa(StrUtil.uaFormat(params.getUa()));
        iuPO.setClickId(params.getImp());
        iuPO.setLinkId(params.getLinkId());
        iuPO.setCreateTime(DateUtil.getNowDate());
        iuPO.setType(2);
        if (StrUtil.isNotEmpty(iuPO.getIp())) {
            if (fastIpUaMapper.insertSelective(iuPO) == 0) {
                return MethodVO.error("保存失败");
            }
        }
        return MethodVO.success();
    }

    /**
     * 通过id查询单个对象
     */
    public FastIpUaPO queryById(FastIpUaPO item) {
        return fastIpUaMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastIpUaPO queryById(Integer id) {
        return fastIpUaMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastIpUaPO queryOne(FastIpUaPO item) {
        return fastIpUaMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastIpUaPO> queryList(FastIpUaPO item) {
        return fastIpUaMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastIpUaPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastIpUaPO> list = fastIpUaMapper.queryList(item);
        for (FastIpUaPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastIpUaPO item) {
        return fastIpUaMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastIpUaPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setSysPhone(StrUtil.getSysPhone(item.getUa()));// 操作系统+手机型号
        item.setUa(StrUtil.getSubUa(item.getUa()));// 截取+加密
        if (fastIpUaMapper.insertSelective(item) == 0) {
            transactionRollBack();
            log.info("h5中间落地页参数保存失败");
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        log.info("h5中间落地页参数保存成功");
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastIpUaPO> list) {
        if (fastIpUaMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastIpUaPO item) {
        Date nowTime = DateUtil.getNowDate();
//        item.setUpdateTime(nowTime);
        if (fastIpUaMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
