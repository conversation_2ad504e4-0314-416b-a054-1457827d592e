/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.fee.FastFeeModelDetailMapper;
import com.fast.mapper.fee.FastFeeModelGearMapper;
import com.fast.mapper.promote.FastActivityClickMapper;
import com.fast.mapper.promote.FastActivityMapper;
import com.fast.mapper.promote.FastActivityPayMapper;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.po.fee.FastFeeModelGearPO;
import com.fast.po.promote.FastActivityClickPO;
import com.fast.po.promote.FastActivityPO;
import com.fast.po.promote.FastActivityPayPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.promote.FastActivityVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastActivityService extends BaseService {

    @Autowired
    private FastActivityMapper activityMapper;
    @Autowired
    private FastActivityPayMapper activityPayMapper;
    @Autowired
    private FastActivityClickMapper activityClickMapper;
    @Autowired
    private FastFeeModelDetailMapper feeModelDetailMapper;
    @Autowired
    private FastFeeModelGearMapper feeModelGearMapper;

    /**
     * 通过id查询单个对象
     */
    public FastActivityPO queryInfoByRedis(FastActivityPO item) {
        if (item.getId() == null) {
            return null;
        }
        FastActivityPO po;
        String key = StaticVar.ACTIVITY_INFO_ID + item.getId();
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            po = JsonUtil.toJavaObject(value, FastActivityPO.class);
        } else {
            item.setDelFlag(StaticVar.NO);
            po = activityMapper.queryById(item);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                po.setEncryptionId(encode(po.getId()));
                po.setEncryptionModelDetailId(encode(po.getModelDetailId()));
                RedisUtil.set(key, JsonUtil.toString(po), RedisUtil.TIME_2D);
            }
        }
        return po;
    }

    /**
     * 通过id查询单个对象
     */
    public FastActivityPO queryById(Integer id) {
        FastActivityPO itemParam = new FastActivityPO();
        itemParam.setId(id);
        return activityMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastActivityPO queryOne(FastActivityPO item) {
        return activityMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastActivityPO> queryList(FastActivityPO item) {
        return activityMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastActivityPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastActivityPO> list = activityMapper.queryList(item);
        for (FastActivityPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            cur.setEncryptionModelDetailId(encode(cur.getModelDetailId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询全部(分页)-按天
     */
    public ResultVO<?> getActivityData(FastActivityVO params, PageVO pageVO) {
        List<FastActivityVO> list = new ArrayList<>();

        // 查询点击事件
        FastActivityClickPO click = new FastActivityClickPO();
        click.setActivityId(params.getActivityId());
        click.setActivityTimeS(params.getActivityTimeS());
        click.setActivityTimeE(params.getActivityTimeE());
        startPage(pageVO);
        List<FastActivityClickPO> clickList = activityClickMapper.queryListGroupDate(click);

        // 查询支付订单
        FastActivityPayPO pay = new FastActivityPayPO();
        pay.setActivityId(params.getActivityId());
        pay.setActivityTimeS(params.getActivityTimeS());
        pay.setActivityTimeE(params.getActivityTimeE());
        pay.setState(1);
        startPage(pageVO);
        List<FastActivityPayPO> payList = activityPayMapper.queryListGroupDate(pay);

        // 组装数据
        doData(list, clickList, payList);
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询全部-按天
     */
    public List<FastActivityVO> getActivityData(FastActivityVO params) {
        List<FastActivityVO> list = new ArrayList<>();

        // 查询点击事件
        FastActivityClickPO click = new FastActivityClickPO();
        click.setActivityId(params.getActivityId());
        click.setActivityTimeS(params.getActivityTimeS());
        click.setActivityTimeE(params.getActivityTimeE());

        List<FastActivityClickPO> clickList = activityClickMapper.queryListGroupDate(click);

        // 查询支付订单
        FastActivityPayPO pay = new FastActivityPayPO();
        pay.setActivityId(params.getActivityId());
        pay.setActivityTimeS(params.getActivityTimeS());
        pay.setActivityTimeE(params.getActivityTimeE());
        pay.setState(1);

        List<FastActivityPayPO> payList = activityPayMapper.queryListGroupDate(pay);

        // 组装数据
        doData(list, clickList, payList);
        return list;
    }

    /**
     * 组装数据
     *
     * @param list
     * @param clickList
     * @param payList
     */
    private static void doData(List<FastActivityVO> list, List<FastActivityClickPO> clickList, List<FastActivityPayPO> payList) {
        if (CollUtil.hasContent(payList)) {
            Map<Integer, FastActivityPayPO> data = new HashMap<>();
            for (FastActivityPayPO cur : payList) {
                data.put(cur.getCreateDate(), cur);
            }
            for (FastActivityClickPO cur : clickList) {
                FastActivityVO vo = new FastActivityVO();
                vo.setActivityDate(DateUtil.format06To09Str(cur.getCreateDate()));
                vo.setActivityId(cur.getActivityId());
                vo.setEncryptionId(encode(cur.getActivityId()));
                vo.setClickMemberNum(cur.getClickCount());
                FastActivityPayPO payPO = data.get(cur.getCreateDate());
                if (payPO != null) {
                    vo.setRechargeMemberNum(payPO.getPayMemberCount());
                    vo.setRechargeNum(payPO.getPayCount());
                    vo.setRechargeMoney(payPO.getMoneyRecharge());
                } else {
                    vo.setRechargeMemberNum(0);
                    vo.setRechargeNum(0);
                    vo.setRechargeMoney(BigDecimal.ZERO);
                }
                list.add(vo);
            }
        }
    }

    /**
     * 查询全部(分页)-全部
     */
    public ResultVO<?> getActivityDataAll(FastActivityVO params, PageVO pageVO) {
        FastActivityVO vo = getActivityDataAll(params);
        return ResultVO.success(vo);
    }

    /**
     * 查询全部(分页)-全部
     */
    public FastActivityVO getActivityDataAll(FastActivityVO params) {
        FastActivityVO vo = new FastActivityVO();
        // 查询点击事件
        FastActivityClickPO click = new FastActivityClickPO();
        click.setActivityId(params.getActivityId());

        FastActivityClickPO clickPO = activityClickMapper.queryListGroupMember(click);
        // 查询支付订单
        FastActivityPayPO pay = new FastActivityPayPO();
        pay.setActivityId(params.getActivityId());
        pay.setState(1);
        FastActivityPayPO payPO = activityPayMapper.queryListGroupMember(pay);

        vo.setActivityId(clickPO.getActivityId());
        vo.setEncryptionId(encode(clickPO.getActivityId()));
        vo.setClickMemberNum(clickPO.getClickCount());

        if (payPO != null) {
            vo.setRechargeMemberNum(StrUtil.defaultIfBlank(payPO.getPayMemberCount(), 0));
            vo.setRechargeNum(StrUtil.defaultIfBlank(payPO.getPayCount(), 0));
            vo.setRechargeMoney(StrUtil.defaultIfBlank(payPO.getMoneyRecharge(), BigDecimal.ZERO));
        } else {
            vo.setRechargeMemberNum(0);
            vo.setRechargeNum(0);
            vo.setRechargeMoney(BigDecimal.ZERO);
        }
        return vo;
    }

    /**
     * 查询总数
     */
    public int queryCount(FastActivityPO item) {
        return activityMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastActivityPO params) {
        Date nowTime = DateUtil.getNowDate();
        FastFeeModelGearPO query = new FastFeeModelGearPO();
        query.setId(params.getModelGearId());
        FastFeeModelGearPO gear = feeModelGearMapper.queryById(query);
        if (gear == null) {
            throw new MyException("充值挡位不存在");
        }
        // 创建modelDetail
        FastFeeModelDetailPO modelDetail = new FastFeeModelDetailPO();
        BeanUtils.copyProperties(gear, modelDetail);
        modelDetail.setModelId(0);
        modelDetail.setType(params.getRechargeType());
        modelDetail.setGearId(gear.getId());
        modelDetail.setCreatorId(params.getCreatorId());
        modelDetail.setCreateTime(nowTime);
        modelDetail.setUpdateTime(null);
        modelDetail.setUpdatorId(null);
        if (feeModelDetailMapper.insertSelective(modelDetail) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        params.setModelDetailId(modelDetail.getId());
        params.setCreateTime(nowTime);
        if (activityMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastActivityPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (activityMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新-启用/禁用
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateState(FastActivityPO params) {
        FastActivityPO po = new FastActivityPO();
        Date nowTime = DateUtil.getNowDate();
        po.setUpdateTime(nowTime);
        po.setUpdatorId(params.getUpdatorId());
        po.setId(params.getId());
        po.setState(params.getState());
        if (activityMapper.updateById(po) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
