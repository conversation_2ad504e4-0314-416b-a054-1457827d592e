/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaService;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastStatisDataService extends BaseService {

    @Autowired
    private FastDramaService fastDramaService;
    @Autowired
    private FastMemberOrderRechargeMapper rechargeMapper;

    /**
     * 数据统计-充值汇总情况
     */
    public ResultVO<?> getRechargeAllSum(FastMemberOrderRechargePO item) {
        // 获得剧名
        FastDramaPO fastDramaPO = new FastDramaPO();
        fastDramaPO.setId(item.getDramaId());
        fastDramaPO = fastDramaService.queryOne(fastDramaPO);
        // 获得充值列表
        FastMemberOrderRechargePO itemOne = new FastMemberOrderRechargePO();
        itemOne.setDramaId(item.getDramaId());
        List<FastMemberOrderRechargePO> list = rechargeMapper.queryListRechargeAllSum(itemOne);
        LinkedHashMap<String, String[]> recCountArray1 = new LinkedHashMap<String, String[]>(100);
        LinkedHashMap<String, LinkedHashMap<String, String[]>> recMoneyCountArray1 = new LinkedHashMap<String, LinkedHashMap<String, String[]>>(100);
        // 循环处理充值列表
        Long mid = Long.parseLong("0");
        int recCount = 1;
        for (FastMemberOrderRechargePO fastMemberOrderRechargePO : list) {
            if (fastMemberOrderRechargePO.getMemberId().longValue() != mid.longValue()) {
                recCount = 1;
                mid = fastMemberOrderRechargePO.getMemberId();
            }
            if (recCountArray1.get("充值" + recCount + "次") != null) {
                String[] temStr = recCountArray1.get("充值" + recCount + "次");
                temStr[0] = (Integer.parseInt(temStr[0]) + 1) + "";
                recCountArray1.put("充值" + recCount + "次", temStr);
                LinkedHashMap<String, String[]> hashMap = recMoneyCountArray1.get("充值" + recCount + "次");
                if (hashMap.get(fastMemberOrderRechargePO.getMoneyRecharge().toString()) != null) {
                    String[] temStr1 = hashMap.get(fastMemberOrderRechargePO.getMoneyRecharge().toString());
                    temStr1[0] = (Integer.parseInt(temStr1[0]) + 1) + "";
                    hashMap.put(fastMemberOrderRechargePO.getMoneyRecharge().toString(), temStr1);
                } else {
                    String[] temStr1 = new String[2];
                    temStr1[0] = 1 + "";
                    hashMap.put(fastMemberOrderRechargePO.getMoneyRecharge().toString(), temStr1);
                }
                recMoneyCountArray1.put("充值" + recCount + "次", hashMap);
            } else {
                String[] temStr1 = new String[2];
                temStr1[0] = 1 + "";
                recCountArray1.put("充值" + recCount + "次", temStr1);
                LinkedHashMap<String, String[]> hashMap = new LinkedHashMap();
                String[] temStr2 = new String[2];
                temStr2[0] = 1 + "";
                hashMap.put(fastMemberOrderRechargePO.getMoneyRecharge().toString(), temStr2);
                recMoneyCountArray1.put("充值" + recCount + "次", hashMap);
            }
            recCount++;
        }
        for (Map.Entry<String, String[]> stringEntry : recCountArray1.entrySet()) {
            String[] oneStr = recCountArray1.get("充值1次");
            int oneInt = Integer.parseInt(oneStr[0]);
            int curInt = Integer.parseInt(stringEntry.getValue()[0]);
            String str = (new BigDecimal(curInt).divide(new BigDecimal(oneInt), 4, RoundingMode.HALF_DOWN).multiply(new BigDecimal(100))).setScale(2, RoundingMode.DOWN).toString() + "%";
            stringEntry.getValue()[1] = "充值比例：" + str;
        }

        for (Map.Entry<String, LinkedHashMap<String, String[]>> stringEntry : recMoneyCountArray1.entrySet()) {
            String[] oneStr = recCountArray1.get(stringEntry.getKey());
            int oneInt = Integer.parseInt(oneStr[0]);
            for (Map.Entry<String, String[]> entry : stringEntry.getValue().entrySet()) {
                int curInt = Integer.parseInt(entry.getValue()[0]);
                String str = (new BigDecimal(curInt).divide(new BigDecimal(oneInt), 4, RoundingMode.HALF_DOWN).multiply(new BigDecimal(100))).setScale(2, RoundingMode.DOWN).toString() + "%";
                entry.getValue()[1] = "档位占比：" + str;
            }
        }

        Map<String, Object> results = ResultVO.getMap();
        results.put("短剧名称", fastDramaPO.getDramaName());
        results.put("充值次数人数", recCountArray1);
        results.put("充值次数档位人数", recMoneyCountArray1);
        return ResultVO.success(results);
    }

    /**
     * 数据统计-剧集留存汇总情况
     */
    public ResultVO<?> getDramaSeriesAllSum(FastMemberRecentLogPO item) {

        // 获得剧名
        FastDramaPO fastDramaPO = new FastDramaPO();
        fastDramaPO.setId(item.getDramaId());
        fastDramaPO = fastDramaService.queryOne(fastDramaPO);

        // 获得留存列表
        FastMemberRecentLogPO itemOne = new FastMemberRecentLogPO();
        itemOne.setDramaId(item.getDramaId());
//        List<FastMemberOrderRechargePO> list = rechargeMapper.queryListRechargeAllSum(itemOne);
        LinkedHashMap<String, String[]> recCountArray1 = new LinkedHashMap<String, String[]>(100);
        LinkedHashMap<String, LinkedHashMap<String, String[]>> recMoneyCountArray1 = new LinkedHashMap<String, LinkedHashMap<String, String[]>>(100);


        Map<String, Object> results = ResultVO.getMap();
        results.put("短剧名称", fastDramaPO.getDramaName());
        results.put("充值次数人数", recCountArray1);
        results.put("充值次数档位人数", recMoneyCountArray1);
        return ResultVO.success(results);
    }
}
