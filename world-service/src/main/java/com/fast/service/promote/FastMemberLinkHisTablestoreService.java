/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.mapper.promote.FastMemberLinkHisTablestoreMapper;
import com.fast.po.promote.FastMemberLinkHisTablestorePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberLinkHisTablestoreService extends BaseService {

    @Autowired
    private FastMemberLinkHisTablestoreMapper fastMemberLinkHisTablestoreMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkHisTablestorePO queryById(FastMemberLinkHisTablestorePO item) {
        return fastMemberLinkHisTablestoreMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkHisTablestorePO queryById(Integer id) {
        return fastMemberLinkHisTablestoreMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberLinkHisTablestorePO queryOne(FastMemberLinkHisTablestorePO item) {
        return fastMemberLinkHisTablestoreMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberLinkHisTablestorePO> queryList(FastMemberLinkHisTablestorePO item) {
        return fastMemberLinkHisTablestoreMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberLinkHisTablestorePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberLinkHisTablestorePO> list = fastMemberLinkHisTablestoreMapper.queryList(item);
        for (FastMemberLinkHisTablestorePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberLinkHisTablestorePO item) {
        return fastMemberLinkHisTablestoreMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberLinkHisTablestorePO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberLinkHisTablestoreMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberLinkHisTablestorePO> list) {
        if (fastMemberLinkHisTablestoreMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberLinkHisTablestorePO item) {
        Date nowTime = DateUtil.getNowDate();
//        item.setUpdateTime(nowTime);
        if (fastMemberLinkHisTablestoreMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
