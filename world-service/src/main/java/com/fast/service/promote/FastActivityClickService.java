/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.promote.FastActivityClickMapper;
import com.fast.po.promote.FastActivityClickPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastActivityClickService extends BaseService {

    @Autowired
    private FastActivityClickMapper fastActivityClickMapper;

    /**
     * 通过id查询单个对象
     */
    public FastActivityClickPO queryById(FastActivityClickPO item) {
        return fastActivityClickMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastActivityClickPO queryById(Integer id) {
        FastActivityClickPO itemParam = new FastActivityClickPO();
        itemParam.setId(id);
        return fastActivityClickMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastActivityClickPO queryOne(FastActivityClickPO item) {
        return fastActivityClickMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastActivityClickPO> queryList(FastActivityClickPO item) {
        return fastActivityClickMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastActivityClickPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastActivityClickPO> list = fastActivityClickMapper.queryList(item);
        for (FastActivityClickPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastActivityClickPO item) {
        return fastActivityClickMapper.queryCount(item);
    }

    /**
     * 点击-推送到redis队列
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO click(FastActivityClickPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setCreateDate(DateUtil.format06Int(nowTime));
        RedisUtil.pushQueue(StaticVar.ACTIVITY_CLICK_LIST, JsonUtil.toString(item));

        return MethodVO.success();
    }

    private static final int CLICK_COUNT = 2000;

    /**
     * 点击-从redis队列拉取
     */
    @Transactional(rollbackFor = Exception.class)
    public void clickDb() {
        List<String> queue;
        do {
            queue = RedisUtil.popQueue(StaticVar.ACTIVITY_CLICK_LIST, CLICK_COUNT);
            if (CollUtil.isEmpty(queue)) {
                return;
            }
            List<FastActivityClickPO> list = JsonUtil.toList(queue, FastActivityClickPO.class);
            // 判断是否已经存在数据库(每个用户每天点击只计算一次)
            String key = "a_click:";
            List<FastActivityClickPO> addList = new ArrayList<>();
            for (FastActivityClickPO click : list) {
                String key2 = key + click.getActivityId() + "-" + click.getMemberId() + "-" + click.getCreateDate();
                if (isBlank(RedisUtil.get(key2))) {
                    addList.add(click);
                    RedisUtil.set(key2, "1", RedisUtil.TIME_2D);
                }
            }

            if (CollUtil.hasContent(addList)) {
                if (fastActivityClickMapper.insertBatch(addList) == 0) {
                    throw new MyException("从redis队列拉取促销活动点击数据失败");
                }
            }
        } while (queue.size() == CLICK_COUNT);
    }
}
