/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.mapper.promote.FastMemberLinkHisMapper;
import com.fast.po.promote.FastMemberLinkHisPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberLinkHisService extends BaseService {

    @Autowired
    private FastMemberLinkHisMapper memberLinkHisMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkHisPO queryById(FastMemberLinkHisPO item) {
        return memberLinkHisMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkHisPO queryById(Long id) {
        FastMemberLinkHisPO itemParam = new FastMemberLinkHisPO();
        itemParam.setId(id);
        return memberLinkHisMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberLinkHisPO queryOne(FastMemberLinkHisPO item) {
        return memberLinkHisMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberLinkHisPO> queryList(FastMemberLinkHisPO item) {
        return memberLinkHisMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberLinkHisPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberLinkHisPO> list = memberLinkHisMapper.queryList(item);
        for (FastMemberLinkHisPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberLinkHisPO item) {
        return memberLinkHisMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberLinkHisPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (memberLinkHisMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberLinkHisPO> list) {
        if (memberLinkHisMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberLinkHisPO item) {
        if (memberLinkHisMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
