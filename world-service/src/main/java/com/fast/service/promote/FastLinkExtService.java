/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.promote.FastLinkExtMapper;
import com.fast.po.promote.FastLinkExtPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastLinkExtService extends BaseService {

    @Autowired
    private FastLinkExtMapper fastLinkExtMapper;

    /**
     * 通过id查询单个对象
     */
    public FastLinkExtPO queryById(FastLinkExtPO params) {
        return fastLinkExtMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLinkExtPO queryById(Integer id) {
        return fastLinkExtMapper.queryById(id);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLinkExtPO queryRedisById(Integer id) {
        if (id == null || id <= 0) {
            return null;
        }
        FastLinkExtPO po;
        String key = StaticVar.LINK_EXT_INFO_ID + id;
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            po = JsonUtil.toJavaObject(value, FastLinkExtPO.class);
        } else {
            po = fastLinkExtMapper.queryById(id);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                RedisUtil.set(key, JsonUtil.toString(po), RedisUtil.TIME_2D);
            }
        }
        return po;
    }

    /**
     * 通过条件查询单个对象
     */
    public FastLinkExtPO queryOne(FastLinkExtPO params) {
        return fastLinkExtMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastLinkExtPO> queryList(FastLinkExtPO params) {
        return fastLinkExtMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastLinkExtPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastLinkExtPO> list = fastLinkExtMapper.queryList(params);
        // for (FastLinkExtPO cur : list) {
        // }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastLinkExtPO params) {
        return fastLinkExtMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastLinkExtPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastLinkExtMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastLinkExtPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastLinkExtMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
