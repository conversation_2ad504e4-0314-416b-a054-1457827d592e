/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.promote.FastRecolorSetMapper;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.promote.FastLinkRoadPO;
import com.fast.po.promote.FastRecolorSetPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastRecolorSetService extends BaseService {

    @Autowired
    private FastRecolorSetMapper fastRecolorSetMapper;
    @Autowired
    private FastLinkRoadService fastLinkRoadService;
    @Autowired
    private FastMiniMapper fastMiniMapper;

    /**
     * 通过id查询单个对象
     */
    public FastRecolorSetPO queryById(FastRecolorSetPO item) {
        return fastRecolorSetMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastRecolorSetPO queryById(Integer id) {
        return fastRecolorSetMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastRecolorSetPO queryOne(FastRecolorSetPO item) {
        return fastRecolorSetMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastRecolorSetPO> queryList(FastRecolorSetPO item) {
        return fastRecolorSetMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastRecolorSetPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastRecolorSetPO> list = fastRecolorSetMapper.queryList(item);
        for (FastRecolorSetPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        if (item.getType() != null && item.getType() == 1 && list.size() < 1) {
            // 全局初始化
            FastRecolorSetPO itemParam = new FastRecolorSetPO();
            itemParam.setType(1);
            List<FastRecolorSetPO> listAll = fastRecolorSetMapper.queryList(itemParam);
            if (listAll.size() == 0) {
                Date nowTime = DateUtil.getNowDate();
                for (int i = 1; i < 7; i++) {
                    FastRecolorSetPO rsPO = new FastRecolorSetPO();
                    rsPO.setAppType(i);
                    rsPO.setCreateTime(nowTime);
                    rsPO.setUpdateTime(nowTime);
                    rsPO.setCreatorId(item.getCreatorId());
                    rsPO.setUpdatorId(item.getCreatorId());
                    rsPO.setRecolorHours(24);
                    rsPO.setType(1);
                    rsPO.setContentType(item.getContentType());
                    fastRecolorSetMapper.insertSelective(rsPO);
                }
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastRecolorSetPO item) {
        return fastRecolorSetMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastRecolorSetPO item) {
        if (item.getType() == 2) {
            // 查询链路
            FastLinkRoadPO roadPO = fastLinkRoadService.queryById(item.getRoadId());
            if (roadPO == null) {
                return MethodVO.error("链路不存在");
            }
            item.setAppType(roadPO.getAppType());
            item.setBridge(roadPO.getFansType());
            item.setMediaType(roadPO.getMediaType());
            // 判断是否已经存在
            FastRecolorSetPO setParam = new FastRecolorSetPO();
            setParam.setContentType(item.getContentType());
            setParam.setRoadId(item.getRoadId());
            setParam.setType(item.getType());
            FastRecolorSetPO setPO = fastRecolorSetMapper.queryOne(setParam);
            if (setPO != null) {
                return MethodVO.error("已经存在");
            }
            Date nowTime = DateUtil.getNowDate();
            item.setCreateTime(nowTime);
            item.setUpdateTime(nowTime);
            if (fastRecolorSetMapper.insertSelective(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
        } else if (item.getType() == 3) {
            List<String> appIdList = StrUtil.getStrListFromStr(item.getAppIds());
            Date nowTime = DateUtil.getNowDate();
            item.setCreateTime(nowTime);
            item.setUpdateTime(nowTime);
            for (String appId : appIdList) {
                FastMiniPO mParam = new FastMiniPO();
                mParam.setAppId(appId);
                FastMiniPO mPO = fastMiniMapper.queryOne(mParam);
                item.setAppType(mPO.getType());
                // 判断是否已经存在
                FastRecolorSetPO setParam = new FastRecolorSetPO();
                setParam.setAppId(appId);
                setParam.setType(item.getType());
                FastRecolorSetPO setPO = fastRecolorSetMapper.queryOne(setParam);
                if (setPO != null) {
                    transactionRollBack();
                    return MethodVO.error("【" + mPO.getMiniName() + "】已经存在");
                }
                // 新增
                item.setAppId(appId);
                if (fastRecolorSetMapper.insertSelective(item) == 0) {
                    transactionRollBack();
                    return MethodVO.error("【" + mPO.getMiniName() + "】" + StaticStr.ADD_FAILED);
                }
            }
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastRecolorSetPO> list) {
        if (fastRecolorSetMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastRecolorSetPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastRecolorSetMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
