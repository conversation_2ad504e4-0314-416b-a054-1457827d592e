/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.mapper.promote.FastLinkRoadMapper;
import com.fast.po.promote.FastLinkRoadPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastLinkRoadService extends BaseService {

    @Autowired
    private FastLinkRoadMapper fastLinkRoadMapper;

    /**
     * 通过id查询单个对象
     */
    public FastLinkRoadPO queryById(FastLinkRoadPO item) {
        return fastLinkRoadMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLinkRoadPO queryById(Integer id) {
        return fastLinkRoadMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastLinkRoadPO queryOne(FastLinkRoadPO item) {
        return fastLinkRoadMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastLinkRoadPO> queryList(FastLinkRoadPO item) {
        return fastLinkRoadMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastLinkRoadPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastLinkRoadPO> list = fastLinkRoadMapper.queryList(item);
        for (FastLinkRoadPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询全部名称
     */
    public Map<Integer, String> queryLinkRoadName(FastLinkRoadPO item) {
        List<FastLinkRoadPO> list = fastLinkRoadMapper.queryLinkRoadName(item);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, String> map = new HashMap<>();
        list.forEach(cur -> map.put(cur.getId(), cur.getRoadName()));
        return map;
    }

    /**
     * 查询总数
     */
    public int queryCount(FastLinkRoadPO item) {
        return fastLinkRoadMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastLinkRoadPO item) {
        FastLinkRoadPO rParam = new FastLinkRoadPO();
        rParam.setAppType(item.getAppType());
        rParam.setMediaType(item.getMediaType());
        rParam.setAdvMode(item.getAdvMode());
        rParam.setFansType(item.getFansType());
        rParam.setContentType(item.getContentType());
        rParam.setFeeFlag(item.getFeeFlag());
        FastLinkRoadPO rPO = fastLinkRoadMapper.queryOne(rParam);
        if (rPO != null) {
            return MethodVO.error("添加失败，链路已经存在");
        }
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastLinkRoadMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastLinkRoadPO> list) {
        if (fastLinkRoadMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastLinkRoadPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastLinkRoadMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
