/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.alibaba.fastjson.JSONObject;
import com.fast.annotation.Master;
import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.datalake.DataLakeUnlockStartLogMapper;
import com.fast.mapper.promote.FastLinkMapper;
import com.fast.mapper.promote.FastMemberLinkMapper;
import com.fast.mapper.promote.FastStatisLinkFreeMapper;
import com.fast.mapper.promote.FastStatisLinkMapper;
import com.fast.mapper.statis.FastStatisLinkPvMapper;
import com.fast.mq.MQStatisLinkHisProvider;
import com.fast.mq.MQStatisLinkProvider;
import com.fast.po.drama.FastDramaSeriesPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.promote.FastMemberLinkPO;
import com.fast.po.promote.FastStatisLinkFreePO;
import com.fast.po.promote.FastStatisLinkPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaSeriesService;
import com.fast.service.drama.FastDramaService;
import com.fast.utils.*;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.drama.FastDramaVO;
import com.fast.vo.promote.FastLinkQueryVO;
import com.fast.vo.promote.FastStatisLinkVO;
import com.fast.vo.statistics.LinkStatisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static com.fast.utils.DoubleUtil.DEF_SCALE_2;

/**
 * <AUTHOR>
 */
@Service
public class FastStatisLinkService extends BaseService {

    @Autowired
    private FastStatisLinkMapper fastStatisLinkMapper;
    @Autowired
    private FastLinkMapper fastLinkMapper;
    @Autowired
    private FastMemberLinkMapper fastMemberLinkMapper;
    @Autowired
    private FastStatisLinkFreeMapper fastStatisLinkFreeMapper;
    @Autowired
    private DataLakeUnlockStartLogMapper dataLakeUnlockStartLogMapper;
    @Autowired
    private FastStatisLinkPvMapper fastStatisLinkPvMapper;
    @Lazy
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastDramaService fastDramaService;
    @Autowired
    private FastDramaSeriesService fastDramaSeriesService;
    @Resource
    private MQStatisLinkProvider mqStatisLinkProvider;
    @Resource
    private MQStatisLinkHisProvider mqStatisLinkHisProvider;

    /**
     * 根据日期查询链接id
     */
    public Set<Integer> queryStaticDataLinkIds(String statisDate) {
        return fastStatisLinkMapper.queryStaticDataLinkIds(statisDate);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStatisLinkPO queryById(FastStatisLinkPO item) {
        return fastStatisLinkMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStatisLinkPO queryById(Integer id) {
        FastStatisLinkPO itemParam = new FastStatisLinkPO();
        itemParam.setId(id);
        return fastStatisLinkMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStatisLinkPO queryOne(FastStatisLinkPO item) {
        return fastStatisLinkMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastStatisLinkPO> queryList(FastStatisLinkPO item) {
        return fastStatisLinkMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStatisLinkPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastStatisLinkPO> list = fastStatisLinkMapper.queryList(item);
        for (FastStatisLinkPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStatisLinkPO item) {
        return fastStatisLinkMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStatisLinkPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastStatisLinkMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStatisLinkPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastStatisLinkMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新成本
     */
    public MethodVO updateCost(FastStatisLinkPO item) {
        FastStatisLinkPO fastStatisLinkPO = new FastStatisLinkPO();
        fastStatisLinkPO.setId(item.getId());
        fastStatisLinkPO.setCostDay(item.getCostDay());
        fastStatisLinkPO.setUpdateTime(DateUtil.getNowDate());
        if (fastStatisLinkMapper.updateById(fastStatisLinkPO) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        // 刷新链接统计数据
        // 刷新选中记录的统计数据
        statisLinkPlus(item.getStatisDate(), item.getLinkId(), 1);
        // 刷新今天的统计数据
        String nowDate = DateUtil.getDateStr("yyyy-MM-dd", DateUtil.getNowDate());
        if (!item.getStatisDate().equals(nowDate)) {
            statisLinkPlus(nowDate, item.getLinkId(), 2);
        }

        return MethodVO.success();
    }

    /**
     * 弃用
     *
     * @param fastStatisLink
     * @param date
     */
    public void updateDay60Cost(FastStatisLinkPO fastStatisLink, Date date) {
        List<FastStatisLinkPO> fastStatisLinkList = new ArrayList<>();
        List<FastStatisLinkPO> fastStatisLinkCostList = fastStatisLinkMapper.queryListCostPlus(fastStatisLink);
        Map<String, FastStatisLinkPO> day60Map = new HashMap<>();
        for (FastStatisLinkPO fastStatisLinkCost : fastStatisLinkCostList) {
            // log.info(toJSONString(fastStatisLinkCost));
            day60Map.put(fastStatisLinkCost.getStatisDate(), fastStatisLinkCost);
        }
        // log.info(toJSONString(day60Map));
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 61; i++) {
            String dateFormat9 = DateUtil.format09(date);
            // log.info(dateFormat9);
            if (day60Map.containsKey(dateFormat9)) {
                FastStatisLinkPO fastStatisLinkInfo = day60Map.get(dateFormat9);
                if (fastStatisLinkInfo.getCostDayPlus() != null) {
                    sb.insert(0, fastStatisLinkInfo.getCostDayPlus() + ",");
                } else {
                    sb.insert(0, "0,");
                }
                StringBuilder separatorSymbol = new StringBuilder();
                for (int j = 0; j < 59 - i; j++) {
                    separatorSymbol.append(",");
                }
                FastStatisLinkPO fastStatisLinkCost = new FastStatisLinkPO();
                // log.info(toJSONString(fastStatisLinkInfo));
                fastStatisLinkCost.setId(fastStatisLinkInfo.getId());
                fastStatisLinkCost.setMoneyD60CostPlus(String.valueOf(sb.append(separatorSymbol)));
                fastStatisLinkList.add(fastStatisLinkCost);
            }

            date = DateUtil.addDays(date, -1);
        }
        // log.info(toJSONString(fastStatisLinkList));
        // long start = System.currentTimeMillis();

        // log.info("|--弃用更新moneyD60CostPlus，后续如果启用需进行批量更新========");
        // 批量更新逻辑开始
        // 批量更新逻辑结束
    }

    public Integer getRetailIdByLinkId(Integer linkId) {
        if (linkId == null) {
            return 0;
        }
        String key = StaticVar.LINK_RETAIL + linkId;
        String retailIdStr = RedisUtil.get(key);
        if (StrUtil.isEmpty(retailIdStr)) {
            FastLinkPO linkParam = new FastLinkPO();
            linkParam.setId(linkId);
            FastLinkPO linkPO = fastLinkMapper.queryById(linkParam);
            if (linkPO != null) {
                retailIdStr = linkPO.getRetailId().toString();
            } else {
                retailIdStr = "0";
            }
            RedisUtil.set(key, retailIdStr, RedisUtil.ONE_DAY);
        }
        return Integer.valueOf(retailIdStr);
    }

    private static final long time = 3 * 24 * 3600 * 1000L;

    /**
     * 生成指定日期的链接统计数据。2.0版本
     *
     * @param statisDate 统计日期 yyyy-MM-dd
     */
    public void statisLinkPlus(final String statisDate, final int linkId, final Integer invokeId) {
        String key = StaticVar.LOCK_STATIS_LINK_DATE + statisDate + "_" + linkId;
        // 此处锁不做释放,保证相同渠道在指定时间内不能重复被统计
        final JedisLock lock = new JedisLock(key, 100, 60_000 * 5, false);
        try {
            if (!lock.lock()) {
                // log.info("链接统计重复,放弃处理,key:{}", key);
                return;
            }
            Date nowDate = DateUtil.getNowDate();
            long nowTime = nowDate.getTime();
            FastStatisLinkPO slParam = new FastStatisLinkPO();
            slParam.setLinkId(linkId);
            slParam.setStatisDate(statisDate);
            FastStatisLinkPO slPO = queryOne(slParam);
            if (slPO == null) {
                // 新增
                slPO = new FastStatisLinkPO();
                slPO.setRetailId(getRetailIdByLinkId(linkId));
                slPO.setLinkId(linkId);
                slPO.setStatisDate(statisDate);
                slPO.setCreateTime(nowDate);
            }
            // 0=默认，1=星图推广，2=小程序推广，3=作品挂载，4=聚星推广;5=微信视频号
            Integer linkSubType = defaultIfNull(fastLinkService.queryLinkSubTypeByIdRedis(linkId), 0);
            // 两天内，统计相关数据（改成1天，减少数据湖访问次数）
            if (DateUtil.daysBetweenUp(DateUtil.format09(statisDate), nowDate) <= 1) {
                // 当日新增用户数，通过此链接当天新增的人数（包括被染色用户）
                FastStatisLinkVO slVO = new FastStatisLinkVO();
                slVO.setLastLinkId(linkId);
                slVO.setStatisDate(statisDate);
                if (biggerZero(linkSubType) && linkSubType == 5) {
                    slPO.setNumDay(fastStatisLinkMapper.querySubLinkNumDayPlus(slVO));
                } else {
                    slPO.setNumDay(fastStatisLinkMapper.queryNumDayPlus(slVO));
                }
                // 当日-充值人数、充值笔数、充值金额，普通充值人数、笔数、金额，用户充值人数、笔数、金额（link下所有用户）
                FastStatisLinkVO numAllDayRech = fastStatisLinkMapper.queryNumAllDayRechPlus(slVO);
                slPO.setNumAllDayRech(numAllDayRech.getNumAllDayRech());
                slPO.setNumAllDayOrder(numAllDayRech.getNumAllDayOrder());
                slPO.setNumAllDayCommonRech(numAllDayRech.getNumAllDayCommonRech());
                slPO.setNumAllDayCommonOrder(numAllDayRech.getNumAllDayCommonOrder());
                slPO.setNumAllDayVipRech(numAllDayRech.getNumAllDayVipRech());
                slPO.setNumAllDayVipOrder(numAllDayRech.getNumAllDayVipOrder());

                slPO.setMoneyAllDay(numAllDayRech.getMoneyAllDay());
                slPO.setMoneyProfitAllDay(numAllDayRech.getMoneyProfitAllDay());

                slPO.setMoneyAllDayCommon(numAllDayRech.getMoneyAllDayCommon());
                slPO.setMoneyProfitAllDayCommon(numAllDayRech.getMoneyProfitAllDayCommon());

                slPO.setMoneyAllDayVip(numAllDayRech.getMoneyAllDayVip());
                slPO.setMoneyProfitAllDayVip(numAllDayRech.getMoneyProfitAllDayVip());

                FastStatisLinkVO slVO2 = new FastStatisLinkVO();
                slVO2.setLastLinkId(linkId);
                slVO2.setStatisDate(statisDate);
                // 所有新增用户累计-充值人数、充值笔数、充值金额
                FastStatisLinkVO numAllRech = null;
                if (StaticVar.LIULINGYI_PLATFORM == 1) {
//                	numAllRech = fastStatisLinkMapper.queryNumAllRechSimple(slVO2);
                    String keyLake = StaticVar.LAKE_REDIS + linkId + "_" + statisDate;
                    String resLake = RedisUtil.get(keyLake);
                    if (StrUtil.isNotEmpty(resLake)) {
                        numAllRech = JsonUtil.toJavaObject(resLake, FastStatisLinkVO.class);
                    }
                    if (numAllRech == null) {
                        numAllRech = new FastStatisLinkVO();
                        numAllRech.setNumAllRech(0);
                        numAllRech.setNumAllOrder(0);
                        numAllRech.setMoneyAll(BigDecimal.ZERO);
                        numAllRech.setMoneyProfitAll(BigDecimal.ZERO);
                    }
                    actionLogService.log("statis_rech_all", statisDate + "_" + linkId + "_" + JsonUtil.toString(numAllRech));
                } else {
                    numAllRech = fastStatisLinkMapper.queryNumAllRech(slVO2);
                    actionLogService.log("statis_rech_all", "______" + JsonUtil.toString(numAllRech));
                }
                slPO.setNumAllRech(numAllRech.getNumAllRech());
                slPO.setNumAllOrder(numAllRech.getNumAllOrder());
                slPO.setMoneyAll(numAllRech.getMoneyAll());
                slPO.setMoneyProfitAll(numAllRech.getMoneyProfitAll());
            }

            FastStatisLinkVO slvParam = new FastStatisLinkVO();
            slvParam.setLastLinkId(linkId);
            slvParam.setStatisDate(statisDate);
            // 当日新增用户-充值人数、充值笔数、充值金额
            FastStatisLinkVO numDayRech = fastStatisLinkMapper.queryNumDayRech(slvParam);
            slPO.setNumDayRech(numDayRech.getNumDayRech());
            slPO.setNumDayOrder(numDayRech.getNumDayOrder());
            slPO.setMoneyDay(numDayRech.getMoneyDay());
            slPO.setMoneyProfitDay(numDayRech.getMoneyProfitDay());
            slPO.setUpdateTime(nowDate);

            // D0-D60充值人数、充值金额
            String[] moneyD60Rech;
            String[] moneyProfitD60Rech;
            String[] numD60Rech;
            if (StrUtil.isNotEmpty(slPO.getMoneyD60Rech())) {
                moneyD60Rech = StrUtil.splitN(slPO.getMoneyD60Rech(), ",");
                numD60Rech = StrUtil.splitN(slPO.getNumD60Rech(), ",");
            } else {
                moneyD60Rech = new String[61];
                numD60Rech = new String[61];
            }
            if (StrUtil.isNotEmpty(slPO.getMoneyProfitD60Rech())) {
                moneyProfitD60Rech = StrUtil.splitN(slPO.getMoneyProfitD60Rech(), ",");
            } else {
                moneyProfitD60Rech = new String[61];
            }
            if (moneyD60Rech.length < 61) {
                log.error("链接统计 moneyD60Rech 小于 61:{}", Arrays.toString(moneyD60Rech));
                log.error("链接统计 moneyProfitD60Rech 小于 61:{}", Arrays.toString(moneyProfitD60Rech));
                return;
            }
            for (int i = 0; i < 61; i++) {
                FastStatisLinkVO dFastStatisLinkVO = new FastStatisLinkVO();
                dFastStatisLinkVO.setLastLinkId(linkId);
                dFastStatisLinkVO.setStatisDate(statisDate);
                boolean calFlag = false;
                String dnDate = DateUtil.format09(DateUtil.dateAdd(DateUtil.format09(statisDate), i));
                long dnDateTime = DateUtil.format07(dnDate + " 00:00:00").getTime();
                // Dn日期小于等于现在日期
                if (dnDateTime < nowTime) {
                    // 大于等于 3天前的时间，才计算
                    if (dnDateTime > (nowTime - time)) {
                        calFlag = true;
                    }
                    if (StrUtil.isEmpty(moneyD60Rech[i])) {
                        calFlag = true;
                    }
                    if (StrUtil.isEmpty(moneyProfitD60Rech[i]) || StaticVar.STR_000.equals(moneyProfitD60Rech[i]) || StaticVar.STR_0.equals(moneyProfitD60Rech[i])) {
                        calFlag = true;
                    }
                }
                if (calFlag) {
                    dFastStatisLinkVO.setDnDate(dnDate);
                    FastStatisLinkVO rech = fastStatisLinkMapper.queryMoneyD60Rech(dFastStatisLinkVO);
                    if (rech != null) {
                        // 如果当日用户没有充值，设置金额和人数为0
                        if (rech.getMoneyDay() == null) {
                            rech.setMoneyDay(BigDecimal.ZERO);
                        }
                        if (rech.getMoneyProfitDay() == null) {
                            rech.setMoneyProfitDay(BigDecimal.ZERO);
                        }
                        if (rech.getNumDayRech() == null) {
                            rech.setNumDayRech(0);
                        }
                        if (rech.getMoneyDay() != null) {
                            moneyD60Rech[i] = rech.getMoneyDay().setScale(DEF_SCALE_2, RoundingMode.HALF_UP).toString();
                            moneyProfitD60Rech[i] = rech.getMoneyProfitDay().setScale(DEF_SCALE_2, RoundingMode.HALF_UP).toString();
                            numD60Rech[i] = rech.getNumDayRech().toString();
                        }
                    }
                }
            }
            slPO.setMoneyD60Rech(StrUtil.joinArr(moneyD60Rech));
            slPO.setMoneyProfitD60Rech(StrUtil.joinArr(moneyProfitD60Rech));
            slPO.setNumD60Rech(StrUtil.joinArr(numD60Rech));
            // slPO.setMoneyD60Cost(getMoneyD60Cost(statisDate, linkId));
            FastStatisLinkVO moneyDAllRech = fastStatisLinkMapper.queryMoneyDAllRech(slvParam);
            slPO.setMoneyDAllRech(defaultIfNull(moneyDAllRech.getMoneyDAllRech(), BigDecimal.ZERO));
            slPO.setMoneyProfitDAllRech(defaultIfNull(moneyDAllRech.getMoneyProfitDAllRech(), BigDecimal.ZERO));
            slPO.setNumDAllRech(defaultIfNull(moneyDAllRech.getNumDAllRech(), 0));

            // 累计新增用户数/累计成本
            FastStatisLinkVO numAll = fastStatisLinkMapper.queryNumAll(slvParam);
            if (numAll != null) {
                slPO.setNumAll(numAll.getNumAll());
                slPO.setCostAll(numAll.getCostAll());
            }
            // 新建或更新链接统计数据
            if (slPO.getId() != null) {
                slPO.setUpdateTime(nowDate);
                // 更新
                if (fastStatisLinkMapper.updateById(slPO) == 0) {
                    log.error("链接统计更新失败,key:{}", key);
                }
            } else {
                // 新增
                slPO.setCreateTime(nowDate);
                if (fastStatisLinkMapper.insertSelective(slPO) == 0) {
                    log.error("链接统计新增失败,key:{}", key);
                }
            }
            long costTime = System.currentTimeMillis() - nowTime;
            log.info("链接统计完成 key:{};耗时:{}ms", key, costTime);
            // 基于上面的链接统计数据，更新累计新增用户数/累计成本
        } catch (Exception e) {
            log.error("链接统计失败 error:", e);
        }
    }

    /**
     * 订单汇总数据从湖同步到redis
     */
    public void synDataLakeToRedisJob() {
        FastStatisLinkVO slParam = new FastStatisLinkVO();
        slParam.setStatisDate(DateUtil.format09(DateUtil.beginOfDay(DateUtil.getNowDate())));
        Integer linkId = 1;
        Integer limit = 5000;
        boolean loop = true;
        Integer count = 0;
        slParam.setLimit(limit);
        Integer countSuccess = 0;
        while (loop && count < 80) {
            count++;
            slParam.setLinkId(linkId);
            List<FastStatisLinkVO> list = fastStatisLinkMapper.queryNumAllRechList(slParam);
            for (FastStatisLinkVO item : list) {
                // 设置缓存
                String key = StaticVar.LAKE_REDIS + item.getLinkId() + "_" + DateUtil.format09(item.getReportDate());
                RedisUtil.set(key, JsonUtil.toString(item), 60 * 30);
                if (item.getLinkId() > linkId) {
                    linkId = item.getLinkId();
                }
                countSuccess++;
            }
            log.info("数据湖同步到缓存，数量=" + countSuccess);
            if (list.size() < limit) {
                loop = false;
                log.info("数据湖同步到缓存-完成，数量=" + countSuccess);
            }
        }

    }

    /**
     * 更新累计新增用户数/累计成本
     */
    @Master
    public void updateNumAll(FastStatisLinkPO result) {
        FastStatisLinkPO fastStatisLinkPO = new FastStatisLinkPO();
        fastStatisLinkPO.setStatisDate(result.getStatisDate());
        fastStatisLinkPO.setLinkId(result.getLinkId());
        FastStatisLinkPO queryResult = queryOne(fastStatisLinkPO);
        if (queryResult != null) {
            // 累计新增用户数/累计成本
            FastStatisLinkVO fastStatisLinkVO = new FastStatisLinkVO();
            fastStatisLinkVO.setLastLinkId(result.getLinkId());
            fastStatisLinkVO.setStatisDate(result.getStatisDate());
            FastStatisLinkVO numAll = fastStatisLinkMapper.queryNumAll(fastStatisLinkVO);
            if (numAll != null) {
                FastStatisLinkPO updatefastStatisLinkPO = new FastStatisLinkPO();
                updatefastStatisLinkPO.setId(queryResult.getId());
                updatefastStatisLinkPO.setNumAll(numAll.getNumAll());
                updatefastStatisLinkPO.setCostAll(numAll.getCostAll());
                fastStatisLinkMapper.updateById(updatefastStatisLinkPO);
            }
        }

    }

    /**
     * statisDate 统计日期 yyyy-MM-dd
     *
     * @return
     */
    private String getMoneyD60Cost(String statisDate, Integer linkId) {
        log.info("查询60天消耗" + statisDate + "," + linkId);
        // 查询缓存，通过缓存，获取消耗(在有消耗更新时，此缓存会被删除)
        String key = StaticVar.STATIS_COST + linkId;
        Map<String, BigDecimal> costMap = RedisUtil.getObject(key, HashMap.class);
        if (costMap == null) {
            costMap = new HashMap<>();
            FastStatisLinkPO slParam = new FastStatisLinkPO();
            slParam.setLinkId(linkId);
            List<FastStatisLinkPO> linkList = queryList(slParam);
            for (FastStatisLinkPO item : linkList) {
                costMap.put(item.getStatisDate(), item.getCostDay());
            }
            RedisUtil.setObject(key, costMap, 60 * 10);
        }
        // 开始组装数据
        StringBuffer sb = new StringBuffer();
        Date StartDate = DateUtil.format09(statisDate);// 开始日期
        int statisDays = 61;
        for (int i = 0; i < statisDays; i++) {
            Date curDate = DateUtil.addDays(StartDate, i);
            if (!curDate.after(DateUtil.getNowDate())) {
                String curDateStr = DateUtil.format09(curDate);
                BigDecimal curCostDay = costMap.get(curDateStr);
                if (curCostDay == null) {
                    curCostDay = BigDecimal.ZERO;
                }
                sb.append(curCostDay);
            }
            if (i < statisDays - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    /**
     * 新增或存在更新
     */
    @Master
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertOrUpdate(FastStatisLinkPO item) {
        FastStatisLinkPO fastStatisLinkPO = new FastStatisLinkPO();
        fastStatisLinkPO.setStatisDate(item.getStatisDate());
        fastStatisLinkPO.setLinkId(item.getLinkId());
        FastStatisLinkPO result = queryOne(fastStatisLinkPO);
        if (result != null) {
            // 执行更新
            // 当日新增用户
            result.setNumDay(item.getNumDay());
            // 当日新增用户-充值人数、充值笔数、充值金额
            result.setNumDayRech(item.getNumDayRech());
            result.setNumDayOrder(item.getNumDayOrder());
            result.setMoneyDay(item.getMoneyDay());
            ////所有新增用户当日-充值人数、充值笔数、充值金额，普通充值人数、笔数、金额，用户充值人数、笔数、金额
            result.setNumAllDayRech(item.getNumAllDayRech());
            result.setNumAllDayOrder(item.getNumAllDayOrder());
            result.setNumAllDayCommonRech(item.getNumAllDayCommonRech());
            result.setNumAllDayCommonOrder(item.getNumAllDayCommonOrder());
            result.setNumAllDayVipRech(item.getNumAllDayVipRech());
            result.setNumAllDayVipOrder(item.getNumAllDayVipOrder());
            result.setMoneyAllDay(item.getMoneyAllDay());
            result.setMoneyAllDayCommon(item.getMoneyAllDayCommon());
            result.setMoneyAllDayVip(item.getMoneyAllDayVip());
            // 新增用户累计-充值人数、充值笔数、充值金额
            result.setNumAllRech(item.getNumAllRech());
            result.setNumAllOrder(item.getNumAllOrder());
            result.setMoneyAll(item.getMoneyAll());
            // 累计新增用户数/累计成本
            result.setNumAll(item.getNumAll());
            result.setCostAll(item.getCostAll());
            // result.setCostDay(item.getCostDay());
            // D0-D60充值人数、充值金额
            result.setMoneyD60Rech(item.getMoneyD60Rech());
            result.setNumD60Rech(item.getNumD60Rech());
            result.setMoneyD60Cost(item.getMoneyD60Cost());

            //"moneyDAllRech:统计日期新增用户-累计充值金额",
            //"numDAllRech:统计日期新增用户--累计充值用户数",
            result.setMoneyDAllRech(item.getMoneyDAllRech());
            result.setNumDAllRech(item.getNumDAllRech());

            result.setUpdateTime(DateUtil.getNowDate());
            // 不更新linkId和statisdate
            result.setLinkId(null);
            result.setStatisDate(null);
            if (fastStatisLinkMapper.updateById(result) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }

        } else {
            // 新增
            Date nowTime = DateUtil.getNowDate();
            item.setCreateTime(nowTime);
            if (fastStatisLinkMapper.insertSelective(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
        }
        return MethodVO.success();
    }

    /**
     * 发送mq消息:链接数据统计 - 15天之内链接，5分钟统计一次
     */
    public void sendMesForStatisLink_bak() {
        Date now = DateUtil.getNowDate();
        List<JSONObject> sendList = new ArrayList<>();
        int limit = 0;
        int count = 0;
        // 循环处理推广链接表中链接
        FastLinkQueryVO queryVO = new FastLinkQueryVO();
        queryVO.setBeginTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", DateUtil.beginOfDay(DateUtil.dateAdd(now, -15))));
        queryVO.setEndTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", now));
        List<FastLinkPO> list = fastLinkMapper.queryLinkIdsForStatisV1(queryVO);
        for (FastLinkPO fastLinkPO : list) {
            limit++;
            count++;
            // 统计近10天各个链接数据  todo
            for (int i = 0; i < 10; i++) {
                JSONObject params = new JSONObject();
                String dnDate = DateUtil.getDateStr("yyyy-MM-dd", DateUtil.dateAdd(now, -i));
                // statisType  1-推广链接数据统计 2.待扩展
                params.put("statisType", 1);
                params.put("statisDate", dnDate);
                params.put("linkId", fastLinkPO.getId());
                sendList.add(params);
            }
            if (limit == 10 || count == list.size()) {
                mqStatisLinkProvider.sendExpMes(JsonUtil.toString(sendList), "400000");// 400秒未处理则过期清除
                limit = 0;
                sendList = new ArrayList<>();
                // 每次最多传递10个，休息2秒
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void statisLinkPlusJob() {
        // 24小时内有用户的
        // CalTime calTime = new CalTime();
        // Double random = Math.random();
        Date now = DateUtil.getNowDate();
        FastMemberLinkPO mlParam = new FastMemberLinkPO();
        mlParam.setLastLinkTime(DateUtil.addHours(now, -24));
        List<FastMemberLinkPO> mlList = fastMemberLinkMapper.queryActiveLinkList(mlParam);
        // actionLogService.log("job_statis_link_1123", random + "待同步" + mlList.size() + "个链接");
        String statisDate = DateUtil.format09(now);
        for (FastMemberLinkPO mlink : mlList) {
            statisLinkPlus(statisDate, mlink.getLastLinkId(), 9);
        }
    }

    /**
     * 发送mq消息:链接数据统计 - 24小时有新用户进来的
     * startDayDef开始更新天数 0~20 20~61
     * endDayDef结束更新天数
     * type 1:24小时内有用户的，2根据linkId的创建时间15天，3根据linkId创建时间大于15天到90天，4根据创建时间大于90天小于180天，5根据创建时间大于180天小于242天
     */
    public void sendMesForStatisLinkPlus(Integer startDayDef, Integer endDayDef, Integer type) {
        if (startDayDef == null) {
            startDayDef = 0;
        }
        if (endDayDef == null) {
            endDayDef = 61;
        }
        Date now = DateUtil.getNowDate();
        List<JSONObject> sendList = new ArrayList<>();

        List<Integer> linkList = new ArrayList<>();
        if (type == 1) {
            // 24小时内有用户的
            FastMemberLinkPO mlParam = new FastMemberLinkPO();
            mlParam.setLastLinkTime(DateUtil.addHours(now, -24));
            List<FastMemberLinkPO> mlList = fastMemberLinkMapper.queryActiveLinkList(mlParam);
            for (FastMemberLinkPO mlink : mlList) {
                linkList.add(mlink.getLastLinkId());
            }
            mlList = fastMemberLinkMapper.queryActiveSubLinkList(mlParam);
            for (FastMemberLinkPO mlink : mlList) {
                linkList.add(mlink.getLastLinkId());
            }
        } else if (type == 2) {
            // 15天内创建的
            FastLinkQueryVO queryVO = new FastLinkQueryVO();
            queryVO.setBeginTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", DateUtil.beginOfDay(DateUtil.dateAdd(now, -15))));
            queryVO.setEndTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", now));
            List<FastLinkPO> list = fastLinkMapper.queryLinkIdsForStatisV1(queryVO);
            for (FastLinkPO item : list) {
                linkList.add(item.getId());
            }
        } else if (type == 3) {
            // 15到90天内创建的
            FastLinkQueryVO queryVO = new FastLinkQueryVO();
            queryVO.setBeginTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", DateUtil.beginOfDay(DateUtil.dateAdd(now, -90))));
            queryVO.setEndTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", DateUtil.beginOfDay(DateUtil.dateAdd(now, -15))));
            List<FastLinkPO> list = fastLinkMapper.queryLinkIdsForStatisV1(queryVO);
            for (FastLinkPO item : list) {
                linkList.add(item.getId());
            }
        } else if (type == 4) {
            // 90到180天内创建的
            FastLinkQueryVO queryVO = new FastLinkQueryVO();
            queryVO.setBeginTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", DateUtil.beginOfDay(DateUtil.dateAdd(now, -180))));
            queryVO.setEndTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", DateUtil.beginOfDay(DateUtil.dateAdd(now, -90))));
            List<FastLinkPO> list = fastLinkMapper.queryLinkIdsForStatisV1(queryVO);
            for (FastLinkPO item : list) {
                linkList.add(item.getId());
            }
        } else if (type == 5) {
            // 180到242天内创建的
            FastLinkQueryVO queryVO = new FastLinkQueryVO();
            queryVO.setBeginTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", DateUtil.beginOfDay(DateUtil.dateAdd(now, -242))));
            queryVO.setEndTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", DateUtil.beginOfDay(DateUtil.dateAdd(now, -180))));
            List<FastLinkPO> list = fastLinkMapper.queryLinkIdsForStatisV1(queryVO);
            for (FastLinkPO item : list) {
                linkList.add(item.getId());
            }
        }
        // int count = 0;
        // 根据查询的渠道link，开始更新不同日期
        for (Integer linkId : linkList) {
            if (linkId == 0) {
                continue;
            }
            String keyCreate = StaticVar.STATIS_LINK_CREATE + linkId;
            String createTimeStr = RedisUtil.get(keyCreate);
            if (StrUtil.isEmpty(createTimeStr)) {
                FastLinkPO lpParam = new FastLinkPO();
                lpParam.setId(linkId);
                FastLinkPO lpPO = fastLinkMapper.queryById(lpParam);
                if (lpPO == null) {
                    continue;
                }
                createTimeStr = DateUtil.format07(lpPO.getCreateTime());
                RedisUtil.set(keyCreate, createTimeStr, 60 * 60 * 24 * 300);
            }
            Date createTime = DateUtil.format07(createTimeStr);
            int days = DateUtil.daysBetween(createTime, now);
            if (days > 242) { // 181+61=242纵向181天，横向61天
                continue;
            }
            int startDay = 181 > days ? 0 : days - 180;
            if (startDay < startDayDef) {
                startDay = startDayDef;
            }
            for (int i = startDay; i < endDayDef; i++) {
                JSONObject params = new JSONObject();
                String dnDate = DateUtil.getDateStr("yyyy-MM-dd", DateUtil.dateAdd(now, -i));
                // statisType  1-推广链接数据统计 2.待扩展
                params.put("statisType", 1);
                params.put("statisDate", dnDate);
                params.put("linkId", linkId);
                sendList.add(params);
                // count++;
            }
            if (sendList.size() > 30) {
                mqStatisLinkProvider.sendExpMes(JsonUtil.toString(sendList), "400000");// 400秒未处理则过期清除
                sendList.clear();
            }
        }
        if (CollUtil.hasContent(sendList)) {
            mqStatisLinkProvider.sendExpMes(JsonUtil.toString(sendList), "400000");// 400秒未处理则过期清除
            sendList.clear();
        }
    }

    /**
     * 发送mq消息:链接数据统计 - 15天之前链接(最多前180天)，30分钟统计一次
     */
    public void sendMesForStatisLinkBefore15() {
        Date now = DateUtil.getNowDate();
        List<JSONObject> sendList = new ArrayList<>();
        int limit = 0;
        int count = 0;
        // 循环处理推广链接表中链接
        FastLinkQueryVO queryVO = new FastLinkQueryVO();
        queryVO.setBeginTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", DateUtil.beginOfDay(DateUtil.dateAdd(now, -180))));
        queryVO.setEndTime(DateUtil.getDateStr("yyyy-MM-dd HH:mm:ss", DateUtil.beginOfDay(DateUtil.dateAdd(now, -15))));
        List<FastLinkPO> list = fastLinkMapper.queryLinkIdsForStatisV1(queryVO);
        // List<FastLinkPO> list = fastLinkService.queryLinkIdsForStatis();
        for (FastLinkPO fastLinkPO : list) {
            limit++;
            count++;
            // 统计近10天各个链接数据  todo
            for (int i = 0; i < 10; i++) {
                JSONObject params = new JSONObject();
                String dnDate = DateUtil.getDateStr("yyyy-MM-dd", DateUtil.dateAdd(now, -i));
                // statisType  1-推广链接数据统计 2.待扩展
                params.put("statisType", 1);
                params.put("statisDate", dnDate);
                params.put("linkId", fastLinkPO.getId());
                sendList.add(params);
            }
            if (limit == 10 || count == list.size()) {
                mqStatisLinkHisProvider.sendExpMes(JsonUtil.toString(sendList), "1200000");// 20分钟未处理则过期清除
                limit = 0;
                sendList = new ArrayList<>();
                // 每次最多传递10个，休息2秒
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 短剧推广-推广链接-推广数据详情页-累计数据
     */
    public ResultVO<?> getAllData(FastLinkQueryVO item) {
        FastLinkPO fastLinkPO = fastLinkService.queryMoreById(item.getId());
        item.setLinkName(fastLinkPO.getLinkName());
        item.setCreateTime(fastLinkPO.getCreateTime());
        item.setMiniName(fastLinkPO.getMiniName());
        item.setUserName(fastLinkPO.getUserName());
        // 入口页面
        FastDramaVO fastDramaVO = fastDramaService.queryInfoByRedis(fastLinkPO.getDramaId());
        FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(fastLinkPO.getSeriesId());
        if (fastDramaVO != null) {
            item.setInPage(fastDramaVO.getDramaName() + "-第" + (fastDramaSeriesPO == null ? "1" : fastDramaSeriesPO.getSeriesNum()) + "集");
        } else {
            item.setInPage("-第" + (fastDramaSeriesPO == null ? "1" : fastDramaSeriesPO.getSeriesNum()) + "集");
        }
        FastStatisLinkPO fastStatisLinkPO = new FastStatisLinkPO();
        fastStatisLinkPO.setLinkId(item.getId());
        fastStatisLinkPO.setStatisDate(DateUtil.getDateStr("yyyy-MM-dd", DateUtil.getNowDate()));
        FastStatisLinkPO statis = queryOne(fastStatisLinkPO);
        if (statis != null) {
            statis.setMoneyAll(statis.getMoneyProfitAll());
            statis.setMoneyDay(statis.getMoneyProfitDay());
            statis.setMoneyAllDay(statis.getMoneyProfitAllDay());
            // 用户数据-新增用户数、今日新增、pv、uv
            item.setNumAll(statis.getNumAll());
            item.setNumDay(statis.getNumDay());
            // 总充值数据-充值金额、充值人数、充值笔数
            item.setMoneyAll(statis.getMoneyAll());
            item.setNumAllRech(statis.getNumAllRech());
            item.setNumAllOrder(statis.getNumAllOrder());
            // 今日充值数据-充值金额、充值人数、充值笔数
            item.setNumAllDayRech(statis.getNumAllDayRech());
            item.setNumAllDayOrder(statis.getNumAllDayOrder());
            item.setMoneyAllDay(statis.getMoneyAllDay());
            // 计算ARPPU
            item.setMoneyAllArppu(DoubleUtil.divB4Zero(item.getMoneyAll(), new BigDecimal(item.getNumAllRech())));
            item.setMoneyDayArppu(DoubleUtil.divB4Zero(item.getMoneyAllDay(), new BigDecimal(item.getNumAllDayRech())));
            // 利润、回报率、总成本
            if (statis.getMoneyAll() != null && statis.getCostAll() != null) {
                BigDecimal profitAll = (statis.getMoneyAll().subtract(statis.getCostAll())).setScale(DEF_SCALE_2, RoundingMode.HALF_UP);
                item.setProfitAll(profitAll);
                if (statis.getCostAll() != null && !(statis.getCostAll().compareTo(BigDecimal.ZERO) == 0)) {
                    item.setProfitAllRate(((profitAll.divide(statis.getCostAll(), 4, RoundingMode.HALF_DOWN)).multiply(BigDecimalVar.BD_100)).setScale(DEF_SCALE_2, RoundingMode.HALF_UP) + "%");
                }
                item.setCostAll(statis.getCostAll());
            }
        }
        // 获得 pv、uv
//        JSONObject condition = new JSONObject();
//        condition.put("linkId", item.getId() + "");// 查询列要在tablestore的多元索引中
//        IndexSearchReply<FastBackToutiaoPO> reply = TableStoreUtil.andQueryCount(tableStoreProperties.getFasBackToutiaoTable(), tableStoreProperties.getFasBackToutiaoTableIndex(), condition, FastBackToutiaoPO.class);
//        item.setPv(reply.totalCount());
        Long pvCount = fastStatisLinkPvMapper.queryLinkPVCount(item.getId());
        item.setPv(pvCount);
        item.setUv(item.getNumAll());

        return ResultVO.success(item);
    }

    /**
     * 短剧推广-推广链接-推广数据详情页-实时监测数据
     */
    public ResultVO<?> getImmeMonitorData(FastLinkQueryVO item) {
        FastStatisLinkPO fastStatisLinkPO = new FastStatisLinkPO();
        fastStatisLinkPO.setLinkId(item.getId());
        fastStatisLinkPO.setStatisDate(DateUtil.getDateStr("yyyy-MM-dd", DateUtil.getNowDate()));
        FastStatisLinkPO statis = queryOne(fastStatisLinkPO);
        if (statis != null) {
            statis.setMoneyAll(statis.getMoneyProfitAll());
            statis.setMoneyDay(statis.getMoneyProfitDay());
            statis.setMoneyAllDay(statis.getMoneyProfitAllDay());
            statis.setMoneyAllDayCommon(statis.getMoneyProfitAllDayCommon());
            // vip = vip + 剧卡（= 总 - k币 - 赠送）
            BigDecimal moneyVipProfitAllDay = statis.getMoneyAllDay().subtract(statis.getMoneyAllDayCommon());
//            statis.setMoneyAllDayVip(statis.getMoneyProfitAllDayVip());
            statis.setMoneyAllDayVip(moneyVipProfitAllDay);
            // 当日新增用户数-充值人数、充值笔数、充值金额、客单价、付费率
            item.setNumDay(statis.getNumDay());
            item.setNumDayRech(statis.getNumDayRech());
            item.setNumDayOrder(statis.getNumDayOrder());
            item.setMoneyDay(statis.getMoneyDay());
            if (statis.getMoneyDay() != null && statis.getNumDayRech() != null && (statis.getNumDayRech() != 0)) {
                item.setMoneyDayArpu(statis.getMoneyDay().divide(new BigDecimal(statis.getNumDayRech()), 4, RoundingMode.HALF_DOWN).setScale(DEF_SCALE_2, RoundingMode.HALF_UP));
            }
            if (statis.getNumDayRech() != null && statis.getNumDay() != null && (statis.getNumDay() != 0)) {
                item.setMoneyDayRateUserPay(new BigDecimal(statis.getNumDayRech()).divide(new BigDecimal(statis.getNumDay()), 4, RoundingMode.HALF_DOWN).multiply(BigDecimalVar.BD_100).setScale(DEF_SCALE_2, RoundingMode.HALF_UP) + "%");
            }
            // 所有新增用户当日-充值人数、充值笔数、充值金额、客单价、付费率
            item.setNumAllDayRech(statis.getNumAllDayRech());
            item.setNumAllDayOrder(statis.getNumAllDayOrder());
            item.setMoneyAllDay(statis.getMoneyAllDay());
            if (statis.getMoneyAllDay() != null && statis.getNumAllDayRech() != null && (statis.getNumAllDayRech() != 0)) {
                item.setMoneyAllDayArpu(statis.getMoneyAllDay().divide(new BigDecimal(statis.getNumAllDayRech()), 4, RoundingMode.HALF_DOWN).setScale(DEF_SCALE_2, RoundingMode.HALF_UP));
            }
            if (statis.getNumAllDayRech() != null && statis.getNumAll() != null && (statis.getNumAll() != 0)) {
                item.setMoneyAllDayRateUserPay(new BigDecimal(statis.getNumAllDayRech()).divide(new BigDecimal(statis.getNumAll()), 4, RoundingMode.HALF_DOWN).multiply(BigDecimalVar.BD_100).setScale(DEF_SCALE_2, RoundingMode.HALF_UP) + "%");
            }

            // 所有新增用户当日-普通充值-充值人数、充值笔数、充值金额、客单价、付费率
            item.setNumAllDayCommonRech(statis.getNumAllDayCommonRech());
            item.setNumAllDayCommonOrder(statis.getNumAllDayCommonOrder());
            item.setMoneyAllDayCommon(statis.getMoneyAllDayCommon());
            if (statis.getMoneyAllDayCommon() != null && statis.getNumAllDayCommonRech() != null && (statis.getNumAllDayCommonRech() != 0)) {
                item.setMoneyAllDayCommonArpu(statis.getMoneyAllDayCommon().divide(new BigDecimal(statis.getNumAllDayCommonRech()), 4, RoundingMode.HALF_DOWN).setScale(DEF_SCALE_2, RoundingMode.HALF_UP));

            }
            if (statis.getNumAllDayCommonRech() != null && statis.getNumAll() != null && (statis.getNumAll() != 0)) {
                item.setMoneyAllDayCommonRateUserPay(new BigDecimal(statis.getNumAllDayCommonRech()).divide(new BigDecimal(statis.getNumAll()), 4, RoundingMode.HALF_DOWN).multiply(BigDecimalVar.BD_100).setScale(DEF_SCALE_2, RoundingMode.HALF_UP) + "%");
            }

            // 所有新增用户当日-用户充值-充值人数、充值笔数、充值金额、客单价、付费率
//            item.setNumAllDayVipRech(statis.getNumAllDayVipRech());
            Integer numAllDayVipRech = statis.getNumAllDayRech() - statis.getNumAllDayCommonRech();
            item.setNumAllDayVipRech(numAllDayVipRech);
            Integer numAllDayVipOrder = statis.getNumAllDayOrder() - statis.getNumAllDayCommonOrder();
            item.setNumAllDayVipOrder(numAllDayVipOrder);
//            item.setNumAllDayVipOrder(statis.getNumAllDayVipOrder());
            item.setMoneyAllDayVip(statis.getMoneyAllDayVip());
            if (statis.getMoneyAllDayVip() != null && statis.getNumAllDayVipRech() != null && (statis.getNumAllDayVipRech() != 0)) {
                item.setMoneyAllDayVipArpu(statis.getMoneyAllDayVip().divide(new BigDecimal(numAllDayVipRech), 4, RoundingMode.HALF_DOWN).setScale(DEF_SCALE_2, RoundingMode.HALF_UP));
            }
            if (statis.getNumAllDayVipRech() != null && statis.getNumAll() != null && (statis.getNumAll() != 0)) {
                item.setMoneyAllDayVipRateUserPay(new BigDecimal(numAllDayVipRech).divide(new BigDecimal(statis.getNumAll()), 4, RoundingMode.HALF_DOWN).multiply(BigDecimalVar.BD_100).setScale(DEF_SCALE_2, RoundingMode.HALF_UP) + "%");
            }
        }
        item.setStatisDate(fastStatisLinkPO.getStatisDate());
        return ResultVO.success(item);
    }

    /**
     * 短剧推广-推广链接-推广数据详情页-每日回收数据列表
     */
    public ResultVO<?> getDayRoiList(FastLinkQueryVO item, PageVO pageVO) {
        startPage(pageVO);
        // 处理查询参数
        if (!StrUtil.isEmpty(item.getQueryCreateTime())) {
            String[] queryTime = item.getQueryCreateTime().split(" - ");
            item.setBeginTime(queryTime[0]);
            item.setEndTime(queryTime[1]);
        }
        item.setLinkId(item.getId());
        // 默认显示全部链接全部日期，不屏蔽新增用户为0的日期
        if (item.getIsAll() == null) {
            item.setIsAll(0);
        }
        List<FastStatisLinkVO> list = fastStatisLinkMapper.queryDayRoiList(item);
        for (FastStatisLinkVO cur : list) {
            cur.setMoneyDAllRech(cur.getMoneyProfitDAllRech());
            cur.setMoneyD60Rech(cur.getMoneyProfitD60Rech());
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getCostDay() == null) {
                cur.setCostDay(BigDecimal.ZERO);
            }
            // 用户成本
            if (cur.getNumDay() != null && cur.getNumDay() != 0) {
                cur.setCostDayPu(cur.getCostDay().divide(new BigDecimal(cur.getNumDay()), 4, RoundingMode.HALF_DOWN).setScale(DEF_SCALE_2, RoundingMode.HALF_UP));
            }

            // 付费用户成本
            if (cur.getNumDayRech() != null && cur.getNumDayRech() != 0) {
                cur.setCostDayPayPu(cur.getCostDay().divide(new BigDecimal(cur.getNumDAllRech()), 4, RoundingMode.HALF_DOWN).setScale(DEF_SCALE_2, RoundingMode.HALF_UP));
            }
            //"moneyDAllRech:累计充值金额",
            //"numDAllRech:累计充值用户",
            //"roiDAllRech:累计回报率",
            if (cur.getMoneyDAllRech() != null && cur.getCostDay() != null && !(cur.getCostDay().compareTo(BigDecimal.ZERO) == 0)) {
                cur.setRoiDAllRech(cur.getMoneyDAllRech().divide(cur.getCostDay(), 4, RoundingMode.HALF_DOWN).multiply(BigDecimalVar.BD_100).setScale(DEF_SCALE_2, RoundingMode.HALF_UP) + "%");
            }

            //"moneyD60Rech:D0-D60充值金额 示例 1.20,2.23,,,,,,3.33,9.99",
            //"numD60Rech:D0-D60充值人数 示例 1,2,,,,,,3,9",
            //"roiD60Rech:D0-D60充值回报率 示例 1.20%,2.23%,,,,,,3.33%,9.99%"
            if (!StrUtil.isEmpty(cur.getMoneyD60Rech())) {
                // String[] moneyD60Rech = cur.getMoneyD60Rech().split(",");
                String[] moneyD60Rech = StrUtil.splitN(cur.getMoneyD60Rech(), ",");
                String[] roiD60Rech = new String[61];
                log.info("处理开始-getDayRoiList-setRoiD60Rech={}", JsonUtil.toString(cur.getRoiD60Rech()));

                if (moneyD60Rech.length > 60) {
                    for (int i = 0; i < 61; i++) {
                        if (!StrUtil.isEmpty(moneyD60Rech[i]) && cur.getCostDay() != null && !(cur.getCostDay().compareTo(BigDecimal.ZERO) == 0)) {
                            roiD60Rech[i] = new BigDecimal(moneyD60Rech[i]).divide(cur.getCostDay(), 4, RoundingMode.HALF_DOWN).multiply(BigDecimalVar.BD_100).setScale(DEF_SCALE_2, RoundingMode.HALF_UP) + "%";
                        }
                    }
                }
                cur.setRoiD60Rech(StrUtil.joinArr(roiD60Rech));
                log.info("处理结束-getDayRoiList-setRoiD60Rech={}", JsonUtil.toString(cur.getRoiD60Rech()));
            }
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 链路ID数据同步
     */
    public void linkRoad() {
        FastLinkPO query = new FastLinkPO();
        query.setRoadId(0);
        // 查询没有配置链路的链接
        List<FastLinkPO> list = fastLinkMapper.queryLinkOfficialList(query);
        if (CollUtil.hasContent(list)) {
            for (FastLinkPO linkPO : list) {
                if (linkPO.getRoadId() != null) {
                    fastLinkMapper.updateById(linkPO);
                }
            }
        }
    }

    /**
     * 查询渠道的订单统计
     */
    public ResultVO getLinkOrderStatisHour(FastLinkQueryVO params) {
        Date beginDate = DateUtil.format09(params.getBeginDate());
        Integer orderCountAll = 0;// 累计
        BigDecimal moneyRechargeAll = BigDecimal.ZERO; // 累计
        BigDecimal moneyEcpmRechargeAll = BigDecimal.ZERO; // 累计
        List<LinkStatisticsVO> lsList = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            // 查询统计的数据
            String hourStr = String.valueOf(i);
            hourStr = hourStr.length() == 1 ? "0" + hourStr : hourStr;
            for (int j = 0; j < 2; j++) {
                String minStart = "00";
                String minEnd = "29";
                if (j == 1) {
                    minStart = "30";
                    minEnd = "59";
                }
                Date beginTime = DateUtil.format07(DateUtil.format09(beginDate) + " " + hourStr + ":" + minStart + ":00");// 查询一天的数据
                Date endTime = DateUtil.format07(DateUtil.format09(beginDate) + " " + hourStr + ":" + minEnd + ":59");// 查询一天的数据
                FastStatisLinkPO linkParam = new FastStatisLinkPO();
                linkParam.setBeginTime(beginTime);
                linkParam.setEndTime(endTime);
                linkParam.setLinkId(params.getLinkId());
                FastStatisLinkVO linkVO = fastStatisLinkMapper.getLinkOrderStatisDay(linkParam);
                FastStatisLinkVO linkFreeVO = fastStatisLinkMapper.getLinkOrderStatisFreeDay(linkParam);
                Integer orderCount = 0;
                BigDecimal moneyRecharge = BigDecimal.ZERO;
                BigDecimal moneyEcpmRecharge = BigDecimal.ZERO;
                if (linkVO != null) {
                    orderCount = linkVO.getOrderCount();
                    moneyRecharge = linkVO.getMoneyRecharge();
                }
                if (linkFreeVO != null) {
                    moneyEcpmRecharge = linkFreeVO.getMoneyRecharge();
                }
                orderCountAll = orderCountAll + orderCount;
                moneyRechargeAll = DoubleUtil.addB(moneyRechargeAll, moneyRecharge);
                moneyEcpmRechargeAll = DoubleUtil.addB(moneyEcpmRechargeAll, moneyEcpmRecharge);
                LinkStatisticsVO ls = new LinkStatisticsVO();
                ls.setHour(hourStr + ":" + minStart);
                ls.setOrderCount(orderCount);
                ls.setOrderCountAll(orderCountAll);
                ls.setMoneyRecharge(moneyRecharge);
                ls.setMoneyRechargeAll(moneyRechargeAll);
                ls.setMoneyEcpmRecharge(moneyEcpmRecharge);
                ls.setMoneyEcpmRechargeAll(moneyEcpmRechargeAll);
                lsList.add(ls);
            }
        }
        Map<String, Object> results = new HashMap<>();
        results.put("statisticsList", lsList);
        return ResultVO.success(results);
    }

    /**
     * 查询渠道的订单统计
     */
    public ResultVO getLinkOrderStatisDay(FastLinkQueryVO params) {
        Date beginDate = DateUtil.format09(params.getBeginDate());
        Date endDate = DateUtil.format09(params.getEndDate());
        if (endDate.before(beginDate)) {
            return ResultVO.error(StaticStr.INVALID_PARAM);
        }
        Integer orderCountAll = 0;// 累计
        BigDecimal moneyRechargeAll = BigDecimal.ZERO; // 累计
        BigDecimal moneyEcpmRechargeAll = BigDecimal.ZERO; // 免费累计
        List<LinkStatisticsVO> lsList = new ArrayList<>();
        Integer loop = 0;
        while (!beginDate.after(endDate) && loop < 365) {
            loop++;
            // 检查缓存
            String dateStr = DateUtil.format09(beginDate);
            String key = StaticVar.LINK_DAY + params.getLinkId() + "_" + dateStr;
            String res = RedisUtil.get(key);
            if (DateUtil.getNowDate().after(beginDate)) {
                res = "";// 小于今天的才缓存
            }
            if (StrUtil.isNotEmpty(res)) {
                LinkStatisticsVO ls = JsonUtil.toJavaObject(res, LinkStatisticsVO.class);
                lsList.add(ls);
                continue;
            }
            // 查询统计的数据
            Date beginTime = DateUtil.format07(DateUtil.format09(beginDate) + " 00:00:00");// 查询一天的数据
            Date endTime = DateUtil.format07(DateUtil.format09(beginDate) + " 23:59:59");// 查询一天的数据

            FastStatisLinkPO linkParam = new FastStatisLinkPO();
            linkParam.setBeginTime(beginTime);
            linkParam.setEndTime(endTime);
            linkParam.setLinkId(params.getLinkId());
            FastStatisLinkVO linkVO = fastStatisLinkMapper.getLinkOrderStatisDay(linkParam);
            FastStatisLinkVO linkFreeVO = fastStatisLinkMapper.getLinkOrderStatisFreeDay(linkParam);
            Integer orderCount = 0;
            BigDecimal moneyRecharge = BigDecimal.ZERO;
            BigDecimal moneyEcpmRecharge = BigDecimal.ZERO;
            if (linkVO != null) {
                orderCount = linkVO.getOrderCount();
                moneyRecharge = linkVO.getMoneyRecharge();
            }
            if (linkFreeVO != null) {
                moneyEcpmRecharge = linkFreeVO.getMoneyRecharge();
            }
            orderCountAll = orderCountAll + orderCount;
            moneyRechargeAll = DoubleUtil.addB(moneyRechargeAll, moneyRecharge);
            moneyEcpmRechargeAll = DoubleUtil.addB(moneyEcpmRechargeAll, moneyEcpmRecharge);
            LinkStatisticsVO ls = new LinkStatisticsVO();
            ls.setDate(DateUtil.format09(beginDate));
            ls.setOrderCount(orderCount);
            ls.setOrderCountAll(orderCountAll);
            ls.setMoneyRecharge(moneyRecharge);
            ls.setMoneyRechargeAll(moneyRechargeAll);
            ls.setMoneyEcpmRecharge(moneyEcpmRecharge);
            ls.setMoneyEcpmRechargeAll(moneyEcpmRechargeAll);
            lsList.add(ls);
            RedisUtil.set(key, JsonUtil.toString(ls), 60 * 60 * 24 * 7);
            // 加1天
            beginDate = DateUtil.addDays(beginDate, 1);
        }
        Map<String, Object> results = new HashMap<>();
        results.put("statisticsList", lsList);
        return ResultVO.success(results);
    }

    /**
     * 推广链接-累计数据
     *
     * @param params
     * @return
     */
    public ResultVO<?> getAllFreeData(FastLinkQueryVO params) {
        // 总消耗
        FastLinkQueryVO query = new FastLinkQueryVO();
        query.setStatisDate(DateUtil.format09(DateUtil.getNowDate()));
        query.setId(params.getId());
        FastLinkQueryVO fastStatisLinkVO = fastStatisLinkMapper.queryFreeData(query);
        if (fastStatisLinkVO == null) {
            return ResultVO.success();
        }
        if (fastStatisLinkVO.getLinkType() != null && fastStatisLinkVO.getLinkType() == 3) {
            fastStatisLinkVO.setNumDay(fastStatisLinkVO.getColorMemberDay().intValue());
            fastStatisLinkVO.setNumAll(fastStatisLinkVO.getColorMemberAll().intValue());
        }
        String inPage = fastStatisLinkVO.getDramaName() + '-' + "第" + fastStatisLinkVO.getSeriesNum() + "集";
        fastStatisLinkVO.setInPage(inPage);
//        JSONObject condition = new JSONObject();
//        condition.put("linkId", fastStatisLinkVO.getId() + "");// 查询列要在tablestore的多元索引中
//        IndexSearchReply<FastBackToutiaoPO> reply = TableStoreUtil.andQueryCount(tableStoreProperties.getFasBackToutiaoTable(), tableStoreProperties.getFasBackToutiaoTableIndex(), condition, FastBackToutiaoPO.class);
//        fastStatisLinkVO.setPv(reply.totalCount());
        long pvCount = fastStatisLinkPvMapper.queryLinkPVCount(fastStatisLinkVO.getId());
        fastStatisLinkVO.setPv(pvCount);
        fastStatisLinkVO.setUv(defaultIfNull(fastStatisLinkVO.getNumAll(), 0));
        // 累计收入=累计广告收入+累计充值金额
        BigDecimal totalIncome = DoubleUtil.addB(fastStatisLinkVO.getAdIncomeAll(), fastStatisLinkVO.getTotalRechargeMoney());
        // 利润=累计收入-投放消耗总成本
        BigDecimal income = DoubleUtil.subB(totalIncome, defaultIfNull(fastStatisLinkVO.getCostAll(), BigDecimal.ZERO));
        // 回报率(ROI)=（累计收入➗投放总消耗）*100%
        BigDecimal returnRatio = DoubleUtil.mulB(DoubleUtil.divB4Zero(totalIncome,
                        defaultIfNull(fastStatisLinkVO.getCostAll(), BigDecimal.ZERO)),
                new BigDecimal("100"));
        // 累计回传率=该渠道下已回传用户数➗累计新增用户数
        BigDecimal totalBackRatio = BigDecimal.ZERO;
        if (fastStatisLinkVO.getNumAll() > 0) {
            totalBackRatio = DoubleUtil.mulB(DoubleUtil.divB(fastStatisLinkVO.getBackUserNum(),
                            new BigDecimal(fastStatisLinkVO.getNumAll())),
                    new BigDecimal("100"));
        }
        fastStatisLinkVO.setTotalIncome(totalIncome);
        fastStatisLinkVO.setIncome(income);
        fastStatisLinkVO.setReturnRatio(returnRatio);
        fastStatisLinkVO.setTotalBackRatio(totalBackRatio);
        return ResultVO.success(fastStatisLinkVO);
    }

    /**
     * 推广链接-实时监测数据
     *
     * @param params
     * @return
     */
    public ResultVO<?> getFreeImmeMonitorData(FastLinkQueryVO params) {
        FastStatisLinkFreePO query = new FastStatisLinkFreePO();
        query.setStatisDate(DateUtil.beginOfDay());
        query.setLinkId(params.getId());
        FastStatisLinkFreePO freePO = fastStatisLinkMapper.getFreeImmeMonitorData(query);
        if (freePO == null) {
            return ResultVO.success();
        }
        // 挂载链接使用免费统计表
        if (freePO.getLinkType() != null && freePO.getLinkType() == 3) {
            freePO.setAddMemberNum(freePO.getColorMemberDay());
        }
        // 客单价：（广告收入+充值收入）➗观看人数
        BigDecimal memberPrice = BigDecimal.ZERO;   // 新增用户客单价
        // 转化率：（产生广告收入+充值的收入）➗今日新增用户数
        BigDecimal addMemberConvertRatio = BigDecimal.ZERO; // 新增用户转化率
        BigDecimal addMemberIncome = DoubleUtil.addB(defaultIfNull(freePO.getAddMemberRecharge(), BigDecimal.ZERO),
                defaultIfNull(freePO.getAddMemberInocme(), BigDecimal.ZERO));
        Integer totalNum = defaultIfNull(freePO.getRechargeMemberNum(), 0) + defaultIfNull(freePO.getAddMemberWatchAmount(), 0);
        if (freePO.getAddMemberWatchAmount() > 0) {
            memberPrice = DoubleUtil.divB(addMemberIncome, new BigDecimal(freePO.getAddMemberWatchAmount()));
        }
        if (freePO.getAddMemberNum() > 0) {
            addMemberConvertRatio = DoubleUtil.divB(DoubleUtil.mulB(new BigDecimal(totalNum), new BigDecimal(100)),
                    new BigDecimal(freePO.getAddMemberNum()));
        }
        FastLinkQueryVO todayIncome = new FastLinkQueryVO();
        todayIncome.setAdIncomeDay(freePO.getAddMemberInocme());
        todayIncome.setMoneyRecharge(freePO.getAddMemberRecharge());
        todayIncome.setAdWatchMemberNumDay(freePO.getAddMemberWatchAmount());
        todayIncome.setAdWatchNum(freePO.getAddMemberWatchNum());
        todayIncome.setMemberPrice(memberPrice);
        todayIncome.setConvertRetio(addMemberConvertRatio);

        FastStatisLinkFreePO query1 = new FastStatisLinkFreePO();
        query1.setCreateTimeBegin(DateUtil.beginOfDay());
        query1.setCreateTimeEnd(DateUtil.endOfDay());
        query1.setLinkId(params.getId());
        FastStatisLinkFreePO todayIncomeResult = dataLakeUnlockStartLogMapper.queryTodayIncome(query1);
        BigDecimal todayIncomeMoney = DoubleUtil.addB(defaultIfNull(freePO.getRechargeMoney(), BigDecimal.ZERO),
                defaultIfNull(todayIncomeResult.getAdIncome(), BigDecimal.ZERO));
        if (todayIncomeResult.getAdWatchMemberNum() > 0) {
            memberPrice = DoubleUtil.divB(todayIncomeMoney, new BigDecimal(todayIncomeResult.getAdWatchMemberNum()));
        }
        FastLinkQueryVO todayIncomeAll = new FastLinkQueryVO();
        todayIncomeAll.setAdIncomeDay(todayIncomeResult.getAdIncome());
        todayIncomeAll.setMoneyRecharge(freePO.getRechargeMoney());
        todayIncomeAll.setAdWatchMemberNumDay(todayIncomeResult.getAdWatchMemberNum());
        todayIncomeAll.setAdWatchNumDay(todayIncomeResult.getAdWatchNum());
        todayIncomeAll.setMemberPrice(memberPrice);
        BigDecimal ROI = BigDecimal.ZERO;
        if (freePO.getTotalCost() != null && freePO.getTotalCost().compareTo(BigDecimal.ZERO) > 0) {
            ROI = DoubleUtil.divB(DoubleUtil.mulB(todayIncomeMoney, new BigDecimal("100")), freePO.getTotalCost());
        }
        FastLinkQueryVO data = new FastLinkQueryVO();
        data.setStatisDate(DateUtil.format09(freePO.getStatisDate()));
        data.setAddMemberNum(freePO.getAddMemberNum());
        data.setTodayIncome(todayIncome);
        data.setTodayIncomeAll(todayIncomeAll);
        data.setTotalROI(ROI);
        return ResultVO.success(data);
    }

    /**
     * 每日回收数据
     *
     * @param params
     * @param pageVO
     * @return
     */
    public ResultVO<?> getFreeDayRoiList(FastLinkQueryVO params, PageVO pageVO) {
        startPage(pageVO);
        FastStatisLinkFreePO query = new FastStatisLinkFreePO();
        if (params.getQueryCreateTime() != null && params.getQueryCreateTime().length() > 0) {
            String[] queryTime = params.getQueryCreateTime().split(" - ");
            query.setBeginStatis(queryTime[0] + " 00:00:00");
            query.setEndStatis(queryTime[1] + " 23:59:59");
        }
        query.setLinkId(params.getLinkId());
        List<FastStatisLinkFreePO> list = fastStatisLinkFreeMapper.getFreeDayRoiList(query);
        if (CollUtil.isNotEmpty(list) && list.size() > 0) {
            for (FastStatisLinkFreePO item : list) {
                if (item.getStatisId() != null) {
                    item.setEncryptionId(encode(item.getStatisId()));
                }
                Map<String, Object> summaryD60Result = summaryD60(item);
                BigDecimal adMoneyConsume = defaultIfNull(item.getTotalCost(), BigDecimal.ZERO);
                Integer convertMemberCount = defaultIfNull(item.getConvertMemberDay(), 0);
                Integer addMemberCount = defaultIfNull(item.getAddMemberNum(), 0);
                BigDecimal convertMemberCost = BigDecimal.ZERO;      // 转化用户成本
                if (convertMemberCount > 0) {
                    convertMemberCost = DoubleUtil.divB(adMoneyConsume, convertMemberCount);
                }
                // 消耗金额➗累计新增用户数
                BigDecimal addMemberCost = BigDecimal.ZERO;         // 新增用户成本
                if (addMemberCount > 0) {
                    addMemberCost = DoubleUtil.divB(adMoneyConsume, addMemberCount);
                }
                item.setMemberCost(addMemberCost);
                item.setUnlockCost(convertMemberCost);
                item.setUnlcokMemberNum(defaultIfNull((Integer) summaryD60Result.get("adWatchMemberSummary"), 0));
                item.setUnlockIncome(defaultIfNull((BigDecimal) summaryD60Result.get("adIncomSummary"), BigDecimal.ZERO));
                BigDecimal returnRatio = BigDecimal.ZERO;
                if (item.getTotalCost().compareTo(BigDecimal.ZERO) > 0) {
                    returnRatio = DoubleUtil.divB(defaultIfNull((BigDecimal) summaryD60Result.get("adIncomSummary"), BigDecimal.ZERO)
                            , item.getTotalCost());
                    returnRatio = DoubleUtil.mulB(returnRatio, new BigDecimal(100));
                }
                item.setReturnRatio(returnRatio);
                item.setUnlockMemberD60(item.getAdWatchMemberD60());
                item.setUnlockIncomeD60(item.getAdIncomeD60());
                item.setReturnRatioD60(item.getReturnRatioD60());
                item.setSummaryD60Result(summaryD60Result);
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    public static Map<String, Object> summaryD60(FastStatisLinkFreePO item) {
        Map<String, Object> data = new HashMap<>();
        String[] aaArray = new String[61];
        String[] bbArray = new String[61];
        String[] ccArray = new String[61];
        if (StrUtil.isNotEmpty(item.getAdWatchMemberD60())) {
            aaArray = item.getAdWatchMemberD60().split(",");
        }
        if (StrUtil.isNotEmpty(item.getAdIncomeD60())) {
            bbArray = item.getAdIncomeD60().split(",");
        }
        if (StrUtil.isNotEmpty(item.getReturnRatioD60())) {
            ccArray = item.getReturnRatioD60().split(",");
        }
        Integer adWatchMemberSummary = 0;
        BigDecimal adIncomSummary = BigDecimal.ZERO;
        BigDecimal returnRatioSummary = BigDecimal.ZERO;
        for (int i = 0; i < 61; i++) {
            if (i < aaArray.length && aaArray[i] != null) {
                Integer aaInteger = Integer.valueOf(aaArray[i]);
                adWatchMemberSummary += aaInteger;
            }
            if (i < bbArray.length && bbArray[i] != null) {
                BigDecimal bbDecimal = new BigDecimal(bbArray[i]);
                adIncomSummary = DoubleUtil.addB(adIncomSummary, bbDecimal);
            }
            if (i < ccArray.length && ccArray[i] != null) {
                BigDecimal ccDecimal = new BigDecimal(ccArray[i]);
                returnRatioSummary = DoubleUtil.addB(returnRatioSummary, ccDecimal);
            }
        }
        data.put("adWatchMemberSummary", adWatchMemberSummary);
        data.put("adIncomSummary", adIncomSummary);
        data.put("returnRatioSummary", returnRatioSummary);
        return data;
    }

}