/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.promote.FastShortLinkMapper;
import com.fast.mapper.tablestore.FastTablestoreBackMapper;
import com.fast.po.promote.FastShortLinkPO;
import com.fast.po.tablestore.FastTablestoreBackPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.LinkUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastShortLinkService extends BaseService {

    @Autowired
    private FastShortLinkMapper fastShortLinkMapper;
    @Autowired
    private FastTablestoreBackMapper fastTablestoreBackMapper;

    /**
     * 通过id查询单个对象
     */
    public FastShortLinkPO queryById(FastShortLinkPO item) {
        return fastShortLinkMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastShortLinkPO queryById(Integer id) {
        FastShortLinkPO itemParam = new FastShortLinkPO();
        itemParam.setId(id);
        return fastShortLinkMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastShortLinkPO queryOne(FastShortLinkPO item) {
        return fastShortLinkMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastShortLinkPO> queryList(FastShortLinkPO item) {
        return fastShortLinkMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastShortLinkPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastShortLinkPO> list = fastShortLinkMapper.queryList(item);
        for (FastShortLinkPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastShortLinkPO item) {
        return fastShortLinkMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastShortLinkPO item) {
        // 生成短连接
        String shorts = LinkUtil.getShortURL();
        item.setShorts(shorts);
        item.setShortLink(StaticVar.PROMOTE_MINI_SHORT_LINK_DOMAIN + shorts);
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastShortLinkMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        // 添加到redis
        String key = StaticVar.PROMOTE_MINI_SHORT_LINK + item.getShorts();
        RedisUtil.set(key, item.getLongLink(), StaticVar.PROMOTE_MINI_SHORT_LINK_EXP);// 有效期365天
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastShortLinkPO> list) {
        if (fastShortLinkMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastShortLinkPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastShortLinkMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 根据短连接查询长链接
    public String queryByShorts(String shortLink) {
        // 从redis获取
        String key = StaticVar.PROMOTE_MINI_SHORT_LINK + shortLink;
        String longLink = RedisUtil.get(key);
        if (StrUtil.isNotEmpty(longLink)) {
            return longLink;
        } else {
            // 从数据库中获取
            FastShortLinkPO itemParam = new FastShortLinkPO();
            itemParam.setShorts(shortLink);
            FastShortLinkPO item = queryOne(itemParam);
            if (item != null && StrUtil.isNotEmpty(item.getLongLink())) {
                // 添加到redis
                RedisUtil.set(key, item.getLongLink(), StaticVar.PROMOTE_MINI_SHORT_LINK_EXP);// 有效期365天
                return item.getLongLink();
            }
        }
        return null;
    }

    /**
     * 根据短连接，获取投放链接回传Link，记录clickid，并把clidkid拼接到长链接，转跳长链接h5页面后，再去打开小程序
     *
     * @param request
     * @param response
     * @param shortLink
     */
    public void shortLink(HttpServletRequest request, HttpServletResponse response, String shortLink) {
        log.info(String.format("短链请求上一页面url：%s", request.getHeader("Referer")));

        // 根据短连接获取长链接
        // http://prod-6g7thfxk7659f02f-1313407825.tcloudbaseapp.com/jump-mp.html?appId=wxcd870bd0e4c813af&officialId=3&title=%E7%A9%BA%E6%A0%87%E9%A2%98&path=pages%2Findex%2Findex%3FdramaId%3D9%26seriesId%3D8%26officialId%3D3%26linkId%3D16%26from%3D1%26%23
        String longLink = queryByShorts(shortLink);
        if (longLink != null) {
            try {
                String dcLongLink = StrUtil.decode(longLink);
                String[] from = dcLongLink.split("&from=");
                if (from != null && from.length > 1) {
                    String fromStr = from[1];
                    String[] from1 = fromStr.split("&");
                    if (from1 != null && from1.length > 0) {
                        Integer advMediaId = Integer.parseInt(from1[0]);
                        switch (advMediaId) {
                            // 广告媒体：1-头条；2-mp
                            case 1:// 1-头条
                                // 执行头条链接处理
                                touTiaoBackLinkSave(request, response, longLink);
                                break;
                            case 2:// 2-adq
                                break;
                            case 3:// 3-mp
                                break;
                            case 4:// 4-百度
                                // 执行头条链接处理
                                baiduBackLinkSave(request, response, longLink);
                                break;
                            default:
                                break;
                        }
                    }
                }
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 执行头条链接处理
     *
     * @param request
     * @param response
     * @param longLink
     */
    private void touTiaoBackLinkSave(HttpServletRequest request, HttpServletResponse response, String longLink) {
        Enumeration<String> headerNames = request.getHeaderNames();
        HashMap hashMap = new HashMap();

        while (headerNames.hasMoreElements()) {
            String key = headerNames.nextElement();
            // 排除Cookie字段
            if (key.equalsIgnoreCase("Cookie")) {
                continue;
            }
            String value = request.getHeader(key);
            hashMap.put(key, value);
        }
        log.info("shortlink-header = {}", JsonUtil.toString(hashMap));

        // 保存 联调测试落地页Link，为了联调回传
        // 投放落地页链接样例： https://www.chengzijianzhan.com/tetris/page_preview/7137883149481361444?adid=1749629861915553&clickid=EKHXoZP36I0DGI6Y1uzL24sDKI7wg6aRoIwD&creativeid=1749629861915553&creativetype=1&tag=pc_iframe&timestamp=1661997305095&token=a810de7c1e2c950faa9ffaa1c33ab8c8d9df00bf&water_mask=1
        String backLink = request.getHeader("Referer");
        actionLogService.log("short_link_referer", "referer=" + backLink);
        String clickid = null;
        if (backLink != null && backLink.length() > 50) {// 链接长度大于50，才可能为正常链接，则记录回传Link
            String[] params = backLink.split("\\?");
            if (params != null && params.length >= 2) {
                String[] paramsQuery = params[1].split("&");
                if (paramsQuery != null && paramsQuery.length >= 1) {
                    for (String s : paramsQuery) {
                        String[] paramsClick = s.split("=");
                        if (paramsClick != null && paramsClick.length >= 2) {
                            if (paramsClick[0].equals("clickid")) {
                                FastTablestoreBackPO backPO = new FastTablestoreBackPO();
                                backPO.setClickId(paramsClick[1]);
                                backPO.setBackLink(backLink);
                                backPO.setCreateTime(DateUtil.getNowDate());
                                fastTablestoreBackMapper.insertSelective(backPO);
//                                FastLinkToutiaoBacklinkPO fastLinkToutiaoBacklinkPO = new FastLinkToutiaoBacklinkPO();
//                                fastLinkToutiaoBacklinkPO.setBackLink(backLink);
//                                fastLinkToutiaoBacklinkPO.setClickId(paramsClick[1]);
//                                fastLinkToutiaoBacklinkPO.setCreateTime(DateUtil.getNowDate());
//                                // 向阿里云tablestore添加投放链接回传link记录
//                                TableStoreUtil.putRow(tableStoreProperties.getFasBackToutiaoLinkTable(), JsonUtil.toJSONObject(fastLinkToutiaoBacklinkPO), tableStoreProperties.getFasBackToutiaoLinkTablePk(), StrUtil.getUUID());


                                clickid = paramsClick[1];
                                break;
                            }
                        }
                    }
                }

            }
        }
        if (StrUtil.isNotEmpty(longLink)) {
            try {
                // 执行长链接跳转
                if (clickid != null) {
                    longLink = longLink + StrUtil.encode("&clickId=" + clickid);
                }


                try {
                    String ip = LinkUtil.getRemoteAddr(request);
                    String ua = request.getHeader("User-Agent");
                    log.info("ippp1:" + request.getHeader("remoteip"));
                    // 如果ua长度大于100，则截取100长度,匹配ua过长不一致问题
//                	if (ua != null && ua.length() > 100) {
//                    	ua = ua.substring(0, 100);
//                	}
                    log.info(String.format("shortLink-获取ip=%s , ua=%s", ip, ua));
//                  String uaEncode = StrUtil.encode(ua);
                    String uaEncode = StrUtil.getSubUa(ua);// 截取+encode
                    // ua字符限定不能超过700字符 , 实际联调测试长度为417字符
                    // ua样例：Mozilla%2F5.0+%28Linux%3B+Android+10%3B+Redmi+Note+8+Pro+Build%2FQP1A.190711.020%3B+wv%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Version%2F4.0+Chrome%2F75.0.3770.156+Mobile+Safari%2F537.36++aweme_220100+JsSdk%2F1.0+NetType%2FWIFI+Channel%2Fxiaomi_1128_64+AppName%2Faweme+app_version%2F22.1.0+ByteLocale%2Fzh-CN+Region%2FCN+AppSkin%2Fwhite+AppTheme%2Flight+BytedanceWebview%2Fd8a21c6+TTWebView%2F0751130025454
                    // 其他参数实际联调长度117个字符，样例dramaId=9&seriesId=8&officialId=3&linkId=16&from=1&#&clickid=ELLQu62wqY0DGI6Y1uzL24sDKIeRk6OVoYwD&ip=***************
                    uaEncode = uaEncode.length() > 700 ? uaEncode.substring(0, 700) : uaEncode;
                    longLink = longLink + StrUtil.encode("&ip=" + ip + "&ua=" + uaEncode);
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                log.info("开始跳转:" + longLink);
                response.sendRedirect(longLink);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 百度中转
     *
     * @param request
     * @param response
     * @param longLink
     */
    private void baiduBackLinkSave(HttpServletRequest request, HttpServletResponse response, String longLink) {

        Enumeration<String> headerNames = request.getHeaderNames();
        HashMap hashMap = new HashMap();
        while (headerNames.hasMoreElements()) {
            String key = headerNames.nextElement();
            // 排除Cookie字段
            if (key.equalsIgnoreCase("Cookie")) {
                continue;
            }
            String value = request.getHeader(key);
            hashMap.put(key, value);
        }
        log.info("shortlink-header = {}", JsonUtil.toString(hashMap));
        // 保存 联调测试落地页Link，为了联调回传
//        投放落地页链接样例： https://www.chengzijianzhan.com/tetris/page_preview/7137883149481361444?adid=1749629861915553&clickid=EKHXoZP36I0DGI6Y1uzL24sDKI7wg6aRoIwD&creativeid=1749629861915553&creativetype=1&tag=pc_iframe&timestamp=1661997305095&token=a810de7c1e2c950faa9ffaa1c33ab8c8d9df00bf&water_mask=1
        String backLink = request.getHeader("Referer");
        String clickid = null;
        if (backLink != null && backLink.length() > 50) {// 链接长度大于50，才可能为正常链接，则记录回传Link
            String[] params = backLink.split("\\?");
            if (params != null && params.length >= 2) {
                String[] paramsQuery = params[1].split("&");
                if (paramsQuery != null && paramsQuery.length >= 1) {
                    for (String s : paramsQuery) {
                        String[] paramsClick = s.split("=");
                        if (paramsClick != null && paramsClick.length >= 2) {
                            if (paramsClick[0].equals("bd_vid")) {
//    							FastLinkToutiaoBacklinkPO fastLinkToutiaoBacklinkPO = new FastLinkToutiaoBacklinkPO();
//    							fastLinkToutiaoBacklinkPO.setBackLink(backLink);
//    							fastLinkToutiaoBacklinkPO.setClickId(paramsClick[1]);
//    							fastLinkToutiaoBacklinkPO.setCreateTime(DateUtil.getNowDate());
////                                fastLinkToutiaoBacklinkService.insert(fastLinkToutiaoBacklinkPO);
//    							//向阿里云tablestore添加投放链接回传link记录
//    							TableStoreUtil.putRow(tableStoreProperties.getFasBackToutiaoLinkTable(), JsonUtil.toJSONObject(fastLinkToutiaoBacklinkPO), tableStoreProperties.getFasBackToutiaoLinkTablePk(), StrUtil.getUUID());
                                clickid = paramsClick[1];
                                break;
                            }
                        }
                    }
                }

            }
        }
        if (StrUtil.isNotEmpty(longLink)) {
            try {
                // 执行长链接跳转
                if (clickid != null) {
                    longLink = longLink + StrUtil.encode("&clickId=" + clickid);
                }
                try {
                    String ip = LinkUtil.getRemoteAddr(request);
                    String ua = request.getHeader("User-Agent");
                    // 如果ua长度大于100，则截取100长度,匹配ua过长不一致问题
//                	if (ua != null && ua.length() > 100) {
//                    	ua = ua.substring(0, 100);
//                	}
                    log.info(String.format("shortLink-获取ip=%s , ua=%s", ip, ua));
//                  String uaEncode = StrUtil.encode(ua);
                    String uaEncode = StrUtil.getSubUa(ua);// 截取+encode
                    // ua字符限定不能超过700字符 , 实际联调测试长度为417字符
                    // ua样例：Mozilla%2F5.0+%28Linux%3B+Android+10%3B+Redmi+Note+8+Pro+Build%2FQP1A.190711.020%3B+wv%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Version%2F4.0+Chrome%2F75.0.3770.156+Mobile+Safari%2F537.36++aweme_220100+JsSdk%2F1.0+NetType%2FWIFI+Channel%2Fxiaomi_1128_64+AppName%2Faweme+app_version%2F22.1.0+ByteLocale%2Fzh-CN+Region%2FCN+AppSkin%2Fwhite+AppTheme%2Flight+BytedanceWebview%2Fd8a21c6+TTWebView%2F0751130025454
                    // 其他参数实际联调长度117个字符，样例dramaId=9&seriesId=8&officialId=3&linkId=16&from=1&#&clickid=ELLQu62wqY0DGI6Y1uzL24sDKIeRk6OVoYwD&ip=***************
                    uaEncode = uaEncode.length() > 700 ? uaEncode.substring(0, 700) : uaEncode;
                    longLink = longLink + StrUtil.encode("&ip=" + ip + "&ua=" + uaEncode);
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                log.info("开始跳转:" + longLink);
                response.sendRedirect(longLink);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }


}
