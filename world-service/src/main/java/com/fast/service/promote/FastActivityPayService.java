/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.mapper.promote.FastActivityMapper;
import com.fast.mapper.promote.FastActivityPayMapper;
import com.fast.po.promote.FastActivityPO;
import com.fast.po.promote.FastActivityPayPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastActivityPayService extends BaseService {

    @Autowired
    private FastActivityPayMapper fastActivityPayMapper;
    @Autowired
    private FastActivityMapper activityMapper;

    /**
     * 通过id查询单个对象
     */
    public FastActivityPayPO queryById(FastActivityPayPO item) {
        return fastActivityPayMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastActivityPayPO queryById(Integer id) {
        FastActivityPayPO itemParam = new FastActivityPayPO();
        itemParam.setId(id);
        return fastActivityPayMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastActivityPayPO queryOne(FastActivityPayPO item) {
        return fastActivityPayMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastActivityPayPO> queryList(FastActivityPayPO item) {
        return fastActivityPayMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastActivityPayPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastActivityPayPO> list = fastActivityPayMapper.queryList(item);
        for (FastActivityPayPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastActivityPayPO item) {
        return fastActivityPayMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastActivityPayPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastActivityPayMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastActivityPayPO> list) {
        if (fastActivityPayMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 处理活动支付成功
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void dealActivityPaySuccess(Long orderId, BigDecimal moneyRecharge) {
        sleep(20);
        FastActivityPayPO activityPay = fastActivityPayMapper.queryByOrderId(orderId);
        if (activityPay == null) {
            return;
        }
        FastActivityPayPO po = new FastActivityPayPO();
        po.setOrderId(orderId);
        po.setState(1);
        fastActivityPayMapper.updateByOrderId(po);

        // 汇总统计活动数据
        FastActivityPO plus = new FastActivityPO();
        plus.setRechargeMoney(moneyRecharge);
        plus.setRechargeCount(1);
        plus.setId(activityPay.getActivityId());
        activityMapper.updatePlusById(plus);
    }
}
