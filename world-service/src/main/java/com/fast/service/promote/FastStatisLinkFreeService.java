/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.BigDecimalVar;
import com.fast.constant.StaticStr;
import com.fast.mapper.datalake.DataLakeUnlockStartLogMapper;
import com.fast.mapper.promote.FastLinkMapper;
import com.fast.mapper.promote.FastStatisLinkFreeMapper;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.promote.FastStatisLinkFreePO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastStatisLinkFreeService extends BaseService {

    private final Integer pageSize = 5;

    @Autowired
    private FastStatisLinkFreeMapper fastStatisLinkFreeMapper;
    @Autowired
    private FastLinkMapper fastLinkMapper;
    @Autowired
    private DataLakeUnlockStartLogMapper dataLakeUnlockStartLogMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStatisLinkFreePO queryById(FastStatisLinkFreePO params) {
        return fastStatisLinkFreeMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStatisLinkFreePO queryById(Integer id) {
        return fastStatisLinkFreeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStatisLinkFreePO queryOne(FastStatisLinkFreePO params) {
        return fastStatisLinkFreeMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStatisLinkFreePO> queryList(FastStatisLinkFreePO params) {
        return fastStatisLinkFreeMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStatisLinkFreePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStatisLinkFreePO> list = fastStatisLinkFreeMapper.queryList(params);
        for (FastStatisLinkFreePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStatisLinkFreePO params) {
        return fastStatisLinkFreeMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStatisLinkFreePO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastStatisLinkFreeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStatisLinkFreePO> list) {
        if (fastStatisLinkFreeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStatisLinkFreePO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastStatisLinkFreeMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 统计d60数据
     *
     * @param statisDate
     */
    public void statisFreeLink(Date statisDate) {
        Integer[] adWatchMemberD60 = new Integer[61];
        BigDecimal[] adIncomeD60 = new BigDecimal[61];
        BigDecimal[] returnRatioD60 = new BigDecimal[61];
        FastLinkPO linkQuery = new FastLinkPO();
        linkQuery.setStatisDate(DateUtil.format09(statisDate));  // 免费标志
        List<FastLinkPO> linkList = fastLinkMapper.queryFreeLinkStatisList(linkQuery);
        for (FastLinkPO link : linkList) {
            FastStatisLinkFreePO queryOneFreePO = new FastStatisLinkFreePO();
            queryOneFreePO.setStatisDate(statisDate);
            queryOneFreePO.setLinkId(link.getId());
            FastStatisLinkFreePO exists = fastStatisLinkFreeMapper.queryOne(queryOneFreePO);
            if (exists != null) {
                // 1.查询当日新增用户的memberIds
                BigDecimal totalcost = defaultIfNull(link.getCostDay(), BigDecimal.ZERO);
                if (statisDate.before(DateUtil.endOfDay(DateUtil.getNowDate()))) {
                    // 2.查询每日的广告收入、观看用户、回报率
                    for (int i = 0; i < 61; i++) {
                        Date reportDate = DateUtil.addDays(statisDate, i);
                        if (reportDate.before(DateUtil.endOfDay(DateUtil.getNowDate()))) {
                            BigDecimal adIncome = BigDecimal.ZERO;           // 广告收入
                            Integer addMemberCount = 0;                      // 观看用户
                            BigDecimal d60ReturnRatio = BigDecimal.ZERO;     // 回报率
                            FastStatisLinkFreePO queryD60 = new FastStatisLinkFreePO();
                            queryD60.setCreateTimeBegin(DateUtil.beginOfDay(reportDate));
                            queryD60.setCreateTimeEnd(DateUtil.endOfDay(reportDate));
                            queryD60.setStatisDateBegin(DateUtil.beginOfDay(statisDate));
                            queryD60.setStatisDateEnd(DateUtil.endOfDay(statisDate));
                            queryD60.setLinkId(link.getId());
                            FastStatisLinkFreePO fastStatisLinkFreePO = dataLakeUnlockStartLogMapper.queryD60(queryD60);
                            adIncome = fastStatisLinkFreePO.getD60AdIncome() == null ? BigDecimal.ZERO :
                                    fastStatisLinkFreePO.getD60AdIncome().setScale(2, RoundingMode.UP);
                            addMemberCount = defaultIfNull(fastStatisLinkFreePO.getD60AddMember(), 0);
                            if (totalcost.compareTo(BigDecimal.ZERO) > 0) {
                                d60ReturnRatio = DoubleUtil.mulB(DoubleUtil.divB(adIncome, totalcost),
                                        new BigDecimal(100));
                            }
                            if (!DateUtil.format09(DateUtil.format09(link.getCreateTime())).after(statisDate)) {
                                adWatchMemberD60[i] = addMemberCount;
                                adIncomeD60[i] = adIncome;
                                returnRatioD60[i] = d60ReturnRatio;
                            }
                        }
                    }
                    FastStatisLinkFreePO update = new FastStatisLinkFreePO();
                    String adWatchMemberD60Str = StrUtil.joinArr(adWatchMemberD60);
                    String adIncomeD60Str = StrUtil.joinArr(adIncomeD60);
                    String returnRatioD60Str = StrUtil.joinArr(returnRatioD60);
                    update.setAdWatchMemberD60(adWatchMemberD60Str);
                    update.setAdIncomeD60(adIncomeD60Str);
                    update.setReturnRatioD60(returnRatioD60Str);
                    update.setId(exists.getId());
                    fastStatisLinkFreeMapper.updateById(update);
                }
            }
        }
    }

    /**
     * 业务数据
     *
     * @param beginOfDay
     */
    public void syncFreeLinkBusinessData(Date beginOfDay) {
        // 1.查询starLog、rewardlog
        Map<Integer, FastStatisLinkFreePO> statisFreeMap = new HashMap<>();       // start_log累计数据
        Map<Integer, FastStatisLinkFreePO> statisFreeTodayMap = new HashMap<>();  // start_log当天数据
        Map<Integer, FastStatisLinkFreePO> backUserMap = new HashMap<>();         // start_log回传用户累计数据
        Map<Integer, FastStatisLinkFreePO> backUserTodayMap = new HashMap<>();    // start_log回传用户当天数据
        Map<Integer, FastStatisLinkFreePO> rewardMap = new HashMap<>();           // reward_log累计数据
        Map<Integer, FastStatisLinkFreePO> rewardTodayMap = new HashMap<>();      // reward_log当天数据
        Map<Integer, FastStatisLinkFreePO> addMemeberStartMap = new HashMap<>();  // start_log当天数据
        Map<Integer, FastStatisLinkFreePO> addMemberStartTotalMap = new HashMap<>();
        FastStatisLinkFreePO query = new FastStatisLinkFreePO();
        List<FastStatisLinkFreePO> freeLinkList = dataLakeUnlockStartLogMapper.statisLinkFreeGroupbyLinkId(query);
        if (CollUtil.hasContent(freeLinkList)) {
            statisFreeMap = freeLinkList.stream().collect(Collectors.toMap(FastStatisLinkFreePO::getLinkId, Function.identity(), (o1, o2) -> o1));
        }
        List<FastStatisLinkFreePO> backUserList = dataLakeUnlockStartLogMapper.backUserGroupbyLinkId(query);
        if (CollUtil.hasContent(backUserList)) {
            backUserMap = backUserList.stream().collect(Collectors.toMap(FastStatisLinkFreePO::getLinkId, Function.identity(), (o1, o2) -> o1));
        }
        List<FastStatisLinkFreePO> rewardList = dataLakeUnlockStartLogMapper.queryUnlockSuccessListGroupbyLinkId(query);
        if (CollUtil.hasContent(rewardList)) {
            rewardMap = rewardList.stream().collect(Collectors.toMap(FastStatisLinkFreePO::getLinkId, Function.identity(), (o1, o2) -> o1));
        }
        query.setCreateTimeBegin(DateUtil.beginOfDay(beginOfDay));
        query.setCreateTimeEnd(DateUtil.endOfDay(beginOfDay));
        List<FastStatisLinkFreePO> freeLinkTodayList = dataLakeUnlockStartLogMapper.statisLinkFreeGroupbyLinkId(query);
        if (CollUtil.hasContent(freeLinkTodayList)) {
            statisFreeTodayMap = freeLinkTodayList.stream().collect(Collectors.toMap(FastStatisLinkFreePO::getLinkId, Function.identity(), (o1, o2) -> o1));
        }
        List<FastStatisLinkFreePO> backUserTodayList = dataLakeUnlockStartLogMapper.backUserGroupbyLinkId(query);
        if (CollUtil.hasContent(backUserTodayList)) {
            backUserTodayMap = backUserTodayList.stream().collect(Collectors.toMap(FastStatisLinkFreePO::getLinkId, Function.identity(), (o1, o2) -> o1));
        }
        List<FastStatisLinkFreePO> rewardTodayList = dataLakeUnlockStartLogMapper.queryUnlockSuccessListGroupbyLinkId(query);
        if (CollUtil.hasContent(rewardTodayList)) {
            rewardTodayMap = rewardTodayList.stream().collect(Collectors.toMap(FastStatisLinkFreePO::getLinkId, Function.identity(), (o1, o2) -> o1));
        }
        FastStatisLinkFreePO queryNew = new FastStatisLinkFreePO();
        queryNew.setCreateTimeBegin(DateUtil.beginOfDay(beginOfDay));
        queryNew.setCreateTimeEnd(DateUtil.endOfDay(beginOfDay));
        queryNew.setStatisDateBegin(DateUtil.beginOfDay(beginOfDay));
        queryNew.setStatisDateEnd(DateUtil.endOfDay(beginOfDay));
        List<FastStatisLinkFreePO> startAddMemberList = dataLakeUnlockStartLogMapper.statisLinkAddMemberGroupbyLinkId(queryNew);
        if (CollUtil.hasContent(startAddMemberList)) {
            addMemeberStartMap = startAddMemberList.stream().collect(Collectors.toMap(FastStatisLinkFreePO::getLinkId, Function.identity(), (o1, o2) -> o1));
        }

        List<FastStatisLinkFreePO> startAddMemberTotalList = dataLakeUnlockStartLogMapper.queryTotalMemberIdsGroupByLinkId();
        if (CollUtil.hasContent(startAddMemberTotalList)) {
            addMemberStartTotalMap = startAddMemberTotalList.stream().collect(Collectors.toMap(FastStatisLinkFreePO::getLinkId, Function.identity(), (o1, o2) -> o1));
        }
        // 2.分页查询link
        FastLinkPO queryLink = new FastLinkPO();
        queryLink.setFeeFlag(2);
        Integer count = fastLinkMapper.queryCount(queryLink);// 查询免费短剧渠道总数量
        int maxPage = count / pageSize;
        int page = 0;
        int start = 0;
        while (page <= maxPage) {
            FastStatisLinkFreePO queryList = new FastStatisLinkFreePO();
            queryList.setStart(start);
            queryList.setPageSize(pageSize);
            queryList.setStatisDate(beginOfDay);
            queryList.setCreateTimeBegin(DateUtil.beginOfDay(beginOfDay));
            queryList.setCreateTimeEnd(DateUtil.endOfDay(beginOfDay));
            List<FastStatisLinkFreePO> freeList = fastStatisLinkFreeMapper.queryFreeListForPage(queryList);// 查询免费短剧渠道总数量
            for (FastStatisLinkFreePO item : freeList) {
                if (item.getLinkType() != null && item.getLinkType() == 3) {
                    item.setColorMemberDay(item.getLoadColorMemberDay()); // 挂载渠道
                    item.setColorMemberAll(item.getLoadColorMemberAll()); // 挂载渠道
                }
                // 3.查询新增用户
                BigDecimal addMemberRechargeMoney = BigDecimal.ZERO;        // 新增用户充值金额
                Integer addMemberWatchAmount = 0;                           // 新增用户观看人数
                BigDecimal addMemberInocme = BigDecimal.ZERO;               // 新增用户收入
                Integer addMemberWatchNum = 0;                              // 新增用户观看次数
                String rechargeMemberIds = null;                            // 充值用户ids
                Integer todayConvertMemberNum = 0;                          // 当日转化用户数
                Integer totalConvertMemberNum = 0;                          // 累计转化用户数
                // 计算累计转化人数
                Integer adWatchMemberNum = 0; // 当日观看人数
                BigDecimal adIncome = BigDecimal.ZERO; // 当日广告收入
                Integer adWatchNum = 0; // 当日观看次数；
                FastStatisLinkFreePO freePO1 = statisFreeTodayMap.get(item.getLinkId());
                if (freePO1 != null) {
                    adWatchMemberNum = defaultIfNull(freePO1.getAdWatchMemberNum(), 0);
                    adIncome = defaultIfNull(freePO1.getAdIncome(), BigDecimal.ZERO);
                    adWatchNum = defaultIfNull(freePO1.getAdWatchNum(), 0);
                }
                Integer adUnlockMemberNum = 0;    // 当日解锁成功人数
                Integer adUnlockNum = 0;          // 当日解锁成功次数
                FastStatisLinkFreePO freePO2 = rewardTodayMap.get(item.getLinkId());
                if (freePO2 != null) {
                    adUnlockMemberNum = defaultIfNull(freePO2.getAdUnlockMemberNum(), 0);
                    adUnlockNum = defaultIfNull(freePO2.getAdUnlockNum(), 0);
                }
                Integer totalWatchMemberNum = 0;                // 累计观看人数
                BigDecimal totalAdIncome = BigDecimal.ZERO;     // 累计广告收入
                Integer totalWatchNum = 0;                      // 累计观看次数
                FastStatisLinkFreePO freePO3 = statisFreeMap.get(item.getLinkId());
                if (freePO3 != null) {
                    totalWatchMemberNum = defaultIfNull(freePO3.getAdWatchMemberNum(), 0);
                    totalAdIncome = defaultIfNull(freePO3.getAdIncome(), BigDecimal.ZERO);
                    totalWatchNum = defaultIfNull(freePO3.getAdWatchNum(), 0);
                }
                Integer totalUnlockNum = 0;       // 累计解锁成功次数
                Integer totalUnlockMemberNum = 0; // 当日解锁成功人数
                FastStatisLinkFreePO freePO4 = rewardMap.get(item.getLinkId());
                if (freePO4 != null) {
                    totalUnlockMemberNum = defaultIfNull(freePO4.getAdUnlockMemberNum(), 0);
                    totalUnlockNum = defaultIfNull(freePO4.getAdUnlockNum(), 0);
                }
                Integer todayBackUser = 0;        // 当天回传用户数
                FastStatisLinkFreePO freePO5 = backUserTodayMap.get(item.getLinkId());
                if (freePO5 != null) {
                    todayBackUser = defaultIfNull(freePO5.getTodayBackUser(), 0);
                }
                Integer totalBackUser = 0;        // 累计回传用户数
                FastStatisLinkFreePO freePO6 = backUserMap.get(item.getLinkId());
                if (freePO6 != null) {
                    totalBackUser = defaultIfNull(freePO6.getTodayBackUser(), 0);
                }
                FastStatisLinkFreePO freePO7 = addMemeberStartMap.get(item.getLinkId());
                if (freePO7 != null) {     // 新增用户相关
                    addMemberWatchAmount = freePO7.getAddMemberWatchAmount();
                    addMemberInocme = freePO7.getAddMemberInocme();
                    addMemberWatchNum = freePO7.getAddMemberWatchNum();
                    todayConvertMemberNum = defaultIfNull(freePO7.getConvertMemberDay(), 0);// 计算当日转化人数
                }
                FastStatisLinkFreePO freePO8 = addMemberStartTotalMap.get(item.getLinkId());
                if (freePO8 != null) {  // 累计转换人数
                    totalConvertMemberNum = defaultIfNull(freePO8.getConvertMemberAll(), 0);
                }
                FastStatisLinkFreePO queryOne = new FastStatisLinkFreePO();
                queryOne.setStatisDate(beginOfDay);
                queryOne.setLinkId(defaultIfNull(item.getLinkId(), 0));
                FastStatisLinkFreePO exists = fastStatisLinkFreeMapper.queryOne(queryOne);
                if (exists == null) {
                    FastStatisLinkFreePO insert = new FastStatisLinkFreePO();
                    insert.setStatisDate(beginOfDay);
                    insert.setLinkId(defaultIfNull(item.getLinkId(), 0));
                    insert.setAdIncome(adIncome);
                    insert.setAdWatchNum(adWatchNum);
                    insert.setAdWatchMemberNum(adWatchMemberNum);
                    insert.setAdUnlockNum(adUnlockNum);
                    insert.setAdUnlockMemberNum(adUnlockMemberNum);
                    insert.setTotalWatchMemberNum(totalWatchMemberNum);
                    insert.setTotalUnlockMemberNum(totalUnlockMemberNum);
                    insert.setTotalWatchNum(totalWatchNum);
                    insert.setTotalUnlockNum(totalUnlockNum);
                    insert.setTotalAdIncome(totalAdIncome);
                    insert.setCostAll(item.getCostAll());
                    insert.setRechargeMemberNum(item.getRechargeMemberNum());
                    insert.setRechargeNum(item.getRechargeNum());
                    insert.setRechargeMoney(item.getRechargeMoney());
                    insert.setTotalRechargeMemberNum(item.getTotalRechargeMemberNum());
                    insert.setTotalRechargeNum(item.getTotalRechargeNum());
                    insert.setTotalRechargeMoney(item.getTotalRechargeMoney());
                    insert.setAddMemberInocme(addMemberInocme);
                    insert.setAddMemberWatchNum(addMemberWatchNum);
                    insert.setAddMemberWatchAmount(addMemberWatchAmount);
                    insert.setAddMemberRecharge(addMemberRechargeMoney);
                    insert.setTotalBackUser(totalBackUser);
                    insert.setTodayBackUser(todayBackUser);
                    insert.setColorMemberDay(defaultIfNull(item.getColorMemberDay(), 0));
                    insert.setColorMemberAll(defaultIfNull(item.getConvertMemberAll(), 0));
                    insert.setConvertMemberDay(todayConvertMemberNum);
                    insert.setConvertMemberAll(totalConvertMemberNum);
                    fastStatisLinkFreeMapper.insertSelective(insert);
                } else {
                    FastStatisLinkFreePO update = new FastStatisLinkFreePO();
                    update.setStatisDate(beginOfDay);
                    update.setLinkId(defaultIfNull(item.getLinkId(), 0));
                    update.setAdIncome(adIncome);
                    update.setAdWatchNum(adWatchNum);
                    update.setAdWatchMemberNum(adWatchMemberNum);
                    update.setAdUnlockNum(adUnlockNum);
                    update.setAdUnlockMemberNum(adUnlockMemberNum);
                    update.setTotalWatchMemberNum(totalWatchMemberNum);
                    update.setTotalUnlockMemberNum(totalUnlockMemberNum);
                    update.setTotalWatchNum(totalWatchNum);
                    update.setTotalUnlockNum(totalUnlockNum);
                    update.setTotalAdIncome(totalAdIncome);
                    update.setCostAll(item.getCostAll());
                    update.setRechargeMemberNum(item.getRechargeMemberNum());
                    update.setRechargeNum(item.getRechargeNum());
                    update.setRechargeMoney(item.getRechargeMoney());
                    update.setTotalRechargeMemberNum(item.getTotalRechargeMemberNum());
                    update.setTotalRechargeNum(item.getTotalRechargeNum());
                    update.setTotalRechargeMoney(item.getTotalRechargeMoney());
                    update.setAddMemberInocme(addMemberInocme);
                    update.setAddMemberWatchNum(addMemberWatchNum);
                    update.setAddMemberWatchAmount(addMemberWatchAmount);
                    update.setAddMemberRecharge(addMemberRechargeMoney);
                    update.setTotalBackUser(totalBackUser);
                    update.setTodayBackUser(todayBackUser);
                    update.setColorMemberDay(defaultIfNull(item.getColorMemberDay(), 0));
                    update.setColorMemberAll(defaultIfNull(item.getColorMemberAll(), 0));
                    update.setConvertMemberDay(todayConvertMemberNum);
                    update.setConvertMemberAll(totalConvertMemberNum);
                    update.setId(exists.getId());
                    fastStatisLinkFreeMapper.updateById(update);
                }
            }
            start += pageSize;
            page++;
        }
    }

    /**
     * 优化 查询d60新增用户累计数据
     *
     * @param startTime
     * @param endTime
     */
    public void statisFreeLinkD60(Date startTime, Date endTime) {
        List<FastStatisLinkFreePO> startLoglist = dataLakeUnlockStartLogMapper.queryD60Statis(startTime, endTime);
        int days = DateUtil.daysBetween(startTime, endTime);
        for (int i = 0; i <= days; i++) {
            Date linkDate = DateUtil.addDays(startTime, i);
            FastLinkPO linkQuery = new FastLinkPO();
            linkQuery.setStatisDate(DateUtil.format09(linkDate));  // 免费标志
            List<FastLinkPO> linkList = fastLinkMapper.queryFreeLinkStatisList(linkQuery);
            for (FastLinkPO item : linkList) {
                Integer[] adWatchMemberD60 = new Integer[61];
                BigDecimal[] adIncomeD60 = new BigDecimal[61];
                BigDecimal[] returnRatioD60 = new BigDecimal[61];
                BigDecimal costDay = item.getCostDay();
                FastStatisLinkFreePO queryOneFreePO = new FastStatisLinkFreePO();
                queryOneFreePO.setStatisDate(linkDate);
                queryOneFreePO.setLinkId(item.getId());
                FastStatisLinkFreePO exists = fastStatisLinkFreeMapper.queryOne(queryOneFreePO);
                if (exists != null) {
                    for (int j = 0; j <= 60; j++) {
                        Date costDate = DateUtil.addDays(linkDate, j);
                        if (costDate.before(DateUtil.endOfDay(DateUtil.getNowDate()))) {
                            FastStatisLinkFreePO result = startLoglist.stream()
                                    .filter(e -> (e.getLinkId().equals(item.getId())
                                            && e.getLinkTime().equals(DateUtil.format09(linkDate))
                                            && e.getCostTime().equals(DateUtil.format09(costDate))))
                                    .findFirst().orElse(null);
                            int addMemberCount = 0;
                            BigDecimal adIncome = BigDecimal.ZERO;
                            BigDecimal d60ReturnRatio = BigDecimal.ZERO;
                            if (result != null) {
                                addMemberCount = defaultIfNull(result.getD60AddMember(), 0);
                                adIncome = defaultIfNull(result.getD60AdIncome(), BigDecimal.ZERO);
                                d60ReturnRatio = BigDecimal.ZERO;
                                if (costDay.compareTo(BigDecimal.ZERO) > 0) {
                                    d60ReturnRatio = DoubleUtil.divB(DoubleUtil.mulB(adIncome, BigDecimalVar.BD_100), costDay);
                                }
                            }
                            adWatchMemberD60[j] = addMemberCount;
                            adIncomeD60[j] = adIncome;
                            returnRatioD60[j] = d60ReturnRatio;
                        }
                    }
                    FastStatisLinkFreePO update = new FastStatisLinkFreePO();
                    String adWatchMemberD60Str = StrUtil.joinArr(adWatchMemberD60);
                    String adIncomeD60Str = StrUtil.joinArr(adIncomeD60);
                    String returnRatioD60Str = StrUtil.joinArr(returnRatioD60);
                    update.setAdWatchMemberD60(adWatchMemberD60Str);
                    update.setAdIncomeD60(adIncomeD60Str);
                    update.setReturnRatioD60(returnRatioD60Str);
                    update.setId(exists.getId());
                    fastStatisLinkFreeMapper.updateById(update);
                }
            }
        }
    }
}
