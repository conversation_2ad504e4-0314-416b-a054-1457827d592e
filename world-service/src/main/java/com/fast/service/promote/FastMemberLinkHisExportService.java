/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.promote.FastMemberLinkHisMapper;
import com.fast.po.promote.FastMemberLinkHisPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberLinkHisExportService extends BaseService {

    @Autowired
    private FastMemberLinkHisMapper memberLinkHisMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    public ResultVO<?> exportMemberLinkHisList(SessionVO sessionVO, FastMemberLinkHisPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_LINK_HIS_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        // 查询数据
        List<FastMemberLinkHisPO> list = memberLinkHisMapper.queryList(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberLinkHisPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getMemberId());
            CollUtil.addNoRepeat(rowHeadNames, "用户id");

            row.add(DateUtil.format07(cur.getColorIn()));
            CollUtil.addNoRepeat(rowHeadNames, "注册时间");

            row.add(cur.getOfficialName());
            CollUtil.addNoRepeat(rowHeadNames, "归属公众号");

            row.add(cur.getRetailName());
            CollUtil.addNoRepeat(rowHeadNames, "归属分销商");

            row.add(cur.getLinkId());
            CollUtil.addNoRepeat(rowHeadNames, "归属推广链接id");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "归属推广链接名称");

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "归属推广应用名称");

            if (cur.getMiniType() == 1) {
                row.add("微信小程序");
            } else if (cur.getMiniType() == 2) {
                row.add("抖音小程序");
            } else if (cur.getMiniType() == 3) {
                row.add("H5");
            } else if (cur.getMiniType() == 4) {
                row.add("快手小程序");
            } else if (cur.getMiniType() == 5) {
                row.add("快应用小程序");
            }
            CollUtil.addNoRepeat(rowHeadNames, "归属推广应用类型");

            row.add(DateUtil.format07(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "染色发生时间");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "染色用户列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }
}
