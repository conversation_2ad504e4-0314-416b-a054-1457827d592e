/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.promote.FastLinkMapper;
import com.fast.mapper.promote.FastStatisLinkFreeMapper;
import com.fast.mapper.statis.FastStatisLinkPvMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.drama.FastDramaSeriesPO;
import com.fast.po.promote.FastStatisLinkFreePO;
import com.fast.po.promote.FastStatisLinkPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.drama.FastDramaSeriesService;
import com.fast.service.drama.FastDramaService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.fee.FastFeeRuleVO;
import com.fast.vo.promote.FastLinkQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fast.utils.DoubleUtil.DEF_SCALE_2;

/**
 * <AUTHOR>
 */
@Service
public class FastLinkExportService extends BaseService {

    @Autowired
    private FastLinkMapper fastLinkMapper;
    @Autowired
    private FastDramaService fastDramaService;
    @Autowired
    private FastDramaSeriesService fastDramaSeriesService;
    @Autowired
    private FastStatisLinkService fastStatisLinkService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastStatisLinkFreeMapper fastStatisLinkFreeMapper;
    @Autowired
    private FastStatisLinkPvMapper fastStatisLinkPvMapper;

    /**
     * 渠道监控-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    @Slave
    public ResultVO<?> exportLinkList(SessionVO sessionVO, FastLinkQueryVO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_LINK_ROI + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        // 处理查询参数
        if (!StrUtil.isEmpty(params.getQueryCreateTime())) {
            String[] queryTime = params.getQueryCreateTime().split(" - ");
            params.setBeginTime(queryTime[0] + " 00:00:00");
            params.setEndTime(queryTime[1] + " 23:59:59");
        }
        if (!StrUtil.isEmpty(params.getQueryName())) {
            params.setLinkName(params.getQueryName());
        }
        if (StrUtil.isInteger(params.getQueryName())) {
            params.setId(Integer.parseInt(params.getQueryName()));
        }
        params.setStatisDate(DateUtil.getDateStr("yyyy-MM-dd", DateUtil.getNowDate()));
        params.setLimitExport(StaticVar.MILLION);
        List<FastLinkQueryVO> list = fastLinkMapper.queryListV2(params);
        Map<Integer, FastDramaPO> dramaMap = new HashMap<>();
        Map<Integer, FastDramaSeriesPO> seriesMap = new HashMap<>();
        for (FastLinkQueryVO cur : list) {
            // 入口页面
            FastDramaPO fastDramaPO = dramaMap.get(cur.getDramaId());
            if (fastDramaPO == null) {
                FastDramaPO oneFastDramaPO = new FastDramaPO();
                oneFastDramaPO.setId(cur.getDramaId());
                fastDramaPO = fastDramaService.queryOne(oneFastDramaPO);
                dramaMap.put(cur.getDramaId(), fastDramaPO);
            }
            FastDramaSeriesPO fastDramaSeriesPO = seriesMap.get(cur.getSeriesId());
            if (fastDramaSeriesPO == null) {
                fastDramaSeriesPO = fastDramaSeriesService.queryById(cur.getSeriesId());
            }
            if (fastDramaPO != null && fastDramaSeriesPO != null) {
                cur.setInPage(fastDramaPO.getDramaName() + "-第" + fastDramaSeriesPO.getSeriesNum() + "集");
                // 设置剧名、集名、第几集
                cur.setDramaName(fastDramaPO.getDramaName());
                cur.setSeriesName("第" + fastDramaSeriesPO.getSeriesNum() + "集");
                cur.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
            }
            // 相关设置
            cur.setSetInfo("回传规则：" + (cur.getBackType() == 1 ? "全局规则" : "自定义规则") + "\n" + "充值模板：" + (cur.getPayType() == 1 ? "全局模板" : "自定义模板"));
            // 获取设置链接统计数据信息
            FastStatisLinkPO fastStatisLinkPO = new FastStatisLinkPO();
            fastStatisLinkPO.setLinkId(cur.getId());
            fastStatisLinkPO.setStatisDate(DateUtil.getDateStr("yyyy-MM-dd", DateUtil.getNowDate()));
            FastStatisLinkPO queryFastStatisLinkPO = fastStatisLinkService.queryOne(fastStatisLinkPO);
            if (queryFastStatisLinkPO != null) {
                // 当日新增用户
                cur.setNumDay(queryFastStatisLinkPO.getNumDay());
                // 当日新增用户-充值人数、充值笔数、充值金额
                cur.setNumDayRech(queryFastStatisLinkPO.getNumDayRech());
                cur.setNumDayOrder(queryFastStatisLinkPO.getNumDayOrder());
                cur.setMoneyDay(queryFastStatisLinkPO.getMoneyDay());
                // 新增用户当日-充值人数、充值笔数、充值金额
                cur.setNumAllDayRech(queryFastStatisLinkPO.getNumAllDayRech());
                cur.setNumAllDayOrder(queryFastStatisLinkPO.getNumAllDayOrder());
                cur.setMoneyAllDay(queryFastStatisLinkPO.getMoneyAllDay());
                // 新增用户累计-充值人数、充值笔数、充值金额
                cur.setNumAllRech(queryFastStatisLinkPO.getNumAllRech());
                cur.setNumAllOrder(queryFastStatisLinkPO.getNumAllOrder());
                cur.setMoneyAll(queryFastStatisLinkPO.getMoneyAll());
                // 累计新增用户数、总成本(累计成本)
                cur.setNumAll(queryFastStatisLinkPO.getNumAll());
                cur.setCostAll(queryFastStatisLinkPO.getCostAll());
                // 总利润、总利润率
                if (queryFastStatisLinkPO.getMoneyAll() != null && queryFastStatisLinkPO.getCostAll() != null) {
                    BigDecimal profitAll = (queryFastStatisLinkPO.getMoneyAll().subtract(queryFastStatisLinkPO.getCostAll())).setScale(DEF_SCALE_2, RoundingMode.HALF_UP);
                    cur.setProfitAll(profitAll);
                    if (queryFastStatisLinkPO.getCostAll() != null && !(queryFastStatisLinkPO.getCostAll().compareTo(new BigDecimal(0)) == 0)) {
                        cur.setProfitAllRate(((profitAll.divide(queryFastStatisLinkPO.getCostAll(), 4, RoundingMode.HALF_DOWN)).multiply(new BigDecimal(100))).setScale(DEF_SCALE_2, RoundingMode.HALF_UP) + "%");
                    }
                }
            }
            // 获得 pv、uv
//            JSONObject condition = new JSONObject();
//            condition.put("linkId", cur.getId() + "");// 查询列要在tablestore的多元索引中
//            IndexSearchReply<FastBackToutiaoPO> reply = TableStoreUtil.andQueryCount(tableStoreProperties.getFasBackToutiaoTable(), tableStoreProperties.getFasBackToutiaoTableIndex(), condition, FastBackToutiaoPO.class);
//            cur.setPv(reply.totalCount());
            Long pvCount = fastStatisLinkPvMapper.queryLinkPVCount(cur.getId());
            cur.setPv(pvCount);
            cur.setUv(cur.getNumAll());
            // 设置全局
            FastFeeRuleVO feeRule = null;
            fastLinkService.setGlobalNum(cur, feeRule);
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastLinkQueryVO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getId());
            CollUtil.addNoRepeat(rowHeadNames, "链接ID");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "链接名称");

            StringBuffer advName = new StringBuffer();
            if (cur.getAdvMediaId() == 1) {
                advName.append("头条");
                if (cur.getAdvVersion() == 1) {
                    advName.append("-1.0账户");
                } else if (cur.getAdvVersion() == 2) {
                    advName.append("-2.0账户");
                }
            } else if (cur.getAdvMediaId() == 2) {
                advName.append("adq");
            } else if (cur.getAdvMediaId() == 3) {
                advName.append("mp");
            } else if (cur.getAdvMediaId() == 4) {
                advName.append("百度");
            } else if (cur.getAdvMediaId() == 5) {
                advName.append("快手");
            } else if (cur.getAdvMediaId() == 6) {
                advName.append("微博");
            }
            row.add(advName.toString());
            CollUtil.addNoRepeat(rowHeadNames, "投放媒体");

            row.add(cur.getShortLink());
            CollUtil.addNoRepeat(rowHeadNames, "短链");

            row.add(cur.getPromoteLink());
            CollUtil.addNoRepeat(rowHeadNames, "小程序路径");

            row.add(cur.getInPage());
            CollUtil.addNoRepeat(rowHeadNames, "入口页面");

            row.add(cur.getCreateTime());
            CollUtil.addNoRepeat(rowHeadNames, "创建时间");

            if (cur.getBackType() == 1) {
                row.add("全局规则");
            } else if (cur.getBackType() == 2) {
                row.add("自定义规则");
            }
            CollUtil.addNoRepeat(rowHeadNames, "相关设置-回传规则");

            if (cur.getPayType() == 1) {
                row.add("全局模板");
            } else if (cur.getPayType() == 2) {
                row.add("自定义模板");
            }
            CollUtil.addNoRepeat(rowHeadNames, "相关设置-充值模板");

            row.add(cur.getNumAll());
            CollUtil.addNoRepeat(rowHeadNames, "用户数据-新增用户数");
            row.add(cur.getNumDay());
            CollUtil.addNoRepeat(rowHeadNames, "用户数据-今日新增");
            row.add(cur.getPv());
            CollUtil.addNoRepeat(rowHeadNames, "用户数据-PV");
            row.add(cur.getUv());
            CollUtil.addNoRepeat(rowHeadNames, "用户数据-UV");

            row.add(cur.getMoneyAll());
            CollUtil.addNoRepeat(rowHeadNames, "总充值数据-充值金额");
            row.add(cur.getNumAllRech());
            CollUtil.addNoRepeat(rowHeadNames, "总充值数据-充值人数");
            row.add(cur.getNumAllOrder());
            CollUtil.addNoRepeat(rowHeadNames, "总充值数据-充值笔数");
            row.add(cur.getMoneyAllArppu());
            CollUtil.addNoRepeat(rowHeadNames, "总充值数据-ARPPU");

            row.add(cur.getMoneyAllDay());
            CollUtil.addNoRepeat(rowHeadNames, "今日充值数据-充值金额");
            row.add(cur.getNumDay());
            CollUtil.addNoRepeat(rowHeadNames, "今日充值数据-充值人数");
            row.add(cur.getNumDayOrder());
            CollUtil.addNoRepeat(rowHeadNames, "今日充值数据-充值笔数");
            row.add(cur.getMoneyDayArppu());
            CollUtil.addNoRepeat(rowHeadNames, "今日充值数据-ARPPU");

            row.add(cur.getAdvUserName());
            CollUtil.addNoRepeat(rowHeadNames, "优化师");
            row.add(cur.getProfitAll());
            CollUtil.addNoRepeat(rowHeadNames, "利润");
            row.add(cur.getProfitAllRate());
            CollUtil.addNoRepeat(rowHeadNames, "利润-利润率");
            row.add(cur.getCostAll());
            CollUtil.addNoRepeat(rowHeadNames, "利润-总成本");
            row.add(cur.getOfficialName());
            CollUtil.addNoRepeat(rowHeadNames, "归属公众号");
            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "归属应用-名称");
            if (cur.getMiniType() == 1) {
                row.add("微信小程序");
            } else if (cur.getMiniType() == 2) {
                row.add("抖音小程序");
            } else if (cur.getMiniType() == 3) {
                row.add("H5");
            } else if (cur.getMiniType() == 4) {
                row.add("百度小程序");
            }
            CollUtil.addNoRepeat(rowHeadNames, "归属应用-类型");
            row.add(cur.getAdvUserName());
            CollUtil.addNoRepeat(rowHeadNames, "归属分销商");
            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "ROI梯度表-每日ROI数据";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 导出免费短剧渠道链接列表
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportFreeLinkList(SessionVO sessionVO, FastLinkQueryVO params) {
        String key = StaticVar.EXPORT_FREE_LINK_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        if (!StrUtil.isEmpty(params.getQueryCreateTime())) {
            String[] queryTime = params.getQueryCreateTime().split(" - ");
            params.setBeginTime(queryTime[0] + " 00:00:00");
            params.setEndTime(queryTime[1] + " 23:59:59");
        }
        if (!StrUtil.isEmpty(params.getQueryName())) {
            params.setLinkName(params.getQueryName());
        }
        if (StrUtil.isInteger(params.getQueryName())) {
            params.setId(Integer.parseInt(params.getQueryName()));
        }
        params.setBeginStatis(DateUtil.format07(DateUtil.beginOfDay()));
        params.setEndStatis(DateUtil.format07(DateUtil.endOfDay()));
        List<FastLinkQueryVO> fastLinkQueryList = fastLinkMapper.queryFreeLinkList(params);

        // 查询广告收入
        String linkIds = fastLinkQueryList.stream()
                .map(FastLinkQueryVO::getId).distinct()
                .map(String::valueOf).reduce((a, b) -> a + "," + b)
                .orElse("");
        for (FastLinkQueryVO cur : fastLinkQueryList) {
            cur.setEncryptionId(encode(cur.getId()));
            // 相关配置
            StringBuffer sb = new StringBuffer();
            sb.append("回传规则：");
            if (cur.getBackType() == 1) {
                sb.append("全局规则");
            } else {
                sb.append("自定义规则");
            }
            sb.append("\n");
            sb.append("充值模板：");
            if (cur.getPayType() == 1) {
                sb.append("全局模板");
            } else if (cur.getPayType() == 2) {
                sb.append("自定义模板");
            } else if (cur.getPayType() == 3) {
                sb.append("通用模板");
            }
            cur.setSetInfo(sb.toString());
            // 入口页面
            String inPage = cur.getDramaName() + "-第" + cur.getSeriesNum() + "集";
            cur.setInPage(inPage);
            // pv、uv
//            JSONObject condition = new JSONObject();
//            condition.put("linkId", cur.getId() + "");// 查询列要在tablestore的多元索引中
//            IndexSearchReply<FastBackToutiaoPO> reply = TableStoreUtil.andQueryCount(tableStoreProperties.getFasBackToutiaoTable(), tableStoreProperties.getFasBackToutiaoTableIndex(), condition, FastBackToutiaoPO.class);
//            cur.setPv(reply.totalCount());
            Long pvCount = fastStatisLinkPvMapper.queryLinkPVCount(cur.getId());
            cur.setPv(pvCount);
            cur.setUv(cur.getNumAll());
            // 利润=广告总收入-投放消耗总成本
            BigDecimal income = DoubleUtil.subB(defaultIfNull(cur.getAdIncomeAll(), BigDecimal.ZERO), defaultIfNull(cur.getCostAll(), BigDecimal.ZERO));
            // 回报率(ROI)=（广告总收入➗投放总消耗）*100%
            BigDecimal returnRatio = DoubleUtil.mulB(
                    DoubleUtil.divB4Zero(defaultIfNull(cur.getAdIncomeAll(), BigDecimal.ZERO),
                            defaultIfNull(cur.getCostAll(), BigDecimal.ZERO)),
                    new BigDecimal("100"));
            // 累计回传率=该渠道下已回传用户数➗累计新增用户数
            BigDecimal totalBackRatio = BigDecimal.ZERO;
            if (cur.getNumAll() > 0) {
                totalBackRatio = DoubleUtil.mulB(DoubleUtil.divB(cur.getBackUserNum(),
                                new BigDecimal(cur.getNumAll())),
                        new BigDecimal("100"));
            }
            cur.setIncome(income);
            cur.setReturnRatio(returnRatio);
            cur.setTotalBackRatio(totalBackRatio);
            fastLinkService.setGlobalNum(cur, null);
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastLinkQueryVO cur : fastLinkQueryList) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getId());
            CollUtil.addNoRepeat(rowHeadNames, "链接ID");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "链接名称");

            StringBuffer advName = new StringBuffer();
            if (cur.getAdvMediaId() == 1) {
                advName.append("头条");
                Integer advVersion = defaultIfNull(cur.getAdvVersion(), 2);
                if (advVersion == 1) {
                    advName.append("-1.0账户");
                } else {
                    advName.append("-2.0账户");
                }
            } else if (cur.getAdvMediaId() == 2) {
                advName.append("adq");
            } else if (cur.getAdvMediaId() == 3) {
                advName.append("mp");
            } else if (cur.getAdvMediaId() == 4) {
                advName.append("百度");
            }
            row.add(advName.toString());
            CollUtil.addNoRepeat(rowHeadNames, "投放媒体");

            String target = cur.getMixFlag() == 1 ? "混合(付费+免费)" : "免费";
            row.add(target);
            CollUtil.addNoRepeat(rowHeadNames, "转化目标");

            row.add(cur.getShortLink());
            CollUtil.addNoRepeat(rowHeadNames, "短链");

            row.add(cur.getInPage());
            CollUtil.addNoRepeat(rowHeadNames, "入口页面");

            row.add(cur.getCreateTime());
            CollUtil.addNoRepeat(rowHeadNames, "创建时间");

            if (cur.getBackType() == 1) {
                row.add("全局规则");
            } else if (cur.getBackType() == 2) {
                row.add("自定义规则");
            }
            CollUtil.addNoRepeat(rowHeadNames, "相关设置-回传规则");

            row.add(cur.getSetInfo());
            CollUtil.addNoRepeat(rowHeadNames, "剧集设置");

            row.add(cur.getAdIncomeAll());
            CollUtil.addNoRepeat(rowHeadNames, "广告收入");

            row.add(cur.getNumAll());
            CollUtil.addNoRepeat(rowHeadNames, "新增用户数");

            row.add(cur.getNumDay());
            CollUtil.addNoRepeat(rowHeadNames, "今日新增");

            row.add(cur.getPv());
            CollUtil.addNoRepeat(rowHeadNames, "pv");

            row.add(cur.getUv());
            CollUtil.addNoRepeat(rowHeadNames, "uv");

            row.add(cur.getAdIncomeDay());
            CollUtil.addNoRepeat(rowHeadNames, "今日广告收入");

            row.add(cur.getAdWatchMemberNumDay());
            CollUtil.addNoRepeat(rowHeadNames, "今日观看人数");

            row.add(cur.getAdWatchNumDay());
            CollUtil.addNoRepeat(rowHeadNames, "今日观看人次");

            row.add(cur.getAdUnlockMemberNumDay());
            CollUtil.addNoRepeat(rowHeadNames, "解锁成功人数");

            row.add(cur.getAdUnlockNumDay());
            CollUtil.addNoRepeat(rowHeadNames, "解锁成功人次");

            row.add(cur.getRechargeMoney());
            CollUtil.addNoRepeat(rowHeadNames, "当日充值金额");

            row.add(cur.getRechargeMemberNum());
            CollUtil.addNoRepeat(rowHeadNames, "当日充值人数");

            row.add(cur.getRechargeNum());
            CollUtil.addNoRepeat(rowHeadNames, "当日充值笔数");

            row.add(cur.getTotalRechargeMoney());
            CollUtil.addNoRepeat(rowHeadNames, "累计充值金额");

            row.add(cur.getTotalRechargeMemberNum());
            CollUtil.addNoRepeat(rowHeadNames, "累计充值人数");

            row.add(cur.getTotalRechargeNum());
            CollUtil.addNoRepeat(rowHeadNames, "累计充值笔数");

            row.add(cur.getTodayIncome());
            CollUtil.addNoRepeat(rowHeadNames, "累计收入");

            row.add(cur.getIncome());
            CollUtil.addNoRepeat(rowHeadNames, "利润");

            row.add(cur.getReturnRatio());
            CollUtil.addNoRepeat(rowHeadNames, "回报率");

            row.add(cur.getCostAll());
            CollUtil.addNoRepeat(rowHeadNames, "总成本");

            row.add(cur.getTotalBackRatio());
            CollUtil.addNoRepeat(rowHeadNames, "累计回传率");

            row.add(cur.getAdvUserName());
            CollUtil.addNoRepeat(rowHeadNames, "优化师");

            if (sessionVO.getRetailId() == 0) {// 管理平台
                row.add(cur.getRetailName());
                CollUtil.addNoRepeat(rowHeadNames, "分销商");
            }

            row.add(fastLinkService.getMonitorUrl(cur));
            CollUtil.addNoRepeat(rowHeadNames, "监测链接");

            String promoteLink = cur.getPromoteLink();
            if (cur.getAdvMediaId() != null && cur.getAdvMediaId() == 1
                    && StrUtil.isNotEmpty(cur.getPromoteLink())) {
                promoteLink = fastLinkService.addMoreParams(promoteLink);
            }
            row.add(promoteLink);
            CollUtil.addNoRepeat(rowHeadNames, "推广链接地址(启动参数)");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "推广链接（免费模式)报表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    public ResultVO<?> exportFreeDayRoiList(SessionVO sessionVO, FastLinkQueryVO params) {
        String key = StaticVar.EXPORT_FREE_DAY_ROI_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        FastStatisLinkFreePO query = new FastStatisLinkFreePO();
        if (params.getQueryCreateTime() != null && params.getQueryCreateTime().length() > 0) {
            String[] queryTime = params.getQueryCreateTime().split(" - ");
            query.setBeginStatis(queryTime[0] + " 00:00:00");
            query.setEndStatis(queryTime[1] + " 23:59:59");
        }
        query.setLinkId(params.getLinkId());
        List<FastStatisLinkFreePO> list = fastStatisLinkFreeMapper.getFreeDayRoiList(query);

        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        if (CollUtil.isNotEmpty(list) && list.size() > 0) {
            for (FastStatisLinkFreePO item : list) {
                if (item.getStatisId() != null) {
                    item.setEncryptionId(encode(item.getStatisId()));
                }
                Map<String, Object> summaryD60Result = summaryD60(item);
                BigDecimal adMoneyConsume = defaultIfNull(item.getTotalCost(), BigDecimal.ZERO);
                Integer convertMemberCount = defaultIfNull(item.getConvertMemberDay(), 0);
                Integer addMemberCount = defaultIfNull(item.getAddMemberNum(), 0);
                BigDecimal convertMemberCost = BigDecimal.ZERO;      // 转化用户成本
                if (convertMemberCount > 0) {
                    convertMemberCost = DoubleUtil.divB(adMoneyConsume, convertMemberCount);
                }
                // 消耗金额➗累计新增用户数
                BigDecimal addMemberCost = BigDecimal.ZERO;         // 新增用户成本
                if (addMemberCount > 0) {
                    addMemberCost = DoubleUtil.divB(adMoneyConsume, addMemberCount);
                }
                item.setMemberCost(addMemberCost);
                item.setUnlockCost(convertMemberCost);
                item.setUnlcokMemberNum(defaultIfNull((Integer) summaryD60Result.get("adWatchMemberSummary"), 0));
                item.setUnlockIncome(defaultIfNull((BigDecimal) summaryD60Result.get("adIncomSummary"), BigDecimal.ZERO));
                BigDecimal returnRatio = BigDecimal.ZERO;
                if (item.getTotalCost().compareTo(BigDecimal.ZERO) > 0) {
                    returnRatio = DoubleUtil.divB(defaultIfNull((BigDecimal) summaryD60Result.get("adIncomSummary"), BigDecimal.ZERO)
                            , item.getTotalCost());
                    returnRatio = DoubleUtil.mulB(returnRatio, new BigDecimal(100));
                }
                item.setReturnRatio(returnRatio);
                item.setUnlockMemberD60(item.getAdWatchMemberD60());
                item.setUnlockIncomeD60(item.getAdIncomeD60());
                item.setReturnRatioD60(item.getReturnRatioD60());
                item.setSummaryD60Result(summaryD60Result);

                List<Object> row = new ArrayList<>();// 导出的数据(一行)

                row.add(item.getStatisDate());
                CollUtil.addNoRepeat(rowHeadNames, "日期");

                row.add(item.getAddMemberNum());
                CollUtil.addNoRepeat(rowHeadNames, "新增用户数");

                row.add(item.getTotalCost());
                CollUtil.addNoRepeat(rowHeadNames, "总成本");

                row.add(item.getMemberCost());
                CollUtil.addNoRepeat(rowHeadNames, "用户成本");

                row.add(item.getUnlockCost());
                CollUtil.addNoRepeat(rowHeadNames, "付费成本");

                row.add(item.getAdWatchMemberNum());
                CollUtil.addNoRepeat(rowHeadNames, "累计观看用户");

                row.add(item.getAdIncome());
                CollUtil.addNoRepeat(rowHeadNames, "累计广告收入");

                row.add(item.getReturnRatio());
                CollUtil.addNoRepeat(rowHeadNames, "累计回报率");

                String[] adWatchMemberD60 = item.getAdWatchMemberD60().split(",");
                String[] adIncomeD60 = item.getAdIncomeD60().split(",");
                String[] returnRatioD60 = item.getReturnRatioD60().split(",");

                for (int h = 0; h < 61; h++) {
                    row.add(adWatchMemberD60[h] + "\n" + adIncomeD60[h] + "\n" + returnRatioD60[h]);
                    CollUtil.addNoRepeat(rowHeadNames, "D" + h);
                }
            }
        }
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "推广链接（免费模式)每日回收数据";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    public static Map<String, Object> summaryD60(FastStatisLinkFreePO item) {
        Map<String, Object> data = new HashMap<>();
        String[] aaArray = new String[61];
        String[] bbArray = new String[61];
        String[] ccArray = new String[61];
        if (StrUtil.isNotEmpty(item.getAdWatchMemberD60())) {
            aaArray = item.getAdWatchMemberD60().split(",");
        }
        if (StrUtil.isNotEmpty(item.getAdIncomeD60())) {
            bbArray = item.getAdIncomeD60().split(",");
        }
        if (StrUtil.isNotEmpty(item.getReturnRatioD60())) {
            ccArray = item.getReturnRatioD60().split(",");
        }
        Integer adWatchMemberSummary = 0;
        BigDecimal adIncomSummary = BigDecimal.ZERO;
        BigDecimal returnRatioSummary = BigDecimal.ZERO;
        for (int i = 0; i < 61; i++) {
            if (i < aaArray.length && aaArray[i] != null) {
                Integer aaInteger = Integer.valueOf(aaArray[i]);
                adWatchMemberSummary += aaInteger;
            }
            if (i < bbArray.length && bbArray[i] != null) {
                BigDecimal bbDecimal = new BigDecimal(bbArray[i]);
                adIncomSummary = DoubleUtil.addB(adIncomSummary, bbDecimal);
            }
            if (i < ccArray.length && ccArray[i] != null) {
                BigDecimal ccDecimal = new BigDecimal(ccArray[i]);
                returnRatioSummary = DoubleUtil.addB(returnRatioSummary, ccDecimal);
            }
        }
        data.put("adWatchMemberSummary", adWatchMemberSummary);
        data.put("adIncomSummary", adIncomSummary);
        data.put("returnRatioSummary", returnRatioSummary);
        return data;
    }
}
