/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.*;
import com.fast.mapper.promote.FastMemberLinkHisMapper;
import com.fast.mapper.promote.FastMemberLinkMapper;
import com.fast.mapper.promote.FastRecolorSetMapper;
import com.fast.po.member.*;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.promote.FastMemberLinkHisPO;
import com.fast.po.promote.FastMemberLinkPO;
import com.fast.po.promote.FastRecolorSetPO;
import com.fast.po.setting.FastSettingSystemPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.FastActionLogService;
import com.fast.service.member.FastMemberBackHandService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.setting.FastSettingSystemService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.mini.FastMiniVO;
import com.fast.vo.promote.FastLinkVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberLinkService extends BaseService {

    @Autowired
    private FastMemberLinkMapper memberLinkMapper;
    @Autowired
    private FastMemberLinkHisMapper memberLinkHisMapper;
    @Autowired
    private FastRecolorSetMapper fastRecolorSetMapper;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastActionLogService actionLogService;
    @Autowired
    private FastMemberBackMapper fastMemberBackMapper;
    @Autowired
    private FastMemberMapper fastMemberMapper;
    @Autowired
    private FastMemberDivideMapper fastMemberDivideMapper;
    @Autowired
    private FastMemberLinkClickidMapper fastMemberLinkClickidMapper;
    @Autowired
    private FastMemberSubLinkMapper fastMemberSubLinkMapper;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastMemberSubLinkHisMapper memberSubLinkHisMapper;
    @Autowired
    private FastMemberLinkAdvMapper fastMemberLinkAdvMapper;
    @Autowired
    private FastSettingSystemService fastSettingSystemService;
    @Autowired
    private FastMemberLinkLoadMapper fastMemberLinkLoadMapper;
    @Autowired
    private FastMemberBackHandService fastMemberBackHandService;

    /**
     * 查询用户关联的广告信息
     */
    public FastMemberLinkPO queryAdvByMemberIdRedis(Long memberId) {
        String key = StaticVar.MEMBER_ADV_DETAIL_V2 + memberId;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            FastMemberLinkPO mlParam = new FastMemberLinkPO();
            mlParam.setMemberId(memberId);
            FastMemberLinkPO mlPO = memberLinkMapper.queryAdvByMemberId(mlParam);
            res = JsonUtil.toString(mlPO);
            RedisUtil.set(key, res, 60 * 60 * 24);
        }
        if (StrUtil.isNotEmpty(res)) {
            return JsonUtil.toJavaObject(res, FastMemberLinkPO.class);
        }
        return null;
    }


    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkPO queryById(FastMemberLinkPO item) {
        return memberLinkMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkPO queryById(Long id) {
        FastMemberLinkPO itemParam = new FastMemberLinkPO();
        itemParam.setId(id);
        return memberLinkMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberLinkPO queryOne(FastMemberLinkPO item) {
        return memberLinkMapper.queryOne(item);
    }

    /**
     * 查询用户是否为新增(当天新注册或新染色)用户
     */
    public boolean queryMemberIsAdd(Long memberId) {
        Integer lastLinkTime = memberLinkMapper.queryLastLinkTime(memberId);
        if (lastLinkTime == null) {
            return false;
        }
        return lastLinkTime == DateUtil.getNowTime06();
    }

    /**
     * 查询用户是否为新增(当天新注册或新染色)用户
     */
    public boolean queryMemberIsAdd(Date lastLinkDate) {
        if (lastLinkDate == null) {
            return false;
        }
        return DateUtil.format06Int(lastLinkDate) == DateUtil.getNowTime06();
    }

    /**
     * 查询全部
     */
    public List<FastMemberLinkPO> queryList(FastMemberLinkPO item) {
        return memberLinkMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberLinkPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberLinkPO> list = memberLinkMapper.queryList(item);
        for (FastMemberLinkPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberLinkPO item) {
        return memberLinkMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberLinkPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (memberLinkMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberLinkPO> list) {
        if (memberLinkMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberLinkPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (memberLinkMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 验证挂载保护期
     * true非保护期，false保护期内
     */
    public boolean checkNotLoadProtect(Integer appType, FastMemberLinkPO memberLinkNew) {
        if (appType != 2) {
            return true;
        }
        String key = StaticVar.LOAD_MEMBER_PROTECT + memberLinkNew.getMemberId();// 是否挂载保护期内
        String res = RedisUtil.get(key);
        if (StrUtil.isNotEmpty(res) && StaticVar.OK.equals(res)) {
            actionLogService.log("load_protect", "触发挂载保护" + memberLinkNew.getMemberId());
            return false;
        }
        actionLogService.log("load_protect", "----未触发挂载保护" + memberLinkNew.getMemberId());
        return true;
    }

    /**
     * 设置保护期
     */
    public void addLoadProtect(Long memberId) {
        try {
            String code = "load_protect_hour";
            String key = StaticVar.LOAD_MEMBER_PROTECT + memberId;// 是否挂载保护期内
            String keyTime = StaticVar.SETTING_SYSTEM_CODE + code;
            String resTime = RedisUtil.get(keyTime);
            if (StrUtil.isEmpty(resTime)) {
                FastSettingSystemPO sParam = new FastSettingSystemPO();
                sParam.setCode(code);
                FastSettingSystemPO sPO = fastSettingSystemService.queryOne(sParam);
                if (sPO == null) {
                    resTime = "0";
                } else {
                    resTime = sPO.getContent();
                }
                RedisUtil.set(keyTime, resTime, 60 * 60);// 缓存1小时
            }
            if ("0".equals(resTime)) {
                // 无保护期
            } else {
                Integer hour = Integer.valueOf(resTime);
                RedisUtil.set(key, StaticVar.OK, 60 * 60 * hour);
            }
        } catch (Exception e) {
            log.error("----添加保护期异常");
        }
    }

    /**
     * 新增或更新用户推广链接
     * appType:1微信小程序，2抖音小程序，3h5，4快手小程序
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO updateLinkV1(FastMemberLinkPO memberLinkNew, FastMemberPO memberPO, Integer appType, String appId, String divideFrom) {
        Integer colorType = 0; // 0无处理1新增2染色
        if (!StrUtil.isEmpty(memberLinkNew.getLastLinkId()) && !StrUtil.isEmpty(memberLinkNew.getMemberId())) {
            FastMemberLinkPO memberlinkOld = new FastMemberLinkPO();
            memberlinkOld.setMemberId(memberLinkNew.getMemberId());
            memberlinkOld = queryOne(memberlinkOld);
            if (memberlinkOld == null) {
                if (StrUtil.isNotEmpty(memberLinkNew.getClickId()) && memberLinkNew.getClickId().length() > 200) {
                    // clickid长度超长，存到扩展表
                    String clickId = memberLinkNew.getClickId();
                    memberLinkNew.setClickId("save_in_fast_member_link_clickid_" + memberLinkNew.getMemberId());
                    FastMemberLinkClickidPO clickidPO = new FastMemberLinkClickidPO();
                    clickidPO.setId(memberLinkNew.getMemberId());
                    clickidPO.setClickId(clickId);
                    fastMemberLinkClickidMapper.insertSelective(clickidPO);
                }
                if (memberLinkMapper.insertSelective(memberLinkNew) == 0) {
                    transactionRollBack();
                    return ResultVO.error(StaticStr.ADD_FAILED);
                }
                if (StrUtil.isNotEmpty(memberLinkNew.getMaterialId())) {
                    // 存在素材id
                    FastMemberLinkAdvPO maPO = new FastMemberLinkAdvPO();
                    maPO.setId(memberLinkNew.getMemberId());
                    maPO.setMaterialId(memberLinkNew.getMaterialId());
                    maPO.setCreateTime(DateUtil.getNowDate());
                    fastMemberLinkAdvMapper.insertSelective(maPO);
                }
                memberLinkNew.setCreateNew(1);
                colorType = 1;// 新增
                // 次归属
                if (biggerZero(memberLinkNew.getSubLinkId())) {
                    // 次归属添加保护期
                    addLoadProtect(memberLinkNew.getMemberId());
                    updateMemberSubLink(memberLinkNew, memberlinkOld, memberPO);
                }
                if (memberLinkNew.getLastLinkId() != null && memberLinkNew.getLastLinkId() > 0) {
                    fastMemberBackHandService.insertMemberBackHand(memberLinkNew.getLastLinkId(), memberLinkNew.getMemberId(), memberLinkNew.getClickId(), memberLinkNew.getPromotionId());
                }
                // 保存挂载需求id
                if (memberLinkNew.getDemandId() != null && memberLinkNew.getDemandId() > 0) {
                    Date nowTime = DateUtil.getNowDate();
                    FastMemberLinkLoadPO loadPO = new FastMemberLinkLoadPO();
                    loadPO.setCreateTime(nowTime);
                    loadPO.setUpdateTime(nowTime);
                    loadPO.setDemandId(memberLinkNew.getDemandId());
                    loadPO.setId(memberLinkNew.getMemberId());
                    fastMemberLinkLoadMapper.insertSelective(loadPO);
                }
            } else {
                // 更新用户渠道信息
                Integer linkType = 0;// 0无link 1推广，2运营，3挂载
//				FastLinkPO linkOrgPO = fastLinkMapper.queryById(memberLinkNew.getLastLinkId());
                FastLinkVO linkOrgVO = fastLinkService.queryInfoByRedis(memberLinkNew.getLastLinkId());
                if (linkOrgVO != null) {
                    linkType = linkOrgVO.getLinkType();
                    linkType = linkType == null ? 1 : linkType;
                }
                // 运营链接不染色，推广链接染色
                if (memberLinkNew.getLastLinkId() > 0 && linkType == 1) {
                    actionLogService.log("member_color", "memberId:" + memberLinkNew.getMemberId() + ",linkId:" + memberLinkNew.getLastLinkId());
                    if (!Objects.equals(memberlinkOld.getLastLinkId(), memberLinkNew.getLastLinkId())) {
                        // 更新用户最新推广链接信息，根据染色机制判断是否更新：推广链接不同，并且距离上一次最新推广链接时间已经满24小时，则刷新为新推广链接，回传充值统计作为归因推广链接使用
                        Integer recolorHours = getRecolorHour(appType, memberLinkNew.getOfficialId(), appId);// 获取染色时间
                        if (recolorHours == null) {
                            recolorHours = 24;
                        }
                        if ((DateUtil.getNowDate().getTime() - memberlinkOld.getLastLinkTime().getTime()) > (recolorHours * 60 * 60 * 1000L)) {
                            // 开始染色逻辑
                            Integer linkIdOrg = memberlinkOld.getLastLinkId();
                            if (linkIdOrg == null) {
                                linkIdOrg = 0;
                            }
                            Date lastLinkTime = memberlinkOld.getLastLinkTime(); // 上次染色进来时间

                            memberlinkOld.setOfficialId(memberLinkNew.getOfficialId());
                            memberlinkOld.setLastLinkId(memberLinkNew.getLastLinkId());
                            memberlinkOld.setLastLinkTime(memberLinkNew.getLastLinkTime());
                            memberlinkOld.setIp(memberLinkNew.getIp());
                            memberlinkOld.setUa(memberLinkNew.getUa());
                            if (StrUtil.isNotEmpty(memberLinkNew.getClickId()) && memberLinkNew.getClickId().length() > 200) {
                                String clickId = memberLinkNew.getClickId();
                                memberlinkOld.setClickId("save_in_fast_member_link_clickid_" + memberLinkNew.getMemberId());
                                FastMemberLinkClickidPO clickidPO = new FastMemberLinkClickidPO();
                                clickidPO.setId(memberLinkNew.getMemberId());
                                clickidPO.setClickId(clickId);
                                FastMemberLinkClickidPO clickOrg = fastMemberLinkClickidMapper.queryById(memberLinkNew.getMemberId());
                                if (clickOrg == null) {
                                    // 新增
                                    fastMemberLinkClickidMapper.insertSelective(clickidPO);
                                } else {
                                    // 更新
                                    fastMemberLinkClickidMapper.updateById(clickidPO);
                                }
                            } else {
                                memberlinkOld.setClickId(memberLinkNew.getClickId());
                            }
                            memberlinkOld.setCid(memberLinkNew.getCid());
                            memberlinkOld.setAid(memberLinkNew.getAid());
                            memberlinkOld.setPromotionId(memberLinkNew.getPromotionId());
                            memberlinkOld.setProjectId(memberLinkNew.getProjectId());
                            memberlinkOld.setCtype(memberLinkNew.getCtype());
                            memberlinkOld.setUpdateTime(DateUtil.getNowDate());

//                            TouTiaoVO toutiaoVO = backRuleService.getToutiaoByLink(memberLinkNew.getIp(), memberLinkNew.getUa(), memberLinkNew.getLastLinkId());
//                            if(toutiaoVO != null) {
//                            	if(StrUtil.isEmpty(memberlinkOld.getAid())) {
//                            		memberlinkOld.setAid(toutiaoVO.getAid());                            		
//                            	}
//                            	if(StrUtil.isEmpty(memberlinkOld.getCid())) {
//                            		memberlinkOld.setCid(toutiaoVO.getCid());                            		
//                            	}
//                            	if(StrUtil.isEmpty(memberlinkOld.getPromotionId())) {
//                            		memberlinkOld.setPromotionId(toutiaoVO.getPromotionId());                            		
//                            	}
//                            	if(StrUtil.isEmpty(memberlinkOld.getProjectId())) {
//                            		memberlinkOld.setProjectId(toutiaoVO.getProjectId());                            		
//                            	}
//                            }
                            if (memberLinkMapper.updateById(memberlinkOld) == 0) {
                                transactionRollBack();
                                return ResultVO.error(StaticStr.UPDATE_FAILED);
                            }
                            colorType = 2;// 染色成功
                            // 删除旧的素材id
                            fastMemberLinkAdvMapper.delById(memberLinkNew.getMemberId());
                            if (StrUtil.isNotEmpty(memberLinkNew.getMaterialId())) {
                                // 存在素材id
                                FastMemberLinkAdvPO maPO = new FastMemberLinkAdvPO();
                                maPO.setId(memberLinkNew.getMemberId());
                                maPO.setMaterialId(memberLinkNew.getMaterialId());
                                maPO.setCreateTime(DateUtil.getNowDate());
                                fastMemberLinkAdvMapper.insertSelective(maPO);
                            }
                            // 添加用户，被资产染色
                            if (memberLinkNew.getVersionType() != null && memberLinkNew.getVersionType() == 3) {
                                FastMemberBackPO backPO = fastMemberBackMapper.queryById(memberLinkNew.getMemberId());
                                if (backPO == null) {
                                    FastMemberBackPO mbPO = new FastMemberBackPO();
                                    mbPO.setId(memberPO.getId());
                                    mbPO.setBackmode(1);
                                    fastMemberBackMapper.insertSelective(mbPO);
                                    actionLogService.log("member_color_property", "资产化染色：memberId:" + memberPO.getId());
                                }
                            } else {
                                actionLogService.log("member_color_property", "非资产化染色：memberId:" + memberPO.getId());
                            }
                            actionLogService.log("member_color", "ok_memberId:" + memberLinkNew.getMemberId() + ",linkId:" + memberLinkNew.getLastLinkId());
                            // 记录到数据库
                            FastMemberLinkHisPO memberLinkHis = new FastMemberLinkHisPO();
                            memberLinkHis.setMemberId(memberLinkNew.getMemberId());
                            memberLinkHis.setLinkId(memberLinkNew.getLastLinkId());// 新的
                            memberLinkHis.setCreateTime(memberLinkNew.getLastLinkTime());// 染走时间
                            memberLinkHis.setMiniId(memberPO.getMiniId());// 旧的
                            memberLinkHis.setRetailId(memberPO.getRetailId());// 旧的
                            memberLinkHis.setOfficialId(memberPO.getOfficialId());// 旧的
                            memberLinkHis.setLinkIdOrg(linkIdOrg);// 旧的
                            memberLinkHis.setColorIn(lastLinkTime);

                            if (memberLinkHisMapper.insertSelective(memberLinkHis) == 1) {
                                actionLogService.log("divide_member_udpate_color", "更新用户染色" + memberPO.getId() + "成功");
                            }
                            memberLinkNew.setColorFlag(1);// 染色成功
                            // 更新用户表
                            if (biggerZero(memberPO.getId()) && biggerZero(memberLinkNew.getRetailId()) && biggerZero(memberPO.getOfficialId()) && StrUtil.isNotEmpty(divideFrom)) {
                                FastMemberPO memberParam = new FastMemberPO();
                                memberParam.setId(memberPO.getId());
                                memberParam.setRetailId(memberLinkNew.getRetailId());
                                memberParam.setOfficialId(memberLinkNew.getOfficialId());
                                if (fastMemberMapper.updateById(memberParam) == 0) {
                                    actionLogService.log("divide_member_udpate", "更新用户分销商和公众号" + memberPO.getId() + "失败");
                                } else {
                                    actionLogService.log("divide_member_udpate", "更新用户分销商和公众号" + memberPO.getId() + "失败");
                                }
                                // 生成用户分流标志
                                FastMemberDividePO dPO = new FastMemberDividePO();
                                dPO.setMemberId(memberPO.getId());
                                dPO.setRetailId(memberLinkNew.getRetailId());
                                dPO.setOfficialId(memberLinkNew.getOfficialId());
                                dPO.setCreateTime(DateUtil.getNowDate());
                                dPO.setDivideFlag(1);
                                fastMemberDivideMapper.insertSelective(dPO);
                            }
                            // 清除挂载渠道
                            // 更新次归属渠道信息
                            FastMemberSubLinkPO slPO = new FastMemberSubLinkPO();
                            slPO.setId(memberLinkNew.getMemberId());
                            slPO.setUpdateTime(DateUtil.getNowDate());
                            slPO.setDelFlag(1);
                            fastMemberSubLinkMapper.updateById(slPO);
                            fastMemberBackHandService.insertMemberBackHand(memberLinkNew.getLastLinkId(), memberLinkNew.getMemberId(), memberLinkNew.getClickId(), memberLinkNew.getPromotionId());
                        } else {
                            // 调取库中用户的lastLinkId，传递给后续sessionVo
                            memberLinkNew.setLastLinkId(memberlinkOld.getLastLinkId());
                            // 不染色的情况下，如果存在次归属渠道，当前登录归属到次归属渠道上
                            FastMemberSubLinkPO slPO = fastMemberSubLinkMapper.queryById(memberLinkNew.getMemberId());
                            if (slPO != null) {
                                memberLinkNew.setLastLinkId(slPO.getSubLinkId());
                            }
                        }
                    }
                } else {
                    // 次归属进来（只要从挂载进来就立马归属给挂载链接，不用24小时保护投放渠道，和慧产品再次明确逻辑20240716）
                    if (biggerZero(memberLinkNew.getSubLinkId())) {
                        // 挂载进来，再次增加保护期
                        addLoadProtect(memberLinkNew.getMemberId());
                        updateMemberSubLink(memberLinkNew, memberlinkOld, memberPO);
                        // 当前登录用户归属次链接
                        memberLinkNew.setLastLinkId(memberLinkNew.getSubLinkId());
                        // 保存挂载需求id
                        if (memberLinkNew.getDemandId() != null && memberLinkNew.getDemandId() > 0) {
                            Date nowTime = DateUtil.getNowDate();
                            FastMemberLinkLoadPO loadPO = new FastMemberLinkLoadPO();
                            loadPO.setCreateTime(nowTime);
                            loadPO.setUpdateTime(nowTime);
                            loadPO.setDemandId(memberLinkNew.getDemandId());
                            loadPO.setId(memberLinkNew.getMemberId());
                            fastMemberLinkLoadMapper.insertSelective(loadPO);
                        }
                    }
                    // 其他复返进入小程序的情况
                    else {
                        // 如果是从公众号等其他途径进入，传递过来的lastLinkId 为0, 但用户实际 在库中存在，不刷新 库中最新Linkid和时间
                        // 调取库中用户的lastLinkId，传递给后续sessionVo
                        Integer lastLinkId = 0;
                        if (memberlinkOld.getLastLinkId() != null && memberlinkOld.getLastLinkId() > 0 && checkNotLoadProtect(appType, memberLinkNew)) {
                            lastLinkId = memberlinkOld.getLastLinkId();
                        } else {
                            // 查询次归属
                            FastMemberSubLinkPO slPO = fastMemberSubLinkMapper.queryById(memberLinkNew.getMemberId());
                            if (slPO != null) {
                                lastLinkId = slPO.getSubLinkId();
                            }
                        }
                        memberLinkNew.setLastLinkId(lastLinkId);
                    }
                }
            }
            // 记录用户历史推广链接表,存储到阿里云tablestore中
//            FastMemberLinkHisPO memberLinkHis = new FastMemberLinkHisPO();
//            memberLinkHis.setMemberId(memberLinkNew.getMemberId());
//            memberLinkHis.setLinkId(memberLinkNew.getLastLinkId());
//            memberLinkHis.setLinkTime(memberLinkNew.getLastLinkTime());
//            memberLinkHis.setIp(memberLinkNew.getIp());
//            memberLinkHis.setUa(memberLinkNew.getUa());
//            memberLinkHis.setClickId(memberLinkNew.getClickId());
//            memberLinkHis.setCreateTime(memberLinkNew.getCreateTime());
//            memberLinkHis.setUa(memberLinkNew.getUa());
//            //向阿里云tablestore添加一条记录
//            TableStoreUtil.putRow(tableStoreProperties.getFasMemberLinkHisTable(), JsonUtil.toJSONObject(memberLinkHis), tableStoreProperties.getFasMemberLinkHisTablePk(), StrUtil.getUUID());
        } else if (appType == 2) {
            // 抖音小程序，默认为用户当前的渠道id
            FastMemberLinkPO memberlinkOld = new FastMemberLinkPO();
            memberlinkOld.setMemberId(memberLinkNew.getMemberId());
            memberlinkOld = queryOne(memberlinkOld);
            if (memberlinkOld != null && memberlinkOld.getLastLinkId() != null && memberlinkOld.getLastLinkId() > 0) {
                memberLinkNew.setLastLinkId(memberlinkOld.getLastLinkId());
            } else {
                // 查询挂载
                FastMemberSubLinkPO subLinkPO = fastMemberSubLinkMapper.queryById(memberLinkNew.getMemberId());
                if (subLinkPO != null && subLinkPO.getSubLinkId() != null && subLinkPO.getSubLinkId() > 0) {
                    memberLinkNew.setLastLinkId(subLinkPO.getSubLinkId());
                }
            }
        }
        if (Objects.nonNull(memberLinkNew.getLastLinkId())) {
            FastLinkVO fastLinkVO = fastLinkService.queryInfoByRedis(memberLinkNew.getLastLinkId());
            // log.info("次归属更新用户链接contentType:{}", fastLinkVO);
            if (Objects.nonNull(fastLinkVO)) {
                FastMemberPO memberContentTypeParam = new FastMemberPO();
                memberContentTypeParam.setContentType(fastLinkVO.getContentType());
                memberContentTypeParam.setId(memberPO.getId());
                memberPO.setContentType(fastLinkVO.getContentType());
                fastMemberMapper.updateById(memberContentTypeParam);
            }
        }
        Map<String, Object> results = new HashMap<>();
        results.put("colorType", colorType);
        return ResultVO.success(results);
    }


    /**
     * 更新用户的次归属渠道
     */
    private void updateMemberSubLink(FastMemberLinkPO memberLinkNew, FastMemberLinkPO memberLinkOld, FastMemberPO memberPO) {
        Date timeNow = DateUtil.getNowDate();
        FastMemberSubLinkPO slPO = fastMemberSubLinkMapper.queryById(memberLinkNew.getMemberId());
        Integer lastLinkId = 0;
        Date lastLinkTime = null;
        if (memberLinkOld != null && memberLinkOld.getLastLinkId() > 0) {
            lastLinkId = memberLinkOld.getLastLinkId();
            lastLinkTime = memberLinkOld.getLastLinkTime();
        }
        if (slPO == null) {
            // 新增次归属
            slPO = new FastMemberSubLinkPO();
            slPO.setId(memberLinkNew.getMemberId());
            slPO.setSubLinkId(memberLinkNew.getSubLinkId());
            slPO.setSubLinkTime(timeNow);
            slPO.setSubLinkEndTime(StaticVar.MAX_DATE);
            slPO.setCreateTime(timeNow);
            slPO.setUpdateTime(timeNow);
            fastMemberSubLinkMapper.insertSelective(slPO);
            // 增加变更记录
            FastMemberSubLinkHisPO slHisPO = new FastMemberSubLinkHisPO();
            slHisPO.setCreateTime(timeNow);
            slHisPO.setMemberId(memberLinkNew.getMemberId());
            slHisPO.setLinkId(lastLinkId);
            slHisPO.setLinkTime(lastLinkTime);
            if (biggerZero(lastLinkId)) {
                FastLinkPO linkPO = fastLinkService.queryInfoByRedis(lastLinkId);
                slHisPO.setLinkName(linkPO.getLinkName());
                FastLinkPO subLinkPO = fastLinkService.queryInfoByRedis(memberLinkNew.getSubLinkId());
                slHisPO.setLinkSubType(subLinkPO.getLinkSubType());
            }
            slHisPO.setSubLinkId(memberLinkNew.getSubLinkId());
            slHisPO.setMiniId(memberPO.getMiniId());
            slHisPO.setOfficialId(memberPO.getOfficialId());
            slHisPO.setRetailId(memberPO.getRetailId());
            slHisPO.setSubLinkTime(timeNow);
            memberSubLinkHisMapper.insertSelective(slHisPO);
        } else {
            // 不同才变更
            if (!memberLinkNew.getSubLinkId().equals(slPO.getSubLinkId())) {
                // 更新次归属
                slPO.setId(memberLinkNew.getMemberId());
                slPO.setSubLinkId(memberLinkNew.getSubLinkId());
                slPO.setSubLinkTime(timeNow);
                slPO.setSubLinkEndTime(StaticVar.MAX_DATE);
                slPO.setCreateTime(timeNow);
                slPO.setUpdateTime(timeNow);
                fastMemberSubLinkMapper.updateById(slPO);
                // 增加变更记录
                FastMemberSubLinkHisPO slHisPO = new FastMemberSubLinkHisPO();
                slHisPO.setCreateTime(timeNow);
                slHisPO.setMemberId(memberLinkNew.getMemberId());
                slHisPO.setLinkId(lastLinkId);
                slHisPO.setLinkTime(lastLinkTime);
                if (biggerZero(lastLinkId)) {
                    FastLinkPO linkPO = fastLinkService.queryInfoByRedis(lastLinkId);
                    slHisPO.setLinkName(linkPO.getLinkName());
                    FastLinkPO subLinkPO = fastLinkService.queryInfoByRedis(memberLinkNew.getSubLinkId());
                    slHisPO.setLinkSubType(subLinkPO.getLinkSubType());
                }
                slHisPO.setMiniId(memberPO.getMiniId());
                slHisPO.setOfficialId(memberPO.getOfficialId());
                slHisPO.setRetailId(memberPO.getRetailId());
                slHisPO.setSubLinkId(memberLinkNew.getSubLinkId());
                slHisPO.setSubLinkTime(timeNow);
                memberSubLinkHisMapper.insertSelective(slHisPO);
            }
        }
    }

    private Integer getRecolorHour(Integer appType, Integer officialId, String appId) {
        FastMiniVO miniPO = fastMiniService.queryByAppIdRedis(appId);
        Integer contentType = 0;
        // step1：优先查询小程序级别的染色时间设置
        String keyMini = StaticVar.MINI_RECOLOR_HOUR + appId;
        String resMini = RedisUtil.get(keyMini);
        if (StrUtil.isEmpty(resMini)) {
            FastRecolorSetPO rsParam = new FastRecolorSetPO();
            rsParam.setAppId(appId);
            rsParam.setType(3);
            rsParam.setActive(1);
            FastRecolorSetPO rsPO = fastRecolorSetMapper.queryOne(rsParam);
            if (rsPO != null) {
                resMini = rsPO.getRecolorHours().toString();
            } else {
                resMini = StaticVar.EMPTY_FLAG;
            }
            RedisUtil.set(keyMini, resMini, 60 * 60);
        }
        if (!StaticVar.EMPTY_FLAG.equals(resMini)) {
            actionLogService.log("color_hour", "小程序级别返回染色时间appId=" + appId + ",hour=" + resMini);
            return Integer.valueOf(resMini);
        }
        // 查询链路染色时间
        Integer roadId = 0;
        if (miniPO != null && miniPO.getRoadId() != null && miniPO.getRoadId() > 0) {
            // 抖音小程序
            roadId = miniPO.getRoadId();
            contentType = miniPO.getContentType();
        }
        if (roadId > 0) {
            String keyRoad = StaticVar.ROADID_RECOLOR_HOUR + roadId + "_" + appType;
            String resRoad = RedisUtil.get(keyRoad);
            if (StrUtil.isEmpty(resRoad)) {
                // 链路级别的染色时间
                FastRecolorSetPO rsParam = new FastRecolorSetPO();
                rsParam.setAppType(appType);
                rsParam.setRoadId(roadId);
                rsParam.setType(2);
                rsParam.setActive(1);
                rsParam.setContentType(contentType);
                FastRecolorSetPO rsPO = fastRecolorSetMapper.queryOne(rsParam);
                if (rsPO != null) {
                    resRoad = rsPO.getRecolorHours().toString();
                } else {
                    resRoad = StaticVar.EMPTY_FLAG;
                }
                RedisUtil.set(keyRoad, resRoad, 60 * 60);
            }
            if (!StaticVar.EMPTY_FLAG.equals(resRoad)) {
                actionLogService.log("color_hour", "链路级别返回染色时间roadId=" + roadId + ",hour=" + resRoad);
                return Integer.valueOf(resRoad);
            }
        }
        // 全局
        String keyGlobal = StaticVar.GLOBAL_RECOLOR_HOUR + "_" + appType;
        String resGlobal = RedisUtil.get(keyGlobal);
        if (StrUtil.isEmpty(resGlobal)) {
            FastRecolorSetPO rsParam = new FastRecolorSetPO();
            rsParam.setAppType(appType);
            rsParam.setType(1);
            FastRecolorSetPO rsPO = fastRecolorSetMapper.queryOne(rsParam);
            if (rsPO != null) {
                resGlobal = rsPO.getRecolorHours().toString();
            } else {
                resGlobal = StaticVar.EMPTY_FLAG;
            }
            RedisUtil.set(keyGlobal, resGlobal, 60 * 60);
        }
        if (!StaticVar.EMPTY_FLAG.equals(resGlobal)) {
            actionLogService.log("color_hour", "全局级别返回染色时间hour=" + resGlobal);
            return Integer.valueOf(resGlobal);
        }
        // 兜底
        actionLogService.log("color_hour", "兜底级别返回染色时间hour=24");
        return 24;
    }
}
