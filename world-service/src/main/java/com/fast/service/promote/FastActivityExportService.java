/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.promote.FastActivityPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.promote.FastActivityVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 导出
 *
 * <AUTHOR>
 */
@Service
public class FastActivityExportService extends BaseService {

    @Autowired
    private FastActivityService dataService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 活动数据记录-导出
     *
     * @param params
     * @return
     */
    public ResultVO<?> exportActivityDataList(SessionVO sessionVO, FastActivityVO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_ACTIVITY_DATA_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        List<FastActivityVO> list = dataService.getActivityData(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastActivityVO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据

            row.add(cur.getActivityDate());
            CollUtil.addNoRepeat(rowHeadNames, "日期");

            row.add(cur.getClickMemberNum());
            CollUtil.addNoRepeat(rowHeadNames, "点击人数");

            row.add(cur.getRechargeMemberNum());
            CollUtil.addNoRepeat(rowHeadNames, "充值人数");

            row.add(cur.getRechargeNum());
            CollUtil.addNoRepeat(rowHeadNames, "充值次数");

            row.add(cur.getRechargeMoney());
            CollUtil.addNoRepeat(rowHeadNames, "充值金额");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "活动数据";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 活动数据记录-导出
     *
     * @param params
     * @return
     */
    public ResultVO<?> exportActivityDataAll(SessionVO sessionVO, FastActivityVO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_ACTIVITY_DATA_ALL + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        FastActivityPO query = new FastActivityPO();
        query.setId(params.getActivityId());
        FastActivityPO activity = dataService.queryInfoByRedis(query);
        FastActivityVO item = dataService.getActivityDataAll(params);
        if (item == null) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        List<Object> row = new ArrayList<>();// 导出的数据(一行)

        row.add(activity.getTitle());
        CollUtil.addNoRepeat(rowHeadNames, "标题");

        row.add(item.getClickMemberNum());
        CollUtil.addNoRepeat(rowHeadNames, "点击人数");

        row.add(item.getRechargeMemberNum());
        CollUtil.addNoRepeat(rowHeadNames, "充值人数");

        row.add(item.getRechargeNum());
        CollUtil.addNoRepeat(rowHeadNames, "充值次数");

        row.add(item.getRechargeMoney());
        CollUtil.addNoRepeat(rowHeadNames, "充值金额");

        dataList.add(row);

        String title = "活动数据";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }
}
