/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.promote.FastLinkImportAdvertiserMapper;
import com.fast.po.promote.FastLinkImportAdvertiserPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.poi.ExcelImportUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastLinkImportAdvertiserService extends BaseService {

    @Autowired
    private FastLinkImportAdvertiserMapper fastLinkImportAdvertiserMapper;

    /**
     * 通过id查询单个对象
     */
    public FastLinkImportAdvertiserPO queryById(FastLinkImportAdvertiserPO params) {
        return fastLinkImportAdvertiserMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLinkImportAdvertiserPO queryById(Integer id) {
        return fastLinkImportAdvertiserMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastLinkImportAdvertiserPO queryOne(FastLinkImportAdvertiserPO params) {
        return fastLinkImportAdvertiserMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastLinkImportAdvertiserPO> queryList(FastLinkImportAdvertiserPO params) {
        return fastLinkImportAdvertiserMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastLinkImportAdvertiserPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastLinkImportAdvertiserPO> list = fastLinkImportAdvertiserMapper.queryList(params);
        for (FastLinkImportAdvertiserPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastLinkImportAdvertiserPO params) {
        return fastLinkImportAdvertiserMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastLinkImportAdvertiserPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastLinkImportAdvertiserMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastLinkImportAdvertiserPO> list) {
        if (fastLinkImportAdvertiserMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastLinkImportAdvertiserPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastLinkImportAdvertiserMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public Long importAdvertiserAccount(MultipartFile file) {
        List<Long> advertiserIds = Lists.newArrayList();
        List<List<Object>> data = ExcelImportUtil.readExcel(file, 0, "账户主体");
        Long importBatchId = System.currentTimeMillis();
        List<FastLinkImportAdvertiserPO> advertiserList = Lists.newArrayList();
        for (int i = 1; i < data.size(); i++) {
            if (CollUtil.isEmpty(data.get(i))) {
                continue;
            }
            if (data.get(i).size() < 2) {
                continue;
            }
            String company = data.get(i).get(0).toString();
            String account = data.get(i).get(1).toString();
            FastLinkImportAdvertiserPO item = new FastLinkImportAdvertiserPO();
            if (StrUtil.isNotEmpty(company) && StrUtil.isNotEmpty(account)) {
                item.setCompany(company);
                item.setAccount(account);
                item.setBatchImportId(importBatchId);
                advertiserList.add(item);
            }
        }
        if (CollUtil.isNotEmpty(advertiserList)) {
            String jsonValue = JSON.toJSONString(advertiserList);
            RedisUtil.set(StaticVar.IMPORT_BATCH_AD_ID_KEY + "_" + importBatchId, jsonValue, 2 * RedisUtil.ONE_HOUR);
        }
        return importBatchId;
    }

    public List<FastLinkImportAdvertiserPO> queryByRedisKey(Long batchImportId) {
        List<FastLinkImportAdvertiserPO> advList = new ArrayList<>();
        String value = RedisUtil.get(StaticVar.IMPORT_BATCH_AD_ID_KEY + "_" + batchImportId);
        if (StrUtil.isEmpty(value)) {
            FastLinkImportAdvertiserPO queryAdvertiser = new FastLinkImportAdvertiserPO();
            queryAdvertiser.setBatchImportId(batchImportId);
            advList = fastLinkImportAdvertiserMapper.queryList(queryAdvertiser);
        } else {
            advList = JSON.parseObject(value, new TypeReference<List<FastLinkImportAdvertiserPO>>() {
            });
        }
        return advList;
    }
}
