/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.promote.FastAdGroundMapper;
import com.fast.mapper.promote.FastAdGroundPageMapper;
import com.fast.po.promote.FastAdGroundPO;
import com.fast.po.promote.FastAdGroundPagePO;
import com.fast.service.aliyun.AliCdnService;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.promote.FastAdGroundVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastAdGroundService extends BaseService {

    @Autowired
    private FastAdGroundMapper adGroundMapper;
    @Autowired
    private FastAdGroundPageMapper fastAdGroundPageMapper;
    @Autowired
    private AliCdnService aliCdnService;

    /**
     * 查询单个对象
     */
    public FastAdGroundVO queryInfoByRedis(FastAdGroundPO item) {
        if (item.getId() == null) {
            return null;
        }
        item.setDelFlag(StaticVar.NO);
        FastAdGroundVO vo = new FastAdGroundVO();
        String key = StaticVar.AD_GROUND_ID + item.getId();
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            vo = JsonUtil.toJavaObject(value, FastAdGroundVO.class);
        } else {
            item.setId(item.getId());
            FastAdGroundPO po = queryById(item);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_10D);
                return null;
            } else {
                BeanUtils.copyProperties(po, vo);
                vo.setEncryptionId(encode(vo.getId()));
                RedisUtil.set(key, JsonUtil.toString(vo), RedisUtil.TIME_10D);
            }
        }
        return vo;
    }

    /**
     * 通过id查询单个对象
     */
    public FastAdGroundVO queryInfoByRedis(Integer id) {
        FastAdGroundPO itemParam = new FastAdGroundPO();
        itemParam.setId(id);
        return queryInfoByRedis(itemParam);
    }

    /**
     * 通过id查询单个对象
     */
    public FastAdGroundPO queryById(FastAdGroundPO item) {
        FastAdGroundPO po = adGroundMapper.queryById(item);
        if (po != null) {
            FastAdGroundPagePO page = new FastAdGroundPagePO();
            page.setAdGroundId(item.getId());
            List<FastAdGroundPagePO> pages = fastAdGroundPageMapper.queryList(page);
            for (FastAdGroundPagePO cur : pages) {
                if (notBlank(cur.getPicUrl())) {
                    // 图片默认为0，0
                    cur.setPicUrl(aliCdnService.getImgFullUrl(cur.getPicUrl()));
                }
            }
            po.setPages(pages);
        }
        return po;
    }

    /**
     * 通过id查询单个对象
     */
    public FastAdGroundPO queryById(Integer id) {
        return adGroundMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastAdGroundPO queryOne(FastAdGroundPO item) {
        return adGroundMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastAdGroundPO> queryList(FastAdGroundPO item) {
        return adGroundMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastAdGroundPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastAdGroundPO> list = adGroundMapper.queryList(item);
        for (FastAdGroundPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (notBlank(cur.getPageFirst())) {
                // 图片默认为0，0
                cur.setPageFirst(aliCdnService.getImgFullUrl(cur.getPageFirst()));
            }
            if (cur.getLinkCount() > 0) {
                cur.setState(1);
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastAdGroundPO item) {
        return adGroundMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastAdGroundPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (adGroundMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        // 新增图片内容
        if (CollUtil.isEmpty(params.getPages())) {
            transactionRollBack();
            return MethodVO.error("图片内容不能为空");
        }
        List<FastAdGroundPagePO> pages = params.getPages();
        int i = 0;
        for (FastAdGroundPagePO page : pages) {
            page.setSequence(i++);
            page.setAdGroundId(params.getId());
            if (page.getHeight() > StaticVar.SMALL_INT) {
                transactionRollBack();
                return MethodVO.error("图片高度不能大于65535px");
            }
            page.setPicUrl(aliCdnService.replaceHost(page.getPicUrl()));
            fastAdGroundPageMapper.insertSelective(page);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastAdGroundPO> list) {
        if (adGroundMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastAdGroundPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (adGroundMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        FastAdGroundPagePO delete = new FastAdGroundPagePO();
        delete.setAdGroundId(params.getId());
        fastAdGroundPageMapper.deleteByAdGroundId(delete);
        // 新增图片内容
        if (CollUtil.isEmpty(params.getPages())) {
            transactionRollBack();
            return MethodVO.error("图片内容不能为空");
        }

        List<FastAdGroundPagePO> pages = params.getPages();
        int i = 0;
        for (FastAdGroundPagePO page : pages) {
            page.setSequence(i++);
            page.setAdGroundId(params.getId());
            if (page.getHeight() > StaticVar.SMALL_INT) {
                transactionRollBack();
                return MethodVO.error("图片高度不能大于65535px");
            }
            page.setPicUrl(aliCdnService.replaceHost(page.getPicUrl()));
            fastAdGroundPageMapper.insertSelective(page);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastAdGroundPO params) {
        Date nowTime = DateUtil.getNowDate();
        FastAdGroundPO delete = new FastAdGroundPO();
        delete.setUpdateTime(nowTime);
        delete.setId(params.getId());
        delete.setDelFlag(1);
        if (adGroundMapper.updateById(delete) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
