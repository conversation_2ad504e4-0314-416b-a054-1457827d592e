/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.mapper.promote.FastMiniLinkMapper;
import com.fast.mapper.promote.FastShortLinkMapper;
import com.fast.po.promote.FastMiniLinkPO;
import com.fast.po.promote.FastShortLinkPO;
import com.fast.service.base.BaseService;
import com.fast.service.mini.FastMiniService;
import com.fast.utils.DateUtil;
import com.fast.utils.LinkUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniLinkService extends BaseService {

    @Autowired
    private FastMiniLinkMapper fastMiniLinkMapper;
    @Autowired
    private FastShortLinkMapper shortLinkMapper;
    @Autowired
    private FastMiniService miniService;

    /**
     * 通过id查询单个对象
     */
    public FastMiniLinkPO queryById(FastMiniLinkPO item) {
        return fastMiniLinkMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniLinkPO queryById(Integer id) {
        FastMiniLinkPO itemParam = new FastMiniLinkPO();
        itemParam.setId(id);
        return fastMiniLinkMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniLinkPO queryOne(FastMiniLinkPO item) {
        return fastMiniLinkMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniLinkPO> queryList(FastMiniLinkPO item) {
        return fastMiniLinkMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniLinkPO item, PageVO pageVO, SessionVO sessionVO) {
        pageVO.setLimit(100);
        startPage(pageVO);
        List<FastMiniLinkPO> list = fastMiniLinkMapper.queryList(item);
        String domain = "";
        for (FastMiniLinkPO cur : list) {
            if (cur.getType() == 1) {
                // 微信小程序，抖音小程序
                cur.setEncryptionId(encode(cur.getId()));
                cur.setMiniLinkUrl(cur.getMiniLinkUrl() + "?officialId=" + sessionVO.getOfficialId() + "&fromOfficial=1");
            } else if (cur.getType() == 3) {
                // h5
                String shortLink = "minilink_" + cur.getId();
                FastShortLinkPO shortParam = new FastShortLinkPO();
                shortParam.setShortLink(shortLink);
                FastShortLinkPO shortLinkPO = shortLinkMapper.queryOne(shortParam);
                if (shortLinkPO == null) {
                    // 新增
                    shortLinkPO = new FastShortLinkPO();
                    shortLinkPO.setShortLink(shortLink);
                    shortLinkPO.setLongLink(cur.getMiniLinkUrl() + "?officialId=" + sessionVO.getOfficialId() + "&fromOfficial=1");
                    shortLinkPO.setShorts(LinkUtil.getShortURL());
                    shortLinkPO.setType(2);
                    shortLinkPO.setFlag(0);
                    shortLinkPO.setCreatorId(sessionVO.getUserId());
                    shortLinkPO.setCreateTime(DateUtil.getNowDate());
                    if (shortLinkMapper.insertSelective(shortLinkPO) < 1) {
                        return ResultVO.error(StaticStr.FAILED_TO_GENERATE_SHORT_LINK);
                    }
                }
                // 开始拼装
                cur.setMiniLinkUrl(domain + cur.getMiniLinkUrl() + "?st=" + shortLinkPO.getShorts());
            }
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniLinkPO item) {
        return fastMiniLinkMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniLinkPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMiniLinkMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniLinkPO> list) {
        if (fastMiniLinkMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniLinkPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastMiniLinkMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
