/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.*;
import com.fast.enums.MiniTypeEnum;
import com.fast.enums.OperationLogEnum;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.mapper.fee.FastFeeGearRestrictMapper;
import com.fast.mapper.fee.FastFeeGearRestrictRetailMapper;
import com.fast.mapper.fee.FastFeeModelBatchMapper;
import com.fast.mapper.fee.FastFeeRuleMapper;
import com.fast.mapper.promote.FastLinkExtMapper;
import com.fast.mapper.promote.FastLinkMapper;
import com.fast.mapper.promote.FastShortLinkMapper;
import com.fast.mapper.statis.FastStatisLinkPvMapper;
import com.fast.mapper.user.FastUserMapper;
import com.fast.po.common.FastLogOperationPO;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.drama.FastDramaSeriesPO;
import com.fast.po.fee.*;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.promote.*;
import com.fast.po.user.FastUserPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.common.FastOperationLogService;
import com.fast.service.drama.FastDramaSeriesService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.fee.FastFeeGearRestrictService;
import com.fast.service.fee.FastFeeModelService;
import com.fast.service.fee.FastFeeRuleService;
import com.fast.service.mini.FastMiniService;
import com.fast.utils.*;
import com.fast.utils.redis.RedisLauUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.drama.FastDramaVO;
import com.fast.vo.fee.FastFeeRuleVO;
import com.fast.vo.mini.FastMiniVO;
import com.fast.vo.promote.FastLinkQueryVO;
import com.fast.vo.promote.FastLinkVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.fast.utils.DoubleUtil.DEF_SCALE_2;

/**
 * <AUTHOR>
 */
@Service
public class FastLinkService extends BaseService {

    @Autowired
    private FastLinkMapper fastLinkMapper;
    @Autowired
    private FastLinkExtMapper linkExtMapper;
    @Autowired
    private FastShortLinkMapper fastShortLinkMapper;
    @Autowired
    private FastShortLinkMapper shortLinkMapper;
    @Autowired
    private FastFeeRuleMapper fastFeeRuleMapper;
    @Autowired
    private FastFeeModelBatchMapper feeModelBatchMapper;
    @Autowired
    private FastShortLinkService fastShortLinkService;
    @Autowired
    private FastDramaService fastDramaService;
    @Autowired
    private FastDramaSeriesService fastDramaSeriesService;
    @Autowired
    private FastMiniService fastMiniService;
    @Lazy
    @Autowired
    private FastStatisLinkService fastStatisLinkService;
    @Lazy
    @Resource
    private FastFeeRuleService feeRuleService;
    @Autowired
    private FastDramaMapper dramaMapper;
    @Autowired
    private FastFeeModelService fastFeeModelService;
    @Autowired
    private FastFeeGearRestrictService fastFeeGearRestrictService;
    @Autowired
    private FastOperationLogService operationLogService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastFeeGearRestrictRetailMapper fastFeeGearRestrictRetailMapper;
    @Autowired
    private FastFeeGearRestrictMapper fastFeeGearRestrictMapper;
    @Autowired
    private FastStatisLinkPvMapper fastStatisLinkPvMapper;
    @Autowired
    private FastLinkImportAdvertiserService fastLinkImportAdvertiserService;
    @Autowired
    private FastUserMapper fastUserMapper;

    /**
     * 通过id查询单个对象
     */
    public FastLinkVO queryInfoByRedis(FastLinkPO item) {
        if (item.getId() == null) {
            return null;
        }
        FastLinkVO vo = new FastLinkVO();
        String key = StaticVar.LINK_INFO_ID + item.getId();
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            vo = JsonUtil.toJavaObject(value, FastLinkVO.class);
        } else {
            FastLinkPO po = fastLinkMapper.queryById(item);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.ONE_MINUTE);
                return null;
            } else {
                BeanUtils.copyProperties(po, vo);
                vo.setEncryptionId(encode(vo.getId()));
                RedisUtil.set(key, JsonUtil.toString(vo), RedisUtil.TIME_1D);
            }
        }
        return vo;
    }

    /**
     * 通过id查询单个对象
     */
    public FastLinkVO queryInfoByRedis(Integer id) {
        FastLinkPO itemParam = new FastLinkPO();
        itemParam.setId(id);
        return queryInfoByRedis(itemParam);
    }

    /**
     * 通过ids查询多个对象
     */
    public List<FastLinkVO> queryInfoByRedis(String ids) {
        List<FastLinkVO> result = new ArrayList<>();
        if (notEmpty(ids)) {
            FastLinkPO itemParam = new FastLinkPO();
            List<Integer> idList = CollUtil.parseIntStr2List(ids);
            for (Integer id : idList) {
                itemParam.setId(id);
                FastLinkVO vo = queryInfoByRedis(itemParam);
                if (vo != null) {
                    result.add(vo);
                }
            }
        }
        return result;
    }

    public Integer queryLinkSubTypeByIdRedis(Integer linkId) {
        String key = "link_sub_type_id:" + linkId;
        String value = RedisLauUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            return toInteger(value);
        } else {
            Integer po = fastLinkMapper.queryLinkSubTypeById(linkId);
            if (po == null) {
                RedisLauUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.ONE_MINUTE);
                return null;
            } else {
                RedisLauUtil.set(key, po.toString(), RedisUtil.TIME_1D);
                return po;
            }
        }
    }

    /**
     * 通过id查询单个对象
     */
    public FastLinkPO queryById(FastLinkPO item) {
        return fastLinkMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLinkPO queryById(Integer id) {
        FastLinkPO itemParam = new FastLinkPO();
        itemParam.setId(id);
        return fastLinkMapper.queryById(itemParam);
    }

    public FastLinkPO queryMoreById(Integer id) {
        return fastLinkMapper.queryMoreById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastLinkPO queryOne(FastLinkPO item) {
        return fastLinkMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastLinkPO> queryList(FastLinkPO item) {
        return fastLinkMapper.queryList(item);
    }

    /**
     * 查询全部
     */
    public List<FastLinkPO> getSimpleList(FastLinkPO item) {
        return fastLinkMapper.getSimpleList(item);
    }

    /**
     * 查询链接id
     */
    public Set<Integer> queryLinkIds(FastLinkPO item) {
        return fastLinkMapper.queryLinkIds(item);
    }

    /**
     * 根据短剧的起始付费集查询链接id
     */
    public Set<Integer> queryLinkIdsByStartNum(Integer dramaId, Integer startNum) {
        Set<Integer> ids = fastLinkMapper.queryLinkIdsByStartNum(dramaId, startNum);
        if (CollUtil.isEmpty(ids)) {
            return fastLinkMapper.queryLinkIdsByStartNumDef(dramaId, startNum);
        }
        return ids;
    }

    /**
     * 查询链接id
     */
    public Set<Integer> queryLinkIdsByAdvMediaId(Integer advMediaId) {
        return fastLinkMapper.queryLinkIdsByAdvMediaId(advMediaId);
    }

    /**
     * 查询优化师id
     */
    public List<Integer> queryAdvUserIds(FastLinkPO item) {
        return fastLinkMapper.queryAdvUserIds(item);
    }

    /**
     * 查询链接名称Map
     */
    public Map<Integer, String> queryLinkNameMap(FastLinkPO item) {
        List<FastLinkPO> list = fastLinkMapper.queryLinkNames(item);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, String> map = new HashMap<>();
        list.forEach(cur -> map.put(cur.getId(), cur.getLinkName()));
        return map;
    }

    /**
     * 查询链接Map
     */
    public Map<Integer, FastLinkPO> queryLinkMap(FastLinkPO item) {
        List<FastLinkPO> list = fastLinkMapper.queryLinkNames(item);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, FastLinkPO> map = new HashMap<>();
        list.forEach(cur -> map.put(cur.getId(), cur));
        return map;
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> exportLinkList(SessionVO sessionVO, FastLinkQueryVO params) {
        String key = StaticVar.EXPORT_LINK_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);

        // 查询数据
        String name = (params.getContentType() == 1 || params.getContentType() == 4) ? "集" : params.getContentType() == 2 ? "话" : "说";
        // 处理查询参数
        if (!StrUtil.isEmpty(params.getQueryCreateTime())) {
            String[] queryTime = params.getQueryCreateTime().split(" - ");
            params.setBeginTime(queryTime[0] + " 00:00:00");
            params.setEndTime(queryTime[1] + " 23:59:59");
        }
        if (!StrUtil.isEmpty(params.getQueryName())) {
            params.setLinkName(params.getQueryName());
        }
        if (StrUtil.isInteger(params.getQueryName())) {
            params.setId(Integer.parseInt(params.getQueryName()));
        }

        List<FastLinkQueryVO> list = fastLinkMapper.queryListV1(params);
        for (FastLinkQueryVO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            // 入口页面
            FastDramaPO oneFastDramaPO = new FastDramaPO();
            oneFastDramaPO.setId(cur.getDramaId());
            FastDramaPO fastDramaPO = fastDramaService.queryOne(oneFastDramaPO);
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(cur.getSeriesId());
            if (fastDramaPO != null && fastDramaSeriesPO != null) {
                cur.setInPage(fastDramaPO.getDramaName() + "-第" + fastDramaSeriesPO.getSeriesNum() + name);
                // 设置剧名、集名、第几集
                cur.setDramaName(fastDramaPO.getDramaName());
                cur.setSeriesName("第" + fastDramaSeriesPO.getSeriesNum() + name);
                cur.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
            }
        }

        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastLinkQueryVO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            // 链接 ID+入口页面+短链+小程序路径吧
            row.add(cur.getId());
            CollUtil.addNoRepeat(rowHeadNames, "链接ID");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "渠道名称");

            row.add(convertAppType(cur.getAppType()));
            CollUtil.addNoRepeat(rowHeadNames, "应用名称");

            row.add(cur.getInPage());
            CollUtil.addNoRepeat(rowHeadNames, "入口页面");

            String promoteLink = cur.getPromoteLink();
            if (cur.getAdvMediaId() != null && cur.getAdvMediaId() == 1 && StrUtil.isNotEmpty(cur.getPromoteLink())) {
                promoteLink = addMoreParams(promoteLink);
            }
            row.add(promoteLink);
            CollUtil.addNoRepeat(rowHeadNames, "推广链接地址(启动参数)");

            String miniStartPath = "";
            if (cur.getAppType() == 2) {
                miniStartPath = StaticYml.PROMOTE_TIKTOK_MINI_PAGE_PATH;
            }
            row.add(miniStartPath);
            CollUtil.addNoRepeat(rowHeadNames, "启动页面");

            row.add(cur.getMiniAppId());
            CollUtil.addNoRepeat(rowHeadNames, "APPID");

            row.add(cur.getShortLink() + "​");
            CollUtil.addNoRepeat(rowHeadNames, "短链");

            row.add(cur.getPromoteLink());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接地址");

            row.add(getMonitorUrl(cur));
            CollUtil.addNoRepeat(rowHeadNames, "监测链接");

            // if (Objects.nonNull(cur.getKeepType())) {
            //     if (cur.getKeepType() == 1) {
            //         row.add("平台全局规则");
            //     } else {
            //         row.add("自定义规则：" + cur.getKeepBatchName());
            //     }
            // } else {
            //     row.add("-");
            // }
            // CollUtil.addNoRepeat(rowHeadNames, "支付挽留");
            //
            // if (cur.getAdvUnlockFlag() == 1) {
            //     row.add(String.format("开启（单集解锁%s集）", cur.getAdUnlockNum()));
            // } else {
            //     row.add("关闭");
            // }
            // CollUtil.addNoRepeat(rowHeadNames, "是否开启广告");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "推广运营-推广链接列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastLinkQueryVO params, PageVO pageVO) {
        String name = params.getContentType() == 1 || params.getContentType() == 4 ? "集" : params.getContentType() == 2 ? "话" : "说";
        startPage(pageVO);
        // 处理查询参数
        if (!StrUtil.isEmpty(params.getQueryCreateTime())) {
            String[] queryTime = params.getQueryCreateTime().split(" - ");
            params.setBeginTime(queryTime[0] + " 00:00:00");
            params.setEndTime(queryTime[1] + " 23:59:59");
        }
        if (!StrUtil.isEmpty(params.getQueryName())) {
            params.setLinkName(params.getQueryName());
        }
        if (StrUtil.isInteger(params.getQueryName())) {
            params.setId(Integer.parseInt(params.getQueryName()));
        }

        List<FastLinkQueryVO> list = fastLinkMapper.queryListV1(params);

        String defaultGroundNet = fastMiniService.getGroundNet(params.getContentType());

        for (FastLinkQueryVO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));

            // 入口页面
            FastDramaPO oneFastDramaPO = new FastDramaPO();
            oneFastDramaPO.setId(cur.getDramaId());
            FastDramaPO fastDramaPO = fastDramaService.queryOne(oneFastDramaPO);
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(cur.getSeriesId());
            if (fastDramaPO != null && fastDramaSeriesPO != null) {
                cur.setInPage(fastDramaPO.getDramaName() + "-第" + fastDramaSeriesPO.getSeriesNum() + name);
                // 设置剧名、集名、第几集
                cur.setDramaName(fastDramaPO.getDramaName());
                cur.setSeriesName("第" + fastDramaSeriesPO.getSeriesNum() + name);
                cur.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
            }
            // 相关设置
            StringBuilder sb = new StringBuilder();
            sb.append("回传规则：");
            if (cur.getBackType() == 1) {
                sb.append("全局规则");
            } else {
                sb.append("自定义规则");
            }
            sb.append("\n");
            sb.append("充值模板：");
            if (cur.getPayType() == 1) {
                sb.append("全局模板");
            } else if (cur.getPayType() == 2) {
                sb.append("自定义模板");
            } else if (cur.getPayType() == 3) {
                sb.append("通用模板");
            }
            cur.setSetInfo(sb.toString());
            // 获取设置链接统计数据信息
            FastStatisLinkPO fastStatisLinkPO = new FastStatisLinkPO();
            fastStatisLinkPO.setLinkId(cur.getId());
            fastStatisLinkPO.setStatisDate(DateUtil.getDateStr("yyyy-MM-dd", DateUtil.getNowDate()));
            FastStatisLinkPO queryFastStatisLinkPO = fastStatisLinkService.queryOne(fastStatisLinkPO);
            if (queryFastStatisLinkPO != null) {
                // 当日新增用户
                cur.setNumDay(queryFastStatisLinkPO.getNumDay());
                // 当日新增用户-充值人数、充值笔数、充值金额
                cur.setNumDayRech(queryFastStatisLinkPO.getNumDayRech());
                cur.setNumDayOrder(queryFastStatisLinkPO.getNumDayOrder());
                cur.setMoneyDay(queryFastStatisLinkPO.getMoneyDay());
                // 新增用户当日-充值人数、充值笔数、充值金额
                cur.setNumAllDayRech(queryFastStatisLinkPO.getNumAllDayRech());
                cur.setNumAllDayOrder(queryFastStatisLinkPO.getNumAllDayOrder());
                cur.setMoneyAllDay(queryFastStatisLinkPO.getMoneyAllDay());
                // 新增用户累计-充值人数、充值笔数、充值金额
                cur.setNumAllRech(queryFastStatisLinkPO.getNumAllRech());
                cur.setNumAllOrder(queryFastStatisLinkPO.getNumAllOrder());
                cur.setMoneyAll(queryFastStatisLinkPO.getMoneyAll());
                // 累计新增用户数、总成本(累计成本)
                cur.setNumAll(queryFastStatisLinkPO.getNumAll());
                cur.setCostAll(queryFastStatisLinkPO.getCostAll());
                // 计算ARPPU
                cur.setMoneyAllArppu(DoubleUtil.divB4Zero(cur.getMoneyAll(), new BigDecimal(cur.getNumAllRech())));
                cur.setMoneyDayArppu(DoubleUtil.divB4Zero(cur.getMoneyAllDay(), new BigDecimal(cur.getNumAllDayRech())));
                // 总利润、总利润率
                if (queryFastStatisLinkPO.getMoneyAll() != null && queryFastStatisLinkPO.getCostAll() != null) {
                    BigDecimal profitAll = (queryFastStatisLinkPO.getMoneyAll().subtract(queryFastStatisLinkPO.getCostAll())).setScale(DEF_SCALE_2, RoundingMode.HALF_UP);
                    cur.setProfitAll(profitAll);
                    if (queryFastStatisLinkPO.getCostAll() != null && !(queryFastStatisLinkPO.getCostAll().compareTo(BigDecimal.ZERO) == 0)) {
                        cur.setProfitAllRate(((profitAll.divide(queryFastStatisLinkPO.getCostAll(), 4, RoundingMode.HALF_DOWN)).multiply(BigDecimalVar.BD_100)).setScale(DEF_SCALE_2, RoundingMode.HALF_UP) + "%");
                    }
                }
            }
            Long pvCount = fastStatisLinkPvMapper.queryLinkPVCount(cur.getId());
            cur.setPv(pvCount);
            cur.setUv(cur.getNumAll());// 累计用户
            if (cur.getAppType() == 2) {
                // 抖音小程序
                cur.setMiniStartPath(StaticYml.PROMOTE_TIKTOK_MINI_PAGE_PATH);
            }
            if (cur.getAppType() == 3) {
                // h5站，填充落地页域名
                String groundNet = defaultGroundNet;
                if (StrUtil.isNotEmpty(cur.getAdGroundDomain())) {
                    groundNet = cur.getAdGroundDomain();
                    if (groundNet.lastIndexOf("/") == groundNet.length() - 1) {
                        groundNet = groundNet.substring(0, groundNet.length() - 1);
                    }
                }
                cur.setShortLink(groundNet + cur.getShortLink());
            }
            if (cur.getLinkType() == 3) {
                // 添加审核id
                cur.setPromoteLink(addMoreParams(cur.getPromoteLink()));
            }
            // 设置全局
            FastFeeRuleVO feeRule = null;
            setGlobalNum(cur, feeRule);
        }

        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 设置全局的剧集设置
     *
     * @param cur
     * @param feeRule
     */
    public void setGlobalNum(FastLinkQueryVO cur, FastFeeRuleVO feeRule) {
        // 设置剧集
        if (cur.getStartNumGlobal() != null && cur.getStartNumGlobal() == 1) {
            if (feeRule == null) {
                // 公众号全局
                feeRule = feeRuleService.queryInfoByRedis(cur.getOfficialId(), cur.getDramaId());
            }
            if (feeRule == null) {
                // 平台全局
                feeRule = feeRuleService.queryInfoByRedis(0, cur.getDramaId());
            }
            if (feeRule != null && feeRule.getStartNum() != null) {
                cur.setStartNum(feeRule.getStartNum());
            }
        }
        if (cur.getFollowNumGlobal() != null && cur.getFollowNumGlobal() == 1) {
            if (feeRule == null) {
                // 公众号全局
                feeRule = feeRuleService.queryInfoByRedis(cur.getOfficialId(), cur.getDramaId());
            }
            if (feeRule == null) {
                // 平台全局
                feeRule = feeRuleService.queryInfoByRedis(0, cur.getDramaId());
            }
            if (feeRule != null && feeRule.getFollowNum() != null) {
                cur.setFollowNum(feeRule.getFollowNum());
            }
        }
        if (cur.getCoinPerGlobal() != null && cur.getCoinPerGlobal() == 1) {
            if (feeRule == null) {
                // 公众号全局
                feeRule = feeRuleService.queryInfoByRedis(cur.getOfficialId(), cur.getDramaId());
            }
            if (feeRule == null) {
                // 平台全局
                feeRule = feeRuleService.queryInfoByRedis(0, cur.getDramaId());
            }
            if (feeRule != null && feeRule.getCoinPer() != null) {
                cur.setCoinPer(feeRule.getCoinPer());
            }
        }
    }

    /**
     * 查询挂载分析
     */
    public ResultVO<?> querySubListAdmin(FastLinkQueryVO item) {
        if (StrUtil.isNotEmpty(item.getQueryStatisTime())) {
            List<Date> dateList = DateUtil.getStartEndDate(item.getQueryStatisTime());
            item.setBeginTime(DateUtil.format07(DateUtil.beginOfDay(dateList.get(0))));
            item.setEndTime(DateUtil.format07(DateUtil.endOfDay(dateList.get(1))));
        }
        List<FastLinkQueryVO> list = fastLinkMapper.querySubListAdmin(item);
        Set<Integer> typeSet = list.stream().map(FastLinkQueryVO::getLinkSubType).collect(Collectors.toSet());
        for (int i = 1; i <= 4; i++) {
            if (!typeSet.contains(i)) {
                FastLinkQueryVO qv = new FastLinkQueryVO();
                qv.setLinkSubType(i);
                qv.setMoneyRecharge(BigDecimal.ZERO);
                qv.setMoneyProfit(BigDecimal.ZERO);
                list.add(qv);
            }
        }

        Map<String, Object> results = ResultVO.getMap();
        results.put("list", list);
        return ResultVO.success(results);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageListV2(FastLinkQueryVO item, PageVO pageVO) {
        startPage(pageVO);
        // 处理查询参数
        if (!StrUtil.isEmpty(item.getQueryCreateTime())) {
            List<Date> dateList = DateUtil.getStartEndDate(item.getQueryCreateTime());
            item.setBeginTime(DateUtil.format07(DateUtil.beginOfDay(dateList.get(0))));
            item.setEndTime(DateUtil.format07(DateUtil.endOfDay(dateList.get(1))));
        }
        if (!StrUtil.isEmpty(item.getQueryName())) {
            item.setLinkName(item.getQueryName());
        }
        if (StrUtil.isInteger(item.getQueryName())) {
            item.setId(Integer.parseInt(item.getQueryName()));
        }
        item.setStatisDate(DateUtil.getDateStr("yyyy-MM-dd", DateUtil.getNowDate()));

        List<FastLinkQueryVO> list = fastLinkMapper.queryListV2(item);
        Integer numDayAll = 0;// 新增用户数累计
        Integer numDayRechAll = 0;// 新增充值用户累计
        BigDecimal moneyDayAll = BigDecimal.ZERO;// 新增充值累计
        BigDecimal moneyAllAll = BigDecimal.ZERO;// 累计充值累计
        for (FastLinkQueryVO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            // 入口页面
            // FastDramaVO fastDramaVO = fastDramaService.queryInfoByRedis(cur.getDramaId());
            FastDramaPO oneFastDramaPO = new FastDramaPO();
            oneFastDramaPO.setId(cur.getDramaId());
            FastDramaPO fastDramaPO = fastDramaService.queryOne(oneFastDramaPO);
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(cur.getSeriesId());
            if (fastDramaPO != null && fastDramaSeriesPO != null) {
                cur.setInPage(fastDramaPO.getDramaName() + "-第" + fastDramaSeriesPO.getSeriesNum() + "集");
                // 设置剧名、集名、第几集
                cur.setDramaName(fastDramaPO.getDramaName());
                cur.setSeriesName("第" + fastDramaSeriesPO.getSeriesNum() + "集");
                cur.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
            }
            // 相关设置
            cur.setSetInfo("回传规则：" + (cur.getBackType() == 1 ? "全局规则" : "自定义规则") + "\n" + "充值模板：" + (cur.getPayType() == 1 ? "全局模板" : "自定义模板"));

            // 获取设置链接统计数据信息
            FastStatisLinkPO fastStatisLinkPO = new FastStatisLinkPO();
            fastStatisLinkPO.setLinkId(cur.getId());
            fastStatisLinkPO.setStatisDate(DateUtil.getDateStr("yyyy-MM-dd", DateUtil.getNowDate()));
            FastStatisLinkPO statis = fastStatisLinkService.queryOne(fastStatisLinkPO);
            if (statis != null) {
                statis.setMoneyAll(statis.getMoneyProfitAll());
                statis.setMoneyDay(statis.getMoneyProfitDay());
                statis.setMoneyAllDay(statis.getMoneyProfitAllDay());
                // 当日新增用户
                cur.setNumDay(statis.getNumDay());
                // 当日新增用户-充值人数、充值笔数、充值金额
                cur.setNumDayRech(statis.getNumDayRech());
                cur.setNumDayOrder(statis.getNumDayOrder());
                cur.setMoneyDay(statis.getMoneyDay());
                // 新增用户当日-充值人数、充值笔数、充值金额
                cur.setNumAllDayRech(statis.getNumAllDayRech());
                cur.setNumAllDayOrder(statis.getNumAllDayOrder());
                cur.setMoneyAllDay(statis.getMoneyAllDay());
                // 新增用户累计-充值人数、充值笔数、充值金额
                cur.setNumAllRech(statis.getNumAllRech());
                cur.setNumAllOrder(statis.getNumAllOrder());
                cur.setMoneyAll(statis.getMoneyAll());
                // 累计新增用户数、总成本(累计成本)
                cur.setNumAll(statis.getNumAll());
                cur.setCostAll(statis.getCostAll());
                // 计算ARPPU
                cur.setMoneyAllArppu(DoubleUtil.divB4Zero(cur.getMoneyAll(), new BigDecimal(cur.getNumAllRech())));
                cur.setMoneyDayArppu(DoubleUtil.divB4Zero(cur.getMoneyAllDay(), new BigDecimal(cur.getNumAllDayRech())));
                // 总利润、总利润率
                if (statis.getMoneyAll() != null && statis.getCostAll() != null) {
                    BigDecimal profitAll = (statis.getMoneyAll().subtract(statis.getCostAll())).setScale(DEF_SCALE_2, RoundingMode.HALF_UP);
                    cur.setProfitAll(profitAll);
                    if (statis.getCostAll() != null && !(statis.getCostAll().compareTo(BigDecimal.ZERO) == 0)) {
                        cur.setProfitAllRate(((profitAll.divide(statis.getCostAll(), 4, RoundingMode.HALF_DOWN)).multiply(BigDecimalVar.BD_100)).setScale(DEF_SCALE_2, RoundingMode.HALF_UP) + "%");
                    }
                }
                numDayAll += statis.getNumDay();
                numDayRechAll += statis.getNumDayRech();
                moneyDayAll = DoubleUtil.addB(moneyDayAll, statis.getMoneyDay());
                moneyAllAll = DoubleUtil.addB(moneyAllAll, statis.getMoneyAll());
            }
            // 获得 pv、uv
//            JSONObject condition = new JSONObject();
//            condition.put("linkId", cur.getId() + "");// 查询列要在tablestore的多元索引中
//            IndexSearchReply<FastBackToutiaoPO> reply = TableStoreUtil.andQueryCount(tableStoreProperties.getFasBackToutiaoTable(), tableStoreProperties.getFasBackToutiaoTableIndex(), condition, FastBackToutiaoPO.class);
//            cur.setPv(reply.totalCount());
            Long pvCount = fastStatisLinkPvMapper.queryLinkPVCount(cur.getId());
            cur.setPv(pvCount);
            cur.setUv(cur.getNumAll());
            // 设置全局
            // FastFeeRuleVO feeRule = null;
            setGlobalNum(cur, null);
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        results.put("numDayAll", numDayAll);
        results.put("numDayRechAll", numDayRechAll);
        results.put("moneyDayAll", moneyDayAll);
        results.put("moneyAllAll", moneyAllAll);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastLinkPO item) {
        return fastLinkMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO insert(FastLinkPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (fastLinkMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return ResultVO.error(StaticStr.ADD_FAILED);
        }
        if (item.getFeeFlag() == 2) {
            FastLinkExtPO linkExt = new FastLinkExtPO();
            linkExt.setId(item.getId());
            linkExt.setContentType(item.getContentType());
            linkExt.setAnUnlockNum(item.getAnUnlockNum());
            linkExt.setIosUnlockNum(item.getIosUnlockNum());
            if (linkExtMapper.updateById(linkExt) == 0 && linkExtMapper.insertSelective(linkExt) == 0) {
                transactionRollBack();
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
        }
        try {
            ResultVO resVO = _checkRechargeModel(item, item);
            if (resVO.getCode() != 0) {
                transactionRollBack();
                return resVO;
            }
            actionLogService.log("check_vip", resVO.getMessage());
        } catch (Exception e) {
            log.error(JSONObject.toJSONString(e.getStackTrace()));
        }
        // 设置起始费用设置
        Integer coinPer = item.getCoinPer() == null ? 0 : item.getCoinPer();
        Integer startNum = item.getStartNum() == null ? 0 : item.getStartNum();
        Integer followNum = item.getFollowNum() == null ? 0 : item.getFollowNum();
        FastFeeRulePO rulePO = new FastFeeRulePO();
        rulePO.setCoinPer(coinPer);
        rulePO.setCreateTime(nowTime);
        rulePO.setCreatorId(item.getCreatorId());
        rulePO.setDramaId(item.getDramaId());
        rulePO.setFollowNum(followNum);
        rulePO.setStartNum(startNum);
        rulePO.setRetailId(item.getRetailId());
        rulePO.setOfficialId(0);
        rulePO.setLinkId(item.getId());
        if (fastFeeRuleMapper.insertSelective(rulePO) == 0) {
            transactionRollBack();
            return ResultVO.error(StaticStr.ADD_FAILED);
        }
        String keyLinkRule = StaticVar.FEE_LINKID_RULE_ID + item.getId() + StaticVar.UNDERLINE + item.getDramaId();
        RedisUtil.del(keyLinkRule);
        return ResultVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> insertBatch(HttpServletRequest request, FastLinkPO param, FastDramaPO dramaPO, SessionVO sessionVO) {
        List<FastLinkPO> linkList = new ArrayList<>();
        Date nowTime = DateUtil.getNowDate();
        param.setCreateTime(nowTime);
        param.setUpdateTime(nowTime);
        List<FastLinkImportAdvertiserPO> advList = new ArrayList<>();
        if (param.getGenerateLinkNameType() == 2 && (param.getBatchImportId() != null && param.getBatchImportId() > 0)) {// 系统生成
            advList = fastLinkImportAdvertiserService.queryByRedisKey(param.getBatchImportId());
            if (CollUtil.isNotEmpty(advList)) {
                param.setBatchCount(advList.size());
            }
        }
        // 批量新增个数
        Integer batchCount = param.getBatchCount();
        batchCount = batchCount == null ? 1 : batchCount;
        batchCount = batchCount > 100 ? 100 : batchCount;
        String linkTitle = param.getLinkTitle();
        for (int i = 1; i <= batchCount; i++) {
            StringBuilder linkName = new StringBuilder();
            if (param.getGenerateLinkNameType() == 2) {
                // 剧名-应用类型-账户主体名称-账户名称-优化师简称
                if (StrUtil.isNotEmpty(param.getDramaShortName())) {
                    linkName.append(param.getDramaShortName());
                } else {
                    if (StrUtil.isNotEmpty(dramaPO.getDramaName())) {
                        linkName.append(dramaPO.getDramaName());
                    }
                }
                String appTypeName = convertAppType(param.getAppType());
                if (StrUtil.isNotEmpty(appTypeName)) {
                    linkName.append("-").append(appTypeName);
                }
                if (advList.size() >= i && advList.get(i - 1) != null) {
                    String company = String.valueOf(advList.get(i - 1).getCompany());
                    String account = String.valueOf(advList.get(i - 1).getAccount());
                    if (StrUtil.isNotEmpty(company)) {
                        linkName.append("-").append(company);
                    }
                    if (StrUtil.isNotEmpty(account)) {
                        linkName.append("-").append(account);
                    }
                }
                if (StrUtil.isNotEmpty(sessionVO.getShortName())) {
                    linkName.append("-").append(sessionVO.getShortName());
                } else {
                    if (sessionVO.getUserId() != null) {
                        FastUserPO fastUserPO = fastUserMapper.queryById(sessionVO.getUserId());
                        if (fastUserPO != null && StrUtil.isNotEmpty(fastUserPO.getShortName())) {
                            linkName.append("-").append(fastUserPO.getShortName());
                        }
                    }
                }
                param.setLinkName(linkName.toString());
            }
            if (StrUtil.isEmpty(linkTitle)) {
                param.setLinkTitle(dramaPO.getDramaName());
            } else {
                param.setLinkTitle(linkTitle + "_" + i);
            }
            if (fastLinkMapper.insertSelective(param) == 0) {
                transactionRollBack();
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
            if (param.getFeeFlag() == 2) {
                FastLinkExtPO linkExt = new FastLinkExtPO();
                linkExt.setId(param.getId());
                linkExt.setContentType(param.getContentType());
                linkExt.setAnUnlockNum(param.getAnUnlockNum());
                linkExt.setIosUnlockNum(param.getIosUnlockNum());
                if (linkExtMapper.updateById(linkExt) == 0 && linkExtMapper.insertSelective(linkExt) == 0) {
                    transactionRollBack();
                    return ResultVO.error(StaticStr.ADD_FAILED);
                }
            }
            // 验证档位限制
            try {
                ResultVO<?> resVO = _checkRechargeModel(param, param);
                if (resVO.getCode() != 0) {
                    transactionRollBack();
                    return resVO;
                }
                actionLogService.log("check_vip", resVO.getMessage());
            } catch (Exception e) {
                log.error(JSONObject.toJSONString(e.getStackTrace()));
            }
            // 设置起始费用设置
            Integer coinPer = param.getCoinPer() == null ? 0 : param.getCoinPer();
            Integer startNum = param.getStartNum() == null ? 0 : param.getStartNum();
            Integer followNum = param.getFollowNum() == null ? 0 : param.getFollowNum();
            Integer skipSeries = param.getSkipSeries() == null ? 0 : param.getSkipSeries();
            FastFeeRulePO rulePO = new FastFeeRulePO();
            rulePO.setCoinPer(coinPer);
            rulePO.setCreateTime(nowTime);
            rulePO.setCreatorId(param.getCreatorId());
            rulePO.setDramaId(param.getDramaId());
            rulePO.setFollowNum(followNum);
            rulePO.setStartNum(startNum);
            rulePO.setRetailId(param.getRetailId());
            rulePO.setOfficialId(0);
            rulePO.setLinkId(param.getId());
            rulePO.setSkipSeries(skipSeries);
            if (fastFeeRuleMapper.insertSelective(rulePO) == 0) {
                transactionRollBack();
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
            String keyLinkRule = StaticVar.FEE_LINKID_RULE_ID + param.getId() + StaticVar.UNDERLINE + param.getDramaId();
            RedisUtil.del(keyLinkRule);
            FastLinkPO linkPO = new FastLinkPO();
            BeanUtils.copyProperties(param, linkPO);
            linkList.add(linkPO);
            doLogInsert(request, linkPO);
        }
        return ResultVO.success(linkList);
    }

    /**
     * 校验微信小程序不能配置上线vip
     */
    private MethodVO _checkRechargeModelVip(FastLinkPO item) {
        FastMiniPO miniPO = fastMiniService.queryById(item.getMiniId());
        if (miniPO != null && miniPO.getType() == 1) {
            // 针对微小
            FastFeeModelPO fmParam = new FastFeeModelPO();
            fmParam.setMiniId(item.getMiniId());
            fmParam.setRetailId(item.getRetailId());
            fmParam.setOfficialId(item.getOfficialId());
            fmParam.setLinkId(item.getId());
            actionLogService.log("check_vip", item.getId() + "params:" + JSONObject.toJSONString(fmParam));
            ResultVO resVO = fastFeeModelService.queryLinkFeeModelList(fmParam);
            actionLogService.log("check_vip", item.getId() + "校验vip，resVO=" + JSONObject.toJSONString(resVO));
            if ("ok".equals(resVO.getState())) {
                Map<String, Object> results = (Map) resVO.getResults();
                List<FastFeeModelPO> unpayRechargeList = (List<FastFeeModelPO>) results.get("unpayRechargeList");
                List<FastFeeModelPO> payRechargeList = (List<FastFeeModelPO>) results.get("payRechargeList");
                List<FastFeeModelPO> unpayVipList = (List<FastFeeModelPO>) results.get("unpayVipList");
                List<FastFeeModelPO> payVipList = (List<FastFeeModelPO>) results.get("payVipList");
                for (FastFeeModelPO fmPO : unpayRechargeList) {
                    for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                        if (fmd.getVipType() == 2) {
                            return MethodVO.error("微信小程序暂不支持上线vip");
                        }
                    }
                }
                for (FastFeeModelPO fmPO : payRechargeList) {
                    for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                        if (fmd.getVipType() == 2) {
                            return MethodVO.error("微信小程序暂不支持上线vip");
                        }
                    }
                }
                for (FastFeeModelPO fmPO : unpayVipList) {
                    for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                        if (fmd.getVipType() == 2) {
                            return MethodVO.error("微信小程序暂不支持上线vip");
                        }
                    }
                }
                for (FastFeeModelPO fmPO : payVipList) {
                    for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                        if (fmd.getVipType() == 2) {
                            return MethodVO.error("微信小程序暂不支持上线vip");
                        }
                    }
                }
            }
        }

        return MethodVO.success();
    }

    /**
     * 检查充值模板是否符合要求，包含不符合要求的模板，比如新剧25天内的小额vip充值等
     * true 符合要求，false 不符合要求
     */
    private ResultVO _checkRechargeModel(FastLinkPO item, FastLinkPO dbData) {
        MethodVO methodVO = _checkRechargeModelVip(dbData);
        if (methodVO.getCode() != 0) {
            return ResultVO.fromMethodVO(methodVO);
        }
        // 查询是否存在关联
        FastFeeGearRestrictRetailPO rrParam = new FastFeeGearRestrictRetailPO();
        rrParam.setRetailId(item.getRetailId());
        rrParam.setDramaId(item.getDramaId());
        rrParam.setDelFlag(0);
        FastFeeGearRestrictRetailPO rrPO = fastFeeGearRestrictRetailMapper.queryOne(rrParam);
        if (rrPO == null) {
            actionLogService.log("checkRecharge", item.getId() + "么有限制规则");
            return ResultVO.success();
        }

        // 查询上线天数
        FastDramaPO dramaPO = dramaMapper.queryById(item.getDramaId());
        Date releaseDate = dramaPO.getReleaseTime();
        if (releaseDate == null) {
            releaseDate = dramaPO.getReleaseDate();
        }
        if (releaseDate == null) {
            releaseDate = dramaPO.getCreateTime();
        }
        actionLogService.log("check_vip", "校验vip");
        Integer days = DateUtil.daysBetween(releaseDate, DateUtil.getNowDate());// 7天，就是7<天<8
        Double hours = DateUtil.hoursBetween(releaseDate, DateUtil.getNowDate());// 7小时，就是7<小时<8

        FastFeeGearRestrictPO grPO = null;
        String tipMsg = "";
        if (rrPO.getFrontHours() != null && rrPO.getFrontHours() > 0) {
            // 优先走小时
            if (hours < rrPO.getFrontHours()) {
                tipMsg = rrPO.getFrontHours() + "小时内";
                // 限制小时数内
                grPO = fastFeeGearRestrictMapper.queryById(rrPO.getFrontRestrictId());
                if (grPO.getState() == 0 || grPO.getDelFlag() == 1) {
                    return ResultVO.success();// 规则无效
                }
            } else if (hours >= rrPO.getAfterHours()) {
                tipMsg = rrPO.getAfterHours() + "小时后";
                // 限制天数后
                grPO = fastFeeGearRestrictMapper.queryById(rrPO.getAfterRestrictId());
                if (grPO.getState() == 0 || grPO.getDelFlag() == 1) {
                    return ResultVO.success();// 规则无效
                }
            }
        } else {
            // 老逻辑走天
            if (days < rrPO.getFrontDays()) {
                tipMsg = rrPO.getFrontDays() + "天内";
                // 限制天数内
                grPO = fastFeeGearRestrictMapper.queryById(rrPO.getFrontRestrictId());
                if (grPO.getState() == 0 || grPO.getDelFlag() == 1) {
                    return ResultVO.success();// 规则无效
                }
            } else if (days >= rrPO.getAfterDays()) {
                tipMsg = rrPO.getAfterDays() + "天后";
                // 限制天数后
                grPO = fastFeeGearRestrictMapper.queryById(rrPO.getAfterRestrictId());
                if (grPO.getState() == 0 || grPO.getDelFlag() == 1) {
                    return ResultVO.success();// 规则无效
                }
            }
        }

        if (grPO == null) {
            // 没有对应的限制规则
            actionLogService.log("checkRecharge", item.getId() + "么有对应的限制规则");
            return ResultVO.success();
        }

        int limitAllFlag = 0; // 是否存在限制设置  0 不限制,1 限制
        // 6种情况，有一种符合要求就限制，一个if写的太长了，分开写
        if (grPO.getRechargeFlag() == 1 || grPO.getUnrechargeFlag() == 1) {
            limitAllFlag = 1;
        }
        if (grPO.getVipFlag() == 1 || grPO.getUnvipFlag() == 1) {
            limitAllFlag = 1;
        }
        if (grPO.getDramaCardFlag() == 1 || grPO.getUndramaCardFlag() == 1) {
            limitAllFlag = 1;
        }
        // 添加其他规则验证
        Integer vipDaysLimitFlag = 0;// vip当日可用
        Integer vipPriceLimitFlag = 0;// 大7天vip档位金额
        Integer coinCountLimitFlag = 0;// 单集K币限制
        Integer coinGiftCountLimitFlag = 0;// 赠币限制

        BigDecimal vipDaysPriceStart = BigDecimal.ZERO;// vip当日可用 - 起始价格
        BigDecimal vipDaysPriceEnd = BigDecimal.ZERO;// vip当日可用 - 上线价格
        BigDecimal vipPriceStart = BigDecimal.ZERO;// 大7天vip档位金额 - 起始价格
        Integer coinCountStart = 200;// 单集K币限制
        BigDecimal coinGiftCountTimes = BigDecimal.ONE;// 赠币限制倍数


        if (StrUtil.isNotEmpty(grPO.getRestrictJson())) {
            JSONObject resObj = JSONObject.parseObject(grPO.getRestrictJson());
            // 数据格式如下
//        	{   
//				"vipDaysLimit":{ flag : 0, priceStart: 10, priceEnd : 15 },// vip当日可用
//				"vipPriceLimit":{ flag : 1, priceStart : 20 },// 大7天vip档位金额
//				"coinCountLimit":{ flag : 1, countStart : 100 },// 单集K币限制
//				"coinGiftCountLimit":{ flag : 0, times : 1 // 1倍 }// 赠币限制
//			}
            JSONObject vipDaysLimitObj = resObj.getJSONObject("vipDaysLimit");
            JSONObject vipPriceLimitObj = resObj.getJSONObject("vipPriceLimit");
            JSONObject coinCountLimitObj = resObj.getJSONObject("coinCountLimit");
            JSONObject coinGiftCountLimitObj = resObj.getJSONObject("coinGiftCountLimit");
            if (vipDaysLimitObj != null && vipDaysLimitObj.getInteger("flag") != null && vipDaysLimitObj.getInteger("flag") == 1) {
                limitAllFlag = 1;
                vipDaysLimitFlag = 1;
                vipDaysPriceStart = vipDaysLimitObj.getBigDecimal("priceStart");
                vipDaysPriceEnd = vipDaysLimitObj.getBigDecimal("priceEnd");
            }
            if (vipPriceLimitObj != null && vipPriceLimitObj.getInteger("flag") != null && vipPriceLimitObj.getInteger("flag") == 1) {
                limitAllFlag = 1;
                vipPriceLimitFlag = 1;
                vipPriceStart = vipPriceLimitObj.getBigDecimal("priceStart");
            }

            if (coinCountLimitObj != null && coinCountLimitObj.getInteger("flag") != null && coinCountLimitObj.getInteger("flag") == 1) {
                limitAllFlag = 1;
                coinCountLimitFlag = 1;
                coinCountStart = coinCountLimitObj.getInteger("countStart");
            }
            if (coinGiftCountLimitObj != null && coinGiftCountLimitObj.getInteger("flag") != null && coinGiftCountLimitObj.getInteger("flag") == 1) {
                limitAllFlag = 1;
                coinGiftCountLimitFlag = 1;
                coinGiftCountTimes = coinGiftCountLimitObj.getBigDecimal("times");
            }
        }

        BigDecimal rechargeStartLimit = grPO.getRechargeStart();
        BigDecimal rechargeEndLimit = grPO.getRechargeEnd();
        BigDecimal unrechargeStartLimit = grPO.getUnrechargeStart();
        BigDecimal unrechargeEndLimit = grPO.getUnrechargeEnd();
        BigDecimal vipStartLimit = grPO.getVipStart();
        BigDecimal vipEndLimit = grPO.getVipEnd();
        BigDecimal unvipStartLimit = grPO.getUnvipStart();
        BigDecimal unvipEndLimit = grPO.getUnvipEnd();
        BigDecimal dramaCardStartLimit = grPO.getDramaCardStart();
        BigDecimal dramaCardEndLimit = grPO.getDramaCardEnd();
        BigDecimal undramaCardStartLimit = grPO.getDramaCardStart();
        BigDecimal undramaCardEndLimit = grPO.getDramaCardEnd();

        actionLogService.log("check_vip", "校验vip，days=" + days);
        if (limitAllFlag > 0) {
            actionLogService.log("check_vip", "校验模板开始");
            // 判断充值模板是否存在X元以下
            FastFeeModelPO fmParam = new FastFeeModelPO();
            fmParam.setMiniId(item.getMiniId());
            fmParam.setRetailId(item.getRetailId());
            fmParam.setOfficialId(item.getOfficialId());
            fmParam.setLinkId(item.getId());
            actionLogService.log("check_vip", item.getId() + "params:" + JSONObject.toJSONString(fmParam));
            ResultVO resVO = fastFeeModelService.queryLinkFeeModelList(fmParam);
            actionLogService.log("check_vip", item.getId() + "校验vip，resVO=" + JSONObject.toJSONString(resVO));
            if ("ok".equals(resVO.getState())) {
                Map<String, Object> results = (Map) resVO.getResults();
                List<FastFeeModelPO> unpayRechargeList = (List<FastFeeModelPO>) results.get("unpayRechargeList");
                List<FastFeeModelPO> payRechargeList = (List<FastFeeModelPO>) results.get("payRechargeList");
                List<FastFeeModelPO> unpayVipList = (List<FastFeeModelPO>) results.get("unpayVipList");
                List<FastFeeModelPO> payVipList = (List<FastFeeModelPO>) results.get("payVipList");
                boolean checkFlag = false;// 判断是否存在不合适的模板
                int limitType = 0;
                // 1验证k币-未充值
                if (grPO.getUnrechargeFlag() == 1) {
                    // 验证k币充值
                    for (FastFeeModelPO fmPO : unpayRechargeList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() == 1) {
                                if (fmd.getMoneyRecharge().compareTo(unrechargeEndLimit) <= 0 && fmd.getMoneyRecharge().compareTo(unrechargeStartLimit) >= 0) {
                                    checkFlag = true;
                                    limitType = 1;
                                }
                            }
                        }
                    }
                }
                // 2验证k币-已充值
                if (grPO.getRechargeFlag() == 1) {
                    for (FastFeeModelPO fmPO : payRechargeList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() == 1) {
                                if (fmd.getMoneyRecharge().compareTo(rechargeEndLimit) <= 0 && fmd.getMoneyRecharge().compareTo(rechargeStartLimit) >= 0) {
                                    checkFlag = true;
                                    limitType = 2;
                                }
                            }
                        }
                    }
                }
                // 3验证vip未充值
                if (grPO.getUnvipFlag() == 1) {
                    // 验证vip
                    for (FastFeeModelPO fmPO : unpayVipList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() == 2) {
                                if (fmd.getMoneyRecharge().compareTo(unvipEndLimit) <= 0 && fmd.getMoneyRecharge().compareTo(unvipStartLimit) >= 0) {
                                    checkFlag = true;
                                    limitType = 3;
                                }
                            }
                        }
                    }
                    // 验证k币充值里的vip档位
                    for (FastFeeModelPO fmPO : unpayRechargeList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() != null && fmd.getType() == 2) {
                                if (fmd.getMoneyRecharge().compareTo(unvipEndLimit) <= 0 && fmd.getMoneyRecharge().compareTo(unvipStartLimit) >= 0) {
                                    checkFlag = true;
                                    limitType = 3;
                                }
                            }
                        }
                    }
                }
                // 4验证vip已充值
                if (grPO.getVipFlag() == 1) {
                    for (FastFeeModelPO fmPO : payVipList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() != null && fmd.getType() == 2) {
                                if (fmd.getMoneyRecharge().compareTo(vipEndLimit) <= 0 && fmd.getMoneyRecharge().compareTo(vipStartLimit) >= 0) {
                                    checkFlag = true;
                                    limitType = 4;
                                }
                            }
                        }
                    }
                    // k币充值里面的vip充值
                    for (FastFeeModelPO fmPO : payRechargeList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() != null && fmd.getType() == 2) {
                                if (fmd.getMoneyRecharge().compareTo(vipEndLimit) <= 0 && fmd.getMoneyRecharge().compareTo(vipStartLimit) >= 0) {
                                    checkFlag = true;
                                    limitType = 4;
                                }
                            }
                        }
                    }
                }
                // 5验证剧卡未充值
                if (grPO.getUndramaCardFlag() == 1) {
                    // 验证k币充值里的剧卡
                    for (FastFeeModelPO fmPO : unpayRechargeList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() != null && fmd.getType() == 3) {
                                if (fmd.getMoneyRecharge().compareTo(undramaCardEndLimit) <= 0 && fmd.getMoneyRecharge().compareTo(undramaCardStartLimit) >= 0) {
                                    checkFlag = true;
                                    limitType = 5;
                                }
                            }
                        }
                    }
                    // 验证vip里面的剧卡
                    for (FastFeeModelPO fmPO : unpayVipList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() != null && fmd.getType() == 3) {
                                if (fmd.getMoneyRecharge().compareTo(undramaCardEndLimit) <= 0 && fmd.getMoneyRecharge().compareTo(undramaCardStartLimit) >= 0) {
                                    checkFlag = true;
                                    limitType = 5;
                                }
                            }
                        }
                    }
                }
                // 6验证剧卡已充值
                if (grPO.getDramaCardFlag() == 1) {
                    // k币充值里面的vip充值
                    for (FastFeeModelPO fmPO : payRechargeList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() != null && fmd.getType() == 3) {
                                if (fmd.getMoneyRecharge().compareTo(dramaCardEndLimit) <= 0 && fmd.getMoneyRecharge().compareTo(dramaCardStartLimit) >= 0) {
                                    checkFlag = true;
                                    limitType = 6;
                                }
                            }
                        }
                    }
                    // vip已充值列表
                    for (FastFeeModelPO fmPO : payVipList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() != null && fmd.getType() == 3) {
                                if (fmd.getMoneyRecharge().compareTo(dramaCardEndLimit) <= 0 && fmd.getMoneyRecharge().compareTo(dramaCardStartLimit) >= 0) {
                                    checkFlag = true;
                                    limitType = 6;
                                }
                            }
                        }
                    }
                }
                // 7其他 vip当日可用
                if (vipDaysLimitFlag == 1) {
                    // 首复充面板中，VIP档位金额在vipDaysPriceStart到vipDaysPriceEnd元时，VIP 时效只能为单日可用
                    // vip未充
                    for (FastFeeModelPO fmPO : unpayVipList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() == 2 && fmd.getMoneyRecharge().compareTo(vipDaysPriceEnd) <= 0 && fmd.getMoneyRecharge().compareTo(vipDaysPriceStart) >= 0) {
                                if (fmd.getValidUnit() == 4 && fmd.getValidDate() == 1) {
                                    // 只能设置1天
                                } else {
                                    checkFlag = true;
                                    limitType = 7;
                                }
                            }
                        }
                    }
                    // vip已充
                    for (FastFeeModelPO fmPO : payVipList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() == 2 && fmd.getMoneyRecharge().compareTo(vipDaysPriceEnd) <= 0 && fmd.getMoneyRecharge().compareTo(vipDaysPriceStart) >= 0) {
                                if (fmd.getValidUnit() == 4 && fmd.getValidDate() == 1) {
                                    // 只能设置1天
                                } else {
                                    checkFlag = true;
                                    limitType = 7;
                                }
                            }
                        }
                    }
                }
                // 8大7天vip档位金额
                if (vipPriceLimitFlag == 1) {
                    // 首复充面板中，周卡(大于7日）以上的 VIP，档位金额需≥vipPriceStart
                    // vip未充
                    for (FastFeeModelPO fmPO : unpayVipList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getValidUnit() == 4 && fmd.getValidDate() <= 7) {
                                // 日卡7天，不处理
                            } else if (fmd.getValidUnit() == 1 && fmd.getValidDate() == 1) {
                                // 周卡1周，不处理
                            } else {
                                if (fmd.getMoneyRecharge().compareTo(vipPriceStart) <= 0) {
                                    checkFlag = true;
                                    limitType = 8;
                                }
                            }
                        }
                    }
                    // vip已充
                    for (FastFeeModelPO fmPO : payVipList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getValidUnit() == 4 && fmd.getValidDate() <= 7) {
                                // 日卡7天，不处理
                            } else if (fmd.getValidUnit() == 1 && fmd.getValidDate() == 1) {
                                // 周卡1周，不处理
                            } else {
                                if (fmd.getMoneyRecharge().compareTo(vipPriceStart) <= 0) {
                                    checkFlag = true;
                                    limitType = 8;
                                }
                            }
                        }
                    }
                }
                // 9单集K币限制
                if (coinCountLimitFlag == 1) {
                    Integer coinPer = 0;// 默认200
                    // 单集K币不得小于coinCountStart K币
                    if (item.getCoinPerGlobal() == 1) {
                        // 全局
                        FastFeeRuleVO feeRule = feeRuleService.queryInfoByRedis(item.getOfficialId(), item.getDramaId());
                        if (feeRule == null) {
                            feeRule = feeRuleService.queryInfoByRedis(0, item.getDramaId());
                        }
                        if (feeRule != null) {
                            coinPer = feeRule.getCoinPer();
                        }
                    } else {
                        coinPer = item.getCoinPer();
                    }
                    if (coinPer < coinCountStart) {
                        checkFlag = true;
                        limitType = 9;
                    }
                }
                // 10赠币限制
                if (coinGiftCountLimitFlag == 1) {
                    // K 币档位中赠送 K 币数不得大于档位充值币的coinGiftCountTimes倍
                    for (FastFeeModelPO fmPO : unpayRechargeList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() == 1) {
                                BigDecimal coinGiftMax = DoubleUtil.mulB(BigDecimal.valueOf(fmd.getCoinRecharge()), coinGiftCountTimes);
                                if (fmd.getCoinGive() > coinGiftMax.intValue()) {
                                    checkFlag = true;
                                    limitType = 10;
                                }
                            }
                        }
                    }
                    for (FastFeeModelPO fmPO : payRechargeList) {
                        for (FastFeeModelDetailPO fmd : fmPO.getDetailList()) {
                            if (fmd.getType() == 1) {
                                BigDecimal coinGiftMax = DoubleUtil.mulB(BigDecimal.valueOf(fmd.getCoinRecharge()), coinGiftCountTimes);
                                if (fmd.getCoinGive() > coinGiftMax.intValue()) {
                                    checkFlag = true;
                                    limitType = 10;
                                }
                            }
                        }
                    }
                }

                if (checkFlag) {
                    // 模板不符合要求
                    StringBuilder sb = new StringBuilder();
                    sb.append("限制内容：短剧上线");
                    sb.append(tipMsg);
                    sb.append("，链接充值模板中，");
                    if (limitType == 1) {
                        sb.append("未充值-K币中不得含有");
                        sb.append(unrechargeStartLimit);
                        sb.append("-");
                        sb.append(unrechargeEndLimit);
                        sb.append("元档位");
                    }
                    if (limitType == 2) {
                        sb.append("已充值-K币中不得含有");
                        sb.append(rechargeStartLimit);
                        sb.append("-");
                        sb.append(rechargeEndLimit);
                        sb.append("元档位");
                    }
                    if (limitType == 3) {
                        sb.append("未充值-VIP卡不得含有");
                        sb.append(unvipStartLimit);
                        sb.append("-");
                        sb.append(unvipEndLimit);
                        sb.append("元档位");
                    }
                    if (limitType == 4) {
                        sb.append("已充值-VIP卡不得含有");
                        sb.append(vipStartLimit);
                        sb.append("-");
                        sb.append(vipEndLimit);
                        sb.append("元档位");
                    }
                    if (limitType == 5) {
                        sb.append("未充值-剧卡不得含有");
                        sb.append(undramaCardStartLimit);
                        sb.append("-");
                        sb.append(undramaCardEndLimit);
                        sb.append("元档位");
                    }
                    if (limitType == 6) {
                        sb.append("已充值-剧卡不得含有");
                        sb.append(dramaCardStartLimit);
                        sb.append("-");
                        sb.append(dramaCardEndLimit);
                        sb.append("元档位");
                    }
                    if (limitType == 7) {
                        sb.append("首复充面板中，VIP档位金额在");
                        sb.append(vipDaysPriceStart);
                        sb.append("到");
                        sb.append(vipDaysPriceEnd);
                        sb.append("元时，VIP 时效只能为单日可用");
                    }
                    if (limitType == 8) {
                        sb.append("首复充面板中，周卡(大于7日）以上的 VIP，档位金额需≥");
                        sb.append(vipPriceStart);
                    }
                    if (limitType == 9) {
                        sb.append("单集K币不得小于");
                        sb.append(coinCountStart);
                        sb.append("K币");
                    }
                    if (limitType == 10) {
                        sb.append("K币档位中赠送K币数不得大于档位充值币的");
                        sb.append(coinGiftCountTimes);
                        sb.append("倍");
                    }
                    Map<String, Object> resMap = ResultVO.getMap();

                    Map<Integer, String> restrictMap = fastFeeGearRestrictService.getRestrictMap(grPO.getId());
                    resMap.put("restrictMap", restrictMap);
                    return ResultVO.error(505, sb.toString(), resMap);
                }
            } else {
                actionLogService.log("check_vip", "无需校验模板开始");
                return ResultVO.error(resVO.getMessage());
            }

        }
        return ResultVO.success();
    }

    /**
     * 批量更新渠道链接
     */
    public ResultVO updateBatch(HttpServletRequest request, FastLinkPO params) {
        List<String> enList = CollUtil.parseStr2List(params.getEncryptionIds());
        List<Integer> linkIdList = new ArrayList<>();
        for (String encryptionId : enList) {
            Integer id = decodeInt(encryptionId);
            if (id != null) {
                linkIdList.add(id);
            }
        }
        // 开始处理
        for (Integer linkId : linkIdList) {
            FastLinkPO linkPO = fastLinkMapper.queryById(linkId);
            // 起始付费
            if (params.getStartNumGlobal() != null) {
                linkPO.setStartNumGlobal(params.getStartNumGlobal());
                linkPO.setStartNum(params.getStartNum());
            }
            // 单集k币
            if (params.getCoinPerGlobal() != null) {
                linkPO.setCoinPerGlobal(params.getCoinPerGlobal());
                linkPO.setCoinPer(params.getCoinPer());
            }
            // 充值模板
            if (params.getPayType() != null) {
                linkPO.setPayType(params.getPayType());
                linkPO.setPayRule(params.getPayRule());
            }
            // 回传
            if (params.getBackType() != null) {
                linkPO.setBackType(params.getBackType());
                linkPO.setBackRule(params.getBackRule());
            }
            // 支付挽留
            if (params.getKeepType() != null) {
                linkPO.setKeepType(params.getKeepType());
                linkPO.setKeepId(params.getKeepId());
            }
            // 免费广告
            if (params.getAdvUnlockFlag() != null) {
                linkPO.setAdvUnlockFlag(params.getAdvUnlockFlag());
                linkPO.setAdvUnlockNum(params.getAdvUnlockNum());
            }
            // 免费广告
            if (params.getSkipSeries() != null) {
                linkPO.setSkipSeries(params.getSkipSeries());
            }
            linkPO.setUpdatorId(params.getUpdatorId());
            ResultVO resVO = update(request, linkPO);
            if (resVO.getCode() != 0) {
                return resVO;
            }
        }
        for (Integer linkId : linkIdList) {
            // 删除缓存
            String key = StaticVar.LINK_INFO_ID + linkId;
            RedisUtil.del(key);
        }
        return ResultVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO update(HttpServletRequest request, FastLinkPO params) {
        ResultVO feeModelOld = fastFeeModelService.getModelBatch(params.getId(), params.getPhoneOs());
        Date nowDate = DateUtil.getNowDate();
        FastLinkPO dbData = fastLinkMapper.queryById(params.getId());
        params.setUpdateTime(nowDate);
        params.setMiniId(dbData.getMiniId());
        params.setMiniAppId(dbData.getMiniAppId());
        if (!dbData.getRetailId().equals(params.getRetailId())) {
            return ResultVO.error(StaticStr.UPDATE_FAILED);
        }
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return ResultVO.error(StaticStr.UPDATE_FAILED);
        }
        try {
            ResultVO resVO = _checkRechargeModel(params, dbData);
            if (resVO.getCode() != 0) {
                transactionRollBack();
                return resVO;
            }
            actionLogService.log("check_vip", resVO.getMessage());
        } catch (Exception e) {
            log.error(JSONObject.toJSONString(e.getStackTrace()));
        }

        // 计费规则-查询是否存在渠道维度的
        Date nowTime = DateUtil.getNowDate();
        FastFeeRulePO frParam = new FastFeeRulePO();
        frParam.setLinkId(params.getId());
        FastFeeRulePO frPO = fastFeeRuleMapper.queryOne(frParam);
        if (frPO != null && params.getCoinPer() == null) {
            params.setCoinPer(frPO.getCoinPer());
        }
        if (frPO != null && params.getStartNum() == null) {
            params.setStartNum(frPO.getStartNum());
        }
        if (frPO != null && params.getFollowNum() == null) {
            params.setFollowNum(frPO.getFollowNum());
        }
        if (frPO != null && params.getSkipSeries() == null) {
            params.setSkipSeries(frPO.getSkipSeries());
        }
        // 更新修改计费规则
        Integer coinPer = params.getCoinPer() == null ? 0 : params.getCoinPer();
        Integer startNum = params.getStartNum() == null ? 0 : params.getStartNum();
        Integer followNum = params.getFollowNum() == null ? 0 : params.getFollowNum();
        Integer skipSeries = params.getSkipSeries() == null ? 0 : params.getSkipSeries();
        FastFeeRulePO feePO = new FastFeeRulePO();
        feePO.setLinkId(params.getId());
        feePO.setStartNum(startNum);
        feePO.setCoinPer(coinPer);
        feePO.setFollowNum(followNum);
        feePO.setSkipSeries(skipSeries);
        feePO.setRetailId(params.getRetailId());
        feePO.setUpdatorId(params.getUpdatorId());
        feePO.setUpdateTime(nowTime);
        Integer updateCount = fastFeeRuleMapper.updateByLinkId(feePO);
        if (updateCount == 0) {
            // 新增起始费用设置
            FastFeeRulePO rulePO = new FastFeeRulePO();
            rulePO.setCoinPer(coinPer);
            rulePO.setCreateTime(nowTime);
            rulePO.setCreatorId(params.getUpdatorId());
            rulePO.setDramaId(params.getDramaId());
            rulePO.setFollowNum(followNum);
            rulePO.setStartNum(startNum);
            rulePO.setSkipSeries(skipSeries);
            rulePO.setRetailId(params.getRetailId());
            rulePO.setOfficialId(0);
            rulePO.setLinkId(params.getId());
            if (fastFeeRuleMapper.insertSelective(rulePO) == 0) {
                transactionRollBack();
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
        }
        String keyLinkRule = StaticVar.FEE_LINKID_RULE_ID + params.getId() + StaticVar.UNDERLINE + params.getDramaId();
        RedisUtil.del(keyLinkRule);
        // 更新link里面的promoteLink
        FastLinkPO linkPO = fastLinkMapper.queryById(params);
        FastDramaSeriesPO seriesPO = fastDramaSeriesService.queryById(linkPO.getSeriesId());
        if (seriesPO != null) {
            linkPO.setSeriesNum(seriesPO.getSeriesNum());
        } else {
            linkPO.setSeriesNum(1);
        }
        linkPO.setMiniAppId(params.getMiniAppId());
        String promoteLink = getPromoteLinkByLinkPO(linkPO);
        linkPO.setPromoteLink(promoteLink);
        if (fastLinkMapper.updateById(linkPO) == 0) {
            transactionRollBack();
            return ResultVO.error(StaticStr.UPDATE_FAILED);
        }
        // 更新关联短链
        FastShortLinkPO slParam = new FastShortLinkPO();
        slParam.setShortLink(linkPO.getShortLink());
        FastShortLinkPO shortLink = fastShortLinkService.queryOne(slParam);
        if (shortLink != null) {
            shortLink.setLongLink(getLongLinkByLinkPO(linkPO));
            if (fastShortLinkMapper.updateById(shortLink) == 0) {
                transactionRollBack();
                return ResultVO.error(StaticStr.UPDATE_FAILED);
            }
        }
        if (params.getFeeFlag() == 2) {
            FastLinkExtPO linkExt = new FastLinkExtPO();
            linkExt.setId(params.getId());
            linkExt.setContentType(params.getContentType());
            linkExt.setAnUnlockNum(params.getAnUnlockNum());
            linkExt.setIosUnlockNum(params.getIosUnlockNum());
            if (linkExtMapper.updateById(linkExt) == 0 && linkExtMapper.insertSelective(linkExt) == 0) {
                transactionRollBack();
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
        }

        return ResultVO.success();
    }

    /**
     * 记录操作日志-新增
     */
    private void doLogInsert(HttpServletRequest request, FastLinkPO params) {
        Date nowDate = DateUtil.getNowDate();
        FastLogOperationPO operationPO = new FastLogOperationPO();
        operationPO.setRetailId(params.getRetailId());
        operationPO.setObject(params.getId() + "-" + params.getLinkName());// 操作对象
        operationPO.setObjectId(params.getId());// 操作对象ID
        operationPO.setObjectType(1);
        operationPO.setIp(IPUtil.ipToLong(request));
        operationPO.setUserId(params.getUpdatorId());
        operationPO.setCreateTime(nowDate);
        operationPO.setOfficialId(params.getOfficialId());
        ResultVO<?> resultVO = fastFeeModelService.getModelBatch(params.getId(), params.getPhoneOs());
        operationPO.setSnapshotNew(JsonUtil.toString(resultVO));
        // 新增充值模板记录
        operationPO.setType(OperationLogEnum.OP_15.index);
        operationPO.setRemark("首次关联充值模板");
        operationLogService.insertLog(operationPO);
    }

    /**
     * 更新投放链接信息
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateLink(FastLinkPO params) {
        // 根据应用类型和投放媒体类型，生成链接信息
        // 应用类型: 1-微信小程序 2-微信H5
        switch (params.getAppType()) {
            case 1:// 1-微信小程序
                switch (params.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                        updateLinkToutiao(params);
                        break;
                    case 2:// 2-adq
                        updateLinkAdq(params);
                        break;
                    case 3:// 3-mp
                        updateLinkMp(params);
                        break;
                    case 4:// 4-百度
                        updateLinkBaidu(params);
                        break;
                    case 5:// 5-快手
                        updateLinkKuaishou(params);
                        break;
                    case 6:// 6-新浪微博
                        updateLinkWeibo(params);
                        break;
                    case 7:// 7-哔哩哔哩
                        updateLinkBilibili(params);
                        break;
                    default:
                        break;
                }
                break;
            case 2:// 2-抖音小程序
                switch (params.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                        updateLinkTiktok(params);
                        break;
                    default:
                        break;
                }
                break;
            case 3:// 3-h5站
                switch (params.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                        updateLinkH5(params);
                        break;
                    default:
                        break;
                }
                break;
            case 4:// 4-快手
                switch (params.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 5:// 5-快手
                        updateLinkKuaishou(params);
                        break;
                    default:
                        break;
                }
                break;
            case 5:// 5-快应用
                switch (params.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 2:// 2-adq
                        updateLinkAdqQuick(params);
                        break;
                    case 4:// 4-百度
                        updateLinkBaiduQuick(params);
                        break;
                    default:
                        break;
                }
                break;
            case 6:// 6-支付宝
                switch (params.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                        updateLinkToutiao(params);
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        return MethodVO.success();
    }

    // 抖音小程序，抖音渠道设置链接
    public MethodVO updateLinkTiktok(FastLinkPO params) {
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        // 设置推广链接
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
        }
        String promoteLink = "";

        if (params.getLinkType() == 3) {
            Integer refer = params.getLinkPlatform();
            if (refer == null) {
                refer = 0;
            }
            promoteLink = String.format(StaticYml.PROMOTE_MINI_PAGE_OPERATION_URL, params.getDramaId(), params.getSeriesNum(), params.getOfficialId(), params.getId(), params.getAdvMediaId(), refer);
        } else {
            promoteLink = String.format(StaticVar.PROMOTE_TIKTOK_MINI_PAGE_URL, params.getMiniAppId(), params.getDramaId(), params.getSeriesNum(), params.getId(), params.getAdvMediaId());
        }
        params.setPromoteLink(promoteLink);

        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 获取组装的promoteLink
     *
     * @param linkPO
     * @return
     */
    private String getPromoteLinkByLinkPO(FastLinkPO linkPO) {
        String promoteLink = "";
        Integer refer = linkPO.getLinkPlatform();
        if (refer == null) {
            refer = 0;
        }
        if (linkPO.getLinkType() != null && linkPO.getLinkType() == 2) {
            // 运营
            promoteLink = String.format(StaticYml.PROMOTE_MINI_PAGE_OPERATION_URL, linkPO.getDramaId(), linkPO.getSeriesNum(), linkPO.getOfficialId(), linkPO.getId(), linkPO.getAdvMediaId(), refer);
        } else if (linkPO.getLinkType() != null && linkPO.getLinkType() == 3) {
            // 挂载
            promoteLink = String.format(StaticYml.PROMOTE_MINI_PAGE_OPERATION_URL, linkPO.getDramaId(), linkPO.getSeriesNum(), linkPO.getOfficialId(), linkPO.getId(), linkPO.getAdvMediaId(), refer);
        } else {
            if (linkPO.getAppType() == 2) {
                // 抖音小程序
                promoteLink = String.format(StaticVar.PROMOTE_TIKTOK_MINI_PAGE_URL, linkPO.getMiniAppId(), linkPO.getDramaId(), linkPO.getSeriesNum(), linkPO.getId(), linkPO.getAdvMediaId());
            } else if (linkPO.getAppType() == 5) {
                // 快应用
                promoteLink = String.format(StaticVar.PROMOTE_QUICK_PAGE_URL, linkPO.getMiniAppId(), linkPO.getDramaId(), linkPO.getSeriesNum(), linkPO.getOfficialId(), linkPO.getId(), linkPO.getAdvMediaId());
            } else {
                // 微信小程序
                promoteLink = String.format(StaticVar.PROMOTE_MINI_PAGE_URL, linkPO.getDramaId(), linkPO.getSeriesNum(), linkPO.getOfficialId(), linkPO.getId(), linkPO.getAdvMediaId());
            }
        }
        return promoteLink;
    }

    private String getLongLinkByLinkPO(FastLinkPO linkPO) {
        String longLink = "";
        longLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, linkPO.getMiniAppId(), linkPO.getOfficialId(), URLEncoder.encode(linkPO.getLinkTitle(), StandardCharsets.UTF_8), URLEncoder.encode(linkPO.getPromoteLink(), StandardCharsets.UTF_8));
        return longLink;
    }

    // 根据抖音广告渠道，设置链接信息
    public MethodVO updateLinkToutiao(FastLinkPO params) {
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        // 设置推广链接
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            if (fastDramaSeriesPO != null) {
                params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
            } else {
                params.setSeriesNum(1);
            }
        }
        String promoteLink = getPromoteLinkByLinkPO(params);
        params.setPromoteLink(promoteLink);

        // 设置短连接
        FastShortLinkPO fslItem = new FastShortLinkPO();
        fslItem.setCreatorId(params.getCreatorId());
        String longLink = getLongLinkByLinkPO(params);

        fslItem.setLongLink(longLink);
        fastShortLinkService.insert(fslItem);
        params.setShortLink(fslItem.getShortLink());

        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 根据百度，设置链接信息
    public MethodVO updateLinkBaidu(FastLinkPO params) {
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        // 设置推广链接
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
        }

        String promoteLink = String.format(StaticVar.PROMOTE_MINI_PAGE_URL, params.getDramaId(), params.getSeriesNum(), params.getOfficialId(), params.getId(), params.getAdvMediaId());
        params.setPromoteLink(promoteLink);

        // 设置短连接
        FastShortLinkPO fslItem = new FastShortLinkPO();
        fslItem.setCreatorId(params.getCreatorId());
        String longLink = null;
        longLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_BAIDU_URL, params.getMiniAppId(), params.getOfficialId(), URLEncoder.encode(params.getLinkTitle(), StandardCharsets.UTF_8), URLEncoder.encode(params.getPromoteLink(), StandardCharsets.UTF_8));
        fslItem.setLongLink(longLink);
        fastShortLinkService.insert(fslItem);
        params.setShortLink(fslItem.getShortLink());

        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 根据百度，设置链接信息（快应用）
    public MethodVO updateLinkBaiduQuick(FastLinkPO params) {
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        // 设置推广链接
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
        }
        String promoteLink = String.format(StaticVar.PROMOTE_QUICK_PAGE_URL, params.getMiniAppId(), params.getDramaId(), params.getSeriesNum(), params.getOfficialId(), params.getId(), params.getAdvMediaId());
        params.setPromoteLink(promoteLink);
        // 设置短连接
        FastShortLinkPO fslItem = new FastShortLinkPO();
        fslItem.setCreatorId(params.getCreatorId());
        String longLink = null;
        longLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_BAIDU_URL, params.getMiniAppId(), params.getOfficialId(), URLEncoder.encode(params.getLinkTitle(), StandardCharsets.UTF_8), URLEncoder.encode(params.getPromoteLink(), StandardCharsets.UTF_8));
        fslItem.setLongLink(longLink);
        fastShortLinkService.insert(fslItem);
        params.setShortLink(fslItem.getShortLink());

        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // H5站，巨量投放链接更新
    public MethodVO updateLinkH5(FastLinkPO params) {
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
        }
        // 设置推广链接(直达h5页面)
        String promoteLinkPath = String.format(StaticVar.PROMOTE_H5_PAGE_URL, params.getOfficialId(), params.getDramaId(), params.getSeriesNum(), params.getId(), params.getAdvMediaId());
        // 生成短链
        FastShortLinkPO shortLinkPO = new FastShortLinkPO();
        shortLinkPO = new FastShortLinkPO();
        shortLinkPO.setLongLink(promoteLinkPath);
        shortLinkPO.setShorts(LinkUtil.getShortURL());
        shortLinkPO.setType(2);
        shortLinkPO.setFlag(0);
        shortLinkPO.setCreatorId(params.getCreatorId());
        shortLinkPO.setCreateTime(DateUtil.getNowDate());
        if (shortLinkMapper.insertSelective(shortLinkPO) < 1) {
            return MethodVO.error("短链生成失败，请重试");
        }
        params.setPromoteLink(shortLinkPO.getShorts());// 存短链，域名在查询的时候，动态获取，然后拼装
        // 设置落地页链接（中间落地页，广告计划跳转的页面，自研落地页的地址）
        String shortLink = String.format(StaticYml.PROMOTE_H5_GROUND_URL, params.getOfficialId(), params.getId());
        params.setShortLink(shortLink);
        // 开始更新
        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 根据adq广告渠道，设置链接信息
    public MethodVO updateLinkAdq(FastLinkPO params) {
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        // 设置推广链接
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
        }
        String promoteLink = String.format(StaticVar.PROMOTE_MINI_PAGE_URL, params.getDramaId(), params.getSeriesNum(), params.getOfficialId(), params.getId(), params.getAdvMediaId());
        params.setPromoteLink(promoteLink);

        // 设置短连接
        FastShortLinkPO fslItem = new FastShortLinkPO();
        fslItem.setCreatorId(params.getCreatorId());
        String longLink = null;
        longLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, params.getMiniAppId(), params.getOfficialId(), URLEncoder.encode(params.getLinkTitle(), StandardCharsets.UTF_8), URLEncoder.encode(params.getPromoteLink(), StandardCharsets.UTF_8));
        fslItem.setLongLink(longLink);
        fastShortLinkService.insert(fslItem);
        params.setShortLink(fslItem.getShortLink());

        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 根据adq广告渠道，设置链接信息（快应用）
    public MethodVO updateLinkAdqQuick(FastLinkPO params) {
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        // 设置推广链接
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
        }
        String promoteLink = String.format(StaticVar.PROMOTE_QUICK_PAGE_URL, params.getMiniAppId(), params.getDramaId(), params.getSeriesNum(), params.getOfficialId(), params.getId(), params.getAdvMediaId());
        params.setPromoteLink(promoteLink);

        // 设置短连接
        FastShortLinkPO fslItem = new FastShortLinkPO();
        fslItem.setCreatorId(params.getCreatorId());
        String longLink = null;
        longLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, params.getMiniAppId(), params.getOfficialId(), URLEncoder.encode(params.getLinkTitle(), StandardCharsets.UTF_8), URLEncoder.encode(params.getPromoteLink(), StandardCharsets.UTF_8));
        fslItem.setLongLink(longLink);
        fastShortLinkService.insert(fslItem);
        params.setShortLink(fslItem.getShortLink());

        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 根据mp广告渠道，设置链接信息
    public MethodVO updateLinkMp(FastLinkPO params) {
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        // 设置推广链接
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
        }
        String promoteLink = String.format(StaticVar.PROMOTE_MINI_PAGE_URL, params.getDramaId(), params.getSeriesNum(), params.getOfficialId(), params.getId(), params.getAdvMediaId());
        params.setPromoteLink(promoteLink);

        // 设置短连接
        FastShortLinkPO fslItem = new FastShortLinkPO();
        fslItem.setCreatorId(params.getCreatorId());
        String longLink = null;
        longLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, params.getMiniAppId(), params.getOfficialId(), URLEncoder.encode(params.getLinkTitle(), StandardCharsets.UTF_8), URLEncoder.encode(params.getPromoteLink(), StandardCharsets.UTF_8));
        fslItem.setLongLink(longLink);
        fastShortLinkService.insert(fslItem);
        params.setShortLink(fslItem.getShortLink());

        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 根据抖音广告渠道，设置链接信息
    public MethodVO updateLinkKuaishou(FastLinkPO params) {
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        // 设置推广链接
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
        }
        String promoteLink = getPromoteLinkByLinkPO(params);
        params.setPromoteLink(promoteLink);

        // 设置短连接
        FastShortLinkPO fslItem = new FastShortLinkPO();
        fslItem.setCreatorId(params.getCreatorId());
        String longLink = getLongLinkByLinkPO(params);

        fslItem.setLongLink(longLink);
        fastShortLinkService.insert(fslItem);
        params.setShortLink(fslItem.getShortLink());

        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 新浪微博-渠道设置
    public MethodVO updateLinkWeibo(FastLinkPO params) {
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        // 设置推广链接
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
        }
        String promoteLink = getPromoteLinkByLinkPO(params);
        params.setPromoteLink(promoteLink);

        // 设置短连接
        FastShortLinkPO fslItem = new FastShortLinkPO();
        fslItem.setCreatorId(params.getCreatorId());
        String longLink = getLongLinkByLinkPO(params);

        fslItem.setLongLink(longLink);
        fastShortLinkService.insert(fslItem);
        params.setShortLink(fslItem.getShortLink());

        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 哔哩哔哩-渠道设置
    public MethodVO updateLinkBilibili(FastLinkPO params) {
        // TODO:
        // 设置外链标题
        FastDramaVO fdVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (fdVO != null) {
            params.setLinkTitle(fdVO.getDramaName());
        } else {
            params.setLinkTitle("短剧剧场");
        }
        // 设置小程序appid
        params.setMiniAppId(fastMiniService.queryById(params.getMiniId()).getAppId());
        // 设置推广链接
        if (params.getSeriesNum() == null) {
            FastDramaSeriesPO fastDramaSeriesPO = fastDramaSeriesService.queryById(params.getSeriesId());
            params.setSeriesNum(fastDramaSeriesPO.getSeriesNum());
        }
        String promoteLink = getPromoteLinkByLinkPO(params);
        promoteLink += "&trackId=__TRACKID__&projectId=__UNITID__&promotionId=__CAMPAIGNID__&accountId=__ACCOUNTID__";
        params.setPromoteLink(promoteLink);

        // 设置短连接
        FastShortLinkPO fslItem = new FastShortLinkPO();
        fslItem.setCreatorId(params.getCreatorId());
        String longLink = getLongLinkByLinkPO(params);

        fslItem.setLongLink(longLink);
        fastShortLinkService.insert(fslItem);
        params.setShortLink(fslItem.getShortLink());

        params.setUpdateTime(DateUtil.getNowDate());
        if (fastLinkMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }


    /**
     * 短剧推广-推广链接-复制链接
     */
    public String copyLink(Integer id) {
        FastLinkPO params = new FastLinkPO();
        params.setId(id);
        FastLinkPO fastLink = queryById(params);

        // 根据应用类型和投放媒体类型，生成链接信息
        // 应用类型: 1-微信小程序 2-微信H5
        switch (fastLink.getAppType()) {
            case 1:// 1-微信小程序
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                        return getLinkToutiao(fastLink);
                    case 2:// 2-adq
                        return getLinkAdq(fastLink);
                    case 3:// 3-mp
                        return getLinkMp(fastLink);
                    case 4:// 4-百度
                        return getLinkBaidu(fastLink);
                    case 5:// 5-快手
                        return getLinkKuaishou(fastLink);
                    case 6:// 6-新浪微博
                        return getLinkWeibo(fastLink);
                    case 7:// 7-B站
                        return getLinkBilibili(fastLink);
                    default:
                        break;
                }
                break;
            case 2:// 2-抖音小程序
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                        return getLinkTiktok(fastLink);
                    default:
                        break;
                }
                break;
            case 3:// 3-H5站
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                    default:
                        break;
                }
                break;
            case 4:// 4-快手小程序
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 5:// 5-快手
                        return getLinkKuaishou(fastLink);
                    default:
                        break;
                }
                break;
            case 5:// 5-快应用
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 2:// 2-adq
                        return getLinkAdqQuick(fastLink);
                    case 4:// 4-百度
                        return getLinkBaiduQuick(fastLink);
                    default:
                        break;
                }
                break;
            case 6:// 6-支付宝
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                        return getLinkToutiao(fastLink);
                    default:
                        break;
                }
                break;
            default:
                break;

        }
        return null;
    }

    /**
     * 头条复制链接
     *
     * @param fastLink
     * @return
     */
    public String getLinkToutiao(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 内部推广链接
        String promote_link = "推广链接:" + "\n" + fastLink.getPromoteLink() + "\n";
        // 外部推广链接
        String normal_link = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normal_link = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));
        //        String monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL;
        String monitorVersionStr = "(1.0账户)";
        if (fastLink.getAdvVersion() != null && fastLink.getAdvVersion() == 2) {
//            monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL_V2;
            monitorVersionStr = "(2.0账户)";
        }
        // 短连接
        String long_link = "长连接：" + "\n" + normal_link + "\n";
        String short_link = "短连接：" + "\n" + fastLink.getShortLink() + "\n";
        String monitor_link = "监测链接" + monitorVersionStr + "：" + getMonitorUrl(fastLink);
        // 复制链接
        return promote_link + short_link + long_link + monitor_link;
    }

    /**
     * 获取监测链接
     * 1=头条;2=ADQ;3=MP;4=百度;5=快手;6=新浪微博
     */
    public String getMonitorUrl(FastLinkPO fastLink) {
        String monitorUrl = "";
        if (fastLink.getAdvMediaId() == 1) {
            if (fastLink.getAdvVersion() != null && fastLink.getAdvVersion() == 2) {
                // 巨量的监测链接 v2
                monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL_V2;
            } else {
                // 巨量的监测链接 v1
                monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL;
            }
        } else if (fastLink.getAdvMediaId() == 2) {
            // adq
            if (fastLink.getAppType() == 5) {
                monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_ADQ_QUICK_3V_URL;
            } else {
                monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_ADQ_3V_URL;
            }
        } else if (fastLink.getAdvMediaId() == 4) {
            if (fastLink.getAppType() == 5) {
                // 快应用
                monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_BAIDU_QUICK_URL;
            } else {
                monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_BAIDU_URL;
            }
        } else if (fastLink.getAdvMediaId() == 5) {
            // 快手
            monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_KUAISHOU_URL;
        } else if (fastLink.getAdvMediaId() == 6) {
            // 微博
            monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_WEIBO_URL;
        } else if (fastLink.getAdvMediaId() == 7) {
            // B站
            monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_BILIBILI_URL;
        }
        monitorUrl = String.format(monitorUrl, fastLink.getOfficialId(), fastLink.getId());
        return monitorUrl;
    }

    public String getLinkWeibo(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 内部推广链接
        String promote_link = "推广链接:" + "\n" + fastLink.getPromoteLink() + "\n";
        // 外部推广链接
        String normal_link = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normal_link = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));
        // 短连接
        String long_link = "长连接：" + "\n" + normal_link + "\n";
        String short_link = "短连接：" + "\n" + fastLink.getShortLink() + "\n";
        String monitor_link = "监测链接" + getMonitorUrl(fastLink);
        // 复制链接
        String link = promote_link + short_link + long_link + monitor_link;
        return link;
    }

    public String getLinkBilibili(FastLinkPO fastLink) {
        // 内部推广链接
        String promote_link = "推广链接:" + "\n" + fastLink.getPromoteLink() + "\n";
        // 外部推广链接
        String jump_url = "小程序跳转链接：" + "\n" + getJumpWechatMiniUrl(fastLink) + "\n";
        String monitor_link = "监测链接" + getMonitorUrl(fastLink);
        // 复制链接
        String link = promote_link + jump_url + monitor_link;
        return link;
    }

    public String getJumpWechatMiniUrl(FastLinkPO link) {

        // 查询关联小程序
        FastMiniVO miniVO = fastMiniService.queryInfoByRedis(link.getMiniId());
        StringBuffer sb = new StringBuffer();
        sb.append("weixin://dl/business/?appid=");
        sb.append(miniVO.getAppId());
        sb.append("&path=pages/play/play&query=");
        // 组织自定义参数
        StringBuffer query = new StringBuffer();
        String promoteLink = link.getPromoteLink();
        String[] params = promoteLink.split("\\?");
        if (params.length > 1) {
            query.append(params[1]);
        }
        query.append("&trackId=__TRACKID__&projectId=__UNITID__&promotionId=__CAMPAIGNID__&accountId=__ACCOUNTID__");
        try {
            sb.append(URLEncoder.encode(query.toString(), StandardCharsets.UTF_8));
        } catch (Exception e) {

        }
        return sb.toString();
    }


    /**
     * 快手
     *
     * @param fastLink
     * @return
     */
    public String getLinkKuaishou(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 内部推广链接
        String promote_link = "推广链接:" + "\n" + fastLink.getPromoteLink() + "\n";
        // 外部推广链接
        String normal_link = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normal_link = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));
        //        String monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_KUAISHOU_URL;
        // 短连接
        String long_link = "长连接：" + "\n" + normal_link + "\n";
        String short_link = "短连接：" + "\n" + fastLink.getShortLink() + "\n";
        String monitor_link = "监测链接" + getMonitorUrl(fastLink);
        // 复制链接
        String link = promote_link + short_link + long_link + monitor_link;
        return link;
    }

    /**
     * 百度复制链接
     *
     * @param fastLink
     * @return
     */
    public String getLinkBaidu(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 内部推广链接
        String promote_link = "推广链接:" + "\n" + fastLink.getPromoteLink() + "\n";
        // 外部推广链接
        String normal_link = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normal_link = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_BAIDU_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));
        //        String monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_BAIDU_URL;
        // 短连接
        String long_link = "长连接：" + "\n" + normal_link + "\n";
        String short_link = "短连接：" + "\n" + fastLink.getShortLink() + "\n";
        String monitor_link = "监测链接：" + getMonitorUrl(fastLink);
        // 复制链接
        String link = promote_link + short_link + long_link + monitor_link;
        return link;
    }

    /**
     * 百度复制链接（快应用）
     *
     * @param fastLink
     * @return
     */
    public String getLinkBaiduQuick(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 内部推广链接
        String promote_link = "推广链接:" + "\n" + fastLink.getPromoteLink() + "\n";
        // 外部推广链接
        String normal_link = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normal_link = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_BAIDU_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));
        // 短连接
        String monitor_link = "监测链接：" + getMonitorUrl(fastLink);
        // 复制链接
        String link = promote_link + monitor_link;
        return link;
    }

    /**
     * 头条复制链接
     *
     * @param fastLink
     * @return
     */
    public String getLinkTiktok(FastLinkPO fastLink) {
//        String monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL;
//        if (fastLink.getAdvVersion() != null && fastLink.getAdvVersion() == 2) {
//            monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL_V2;
//        }
        String strBuffer = "AppID:" +
                fastLink.getMiniAppId() +
                "\n" +
                "启动页面:" +
                StaticYml.PROMOTE_TIKTOK_MINI_PAGE_PATH +
                "\n" +
                "推广链接(启动参数):" +
                addMoreParams(fastLink.getPromoteLink()) +
                "\n" +
                "监测链接(选填):" +
                getMonitorUrl(fastLink);
        return strBuffer;
    }


    public String addMoreParams(String paramsStr) {
        String path = "";
        if (paramsStr.contains("?")) {
            path = paramsStr.split("\\?")[0] + "?";
            paramsStr = paramsStr.split("\\?")[1];
        }
        if (!paramsStr.contains("dramaId")) {
            return path + paramsStr;
        }
        String[] paramsArray = paramsStr.split("&");
        Map<String, String> paramsMap = new HashMap<>();
        for (int i = 0; i < paramsArray.length; i++) {
            String[] pv = paramsArray[i].split("=");
            if (pv.length == 2) {
                paramsMap.put(pv[0], pv[1]);
            }
        }
        String dramaIdStr = paramsMap.get("dramaId");
        if (StrUtil.isEmpty(dramaIdStr)) {
            return path + paramsStr;
        }


        StringBuffer pBuf = new StringBuffer();
        Integer add = 0;
        for (String key : paramsMap.keySet()) {
            if (pBuf.length() > 0) {
                pBuf.append("&");
            }
            pBuf.append(key + "=" + paramsMap.get(key));
        }
        return path + pBuf;
    }

    /**
     * adq复制链接
     *
     * @param fastLink
     * @return
     */
    public String getLinkAdq(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 企微专用链接
        String promoteLink = fastLink.getPromoteLink();
        String qwLink = null;
        if (promoteLink != null) {
            String[] promoteLinkStrs = promoteLink.split("\\?");
            if (promoteLinkStrs != null && promoteLinkStrs.length > 1) {
                qwLink = "企微推广链接：" + "\n" + promoteLinkStrs[0] + ".html?" + promoteLinkStrs[1] + "\n";
            }
        }
        // 内部通用推广链接
        String innerPromoteLink = "通用推广链接:" + "\n" + fastLink.getPromoteLink() + "\n";
        // 外部推广链接
        String normalLink = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normalLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));
        // 短连接
        String longLink = "长连接：" + "\n" + normalLink + "\n";
        String shortLink = "短连接：" + "\n" + fastLink.getShortLink() + "\n";

        String minitorLink = "监测链接：\n" + getMonitorUrl(fastLink);
        // 复制链接
        String backBuf = qwLink +
                innerPromoteLink +
                shortLink +
                longLink +
                minitorLink;
        return backBuf;
    }

    /**
     * adq复制链接（快应用）
     *
     * @param fastLink
     * @return
     */
    public String getLinkAdqQuick(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 内部通用推广链接
        String innerPromoteLink = "通用推广链接:" + "\n" + fastLink.getPromoteLink() + "\n";
        // 外部推广链接
        String normalLink = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normalLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));
        // 短连接
//        String longLink = "长连接：" + "\n" + normalLink + "\n";
//        String shortLink = "短连接：" + "\n" + fastLink.getShortLink() + "\n";

        String minitorLink = "监测链接：\n" + getMonitorUrl(fastLink);
        // 复制链接
        String backBuf = innerPromoteLink +
//        backBuf.append(shortLink);
//        backBuf.append(longLink);
                minitorLink;
        return backBuf;
    }

    /**
     * mp复制链接
     *
     * @param fastLink
     * @return
     */
    public String getLinkMp(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 企微专用链接
        String promoteLink = fastLink.getPromoteLink();
        String qwLink = null;
        if (promoteLink != null) {
            String[] promoteLinkStrs = promoteLink.split("\\?");
            if (promoteLinkStrs != null && promoteLinkStrs.length > 1) {
                qwLink = "企微推广链接：" + "\n" + promoteLinkStrs[0] + ".html?" + promoteLinkStrs[1] + "\n";
            }
        }
        // 内部通用推广链接
        String innerPromoteLink = "通用推广链接:" + "\n" + fastLink.getPromoteLink() + "\n";
        // 外部推广链接
        String normalLink = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normalLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));
        // 短连接
        String longLink = "长连接：" + "\n" + normalLink + "\n";
        String shortLink = "短连接：" + "\n" + fastLink.getShortLink() + "\n";
//        String monitor_link = "监测链接：" + String.format(StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL, fastLink.getOfficialId(), fastLink.getId());
        // 复制链接
        String link = qwLink + innerPromoteLink + shortLink + longLink;
        return link;
    }

    /**
     * 短剧推广-推广链接-添加后获得3个链接
     */
    public HashMap getAddLink(Integer id) {
        FastLinkPO params = new FastLinkPO();
        params.setId(id);
        FastLinkPO fastLink = queryById(params);

        // 根据应用类型和投放媒体类型，生成链接信息
        // 应用类型: 1-微信小程序 2-微信H5
        switch (fastLink.getAppType()) {
            case 1:// 1-微信小程序
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                        return getAddLinkToutiao(fastLink);
                    case 2:// 2-adq
                        return getAddLinkAdq(fastLink);
                    case 3:// 3-mp
                        return getAddLinkMp(fastLink);
                    case 6:// 6-新浪微博
                        return getAddLinkWeibo(fastLink);
                    case 7:// 7-B站
                        return getAddLinkBilibili(fastLink);
                    default:
                        break;
                }
                break;
            case 2:// 2-抖音小程序
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                        return getAddLinkTiktok(fastLink);
                    default:
                        break;
                }
                break;
            case 3:// 3-H5站
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 1-头条
                    default:
                        break;
                }
                break;
            case 5:// 5-快应用
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 2:// 2-adq
                        return getAddLinkAdqQuick(fastLink);
                    default:
                        break;
                }
                break;
            case 6:// 6-支付宝小程序
                switch (fastLink.getAdvMediaId()) {
                    // 广告媒体：1-头条；2-mp
                    case 1:// 2-adq
                        return getAddLinkToutiao(fastLink);
                    default:
                        break;
                }
                break;
            default:
                break;

        }
        return null;
    }

    // 获得推广链接-头条(添加链接时弹窗需要)
    public HashMap getAddLinkMp(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 企微专用链接
        String promoteLink = fastLink.getPromoteLink();
        String qwLink = null;
        if (promoteLink != null) {
            String[] promoteLinkStrs = promoteLink.split("\\?");
            if (promoteLinkStrs != null && promoteLinkStrs.length > 1) {
                qwLink = "企微推广链接：" + "\n" + promoteLinkStrs[0] + ".html?" + promoteLinkStrs[1] + "\n";
            }
        }
        // 内部通用推广链接
        String innerPromoteLink = "通用推广链接:" + "\n" + fastLink.getPromoteLink() + "&#\n";
        // 外部推广链接
        String normalLink = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normalLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));

//        String long_link = "长连接：" + "\n" + normal_link + "\n";
        String shortLink = "短连接：" + "\n" + fastLink.getShortLink() + "\n";
//        String monitor_link = "监测链接：" + String.format(StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL, fastLink.getOfficialId(), fastLink.getId());


        // 复制链接
        String link = qwLink + innerPromoteLink + shortLink;

        HashMap hashMap = new HashMap<>();
        hashMap.put("in_promote_link", normalLink);
        hashMap.put("mini_link", fastLink.getPromoteLink());
        hashMap.put("promote_link", link);

        return hashMap;
    }

    // 获得推广链接-adq(添加链接时弹窗需要)
    public HashMap getAddLinkAdq(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 企微专用链接
        String promoteLink = fastLink.getPromoteLink();
        String qwLink = null;
        if (promoteLink != null) {
            String[] promoteLinkStrs = promoteLink.split("\\?");
            if (promoteLinkStrs != null && promoteLinkStrs.length > 1) {
                qwLink = "企微推广链接：" + "\n" + promoteLinkStrs[0] + ".html?" + promoteLinkStrs[1] + "\n";
            }
        }
        // 内部通用推广链接
        String innerPromoteLink = "通用推广链接:" + "\n" + fastLink.getPromoteLink() + "&#\n";
        // 外部推广链接
        String normalLink = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normalLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));

//        String long_link = "长连接：" + "\n" + normal_link + "\n";
        String shortLink = "短连接：" + "\n" + fastLink.getShortLink() + "\n";
//        String monitor_link = "监测链接：" + String.format(StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL, fastLink.getOfficialId(), fastLink.getId());


        // 复制链接
        String link = qwLink + innerPromoteLink + shortLink;

        HashMap hashMap = new HashMap<>();
        hashMap.put("in_promote_link", normalLink);
        hashMap.put("mini_link", fastLink.getPromoteLink());
        hashMap.put("promote_link", link);

        return hashMap;
    }

    // 获得推广链接-adq(添加链接时弹窗需要)-快应用
    public HashMap getAddLinkAdqQuick(FastLinkPO fastLink) {
        // 拼接投放用链接
        String promoteLink = fastLink.getPromoteLink();
        String qwLink = null;
        if (promoteLink != null) {
            String[] promoteLinkStrs = promoteLink.split("\\?");
            if (promoteLinkStrs != null && promoteLinkStrs.length > 1) {
                qwLink = "企微推广链接：" + "\n" + promoteLinkStrs[0] + ".html?" + promoteLinkStrs[1] + "\n";
            }
        }
        // 内部通用推广链接
        String innerPromoteLink = "通用推广链接:" + "\n" + fastLink.getPromoteLink() + "&#\n";
        // 外部推广链接
        String normalLink = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normalLink = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));

//        String long_link = "长连接：" + "\n" + normal_link + "\n";
        String shortLink = "短连接：" + "\n" + fastLink.getShortLink() + "\n";
//        String monitor_link = "监测链接：" + String.format(StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL, fastLink.getOfficialId(), fastLink.getId());


        // 复制链接
        String link = qwLink + innerPromoteLink + shortLink;

        HashMap hashMap = new HashMap<>();
        hashMap.put("in_promote_link", normalLink);
        hashMap.put("mini_link", fastLink.getPromoteLink());
        hashMap.put("promote_link", link);

        return hashMap;
    }

    // 获得头条推广链接-mp(添加链接时弹窗需要)
    public HashMap getAddLinkTiktok(FastLinkPO fastLink) {
        // 拼接投放用链接
        HashMap hashMap = new HashMap<>();
        hashMap.put("AppID", fastLink.getMiniAppId());
        hashMap.put("startPath", StaticYml.PROMOTE_TIKTOK_MINI_PAGE_PATH);
        hashMap.put("startParams", addMoreParams(fastLink.getPromoteLink()));
        return hashMap;
    }

    // 获得头条推广链接-mp(添加链接时弹窗需要)
    public HashMap getAddLinkToutiao(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 内部推广链接
        String promote_link = "推广链接:" + "\n" + fastLink.getPromoteLink() + "&#\n";
        // 外部推广链接
        String normal_link = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normal_link = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));
        // 短连接
        String long_link = "长连接：" + "\n" + normal_link + "\n";
        String short_link = "短连接：" + "\n" + fastLink.getShortLink() + "\n";
//        String monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL;
        String monitorVersionStr = "(1.0账户)";
        if (fastLink.getAdvVersion() != null && fastLink.getAdvVersion() == 2) {
//            monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_URL_V2;
            monitorVersionStr = "(2.0账户)";
        }
        String monitor_link = "监测链接" + monitorVersionStr + "：" + getMonitorUrl(fastLink);
        // 复制链接
        String link = promote_link + short_link + long_link + monitor_link;

        HashMap hashMap = new HashMap<>();
        hashMap.put("in_promote_link", normal_link);
        hashMap.put("mini_link", fastLink.getPromoteLink());
        hashMap.put("promote_link", link);

        return hashMap;
    }

    // 获得头条推广链接-mp(添加链接时弹窗需要)
    public HashMap getAddLinkWeibo(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 内部推广链接
        String promote_link = "推广链接:" + "\n" + fastLink.getPromoteLink() + "&#\n";
        // 外部推广链接
        String normal_link = null;
        // 获得小程序名
        FastMiniPO fastMiniPO = new FastMiniPO();
        fastMiniPO.setAppId(fastLink.getMiniAppId());
        fastMiniPO = fastMiniService.queryOne(fastMiniPO);
        normal_link = String.format(StaticVar.PROMOTE_MINI_DOMAIN_OUT_URL, fastLink.getMiniAppId(), fastLink.getOfficialId(), fastMiniService.queryOne(fastMiniPO).getMiniName(), URLEncoder.encode(fastLink.getPromoteLink(), StandardCharsets.UTF_8));
        // 短连接
        String long_link = "长连接：" + "\n" + normal_link + "\n";
        String short_link = "短连接：" + "\n" + fastLink.getShortLink() + "\n";
//    	String monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_WEIBO_URL;
        String monitor_link = "监测链接" + getMonitorUrl(fastLink);
        // 复制链接
        String link = promote_link + short_link + long_link + monitor_link;

        HashMap hashMap = new HashMap<>();
        hashMap.put("in_promote_link", normal_link);
        hashMap.put("mini_link", fastLink.getPromoteLink());
        hashMap.put("promote_link", link);

        return hashMap;
    }

    // 获得头条推广链接-mp(添加链接时弹窗需要)
    public HashMap getAddLinkBilibili(FastLinkPO fastLink) {
        // 拼接投放用链接
        // 内部推广链接
        String promote_link = "推广链接:" + "\n" + fastLink.getPromoteLink() + "&#\n";
        // 外部推广链接
        String jump_link = "小程序跳转链接：" + "\n" + getJumpWechatMiniUrl(fastLink) + "\n";
//    	String monitorUrl = StaticVar.PROMOTE_MINI_DOMAIN_MONITOR_WEIBO_URL;
        String monitor_link = "监测链接" + getMonitorUrl(fastLink);
        // 复制链接
        String link = promote_link + jump_link + monitor_link;

        HashMap hashMap = new HashMap<>();
        hashMap.put("in_promote_link", jump_link);
        hashMap.put("mini_link", fastLink.getPromoteLink());
        hashMap.put("promote_link", link);

        return hashMap;
    }

    /**
     * 推广链接（免费短剧）列表
     *
     * @param params
     * @param pageVO
     * @return
     */
    public ResultVO<?> getFreePageList(FastLinkQueryVO params, PageVO pageVO) {
        if (!StrUtil.isEmpty(params.getQueryCreateTime())) {
            String[] queryTime = params.getQueryCreateTime().split(" - ");
            params.setBeginTime(queryTime[0] + " 00:00:00");
            params.setEndTime(queryTime[1] + " 23:59:59");
        }
        if (!StrUtil.isEmpty(params.getQueryName())) {
            params.setLinkName(params.getQueryName());
        }
        if (StrUtil.isInteger(params.getQueryName())) {
            params.setId(Integer.parseInt(params.getQueryName()));
        }
        params.setStatisDate(DateUtil.format09(DateUtil.beginOfDay()));
        params.setBeginStatis(DateUtil.format07(DateUtil.beginOfDay()));
        params.setEndStatis(DateUtil.format07(DateUtil.endOfDay()));
        startPage(pageVO);
        List<FastLinkQueryVO> fastLinkQueryList = fastLinkMapper.queryFreeLinkList(params);
        // 查询广告收入
        String linkIds = fastLinkQueryList.stream()
                .map(FastLinkQueryVO::getId).distinct()
                .map(String::valueOf).reduce((a, b) -> a + "," + b)
                .orElse("");
        Integer numDayAll = 0;// 新增用户数累计
        Integer numDayRechAll = 0;// 新增充值用户累计
        BigDecimal moneyDayAll = BigDecimal.ZERO;// 新增充值累计
        BigDecimal moneyAllAll = BigDecimal.ZERO;// 累计充值累计
        BigDecimal adIncomeAll = BigDecimal.ZERO;
        BigDecimal totalIncomeAll = BigDecimal.ZERO;


        for (FastLinkQueryVO cur : fastLinkQueryList) {
            if (cur.getLinkType() != null && cur.getLinkType() == 3) {
                // 挂载
                cur.setNumDay(cur.getColorMemberDay().intValue());
                cur.setNumAll(cur.getColorMemberAll().intValue());
            }
            cur.setEncryptionId(encode(cur.getId()));
            // 相关配置
            StringBuffer sb = new StringBuffer();
            sb.append("回传规则：");
            if (cur.getBackType() == 1) {
                sb.append("全局规则");
            } else {
                sb.append("自定义规则");
            }
            sb.append("\n");
            sb.append("充值模板：");
            if (cur.getPayType() == 1) {
                sb.append("全局模板");
            } else if (cur.getPayType() == 2) {
                sb.append("自定义模板");
            } else if (cur.getPayType() == 3) {
                sb.append("通用模板");
            }
            cur.setSetInfo(sb.toString());
            // 入口页面
            String inPage = "";
            if (StrUtil.isNotEmpty(cur.getDramaName()) && cur.getSeriesNum() != null) {
                cur.setSeriesName("第" + cur.getSeriesNum() + "集");
                inPage = cur.getDramaName() + "-第" + cur.getSeriesNum() + "集";
                cur.setSeriesName("第" + cur.getSeriesNum() + "集");
            }
            cur.setInPage(inPage);
            // pv、uv
//            JSONObject condition = new JSONObject();
//            condition.put("linkId", cur.getId() + "");// 查询列要在tablestore的多元索引中
//            IndexSearchReply<FastBackToutiaoPO> reply = TableStoreUtil.andQueryCount(tableStoreProperties.getFasBackToutiaoTable(), tableStoreProperties.getFasBackToutiaoTableIndex(), condition, FastBackToutiaoPO.class);
//            cur.setPv(reply.totalCount());
            Long pvCount = fastStatisLinkPvMapper.queryLinkPVCount(cur.getId());
            cur.setPv(pvCount);
            cur.setUv(cur.getNumAll());
            // 累计收入=累计广告收入+累计充值收入
            BigDecimal totalIncome = DoubleUtil.addB(defaultIfNull(cur.getAdIncomeAll(), BigDecimal.ZERO),
                    defaultIfNull(cur.getTotalRechargeMoney(), BigDecimal.ZERO));
            // 利润=广告总收入-投放消耗总成本
            BigDecimal income = DoubleUtil.subB(totalIncome, defaultIfNull(cur.getCostAll(), BigDecimal.ZERO));
            // 回报率(ROI)=（广告总收入➗投放总消耗）*100%
            BigDecimal returnRatio = DoubleUtil.mulB(
                    DoubleUtil.divB4Zero(totalIncome,
                            defaultIfNull(cur.getCostAll(), BigDecimal.ZERO)),
                    new BigDecimal("100"));
            // 累计回传率=该渠道下已回传用户数➗累计新增用户数
            BigDecimal totalBackRatio = BigDecimal.ZERO;
            if (cur.getNumAll() > 0) {
                totalBackRatio = DoubleUtil.mulB(DoubleUtil.divB(cur.getBackUserNum(),
                                new BigDecimal(cur.getNumAll())),
                        new BigDecimal("100"));
            }
            cur.setTotalIncome(totalIncome);
            cur.setIncome(income);
            cur.setReturnRatio(returnRatio);
            cur.setTotalBackRatio(totalBackRatio);
            setGlobalNum(cur, null);
            if (cur.getLinkType() == 3) {
                // 添加审核id
                cur.setPromoteLink(addMoreParams(cur.getPromoteLink()));
            }
            numDayAll += cur.getNumDay();
            numDayRechAll += cur.getRechargeMemberNum();
            moneyDayAll = DoubleUtil.addB(moneyDayAll, cur.getRechargeMoney());
            moneyAllAll = DoubleUtil.addB(moneyAllAll, cur.getTotalRechargeMoney());
            if (cur.getAdIncomeAll() != null) {
                adIncomeAll = DoubleUtil.addB(adIncomeAll, cur.getAdIncomeAll());
            }
            totalIncomeAll = DoubleUtil.addB(totalIncomeAll, cur.getTotalIncome());
        }
        Map<String, Object> results = getPageListData(fastLinkQueryList, pageVO);
        results.put("numDayAll", numDayAll);
        results.put("numDayRechAll", numDayRechAll);
        results.put("moneyDayAll", moneyDayAll);
        results.put("moneyAllAll", moneyAllAll);
        results.put("adIncomeAll", adIncomeAll);
        results.put("totalIncome", totalIncomeAll);
        return ResultVO.success(results);
    }

    public String convertAppType(Integer appType) {
        // 应用类型:1=微信小程序 2=抖音小程序;3=H5;4=快手;5=快应用
        String mediaName = "";
        if (appType == 1) {
            mediaName = "微小";
        } else if (appType == 2) {
            mediaName = "抖小";
        } else if (appType == 3) {
            mediaName = "H5";
        } else if (appType == 4) {
            mediaName = "快小";
        } else if (appType == 5) {
            mediaName = "快应用";
        } else if (appType == MiniTypeEnum.ALI_MINI.index) {
            mediaName = MiniTypeEnum.ALI_MINI.name;
        }
        return mediaName;
    }
}
