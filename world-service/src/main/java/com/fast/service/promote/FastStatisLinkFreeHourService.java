/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.constant.StaticStr;
import com.fast.mapper.promote.FastStatisLinkFreeHourMapper;
import com.fast.po.promote.FastStatisLinkFreeHourPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastStatisLinkFreeHourService extends BaseService {

    @Autowired
    private FastStatisLinkFreeHourMapper statisLinkFreeHourMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStatisLinkFreeHourPO queryById(FastStatisLinkFreeHourPO params) {
        return statisLinkFreeHourMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastStatisLinkFreeHourPO queryById(Integer id) {
        return statisLinkFreeHourMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStatisLinkFreeHourPO queryOne(FastStatisLinkFreeHourPO params) {
        return statisLinkFreeHourMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStatisLinkFreeHourPO> queryList(FastStatisLinkFreeHourPO params) {
        return statisLinkFreeHourMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastStatisLinkFreeHourPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastStatisLinkFreeHourPO> list = statisLinkFreeHourMapper.queryList(params);
        for (FastStatisLinkFreeHourPO cur : list) {

        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastStatisLinkFreeHourPO params) {
        return statisLinkFreeHourMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastStatisLinkFreeHourPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (statisLinkFreeHourMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastStatisLinkFreeHourPO> list) {
        if (statisLinkFreeHourMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastStatisLinkFreeHourPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (statisLinkFreeHourMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public void setFreeHourData(String statDateStr) {
        Date nowDate = DateUtil.getNowDate();
        if (StrUtil.isEmpty(statDateStr)) {
            statDateStr = DateUtil.format09(nowDate);
        }
        FastStatisLinkFreeHourPO po = new FastStatisLinkFreeHourPO();
        po.setStatDateStr(statDateStr);
        List<FastStatisLinkFreeHourPO> data = statisLinkFreeHourMapper.queryStatData(po);

        if (CollUtil.hasContent(data)) {
            for (FastStatisLinkFreeHourPO val : data) {
                val.setBackRate(DoubleUtil.divB4Zero(val.getRechargeNewBack(), val.getRechargeNew()));
                val.setCreateTime(nowDate);
                val.setUpdateTime(nowDate);
            }
            statisLinkFreeHourMapper.insertUpdateBatch(data);
        }

        List<FastStatisLinkFreeHourPO> list = statisLinkFreeHourMapper.queryList(po);
        if (CollUtil.isEmpty(list)) {
            return;
        }

        for (FastStatisLinkFreeHourPO val : list) {
            int hour = DateUtil.getHourByDate(val.getStatDate());
            List<FastStatisLinkFreeHourPO> hourList = statisLinkFreeHourMapper.queryStatHour(val);
            Map<Integer, FastStatisLinkFreeHourPO> hourMap = new HashMap<>();
            if (CollUtil.hasContent(hourList)) {
                for (FastStatisLinkFreeHourPO hourVal : hourList) {
                    hourMap.put(hourVal.getH(), hourVal);
                }
                StringBuilder h24Back = new StringBuilder();
                StringBuilder h24Money = new StringBuilder();
                for (int i = 0; i <= 23; i++) {
                    if (h24Back.length() > 0) {
                        h24Back.append(",");
                    }
                    if (h24Money.length() > 0) {
                        h24Money.append(",");
                    }
                    if (i < hour) {
                        h24Back.append(0);
                        h24Money.append(0);
                        continue;
                    }
                    if (hourMap.containsKey(i)) {
                        h24Back.append(hourMap.get(i).getRechargeNewBack());
                        h24Money.append(hourMap.get(i).getMoneyHour());
                    } else {
                        h24Back.append(0);
                        h24Money.append(0);
                    }
                }
                val.setBackUsers24h(h24Back.toString());
                val.setMoneyUsers24h(h24Money.toString());
                val.setUpdateTime(nowDate);
                statisLinkFreeHourMapper.updateById(val);
            }
        }
    }
}
