/*
 * Powered By fast.up
 */
package com.fast.service.promote;

import com.fast.mapper.promote.FastStatisLinkFreeDayMapper;
import com.fast.po.promote.FastStatisLinkFreeDayPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastStatisLinkFreeDayService extends BaseService {

    @Autowired
    private FastStatisLinkFreeDayMapper statisLinkFreeDayMapper;

    /**
     * 通过id查询单个对象
     */
    public FastStatisLinkFreeDayPO queryById(FastStatisLinkFreeDayPO params) {
        return statisLinkFreeDayMapper.queryById(params);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastStatisLinkFreeDayPO queryOne(FastStatisLinkFreeDayPO params) {
        return statisLinkFreeDayMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastStatisLinkFreeDayPO> queryList(FastStatisLinkFreeDayPO params) {
        return statisLinkFreeDayMapper.queryList(params);
    }

    public void setFreeDayData(Date d) {
        Date nowTime = DateUtil.getNowDate();
        String statDateStr = DateUtil.format09(d);
        FastStatisLinkFreeDayPO po = new FastStatisLinkFreeDayPO();
        po.setStatDateStr(statDateStr);
        List<FastStatisLinkFreeDayPO> dataRec = statisLinkFreeDayMapper.queryStatRecharge(po);

        Map<Integer, FastStatisLinkFreeDayPO> dataMap = new HashMap<>();
        if (CollUtil.hasContent(dataRec)) {
            for (FastStatisLinkFreeDayPO val : dataRec) {
                val.setStatDate(DateUtil.format09(statDateStr));
                val.setRegisterNew(0);
                val.setRegisterUser(0);
                dataMap.put(val.getLinkId(), val);
            }
        }

        po.setFeeFlag(2);
        List<FastStatisLinkFreeDayPO> dataReg = statisLinkFreeDayMapper.queryStatRegister(po);
        if (CollUtil.hasContent(dataReg)) {
            for (FastStatisLinkFreeDayPO val : dataReg) {
                if (dataMap.containsKey(val.getLinkId())) {
                    dataMap.get(val.getLinkId()).setRegisterNew(val.getRegisterNew());
                    dataMap.get(val.getLinkId()).setRegisterUser(val.getRegisterUser());
                } else {
                    val.setStatDate(DateUtil.format09(statDateStr));
                    val.setNumRecharge(0);
                    val.setRechargeNewBack(0);
                    val.setClickAdUser(0);
                    val.setRechargeNew(0);
                    val.setMoneyDayNew(BigDecimal.ZERO);
                    val.setMoneyDay(BigDecimal.ZERO);
                    val.setAdIncomeShowNum(0);
                    val.setAdIncomeMemberNum(0);
                    val.setAdShowNum(0);
                    val.setAdShowMemberNum(0);
                    dataMap.put(val.getLinkId(), val);
                }
            }
        }
        if (CollUtil.hasContent(dataMap)) {
            List<FastStatisLinkFreeDayPO> data = new ArrayList<>();
            for (FastStatisLinkFreeDayPO val : dataMap.values()) {
                BigDecimal rechargeNewBack = BigDecimal.valueOf(val.getRechargeNewBack().longValue());
                BigDecimal rechargeNew = BigDecimal.valueOf(val.getRechargeNew().longValue());
                BigDecimal registerNew = BigDecimal.valueOf(val.getRegisterNew().longValue()); // 新染色用户
                val.setKeyActionRate(DoubleUtil.divB4Zero(rechargeNewBack, registerNew, 4));
                val.setBackRate(DoubleUtil.divB4Zero(rechargeNewBack, rechargeNew, 4));
                val.setArpu(DoubleUtil.divB4Zero(val.getMoneyDayNew(), val.getRegisterNew()));
                val.setArppu(DoubleUtil.divB4Zero(val.getMoneyDayNew(), val.getRechargeNew()));
                val.setCreateTime(nowTime);
                data.add(val);
            }
            statisLinkFreeDayMapper.insertUpdateBatch(data);
        }
    }
}
