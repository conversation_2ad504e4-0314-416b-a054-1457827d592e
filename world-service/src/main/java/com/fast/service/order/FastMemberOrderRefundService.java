/*
 * Powered By fast.up
 */
package com.fast.service.order;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.constant.StaticVarField;
import com.fast.enums.ContentTypeEnum;
import com.fast.mapper.member.*;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.order.FastMemberOrderDailyMoneyMapper;
import com.fast.mapper.order.FastMemberOrderRefundMapper;
import com.fast.po.member.*;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.order.FastMemberOrderDailyMoneyPO;
import com.fast.po.order.FastMemberOrderRefundPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.*;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderRefundService extends BaseService {

    @Autowired
    private FastMemberOrderRefundMapper fastMemberOrderRefundMapper;
    @Autowired
    private FastMemberOrderRechargeMapper fastMemberOrderRechargeMapper;
    @Autowired
    private FastMemberAccountMapper fastMemberAccountMapper;
    @Autowired
    private FastMemberCoinChangeMapper fastMemberCoinChangeMapper;
    @Autowired
    private FastMemberOrderDailyMoneyMapper fastMemberOrderDailyMoneyMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastMemberOrderConsumeMapper fastMemberOrderConsumeMapper;
    @Autowired
    private FastMiniMapper fastMiniMapper;
    @Autowired
    private FastMemberOrderRechargeContractMapper fastMemberOrderRechargeContractMapper;
    @Autowired
    private FastMemberOrderContractRecordMapper fastMemberOrderContractRecordMapper;

    @Slave
    public ResultVO queryOrderDetailRefund(FastMemberOrderRefundPO item) {
        // 判断是否已经存在退费
        FastMemberOrderRefundPO morParam = new FastMemberOrderRefundPO();
        morParam.setOutTransId(item.getOutTransId());
        FastMemberOrderRefundPO morPO = fastMemberOrderRefundMapper.queryOne(morParam);
        if (morPO != null) {
            return ResultVO.error(StaticStr.DUPLICATE_REFUND_NOT_ALLOWED);
        }
        // 查询订单
        FastMemberOrderRechargePO morgParam = new FastMemberOrderRechargePO();
        morgParam.setOutTransIdOnly(item.getOutTransId());
        morgParam.setContentType(item.getContentType());
        FastMemberOrderRechargePO morgPO = fastMemberOrderRechargeMapper.queryOneDetail(morgParam);
        if (morgPO == null) {
            return ResultVO.error(StaticStr.ORDER_NOT_EXIST);
        }
        if (morgPO.getState() != 1) {
            return ResultVO.error(StaticStr.ORDER_HAS_NOT_PAID);
        }
        if (morgPO.getOrderType() == 2) {
            // vip退费，验证是否最后一笔订单
            FastMemberOrderRechargePO morgParam2 = new FastMemberOrderRechargePO();
            morgParam2.setMemberId(morgPO.getMemberId());
            morgParam2.setOrderType(2);
            morgParam2.setState(1);
            morgParam2.setContentType(item.getContentType());
            List<FastMemberOrderRechargePO> morgList = fastMemberOrderRechargeMapper.queryList(morgParam2);
            if (!morgPO.getId().equals(morgList.get(0).getId())) {
//                return ResultVO.error("非最后一笔vip订单，不能退费");
            }
        }
        morgPO = getRechargeMore(morgPO);
        Map<String, Object> results = ResultVO.getMap();
        results.put("detail", morgPO);
        return ResultVO.success("ok", results);
    }

    /**
     * 计算订单相关属性
     *
     * @param morgPO
     * @return
     */
    private FastMemberOrderRechargePO getRechargeMore(FastMemberOrderRechargePO morgPO) {
        Long memberId = morgPO.getMemberId();
        Long orderId = morgPO.getId();
        // 计算字段
        if (morgPO.getOrderType() == 1) {
            // 金币充值，剩余币数
            // 查询用户账户
            FastMemberAccountPO maParam = new FastMemberAccountPO();
            maParam.setMemberId(memberId);
            FastMemberAccountPO maPO = fastMemberAccountMapper.queryOne(maParam);
            // 查询全部充值金币订单
            FastMemberOrderRechargePO morgParam2 = new FastMemberOrderRechargePO();
            morgParam2.setMemberId(memberId);
            morgParam2.setOrderType(1);
            morgParam2.setState(1);
            morgParam2.setContentType(morgPO.getContentType());
            List<FastMemberOrderRechargePO> morgList = fastMemberOrderRechargeMapper.queryList(morgParam2);

            // 查询全部手动赠送、扣币记录
            FastMemberCoinChangePO ccParam = new FastMemberCoinChangePO();
            ccParam.setMemberId(memberId);
            ccParam.setContentType(morgPO.getContentType());
            List<FastMemberCoinChangePO> ccList = fastMemberCoinChangeMapper.queryList(ccParam);

            Integer coinRechargeAll = maPO.getCoinRechargeAll();// 充值金币总数
            Integer coinGiveAll = maPO.getCoinGiveAll(); // 赠送金币总数
            Integer coinRechargeRemain = maPO.getCoinRechargeRemain();// 金币充值剩余
            Integer coinGiveRemain = maPO.getCoinGiveRemain();// 金币赠送剩余

            Integer coinConsumeGive = coinGiveAll - coinGiveRemain;// 赠送总消耗
            Integer coinConsumeRecharge = coinRechargeAll - coinRechargeRemain; // 充值总消耗

            Integer changeByMan = 0; // 手动操作剩余币
            for (FastMemberCoinChangePO cc : ccList) {
                if (cc.getChangeType() == 1) { // 赠送
                    changeByMan = changeByMan + cc.getCoinChange();
                } else if (cc.getChangeType() == 2) {
                    changeByMan = changeByMan - cc.getCoinChange();
                }
            }
            coinGiveAll = coinGiveAll - changeByMan; // 去除手动，剩余赠币全部来自充值

            Integer consumeGive = 0;// 订单赠送消耗
            Integer consumeRecharge = 0;// 订单充值消耗
            // 开始轮询订单
            for (int i = morgList.size() - 1; i > -1; i--) {
                FastMemberOrderRechargePO orPO = morgList.get(i);
                if (orderId.equals(orPO.getId())) {
                    if (coinConsumeGive > orPO.getCoinGive()) {
                        consumeGive = orPO.getCoinGive();
                    } else if (coinConsumeGive > 0) {
                        consumeGive = coinConsumeGive;
                    }
                    if (coinConsumeRecharge > orPO.getCoinRecharge()) {
                        consumeRecharge = orPO.getCoinRecharge();
                    } else if (coinConsumeRecharge > 0) {
                        consumeRecharge = coinConsumeRecharge;
                    }
                    break;
                } else {
                    consumeGive = consumeGive - orPO.getCoinGive();
                    consumeRecharge = consumeRecharge - orPO.getCoinRecharge();
                }
            }
            Integer leftCoinAll = morgPO.getCoinGive() + morgPO.getCoinRecharge() - consumeGive - consumeRecharge;
            if (leftCoinAll < 0) {
                leftCoinAll = 0;
            }
            morgPO.setConsumeCoinAll(consumeGive + consumeRecharge);// 消耗总币数
            morgPO.setConsumeCoinRecharge(consumeRecharge);// 消耗充值币
            morgPO.setLeftCoinAll(leftCoinAll);// 剩余总币数
            morgPO.setLeftCoinRecharge(morgPO.getCoinRecharge() - consumeRecharge);
            morgPO.setLeftCoinGive(morgPO.getCoinGive() - consumeGive);
            BigDecimal consumeMoney = DoubleUtil.divB(BigDecimal.valueOf(consumeRecharge), BigDecimal.valueOf(100));
            BigDecimal refundMoney = morgPO.getMoneyRecharge().subtract(consumeMoney);
            morgPO.setConsumeMoney(consumeMoney);// 消耗金额
            morgPO.setMoneyRefund(refundMoney);// 应退金额
        } else if (morgPO.getOrderType() == 2) {
            // vip充值，剩余天数
            FastMemberOrderDailyMoneyPO odmParam = new FastMemberOrderDailyMoneyPO();
            odmParam.setRechargeId(orderId);
            odmParam.setContentType(morgPO.getContentType());
            FastMemberOrderDailyMoneyPO odmPO = fastMemberOrderDailyMoneyMapper.queryById(odmParam);
            if (odmPO != null) {
                morgPO.setConsumeDayAll(odmPO.getValidDay() - odmPO.getRemainDay());// 消耗总天数
                morgPO.setLeftDayAll(odmPO.getRemainDay());// 剩余天数
                morgPO.setConsumeMoney(odmPO.getMoneyRecharge().subtract(odmPO.getRemainMoney()));// 消耗金额
                morgPO.setMoneyRefund(odmPO.getRemainMoney());// 应退金额
            }
        }
        return morgPO;
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRefundPO queryById(FastMemberOrderRefundPO item) {
        return fastMemberOrderRefundMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRefundPO queryById(Integer id) {
        return fastMemberOrderRefundMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderRefundPO queryOne(FastMemberOrderRefundPO item) {
        return fastMemberOrderRefundMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderRefundPO> queryList(FastMemberOrderRefundPO item) {
        return fastMemberOrderRefundMapper.queryList(item);
    }

    public ResultVO<?> queryPageList(FastMemberOrderRefundPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderRefundPO> list = fastMemberOrderRefundMapper.queryList(params);
        for (FastMemberOrderRefundPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        // 查询总价格
        BigDecimal refundAll = fastMemberOrderRefundMapper.queryRefundAllList(params);
        results.put("refundAll", refundAll);
        return ResultVO.success(results);
    }

    /**
     * 导出
     *
     * @param sessionVO
     * @param item
     * @return
     */
    public ResultVO<?> exportList(SessionVO sessionVO, FastMemberOrderRefundPO item) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_REFUND_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        item.setLimitExport(StaticVar.MILLION);
        List<FastMemberOrderRefundPO> list = fastMemberOrderRefundMapper.queryList(item);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberOrderRefundPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getOutTransId());
            CollUtil.addNoRepeat(rowHeadNames, "订单交易单号");

            row.add(cur.getMemberId());
            CollUtil.addNoRepeat(rowHeadNames, "用户ID");

            if (cur.getOrderType() == 1) {
                row.add(cur.getMoneyRecharge() + "元充值");
                CollUtil.addNoRepeat(rowHeadNames, "商品名称");
            } else if (cur.getOrderType() == 2) {
                row.add(cur.getTitle());
                CollUtil.addNoRepeat(rowHeadNames, "商品名称");
            }

            row.add(cur.getDramaName());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "名称");

            if (cur.getOrderType() == 1) {
                row.add("K币充值");
            } else if (cur.getOrderType() == 2) {
                row.add("VIP卡充值");
            }
            CollUtil.addNoRepeat(rowHeadNames, "充值类型");

            // 4=H5线上;5=微信线上;6=抖音线上;7=快手线上;8=微信虚拟
//            if (cur.getPayForm() == 4) {
//                row.add("H5线上");
//            } else if (cur.getPayForm() == 5) {
//                row.add("微信线上");
//            } else if (cur.getPayForm() == 6) {
//                row.add("抖音线上");
//            } else if (cur.getPayForm() == 7) {
//                row.add("快手线上");
//            } else if (cur.getPayForm() == 8) {
//                row.add("微信虚拟");
//            }
            if (cur.getPayForm() == 8) {
                row.add("虚拟支付");
            } else {
                row.add("普通支付");
            }
            CollUtil.addNoRepeat(rowHeadNames, "支付类型");

            row.add(cur.getMoneyRefund());
            CollUtil.addNoRepeat(rowHeadNames, "本次退费金额");

            if (cur.getState() == 0) {
                row.add("退款失败");
            } else if (cur.getState() == 1) {
                row.add("退款成功");
            } else if (cur.getState() == 2) {
                row.add("退款中");
            }
            CollUtil.addNoRepeat(rowHeadNames, "退款状态");

            if (cur.getOrderType() == 1) {
                row.add(cur.getCoinUsedRecharge());
            } else {
                row.add(cur.getDayUsedRecharge());
            }
            CollUtil.addNoRepeat(rowHeadNames, "已使用情况(充值)");

            if (cur.getOrderType() == 1) {
                row.add(cur.getCoinUsedGive());
            } else {
                row.add("/");
            }
            CollUtil.addNoRepeat(rowHeadNames, "已使用情况(赠送)");

            if (cur.getOrderType() == 1) {
                row.add(cur.getCoinCut());
            } else {
                row.add(cur.getDayCut());
            }
            CollUtil.addNoRepeat(rowHeadNames, "扣除资产数");

            row.add(DateUtil.format07(cur.getPayTime()));
            CollUtil.addNoRepeat(rowHeadNames, "订单交易时间");

            row.add(DateUtil.format07(cur.getRefundTime()));
            CollUtil.addNoRepeat(rowHeadNames, "退费操作时间");

            row.add(cur.getRetailName());
            CollUtil.addNoRepeat(rowHeadNames, "所属分销商");

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "应用");

            row.add(cur.getOfficialName());
            CollUtil.addNoRepeat(rowHeadNames, "公众号");

            row.add(cur.getLinkId());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接id");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接名称");

            if (StrUtil.isNotEmpty(cur.getAid())) {
                row.add(cur.getAid() + "​");        // 零宽度字符	
            } else {
                row.add("");
            }
            CollUtil.addNoRepeat(rowHeadNames, "计划");

            row.add(cur.getPromotionId());
            CollUtil.addNoRepeat(rowHeadNames, "广告ID");

            row.add(cur.getRemark());
            CollUtil.addNoRepeat(rowHeadNames, "备注");

            row.add(cur.getUserName());
            CollUtil.addNoRepeat(rowHeadNames, "操作人");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = ContentTypeEnum.getName(sessionVO.getContentType()) + "数据明细表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderRefundPO item) {
        return fastMemberOrderRefundMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderRefundPO item) {
        Integer refundByUrl = item.getRefundByUrl() == null ? 0 : item.getRefundByUrl(); // 远程接口退款
        FastMemberOrderRechargePO morgParam = new FastMemberOrderRechargePO();
        morgParam.setId(item.getOrderId());
        morgParam.setContentType(item.getContentType());
        FastMemberOrderRechargePO morgPO = fastMemberOrderRechargeMapper.queryOneDetail(morgParam);
        morgPO = getRechargeMore(morgPO);
        if (morgPO.getOrderType() == 1) {
            item.setCoinUsedRecharge(morgPO.getConsumeCoinRecharge());// 已经使用充值金币
            item.setCoinUsedGive(morgPO.getConsumeCoinAll() - morgPO.getConsumeCoinRecharge());// 已经使用赠送金币
            item.setCoinCut(morgPO.getLeftCoinAll());// 扣除金币
        } else if (morgPO.getOrderType() == 2) {
            item.setDayUsedRecharge(morgPO.getConsumeDayAll());// 已经使用充值天数
            item.setDayUsedGive(0);
            item.setDayCut(morgPO.getLeftDayAll());// 扣除天数
        }
        // 查询签约记录
        FastMemberOrderRechargeContractPO fastMemberOrderRechargeContractPO = fastMemberOrderRechargeContractMapper.queryById(morgPO.getId());
        if (Objects.nonNull(fastMemberOrderRechargeContractPO)) {
            log.info("存在签约记录,签约订单更新为失效:{}", morgPO.getId());
            Long contractId = fastMemberOrderRechargeContractPO.getContractId();
            FastMemberOrderContractRecordPO fastMemberOrderContractRecordPO = fastMemberOrderContractRecordMapper.queryById(contractId);
            if (fastMemberOrderContractRecordPO.getContractState() == 0
                    || fastMemberOrderContractRecordPO.getValidFlag() == 0
                    || fastMemberOrderContractRecordPO.getContractState() == 2) {
                log.info("存在签约记录已解约,退费订单不更新签约状态:{}", morgPO.getId());
            } else {
                FastMemberOrderContractRecordPO updateContract = new FastMemberOrderContractRecordPO();
                updateContract.setId(contractId);
                updateContract.setValidFlag(0);
                updateContract.setContractState(2);
                updateContract.setUpdateTime(DateUtil.getNowDate());
                updateContract.setMiniId(fastMemberOrderContractRecordPO.getMiniId());
                updateContract.setAuthOrderId(fastMemberOrderContractRecordPO.getAuthOrderId());
                updateContract.setContractOrderId(fastMemberOrderContractRecordPO.getContractOrderId());
                updateContract.setRemark("退费解约");
                fastMemberOrderContractRecordMapper.updateById(updateContract);
            }
        }

        FastMiniPO miniPO = fastMiniMapper.queryById(morgPO.getMiniId());
        if (miniPO.getType() == 2 && refundByUrl != 1) {
            transactionRollBack();
            return MethodVO.error("退款失败，请勾选抖音退款");
        }
        if (miniPO.getType() == 4 && refundByUrl != 2) {
            transactionRollBack();
            return MethodVO.error("退款失败，请勾选快手退款");
        }
        if (refundByUrl > 1) {
            item.setState(2); // 退款中
            // 查询是否正确
//        	if(refundByUrl == 1 && miniPO.getType() != 2){
//        		return MethodVO.error("非抖音小程序订单，退款失败");
//        	}
            if (refundByUrl == 2 && miniPO.getType() != 4) {
                return MethodVO.error("非快手小程序订单，退款失败");
            }
        } else {
            item.setState(1); // 默认退款成功
        }
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setRefundTime(nowTime);
        if (fastMemberOrderRefundMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        if (refundByUrl == 0 || refundByUrl == 1) {
            // 0微信2抖音 更新用户剩余资产
            return updateMemberLeftProperty(item, morgPO);
        }
        return null;
    }

    // 更新用户剩余资产
    public MethodVO updateMemberLeftProperty(FastMemberOrderRefundPO item, FastMemberOrderRechargePO morgPO) {

        // 查询用户账户
        FastMemberAccountPO maParam = new FastMemberAccountPO();
        maParam.setMemberId(morgPO.getMemberId());
        FastMemberAccountPO maPO = fastMemberAccountMapper.queryOne(maParam);
        // 扣除用户的账号资产
        if (item.getCoinCut() != null && item.getCoinCut() > 0) {
            // 扣除金币
            // 更新用户的账号
            Integer rechargeRemain = maPO.getCoinRechargeRemain() - morgPO.getLeftCoinRecharge();
            if (rechargeRemain < 0) {
                rechargeRemain = 0;
            }
            Integer giveRemain = maPO.getCoinGiveRemain() - morgPO.getLeftCoinGive();
            if (giveRemain < 0) {
                giveRemain = 0;
            }
            maPO.setCoinRechargeRemain(rechargeRemain);
            maPO.setCoinGiveRemain(giveRemain);
            fastMemberAccountMapper.updateById(maPO);
            // 更新消费记录
            FastMemberOrderConsumePO mocPO = new FastMemberOrderConsumePO();
            mocPO.setMemberId(morgPO.getMemberId());
            mocPO.setCoinRechargeConsume(maPO.getCoinRechargeRemain() - rechargeRemain);
            mocPO.setCoinGiveConsume(maPO.getCoinGiveRemain() - giveRemain);
            mocPO.setRetailId(morgPO.getRetailId());
            mocPO.setOfficialId(morgPO.getOfficialId());
            mocPO.setMiniId(morgPO.getMiniId());
            mocPO.setCreateTime(DateUtil.getNowDate());
            if (fastMemberOrderConsumeMapper.insertSelective(mocPO) < 1) {
                transactionRollBack();
                return MethodVO.error("添加消费记录失败");
            }
        }
        if (item.getDayCut() != null && item.getDayCut() > 0) {
            // 扣除天数
            Date deadTime = DateUtil.addDays(maPO.getDeadTime(), -1 * morgPO.getLeftDayAll());
            maPO.setDeadTime(deadTime);
            fastMemberAccountMapper.updateById(maPO);
            // 更新vip订单
            FastMemberOrderDailyMoneyPO modParam = new FastMemberOrderDailyMoneyPO();
            modParam.setRechargeId(morgPO.getId());
            modParam.setRemainMoney(BigDecimal.ZERO);
            modParam.setRemainDay(0);
            fastMemberOrderDailyMoneyMapper.updateById(modParam);
        }
        return MethodVO.success();
    }

    // 抖音担保交易退款回调
    public JSONObject ttRefundCallBack(JSONObject params, int type) {
        actionLogService.log("tittok_refund", "退款回调：" + params.toJSONString());
        // 判断签名数据是否正确
        if (ComUtil.ttCallbackSign(params).equals(params.getString(StaticVarField.F_MSG_SIGNATURE))) {
            actionLogService.log("tittok_refund", "签名校验成功");
            JSONObject msgObj = params.getJSONObject("msg");
            actionLogService.log("tittok_refund", "抖音退款回调返回内容" + msgObj.toJSONString());
            Long orderId = msgObj.getLong("cp_refundno");
            FastMemberOrderRechargePO morPO = fastMemberOrderRechargeMapper.queryById(orderId);
            FastMemberOrderRefundPO rpParam = new FastMemberOrderRefundPO();
            rpParam.setOrderId(orderId);
            FastMemberOrderRefundPO rpPO = fastMemberOrderRefundMapper.queryOne(rpParam);
            if ("SUCCESS".equals(msgObj.getString("status"))) {
                rpParam.setState(1);
            } else {
                String message = msgObj.getString("message");
                rpParam.setReason(message);
                rpParam.setState(0);
            }
            rpParam.setId(rpPO.getId());
            if (fastMemberOrderRefundMapper.updateById(rpParam) == 0) {
                actionLogService.log("tittok_refund", "抖音退款状态更新失败");
            }
            updateMemberLeftProperty(rpPO, morPO);
            JSONObject resObj = new JSONObject();
            resObj.put("err_no", 0);
            resObj.put("err_tips", "success");
            return resObj;
        } else {
            log.error("抖音签名错误");
        }
        return null;
    }

    // 快手担保交易退款回调
    public JSONObject ksRefundCallBack(JSONObject bodyObj) {
        JSONObject dataObj = bodyObj.getJSONObject("data");
        Long orderId = dataObj.getLong("out_refund_no");
        FastMemberOrderRechargePO morPO = fastMemberOrderRechargeMapper.queryById(orderId);
        FastMemberOrderRefundPO rpParam = new FastMemberOrderRefundPO();
        rpParam.setOrderId(orderId);
        FastMemberOrderRefundPO rpPO = fastMemberOrderRefundMapper.queryOne(rpParam);
        if ("SUCCESS".equals(dataObj.getString("status"))) {
            rpParam.setUpdateTime(DateUtil.getNowDate());
            rpParam.setState(1);
        } else {
            String message = dataObj.getString("ks_refund_fail_reason");
            rpParam.setReason(message);
            rpParam.setState(0);
            rpParam.setUpdateTime(DateUtil.getNowDate());
        }
        rpParam.setId(rpPO.getId());
        if (fastMemberOrderRefundMapper.updateById(rpParam) == 0) {
            actionLogService.log("kuaishou_refund", "快手退款状态更新失败");
        }
        updateMemberLeftProperty(rpPO, morPO);
        JSONObject resObj = new JSONObject();
        resObj.put("result", 1);
        resObj.put("message_id", bodyObj.getString("message_id"));
        return resObj;
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderRefundPO> list) {
        if (fastMemberOrderRefundMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderRefundPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMemberOrderRefundMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
