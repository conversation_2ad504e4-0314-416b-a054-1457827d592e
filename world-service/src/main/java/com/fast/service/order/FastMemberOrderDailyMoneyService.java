/*
 * Powered By fast.up
 */
package com.fast.service.order;

import com.fast.mapper.member.FastMemberOrderRechargeMapper;
import com.fast.mapper.order.FastMemberOrderDailyLogMapper;
import com.fast.mapper.order.FastMemberOrderDailyMoneyMapper;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.order.FastMemberOrderDailyLogPO;
import com.fast.po.order.FastMemberOrderDailyMoneyPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderDailyMoneyService extends BaseService {

    @Autowired
    private FastMemberOrderDailyMoneyMapper dailyMoneyMapper;
    @Autowired
    private FastMemberOrderDailyLogMapper dailyLogMapper;
    @Autowired
    private FastMemberOrderRechargeMapper rechargeMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderDailyMoneyPO queryById(FastMemberOrderDailyMoneyPO item) {
        return dailyMoneyMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderDailyMoneyPO queryById(Integer id) {
        return dailyMoneyMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderDailyMoneyPO queryOne(FastMemberOrderDailyMoneyPO item) {
        return dailyMoneyMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderDailyMoneyPO> queryList(FastMemberOrderDailyMoneyPO item) {
        return dailyMoneyMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderDailyMoneyPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderDailyMoneyPO> list = dailyMoneyMapper.queryList(item);
        for (FastMemberOrderDailyMoneyPO cur : list) {
            cur.setEncryptionId(encode(cur.getRechargeId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderDailyMoneyPO item) {
        return dailyMoneyMapper.queryCount(item);
    }

    /**
     * 更新-订单同步-废弃
     */
    @Transactional(rollbackFor = Exception.class)
    public void auto() {
        Date nowDate = DateUtil.getNowDate();
        long maxId = defaultIfNull(dailyMoneyMapper.queryMaxRechargeId(), 0L);
        FastMemberOrderRechargePO rechargePO = new FastMemberOrderRechargePO();
        rechargePO.setState(1);
        rechargePO.setOrderType(2);
        rechargePO.setMinId(++maxId);
        rechargePO.setLimitExport(2000);
        List<FastMemberOrderRechargePO> list = rechargeMapper.queryListSimpleAsc(rechargePO);
        do {
            if (CollUtil.isEmpty(list)) {
                return;
            }
            for (FastMemberOrderRechargePO recharge : list) {
                if (recharge.getValidUnit() == 0) {
                    continue;
                }
                insertDailyMoney(nowDate, recharge);
            }
            maxId = defaultIfNull(dailyMoneyMapper.queryMaxRechargeId(), 0L);
            rechargePO.setMinId(++maxId);
            list = rechargeMapper.queryListSimpleAsc(rechargePO);
        } while (list.size() == 2000);
    }

    /**
     * 同步处理VIP订单
     *
     * @param nowDate
     * @param recharge
     */
    public void insertDailyMoney(Date nowDate, FastMemberOrderRechargePO recharge) {
        FastMemberOrderDailyMoneyPO moneyPO = new FastMemberOrderDailyMoneyPO();
        BeanUtils.copyProperties(recharge, moneyPO);
        moneyPO.setRechargeId(recharge.getId());
        moneyPO.setStartDate(recharge.getPayTime());
        // 单位:1=周;2=月;3=年;4=日
        switch (recharge.getValidUnit()) {
            case 1: {
                moneyPO.setEndDate(DateUtil.addWeeks(recharge.getPayTime(), recharge.getValidDate()));
                break;
            }
            case 2: {
                moneyPO.setEndDate(DateUtil.addMonths(recharge.getPayTime(), recharge.getValidDate()));
                break;
            }
            case 3: {
                moneyPO.setEndDate(DateUtil.addYears(recharge.getPayTime(), recharge.getValidDate()));
                break;
            }
            case 4: {
                moneyPO.setEndDate(DateUtil.addDays(recharge.getPayTime(), recharge.getValidDate()));
                break;
            }
        }
        int day = DateUtil.daysBetweenUp(moneyPO.getStartDate(), moneyPO.getEndDate());
        moneyPO.setDailyMoney(DoubleUtil.divB(moneyPO.getMoneyRecharge(), day));
        moneyPO.setRemainMoney(moneyPO.getMoneyRecharge());
        moneyPO.setValidDay(day);
        moneyPO.setRemainDay(day);
        moneyPO.setCreateTime(nowDate);
        dailyMoneyMapper.insertSelective(moneyPO);

        // 当天消耗一天的金额
        consumeOneDay(nowDate, moneyPO);
    }

    /**
     * 同步处理VIP订单
     *
     * @param nowDate
     * @param rechargeId
     */
    @Async
    public void insertDailyMoney(Date nowDate, Long rechargeId) {
        sleep(50);
        FastMemberOrderRechargePO recharge = rechargeMapper.queryById(rechargeId);
        if (recharge.getOrderType() == 2) {
            insertDailyMoney(nowDate, recharge);
        }
    }

    /**
     * 更新-按天滚动
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoLogDaily() {
        Date nowDate = DateUtil.getNowDate();
        FastMemberOrderDailyMoneyPO rechargePO = new FastMemberOrderDailyMoneyPO();
        rechargePO.setLimitExport(2000);
        rechargePO.setRemainDayMin(0);
        rechargePO.setLastConsumeDate(DateUtil.getYesterdayDate());
        List<FastMemberOrderDailyMoneyPO> list = dailyMoneyMapper.queryListSimpleAsc(rechargePO);
        do {
            for (FastMemberOrderDailyMoneyPO money : list) {
                consumeOneDay(nowDate, money);
            }
            list = dailyMoneyMapper.queryListSimpleAsc(rechargePO);
        } while (list.size() == 2000);
    }

    /**
     * 消耗一天的金额
     *
     * @param nowDate
     * @param money
     */
    private void consumeOneDay(Date nowDate, FastMemberOrderDailyMoneyPO money) {
        FastMemberOrderDailyLogPO logPO = new FastMemberOrderDailyLogPO();
        BeanUtils.copyProperties(money, logPO);
        logPO.setConsumeMoney(money.getDailyMoney());
        logPO.setConsumeDate(nowDate);
        logPO.setCreateTime(nowDate);
        logPO.setContentType(money.getContentType());

        // 如果是最后一天, 则全部消费
        if (DateUtil.format06Int(DateUtil.addDays(nowDate, 1)) == DateUtil.format06Int(nowDate)) {
            logPO.setConsumeMoney(money.getRemainMoney());
            money.setRemainMoney(BigDecimal.ZERO);
            money.setRemainDay(0);
        } else {
            money.setRemainMoney(DoubleUtil.subB(money.getRemainMoney(), money.getDailyMoney()));
            money.setRemainDay(money.getRemainDay() - 1);
        }
        // 剩余金额不能小于0
        if (money.getRemainMoney().compareTo(BigDecimal.ZERO) < 0) {
            return;
        }
        // 剩余天数不能小于0
        if (money.getRemainDay() < 0) {
            return;
        }
        dailyLogMapper.insertSelective(logPO);

        // 剩余天数不能小于0
        if (money.getRemainDay() < 0) {
            money.setRemainDay(0);
        }
        money.setLastConsumeDate(nowDate);
        dailyMoneyMapper.updateRemainMoney(money);
    }

    /**
     * 更新-全量,可废弃
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoLog() {
        Date nowDate = DateUtil.getNowDate();
        long maxId = defaultIfNull(dailyLogMapper.queryMaxRechargeId(), 0L);
        FastMemberOrderDailyMoneyPO rechargePO = new FastMemberOrderDailyMoneyPO();
        rechargePO.setMinId(++maxId);
        rechargePO.setLimitExport(200);
        List<FastMemberOrderDailyMoneyPO> list = dailyMoneyMapper.queryListSimpleAsc(rechargePO);
        do {
            for (FastMemberOrderDailyMoneyPO money : list) {
                Date startDate = money.getStartDate();
                for (int i = 0; i < money.getValidDay(); i++) {
                    Date consumeDate = DateUtil.addDays(startDate, i);
                    if (consumeDate.compareTo(nowDate) > 0) {
                        break;
                    }
                    consumeOneDay(nowDate, money);
                }
            }
            maxId = defaultIfNull(dailyLogMapper.queryMaxRechargeId(), 0L);
            rechargePO.setMinId(++maxId);
            list = dailyMoneyMapper.queryListSimpleAsc(rechargePO);
        } while (list.size() == 200);
    }
}
