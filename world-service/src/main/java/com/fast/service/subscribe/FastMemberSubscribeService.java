/*
 * Powered By fast.up
 */
package com.fast.service.subscribe;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.mapper.member.FastMemberRecentMapper;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.subscribe.FastMemberSubscribeMapper;
import com.fast.mapper.subscribe.FastMiniSubscribeTemplateMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.member.FastMemberRecentPO;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.subscribe.FastMemberSubscribePO;
import com.fast.po.subscribe.FastMiniSubscribeTemplatePO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberSubscribeService extends BaseService {

    @Autowired
    private FastMemberSubscribeMapper fastMemberSubscribeMapper;
    @Autowired
    private FastMiniSubscribeTemplateMapper fastMiniSubscribeTemplateMapper;
    @Autowired
    private FastDramaMapper FastDramaMapper;
    @Autowired
    private FastMiniMapper fastMiniMapper;
    @Autowired
    private FastMemberRecentMapper fastMemberRecentMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberSubscribePO queryById(FastMemberSubscribePO item) {
        return fastMemberSubscribeMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberSubscribePO queryById(Integer id) {
        return fastMemberSubscribeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberSubscribePO queryOne(FastMemberSubscribePO item) {
        return fastMemberSubscribeMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberSubscribePO> queryList(FastMemberSubscribePO item) {
        return fastMemberSubscribeMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberSubscribePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberSubscribePO> list = fastMemberSubscribeMapper.queryList(item);
        for (FastMemberSubscribePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberSubscribePO item) {
        return fastMemberSubscribeMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberSubscribePO item) {
        if (StrUtil.isEmpty(item.getTypes())) {
            item.setTypes("1");
        }
        List<Integer> typeList = CollUtil.parseIntStr2List(item.getTypes());
        for (Integer type : typeList) {
            // 查询模板
            FastMiniSubscribeTemplatePO templateParam = new FastMiniSubscribeTemplatePO();
            templateParam.setMiniId(item.getMiniId());
            templateParam.setType(type);// 消息订阅
            FastMiniSubscribeTemplatePO templatePO = fastMiniSubscribeTemplateMapper.queryOne(templateParam);
            if (templatePO == null) {
                return MethodVO.error("模板不存在");
            }
            item.setTemplateId(templatePO.getId());
            // 查询判断是否已经存在
            FastMemberSubscribePO subParam = new FastMemberSubscribePO();
            subParam.setMemberId(item.getMemberId());
            subParam.setDramaId(item.getDramaId());
            subParam.setTemplateId(item.getTemplateId());
            FastMemberSubscribePO subPO = fastMemberSubscribeMapper.queryOne(subParam);
            Date nowTime = DateUtil.getNowDate();
            String key = StaticVar.MEMBER_SUBSCRIBE + item.getMemberId();
            if (subPO != null) {
                subPO.setState(1);
                subPO.setUpdateTime(nowTime);
                if (fastMemberSubscribeMapper.updateById(subPO) == 0) {
                    return MethodVO.error("订阅失败");
                }
                RedisUtil.del(key);
                return MethodVO.success();
            }
            item.setCreateTime(nowTime);
            item.setUpdateTime(nowTime);
            item.setState(1);
            if (fastMemberSubscribeMapper.insertSelective(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
            RedisUtil.del(key);
        }


        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberSubscribePO> list) {
        if (fastMemberSubscribeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberSubscribePO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMemberSubscribeMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    // 发送订阅消息
    @Transactional
    public void sendSubscribeMessageJob() {
        Double rancode = Math.random();
        log.info("开始执行订阅消息job:" + rancode);
        // 查询当前全部剧
        FastDramaPO dramaParam = new FastDramaPO();
        dramaParam.setUpdateState(1);// 连载
        dramaParam.setOpenState(1);// 开放
        dramaParam.setShelfState(1);// 上架
        dramaParam.setDelFlag(0);
        List<FastDramaPO> dramaList = FastDramaMapper.querySimpleList(dramaParam);
        if (dramaList != null && dramaList.size() > 0) {
            for (FastDramaPO drama : dramaList) {
                // 判断是否有更新
                String key = StaticVar.DRAMA_SUB + drama.getId();
                FastDramaPO dramaRedis = RedisUtil.getObject(key, FastDramaPO.class);
                RedisUtil.setObject(key, drama, RedisUtil.ONE_HOUR * 2);
                if (dramaRedis == null) {
                    continue;
                }
                if (!drama.getSeriesNumUpdate().equals(dramaRedis.getSeriesNumUpdate())) {
                    log.info("剧有更新，dramaId:" + drama.getId() + ",剧名:" + drama.getDramaName());
                    try {
                        int start = 0;
                        int limit = 500;
                        boolean hasMore = true;
                        Map<String, String> paramMap = new HashMap<String, String>();
                        while (hasMore) {
                            FastMemberSubscribePO subParam = new FastMemberSubscribePO();
                            subParam.setLimit(limit);
                            subParam.setStart(start);
                            subParam.setState(1);
                            subParam.setDramaId(drama.getId());
                            List<FastMemberSubscribePO> subList = fastMemberSubscribeMapper.queryJobList(subParam);
                            if (subList == null || subList.size() < limit) {
                                hasMore = false;
                            }
                            start += limit;
                            // 开始处理
                            for (FastMemberSubscribePO subPO : subList) {
                                String keyAppid = "appid_" + subPO.getTemplateId();
                                String keyWechatTemplateId = "templateid_" + subPO.getTemplateId();
                                String appid = paramMap.get(keyAppid);// appid
                                String wechatTemplateId = paramMap.get(keyWechatTemplateId);// template_id
                                if (StrUtil.isEmpty(appid) || StrUtil.isEmpty(wechatTemplateId)) {
                                    // 查询关联小程序
                                    FastMiniSubscribeTemplatePO temParam = new FastMiniSubscribeTemplatePO();
                                    temParam.setId(subPO.getTemplateId());
                                    FastMiniSubscribeTemplatePO templatePO = fastMiniSubscribeTemplateMapper.queryById(temParam);
                                    FastMiniPO miniParam = new FastMiniPO();
                                    miniParam.setId(templatePO.getMiniId());
                                    FastMiniPO miniPO = fastMiniMapper.queryById(miniParam);
                                    paramMap.put(keyAppid, miniPO.getAppId());
                                    paramMap.put(keyWechatTemplateId, templatePO.getWechatTemplateId());
                                    appid = miniPO.getAppId();
                                    wechatTemplateId = templatePO.getWechatTemplateId();
                                }
                                String openid = subPO.getOpenid();
                                // 查询用户信息openid,最近读的剧集
                                // 先查询30天的缓存
                                String keySub = StaticVar.MEMBER_RECENT_SERIESNUM + subPO.getMemberId() + "_" + drama.getId();
                                String seriesNumStr = RedisUtil.get(keySub);
                                Integer seriesNum = 0;
                                if (StrUtil.isNotEmpty(seriesNumStr)) {
                                    seriesNum = Integer.valueOf(seriesNumStr);
                                } else {
                                    // 查询实时数据
                                    FastMemberRecentPO recentParam = new FastMemberRecentPO();
                                    recentParam.setMemberId(subPO.getMemberId());
                                    recentParam.setDramaId(drama.getId());
                                    FastMemberRecentPO recentPO = fastMemberRecentMapper.queryOne(recentParam);
                                    if (recentPO != null) {
                                        seriesNum = recentPO.getSeriesNum();
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {

                    }
                }
            }
        }
        log.info("结束执行订阅消息job:" + rancode);
    }
}










