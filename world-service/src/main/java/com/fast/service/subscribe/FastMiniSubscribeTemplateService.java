/*
 * Powered By fast.up
 */
package com.fast.service.subscribe;

import com.fast.constant.StaticStr;
import com.fast.mapper.subscribe.FastMiniSubscribeTemplateMapper;
import com.fast.po.subscribe.FastMiniSubscribeTemplatePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMiniSubscribeTemplateService extends BaseService {

    @Autowired
    private FastMiniSubscribeTemplateMapper fastMiniSubscribeTemplateMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMiniSubscribeTemplatePO queryById(FastMiniSubscribeTemplatePO item) {
        return fastMiniSubscribeTemplateMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMiniSubscribeTemplatePO queryById(Integer id) {
        return fastMiniSubscribeTemplateMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMiniSubscribeTemplatePO queryOne(FastMiniSubscribeTemplatePO item) {
        return fastMiniSubscribeTemplateMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMiniSubscribeTemplatePO> queryList(FastMiniSubscribeTemplatePO item) {
        return fastMiniSubscribeTemplateMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMiniSubscribeTemplatePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMiniSubscribeTemplatePO> list = fastMiniSubscribeTemplateMapper.queryList(item);
        for (FastMiniSubscribeTemplatePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMiniSubscribeTemplatePO item) {
        return fastMiniSubscribeTemplateMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMiniSubscribeTemplatePO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMiniSubscribeTemplateMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMiniSubscribeTemplatePO> list) {
        if (fastMiniSubscribeTemplateMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMiniSubscribeTemplatePO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMiniSubscribeTemplateMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
