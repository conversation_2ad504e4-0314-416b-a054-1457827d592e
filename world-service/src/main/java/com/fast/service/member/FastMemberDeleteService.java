/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.RedisVar;
import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberDeleteMapper;
import com.fast.mapper.member.FastMemberMapper;
import com.fast.po.member.FastMemberDeletePO;
import com.fast.po.member.FastMemberPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberDeleteService extends BaseService {

    @Autowired
    private FastMemberDeleteMapper fastMemberDeleteMapper;
    @Autowired
    private FastMemberMapper fastMemberMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 通过id查询单个对象
     */
    public FastMemberDeletePO queryById(FastMemberDeletePO item) {
        return fastMemberDeleteMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberDeletePO queryById(Integer id) {
        return fastMemberDeleteMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberDeletePO queryOne(FastMemberDeletePO item) {
        return fastMemberDeleteMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberDeletePO> queryList(FastMemberDeletePO item) {
        return fastMemberDeleteMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberDeletePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberDeletePO> list = fastMemberDeleteMapper.queryList(item);
        for (FastMemberDeletePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }


    public ResultVO<?> exportList(SessionVO sessionVO, FastMemberDeletePO item) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_REFUND_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        item.setLimitExport(StaticVar.MILLION);
        List<FastMemberDeletePO> list = fastMemberDeleteMapper.queryList(item);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberDeletePO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getOpenid());
            CollUtil.addNoRepeat(rowHeadNames, "openid");

            row.add(cur.getId());
            CollUtil.addNoRepeat(rowHeadNames, "用户id");

            row.add(cur.getRetailName());
            CollUtil.addNoRepeat(rowHeadNames, "用户归属");

            row.add(cur.getOfficialName());
            CollUtil.addNoRepeat(rowHeadNames, "公众号");

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "归属应用");

            if (cur.getMiniType() == 1) {
                row.add("微信小程序");
            } else if (cur.getMiniType() == 2) {
                row.add("抖音小程序");
            } else if (cur.getMiniType() == 3) {
                row.add("H5");
            } else if (cur.getMiniType() == 4) {
                row.add("快手小程序");
            }
            CollUtil.addNoRepeat(rowHeadNames, "应用类型");

            row.add(cur.getRemark());
            CollUtil.addNoRepeat(rowHeadNames, "备注");

            row.add(cur.getUserName());
            CollUtil.addNoRepeat(rowHeadNames, "操作员");

            row.add(DateUtil.format07(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "操作时间");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "清除用户";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberDeletePO item) {
        return fastMemberDeleteMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberDeletePO item) {
        String key = StaticVar.MEMBER_DEL + item.getCreatorId();
        String cache = RedisUtil.get(key);
        Integer count = 0;
        if (StrUtil.isNotEmpty(cache)) {
            count = Integer.valueOf(cache);
        }
        count = count + 1;
        if (count > 50) {
            return MethodVO.error("删除失败，已达当日删除用户上限50人，联系管理员");
        }
        Long leftTime = (DateUtil.endOfDay().getTime() - DateUtil.getNowDate().getTime()) / 1000;
        RedisUtil.set(key, count.toString(), leftTime.intValue());// 缓存到今天晚上

        // 查询用户相关信息
        FastMemberPO mParam = new FastMemberPO();
        mParam.setMemberId(item.getId());
        mParam.setState(1);
        FastMemberPO memberPO = fastMemberMapper.queryOne(mParam);
        if (memberPO == null) {
            return MethodVO.error("用户不存在或已删除");
        }
        // 新增删除记录
        Date timeNow = DateUtil.getNowDate();
        item.setOpenid(memberPO.getOpenid());
        item.setCreateTime(timeNow);
        item.setMiniId(memberPO.getMiniId());
        item.setOfficialId(memberPO.getOfficialId());
        item.setRetailId(memberPO.getRetailId());
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberDeleteMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        // 更新用户的状态
        memberPO.setState(0);
        String openId = memberPO.getOpenid();
        if (memberPO.getType() == 2) {
            openId = openId.replace("_00", "#99");
        } else {
            openId = openId + "#d";
        }
        memberPO.setOpenid(openId);
        if (fastMemberMapper.updateById(memberPO) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        // 异步删除缓存
        log.info("删除用户的key：" + StaticVar.ACCESS_TOKEN_PRE + item.getId() + "_*");
        Set<String> tokenSet = RedisUtil.getLikeKeys(RedisVar.REDIS_TOKEN_DB, StaticVar.ACCESS_TOKEN_PRE + item.getId() + "_*");
        if (tokenSet != null && tokenSet.size() > 0) {
            for (String itemKey : tokenSet) {
                log.info("删除用户token:" + itemKey);
                RedisUtil.del(RedisVar.REDIS_TOKEN_DB, itemKey);
            }
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberDeletePO> list) {
        if (fastMemberDeleteMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberDeletePO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberDeleteMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
