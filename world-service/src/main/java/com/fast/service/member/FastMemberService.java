/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.MemberStateEnum;
import com.fast.enums.MiniTypeEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.drama.FastDramaSeriesMapper;
import com.fast.mapper.member.*;
import com.fast.mapper.mini.FastMiniSettingMapper;
import com.fast.mapper.setting.FastSettingRiskMapper;
import com.fast.mapper.subscribe.FastMemberSubscribeMapper;
import com.fast.po.drama.FastDramaSeriesPO;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.po.member.*;
import com.fast.po.mini.FastMiniMemberVersionPO;
import com.fast.po.mini.FastMiniRecommendPO;
import com.fast.po.mini.FastMiniSettingPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.po.setting.FastSettingRiskPO;
import com.fast.po.subscribe.FastMemberSubscribePO;
import com.fast.po.user.FastUserPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.FastCommonDictService;
import com.fast.service.common.FastTagService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.fee.FastFeeModelDetailService;
import com.fast.service.mini.FastMiniMemberVersionService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.mini.FastMiniSettingService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.retail.FastRetailService;
import com.fast.service.user.FastUserService;
import com.fast.utils.*;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.drama.FastDramaVO;
import com.fast.vo.member.MemberAccountVO;
import com.fast.vo.member.MemberDramaVO;
import com.fast.vo.member.MemberVO;
import com.fast.vo.mini.FastMiniVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberService extends BaseService {

    @Autowired
    private FastMemberAccountService accountService;
    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastMemberMapper memberMapper;
    @Autowired
    private FastMemberAddictionMapper fastMemberAddictionMapper;
    @Autowired
    private FastMemberRecentMapper fastMemberRecentMapper;
    @Autowired
    private FastMemberSubscribeMapper fastMemberSubscribeMapper;
    @Autowired
    private FastSettingRiskMapper fastSettingRiskMapper;
    @Autowired
    private FastMiniSettingMapper fastMiniSettingMapper;
    @Autowired
    private FastMemberOrderRechargeMapper fastMemberOrderRechargeMapper;
    @Autowired
    private FastMemberBlackMapper fastMemberBlackMapper;
    @Autowired
    private FastMemberRecentLogMapper fastMemberRecentLogMapper;
    @Autowired
    private FastDramaSeriesMapper fastDramaSeriesMapper;
    @Autowired
    private FastMemberDesktopMapper fastMemberDesktopMapper;
    @Autowired
    private FastTagService fastTagService;
    @Autowired
    private FastCommonDictService fastCommonDictService;
    @Autowired
    private FastMiniMemberVersionService memberVersionService;
    @Autowired
    private FastMiniSettingService miniSettingService;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMemberVipOnlineService fastMemberVipOnlineService;
    @Autowired
    private FastMemberWecomService fastMemberWecomService;
    @Autowired
    private FastRetailService fastRetailService;
    @Autowired
    private FastMemberWorkService fastMemberWorkService;
    @Autowired
    private FastMemberAwemeFollowService fastMemberAwemeFollowService;
    @Autowired
    private FastMemberDivideService fastMemberDivideService;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastUserService fastUserService;
    @Autowired
    private FastMemberOrderContractRecordService contractRecordService;
    @Autowired
    private FastFeeModelDetailService fastFeeModelDetailService;
    @Autowired
    private FastMemberAdmobLogService fastMemberAdmobLogService;

    /**
     * 查询会员可看剧列表
     */
    public ResultVO getMyDramaList(SessionVO sessionVO) {
        FastMiniVO miniPO = fastMiniService.queryInfoByRedis(sessionVO.getMiniId());
        if (miniPO == null) {
            return ResultVO.error(StaticStr.APP_NOT_EXIST);
        }
        // 查询当前设置
        FastMiniSettingPO params = new FastMiniSettingPO();
        params.setAppId(miniPO.getAppId());
        params.setMiniId(miniPO.getId());
        params.setCreatorId(0);
        params.setUpdatorId(0);
        params.setType(miniPO.getType());
        FastMiniMemberVersionPO versionPO = memberVersionService.queryInfoByRedis(sessionVO.getMemberId());
        FastMiniSettingPO settingPO = miniSettingService.queryInfoByRedisMember(params, versionPO, sessionVO);
        FastMiniRecommendPO recommendPO = settingPO.getRecommend();
        Map<String, Object> results = new HashMap<>();
        if (Objects.isNull(recommendPO)) {
            results.put("dramaList", new ArrayList<>());
            results.put("dramaCount", 0);
            log.error("推荐区配置为空miniId:{},memberId:{}", sessionVO.getMiniId(), sessionVO.getMemberId());
            return ResultVO.success(results);
        }
        List<FastDramaVO> dramaList = recommendPO.getDramaList();
        // 查询当前用户是否上线vip
        FastMemberVipOnlinePO onlinePO = fastMemberVipOnlineService.queryByIdRedis(sessionVO.getMemberId());
        if (onlinePO != null && onlinePO.getStatus() == 1) {
            // 过滤剧列表
            for (int i = dramaList.size() - 1; i >= 0; i--) {
                boolean removeFlag = false;
                Date releaseDate = dramaList.get(i).getReleaseDate();
                if (releaseDate == null) {
                    // 删除，剧上线日期为空
                    removeFlag = true;
                } else if (DateUtil.getNowDate().after(onlinePO.getVipStart()) && releaseDate.after(onlinePO.getOnlineDate())) {
                    // 删除，1:上线vip生效期内，2：剧发版日期在限制日期后
                    removeFlag = true;
                }
                if (removeFlag) {
                    dramaList.remove(i);
                }
            }
        }

        if (onlinePO != null && onlinePO.getVipEnd() != null && onlinePO.getVipEnd().before(DateUtil.getNowDate())) {
            // vip过期
            results.put("dramaList", new ArrayList<>());
            results.put("dramaCount", 0);
        } else {
            results.put("dramaList", dramaList);
            results.put("dramaCount", dramaList.size());
        }
        return ResultVO.success(results);
    }

    public ResultVO getCanRecharge(SessionVO sessionVO, Integer dramaId) {
        // 判断是苹果还是安卓
        Map<String, Object> results = ResultVO.getMap();
        if (sessionVO.getPhoneOs() != null && sessionVO.getPhoneOs() == 1) {
            results.put("recharge", 1);
            return ResultVO.success(results);
        }
        if (sessionVO.getMiniId() == null) {
            return ResultVO.error(StaticStr.APPID_CANNOT_BE_EMPTY);
        }
        FastMiniVO miniVO = fastMiniService.queryInfoByRedis(sessionVO.getMiniId());
        if (miniVO.getType() == 2) {
            // 抖音小程序判断是否支持支付
            return getCanRechargeTiktok(sessionVO, dramaId);
        } else {
            return getCanRechargeWechat(sessionVO, dramaId);
        }
    }

    /**
     * 获取ios用户是否可以充值（微小）
     *
     * @param sessionVO
     * @return
     */
    public ResultVO getCanRechargeWechat(SessionVO sessionVO, Integer dramaId) {
        Map<String, Object> results = ResultVO.getMap();
        Long memberId = sessionVO.getMemberId();
        Integer officialId = sessionVO.getOfficialId();
        // 验证是否黑名单用户
        String keyBlack = StaticVar.IOS_BLACK + memberId;
        String blackStr = RedisUtil.get(keyBlack);
        if (StrUtil.isEmpty(blackStr)) {
            // 库内查询状态
            FastMemberBlackPO blackParam = new FastMemberBlackPO();
            blackParam.setMemberId(memberId);
            blackParam.setDelFlag(0);
            FastMemberBlackPO blackPO = fastMemberBlackMapper.queryOne(blackParam);
            blackStr = blackPO == null ? "0" : "1";
            RedisUtil.set(keyBlack, blackStr, 60 * 60 * 24);
        }
        Integer recharge = 0;
        if ("1".equals(blackStr)) {
            // 黑名单直接返回不行
            results.put("recharge", recharge);
            return ResultVO.success(results);
        }
        // 不在黑名单，开始匹配后台配置的规则
        Integer enterType = sessionVO.getEnterType();
        if (enterType == null) {
            enterType = 2;// 正常不为空，为空时，默认为投放
        }
        Integer miniId = sessionVO.getMiniId();
        // 查询新配置规则
        String keySettingRisk = StaticVar.IOS_RISK + miniId + "_" + enterType;
        FastSettingRiskPO riskPO = null;
        String settingRiskStr = RedisUtil.get(keySettingRisk);
        if (StrUtil.isNotEmpty(settingRiskStr)) {
            riskPO = JsonUtil.toJavaObject(settingRiskStr, FastSettingRiskPO.class);
        }
        if (riskPO == null) {
            FastSettingRiskPO riskParam = new FastSettingRiskPO();
            riskParam.setMiniId(miniId);
            riskParam.setType(enterType);
            riskPO = fastSettingRiskMapper.queryOne(riskParam);
            if (riskPO != null) {
                RedisUtil.set(keySettingRisk, JsonUtil.toString(riskPO));
            }
        }
        if (riskPO == null) {
            // 未配置新规则，查询老的规则
            FastMiniSettingPO settingParam = new FastMiniSettingPO();
            settingParam.setMiniId(miniId);
            FastMiniSettingPO settingPO = fastMiniSettingMapper.queryOne(settingParam);
            if (settingPO == null) {
                return ResultVO.error(StaticStr.RULE_NOT_CONFIGURED);
            }
            if (enterType == 1 && settingPO.getOpenIosCharge1() == 1) {
                // 兜底
                recharge = DateUtil.getNowInHours(settingPO.getOpenIosTime1());
            } else if (enterType == 2 && settingPO.getOpenIosCharge2() == 1) {
                // 投放
                recharge = DateUtil.getNowInHours(settingPO.getOpenIosTime2());
            } else if (enterType == 3 && settingPO.getOpenIosCharge3() == 1) {
                // 公众号
                recharge = DateUtil.getNowInHours(settingPO.getOpenIosTime3());
            }
            results.put("recharge", recharge);
            return ResultVO.success(results);
        }
        // 根据新规则判定
        if (riskPO.getRechargeFlag() == 0) {
            // 充值关闭
            results.put("recharge", recharge);
            return ResultVO.success(results);
        }
        if (DateUtil.getNowInHours(riskPO.getRechargeTime()) == 0) {
            // 未在充值时间内
            results.put("recharge", recharge);
            return ResultVO.success(results);
        }
        if (riskPO.getRechargeTotalFlag() == 0 && riskPO.getRechargeOneFlag() == 0) {
            // 开关全部关闭，在时间范围内即可
            recharge = 1;
            results.put("recharge", recharge);
            return ResultVO.success(results);
        }
        // 缓存判断单剧是否通过
        String keyOnePass = StaticVar.IOS_ONE_PASS + dramaId + "_" + memberId;
        if (dramaId != null && dramaId > 0) {
            String onePassStr = RedisUtil.get(keyOnePass);
            if (StrUtil.isNotEmpty(onePassStr)) {
                recharge = 1;
                results.put("recharge", recharge);
                return ResultVO.success(results);
            }
        }
        // 缓存判断全部剧是否通过
        String keyTotalPass = StaticVar.IOS_TOTAL_PASS + memberId;
        String totalPassStr = RedisUtil.get(keyTotalPass);
        if (StrUtil.isNotEmpty(totalPassStr)) {
            recharge = 1;
            results.put("recharge", recharge);
            return ResultVO.success(results);
        }

        // 查询充值次数
        FastMemberOrderRechargePO rechargeParam = new FastMemberOrderRechargePO();
        rechargeParam.setMemberId(memberId);
        rechargeParam.setState(1);
        int rechargeCount = fastMemberOrderRechargeMapper.queryCount(rechargeParam); // 充值次数
        // 按剧查询总播放时长
        FastMemberRecentLogPO rlParam = new FastMemberRecentLogPO();
        rlParam.setMemberId(memberId);
        // 用户免费剧集播放总时长
        List<FastMemberRecentLogPO> rlList = fastMemberRecentLogMapper.querySumSecondsList(rlParam);
        // 查询剧的总时长
        List<FastDramaSeriesPO> seriesList = null;
        String keyDramaSeriesTime = StaticVar.IOS_DRAMA_FREE_TIME_LIST;
        String dramaSeriesStr = RedisUtil.get(keyDramaSeriesTime);
        if (StrUtil.isNotEmpty(dramaSeriesStr)) {
            seriesList = JsonUtil.toList(dramaSeriesStr, FastDramaSeriesPO.class);
        } else {
            seriesList = queryFreeSecondsList(sessionVO, officialId, keyDramaSeriesTime);
        }
        Map<Integer, Integer> baseSeriesMap = new HashMap<>();// 剧默认时长map
        Map<Integer, Integer> officialSeriesMap = new HashMap<>(); // 剧按公众号时长map
        Map<Integer, Integer> linkSeriesMap = new HashMap<>(); // 剧按渠道时长map
        for (FastDramaSeriesPO ds : seriesList) {
            baseSeriesMap.put(ds.getDramaId(), ds.getBaseSeriesTime());
            officialSeriesMap.put(ds.getDramaId(), ds.getOfficialSeriesTime());
            linkSeriesMap.put(ds.getDramaId(), ds.getLinkSeriesTime());
        }
        BigDecimal freeAllCount = riskPO.getFreeAllCount();// 免费播放总时长占比
        Set<Integer> dramaIdSet = new HashSet<>();
        if (rlList != null && rlList.size() > 0) {
            for (FastMemberRecentLogPO rl : rlList) {
                Integer baseTime = baseSeriesMap.get(rl.getDramaId());
                Integer officialTime = officialSeriesMap.get(rl.getDramaId());
                Integer linkTime = linkSeriesMap.get(rl.getDramaId());
                if (officialTime != null && officialTime > 0) {
                    baseTime = officialTime; // 以公众号时间为准
                }
                if (linkTime != null && linkTime > 0) {
                    baseTime = linkTime; // 以渠道时间为准
                }
                if (rl.getSecondsAll().compareTo(BigDecimal.ZERO) > 0) {
                    if (baseTime != null) {
                        BigDecimal nowRatio = DoubleUtil.divB(rl.getSecondsAll(), new BigDecimal(baseTime));
                        if (nowRatio.compareTo(freeAllCount) > 0) {
                            dramaIdSet.add(rl.getDramaId());
                        }
                    }
                }
            }
        }
        Set<Integer> dramaIdSingleSet = new HashSet<>();
        if (riskPO.getRechargeOneFlag() != null && riskPO.getRechargeOneFlag() == 1) {
            // 单剧
            // 查询单剧前几集符合要求的剧数量
            rlParam.setOfficialId(officialId);
            rlParam.setFreeSeriesCount(riskPO.getFreeSeriesCount());
            rlParam.setFreeSeriesRatio(riskPO.getFreeSeriesRatio());
            List<FastMemberRecentLogPO> preFreeList = fastMemberRecentLogMapper.queryPreSumSecondsList(rlParam);
            // 满足两者的dramaId集合
            if (riskPO.getOneSelect() == 1) {
                dramaIdSingleSet = dramaIdSet;
                if (preFreeList != null && preFreeList.size() > 0) {
                    for (FastMemberRecentLogPO mr : preFreeList) {
                        dramaIdSingleSet.add(mr.getDramaId());
                    }
                }
            } else if (riskPO.getOneSelect() == 2) {
                if (preFreeList != null && preFreeList.size() > 0) {
                    for (FastMemberRecentLogPO mr : preFreeList) {
                        if (dramaIdSet.contains(mr.getDramaId())) {
                            dramaIdSingleSet.add(mr.getDramaId());
                        }
                    }
                }
            }
        }
        // 全剧开始验证
        if (riskPO.getRechargeTotalFlag() == 1) {
            // 满足任意条件
            if (riskPO.getTotalSelect() == 1) {
                if (dramaIdSingleSet.size() >= riskPO.getTotalBaseCount() || rechargeCount >= riskPO.getTotalRechargeCount()) {// 用户某 2/3/4... 部剧及其以上满足下列单剧可充值的播放条件时
                    recharge = 1;
                    RedisUtil.set(keyTotalPass, "1", 60 * 60);
                }
            }
            // 两个都要满足
            else {
                if (dramaIdSingleSet.size() >= riskPO.getTotalBaseCount() && rechargeCount >= riskPO.getTotalRechargeCount()) {// 用户某 2/3/4... 部剧及其以上满足下列单剧可充值的播放条件时
                    recharge = 1;
                    RedisUtil.set(keyTotalPass, "1", 60 * 60);
                }
            }
        }

        // 未通过全剧的验证，开始验证单剧
        if (recharge == 0 && dramaId != null && dramaId > 0) {
            if (riskPO.getRechargeOneFlag() == 1) {
                // 单剧开始验证
                if (dramaIdSingleSet.contains(dramaId)) {
                    recharge = 1;
                    RedisUtil.set(keyOnePass, "1", 60 * 60);
                }
            }
        }

        results.put("recharge", recharge);
        return ResultVO.success(results);
    }

    /**
     * 获取ios用户是否可以充值（抖小）
     *
     * @param sessionVO
     * @return
     */
    public ResultVO<?> getCanRechargeTiktok(SessionVO sessionVO, Integer dramaId) {
        actionLogService.log("ios_pay", "进入tt的ios判断");
        Map<String, Object> results = ResultVO.getMap();
        Long memberId = sessionVO.getMemberId();
        // 验证是否黑名单用户
        String keyBlack = StaticVar.IOS_BLACK + memberId;
        String blackStr = RedisUtil.get(keyBlack);
        if (StrUtil.isEmpty(blackStr)) {
            // 库内查询状态
            FastMemberBlackPO blackParam = new FastMemberBlackPO();
            blackParam.setMemberId(memberId);
            blackParam.setDelFlag(0);
            FastMemberBlackPO blackPO = fastMemberBlackMapper.queryOne(blackParam);
            blackStr = blackPO == null ? "0" : "1";
            RedisUtil.set(keyBlack, blackStr, RedisUtil.ONE_DAY);
        }
        Integer recharge = 0;
        if ("1".equals(blackStr)) {
            // 黑名单直接返回不行
            results.put("recharge", recharge);
            return ResultVO.success(results);
        }
        // 不在黑名单，开始匹配后台配置的规则
        Integer miniId = sessionVO.getMiniId();
        String key = StaticVar.TIKTOK_IOS_PAY + miniId;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            // 未配置新规则，查询老的规则
            FastMiniSettingPO settingParam = new FastMiniSettingPO();
            settingParam.setMiniId(miniId);
            FastMiniSettingPO settingPO = fastMiniSettingMapper.queryOne(settingParam);

            if (settingPO == null) {
                log.error("抖音小程序未配置ios支付规则");
                res = StaticVar.EMPTY_FLAG;
            } else {
                res = JsonUtil.toString(settingPO);
            }
            RedisUtil.set(key, res, RedisUtil.ONE_DAY);
        }
        if (!StaticVar.EMPTY_FLAG.equals(res)) {
            FastMiniSettingPO settingPO = JsonUtil.toJavaObject(res, FastMiniSettingPO.class);
            recharge = settingPO.getOpenIosCharge2();
            results.put("iosRechargeType", settingPO.getIosRechargeType());
        }
        results.put("recharge", recharge);

        return ResultVO.success(results);
    }

    /**
     * 查询剧的总时长
     *
     * @param sessionVO
     * @param officialId
     * @param keyDramaSeriesTime
     * @return
     */
    private List<FastDramaSeriesPO> queryFreeSecondsList(SessionVO sessionVO, Integer officialId, String keyDramaSeriesTime) {
        // 加锁:同一个用户不能同时操作
        final JedisLock lock = new JedisLock(StaticVar.QUERY_FREE_SECONDS_LIST_LOCK);
        try {
            if (!lock.lock()) {
                throw new MyException(StaticStr.ERROR_OPERATION_REPEAT);
            }

            String dramaSeriesStr = RedisUtil.get(keyDramaSeriesTime);
            if (StrUtil.isNotEmpty(dramaSeriesStr)) {
                return JsonUtil.toList(dramaSeriesStr, FastDramaSeriesPO.class);
            }

            FastDramaSeriesPO seriesParam = new FastDramaSeriesPO();
            seriesParam.setOfficialId(officialId);
            seriesParam.setLinkId(sessionVO.getLinkId());
            List<FastDramaSeriesPO> seriesList = fastDramaSeriesMapper.queryFreeSecondsList(seriesParam);
            RedisUtil.setList(keyDramaSeriesTime, seriesList, RedisUtil.TIME_12H);
            return seriesList;
        } catch (Exception e) {
            log.error("error:", e);
            throw new MyException(e);
        } finally {
            lock.release();
        }
    }

    /**
     * 通过id查询单个对象
     */
    public MemberVO queryInfoByRedis(Integer memberType, Long memberId) {
        if (memberId == null) {
            return null;
        }
        if (memberType == null) {
            memberType = 1; // 默认为微信小程序，兼容发布上线
        }
        MemberVO vo = new MemberVO();
        String key = StaticVar.MEMBER_INFO_ID + memberId;
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            vo = JsonUtil.toJavaObject(value, MemberVO.class);
        } else {
            boolean existMember = true;
            if (memberType == 1) {
                // 微信小程序会员
                FastMemberPO memberParam = new FastMemberPO();
                memberParam.setId(memberId);
                memberParam.setType(1);
                FastMemberPO po = memberMapper.queryOne(memberParam);
                if (po == null) {
                    existMember = false;
                } else {
                    BeanUtils.copyProperties(po, vo);
                }
            } else if (memberType == 2) {
                // 抖音小程序会员
                FastMemberPO memberParam = new FastMemberPO();
                memberParam.setId(memberId);
                memberParam.setType(2);
                FastMemberPO po = memberMapper.queryOne(memberParam);
                if (po == null) {
                    existMember = false;
                } else {
                    BeanUtils.copyProperties(po, vo);
                }
            } else if (memberType == 3) {
                // h5站会员
                FastMemberPO memberParam = new FastMemberPO();
                memberParam.setId(memberId);
                memberParam.setType(3);
                FastMemberPO po = memberMapper.queryOne(memberParam);
                if (po == null) {
                    existMember = false;
                } else {
                    BeanUtils.copyProperties(po, vo);
                }
            } else if (memberType == 4) {
                // 快手小程序会员
                FastMemberPO memberParam = new FastMemberPO();
                memberParam.setId(memberId);
                memberParam.setType(4);
                FastMemberPO po = memberMapper.queryOne(memberParam);
                if (po == null) {
                    existMember = false;
                } else {
                    BeanUtils.copyProperties(po, vo);
                }
            } else if (memberType == 5) {
                // 快应用会员
                FastMemberPO memberParam = new FastMemberPO();
                memberParam.setId(memberId);
                memberParam.setType(5);
                FastMemberPO po = memberMapper.queryOne(memberParam);
                if (po == null) {
                    existMember = false;
                } else {
                    BeanUtils.copyProperties(po, vo);
                }
            } else if (memberType == 6) {
                // 支付宝小程序会员
                FastMemberPO memberParam = new FastMemberPO();
                memberParam.setId(memberId);
                memberParam.setType(6);
                FastMemberPO po = memberMapper.queryOne(memberParam);
                if (po == null) {
                    existMember = false;
                } else {
                    BeanUtils.copyProperties(po, vo);
                }
            } else if (memberType == 7) {
                // APP应用会员
                FastMemberPO memberParam = new FastMemberPO();
                memberParam.setId(memberId);
                memberParam.setType(7);
                FastMemberPO po = memberMapper.queryOne(memberParam);
                if (po == null) {
                    existMember = false;
                } else {
                    BeanUtils.copyProperties(po, vo);
                }
            }
            if (existMember) {
                vo.setEncryptionId(encode(memberId));
                RedisUtil.setObject(key, vo, RedisUtil.ONE_DAY);
            } else {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_30D);
                return null;
            }
        }
        return vo;
    }

    /**
     * 添加我的追剧
     *
     * @param item
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> addAddiction(FastMemberAddictionPO item) {
        try {
            if (fastMemberAddictionMapper.insert(item) == 0) {
                return ResultVO.error(StaticStr.ADD_FAILED);
            }
        } catch (Exception e) {
            return ResultVO.success();
        }
        // 删除追剧缓存
        RedisUtil.del(StaticVar.MEMBER_ADDICTION + item.getMemberId());
        return ResultVO.success();
    }

    /**
     * 停止我的追剧
     *
     * @param item
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> stopAddiction(FastMemberAddictionPO item) {

        // 查询最大随机数
        Integer maxRandomCode = fastMemberAddictionMapper.queryMaxRandom(item);
        item.setRandomCode(maxRandomCode + 1);
        if (fastMemberAddictionMapper.delete(item) == 0) {
            return ResultVO.error(StaticStr.TOP_FAILED);
        }
        // 删除追剧缓存
        RedisUtil.del(StaticVar.MEMBER_ADDICTION + item.getMemberId());
        return ResultVO.success();
    }

    /**
     * 查询用户账号信息
     *
     * @param sessionVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<MemberVO> getUserInfoRedis(SessionVO sessionVO) {
        MemberVO vo = queryInfoByRedis(sessionVO.getMemberType(), sessionVO.getMemberId());
        if (vo != null) {
            if (isEmpty(vo.getOfficialName())) {
                vo.setOfficialName("---");
            }
            MemberAccountVO accountVO = accountService.queryInfoByRedis(sessionVO.getMemberId());
            if (accountVO != null) {
                vo.setCoinRechargeAll(accountVO.getCoinRechargeAll());
                vo.setCoinGiveAll(accountVO.getCoinGiveAll());
                vo.setCoinTaskAll(accountVO.getCoinTaskAll());
                // vo.setCoinSignAll(accountVO.getCoinTaskAll());
                vo.setCoinRechargeRemain(accountVO.getCoinRechargeRemain());
                vo.setCoinGiveRemain(accountVO.getCoinGiveRemain());
                vo.setCoinTaskRemain(accountVO.getCoinTaskRemain());
                // vo.setCoinSignRemain(accountVO.getCoinTaskRemain());
                vo.setDeadTime(accountVO.getDeadTime());
                vo.setCoinDeadTime(accountVO.getCoinDeadTime());
                // 福利任务K币
                // FastMemberTaskRechargePO trPO = fastMemberTaskRechargeService.queryMemberTaskRecharge(sessionVO.getMemberId());
                // if (trPO != null && trPO.getCoinRemain() != null) {
                //     vo.setCoinTaskRemain(trPO.getCoinRemain());
                // } else {
                //     vo.setCoinTaskRemain(0);
                // }
                // 查询签约状态
                FastMemberOrderContractRecordPO mocParam = new FastMemberOrderContractRecordPO();
                mocParam.setMemberId(sessionVO.getMemberId());
                mocParam.setContractState(1);
                FastMemberOrderContractRecordPO mocPO = contractRecordService.queryLastOne(mocParam);
                int contractState = 0;
                if (mocPO != null) {
                    if (mocPO.getContractState() == 1) {
                        contractState = 1;
                        // 续费产品
                        FastFeeModelDetailPO detailPO = fastFeeModelDetailService.queryByIdRedis(mocPO.getDetailId());
                        if (detailPO.getValidUnit() == 2) {
                            vo.setContractProduct("「vip」连续包月");
                        } else if (detailPO.getValidUnit() == 5) {
                            vo.setContractProduct("「vip」连续包季");
                        }
                        vo.setContractMoneyRecharge(detailPO.getContractMoneyRecharge().toString());// 下次续费金额
                        vo.setContractSignWay(2); // 签约渠道：2 - 支付宝，​3 - 抖音支付
                        vo.setContractVipDate(DateUtil.format09(accountVO.getDeadTime()));    // 续费日期
                    }
                }
                vo.setContractState(contractState);
            }
            vo.setLoginType(sessionVO.getLoginType());
            // ads
            String val = fastCommonDictService.queryValueByCodeAndKey("ads", "limit");
            if (StrUtil.isBlank(val)) {
                vo.setAdsNum(0);
            } else {
                int num = fastMemberAdmobLogService.queryUserAdsNum(sessionVO.getMemberId());
                vo.setAdsNum(Integer.parseInt(val));
                vo.setWatchAdsNum(num);
                vo.setRemainAdsNum(Math.max((vo.getAdsNum() - num), 0));
            }
        }
        return ResultVO.success(vo);
    }

    public ResultVO getMemberAutoVip(SessionVO sessionVO) {
        Map<String, Object> result = new HashMap<>();
        result.put("memberId", sessionVO.getMemberId());
        FastMemberOrderContractRecordPO mocParam = new FastMemberOrderContractRecordPO();
        mocParam.setMemberId(sessionVO.getMemberId());
        mocParam.setContractState(1);
        FastMemberOrderContractRecordPO mocPO = contractRecordService.queryLastOne(mocParam);
        Integer contractState = 0;
        if (mocPO != null) {
            if (mocPO.getContractState() == 1) {
                // 刷新代扣状态，有可能支付宝后台取消
                mocPO = contractRecordService.queryLastOne(mocParam);
            }
        }
        if (mocPO != null) {
            if (mocPO.getContractState() == 1) {
                contractState = 1;
                // 续费产品
                FastFeeModelDetailPO detailPO = fastFeeModelDetailService.queryByIdRedis(mocPO.getDetailId());
                if (detailPO.getValidUnit() == 2) {
                    result.put("product", "「vip」连续包月");
                } else if (detailPO.getValidUnit() == 5) {
                    result.put("product", "「vip」连续包季");
                }
                result.put("moneyRecharge", detailPO.getContractMoneyRecharge()); // 下次续费金额
                result.put("signWay", 2); // 签约渠道：2 - 支付宝，​3 - 抖音支付
                // 用户账号信息
                MemberAccountVO accountVO = accountService.queryInfoByRedis(sessionVO.getMemberId());
                if (accountVO != null) {
                    result.put("vipDate", DateUtil.format09(accountVO.getDeadTime())); // 续费日期
                }
            }
        }
        result.put("contractState", contractState);
        return ResultVO.success(result);
    }

    /**
     * 查询用户相关信息
     *
     * @param sessionVO
     * @return
     */
    public ResultVO<?> getUserRelate(SessionVO sessionVO) {
        // 查询我的订阅
        Set<Integer> subSet = getMemberSubscribeListRedis(sessionVO.getMemberId());
        if (subSet == null) {
            subSet = new HashSet<>();
        }
        // 查询我的追剧
        Set<Integer> addictionSet = new HashSet<>();
        List<MemberDramaVO> addictionList = getMemberAddictionListRedis(sessionVO.getMemberId());
        if (addictionList != null && addictionList.size() > 0) {
            for (MemberDramaVO drama : addictionList) {
                if (subSet.contains(drama.getDramaId())) {
                    drama.setSubscribe(1);
                } else {
                    drama.setSubscribe(0);
                }
                // 默认观看到第一集
                drama.setSeriesNum(ObjectUtils.defaultIfNull(drama.getSeriesNum(), 1));
                addictionSet.add(drama.getDramaId());
            }
        }
        // 查询最近观看
        List<MemberDramaVO> recentList = getMemberRecentListRedis(sessionVO.getMemberId());
        if (recentList != null && recentList.size() > 0) {
            for (MemberDramaVO drama : recentList) {
                if (subSet.contains(drama.getDramaId())) {
                    drama.setSubscribe(1);
                } else {
                    drama.setSubscribe(0);
                }
                if (addictionSet.contains(drama.getDramaId())) {
                    drama.setAddiction(1);
                } else {
                    drama.setAddiction(0);
                }
            }
        }
        Map<String, Object> results = ResultVO.getMap();
        results.put("addictionList", addictionList);
        results.put("recentList", recentList);
        return ResultVO.success(results);
    }

    /**
     * 查询我的订阅
     *
     * @param memberId
     * @return
     */
    public Set<Integer> getMemberSubscribeListRedis(Long memberId) {
        String key = StaticVar.MEMBER_SUBSCRIBE + memberId;
        String value = RedisUtil.get(key);
        List<FastMemberSubscribePO> subscribeList = null;
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            subscribeList = JsonUtil.toList(value, FastMemberSubscribePO.class);
        }
        if (CollUtil.isEmpty(subscribeList)) {
            FastMemberSubscribePO params = new FastMemberSubscribePO();
            params.setMemberId(memberId);
            params.setState(1);
            subscribeList = fastMemberSubscribeMapper.queryList(params);
            if (CollUtil.hasContent(subscribeList)) {
                RedisUtil.setList(key, subscribeList, RedisUtil.TIME_2D);
            } else {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
            }
        }
        Set<Integer> subSet = new HashSet<>();
        if (subscribeList != null && subscribeList.size() > 0) {
            for (FastMemberSubscribePO sub : subscribeList) {
                subSet.add(sub.getDramaId());
            }
        }
        return subSet;
    }

    /**
     * 查询我的追剧
     *
     * @param memberId
     * @return
     */
    public List<MemberDramaVO> getMemberAddictionListRedis(Long memberId) {
        String key = StaticVar.MEMBER_ADDICTION + memberId;
        String value = RedisUtil.get(key);
        List<MemberDramaVO> addictionList = null;
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            addictionList = JsonUtil.toList(value, MemberDramaVO.class);
        }
        if (CollUtil.isEmpty(addictionList)) {
            FastMemberAddictionPO params = new FastMemberAddictionPO();
            params.setMemberId(memberId);
            addictionList = fastMemberAddictionMapper.getMemberAddictionList(params);
            if (CollUtil.hasContent(addictionList)) {
                RedisUtil.setList(key, addictionList, RedisUtil.TIME_12H);
            } else {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_12H);
                return null;
            }
        }
        if (CollUtil.hasContent(addictionList)) {
            // 剧相关需要读取短剧的缓存
            setDramaData(addictionList);
        }
        return addictionList;
    }

    /**
     * 查询最近观看
     *
     * @param memberId
     * @return
     */
    public List<MemberDramaVO> getMemberRecentListRedis(Long memberId) {
        String key = StaticVar.MEMBER_RECENT + memberId;
        String value = RedisUtil.get(key);
        List<MemberDramaVO> recentList = null;
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            recentList = JsonUtil.toList(value, MemberDramaVO.class);
        }
        if (CollUtil.isEmpty(recentList)) {
            FastMemberRecentPO recentParam = new FastMemberRecentPO();
            recentParam.setMemberId(memberId);
            recentList = fastMemberRecentMapper.getMemberRecentList(recentParam);
            if (CollUtil.hasContent(recentList)) {
                RedisUtil.setList(key, recentList, RedisUtil.TIME_12H);
            } else {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_12H);
                return null;
            }
        }
        if (CollUtil.hasContent(recentList)) {
            // 剧相关需要读取短剧的缓存
            setDramaData(recentList);
        }
        return recentList;
    }

    private void setDramaData(List<MemberDramaVO> recentList) {
        for (int i = recentList.size() - 1; i > -1; i--) {
            MemberDramaVO vo = recentList.get(i);
            FastDramaVO dramaVO = dramaService.queryInfoByRedis(vo.getDramaId());
            if (dramaVO != null) {
                vo.setUpdateState(dramaVO.getUpdateState());
                vo.setOpenState(dramaVO.getOpenState());
                vo.setDramaName(dramaVO.getDramaName());
                vo.setDramaCover(dramaVO.getDramaCover());
                vo.setDramaCoverHor(dramaVO.getDramaCoverHor());
                vo.setSeriesNumAll(dramaVO.getSeriesNumAll());
                vo.setSeriesNumUpdate(dramaVO.getSeriesNumUpdate());
                vo.setDramaIntroduce(dramaVO.getDramaIntroduce());
                if (notEmpty(dramaVO.getTagIds())) {
                    vo.setTagNameList(fastTagService.queryInfoByRedis(dramaVO.getTagIds()));
                }
                vo.setAddictionCount(dramaVO.getAddictionCount());
            } else {
                recentList.remove(i);
            }
        }
//        for (MemberDramaVO vo : recentList) {
//            FastDramaVO dramaVO = dramaService.queryInfoByRedis(vo.getDramaId());
//            vo.setUpdateState(dramaVO.getUpdateState());
//            vo.setOpenState(dramaVO.getOpenState());
//            vo.setDramaName(dramaVO.getDramaName());
//            vo.setDramaCover(dramaVO.getDramaCover());
//            vo.setDramaCoverHor(dramaVO.getDramaCoverHor());
//            vo.setSeriesNumAll(dramaVO.getSeriesNumAll());
//            vo.setSeriesNumUpdate(dramaVO.getSeriesNumUpdate());
//        }
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberPO queryById(FastMemberPO item) {
        return memberMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberPO queryById(Long id) {
        return memberMapper.queryById(id);
    }

    /**
     * 通过id查询单个对象
     */
    public long queryMaxId() {
        return memberMapper.queryMaxId();
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberPO queryOne(FastMemberPO item) {
        return memberMapper.queryOne(item);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberPO querySimpleOne(FastMemberPO item) {
        return memberMapper.querySimpleOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberPO> queryList(FastMemberPO item) {
        actionLogService.log("test_params", JsonUtil.toString(item));

        List<FastMemberPO> list = memberMapper.queryList(item);
        for (FastMemberPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        // 企微信息
        fillMemberWecom(list);

        // 分销商名称
        fillRetail(list);

        // 企微关注状态
        fillMemberWork(list);

        // 不知道什么关注状态
        fillMemberAwemeFollow(list);

        // 分流标志
        fillMemberDivide(list);

        // link信息
        fillLink(list);

        // link的advUserName
        fillLinkAdvUserName(list);

        return list;
    }

    private void fillMemberWecom(List<FastMemberPO> list) {
        Set<Long> memberIdList = list.stream().map(FastMemberPO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isEmpty(memberIdList)) {
            return;
        }
        FastMemberWecomPO fastMemberWecomPO = new FastMemberWecomPO();
        fastMemberWecomPO.setMemberIds(StrUtil.join(memberIdList));
        List<FastMemberWecomPO> relationList = fastMemberWecomService.queryList(fastMemberWecomPO);
        if (CollUtil.isEmpty(relationList)) {
            return;
        }
        Map<Long, FastMemberWecomPO> map = relationList.stream().collect(Collectors.toMap(FastMemberWecomPO::getMemberId, Function.identity(), (i1, i2) -> i1));
        list.forEach(item -> {
            FastMemberWecomPO temp = map.get(item.getId());
            if (Objects.nonNull(temp)) {
                item.setWxid(temp.getWxid());
                item.setExternalUserid(temp.getExternalUserid());
            }
        });
    }

    private void fillRetail(List<FastMemberPO> list) {
        Set<Integer> retailIdList = list.stream().map(FastMemberPO::getRetailId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isEmpty(retailIdList)) {
            return;
        }
        FastRetailPO fastRetailPO = new FastRetailPO();
        fastRetailPO.setRetailIds(StrUtil.join(retailIdList));
        List<FastRetailPO> relationList = fastRetailService.queryList(fastRetailPO);
        if (CollUtil.isEmpty(relationList)) {
            return;
        }
        Map<Integer, FastRetailPO> map = relationList.stream().collect(Collectors.toMap(FastRetailPO::getId, Function.identity(), (i1, i2) -> i1));
        list.forEach(item -> {
            FastRetailPO temp = map.get(item.getRetailId());
            if (Objects.nonNull(temp)) {
                item.setRetailName(temp.getRetailName());
            }
        });
    }

    private void fillMemberWork(List<FastMemberPO> list) {
        Set<Long> memberIdList = list.stream().map(FastMemberPO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isEmpty(memberIdList)) {
            return;
        }
        FastMemberWorkPO fastMemberWorkPO = new FastMemberWorkPO();
        fastMemberWorkPO.setIds(StrUtil.join(memberIdList));
        List<FastMemberWorkPO> relationList = fastMemberWorkService.queryList(fastMemberWorkPO);
        if (CollUtil.isEmpty(relationList)) {
            return;
        }
        Map<Long, FastMemberWorkPO> map = relationList.stream().collect(Collectors.toMap(FastMemberWorkPO::getId, Function.identity(), (i1, i2) -> i1));
        list.forEach(item -> {
            FastMemberWorkPO temp = map.get(item.getId());
            if (Objects.nonNull(temp)) {
                item.setFollowWork(temp.getFollowWork());
            }
        });
    }

    private void fillMemberAwemeFollow(List<FastMemberPO> list) {
        Set<Long> memberIdList = list.stream().map(FastMemberPO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isEmpty(memberIdList)) {
            return;
        }
        FastMemberAwemeFollowPO fastMemberAwemeFollowPO = new FastMemberAwemeFollowPO();
        fastMemberAwemeFollowPO.setIds(StrUtil.join(memberIdList));
        List<FastMemberAwemeFollowPO> relationList = fastMemberAwemeFollowService.queryList(fastMemberAwemeFollowPO);
        if (CollUtil.isEmpty(relationList)) {
            return;
        }
        Map<Long, FastMemberAwemeFollowPO> map = relationList.stream().collect(Collectors.toMap(FastMemberAwemeFollowPO::getId, Function.identity(), (i1, i2) -> i1));
        list.forEach(item -> {
            FastMemberAwemeFollowPO temp = map.get(item.getId());
            if (Objects.nonNull(temp)) {
                item.setAwemeFollowState(temp.getFollowStatus());
            }
        });
    }

    private void fillMemberDivide(List<FastMemberPO> list) {
        // 默认都给0
        list.forEach(item -> item.setDivideFlag(0));
        Set<Long> memberIdList = list.stream().map(FastMemberPO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isEmpty(memberIdList)) {
            return;
        }
        FastMemberDividePO fastMemberDividePO = new FastMemberDividePO();
        fastMemberDividePO.setMemberIds(StrUtil.join(memberIdList));
        List<FastMemberDividePO> relationList = fastMemberDivideService.queryList(fastMemberDividePO);
        if (CollUtil.isEmpty(relationList)) {
            return;
        }
        //  member_id + retail_id + official_id
        Map<String, FastMemberDividePO> map = relationList.stream().collect(Collectors.toMap(item -> item.getMemberId() + "_" + item.getRetailId() + "_" + item.getOfficialId(),
                Function.identity(), (i1, i2) -> i1));
        list.forEach(item -> {
            FastMemberDividePO temp = map.get(item.getId() + "_" + item.getRetailId() + "_" + item.getOfficialId());
            if (Objects.nonNull(temp)) {
                item.setDivideFlag(temp.getDivideFlag());
            }
        });
    }

    private void fillLink(List<FastMemberPO> list) {
        Set<Integer> lastLinkIdList = list.stream().map(FastMemberPO::getLinkId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isEmpty(lastLinkIdList)) {
            return;
        }
        FastLinkPO fastLinkPO = new FastLinkPO();
        fastLinkPO.setIds(StrUtil.join(lastLinkIdList));
        List<FastLinkPO> relationList = fastLinkService.queryList(fastLinkPO);
        if (CollUtil.isEmpty(relationList)) {
            return;
        }
        Map<Integer, FastLinkPO> map = relationList.stream().collect(Collectors.toMap(FastLinkPO::getId, Function.identity(), (i1, i2) -> i1));
        list.forEach(item -> {
            FastLinkPO temp = map.get(item.getLinkId());
            if (Objects.nonNull(temp)) {
                item.setAdvUserId(temp.getAdvUserId());
                item.setLinkName(temp.getLinkName());
            }
        });
    }

    private void fillLinkAdvUserName(List<FastMemberPO> list) {
        Set<Integer> advUserIdList = list.stream().map(FastMemberPO::getAdvUserId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isEmpty(advUserIdList)) {
            return;
        }
        FastUserPO fastUserPO = new FastUserPO();
        fastUserPO.setIds(StrUtil.join(advUserIdList));
        List<FastUserPO> relationList = fastUserService.queryList(fastUserPO);
        if (CollUtil.isEmpty(relationList)) {
            return;
        }
        Map<Integer, FastUserPO> map = relationList.stream().collect(Collectors.toMap(FastUserPO::getId, Function.identity(), (i1, i2) -> i1));
        list.forEach(item -> {
            FastUserPO temp = map.get(item.getAdvUserId());
            if (Objects.nonNull(temp)) {
                item.setAdvUserName(temp.getUserName());
            }
        });
    }

    /**
     * 查询全部
     */
    public List<FastMemberPO> queryOpenApiList(FastMemberPO item) {
        List<FastMemberPO> list = memberMapper.queryOpenApiList(item);
        for (FastMemberPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return list;
    }

    /**
     * 查询全部 - 601短剧平台投放看板自用
     */
    public List<FastMemberPO> queryList601Drama(FastMemberPO item) {
        List<FastMemberPO> list = memberMapper.queryList601DramaV2(item);
        for (FastMemberPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return list;
    }

    /**
     * 查询全部 - 染色用户- 601短剧平台投放看板自用
     */
    public List<FastMemberPO> queryListRanse601Drama(FastMemberPO item) {
        List<FastMemberPO> list = memberMapper.queryList601DramaRanseV2(item);
        for (FastMemberPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return list;
    }

    /**
     * 查询全部
     */
    public List<Long> queryMemberIds(FastMemberPO item) {
        return memberMapper.queryMemberIds(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberPO> list = queryList(item);
        // 判断用户是否加桌
        Set<Long> ids = list.stream().map(FastMemberPO::getId).collect(Collectors.toSet());
        String idsStr = StrUtil.join(ids);
        List<Long> addDesktopList = ids.isEmpty() ? new ArrayList<>() : fastMemberDesktopMapper.findMemberAddDesktop(idsStr);
        list.forEach(cur -> cur.setAddDesktop(addDesktopList.contains(cur.getId()) ? 1 : 0));
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryOpenApiPageList(FastMemberPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberPO> list = queryOpenApiList(item);
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询全部(分页) -601短剧平台投放看板自用
     */
    public ResultVO<?> queryPageList601Drama(FastMemberPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberPO> list = queryList601Drama(item);
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询全部(分页) -染色用户-601短剧平台投放看板自用
     */
    public ResultVO<?> getMemberListRanse601Drama(FastMemberPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberPO> list = queryListRanse601Drama(item);
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询全部(分页)
     */
    public List<FastMemberPO> queryPageListSimple(FastMemberPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberPO> list = memberMapper.queryPageListSimple(item);
        for (FastMemberPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return list;
    }

    public String getMemberFollowState(Long memberId) {
        String key = StaticVar.MEMBER_FOLLOW + memberId;
        String followState = RedisUtil.get(key);
        if (isEmpty(followState)) {
            FastMemberPO memberParam = new FastMemberPO();
            memberParam.setId(memberId);
            FastMemberPO memberPO = queryById(memberParam);
            if (memberPO == null) {
                RedisUtil.set(key, "0", RedisUtil.TIME_2D);
            } else {
                Integer officialFollowState = memberPO.getOfficialFollowState();
                if (officialFollowState == null) {
                    officialFollowState = 0;
                }
                followState = officialFollowState.toString();
                RedisUtil.set(key, officialFollowState.toString(), RedisUtil.TIME_2D);
            }
        }
        return followState;
    }

    /**
     * 根据openid查询域名
     *
     * @param openid
     * @return
     */
    @Slave
    public String queryDomainByOpenId(String openid) {
        String key = "h5_domain_by_openid:" + openid;
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            return value;
        }
        value = memberMapper.queryDomainByOpenId(openid);
        RedisUtil.set(key, value, RedisUtil.TIME_1D);
        return value;
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberPO item) {
        return memberMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setUpdateTime(nowTime);
        if (memberMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (memberMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        // 删除缓存
        String key = StaticVar.MEMBER_INFO_ID + item.getId();
        RedisUtil.del(key);
        log.info("删除缓存key: {}", key);
        return MethodVO.success();
    }

    public Long queryMemberIdByOutTransId(String outTransId) {
        return memberMapper.queryMemberIdByOutTransId(outTransId);
    }

    public FastMemberPO queryMemberByUid(String uid) {
        if (StrUtil.isBlank(uid)) {
            return null;
        }
        FastMemberPO mParam = new FastMemberPO();
        mParam.setUid(uid);
        mParam.setType(MiniTypeEnum.APP.index);
        mParam.setState(MemberStateEnum.VALID.getCode());
        return memberMapper.queryOne(mParam);
    }

    public FastMemberPO queryMemberByEmail(String email) {
        if (StrUtil.isBlank(email)) {
            return null;
        }
        FastMemberPO mParam = new FastMemberPO();
        mParam.setEmail(email);
        mParam.setType(MiniTypeEnum.APP.index);
        mParam.setState(MemberStateEnum.VALID.getCode());
        return memberMapper.queryOne(mParam);
    }

    public int delMemberById(Long id) {
        if (id == null) {
            return 0;
        }
        return memberMapper.delMemberById(id);
    }
}
