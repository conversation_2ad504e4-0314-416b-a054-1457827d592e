/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberLinkLoadMapper;
import com.fast.po.member.FastMemberLinkLoadPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberLinkLoadService extends BaseService {

    @Autowired
    private FastMemberLinkLoadMapper fastMemberLinkLoadMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkLoadPO queryById(FastMemberLinkLoadPO params) {
        return fastMemberLinkLoadMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkLoadPO queryById(Long id) {
        return fastMemberLinkLoadMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberLinkLoadPO queryOne(FastMemberLinkLoadPO params) {
        return fastMemberLinkLoadMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberLinkLoadPO> queryList(FastMemberLinkLoadPO params) {
        return fastMemberLinkLoadMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberLinkLoadPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberLinkLoadPO> list = fastMemberLinkLoadMapper.queryList(params);
        for (FastMemberLinkLoadPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberLinkLoadPO params) {
        return fastMemberLinkLoadMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberLinkLoadPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberLinkLoadMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberLinkLoadPO> list) {
        if (fastMemberLinkLoadMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberLinkLoadPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberLinkLoadMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
