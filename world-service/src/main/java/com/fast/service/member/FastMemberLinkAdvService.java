/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberLinkAdvMapper;
import com.fast.po.member.FastMemberLinkAdvPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberLinkAdvService extends BaseService {

    @Autowired
    private FastMemberLinkAdvMapper fastMemberLinkAdvMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkAdvPO queryById(FastMemberLinkAdvPO params) {
        return fastMemberLinkAdvMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkAdvPO queryById(Long id) {
        return fastMemberLinkAdvMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberLinkAdvPO queryOne(FastMemberLinkAdvPO params) {
        return fastMemberLinkAdvMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberLinkAdvPO> queryList(FastMemberLinkAdvPO params) {
        return fastMemberLinkAdvMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberLinkAdvPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberLinkAdvPO> list = fastMemberLinkAdvMapper.queryList(params);
        for (FastMemberLinkAdvPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberLinkAdvPO params) {
        return fastMemberLinkAdvMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberLinkAdvPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberLinkAdvMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberLinkAdvPO> list) {
        if (fastMemberLinkAdvMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberLinkAdvPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberLinkAdvMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
