/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.mapper.member.FastMemberAccountExpireMapper;
import com.fast.mapper.member.FastMemberAccountMapper;
import com.fast.po.member.FastMemberAccountExpirePO;
import com.fast.service.base.BaseService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberAccountExpireService extends BaseService {

    @Autowired
    private FastMemberAccountExpireMapper accountExpireMapper;
    @Autowired
    private FastMemberAccountMapper accountMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberAccountExpirePO queryById(FastMemberAccountExpirePO item) {
        return accountExpireMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberAccountExpirePO queryById(Integer id) {
        return accountExpireMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberAccountExpirePO queryOne(FastMemberAccountExpirePO item) {
        return accountExpireMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberAccountExpirePO> queryList(FastMemberAccountExpirePO item) {
        return accountExpireMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberAccountExpirePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberAccountExpirePO> list = accountExpireMapper.queryList(item);
        for (FastMemberAccountExpirePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberAccountExpirePO item) {
        return accountExpireMapper.queryCount(item);
    }

}
