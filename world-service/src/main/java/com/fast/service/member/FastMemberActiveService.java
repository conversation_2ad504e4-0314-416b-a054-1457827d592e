/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberActiveMapper;
import com.fast.po.member.FastMemberActivePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberActiveService extends BaseService {

    @Autowired
    private FastMemberActiveMapper fastMemberActiveMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberActivePO queryById(FastMemberActivePO params) {
        return fastMemberActiveMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberActivePO queryById(Long id) {
        return fastMemberActiveMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberActivePO queryOne(FastMemberActivePO params) {
        return fastMemberActiveMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberActivePO> queryList(FastMemberActivePO params) {
        return fastMemberActiveMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberActivePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberActivePO> list = fastMemberActiveMapper.queryList(params);
        for (FastMemberActivePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberActivePO params) {
        return fastMemberActiveMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberActivePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberActiveMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberActivePO> list) {
        if (fastMemberActiveMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberActivePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberActiveMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

}
