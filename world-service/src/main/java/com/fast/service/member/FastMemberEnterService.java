/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberEnterMapper;
import com.fast.po.member.FastMemberEnterPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberEnterService extends BaseService {

    @Autowired
    private FastMemberEnterMapper fastMemberEnterMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberEnterPO queryById(FastMemberEnterPO item) {
        return fastMemberEnterMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberEnterPO queryById(Integer id) {
        return fastMemberEnterMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberEnterPO queryOne(FastMemberEnterPO item) {
        return fastMemberEnterMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberEnterPO> queryList(FastMemberEnterPO item) {
        return fastMemberEnterMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberEnterPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberEnterPO> list = fastMemberEnterMapper.queryList(item);
        for (FastMemberEnterPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberEnterPO item) {
        return fastMemberEnterMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberEnterPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberEnterMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberEnterPO> list) {
        if (fastMemberEnterMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberEnterPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberEnterMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
