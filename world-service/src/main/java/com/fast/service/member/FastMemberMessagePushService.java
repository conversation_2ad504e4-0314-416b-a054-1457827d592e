/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberMessagePushMapper;
import com.fast.mapper.subscribe.FastMiniSubscribeTemplateMapper;
import com.fast.mapper.task.FastMemberTaskActionMapper;
import com.fast.po.member.FastMemberMessagePushPO;
import com.fast.service.base.BaseService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.task.FastSettingTaskService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberMessagePushService extends BaseService {

    @Autowired
    private FastMemberMessagePushMapper fastMemberMessagePushMapper;
    @Autowired
    private FastMemberMessagePushService fastMemberMessagePushService;
    @Autowired
    private FastMemberService fastMemberService;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMiniSubscribeTemplateMapper fastMiniSubscribeTemplateMapper;
    @Autowired
    private FastSettingTaskService fastSettingTaskService;
    @Autowired
    private FastMemberTaskActionMapper fastMemberTaskActionMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberMessagePushPO queryById(FastMemberMessagePushPO params) {
        return fastMemberMessagePushMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberMessagePushPO queryById(Integer id) {
        return fastMemberMessagePushMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberMessagePushPO queryOne(FastMemberMessagePushPO params) {
        return fastMemberMessagePushMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberMessagePushPO> queryList(FastMemberMessagePushPO params) {
        return fastMemberMessagePushMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberMessagePushPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberMessagePushPO> list = fastMemberMessagePushMapper.queryList(params);
        for (FastMemberMessagePushPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberMessagePushPO params) {
        return fastMemberMessagePushMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberMessagePushPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberMessagePushMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberMessagePushPO> list) {
        if (fastMemberMessagePushMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberMessagePushPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberMessagePushMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public String getMemberSignWarnState(Long memberId) {
        Date date = DateUtil.addDays(DateUtil.beginOfDay(DateUtil.getNowDate()), 1);
        String s = RedisUtil.get(StaticVar.SIGN_WARN_STATE + DateUtil.format06(date) + ":" + memberId);
        if (StrUtil.notBlank(s)) {
            return s;
        }
        FastMemberMessagePushPO fastMemberMessagePushPO = new FastMemberMessagePushPO();
        fastMemberMessagePushPO.setPushTimeS(DateUtil.addDays(DateUtil.beginOfDay(DateUtil.getNowDate()), 1));
        fastMemberMessagePushPO.setType(1);
        fastMemberMessagePushPO.setMemberId(memberId);
        fastMemberMessagePushPO.setState(0);
        int queryCount = fastMemberMessagePushMapper.queryCount(fastMemberMessagePushPO);
        if (queryCount > 0) {
            RedisUtil.set(StaticVar.SIGN_WARN_STATE + DateUtil.format06(date) + ":" + memberId, "1", StaticVar.DAY1_SECOND);
            return "1";
        }
        return "0";
    }
}
