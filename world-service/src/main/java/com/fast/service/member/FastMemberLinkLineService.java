/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticVar;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberLinkLineService extends BaseService {

    // 添加渠道时间线内容（实时）
    public void addToLinkLine(Integer link, String data) {
        if (link == null || link == 0) {
            return;
        }
        Long timeUnix = DateUtil.getTimeNowUnix();
        String key = StaticVar.LINK_LINE + link + "_";// + timeUnix;
        RedisUtil.append(key, "###" + data, 2000);
    }

    // 从时间线获取下发的广告信息（实时）
    public String getLineData(Integer link) {
        if (link == null || link == 0) {
            return "";
        }
        Long timeUnix = DateUtil.getTimeNowUnix();
        for (int i = 100; i > 0; i--) {
            String key = StaticVar.LINK_LINE + link + "_";// + (timeUnix-i);
            String res = RedisUtil.get(key);
            if (StrUtil.isNotEmpty(res)) {
                String[] clickArray = res.split("###");
                String myClick = "";
                StringBuffer sb = new StringBuffer();
                for (String click : clickArray) {
                    if (click.length() > 0 && StrUtil.isEmpty(myClick)) {
                        myClick = click;
                    } else if (click.length() > 0) {
                        sb.append("###");
                        sb.append(click);
                    }
                }
                if (sb.length() > 0) {
                    RedisUtil.set(key, sb.toString(), 20);
                } else {
                    RedisUtil.del(key);
                }
                return myClick;
            }
        }
        return "";
    }

}
