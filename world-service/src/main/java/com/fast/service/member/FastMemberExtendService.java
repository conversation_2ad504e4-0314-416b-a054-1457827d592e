/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberExtendMapper;
import com.fast.po.member.FastMemberExtendPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberExtendService extends BaseService {

    @Autowired
    private FastMemberExtendMapper fastMemberExtendMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberExtendPO queryById(FastMemberExtendPO params) {
        return fastMemberExtendMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberExtendPO queryById(Integer id) {
        return fastMemberExtendMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberExtendPO queryOne(FastMemberExtendPO params) {
        return fastMemberExtendMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberExtendPO> queryList(FastMemberExtendPO params) {
        return fastMemberExtendMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberExtendPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberExtendPO> list = fastMemberExtendMapper.queryList(params);
        for (FastMemberExtendPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberExtendPO params) {
        return fastMemberExtendMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberExtendPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberExtendMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    @Async
    public MethodVO insertExtend(Long memberId, String unionId, String appId) {
        FastMemberExtendPO mePO = new FastMemberExtendPO();
        mePO.setCreateTime(DateUtil.getNowDate());
        mePO.setId(memberId);
        mePO.setUnionId(unionId);
        mePO.setAppId(appId);
        fastMemberExtendMapper.insertSelective(mePO);
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberExtendPO> list) {
        if (fastMemberExtendMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberExtendPO params) {
        if (fastMemberExtendMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
