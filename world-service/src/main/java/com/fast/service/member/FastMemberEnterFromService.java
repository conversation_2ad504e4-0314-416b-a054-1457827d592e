/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberEnterFromMapper;
import com.fast.po.member.FastMemberEnterFromPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberEnterFromService extends BaseService {

    @Autowired
    private FastMemberEnterFromMapper fastMemberEnterFromMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberEnterFromPO queryById(FastMemberEnterFromPO params) {
        return fastMemberEnterFromMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberEnterFromPO queryById(Integer id) {
        return fastMemberEnterFromMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberEnterFromPO queryOne(FastMemberEnterFromPO params) {
        return fastMemberEnterFromMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberEnterFromPO> queryList(FastMemberEnterFromPO params) {
        return fastMemberEnterFromMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberEnterFromPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberEnterFromPO> list = fastMemberEnterFromMapper.queryList(params);
        for (FastMemberEnterFromPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberEnterFromPO params) {
        return fastMemberEnterFromMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberEnterFromPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberEnterFromMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberEnterFromPO> list) {
        if (fastMemberEnterFromMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberEnterFromPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberEnterFromMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Async
    public void insertAsync(FastMemberEnterFromPO fastMemberEnterFromPO) {
        log.info("insert");
        insert(fastMemberEnterFromPO);
    }

    public boolean queryMemberFromDesktopByRedis(Long memberId) {
        String s = RedisUtil.get(StaticVar.WE_CHAT_MEMBER_DESKTOP_FLAG + memberId);
        if (StrUtil.isNotBlank(s)) {
            return true;
        }
        FastMemberEnterFromPO enterQuery = new FastMemberEnterFromPO();
        enterQuery.setMemberId(memberId);
        // 桌面进入
        enterQuery.setType(2);
        FastMemberEnterFromPO fastMemberEnterFromPO = fastMemberEnterFromMapper.queryOne(enterQuery);
        if (Objects.nonNull(fastMemberEnterFromPO)) {
            RedisUtil.set(StaticVar.WE_CHAT_MEMBER_DESKTOP_FLAG + memberId, "1", StaticVar.DAY1_SECOND);
            return true;
        }
        return false;
    }
}
