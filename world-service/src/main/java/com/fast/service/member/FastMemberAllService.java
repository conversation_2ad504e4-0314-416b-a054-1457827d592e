/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberAllMapper;
import com.fast.po.member.FastMemberAllPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberAllService extends BaseService {

    @Autowired
    private FastMemberAllMapper fastMemberAllMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberAllPO queryById(FastMemberAllPO item) {
        return fastMemberAllMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberAllPO queryById(Integer id) {
        return fastMemberAllMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberAllPO queryOne(FastMemberAllPO item) {
        return fastMemberAllMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberAllPO> queryList(FastMemberAllPO item) {
        return fastMemberAllMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberAllPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberAllPO> list = fastMemberAllMapper.queryList(item);
        for (FastMemberAllPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberAllPO item) {
        return fastMemberAllMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberAllPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberAllMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberAllPO> list) {
        if (fastMemberAllMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberAllPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberAllMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
