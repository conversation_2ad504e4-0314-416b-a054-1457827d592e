/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberPushDetailMapper;
import com.fast.po.member.FastMemberPushDetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberPushDetailService extends BaseService {

    @Autowired
    private FastMemberPushDetailMapper fastMemberPushDetailMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberPushDetailPO queryById(FastMemberPushDetailPO params) {
        return fastMemberPushDetailMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberPushDetailPO queryById(Integer id) {
        return fastMemberPushDetailMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberPushDetailPO queryOne(FastMemberPushDetailPO params) {
        return fastMemberPushDetailMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberPushDetailPO> queryList(FastMemberPushDetailPO params) {
        return fastMemberPushDetailMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberPushDetailPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberPushDetailPO> list = fastMemberPushDetailMapper.queryList(params);
        for (FastMemberPushDetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberPushDetailPO params) {
        return fastMemberPushDetailMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberPushDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberPushDetailMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberPushDetailPO> list) {
        if (fastMemberPushDetailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberPushDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberPushDetailMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
