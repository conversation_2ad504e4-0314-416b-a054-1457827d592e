/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberBlackMapper;
import com.fast.mapper.member.FastMemberMapper;
import com.fast.po.member.FastMemberBlackPO;
import com.fast.po.member.FastMemberPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberBlackService extends BaseService {

    @Autowired
    private FastMemberBlackMapper fastMemberBlackMapper;
    @Autowired
    private FastMemberMapper fastMemberMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberBlackPO queryById(FastMemberBlackPO item) {
        return fastMemberBlackMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberBlackPO queryById(Integer id) {
        return fastMemberBlackMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberBlackPO queryOne(FastMemberBlackPO item) {
        return fastMemberBlackMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberBlackPO> queryList(FastMemberBlackPO item) {
        return fastMemberBlackMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberBlackPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberBlackPO> list = fastMemberBlackMapper.queryList(item);
        for (FastMemberBlackPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberBlackPO item) {
        return fastMemberBlackMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberBlackPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);

        FastMemberBlackPO mParam = new FastMemberBlackPO();
        mParam.setMemberId(item.getMemberId());
        mParam.setDelFlag(0);
        FastMemberBlackPO mPO = fastMemberBlackMapper.queryOne(mParam);
        if (mPO != null) {
            return MethodVO.error("已经存在不能重复添加");
        }
        // 判断是否真实用户
        FastMemberPO memberParam = new FastMemberPO();
        memberParam.setId(item.getMemberId());
        FastMemberPO memberPO = fastMemberMapper.queryById(memberParam);
        if (memberPO == null) {
            return MethodVO.error("非系统用户，录入失败");
        }
        if (memberPO.getPhoneOs() != null && memberPO.getPhoneOs() == 1) {
            return MethodVO.error("不支持添加安卓用户");
        }
        if (fastMemberBlackMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        String keyBlack = StaticVar.IOS_BLACK + item.getMemberId();
        RedisUtil.del(keyBlack);
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberBlackPO> list) {
        if (fastMemberBlackMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberBlackPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMemberBlackMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        FastMemberBlackPO blackPO = fastMemberBlackMapper.queryById(item.getId());
        String keyBlack = StaticVar.IOS_BLACK + blackPO.getMemberId();
        RedisUtil.del(keyBlack);
        return MethodVO.success();
    }
}
