/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.FlowTypeEnum;
import com.fast.mapper.member.FastMemberAccountMapper;
import com.fast.po.member.FastMemberAccountFlowPO;
import com.fast.po.member.FastMemberAccountPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.member.MemberAccountVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberAccountService extends BaseService {

    @Autowired
    private FastMemberAccountMapper accountMapper;
    @Autowired
    private FastMemberAccountFlowService accountFlowService;

    /**
     * 通过id查询单个对象
     */
    public MemberAccountVO queryInfoByRedis(FastMemberAccountPO item) {
        if (item.getMemberId() == null) {
            return null;
        }
        MemberAccountVO accountVO = new MemberAccountVO();
        String key = StaticVar.MEMBER_ACCOUNT_ID + item.getMemberId();
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            accountVO = JsonUtil.toJavaObject(value, MemberAccountVO.class);
        } else {
            FastMemberAccountPO account = accountMapper.queryById(item);
            if (account == null) {
                // 如果不存在, 则初始化账户
                account = new FastMemberAccountPO();
                account.setMemberId(item.getMemberId());
                account.setDeadTime(StaticVar.MIN_DATE);
                account.setCoinDeadTime(DateUtil.endOfYear());
                account.setCreateTime(DateUtil.getNowDate());
                accountMapper.insertSelective(account);
                account = accountMapper.queryById(item);
            }
            if (account == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_2D);
                return null;
            } else {
                BeanUtils.copyProperties(account, accountVO);
                // 设置上线vip的限制时间

                RedisUtil.set(key, JsonUtil.toString(accountVO), RedisUtil.TIME_2D);
            }
        }
        return accountVO;
    }

    /**
     * 通过memberId查询单个对象
     */
    public MemberAccountVO queryInfoByRedis(Long memberId) {
        FastMemberAccountPO itemParam = new FastMemberAccountPO();
        itemParam.setMemberId(memberId);
        return queryInfoByRedis(itemParam);
    }

    /**
     * 通过memberId删除单个对象的缓存
     */
    @Async
    public void removeInfoByRedis(Long memberId, Integer linkId) {
        // sleep(20);
        RedisUtil.del(StaticVar.MEMBER_ACCOUNT_ID + memberId);
        RedisUtil.del(StaticVar.MEMBER_RECHARGE + memberId + "_" + linkId);
        RedisUtil.del(StaticVar.MINI_MEMBER_VERSION_ID + memberId);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberAccountPO queryById(FastMemberAccountPO item) {
        return accountMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberAccountPO queryById(Long id) {
        FastMemberAccountPO itemParam = new FastMemberAccountPO();
        itemParam.setMemberId(id);
        return accountMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberAccountPO queryOne(FastMemberAccountPO item) {
        return accountMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberAccountPO> queryList(FastMemberAccountPO item) {
        return accountMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberAccountPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberAccountPO> list = accountMapper.queryList(item);
        for (FastMemberAccountPO cur : list) {
            cur.setEncryptionId(encode(cur.getMemberId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberAccountPO item) {
        return accountMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberAccountPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (accountMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberAccountPO item) {
        JedisLock lock = new JedisLock(StaticVar.MEMBER_ACCOUNT_CHANGE + item.getMemberId());
        try {
            if (!lock.lock()) {
                log.warn("UPDATE，账户余额变更锁获取失败，memberId: {}", item.getMemberId());
                return MethodVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            Date nowDate = item.getUpdateTime() == null ? DateUtil.getNowDate() : item.getUpdateTime();
            item.setUpdateTime(nowDate);
            if (accountMapper.updateById(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
            String key = StaticVar.MEMBER_ACCOUNT_ID + item.getMemberId();
            RedisUtil.del(key);
            log.info("UPDATE，清除用户账户缓存，key: {}", key);
        } catch (InterruptedException e) {
            log.error("UPDATE，账户余额变更锁获取异常，memberId: {}", item.getMemberId(), e);
            return MethodVO.error(StaticStr.SYS_ERROR);
        } finally {
            lock.release();
            log.info("UPDATE，账户余额变更锁已释放，memberId: {}", item.getMemberId());
        }
        return MethodVO.success();
    }

    /**
     * 增加账户余额，唯一入口
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updatePlusById(FastMemberAccountPO item) {
        JedisLock lock = new JedisLock(StaticVar.MEMBER_ACCOUNT_CHANGE + item.getMemberId());
        try {
            if (!lock.lock()) {
                log.warn("IN，账户余额变更锁获取失败，memberId: {}", item.getMemberId());
                return MethodVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            Date nowDate = item.getUpdateTime() == null ? DateUtil.getNowDate() : item.getUpdateTime();
            item.setUpdateTime(nowDate);
            if (accountMapper.updatePlusById(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
            String key = StaticVar.MEMBER_ACCOUNT_ID + item.getMemberId();
            RedisUtil.del(key);
            log.info("IN，清除用户账户缓存，key: {}", key);
            // 入账流水
            if (item.isSyncFlow()) {
                syncAccountFlow(item, FlowTypeEnum.IN.getCode(), nowDate);
            }
        } catch (InterruptedException e) {
            log.error("IN，账户余额变更锁获取异常，memberId: {}", item.getMemberId(), e);
            return MethodVO.error(StaticStr.SYS_ERROR);
        } finally {
            lock.release();
            log.info("IN，账户余额变更锁已释放，memberId: {}", item.getMemberId());
        }
        return MethodVO.success();
    }

    /**
     * 扣除账户余额，唯一入口
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDeductById(FastMemberAccountPO item) {
        JedisLock lock = new JedisLock(StaticVar.MEMBER_ACCOUNT_CHANGE + item.getMemberId());
        try {
            if (!lock.lock()) {
                log.warn("OUT，账户余额变更锁获取失败，memberId: {}", item.getMemberId());
                return MethodVO.error(StaticStr.ERROR_OPERATION_REPEAT);
            }
            Date nowDate = item.getUpdateTime() == null ? DateUtil.getNowDate() : item.getUpdateTime();
            item.setUpdateTime(nowDate);
            if (accountMapper.updateDeductById(item) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.UPDATE_FAILED);
            }
            String key = StaticVar.MEMBER_ACCOUNT_ID + item.getMemberId();
            RedisUtil.del(key);
            log.info("OUT，清除用户账户缓存，key: {}", key);
            // 出账流水
            if (item.isSyncFlow()) {
                syncAccountFlow(item, FlowTypeEnum.OUT.getCode(), nowDate);
            }
        } catch (InterruptedException e) {
            log.error("OUT，账户余额变更锁获取异常，memberId: {}", item.getMemberId(), e);
            return MethodVO.error(StaticStr.SYS_ERROR);
        } finally {
            lock.release();
            log.info("OUT，账户余额变更锁已释放，memberId: {}", item.getMemberId());
        }
        return MethodVO.success();
    }

    /**
     * 同步写账户流水
     *
     * @param item 账户信息
     * @param type 流水类型（1、流入；2、流出）
     * @param time 时间
     */
    private void syncAccountFlow(FastMemberAccountPO item, Integer type, Date time) {
        FastMemberAccountFlowPO flow = new FastMemberAccountFlowPO();
        flow.setMemberId(item.getMemberId());
        flow.setType(type);
        flow.setCoin(item.getCoinAll() == null ? item.getCoinRemain() : item.getCoinAll());
        flow.setCoinRecharge(item.getCoinRechargeAll() == null ? item.getCoinRechargeRemain() : item.getCoinRechargeAll());
        flow.setCoinGive(item.getCoinGiveAll() == null ? item.getCoinGiveRemain() : item.getCoinGiveAll());
        flow.setCoinTask(item.getCoinTaskAll() == null ? item.getCoinTaskRemain() : item.getCoinTaskAll());
        flow.setRemark(item.getRemark());
        flow.setCoinChangeId(item.getCoinChangeId());
        flow.setBatchNo(item.getBatchNo() == null ? StrUtil.getRandomInt(10) : item.getBatchNo());
        flow.setCreateTime(time);
        accountFlowService.insert(flow);
    }

}
