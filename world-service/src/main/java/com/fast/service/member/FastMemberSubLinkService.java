/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberSubLinkMapper;
import com.fast.po.member.FastMemberSubLinkPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberSubLinkService extends BaseService {

    @Autowired
    private FastMemberSubLinkMapper fastMemberSubLinkMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberSubLinkPO queryById(FastMemberSubLinkPO item) {
        return fastMemberSubLinkMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberSubLinkPO queryById(Long id) {
        return fastMemberSubLinkMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberSubLinkPO queryOne(FastMemberSubLinkPO item) {
        return fastMemberSubLinkMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberSubLinkPO> queryList(FastMemberSubLinkPO item) {
        return fastMemberSubLinkMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberSubLinkPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberSubLinkPO> list = fastMemberSubLinkMapper.queryList(item);
        for (FastMemberSubLinkPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberSubLinkPO item) {
        return fastMemberSubLinkMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberSubLinkPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberSubLinkMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberSubLinkPO> list) {
        if (fastMemberSubLinkMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberSubLinkPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMemberSubLinkMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
