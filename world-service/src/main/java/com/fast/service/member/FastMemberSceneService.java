/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberSceneMapper;
import com.fast.po.member.FastMemberScenePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberSceneService extends BaseService {

    @Autowired
    private FastMemberSceneMapper fastMemberSceneMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberScenePO queryById(FastMemberScenePO params) {
        return fastMemberSceneMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberScenePO queryById(Long id) {
        return fastMemberSceneMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberScenePO queryOne(FastMemberScenePO params) {
        return fastMemberSceneMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberScenePO> queryList(FastMemberScenePO params) {
        return fastMemberSceneMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberScenePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberScenePO> list = fastMemberSceneMapper.queryList(params);
        for (FastMemberScenePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberScenePO params) {
        return fastMemberSceneMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberScenePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberSceneMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberScenePO> list) {
        if (fastMemberSceneMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberScenePO params) {
        if (fastMemberSceneMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
