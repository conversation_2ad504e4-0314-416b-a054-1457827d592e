/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.member.FastMemberCoinChangeMapper;
import com.fast.mapper.member.FastMemberMapper;
import com.fast.po.member.FastMemberAccountPO;
import com.fast.po.member.FastMemberCoinChangePO;
import com.fast.po.member.FastMemberPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.member.MemberAccountVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberCoinChangeService extends BaseService {

    @Autowired
    private FastMemberCoinChangeMapper coinChangeMapper;
    @Autowired
    private FastMemberAccountService accountService;
    @Autowired
    private FastMemberAccountFlowService accountFlowService;
    @Autowired
    private FastMemberMapper memberMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberCoinChangePO queryById(FastMemberCoinChangePO item) {
        return coinChangeMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberCoinChangePO queryById(Integer id) {
        return coinChangeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberCoinChangePO queryOne(FastMemberCoinChangePO item) {
        return coinChangeMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberCoinChangePO> queryList(FastMemberCoinChangePO item) {
        return coinChangeMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberCoinChangePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberCoinChangePO> list = coinChangeMapper.queryList(item);
        for (FastMemberCoinChangePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        ResultVO<Map<String, Object>> vo = ResultVO.success(getPageListData(list, pageVO));
        if (notBlank(item.getRetailIds())) {
            Set<Integer> set = CollUtil.parseIntStr2Set(item.getRetailIds());
            if (set.size() == 1) {
                List<FastMemberCoinChangePO> summaryList = coinChangeMapper.getCoinChangeSummaryList(item);
                FastMemberCoinChangePO summary;
                if (CollUtil.hasContent(summaryList)) {
                    summary = summaryList.get(0);
                } else {
                    summary = new FastMemberCoinChangePO();
                    summary.setCoinChangeMemberCount(0);
                    summary.setCoinChangeGive(0);
                    summary.setCoinChangeReduce(0);
                }
                vo.setSummary(summary);
            }
        }
        return vo;
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> getCoinChangeSummaryList(FastMemberCoinChangePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberCoinChangePO> list = coinChangeMapper.getCoinChangeSummaryList(item);
        for (FastMemberCoinChangePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberCoinChangePO item) {
        return coinChangeMapper.queryCount(item);
    }

    /**
     * 用户调币-调币/VIP
     * source
     * 1、调币
     * 2、小程序券赠币
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO addCoinChange(FastMemberCoinChangePO params, Integer source) {
        final Date nowTime = DateUtil.getNowDate();
        Date yearEndDate = DateUtil.endOfYear();

        FastMemberPO member = memberMapper.queryById(params.getMemberId());
        if (member == null) {
            throw new MyException("用户不存在");
        }
        MemberAccountVO accountDB = accountService.queryInfoByRedis(params.getMemberId());
        if (accountDB == null) {
            throw new MyException("用户账户不存在");
        }
        
        // 1=K币;2=VIP卡
        if (params.getCoinType() == 1) {
            // 1=赠送
            if (params.getChangeType() == 1) {
                FastMemberAccountPO accountUpdate = new FastMemberAccountPO();
                accountUpdate.setVersion(accountDB.getVersion());
                accountUpdate.setMemberId(params.getMemberId());
                accountUpdate.setUpdateTime(nowTime);
                // 增加账户余额
                accountUpdate.setCoinAll(params.getCoinChange());
                accountUpdate.setCoinGiveAll(params.getCoinChange());
                accountUpdate.setCoinRemain(params.getCoinChange());
                accountUpdate.setCoinGiveRemain(params.getCoinChange());
                // 备注、关联
                accountUpdate.setRemark(StaticStr.PLATFORM_ARTIFICIAL_GIFT);
                accountUpdate.setCoinChangeId(params.getId());
                MethodVO methodVO = accountService.updatePlusById(accountUpdate);
                if (methodVO.getCode() != 0) {
                    throw new MyException(StaticStr.UPDATE_FAILED);
                }
            }
            // 2=扣除
            else if (params.getChangeType() == 2) {
                Integer coinPer = params.getCoinChange();
                // 优先扣除赠送的金币
                FastMemberAccountPO accountUpdate = new FastMemberAccountPO();
                accountUpdate.setVersion(accountDB.getVersion());
                accountUpdate.setMemberId(params.getMemberId());
                accountUpdate.setUpdateTime(nowTime);
                // 扣除账户余额
                if (accountDB.getCoinGiveRemain() >= coinPer) {
                    // 直接扣除赠送的金币
                    accountUpdate.setCoinRemain(coinPer);
                    accountUpdate.setCoinGiveRemain(coinPer);
                    // 备注、关联
                    accountUpdate.setRemark(StaticStr.PLATFORM_ARTIFICIAL_DEDUCTION);
                    accountUpdate.setCoinChangeId(params.getId());
                    MethodVO methodVO = accountService.updateDeductById(accountUpdate);
                    if (methodVO.getCode() != 0) {
                        throw new MyException(StaticStr.UPDATE_FAILED);
                    }
                } else {
                    // 1.扣除全部赠送的金币
                    // 2.扣除部分充值的金币(应扣金币-赠送扣除金币)
                    accountUpdate.setCoinRemain(coinPer);
                    accountUpdate.setCoinGiveRemain(accountDB.getCoinGiveRemain());
                    accountUpdate.setCoinRechargeRemain(coinPer - accountDB.getCoinGiveRemain());
                    // 备注、关联
                    accountUpdate.setRemark(StaticStr.PLATFORM_ARTIFICIAL_DEDUCTION);
                    accountUpdate.setCoinChangeId(params.getId());
                    MethodVO methodVO = accountService.updateDeductById(accountUpdate);
                    if (methodVO.getCode() != 0) {
                        throw new MyException(StaticStr.UPDATE_FAILED);
                    }
                }
            }
        } else if (params.getCoinType() == 2) {
            // 1=赠送;2=扣除
            int day = params.getCoinChange();
            if (day <= 0) {
                throw new MyException("VIP调整天数不能低于0天");
            }
            if (params.getChangeType() == 1) {
                if (day > 730) {
                    throw new MyException("VIP调整天数不能超过730天");
                }
            } else if (params.getChangeType() == 2) {
                int leftDay = DateUtil.daysBetweenUp(DateUtil.getNowDate(), accountDB.getDeadTime());
                if (day > leftDay) {
                    throw new MyException("VIP减少天数不能低于账户有效天数");
                }
                day = -day;
            }
            FastMemberAccountPO accountUpdate = new FastMemberAccountPO();
            accountUpdate.setVersion(accountDB.getVersion());
            accountUpdate.setMemberId(params.getMemberId());
            accountUpdate.setUpdateTime(nowTime);
            // 更新账户有效期
            Date startTime;
            if (accountDB.getDeadTime().compareTo(nowTime) <= 0) {
                // 已过期的账户从当前时间开始计时
                startTime = nowTime;
            } else {
                // 未过期的账户从将要过期的时间开始计时
                startTime = accountDB.getDeadTime();
            }
            accountUpdate.setDeadTime(DateUtil.addDays(startTime, day));
            MethodVO methodVO = accountService.update(accountUpdate);
            if (methodVO.getCode() != 0) {
                throw new MyException(StaticStr.UPDATE_FAILED);
            }
            yearEndDate = accountUpdate.getDeadTime();
        }

        // 清除用户账户缓存
        String key = StaticVar.MEMBER_ACCOUNT_ID + params.getMemberId();
        RedisUtil.del(key);

        // 增加调币日志记录
        params.setCreateTime(nowTime);
        params.setCoinRemain(accountDB.getCoinRemain());
        params.setDeadTime(yearEndDate);
        params.setRetailId(member.getRetailId());
        params.setOfficialId(member.getOfficialId());
        params.setMiniId(member.getMiniId());
        if (source == 1) {// 调币
            params.setSendType(1);
        } else {// 活动赠送
            params.setSendType(2);
        }
        if (coinChangeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberCoinChangePO item) {
        Date nowTime = DateUtil.getNowDate();
        if (coinChangeMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
