/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberOrderRechargeFromMapper;
import com.fast.po.member.FastMemberOrderRechargeFromPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderRechargeFromService extends BaseService {

    @Autowired
    private FastMemberOrderRechargeFromMapper fastMemberOrderRechargeFromMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeFromPO queryById(FastMemberOrderRechargeFromPO item) {
        return fastMemberOrderRechargeFromMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeFromPO queryById(Integer id) {
        return fastMemberOrderRechargeFromMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderRechargeFromPO queryOne(FastMemberOrderRechargeFromPO item) {
        return fastMemberOrderRechargeFromMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderRechargeFromPO> queryList(FastMemberOrderRechargeFromPO item) {
        return fastMemberOrderRechargeFromMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderRechargeFromPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderRechargeFromPO> list = fastMemberOrderRechargeFromMapper.queryList(item);
        for (FastMemberOrderRechargeFromPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderRechargeFromPO item) {
        return fastMemberOrderRechargeFromMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderRechargeFromPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberOrderRechargeFromMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderRechargeFromPO> list) {
        if (fastMemberOrderRechargeFromMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderRechargeFromPO item) {
        if (fastMemberOrderRechargeFromMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
