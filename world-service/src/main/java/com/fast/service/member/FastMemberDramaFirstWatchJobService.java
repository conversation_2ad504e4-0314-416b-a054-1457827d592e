/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.StaticVar;
import com.fast.enums.DataPosEnum;
import com.fast.enums.api.MonitorApiLogEnum;
import com.fast.mapper.common.FastDataPosMapper;
import com.fast.mapper.member.FastMemberRecentLogMapper;
import com.fast.mapper.monitor.FastMonitorApiLogMapper;
import com.fast.mapper.monitor.FastMonitorMemberLoginMapper;
import com.fast.mapper.promote.FastLinkMapper;
import com.fast.po.common.FastDataPosPO;
import com.fast.po.member.FastMemberDramaFirstWatchPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.po.monitor.FastMonitorApiLogPO;
import com.fast.po.monitor.FastMonitorMemberLoginPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.thread.ContentTypeContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * C端用户首次观看日志
 *
 * <AUTHOR>
 */
@Service
public class FastMemberDramaFirstWatchJobService extends BaseService {

    @Autowired
    private FastMemberDramaFirstWatchService firstWatchService;
    @Autowired
    private FastMemberRecentLogService recentLogService;
    @Autowired
    private FastMemberOrderRechargeService orderRechargeService;
    @Autowired
    private FastDataPosMapper dataPosMapper;
    @Autowired
    private FastMonitorMemberLoginMapper memberLoginMapper;
    @Autowired
    private FastMemberRecentLogMapper memberRecentLogMapper;
    @Autowired
    private FastMonitorApiLogMapper monitorApiLogMapper;
    @Autowired
    private FastLinkMapper linkMapper;

    /**
     * 首次观看某个短剧
     */
    @Transactional(rollbackFor = Exception.class)
    public void auto() {
        FastDataPosPO pos = new FastDataPosPO();
        if (ContentTypeContext.getContentType() == null) {
            ContentTypeContext.setContentType(1);
        }
        if (ContentTypeContext.getContentType() == 1) {
            pos.setId(DataPosEnum.d_1.index);
        }
        if (ContentTypeContext.getContentType() == 2) {
            pos.setId(DataPosEnum.d_2.index);
        }
        if (ContentTypeContext.getContentType() == 3) {
            pos.setId(DataPosEnum.d_3.index);
        }
        long maxId = toLong(dataPosMapper.queryValue(pos), 0L);
        FastMemberRecentLogPO param = new FastMemberRecentLogPO();
        param.setLimitExport(StaticVar.DATA_2K);
        List<FastMemberRecentLogPO> list = new ArrayList<>(StaticVar.DATA_2K);
        do {
            list.clear();
            param.setMinId(++maxId);
            list.addAll(recentLogService.querySlaveSimpleList(param));
            if (list.size() == 0) {
                return;
            }
            for (FastMemberRecentLogPO recentLog : list) {
                FastMemberDramaFirstWatchPO watch = new FastMemberDramaFirstWatchPO();
                BeanUtils.copyProperties(recentLog, watch);
                watch.setCreateDate(watch.getCreateTime());

                try {
                    if (firstWatchService.queryMemberDramaCount(watch) == 0) {
                        firstWatchService.insert(watch);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            maxId = list.get(list.size() - 1).getId();
            pos.setValue(maxId);
            dataPosMapper.updateById(pos);
        }
        while (list.size() == StaticVar.DATA_2K);
    }

    /**
     * 更新-历史数据,可以滚动更新非今日数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoPayTime() {
        FastDataPosPO pos = new FastDataPosPO();
        if (ContentTypeContext.getContentType() == null) {
            ContentTypeContext.setContentType(1);
        }
        if (ContentTypeContext.getContentType() == 1) {
            pos.setId(DataPosEnum.d_4.index);
        }
        if (ContentTypeContext.getContentType() == 2) {
            pos.setId(DataPosEnum.d_5.index);
        }
        if (ContentTypeContext.getContentType() == 3) {
            pos.setId(DataPosEnum.d_6.index);
        }
        long maxId = toLong(dataPosMapper.queryValue(pos), 0L);
        FastMemberDramaFirstWatchPO param = new FastMemberDramaFirstWatchPO();
        param.setLimitExport(StaticVar.DATA_2K);
        // 截止到昨日的结束时间, 今天的数据不能统计
        param.setCreateDateE(DateUtil.endOfDay(DateUtil.getYesterdayDate()));
        List<FastMemberDramaFirstWatchPO> list = new ArrayList<>();
        do {
            list.clear();
            param.setMinId(++maxId);
            list.addAll(firstWatchService.querySlaveSimpleList(param));
            if (list.size() == 0) {
                return;
            }
            for (FastMemberDramaFirstWatchPO watchPO : list) {
                try {
                    if (watchPO.getTodayPay() == 1) {
                        continue;
                    }
                    FastMemberOrderRechargePO rechargeQ = new FastMemberOrderRechargePO();
                    rechargeQ.setMemberId(watchPO.getMemberId());
                    rechargeQ.setDramaId(watchPO.getDramaId());
                    rechargeQ.setState(1);
                    rechargeQ.setPayDate(DateUtil.format06Int(watchPO.getCreateDate()));
                    FastMemberOrderRechargePO rechargePO = orderRechargeService.queryOne(rechargeQ);
                    if (rechargePO != null && rechargePO.getCoinChangeId() == 0) {
                        watchPO.setTodayPay(1);
                        watchPO.setPayDateStr(DateUtil.format09(rechargePO.getPayTime()));
                        watchPO.setPayTime(rechargePO.getPayTime());
                        firstWatchService.updateTodayPayState(watchPO);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            maxId = list.get(list.size() - 1).getId();
            pos.setValue(maxId);
            dataPosMapper.updateById(pos);
        }
        while (list.size() == StaticVar.DATA_2K);
    }

    /**
     * 更新-历史数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoPayState() {
        FastDataPosPO pos = new FastDataPosPO();
        if (ContentTypeContext.getContentType() == null) {
            ContentTypeContext.setContentType(1);
        }
        if (ContentTypeContext.getContentType() == 1) {
            pos.setId(DataPosEnum.d_7.index);
        }
        if (ContentTypeContext.getContentType() == 2) {
            pos.setId(DataPosEnum.d_8.index);
        }
        if (ContentTypeContext.getContentType() == 3) {
            pos.setId(DataPosEnum.d_9.index);
        }
        long maxId = toLong(dataPosMapper.queryValue(pos), 0L);
        FastMemberOrderRechargePO param = new FastMemberOrderRechargePO();
        param.setLimitExport(StaticVar.DATA_2K);
        param.setState(1);
        param.setCoinChangeId(0);
        param.setContentType(ContentTypeContext.getContentType());
        List<FastMemberOrderRechargePO> list = new ArrayList<>();
        do {
            list.clear();
            param.setMinId(++maxId);
            list.addAll(orderRechargeService.querySlaveSimpleList(param));
            if (list.size() == 0) {
                pos.setValue(maxId + StaticVar.DATA_2K);
                dataPosMapper.updateById(pos);
                return;
            }
            for (FastMemberOrderRechargePO recharge : list) {
                try {
                    FastMemberDramaFirstWatchPO watchPO = new FastMemberDramaFirstWatchPO();
                    watchPO.setMemberId(recharge.getMemberId());
                    watchPO.setDramaId(recharge.getDramaId());
                    watchPO.setPayDateStr(DateUtil.format09(recharge.getPayTime()));
                    watchPO.setPayTime(recharge.getPayTime());
                    firstWatchService.updateTodayPayState(watchPO);
                    firstWatchService.updateH24PayState(watchPO);
                    firstWatchService.updateH48PayState(watchPO);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            maxId = list.get(list.size() - 1).getId();
            pos.setValue(maxId);
            dataPosMapper.updateById(pos);
        }
        while (list.size() == StaticVar.DATA_2K);
    }

    public void memberWatchLog() {
        FastMonitorMemberLoginPO query = new FastMonitorMemberLoginPO();
        query.setCreateTimeS(DateUtil.getBeforeYesterdayDate());
        Long minId = memberLoginMapper.queryMinOneId(query);
        if (minId == null) {
            return;
        }
        query.setCreateTimeS(null);
        query.setLimit(StaticVar.DATA_2K);
        List<FastMonitorMemberLoginPO> list = new ArrayList<>();
        do {
            query.setMinId(minId);
            list.clear();
            list.addAll(memberLoginMapper.queryListAsc(query));
            for (FastMonitorMemberLoginPO po : list) {
                boolean needUpdate = false;
                if (po.getDramaId() == 0) {
                    FastLinkPO linkDB = linkMapper.queryById(po.getLinkIdBelong());
                    if (linkDB != null && biggerZero(linkDB.getDramaId())) {
                        po.setDramaId(linkDB.getDramaId());
                        needUpdate = true;
                    }
                }
                JSONObject map = new JSONObject(memberRecentLogMapper.querySumByMemberId(po.getMemberId(), po.getLinkIdBelong(),
                        DateUtil.getDayStartDate(po.getCreateTime()), DateUtil.getDayEndDate(po.getCreateTime())));
                if (po.getWatchCount() != map.getIntValue("watchCount") || po.getWatchSecond() != map.getIntValue("watchSecond")) {
                    needUpdate = true;
                    po.setWatchCount(map.getIntValue("watchCount"));
                    po.setWatchSecond(map.getIntValue("watchSecond"));
                }
                if (needUpdate) {
                    memberLoginMapper.updateCustomById(po);
                }
                minId = po.getId();
            }
        } while (list.size() == StaticVar.DATA_2K);
    }

    public void memberApiLog(MonitorApiLogEnum apiLog) {
        FastMonitorMemberLoginPO query = new FastMonitorMemberLoginPO();
        query.setCreateTimeS(DateUtil.getBeforeYesterdayDate());
        Long minId = memberLoginMapper.queryMinOneId(query);
        if (minId == null) {
            return;
        }
        query.setCreateTimeS(null);
        query.setLimit(StaticVar.DATA_2K);
        List<FastMonitorMemberLoginPO> list = new ArrayList<>();
        do {
            query.setMinId(minId);
            list.clear();
            list.addAll(memberLoginMapper.queryListAsc(query));
            for (FastMonitorMemberLoginPO po : list) {
                boolean needUpdate = false;
                int apiCount = defaultIfNull(monitorApiLogMapper.querySumByMemberId(po.getMemberId(), po.getLinkIdBelong(), apiLog.uri, po.getCreateTime()), 0);
                switch (apiLog) {
                    case GET_DETAIL_SIMPLE:
                    case GET_DETAIL_PLUS: {
                        if (apiCount > 0 && apiCount != po.getCountGetDetailPlus()) {
                            needUpdate = true;
                            po.setCountGetDetailPlus(apiCount);
                        }
                        break;
                    }
                    case GET_DRAMA: {
                        if (apiCount > 0 && apiCount != po.getCountGetDrama()) {
                            needUpdate = true;
                            po.setCountGetDrama(apiCount);
                        }
                        break;
                    }
                }

                if (needUpdate) {
                    memberLoginMapper.updateCustomById(po);
                }
                minId = po.getId();
            }
        } while (list.size() == StaticVar.DATA_2K);
    }

    /**
     * 如果存在请求配置接口,微量补充获取登录接口
     *
     * @return
     */
    public int memberApiLogRepair() {
        int successCount = 0;
        FastMonitorApiLogPO query = new FastMonitorApiLogPO();
        query.setUri(MonitorApiLogEnum.GET_DETAIL_PLUS.uri);
        query.setCreateTimeS(DateUtil.beginOfDay());
        query.setCreateTimeE(DateUtil.addMinutes(query.getCreateTimeS(), 30));
        List<FastMonitorApiLogPO> list = new ArrayList<>();
        Long minId = 0L;
        query.setLimit(StaticVar.DATA_2K);
        do {
            query.setMinId(minId);
            list.clear();
            list.addAll(monitorApiLogMapper.queryListAsc(query));
            if (CollUtil.isEmpty(list)) {
                return successCount;
            }
            FastMonitorMemberLoginPO loginPO = new FastMonitorMemberLoginPO();
            for (FastMonitorApiLogPO po : list) {
                loginPO.setMemberId(po.getMemberId());
                loginPO.setLinkIdBelong(po.getLinkId());
                loginPO.setCreateDate(query.getCreateTimeS());
                if (memberLoginMapper.queryCountByMemberId(loginPO) == 0) {
                    loginPO.setLinkId(po.getLinkId());
                    loginPO.setLinkIdBelong(po.getLinkId());
                    loginPO.setMinId(po.getMinId());
                    loginPO.setPhoneOs(po.getPhoneOs());
                    loginPO.setDramaId(po.getDramaId());
                    loginPO.setCountLogin(1);
                    loginPO.setRepairAdd(1);
                    loginPO.setCreateTime(query.getCreateTimeS());
                    memberLoginMapper.insertSelective(loginPO);
                    successCount++;
                }
            }
            minId = list.get(list.size() - 1).getId();
        } while (list.size() == StaticVar.DATA_2K);
        return successCount;
    }

    private static final String GET_DETAIL_URIS = "'" + MonitorApiLogEnum.GET_DETAIL_PLUS.uri + "','" + MonitorApiLogEnum.GET_DETAIL_SIMPLE.uri + "'";

    /**
     * 如果存在请求剧接口和请求登录接口,补充请求配置接口
     *
     * @return
     */
    public int memberApiLogRepair2() {
        int successCount = 0;
        Date nowDate = DateUtil.getNowDate();
        FastMonitorApiLogPO query = new FastMonitorApiLogPO();
        query.setUri(MonitorApiLogEnum.GET_DRAMA.uri);
        query.setCreateTimeS(DateUtil.addHours(nowDate, 1));
        query.setCreateTimeE(nowDate);
        List<FastMonitorApiLogPO> list = new ArrayList<>();
        Long minId = 0L;
        query.setLimit(StaticVar.DATA_2K);
        do {
            query.setMinId(minId);
            list.clear();
            list.addAll(monitorApiLogMapper.queryListAsc(query));
            if (CollUtil.isEmpty(list)) {
                return successCount;
            }
            FastMonitorMemberLoginPO loginPO = new FastMonitorMemberLoginPO();
            for (FastMonitorApiLogPO po : list) {
                loginPO.setMemberId(po.getMemberId());
                loginPO.setLinkIdBelong(po.getLinkId());
                loginPO.setCreateDate(po.getCreateTime());
                // 判断用户有请求登录接口
                if (memberLoginMapper.queryCountByMemberId(loginPO) > 0) {
                    // 判断用户没有请求配置接口
                    int count = defaultIfNull(monitorApiLogMapper.querySumByMemberId2(po.getMemberId(), po.getLinkId(), GET_DETAIL_URIS, po.getCreateTime()), 0);
                    if (count == 0) {
                        // 补充请求配置的接口
                        FastMonitorApiLogPO monitorApiLog = setMonitorApiLogData(po);
                        monitorApiLogMapper.insertSelective(monitorApiLog);
                    }
                    successCount++;
                }
            }
            minId = list.get(list.size() - 1).getId();
        } while (list.size() == StaticVar.DATA_2K);
        return successCount;
    }

    private static FastMonitorApiLogPO setMonitorApiLogData(FastMonitorApiLogPO po) {
        FastMonitorApiLogPO monitorApiLog = new FastMonitorApiLogPO();
        monitorApiLog.setMemberId(po.getMemberId());
        monitorApiLog.setLinkId(po.getLinkId());
        monitorApiLog.setMiniId(po.getMiniId());
        monitorApiLog.setPhoneOs(po.getPhoneOs());
        monitorApiLog.setDramaId(po.getDramaId());
        monitorApiLog.setApiCount(1);
        monitorApiLog.setRepairAdd(1);
        monitorApiLog.setUri(MonitorApiLogEnum.GET_DETAIL_PLUS.uri);
        monitorApiLog.setCreateTime(po.getCreateTime());
        monitorApiLog.setCreateDate(po.getCreateTime());
        return monitorApiLog;
    }
}
