/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberOpenIdMapper;
import com.fast.po.member.FastMemberOpenIdPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOpenIdService extends BaseService {

    @Autowired
    private FastMemberOpenIdMapper fastMemberOpenIdMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberOpenIdPO queryById(FastMemberOpenIdPO params) {
        return fastMemberOpenIdMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOpenIdPO queryById(Integer id) {
        return fastMemberOpenIdMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOpenIdPO queryOne(FastMemberOpenIdPO params) {
        return fastMemberOpenIdMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOpenIdPO> queryList(FastMemberOpenIdPO params) {
        return fastMemberOpenIdMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOpenIdPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOpenIdPO> list = fastMemberOpenIdMapper.queryList(params);
        for (FastMemberOpenIdPO cur : list) {
            cur.setEncryptionId(encode(cur.getMemberId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOpenIdPO params) {
        return fastMemberOpenIdMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOpenIdPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberOpenIdMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOpenIdPO> list) {
        if (fastMemberOpenIdMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOpenIdPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberOpenIdMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
