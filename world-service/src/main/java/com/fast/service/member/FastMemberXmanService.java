/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberXmanMapper;
import com.fast.po.member.FastMemberXmanPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberXmanService extends BaseService {

    @Autowired
    private FastMemberXmanMapper fastMemberXmanMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberXmanPO queryById(FastMemberXmanPO item) {
        return fastMemberXmanMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberXmanPO queryById(Integer id) {
        return fastMemberXmanMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberXmanPO queryOne(FastMemberXmanPO item) {
        return fastMemberXmanMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberXmanPO> queryList(FastMemberXmanPO item) {
        return fastMemberXmanMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberXmanPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberXmanPO> list = fastMemberXmanMapper.queryList(item);
        for (FastMemberXmanPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberXmanPO item) {
        return fastMemberXmanMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberXmanPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberXmanMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberXmanPO> list) {
        if (fastMemberXmanMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberXmanPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberXmanMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
