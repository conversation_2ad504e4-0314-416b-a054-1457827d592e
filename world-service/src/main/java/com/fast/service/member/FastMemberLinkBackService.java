/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberLinkBackMapper;
import com.fast.po.member.FastMemberLinkBackPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberLinkBackService extends BaseService {

    @Autowired
    private FastMemberLinkBackMapper fastMemberLinkBackMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkBackPO queryById(FastMemberLinkBackPO params) {
        return fastMemberLinkBackMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkBackPO queryById(Long id) {
        return fastMemberLinkBackMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberLinkBackPO queryOne(FastMemberLinkBackPO params) {
        return fastMemberLinkBackMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberLinkBackPO> queryList(FastMemberLinkBackPO params) {
        return fastMemberLinkBackMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberLinkBackPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberLinkBackPO> list = fastMemberLinkBackMapper.queryList(params);
        for (FastMemberLinkBackPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberLinkBackPO params) {
        return fastMemberLinkBackMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberLinkBackPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberLinkBackMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberLinkBackPO> list) {
        if (fastMemberLinkBackMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberLinkBackPO params) {
        if (fastMemberLinkBackMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
