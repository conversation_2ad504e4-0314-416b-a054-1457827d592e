/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberRecentDramaFinishMapper;
import com.fast.po.member.FastMemberRecentDramaFinishPO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaService;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.thread.ContentTypeContext;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.drama.FastDramaVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberRecentDramaFinishService extends BaseService {

    @Autowired
    private FastMemberRecentDramaFinishMapper dramaFinishMapper;
    @Autowired
    private FastMemberRecentLogService recentLogService;
    @Autowired
    private FastDramaService dramaService;

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentDramaFinishPO queryById(FastMemberRecentDramaFinishPO item) {
        return dramaFinishMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentDramaFinishPO queryById(Integer id) {
        return dramaFinishMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberRecentDramaFinishPO queryOne(FastMemberRecentDramaFinishPO item) {
        return dramaFinishMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberRecentDramaFinishPO> queryList(FastMemberRecentDramaFinishPO item) {
        return dramaFinishMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberRecentDramaFinishPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberRecentDramaFinishPO> list = dramaFinishMapper.queryList(item);
        for (FastMemberRecentDramaFinishPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberRecentDramaFinishPO item) {
        return dramaFinishMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberRecentDramaFinishPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (dramaFinishMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberRecentDramaFinishPO> list) {
        if (dramaFinishMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberRecentDramaFinishPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (dramaFinishMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    private static final String FINISH_DATE_KEY = "recent_log_data_drama_finish_date";

    /**
     * 观看记录数据统计用户的完播日
     * 历史数据处理
     */
    public void recentLogDataDramaFinishDate() {
        long maxId = 0;
        String value, key;
        if (ContentTypeContext.getContentType() != null) {
            key = FINISH_DATE_KEY + "_" + ContentTypeContext.getContentType();
            value = RedisUtil.get(key);
        } else {
            key = FINISH_DATE_KEY;
            value = RedisUtil.get(FINISH_DATE_KEY);
        }
        if (notBlank(value)) {
            maxId = toLong(value, 0L);
        }
        FastMemberRecentLogPO params = new FastMemberRecentLogPO();
        params.setLimitExport(StaticVar.DATA_2K);
        List<FastMemberRecentLogPO> list = new ArrayList<>();
        do {
            list.clear();
            params.setMinId(++maxId);
            list.addAll(recentLogService.querySlaveList(params));
            if (list.size() == 0) {
                return;
            }
            maxId = list.get(list.size() - 1).getId();

            // 循环处理历史数据
            for (FastMemberRecentLogPO recentLog : list) {
                // 判断当前用户是否完播
                FastDramaVO drama = dramaService.queryInfoByRedis(recentLog.getDramaId());
                if (drama == null || drama.getSeriesNumUpdate() == null) {
                    continue;
                }
                // 当前播放的非最后一集,跳过统计
                if (!Objects.equals(recentLog.getSeriesNum(), drama.getSeriesNumUpdate())) {
                    continue;
                }
                // 没有完播,跳过统计
                recentLog.setWatchTimeE(DateUtil.endOfDay(recentLog.getCreateTime()));
                if (recentLogService.querySlaveDramaFinish(recentLog) != recentLog.getSeriesNum()) {
                    continue;
                }
                FastMemberRecentDramaFinishPO update = new FastMemberRecentDramaFinishPO();
                update.setDramaId(recentLog.getDramaId());
                update.setMemberId(recentLog.getMemberId());
                update.setRetailId(recentLog.getRetailId());
                update.setMiniId(recentLog.getMiniId());
                update.setOfficialId(recentLog.getOfficialId());
                update.setLinkId(recentLog.getLinkId());
                update.setFinishDate(DateUtil.format06Int(recentLog.getCreateTime()));
                if (dramaFinishMapper.updateFinish(update) == 0) {
                    try {
                        dramaFinishMapper.insertSelective(update);
                        // 清除用户版本号缓存
                        RedisUtil.del(StaticVar.MINI_MEMBER_VERSION_ID + update.getMemberId());
                    } catch (Exception e) {
                        log.error("观看记录数据统计用户的完播日, 插入失败:{}", e.getMessage());
                    }
                }
            }
            RedisUtil.set(key, toString(maxId));
        } while (list.size() == StaticVar.DATA_2K);
    }
}
