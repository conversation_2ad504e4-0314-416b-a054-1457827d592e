/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberUnlockStartLogBackMapper;
import com.fast.po.member.FastMemberUnlockStartLogBackPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberUnlockStartLogBackService extends BaseService {

    @Autowired
    private FastMemberUnlockStartLogBackMapper fastMemberUnlockStartLogBackMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberUnlockStartLogBackPO queryById(FastMemberUnlockStartLogBackPO params) {
        return fastMemberUnlockStartLogBackMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberUnlockStartLogBackPO queryById(Integer id) {
        return fastMemberUnlockStartLogBackMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberUnlockStartLogBackPO queryOne(FastMemberUnlockStartLogBackPO params) {
        return fastMemberUnlockStartLogBackMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberUnlockStartLogBackPO> queryList(FastMemberUnlockStartLogBackPO params) {
        return fastMemberUnlockStartLogBackMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberUnlockStartLogBackPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberUnlockStartLogBackPO> list = fastMemberUnlockStartLogBackMapper.queryList(params);
        for (FastMemberUnlockStartLogBackPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberUnlockStartLogBackPO params) {
        return fastMemberUnlockStartLogBackMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberUnlockStartLogBackPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberUnlockStartLogBackMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberUnlockStartLogBackPO> list) {
        if (fastMemberUnlockStartLogBackMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberUnlockStartLogBackPO params) {
        if (fastMemberUnlockStartLogBackMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
