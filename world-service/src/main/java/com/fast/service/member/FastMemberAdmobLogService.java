/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberAdmobLogMapper;
import com.fast.po.member.FastMemberAdmobLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberAdmobLogService extends BaseService {

    @Autowired
    private FastMemberAdmobLogMapper fastMemberAdmobLogMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberAdmobLogPO queryById(FastMemberAdmobLogPO params) {
        return fastMemberAdmobLogMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberAdmobLogPO queryById(Long id) {
        return fastMemberAdmobLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberAdmobLogPO queryOne(FastMemberAdmobLogPO params) {
        return fastMemberAdmobLogMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberAdmobLogPO> queryList(FastMemberAdmobLogPO params) {
        return fastMemberAdmobLogMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberAdmobLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberAdmobLogPO> list = fastMemberAdmobLogMapper.queryList(params);
        for (FastMemberAdmobLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberAdmobLogPO params) {
        return fastMemberAdmobLogMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberAdmobLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberAdmobLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberAdmobLogPO> list) {
        if (fastMemberAdmobLogMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberAdmobLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberAdmobLogMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastMemberAdmobLogPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        FastMemberAdmobLogPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        fastMemberAdmobLogMapper.deleteById(po.getId());
        return MethodVO.success();
    }

    public int queryUserAdsNum(Long memberId) {
        if (memberId == null) {
            return 0;
        }
        FastMemberAdmobLogPO logPO = new FastMemberAdmobLogPO();
        logPO.setMemberId(memberId);
        logPO.setCreateTimeS(DateUtil.beginOfDay());
        return fastMemberAdmobLogMapper.queryCount(logPO);
    }
}
