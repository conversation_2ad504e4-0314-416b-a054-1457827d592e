/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberAccountFlowMapper;
import com.fast.po.member.FastMemberAccountFlowPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.I18nUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberAccountFlowService extends BaseService {

    @Autowired
    private FastMemberAccountFlowMapper fastMemberAccountFlowMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberAccountFlowPO queryById(FastMemberAccountFlowPO params) {
        return fastMemberAccountFlowMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberAccountFlowPO queryById(Long id) {
        return fastMemberAccountFlowMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberAccountFlowPO queryOne(FastMemberAccountFlowPO params) {
        return fastMemberAccountFlowMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberAccountFlowPO> queryList(FastMemberAccountFlowPO params) {
        return fastMemberAccountFlowMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberAccountFlowPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberAccountFlowPO> list = fastMemberAccountFlowMapper.queryList(params);
        if (CollUtil.isNotEmpty(list)) {
            for (FastMemberAccountFlowPO flow : list) {
                String remark = flow.getRemark();
                if (!StrUtil.isBlank(remark)) {
                    flow.setRemark(I18nUtil.getText(remark));
                }
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberAccountFlowPO params) {
        return fastMemberAccountFlowMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberAccountFlowPO params) {
        if (params.getCreateTime() == null) {
            Date nowTime = DateUtil.getNowDate();
            params.setCreateTime(nowTime);
        }
        if (fastMemberAccountFlowMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberAccountFlowPO> list) {
        if (fastMemberAccountFlowMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberAccountFlowPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberAccountFlowMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO delete(FastMemberAccountFlowPO params) {
        if (params.getId() == null) {
            return MethodVO.error(StaticStr.DELETE_FAILED);
        }
        FastMemberAccountFlowPO po = queryById(params.getId());
        if (po == null) {
            return MethodVO.error("data not exist");
        }
        fastMemberAccountFlowMapper.deleteById(po.getId());
        return MethodVO.success();
    }
}
