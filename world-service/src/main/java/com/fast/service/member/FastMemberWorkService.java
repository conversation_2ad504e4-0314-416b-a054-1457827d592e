/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberWorkMapper;
import com.fast.po.member.FastMemberWorkPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberWorkService extends BaseService {

    @Autowired
    private FastMemberWorkMapper fastMemberWorkMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberWorkPO queryById(FastMemberWorkPO item) {
        return fastMemberWorkMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberWorkPO queryById(Long id) {
        return fastMemberWorkMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberWorkPO queryOne(FastMemberWorkPO item) {
        return fastMemberWorkMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberWorkPO> queryList(FastMemberWorkPO item) {
        return fastMemberWorkMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberWorkPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberWorkPO> list = fastMemberWorkMapper.queryList(item);
        for (FastMemberWorkPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberWorkPO item) {
        return fastMemberWorkMapper.queryCount(item);
    }

    /**
     * 新增企微关注状态
     */
    @Async
    public MethodVO insertMemberFollow(Long memberId) {
        String key = StaticVar.MEMBER_FOLLOW_WORD + memberId;
        String res = RedisUtil.get(key);
        if (StrUtil.isNotEmpty(res)) {
            return MethodVO.error("已经存在关注信息");
        }
        // 查询库是否存在
        FastMemberWorkPO mwPO = fastMemberWorkMapper.queryById(memberId);
        if (mwPO != null) {
            RedisUtil.set(key, "exist", 60 * 60 * 24 * 7);
            return MethodVO.error("已经存在关注信息");
        }
        // 新增数据
        FastMemberWorkPO workPO = new FastMemberWorkPO();
        workPO.setCreateTime(DateUtil.getNowDate());
        workPO.setFollowWork(1);
        workPO.setId(memberId);
        fastMemberWorkMapper.insertSelective(workPO);
        actionLogService.log("member_work", "新增一条企微关注,memberId=" + memberId);
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberWorkPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberWorkMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }


    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberWorkPO item) {
        if (fastMemberWorkMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
