/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.annotation.Master;
import com.fast.mapper.member.FastMemberRecentSeriesDayMapper;
import com.fast.po.common.FastTableUnpackPO;
import com.fast.po.member.FastMemberRecentSeriesDayPO;
import com.fast.service.base.BaseService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberRecentSeriesDayService extends BaseService {

    @Autowired
    private FastMemberRecentSeriesDayMapper recentSeriesMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentSeriesDayPO queryById(FastMemberRecentSeriesDayPO item) {
        return recentSeriesMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentSeriesDayPO queryById(Integer id) {
        return recentSeriesMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberRecentSeriesDayPO queryOne(FastMemberRecentSeriesDayPO item) {
        return recentSeriesMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberRecentSeriesDayPO> queryList(FastMemberRecentSeriesDayPO item) {
        return recentSeriesMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberRecentSeriesDayPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberRecentSeriesDayPO> list = recentSeriesMapper.queryList(item);
        for (FastMemberRecentSeriesDayPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberRecentSeriesDayPO item) {
        return recentSeriesMapper.queryCount(item);
    }

    /**
     * 查询maxId
     */
    @Slave
    public Long queryMaxId() {
        return recentSeriesMapper.queryMaxId();
    }

    /**
     * 查询最大id号
     */
    @Master
    public Long queryTableUnpackList(FastTableUnpackPO item) {
        return recentSeriesMapper.queryTableUnpackList(item);
    }

    /**
     * 查询全部-只读库
     */
    @Slave
    public List<FastMemberRecentSeriesDayPO> querySlaveList(FastMemberRecentSeriesDayPO item) {
        return recentSeriesMapper.querySlaveList(item);
    }

    /**
     * 批量新增-拆表
     */
    @Master
    @Transactional(rollbackFor = Exception.class)
    public int insertBatchTableUnpack(List<FastMemberRecentSeriesDayPO> list, FastTableUnpackPO tableUnpack) {
        return recentSeriesMapper.insertBatchTableUnpack(list, tableUnpack);
    }

    /**
     * 新增
     */
    @Master
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FastMemberRecentSeriesDayPO item) {
        return recentSeriesMapper.insertSelective(item);
    }

    /**
     * 更新
     */
    @Master
    @Transactional(rollbackFor = Exception.class)
    public int updateSeriesFinish(FastMemberRecentSeriesDayPO item) {
        return recentSeriesMapper.updateSeriesFinish(item);
    }
}
