/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberCoinChangeMapper;
import com.fast.mapper.member.FastMemberOrderConsumeMapper;
import com.fast.po.drama.FastDramaI18nPO;
import com.fast.po.member.FastMemberCoinChangePO;
import com.fast.po.member.FastMemberOrderConsumePO;
import com.fast.service.aliyun.AliCdnService;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaI18nService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.thread.LanguageContext;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 订单消费记录
 *
 * <AUTHOR>
 */
@Service
public class FastMemberOrderConsumeService extends BaseService {

    @Autowired
    private FastMemberOrderConsumeMapper consumeMapper;
    @Autowired
    private FastMemberCoinChangeMapper coinChangeMapper;
    @Autowired
    private AliCdnService aliCdnService;
    @Autowired
    private FastDramaI18nService fastDramaI18nService;

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderConsumePO queryById(FastMemberOrderConsumePO item) {
        return consumeMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderConsumePO queryById(Long id) {
        FastMemberOrderConsumePO itemParam = new FastMemberOrderConsumePO();
        itemParam.setId(id);
        return consumeMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderConsumePO queryOne(FastMemberOrderConsumePO item) {
        return consumeMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderConsumePO> queryList(FastMemberOrderConsumePO item) {
        return consumeMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList4Member(FastMemberOrderConsumePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderConsumePO> list = consumeMapper.queryList4Member(item);
        List<Integer> ids = new ArrayList<>();
        for (FastMemberOrderConsumePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getCoinChangeId() != null) {
                ids.add(cur.getCoinChangeId());
            }
            if (StrUtil.isNotEmpty(cur.getDramaCover())) {
                cur.setDramaCover(aliCdnService.getImgFullUrl(cur.getDramaCover()));
            }
            // 国际化
            String languageType = LanguageContext.getLanguageType();
            FastDramaI18nPO i18nPO = fastDramaI18nService.queryByDramaIdAndLangCode(cur.getDramaId(), languageType);
            if (i18nPO != null) {
                cur.setDramaName(i18nPO.getName());
            }
        }
        if (CollUtil.hasContent(ids)) {
            FastMemberCoinChangePO changeQuery = new FastMemberCoinChangePO();
            changeQuery.setIds(StrUtil.join(ids));
            List<FastMemberCoinChangePO> changeList = coinChangeMapper.querySimple(changeQuery);
            if (CollUtil.hasContent(changeList)) {
                Map<Integer, FastMemberCoinChangePO> map = new HashMap<>();
                for (FastMemberCoinChangePO changePO : changeList) {
                    map.put(changePO.getId(), changePO);
                }
                for (FastMemberOrderConsumePO consumePO : list) {
                    if (consumePO.getCoinChangeId() != null) {
                        FastMemberCoinChangePO changePO = map.get(consumePO.getCoinChangeId());
                        if (changePO != null) {
                            consumePO.setChangeType(changePO.getChangeType());
                            consumePO.setCoinChange(changePO.getCoinChange());
                            consumePO.setRemark(changePO.getRemark());
                        }
                    }
                }
            }
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderConsumePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderConsumePO> list = consumeMapper.queryList(item);
        for (FastMemberOrderConsumePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderConsumePO item) {
        return consumeMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderConsumePO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (consumeMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderConsumePO> list) {
        if (consumeMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderConsumePO item) {
        if (consumeMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
