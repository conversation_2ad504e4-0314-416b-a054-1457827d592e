/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberBackMapper;
import com.fast.po.member.FastMemberBackPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberBackService extends BaseService {

    @Autowired
    private FastMemberBackMapper fastMemberBackMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberBackPO queryById(FastMemberBackPO item) {
        return fastMemberBackMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberBackPO queryById(Long id) {
        return fastMemberBackMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberBackPO queryOne(FastMemberBackPO item) {
        return fastMemberBackMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberBackPO> queryList(FastMemberBackPO item) {
        return fastMemberBackMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberBackPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberBackPO> list = fastMemberBackMapper.queryList(item);
        for (FastMemberBackPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberBackPO item) {
        return fastMemberBackMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberBackPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberBackMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberBackPO> list) {
        if (fastMemberBackMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberBackPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberBackMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
