/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberActionCatchMapper;
import com.fast.po.member.FastMemberActionCatchPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberActionCatchService extends BaseService {

    @Autowired
    private FastMemberActionCatchMapper fastMemberActionCatchMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberActionCatchPO queryById(FastMemberActionCatchPO item) {
        return fastMemberActionCatchMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberActionCatchPO queryById(Integer id) {
        return fastMemberActionCatchMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberActionCatchPO queryOne(FastMemberActionCatchPO item) {
        return fastMemberActionCatchMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberActionCatchPO> queryList(FastMemberActionCatchPO item) {
        return fastMemberActionCatchMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberActionCatchPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberActionCatchPO> list = fastMemberActionCatchMapper.queryList(item);
        for (FastMemberActionCatchPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberActionCatchPO item) {
        return fastMemberActionCatchMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberActionCatchPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberActionCatchMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberActionCatchPO> list) {
        if (fastMemberActionCatchMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberActionCatchPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberActionCatchMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
