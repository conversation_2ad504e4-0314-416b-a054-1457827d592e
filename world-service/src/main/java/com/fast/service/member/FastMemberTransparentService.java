/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberTransparentMapper;
import com.fast.po.member.FastMemberTransparentPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberTransparentService extends BaseService {

    @Autowired
    private FastMemberTransparentMapper fastMemberTransparentMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberTransparentPO queryById(FastMemberTransparentPO params) {
        return fastMemberTransparentMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberTransparentPO queryById(Integer id) {
        return fastMemberTransparentMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberTransparentPO queryOne(FastMemberTransparentPO params) {
        return fastMemberTransparentMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberTransparentPO> queryList(FastMemberTransparentPO params) {
        return fastMemberTransparentMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberTransparentPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberTransparentPO> list = fastMemberTransparentMapper.queryList(params);
        for (FastMemberTransparentPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberTransparentPO params) {
        return fastMemberTransparentMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberTransparentPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberTransparentMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberTransparentPO> list) {
        if (fastMemberTransparentMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberTransparentPO params) {
        if (fastMemberTransparentMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
