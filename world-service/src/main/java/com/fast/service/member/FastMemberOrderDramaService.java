/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberOrderDramaMapper;
import com.fast.po.member.FastMemberOrderDramaPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderDramaService extends BaseService {

    @Autowired
    private FastMemberOrderDramaMapper fastMemberOrderDramaMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderDramaPO queryById(FastMemberOrderDramaPO item) {
        return fastMemberOrderDramaMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderDramaPO queryById(Integer id) {
        return fastMemberOrderDramaMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderDramaPO queryOne(FastMemberOrderDramaPO item) {
        return fastMemberOrderDramaMapper.queryOne(item);
    }

    /**
     * 用户的是否有剧卡
     * 返回yes或no
     */
    public String queryOrderDramaRedis(FastMemberOrderDramaPO item) {
        if (item.getMemberId() == null || item.getDramaId() == null) {
            return StaticVar.NO_STR;
        }
        String key = StaticVar.MEMBER_ORDER_DRAMA_V + item.getMemberId() + "_" + item.getDramaId();
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            // 数据库查询
            // FastMemberOrderDramaPO odParam = new FastMemberOrderDramaPO();
            // odParam.setDramaId(item.getDramaId());
            // odParam.setMemberId(item.getMemberId());
            FastMemberOrderDramaPO odPO = fastMemberOrderDramaMapper.queryOne(item);
            if (odPO != null) {
                res = StaticVar.YES_STR;
            } else {
                res = StaticVar.NO_STR;
            }
            RedisUtil.set(key, res, 60 * 60 * 12);
        }

        return res;
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderDramaPO> queryList(FastMemberOrderDramaPO item) {
        return fastMemberOrderDramaMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderDramaPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderDramaPO> list = fastMemberOrderDramaMapper.queryList(item);
        for (FastMemberOrderDramaPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderDramaPO item) {
        return fastMemberOrderDramaMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderDramaPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberOrderDramaMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderDramaPO> list) {
        if (fastMemberOrderDramaMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderDramaPO item) {
        if (fastMemberOrderDramaMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
