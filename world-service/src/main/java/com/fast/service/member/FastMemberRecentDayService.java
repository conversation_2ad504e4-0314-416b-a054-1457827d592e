/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberRecentDayMapper;
import com.fast.po.member.FastMemberRecentDayPO;
import com.fast.po.push.FastPushMessageItemPO;
import com.fast.po.push.FastPushMessagePO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.push.FastPushMessageService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.drama.FastDramaVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户每天最后看的剧集
 *
 * <AUTHOR>
 */
@Service
public class FastMemberRecentDayService extends BaseService {

    @Autowired
    private FastDramaService dramaService;
    @Autowired
    private FastPushMessageService messageService;
    @Autowired
    private FastMemberRecentDayMapper recentDayMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentDayPO queryById(FastMemberRecentDayPO item) {
        return recentDayMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentDayPO queryById(Integer id) {
        FastMemberRecentDayPO itemParam = new FastMemberRecentDayPO();
        itemParam.setId(id);
        return recentDayMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberRecentDayPO queryOne(FastMemberRecentDayPO item) {
        return recentDayMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberRecentDayPO> queryList(FastMemberRecentDayPO item) {
        return recentDayMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberRecentDayPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberRecentDayPO> list = recentDayMapper.queryList(item);
        for (FastMemberRecentDayPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberRecentDayPO item) {
        return recentDayMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberRecentDayPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (recentDayMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberRecentDayPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (recentDayMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 智能推送
     * 给当天超过7小时没有看剧的用户发送看剧提醒
     */
    public void autoPush() {
        // 查询开关
        FastPushMessagePO messageQuery = new FastPushMessagePO();
        messageQuery.setState(1);
        messageQuery.setType(6);
        List<FastPushMessagePO> messageList = messageService.queryList(messageQuery);
        if (CollUtil.isEmpty(messageList)) {
            return;
        }
        for (FastPushMessagePO message : messageList) {
            Date nowTime = DateUtil.getNowDate();
            // 将当前时间减去7小时, 查找当天最后阅读时间比这个小的用户
            Date maxData = DateUtil.addHours(nowTime, -7);
            FastMemberRecentDayPO param = new FastMemberRecentDayPO();
            param.setMaxData(maxData);
            param.setCreateDate(toInteger(DateUtil.getNowTime06Str()));
            param.setState(0);
            param.setOfficialId(message.getOfficialId());
            List<FastMemberRecentDayPO> list = recentDayMapper.queryList(param);
            for (FastMemberRecentDayPO cur : list) {
                FastDramaVO drama = dramaService.queryInfoByRedis(cur.getDramaId());
                if (drama == null) {
                    return;
                }
                if (drama.getShelfState() == 0) {
                    return;
                }
                if (drama.getSeriesNumUpdate() <= cur.getSeriesNum()) {
                    return;
                }
                // 发送消息开始
                // FastPushMessagePO message = new FastPushMessagePO();
                List<FastPushMessageItemPO> itemList = new ArrayList<>(1);
                FastPushMessageItemPO item = new FastPushMessageItemPO();
                item.setContent("亲，你上次看到了《" + cur.getDramaName() + "》第" + cur.getSeriesNum() + "集，后续精彩内容正在等着你~" + StaticVar.CONTINUE_WATCH);
                item.setInteractType(1);
                item.setLinkUrl("/pages/play/play?dramaId=" + cur.getDramaId() + "&seriesNum=" + cur.getSeriesNum() + 1);
                itemList.add(item);
                message.setItemList(itemList);
                message.setMessageType(2);
                if (message.getErrcode() != null && message.getErrcode() == 0) {
                    // 发送成功
                    FastMemberRecentDayPO recentDay = new FastMemberRecentDayPO();
                    recentDay.setMemberId(cur.getMemberId());
                    recentDay.setUpdateTime(nowTime);
                    recentDay.setCreateDate(cur.getCreateDate());
                    recentDay.setState(1);
                    recentDayMapper.updateByMemberIdDay(recentDay);
                }
            }
        }

    }
}
