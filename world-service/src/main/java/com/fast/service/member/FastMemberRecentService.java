/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.member.FastMemberPlayMapper;
import com.fast.mapper.member.FastMemberRecentDayMapper;
import com.fast.mapper.member.FastMemberRecentMapper;
import com.fast.po.member.FastMemberPlayPO;
import com.fast.po.member.FastMemberRecentDayPO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.po.member.FastMemberRecentPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberRecentService extends BaseService {

    @Autowired
    private FastMemberRecentLogService recentLogService;
    @Autowired
    private FastMemberRecentMapper memberRecentMapper;
    @Autowired
    private FastMemberPlayMapper memberPlayMapper;
    @Autowired
    private FastMemberRecentDayMapper recentDayMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentPO queryById(FastMemberRecentPO item) {
        return memberRecentMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentPO queryById(Integer id) {
        FastMemberRecentPO itemParam = new FastMemberRecentPO();
        itemParam.setId(id);
        return memberRecentMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberRecentPO queryOne(FastMemberRecentPO item) {
        return memberRecentMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberRecentPO> queryList(FastMemberRecentPO item) {
        return memberRecentMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberRecentPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberRecentPO> list = memberRecentMapper.queryList(item);
        for (FastMemberRecentPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberRecentPO item) {
        return memberRecentMapper.queryCount(item);
    }

    /**
     * 查询观看历史中未看到最后一集的剧, 随机一个
     */
    public Integer queryRecentDramaNotFinish(FastMemberRecentPO item) {
        return memberRecentMapper.queryRecentDramaNotFinish(item);
    }

    /**
     * 新增最近观看
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO addRecent(FastMemberRecentPO params) {
        if (params.getMemberId() == null) {
            return MethodVO.error("请先登录");
        }
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        // 更新，避免死锁，未使用replace，on duplicate
        if (memberRecentMapper.updateOne(params) == 0) {
            // 就新增一条
            if (memberRecentMapper.insert(params) == 0) {
                // 添加失败
                return MethodVO.error("添加失败");
            }
        }
        // 添加个用户观剧缓存
        String key = StaticVar.MEMBER_RECENT_DRAMA_NUM + params.getMemberId() + "_" + params.getDramaId();
        RedisUtil.set(key, params.getSeriesNum().toString(), RedisUtil.TIME_10D);

        // 添加观看记录，发订阅提醒用
        String keySub = StaticVar.MEMBER_RECENT_SERIESNUM + params.getMemberId() + "_" + params.getDramaId();
        RedisUtil.set(keySub, params.getSeriesNum().toString(), RedisUtil.TIME_30D);
        // 添加短剧播放次数
        FastMemberPlayPO play = new FastMemberPlayPO();
        play.setMemberId(params.getMemberId());
        play.setDramaId(params.getDramaId());
        play.setPlayCount(1);
        if (memberPlayMapper.updatePlusByMemberId(play) == 0) {
            memberPlayMapper.insertSelective(play);
        }

        // 添加按天汇总的需求
        FastMemberRecentDayPO recentDay = new FastMemberRecentDayPO();
        recentDay.setMemberId(params.getMemberId());
        recentDay.setOfficialId(params.getOfficialId());
        recentDay.setDramaId(params.getDramaId());
        recentDay.setSeriesNum(params.getSeriesNum());
        recentDay.setUpdateTime(nowTime);
        recentDay.setCreateDate(toInteger(DateUtil.getNowTime06Str()));
        recentDay.setState(0);// 重置发送条件
        if (recentDayMapper.updateByMemberIdDay(recentDay) == 0) {
            recentDay.setCreateTime(nowTime);
            if (recentDayMapper.insertSelective(recentDay) == 0) {
                throw new MyException("添加失败");
            }
        }

        // 异步添加最近观看记录
        FastMemberRecentLogPO recentLog = new FastMemberRecentLogPO();
        BeanUtils.copyProperties(params, recentLog);
        recentLogService.insertAsync(recentLog);

        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberRecentPO> list) {
        if (memberRecentMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberRecentPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (memberRecentMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
