/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberCoreLoadMapper;
import com.fast.po.member.FastMemberCoreLoadPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberCoreLoadService extends BaseService {

    @Autowired
    private FastMemberCoreLoadMapper fastMemberCoreLoadMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberCoreLoadPO queryById(FastMemberCoreLoadPO params) {
        return fastMemberCoreLoadMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberCoreLoadPO queryById(Integer id) {
        return fastMemberCoreLoadMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberCoreLoadPO queryOne(FastMemberCoreLoadPO params) {
        return fastMemberCoreLoadMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberCoreLoadPO> queryList(FastMemberCoreLoadPO params) {
        return fastMemberCoreLoadMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberCoreLoadPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberCoreLoadPO> list = fastMemberCoreLoadMapper.queryList(params);
        for (FastMemberCoreLoadPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberCoreLoadPO params) {
        return fastMemberCoreLoadMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberCoreLoadPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberCoreLoadMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    @Async
    public void insertAsync(FastMemberCoreLoadPO params) {
        String key = StaticVar.MEMBER_CORE_LOAD + params.getMemberId();
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            Date nowTime = DateUtil.getNowDate();
            params.setCreateTime(nowTime);
            fastMemberCoreLoadMapper.insertSelective(params);
            RedisUtil.set(key, "exist", 60 * 5);// 缓存5分钟
        }
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberCoreLoadPO> list) {
        if (fastMemberCoreLoadMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberCoreLoadPO params) {
        if (fastMemberCoreLoadMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
