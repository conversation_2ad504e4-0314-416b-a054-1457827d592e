/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.annotation.Master;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.datalake.DataLakeRecentLogMapper;
import com.fast.mapper.member.FastMemberRecentLogMapper;
import com.fast.mapper.member.FastMemberRecentLogPlayMapper;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.mapper.promote.FastMemberLinkMapper;
import com.fast.po.common.FastTableUnpackPO;
import com.fast.po.member.FastMemberOrderDramaPO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.po.member.FastMemberRecentLogPlayPO;
import com.fast.po.member.FastMemberUnlockDramaPO;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.promote.FastMemberLinkPO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaSeriesService;
import com.fast.service.drama.FastDramaService;
import com.fast.service.fee.FastFeeRuleService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.promote.FastMemberLinkService;
import com.fast.service.unlock.FastMemberUnlockDramaService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.JedisLock;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.thread.ContentTypeContext;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.drama.FastDramaSeriesVO;
import com.fast.vo.drama.FastDramaVO;
import com.fast.vo.fee.FastFeeRuleVO;
import com.fast.vo.member.MemberAccountVO;
import com.fast.vo.mini.FastMiniVO;
import com.fast.vo.promote.FastLinkVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberRecentLogService extends BaseService {

    @Autowired
    private FastMemberRecentLogMapper recentLogMapper;
    @Autowired
    private FastMemberLinkMapper memberLinkMapper;
    @Autowired
    private FastFeeRuleService feeRuleService;
    @Autowired
    private FastMemberAccountService accountService;
    @Autowired
    private FastDramaSeriesService seriesService;
    @Autowired
    private FastMemberUnlockDramaService unlockDramaService;
    @Autowired
    private FastMemberDramaFirstWatchService firstWatchService;
    @Autowired
    private FastMiniMapper fastMiniMapper;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastDramaService fastDramaService;
    @Autowired
    private FastMemberRecentLogPlayMapper fastMemberRecentLogPlayMapper;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMemberOrderDramaService fastMemberOrderDramaService;
    @Autowired
    private DataLakeRecentLogMapper dataLakeRecentLogMapper;
    @Autowired
    private FastMemberLinkService fastMemberLinkService;

    private static final Map<Integer, Integer> miniMap = new HashMap<>();

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentLogPO queryById(FastMemberRecentLogPO item) {
        return recentLogMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentLogPO queryById(Long id) {
        return recentLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberRecentLogPO queryOne(FastMemberRecentLogPO item) {
        return recentLogMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberRecentLogPO> queryList(FastMemberRecentLogPO item) {
        return recentLogMapper.queryList(item);
    }

    /**
     * 查询用户id
     */
    public List<Long> queryMemberIds(FastMemberRecentLogPO item) {
        return recentLogMapper.queryMemberIds(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberRecentLogPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberRecentLogPO> list = dataLakeRecentLogMapper.queryRecentLogPage(item);
        for (FastMemberRecentLogPO cur : list) {
            FastLinkVO fastLinkVO = fastLinkService.queryInfoByRedis(cur.getLinkId());
            FastMiniVO fastMiniVO = fastMiniService.queryInfoByRedis(cur.getMiniId());
            FastDramaVO fastDramaVO = fastDramaService.queryInfoByRedis(cur.getDramaId());
            cur.setEncryptionId(encode(cur.getId()));
            if (StrUtil.isEmpty(cur.getDramaName()) && fastDramaVO != null) {
                cur.setDramaName(fastDramaVO.getDramaName());
            }
            if (StrUtil.isEmpty(cur.getLinkName()) && fastLinkVO != null) {
                cur.setLinkName(fastLinkVO.getLinkName());
            }
            if (StrUtil.isEmpty(cur.getMiniName()) && fastMiniVO != null) {
                cur.setMiniName(fastMiniVO.getMiniName());
            }
            if (Objects.nonNull(fastMiniVO)) {
                cur.setMiniType(fastMiniVO.getType());
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 投放平台-获取观看记录)
     */
    public ResultVO<?> queryListFor601Drama(FastMemberRecentLogPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberRecentLogPO> list = recentLogMapper.queryListFor601Drama(item);
        for (FastMemberRecentLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberRecentLogPO item) {
        return recentLogMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberRecentLogPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (recentLogMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    // 下面五个方法, 不要私自使用-----------------------------------------------------

    /**
     * 批量新增-拆表
     */
    @Master
    @Transactional(rollbackFor = Exception.class)
    public int insertBatchTableUnpack(List<FastMemberRecentLogPO> list, FastTableUnpackPO tableUnpack) {
        return recentLogMapper.insertBatchTableUnpack(list, tableUnpack);
    }

    /**
     * 查询全部-只读库
     */
    @Slave
    public List<FastMemberRecentLogPO> querySlaveList(FastMemberRecentLogPO item) {
        return recentLogMapper.querySlaveList(item);
    }

    /**
     * 查询全部-只读库
     */
    @Slave
    public List<FastMemberRecentLogPO> querySlaveSimpleList(FastMemberRecentLogPO item) {
        return recentLogMapper.querySlaveSimpleList(item);
    }

    /**
     * 查询用户在指定日期对某剧是否完播-只读库
     */
    @Slave
    public int querySlaveDramaFinish(FastMemberRecentLogPO item) {
        return recentLogMapper.querySlaveDramaFinish(item);
    }

    /**
     * 查询最大id号
     */
    @Master
    public Long queryTableUnpackList(FastTableUnpackPO item) {
        return recentLogMapper.queryTableUnpackList(item);
    }
    // 上面五个方法, 不要私自使用-----------------------------------------------------

    // 计算完播状态的减免时长(秒)
    private static final int PLAY_SECOND_SPARE = 3;

    /**
     * 新增最近观看
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void insertAsync(FastMemberRecentLogPO params) {
        ContentTypeContext.setContentType(params.getContentType());
        Date nowTime = DateUtil.getNowDate();
        FastFeeRuleVO feeRuleVO = null;
        if (params.getLinkId() != null && params.getLinkId() > 0) {
            FastLinkPO linkPO = fastLinkService.queryInfoByRedis(params.getLinkId());
            if (linkPO.getStartNumGlobal() == 0) {
                // 自定义
                feeRuleVO = feeRuleService.queryLinkByRedis(params.getLinkId(), params.getDramaId());
            }
        }
        if (feeRuleVO == null) {
            feeRuleVO = feeRuleService.queryInfoByRedis(params.getOfficialId(), params.getDramaId());
            if (feeRuleVO == null) {
                feeRuleVO = feeRuleService.queryInfoByRedis(0, params.getDramaId());
                if (feeRuleVO == null) {
                    return;
                }
            }
        }
        // 是否收费剧集
        boolean isFee = params.getSeriesNum() >= feeRuleVO.getStartNum();
        // 判断当前剧集是否是免费/付费
        if (isFee) {
            params.setPayRule(1);// 付费规则: 1=付费;0=免费
        } else {
            params.setPayRule(0);
        }
        MemberAccountVO accountVO = accountService.queryInfoByRedis(params.getMemberId());
        // 查询购买的剧卡
        FastMemberOrderDramaPO odParam = new FastMemberOrderDramaPO();
        odParam.setDramaId(params.getDramaId());
        odParam.setMemberId(params.getMemberId());
        String buyDramaStr = fastMemberOrderDramaService.queryOrderDramaRedis(odParam);
        // 判断vip到期
        if (accountVO.getVipState() == 1) {
            if (params.getSeriesNum() >= feeRuleVO.getStartNum()) {
                params.setPayResult(2);// 付费情况: 0=免费;1=K币消费;2=vip免费看;3=解锁后重复看
            } else {
                params.setPayResult(0);
            }
        } else if (StaticVar.YES_STR.equals(buyDramaStr)) {
            if (params.getSeriesNum() >= feeRuleVO.getStartNum()) {
                params.setPayResult(5);// 付费情况: 0=免费;1=K币消费;2=vip免费看;3=解锁后重复看;4=广告解锁观看;5=剧卡免费看
            } else {
                params.setPayResult(0);
            }
        } else {
            if (isFee) {
                // 查看当前剧的解锁状态码是否被消耗(只能消耗一次)
                // 观看剧集日志剧集的一把锁(同一用户不能同时操作)
                final JedisLock lock = new JedisLock(StaticVar.A_ADD_RECENT_LOG_LOCK + params.getMemberId());
                try {
                    if (!lock.lock()) {
                        return;
                    }
                    String key = StaticVar.DRAMA_HAS_UNLOCK + params.getMemberId() + "_" + params.getDramaId() + "_" + params.getSeriesNum();
                    String value = RedisUtil.get(key);
                    if (notBlank(value)) {
                        // 第一次使用, 1=K币消费
                        params.setPayResult(1);
                        params.setCoinConsume(toInteger(value));
                        RedisUtil.del(key);
                    } else {
                        // 判断是否解锁过
                        List<Integer> unlockIds = unlockDramaService.queryInfoByRedis(params.getMemberId(), params.getDramaId());
                        if (unlockIds == null || !unlockIds.contains(params.getSeriesNum())) {
                            log.error("短剧未解锁不能观看:M{},D{},S{}", params.getMemberId(), params.getDramaId(), params.getSeriesNum());
                            return;
                        }
                        // 付费情况: 3=解锁后重复看
                        params.setPayResult(3);
                    }
                    FastMiniPO fastMini = fastMiniMapper.queryById(params.getMiniId());
                    if (fastMini != null && fastMini.getType() != null) {
                        actionLogService.log("tiktok_ad_reward", "insert recent log:请求参数" + JSON.toJSON(params));
                        FastMemberUnlockDramaPO adRewardUnlock = new FastMemberUnlockDramaPO();
                        adRewardUnlock.setDramaId(params.getDramaId());
                        adRewardUnlock.setSeriesNum(params.getSeriesNum());
                        adRewardUnlock.setType(1);
                        adRewardUnlock.setMemberId(params.getMemberId());
                        int count = unlockDramaService.queryCount(adRewardUnlock);
                        actionLogService.log("tiktok_ad_reward", "insert recent log:SQL查询参数" + JSON.toJSON(adRewardUnlock));
                        actionLogService.log("tiktok_ad_reward", "insert recent log:SQL查询结果" + count);
                        if (count > 0) {
                            params.setPayResult(4);
                        }
                    }
                } catch (Exception e) {
                    log.error("error:", e);
                    throw new RuntimeException(e);
                } finally {
                    lock.release();
                }
            } else {
                // 付费情况: 0=免费
                params.setPayResult(0);
            }
        }
        // 计算完播状态
        if (params.getPlaySecondAccurate() != null && params.getPlaySecondAccurate().compareTo(BigDecimal.ZERO) > 0) {
            FastDramaSeriesVO seriesVO = seriesService.queryInfoByIDRedis(params.getSeriesId());
            if (seriesVO != null && seriesVO.getSeriesTime() != null && params.getPlaySecond() >= (seriesVO.getSeriesTime() - PLAY_SECOND_SPARE)) {
                params.setPlayState(1);// 播放状态:1=完播;0=未完播
            }
        }
        params.setCreateTime(nowTime);
        Date lastLinkTime = memberLinkMapper.queryLastLinkTimeDate(params.getMemberId());
        params.setLinkTime(defaultIfNull(lastLinkTime, nowTime));

        // 添加素材id、计划id、项目id
        FastMemberLinkPO mlPO = fastMemberLinkService.queryAdvByMemberIdRedis(params.getMemberId());
        if (mlPO != null) {
            params.setMaterialId(mlPO.getMaterialId());
            params.setPromotionId(mlPO.getPromotionId());
            params.setProjectId(mlPO.getProjectId());
            actionLogService.log("recent_log_mid3", "发现素材id" + params.getMemberId());

        }
        if (recentLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
        }
        // 判断是否投放剧
        FastDramaVO dramaVO = fastDramaService.queryInfoByRedis(params.getDramaId());
        if (dramaVO != null && dramaVO.getDramaType() != null && dramaVO.getDramaType() == 3) {
            // 小程序
            if (miniMap == null || miniMap.get(params.getMiniId()) == null) {
                // 查询小程序列表
                FastMiniPO miniParam = new FastMiniPO();
                List<FastMiniPO> miniList = fastMiniMapper.queryList(miniParam);
                for (FastMiniPO miniPO : miniList) {
                    Integer playType = 0;
                    if (miniPO.getType() == 1) {
                        playType = 1;
                    } else if (miniPO.getType() == 2) {
                        playType = 2;
                    } else if (miniPO.getType() == 4) {
                        playType = 3;
                    }
                    miniMap.put(miniPO.getId(), playType);
                }
            }
            Integer playType = miniMap.get(params.getMiniId());
            if (playType != null && playType > 0) {
                // 投放剧需要关联播放源
                FastMemberRecentLogPlayPO lpp = new FastMemberRecentLogPlayPO();
                lpp.setId(params.getId());
                lpp.setPlayType(playType);
                lpp.setCreateTime(nowTime);
                fastMemberRecentLogPlayMapper.insertSelective(lpp);
            }
        }
        // C端用户首次观看日志清洗
        firstWatchService.insertFirstWatchAsync(params);
        ContentTypeContext.clear();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberRecentLogPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (recentLogMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

}
