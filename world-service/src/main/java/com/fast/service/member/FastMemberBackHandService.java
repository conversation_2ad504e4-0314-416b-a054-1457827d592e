/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.framework.exception.MyException;
import com.fast.mapper.member.FastMemberBackHandMapper;
import com.fast.mapper.member.FastMemberBackMapper;
import com.fast.mapper.tablestore.FastTablestoreBackMapper;
import com.fast.po.member.FastMemberBackHandPO;
import com.fast.service.base.BaseService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.member.OrderBackVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberBackHandService extends BaseService {

    @Autowired
    private FastMemberBackHandMapper fastMemberBackHandMapper;
    @Lazy
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastTablestoreBackMapper fastTablestoreBackMapper;
    @Autowired
    private FastMemberBackMapper fastMemberBackMapper;

    /**
     * 新增一条待回传记录
     */
    public void insertMemberBackHand(Integer linkId, Long memberId, String clickId, String promotionId) {
        try {
            FastMemberBackHandPO bhPO = new FastMemberBackHandPO();
            bhPO.setLinkId(linkId);
            bhPO.setMemberId(memberId);
            bhPO.setClickId(clickId);
            bhPO.setPromotionId(promotionId);
            Date timeNow = DateUtil.getNowDate();
            bhPO.setCreateTime(timeNow);
            bhPO.setUpdateTime(timeNow);
            fastMemberBackHandMapper.insertSelective(bhPO);
        } catch (Exception e) {
            log.error("添加手动记录失败");
        }
    }

    /**
     * 更新订单数量
     */
    public void updateOrderCount(Integer linkId, Long memberId) {
        if (linkId != null && memberId != null) {
            FastMemberBackHandPO bhPO = new FastMemberBackHandPO();
            bhPO.setLinkId(linkId);
            bhPO.setMemberId(memberId);
            Date timeNow = DateUtil.getNowDate();
            bhPO.setUpdateTime(timeNow);
            if (fastMemberBackHandMapper.updateOrderCount(bhPO) > 0) {
                actionLogService.log("back_hand_count", "成功,linkId=" + linkId + ",memberId=" + memberId);
            } else {
                actionLogService.log("back_hand_count", "失败,linkId=" + linkId + ",memberId=" + memberId);
            }
        }
    }

    /**
     * 更新回传状态
     */
    public void updateOrderBack(OrderBackVO obVO) {
        FastMemberBackHandPO bhPO = new FastMemberBackHandPO();
        bhPO.setLinkId(obVO.getLinkId());
        bhPO.setMemberId(obVO.getMemberId());
        Date timeNow = DateUtil.getNowDate();
        bhPO.setUpdateTime(timeNow);
        bhPO.setBackState(obVO.getBackState());
        bhPO.setBackInfo(obVO.getBackInfo());
        bhPO.setBackAuto(obVO.getBackAuto());
        fastMemberBackHandMapper.updateBackInfo(bhPO);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberBackHandPO queryById(FastMemberBackHandPO params) {
        return fastMemberBackHandMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberBackHandPO queryById(Integer id) {
        return fastMemberBackHandMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberBackHandPO queryOne(FastMemberBackHandPO params) {
        return fastMemberBackHandMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberBackHandPO> queryList(FastMemberBackHandPO params) {
        return fastMemberBackHandMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberBackHandPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberBackHandPO> list = fastMemberBackHandMapper.queryList(params);
        // 推广链接信息

        for (FastMemberBackHandPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }


    /**
     * 查询总数
     */
    public int queryCount(FastMemberBackHandPO params) {
        return fastMemberBackHandMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberBackHandPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberBackHandMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberBackHandPO> list) {
        if (fastMemberBackHandMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberBackHandPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberBackHandMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public void manualCallback(Integer updatorId, Integer id, BigDecimal moneyRecharge) {
        FastMemberBackHandPO FastMemberBackHandPO = fastMemberBackHandMapper.queryById(id);
        if (Objects.isNull(FastMemberBackHandPO)) {
            throw new MyException("回传记录不存在!");
        }
        if (StrUtil.isBlank(FastMemberBackHandPO.getClickId())) {
            throw new MyException("clickId为空!");
        }
        if (FastMemberBackHandPO.getBackState() != 1) {
            throw new MyException("不是未回传状态不可操作!");
        }

        Integer backState = 2;
        String backInfo = "手工回传";
        Integer backAuto = 2; // 手动回传

        // 更新回传状态
        int update = fastMemberBackHandMapper.updateBackStatusById(id, updatorId, moneyRecharge, backState, backInfo, backAuto);
        if (update == 0) {
            throw new MyException("状态更新失败!");
        }
    }

}
