/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberBackViewMapper;
import com.fast.po.member.FastMemberBackViewPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberBackViewService extends BaseService {

    @Autowired
    private FastMemberBackViewMapper fastMemberBackViewMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberBackViewPO queryById(FastMemberBackViewPO params) {
        return fastMemberBackViewMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberBackViewPO queryById(Integer id) {
        return fastMemberBackViewMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberBackViewPO queryOne(FastMemberBackViewPO params) {
        return fastMemberBackViewMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberBackViewPO> queryList(FastMemberBackViewPO params) {
        return fastMemberBackViewMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberBackViewPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberBackViewPO> list = fastMemberBackViewMapper.queryList(params);
        for (FastMemberBackViewPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberBackViewPO params) {
        return fastMemberBackViewMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberBackViewPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberBackViewMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberBackViewPO> list) {
        if (fastMemberBackViewMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberBackViewPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberBackViewMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
