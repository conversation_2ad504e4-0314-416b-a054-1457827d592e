/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberSubLinkHisMapper;
import com.fast.po.member.FastMemberSubLinkHisPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberSubLinkHisService extends BaseService {

    @Autowired
    private FastMemberSubLinkHisMapper fastMemberSubLinkHisMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberSubLinkHisPO queryById(FastMemberSubLinkHisPO item) {
        return fastMemberSubLinkHisMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberSubLinkHisPO queryById(Integer id) {
        return fastMemberSubLinkHisMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberSubLinkHisPO queryOne(FastMemberSubLinkHisPO item) {
        return fastMemberSubLinkHisMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberSubLinkHisPO> queryList(FastMemberSubLinkHisPO item) {
        return fastMemberSubLinkHisMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberSubLinkHisPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberSubLinkHisPO> list = fastMemberSubLinkHisMapper.queryList(item);
        for (FastMemberSubLinkHisPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberSubLinkHisPO item) {
        return fastMemberSubLinkHisMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberSubLinkHisPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberSubLinkHisMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberSubLinkHisPO> list) {
        if (fastMemberSubLinkHisMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberSubLinkHisPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMemberSubLinkHisMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
