/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberOrderRechargeDiamondMapper;
import com.fast.po.member.FastMemberOrderRechargeDiamondPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderRechargeDiamondService extends BaseService {

    @Autowired
    private FastMemberOrderRechargeDiamondMapper fastMemberOrderRechargeDiamondMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeDiamondPO queryById(FastMemberOrderRechargeDiamondPO params) {
        return fastMemberOrderRechargeDiamondMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeDiamondPO queryById(Integer id) {
        return fastMemberOrderRechargeDiamondMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderRechargeDiamondPO queryOne(FastMemberOrderRechargeDiamondPO params) {
        return fastMemberOrderRechargeDiamondMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderRechargeDiamondPO> queryList(FastMemberOrderRechargeDiamondPO params) {
        return fastMemberOrderRechargeDiamondMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderRechargeDiamondPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderRechargeDiamondPO> list = fastMemberOrderRechargeDiamondMapper.queryList(params);
        for (FastMemberOrderRechargeDiamondPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderRechargeDiamondPO params) {
        return fastMemberOrderRechargeDiamondMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderRechargeDiamondPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberOrderRechargeDiamondMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderRechargeDiamondPO> list) {
        if (fastMemberOrderRechargeDiamondMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderRechargeDiamondPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberOrderRechargeDiamondMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
