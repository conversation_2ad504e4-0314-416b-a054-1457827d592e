/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticVar;
import com.fast.enums.ContentTypeEnum;
import com.fast.enums.MiniTypeEnum;
import com.fast.framework.thread.ExportThreadExecutor;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.common.FastActionLogService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单充值记录
 *
 * <AUTHOR>
 */
@Service
public class FastMemberOrderRechargeExportService extends BaseService {

    @Autowired
    private FastMemberOrderRechargeService dataService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastActionLogService fastActionLogService;

    /**
     * 订单充值记录-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    @Async(ExportThreadExecutor.NAME)
    public void exportMemberRechargeList(SessionVO sessionVO, FastMemberOrderRechargePO params) {
        long logId = System.currentTimeMillis();
        long startTime = System.currentTimeMillis();
        int size = dataService.queryListCount(params);
        fastActionLogService.log("order_recharge_export", logId + "订单导出查询总数量耗时:" + (System.currentTimeMillis() - startTime) + "size:" + size);
        startTime = System.currentTimeMillis();
        Long maxId = null; // 最大id
        List<FastMemberOrderRechargePO> list = new ArrayList<>(size + 10);
        params.setLimitExport(StaticVar.DATA_2K);
        do {
            params.setMaxId(maxId);
            List<FastMemberOrderRechargePO> temp = dataService.queryList(params);
            if (CollUtil.hasContent(temp)) {
                list.addAll(temp);

                maxId = temp.get(temp.size() - 1).getId();
            }
            if (temp.size() < StaticVar.DATA_2K) {
                break;
            }
        } while (true);
        fastActionLogService.log("order_recharge_export", logId + "订单导出数据列表查询总耗时:" + (System.currentTimeMillis() - startTime));
        if (CollUtil.isEmpty(list)) {
            // 清空导出的频次限制
            RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            return; // 表示数据为空
        }
        startTime = System.currentTimeMillis();
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberOrderRechargePO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getOutTransId());
            CollUtil.addNoRepeat(rowHeadNames, "商户交易单号");

            switch (cur.getOrderType()) {
                case 1:
                    row.add(cur.getMoneyRecharge() + "元充值");
                    break;
                case 2:
                    row.add(cur.getModelDetailName());
                    break;
                default:
                    row.add("");
                    break;
            }
            CollUtil.addNoRepeat(rowHeadNames, "商品名称");

            row.add(cur.getTermOrdId());
            CollUtil.addNoRepeat(rowHeadNames, "平台订单号");

            row.add(cur.getMemberId());
            CollUtil.addNoRepeat(rowHeadNames, "用户ID");

            row.add(cur.getPaySequence());
            CollUtil.addNoRepeat(rowHeadNames, "充值次数");

            row.add(cur.getMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "充值金额");

            row.add(cur.getPayDramaSequence());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "充值次数");

            row.add(cur.getContractOrderId());
            CollUtil.addNoRepeat(rowHeadNames, "签约单号");
            row.add(cur.getContractTemplateId());
            CollUtil.addNoRepeat(rowHeadNames, "签约模版id");
            row.add(cur.getContractNum());
            CollUtil.addNoRepeat(rowHeadNames, "代扣期数");

            row.add(cur.getPayFeeRate() + "%");
            CollUtil.addNoRepeat(rowHeadNames, "通道费");

            row.add(cur.getMoneyProfit());
            CollUtil.addNoRepeat(rowHeadNames, "实际到账金额");

            if (cur.getPayType() != null) {
                switch (cur.getPayType()) {
                    case 10:
                        row.add("微信");
                        break;
                    case 20:
                        row.add("支付宝");
                        break;
                    case 200:
                        row.add("抖音");
                        break;
                    case 60:
                        row.add("抖音钻石");
                        break;
                    default:
                        row.add("其他");
                        break;
                }
            } else {
                row.add("其他");
            }
            CollUtil.addNoRepeat(rowHeadNames, "支付方式");

            if (cur.getOrderType() == 1) {
                row.add("K币充值");
            } else if (cur.getOrderType() == 2) {
                row.add("VIP卡充值");
            } else if (cur.getOrderType() == 3) {
                row.add("免费赠送K币");
            } else if (cur.getOrderType() == 4) {
                row.add("剧卡充值");
            } else {
                row.add("未知");
            }
            CollUtil.addNoRepeat(rowHeadNames, "充值类型");

            if (cur.getLinkSubType() != null && cur.getLinkSubType() == 1) {
                row.add("星图推广");
            } else if (cur.getLinkSubType() != null && cur.getLinkSubType() == 2) {
                row.add("小程序推广");
            } else if (cur.getLinkSubType() != null && cur.getLinkSubType() == 3) {
                row.add("作品挂载");
            } else if (cur.getLinkSubType() != null && cur.getLinkSubType() == 4) {
                row.add("聚星推广");
            } else {
                row.add("");
            }
            CollUtil.addNoRepeat(rowHeadNames, "挂载链接类型");

            row.add(cur.getState() == 1 ? "已支付" : "已取消");
            CollUtil.addNoRepeat(rowHeadNames, "支付状态");

            row.add(cur.getDiamond());
            CollUtil.addNoRepeat(rowHeadNames, "抖音钻石抵扣");


            row.add(cur.getDemandId());
            CollUtil.addNoRepeat(rowHeadNames, "挂载需求id");

            row.add(cur.getPayForm() == 8 || cur.getPayForm() == 9 ? "虚拟支付" : "普通支付");
            CollUtil.addNoRepeat(rowHeadNames, "支付类型");

            if (sessionVO.getRetailId() == 0) {
                row.add(cur.getRetailName());
                CollUtil.addNoRepeat(rowHeadNames, "所属分销商");
            }

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "应用");

            row.add(MiniTypeEnum.getName(defaultIfNull(cur.getMiniType(), 0)));
            CollUtil.addNoRepeat(rowHeadNames, "应用类型");

            row.add(cur.getOfficialName());
            CollUtil.addNoRepeat(rowHeadNames, "公众号");

            row.add(DateUtil.format07(cur.getRegisterTime()));
            CollUtil.addNoRepeat(rowHeadNames, "注册时间");

            row.add(DateUtil.format07(cur.getPayTime()));
            CollUtil.addNoRepeat(rowHeadNames, "交易时间");

            row.add(cur.getDramaName());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "名称");

            row.add((cur.getSeriesNum() == null || cur.getSeriesNum() == 0) ? '-' : cur.getSeriesNum());
            CollUtil.addNoRepeat(rowHeadNames, "充值剧集");

            row.add(cur.getPhoneOs() == 1 ? "Androids系统" : cur.getPhoneOs() == 2 ? "ios系统" : "未知");
            CollUtil.addNoRepeat(rowHeadNames, "手机系统");

            row.add(StrUtil.isEmpty(cur.getScene()) ? "-" : cur.getScene() + "-" + cur.getSceneContent());
            CollUtil.addNoRepeat(rowHeadNames, "场景值");

            row.add(StrUtil.isEmpty(cur.getScene()) ? "-" : cur.getScene());
            CollUtil.addNoRepeat(rowHeadNames, "场景值id");

            row.add(cur.getLinkId());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接ID");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接名称");

            row.add(cur.getSubLinkId());
            CollUtil.addNoRepeat(rowHeadNames, "次归属推广链接ID");

            row.add(cur.getSubLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "次归属推广链接名称");

            // 微信视频号
            if (biggerZero(cur.getLinkSubType()) && cur.getLinkSubType() == 5) {
                row.add(cur.getWxFinderName());
                CollUtil.addNoRepeat(rowHeadNames, "微信视频号名称");

                row.add(cur.getWxDramaId());
                CollUtil.addNoRepeat(rowHeadNames, "微信视频号内容ID");

                row.add(cur.getWxPromotionId());
                CollUtil.addNoRepeat(rowHeadNames, "加热订单ID");

                row.add("微信视频号推广");
                CollUtil.addNoRepeat(rowHeadNames, "微信推广类型");
            }

            row.add(cur.getLinkEntryPage());
            CollUtil.addNoRepeat(rowHeadNames, "入口页面");

            row.add(cur.getAdvUserName());
            CollUtil.addNoRepeat(rowHeadNames, "优化师姓名");

            // if (sessionVO.getRetailId() > 0) {
            row.add(cur.getBackState() == 1 ? "未回传" : cur.getBackState() == 2 ? "回传成功" : cur.getBackState() == 3 ? "回传失败" : "未知");
            CollUtil.addNoRepeat(rowHeadNames, "回传状态");

            row.add(cur.getBackInfo());
            CollUtil.addNoRepeat(rowHeadNames, "上报信息");

            row.add("'" + toString(cur.getAid()));
            CollUtil.addNoRepeat(rowHeadNames, "广告计划id");

            row.add("'" + toString(cur.getCid()));
            CollUtil.addNoRepeat(rowHeadNames, "广告创意id");

            row.add("'" + toString(cur.getPromotionId()));
            CollUtil.addNoRepeat(rowHeadNames, "广告id");

            row.add("'" + toString(cur.getProjectId()));
            CollUtil.addNoRepeat(rowHeadNames, "项目id");
            // }

            row.add(DateUtil.format07(cur.getLinkTime()));
            CollUtil.addNoRepeat(rowHeadNames, "染色时间");

            row.add("`" + toString(cur.getDemandId()));
            CollUtil.addNoRepeat(rowHeadNames, "任务ID");

            row.add(cur.getWxFinderId());
            CollUtil.addNoRepeat(rowHeadNames, "视频号ID");

            row.add(cur.getWxFinderName());
            CollUtil.addNoRepeat(rowHeadNames, "视频号名称");

            row.add(cur.getFinderUserName());
            CollUtil.addNoRepeat(rowHeadNames, "视频号归属人");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return;// 您选择的导出列至少要有2项
        }
        String title = "订单管理-充值明细";
        fastActionLogService.log("order_recharge_export", logId + "订单导出数据组装导出数据耗时:" + (System.currentTimeMillis() - startTime));
        sessionVO.setExportKeyId(logId);
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, params.getExportKey());
        fastActionLogService.log("order_recharge_export", logId + "订单导出数据总耗时:" + (System.currentTimeMillis() - logId));

        // 清空导出的频次限制
        RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
    }
}
