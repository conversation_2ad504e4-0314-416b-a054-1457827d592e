/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberOrderRechargeAdvMapper;
import com.fast.po.member.FastMemberOrderRechargeAdvPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderRechargeAdvService extends BaseService {

    @Autowired
    private FastMemberOrderRechargeAdvMapper fastMemberOrderRechargeAdvMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeAdvPO queryById(FastMemberOrderRechargeAdvPO params) {
        return fastMemberOrderRechargeAdvMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeAdvPO queryById(Integer id) {
        return fastMemberOrderRechargeAdvMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderRechargeAdvPO queryOne(FastMemberOrderRechargeAdvPO params) {
        return fastMemberOrderRechargeAdvMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderRechargeAdvPO> queryList(FastMemberOrderRechargeAdvPO params) {
        return fastMemberOrderRechargeAdvMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderRechargeAdvPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderRechargeAdvPO> list = fastMemberOrderRechargeAdvMapper.queryList(params);
        for (FastMemberOrderRechargeAdvPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderRechargeAdvPO params) {
        return fastMemberOrderRechargeAdvMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderRechargeAdvPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberOrderRechargeAdvMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderRechargeAdvPO> list) {
        if (fastMemberOrderRechargeAdvMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderRechargeAdvPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberOrderRechargeAdvMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
