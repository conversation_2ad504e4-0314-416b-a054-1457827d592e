/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberYoungMapper;
import com.fast.mapper.member.FastMemberYoungResetMapper;
import com.fast.po.member.FastMemberYoungPO;
import com.fast.po.member.FastMemberYoungResetPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberYoungService extends BaseService {

    @Autowired
    private FastMemberYoungMapper fastMemberYoungMapper;
    @Autowired
    private FastMemberYoungResetMapper fastMemberYoungResetMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberYoungPO queryById(FastMemberYoungPO params) {
        return fastMemberYoungMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberYoungPO queryById(Long id) {
        return fastMemberYoungMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberYoungPO queryOne(FastMemberYoungPO params) {
        return fastMemberYoungMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberYoungPO> queryList(FastMemberYoungPO params) {
        return fastMemberYoungMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberYoungPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberYoungPO> list = fastMemberYoungMapper.queryList(params);
        for (FastMemberYoungPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberYoungPO params) {
        return fastMemberYoungMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberYoungPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberYoungMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberYoungPO> list) {
        if (fastMemberYoungMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberYoungPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberYoungMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 设置青少年模式
     */
    public ResultVO<?> updateYoungFlag(FastMemberYoungPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);

        FastMemberYoungPO youngPO = fastMemberYoungMapper.queryById(params.getId());
        // 不存在则新增
        if (youngPO == null) {
            params.setCreateTime(nowTime);
            fastMemberYoungMapper.insertSelective(params);
        }
        // 更新
        if (youngPO != null) {
            // 关闭青少年模式
            if (params.getYoungFlag() == 0) {
                // 校验密码是否正确
                if (!youngPO.getYoungPassword().equals(params.getYoungPassword())) {
                    return ResultVO.error(500, StaticStr.WRONG_PASSWORD);
                }
            }
            fastMemberYoungMapper.updateById(params);
        }
        return ResultVO.success();
    }

    /**
     * 重置青少年密码
     */
    public ResultVO<?> resetYoungPassword(FastMemberYoungPO params, SessionVO sessionVO) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        params.setYoungPassword(Md5Util.getMD5BySalt("8888"));

        FastMemberYoungPO youngPO = fastMemberYoungMapper.queryById(params.getId());
        // 不存在则新增
        if (youngPO == null) {
            params.setCreateTime(nowTime);
            params.setYoungFlag(0);
            fastMemberYoungMapper.insertSelective(params);
        } else {
            params.setYoungFlag(null);
            fastMemberYoungMapper.updateById(params);
        }
        // 新增重置记录
        FastMemberYoungResetPO resetPO = new FastMemberYoungResetPO();
        resetPO.setMemberId(params.getId());
        resetPO.setCreatorId(sessionVO.getUserId());
        resetPO.setCreateTime(nowTime);
        resetPO.setUpdatorId(sessionVO.getUserId());
        resetPO.setUpdateTime(nowTime);
        resetPO.setYoungFlag(params.getYoungFlag());
        resetPO.setRemark(params.getRemark());
        fastMemberYoungResetMapper.insertSelective(resetPO);
        return ResultVO.success();
    }
}
