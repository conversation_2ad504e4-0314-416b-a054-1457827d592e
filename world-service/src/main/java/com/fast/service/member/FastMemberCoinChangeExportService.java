/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.member.FastMemberCoinChangePO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 调币记录
 *
 * <AUTHOR>
 */
@Service
public class FastMemberCoinChangeExportService extends BaseService {

    @Autowired
    private FastMemberCoinChangeService dataService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 调币记录-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportMemberCoinChangeList(SessionVO sessionVO, FastMemberCoinChangePO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_COIN_CHANGE_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        List<FastMemberCoinChangePO> list = dataService.queryList(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        if (params.getCoinType() == 1) {
            for (FastMemberCoinChangePO cur : list) {
                List<Object> row = new ArrayList<>();// 导出的数据(一行)

                row.add(cur.getMemberId());
                CollUtil.addNoRepeat(rowHeadNames, "用户ID");

                row.add(cur.getMemberName());
                CollUtil.addNoRepeat(rowHeadNames, "昵称");

                row.add((cur.getChangeType() == 1 ? "赠送" : "扣除") + cur.getCoinChange() + "K币");
                CollUtil.addNoRepeat(rowHeadNames, "调币情况");

                row.add(DateUtil.format07(cur.getDeadTime()));
                CollUtil.addNoRepeat(rowHeadNames, "到期时间");

                row.add(cur.getCoinRemain());
                CollUtil.addNoRepeat(rowHeadNames, "当前总余额");

                row.add(cur.getOfficialName());
                CollUtil.addNoRepeat(rowHeadNames, "归属公众号");

                row.add(cur.getRemark());
                CollUtil.addNoRepeat(rowHeadNames, "备注");

                row.add(cur.getCreatorName());
                CollUtil.addNoRepeat(rowHeadNames, "操作员");

                dataList.add(row);
            }
        } else if (params.getCoinType() == 2) {
            for (FastMemberCoinChangePO cur : list) {
                List<Object> row = new ArrayList<>();// 导出的数据(一行)

                row.add(cur.getOpenid());
                CollUtil.addNoRepeat(rowHeadNames, "openid");

                row.add(cur.getMemberId());
                CollUtil.addNoRepeat(rowHeadNames, "用户ID");

                row.add((cur.getChangeType() == 1 ? "增加" : "扣减") + cur.getCoinChange() + "天");
                CollUtil.addNoRepeat(rowHeadNames, "VIP调整情况");

                row.add(DateUtil.format07(cur.getDeadTime()));
                CollUtil.addNoRepeat(rowHeadNames, "VIP到期时间");

                row.add(cur.getRetailName() + "-" + cur.getOfficialName());
                CollUtil.addNoRepeat(rowHeadNames, "用户归属");

                row.add((cur.getType() == 1 ? "微信" : cur.getType() == 2 ? "抖音" : cur.getType() == 3 ? "H5" : cur.getType() == 4 ? "快手" : "未知") + "-" + cur.getMiniName());
                CollUtil.addNoRepeat(rowHeadNames, "归属应用");

                row.add(cur.getRemark());
                CollUtil.addNoRepeat(rowHeadNames, "备注");

                row.add(cur.getCreatorName());
                CollUtil.addNoRepeat(rowHeadNames, "操作员");

                row.add(DateUtil.format07(cur.getCreateTime()));
                CollUtil.addNoRepeat(rowHeadNames, "操作时间");

                dataList.add(row);
            }
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "调币记录";
        if (params.getCoinType() == 2) {
            title = "VIP调整记录";
        }
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }
}
