/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.annotation.Slave;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.FlowTypeEnum;
import com.fast.enums.upay.*;
import com.fast.framework.exception.MyException;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.mapper.fee.FastFeeModelDetailMapper;
import com.fast.mapper.member.*;
import com.fast.mapper.promote.FastActivityPayMapper;
import com.fast.mapper.promote.FastMemberLinkMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.fee.FastFeeKeepDetailPO;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.po.member.*;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.promote.FastActivityClickPO;
import com.fast.po.promote.FastActivityPayPO;
import com.fast.po.promote.FastMemberLinkPO;
import com.fast.po.upay.UpayOrderLog;
import com.fast.service.base.BaseService;
import com.fast.service.group.FastMemberGroupDetailService;
import com.fast.service.id.FastIdOrderService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.order.FastMemberOrderDailyMoneyService;
import com.fast.service.promote.FastActivityClickService;
import com.fast.service.promote.FastActivityPayService;
import com.fast.service.promote.FastLinkService;
import com.fast.service.promote.FastMemberLinkService;
import com.fast.service.setting.FastMemberOrderSettingService;
import com.fast.service.setting.FastSettingSystemService;
import com.fast.service.upay.UpayOrderLogService;
import com.fast.utils.*;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.thread.ContentTypeContext;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.fee.FastFeeModelDetailVO;
import com.fast.vo.member.MemberAccountVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单充值记录
 *
 * <AUTHOR>
 */
@Service
public class FastMemberOrderRechargeService extends BaseService {

    @Autowired
    private FastMemberOrderRechargeMapper rechargeMapper;
    @Autowired
    private FastMemberCoinChangeMapper coinChangeMapper;
    @Autowired
    private FastActivityPayMapper activityPayMapper;
    @Autowired
    private FastMemberLinkMapper memberLinkMapper;
    @Autowired
    private FastMemberCoinMoneyMonthMapper coinMoneyMonthMapper;
    @Autowired
    private FastMemberMapper memberMapper;
    @Autowired
    private UpayOrderLogService upayOrderLogService;
    @Autowired
    private FastMemberOrderRechargeDiamondMapper orderRechargeDiamondMapper;
    @Autowired
    private FastMemberLinkService memberLinkService;
    @Autowired
    private FastMemberAccountService accountService;
    @Autowired
    private FastMemberAccountFlowService accountFlowService;
    @Autowired
    private FastActivityPayService activityPayService;
    @Autowired
    private FastMemberOrderDailyMoneyService dailyMoneyService;
    @Autowired
    private FastActivityClickService activityClickService;
    @Autowired
    private FastMiniService miniService;
    @Autowired
    private FastMemberDramaFirstWatchService firstWatchService;
    @Autowired
    private FastLinkService linkService;
    @Autowired
    private FastMemberOrderSettingService orderSettingService;
    @Autowired
    private FastMemberOrderRechargeFromMapper fastMemberOrderRechargeFromMapper;
    @Autowired
    private FastMemberOrderDramaMapper fastMemberOrderDramaMapper;
    @Autowired
    private FastIdOrderService fastIdOrderService;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMemberLinkAdvMapper fastMemberLinkAdvMapper;
    @Autowired
    private FastMemberOrderRechargeAdvMapper memberOrderRechargeAdvMapper;
    @Autowired
    private FastMemberSceneMapper fastMemberSceneMapper;
    @Autowired
    private FastFeeModelDetailMapper fastFeeModelDetailMapper;
    @Autowired
    private FastMemberVipOnlineService fastMemberVipOnlineService;
    @Autowired
    private FastMemberOrderRechargeLoadService fastMemberOrderRechargeLoadService;
    @Autowired
    private FastMemberBackHandService fastMemberBackHandService;
    @Autowired
    private FastSettingSystemService fastSettingSystemService;
    @Autowired
    private FastMemberGroupDetailService fastMemberGroupDetailService;
    @Autowired
    private FastMemberOrderRechargeImService orderRechargeImService;
    @Autowired
    private FastDramaMapper fastDramaMapper;

    // private static final ThreadPoolExecutor THREAD_POOL_EXECUTOR = new MdcThreadPoolExecutor(
    //         Runtime.getRuntime().availableProcessors() * 2,
    //         Runtime.getRuntime().availableProcessors() * 2,
    //         0,
    //         TimeUnit.SECONDS,
    //         new ArrayBlockingQueue<>(1000),
    //         new ThreadFactoryBuilder().setNameFormat("googlepay-%d").build(),
    //         new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 查询剧总充值
     */
    public Map<Integer, BigDecimal> getDramaMoneyMap() {
        List<FastMemberOrderRechargePO> morList = rechargeMapper.queryAllDramaMoney(null);
        Map<Integer, BigDecimal> dmMap = new HashMap<>();
        for (FastMemberOrderRechargePO morItem : morList) {
            dmMap.put(morItem.getDramaId(), morItem.getMoneyRecharge());
        }
        return dmMap;
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargePO queryById(FastMemberOrderRechargePO item) {
        return rechargeMapper.queryById(item);
    }

    /**
     * 查询订单回传状态
     */
    public ResultVO getOrderBack(Long id) {
        String key = StaticVar.ORDER_BACK_DETAIL + id;
        String res = RedisUtil.get(key);
        Integer backState = 1; // 1待回传  2回传成功  3回传失败
        if (StrUtil.isEmpty(res)) {
            FastMemberOrderRechargePO morPO = rechargeMapper.queryById(id);
            if (morPO != null) {
                res = JsonUtil.toString(morPO);
            } else {
                res = StaticVar.EMPTY_FLAG;
            }
            RedisUtil.set(key, res, 60 * 60);
        }
        if (StrUtil.isNotEmpty(res) && !StaticVar.EMPTY_FLAG.equals(res)) {
            FastMemberOrderRechargePO morPO = JsonUtil.toJavaObject(res, FastMemberOrderRechargePO.class);
            if (morPO != null && morPO.getBackState() != null) {
                backState = morPO.getBackState();
            }
        }
        Map<String, Object> results = new HashMap<>();
        results.put("backState", backState);
        return ResultVO.success("ok", results);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargePO queryById(Long id) {
        return rechargeMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderRechargePO queryOne(FastMemberOrderRechargePO item) {
        return rechargeMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderRechargePO> queryList(FastMemberOrderRechargePO params) {
        // CalTime calTime = new CalTime();
        List<FastMemberOrderRechargePO> list = rechargeMapper.queryList(params);
        // log.info("订单列表-查询列表耗时{}", calTime.getCostTime());
        for (FastMemberOrderRechargePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            /*if (biggerZero(cur.getLinkId())) {
	            FastLinkVO linkVO = linkService.queryInfoByRedis(cur.getLinkId());
	            if(linkVO != null){
	            	FastDramaVO dramaVO = dramaService.queryInfoByRedis(linkVO.getDramaId());
	            	FastDramaSeriesVO seriesVO = seriesService.queryInfoByIDRedis(linkVO.getSeriesId());
	            	if (dramaVO != null && seriesVO != null) {
	            		cur.setLinkEntryPage(dramaVO.getDramaName() + "-第" + seriesVO.getSeriesNum() + "集");
	            	}
	            }
	        }*/
            if (StrUtil.isNotEmpty(cur.getLinkDramaName())) {
                cur.setLinkEntryPage(cur.getLinkDramaName() + "-第" + cur.getLinkSeriesNum() + "集");
            }
            if (cur.getLinkType() != null && cur.getLinkType() == 3) {
                cur.setSubLinkId(cur.getLinkId());
                cur.setSubLinkName(cur.getLinkName());
                cur.setLinkId(null);
                cur.setLinkName("");
            }
            if (cur.getPurchaseTime() != null && cur.getPurchaseTime() > 0) {
                cur.setGpCreateTime(new Date(cur.getPurchaseTime()));
            }
            if (cur.getEventTime() != null && cur.getEventTime() > 0) {
                cur.setGpPayTime(new Date(cur.getEventTime()));
            }
        }
        return list;
    }

    /**
     * 查询全部
     */
    public int queryListCount(FastMemberOrderRechargePO item) {
        return rechargeMapper.queryListCount(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderRechargePO> querySlaveSimpleList(FastMemberOrderRechargePO item) {
        return rechargeMapper.querySlaveSimpleList(item);
    }

    /**
     * 查询全部
     */
    @Slave
    public List<FastMemberOrderRechargePO> getMemberRechargeCoinListAuto(FastMemberOrderRechargePO params) {
        String key = "member_recharge_coin:" + params.getCreateTimeStr();
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            return JsonUtil.toList(value, FastMemberOrderRechargePO.class);
        }
        List<FastMemberOrderRechargePO> list = new ArrayList<>();
        params.setOrderType(1);
        List<FastMemberOrderRechargePO> list1 = rechargeMapper.queryMemberRechargeMoneyByMini(params);// 充值K币金额
        params.setOrderType(2);
        List<FastMemberOrderRechargePO> list2 = rechargeMapper.queryMemberRechargeMoneyByMini(params);// 充值VIP金额
        params.setOrderType(1);
        List<FastMemberOrderRechargePO> list3 = rechargeMapper.queryMemberRechargeCoinByMini(params);// 充值K币
        List<FastMemberOrderRechargePO> list4 = rechargeMapper.queryMemberGiveCoinByMini(params);// 赠送K币
        List<FastMemberOrderRechargePO> list5 = rechargeMapper.queryMemberCoinConsumeByMini(params);// 消费充值K币
        List<FastMemberOrderRechargePO> list6 = rechargeMapper.queryMemberCoinGiveConsumeByMini(params);// 消费赠送K币

        Set<Integer> miniIdSet = new HashSet<>();
        Map<Integer, Object> map1 = new HashMap<>(), map2 = new HashMap<>(), map3 = new HashMap<>(), map4 = new HashMap<>(), map5 = new HashMap<>(), map6 = new HashMap<>();
        // 充值K币金额
        list1.forEach(cur -> {
            miniIdSet.add(cur.getMiniId());
            map1.put(cur.getMiniId(), cur.getMoneyRecharge());
        });
        // 充值VIP金额
        list2.forEach(cur -> {
            miniIdSet.add(cur.getMiniId());
            map2.put(cur.getMiniId(), cur.getMoneyRecharge());
        });
        // 充值K币
        list3.forEach(cur -> {
            miniIdSet.add(cur.getMiniId());
            map3.put(cur.getMiniId(), cur.getCoinRecharge());
        });
        // 赠送K币
        list4.forEach(cur -> {
            miniIdSet.add(cur.getMiniId());
            map4.put(cur.getMiniId(), cur.getCoinGive());
        });
        // 消费充值K币
        list5.forEach(cur -> {
            miniIdSet.add(cur.getMiniId());
            map5.put(cur.getMiniId(), cur.getCoinRechargeConsume());
        });
        // 消费赠送K币
        list6.forEach(cur -> {
            miniIdSet.add(cur.getMiniId());
            map6.put(cur.getMiniId(), cur.getCoinGiveConsume());
        });

        FastMiniPO mini = new FastMiniPO();
        Map<Integer, String> nameMap = miniService.queryMiniName2Map(mini);
        for (Integer miniId : miniIdSet) {
            if (miniId == null || miniId <= 0) {
                continue;
            }
            FastMemberOrderRechargePO po = new FastMemberOrderRechargePO();
            po.setMonth(params.getMonth());
            po.setMiniName(nameMap.get(miniId));
            po.setMiniId(miniId);
            po.setMoneyRecharge(new BigDecimal(toString(map1.get(miniId), "0.0")));
            po.setMoneyVipRecharge(new BigDecimal(toString(map2.get(miniId), "0.0")));
            po.setCoinRecharge(toInteger(map3.get(miniId), 0));
            po.setCoinGive(toInteger(map4.get(miniId), 0));
            po.setCoinRechargeConsume(toInteger(map5.get(miniId), 0));
            po.setCoinRechargeRemain(po.getCoinRecharge() - po.getCoinRechargeConsume());
            po.setCoinGiveConsume(toInteger(map6.get(miniId), 0));
            po.setCoinGiveRemain(po.getCoinGive() - po.getCoinGiveConsume());

            list.add(po);
        }

        RedisUtil.set(key, JsonUtil.toString(list), RedisUtil.TIME_2H);

        return list;
    }

    /**
     * 查询全部
     */
    @Slave
    public List<FastMemberCoinMoneyMonthPO> getMemberRechargeCoinList2(FastMemberCoinMoneyMonthPO params) {
        List<FastMemberCoinMoneyMonthPO> list = coinMoneyMonthMapper.queryList(params);
        if (CollUtil.hasContent(list)) {
            Map<Integer, String> nameMap = miniService.queryMiniName2Map(new FastMiniPO());
            for (FastMemberCoinMoneyMonthPO po : list) {
                po.setMonthStr(params.getMonthStr());
                po.setMiniName(nameMap.get(po.getMiniId()));
            }
        }
        return list;
    }

    /**
     * 查询用户id
     */
    public List<Long> queryMemberIds(FastMemberOrderRechargePO item) {
        return rechargeMapper.queryMemberIds(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<Map<String, Object>> queryPageList(FastMemberOrderRechargePO params, PageVO pageVO) {
        // 实时数据查询
        CalTime calTime = new CalTime();
        startPage(pageVO);
        List<FastMemberOrderRechargePO> list = queryList(params);
        log.info("订单列表-详细查询耗时{}", calTime.getCostTime());
        // 支付场景
        if (CollUtil.isNotEmpty(list)) {
            for (FastMemberOrderRechargePO po : list) {
                String scenario = po.getScenario();
                if (StrUtil.equals(scenario, "0")) {
                    po.setScenario("充值页");
                } else {
                    String[] split = StrUtil.split(scenario, "_");
                    FastDramaPO drama = fastDramaMapper.querySimpleById(Integer.valueOf(split[0]));
                    if (drama != null) {
                        po.setScenario(drama.getDramaName() + "-第" + split[1] + "集");
                    }
                }
            }
        }
        Map<String, Object> results = getPageListData(list, pageVO);

        // 查询汇总数据
        calTime = new CalTime();
        // params.setState(1);
        FastMemberOrderRechargePO summary = rechargeMapper.querySummary(params);
        log.info("订单列表-汇总数据查询耗时{}", calTime.getCostTime());
        if (summary == null) {
            summary = new FastMemberOrderRechargePO();
            summary.setRechargeCount(0);
            summary.setRechargeMemberCount(0);
            summary.setMoneyRecharge(BigDecimal.ZERO);
            summary.setMoneyProfit(BigDecimal.ZERO);
            summary.setPaidRechargeCount(0);
            summary.setPaidRechargeMemberCount(0);
            summary.setPaidMoneyRecharge(BigDecimal.ZERO);
            summary.setPaidMoneyProfit(BigDecimal.ZERO);
            summary.setNoPaidRechargeCount(0);
            summary.setNoPaidRechargeMemberCount(0);
            summary.setNoPaidMoneyRecharge(BigDecimal.ZERO);
            summary.setNoPaidMoneyProfit(BigDecimal.ZERO);
        } else {
            summary.setNoPaidRechargeCount(summary.getRechargeCount() - summary.getPaidRechargeCount());
            summary.setNoPaidRechargeMemberCount(summary.getRechargeMemberCount() - summary.getPaidRechargeMemberCount());
            summary.setNoPaidMoneyRecharge(summary.getMoneyRecharge().subtract(summary.getPaidMoneyRecharge()));
            summary.setNoPaidMoneyProfit(summary.getMoneyProfit().subtract(summary.getPaidMoneyProfit()));
        }
        results.put("summary", summary);

        return ResultVO.success(results);
    }

    /**
     * 查询全部(分页)
     */
    public List<FastMemberOrderRechargePO> queryPageListSimple(FastMemberOrderRechargePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderRechargePO> list = rechargeMapper.queryListSimple(item);
        List<Integer> ids = new ArrayList<>();
        for (FastMemberOrderRechargePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getCoinChangeId() != null) {
                ids.add(cur.getCoinChangeId());
            }
            if (Objects.equals(item.getGiven(), 1)) {
                cur.setRemark("充值赠送");
            }
        }
        if (CollUtil.isNotEmpty(list)) {
            Set<Long> orderIds = list.stream().map(FastMemberOrderRechargePO::getId).collect(Collectors.toSet());
            // 补充钻石信息
            FastMemberOrderRechargeDiamondPO diamondQuery = new FastMemberOrderRechargeDiamondPO();
            diamondQuery.setIds(StrUtil.join(orderIds));
            List<FastMemberOrderRechargeDiamondPO> diamondPOList = orderRechargeDiamondMapper.querySimple(diamondQuery);
            if (CollUtil.isNotEmpty(diamondPOList)) {
                Map<Long, FastMemberOrderRechargeDiamondPO> diamondMap = diamondPOList.stream().collect(Collectors.toMap(FastMemberOrderRechargeDiamondPO::getId, Function.identity()));
                list.forEach(recharge -> {
                    FastMemberOrderRechargeDiamondPO diamondPO = diamondMap.get(recharge.getId());
                    if (Objects.nonNull(diamondPO)) {
                        recharge.setDiamond(diamondPO.getDiamond());
                    }
                });
            }
        }
        if (CollUtil.hasContent(ids)) {
            FastMemberCoinChangePO changeQuery = new FastMemberCoinChangePO();
            changeQuery.setIds(StrUtil.join(ids));
            List<FastMemberCoinChangePO> changeList = coinChangeMapper.querySimple(changeQuery);
            if (CollUtil.hasContent(changeList)) {
                Map<Integer, FastMemberCoinChangePO> map = new HashMap<>();
                for (FastMemberCoinChangePO changePO : changeList) {
                    map.put(changePO.getId(), changePO);
                }
                for (FastMemberOrderRechargePO rechargePO : list) {
                    if (rechargePO.getCoinChangeId() != null) {
                        FastMemberCoinChangePO changePO = map.get(rechargePO.getCoinChangeId());
                        if (changePO != null) {
                            rechargePO.setChangeType(changePO.getChangeType());
                            rechargePO.setCoinChange(changePO.getCoinChange());
                            rechargePO.setRemark(changePO.getRemark());
                        }
                    }
                }
            }
        }
        return list;
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<Map<String, Object>> queryPageListSimpleVO(FastMemberOrderRechargePO item, PageVO pageVO) {
        List<FastMemberOrderRechargePO> list = queryPageListSimple(item, pageVO);
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderRechargePO item) {
        return rechargeMapper.queryCount(item);
    }

    /**
     * 查询用户某个充值挡位的充值次数
     */
    public int queryRechargeCount(Long memberId, Integer linkId, Integer modelDetailId) {
        // 校验充值模板充值的次数
        FastMemberOrderRechargePO query = new FastMemberOrderRechargePO();
        query.setMemberId(memberId);
        query.setState(1);
        query.setModelDetailId(modelDetailId);
        query.setLinkId(linkId);
        return rechargeMapper.queryCount(query);
    }

    /**
     * 处理消费明细
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void insertAsync(FastMemberOrderConsumePO consume) {
        // 此处需要加锁, 同一个用户同一时间只能操作一次
        Date nowTime = DateUtil.getNowDate();
        consume.setCreateTime(nowTime);
        // 查询用户全部有效的K币订单
        FastMemberOrderRechargePO query = new FastMemberOrderRechargePO();
        query.setMemberId(consume.getMemberId());
        List<FastMemberOrderRechargePO> rechargeList = rechargeMapper.queryListSimple(query);
        if (CollUtil.isEmpty(rechargeList)) {
            return;
        }
        for (FastMemberOrderRechargePO po : rechargeList) {
            // 处理订单额度的扣除
        }
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderRechargePO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        item.setId(fastIdOrderService.getOrderId());
        if (rechargeMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 创建订单-终端发起
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<?> rechargeAdd(SessionVO sessionVO, FastMemberOrderRechargePO params, FastFeeModelDetailVO modelDetail, FastFeeKeepDetailPO keepDetail) {
        final Date nowTime = DateUtil.getNowDate();
        final Date yearEndDate = DateUtil.endOfYear();
        params.setCreateTime(nowTime);
        params.setPayTime(nowTime);
        params.setMemberId(sessionVO.getMemberId());
        params.setState(0);
        Integer orderType = null;
        Integer modelDetailId = 0;
        Integer coinRecharge = 0;
        Integer coinGive = 0;
        Integer rechargeRemain = 0;
        Integer coinGiveRemain = 0;
        BigDecimal moneyRecharge = BigDecimal.ZERO;
        // 充值模板
        if (modelDetail != null) {
            if (modelDetail.getType() == 3) {
                orderType = 4;
            } else {
                orderType = modelDetail.getType();
            }
            modelDetailId = modelDetail.getId();
            coinRecharge = toInteger(modelDetail.getCoinRecharge(), 0);
            coinGive = toInteger(modelDetail.getCoinGive(), 0);
            rechargeRemain = toInteger(modelDetail.getCoinRecharge(), 0);
            coinGiveRemain = toInteger(modelDetail.getCoinGive(), 0);
            moneyRecharge = modelDetail.getMoneyRecharge();
            if (keepDetail != null) {
                // 加上额外
                coinGive = coinGive + keepDetail.getCoinAdd();
                moneyRecharge = DoubleUtil.subB(moneyRecharge, keepDetail.getMoneySub());
                if (moneyRecharge.compareTo(BigDecimal.ZERO) <= 0) {
                    // 最小金额0.01
                    moneyRecharge = BigDecimal.valueOf(0.1D);
                }
            }
        }
        if (modelDetailId == 0 && keepDetail != null) {
            if (keepDetail.getType() != null && keepDetail.getType() == 3) {
                orderType = 4;
            } else {
                orderType = keepDetail.getType();
            }
            coinRecharge = toInteger(keepDetail.getCoinRecharge(), 0);
            coinGive = toInteger(keepDetail.getCoinGive(), 0);
            coinGive = coinGive + keepDetail.getCoinAdd();
            rechargeRemain = toInteger(keepDetail.getCoinRecharge(), 0);
            coinGiveRemain = toInteger(keepDetail.getCoinGive(), 0);
            moneyRecharge = keepDetail.getMoneyRecharge();
            moneyRecharge = DoubleUtil.subB(moneyRecharge, keepDetail.getMoneySub());
            if (moneyRecharge.compareTo(BigDecimal.ZERO) <= 0) {
                // 最小金额0.01
                moneyRecharge = BigDecimal.valueOf(0.1D);
            }
        }
        int diamondCount = fastSettingSystemService.getDiamondCountByMoney(moneyRecharge);
        actionLogService.log("tt_diamond", "moneyRecharge:" + moneyRecharge + ",抖音钻石数量" + diamondCount);
        // 抖音小程序ios渠道支付金额重新设置
        if (params.getPhoneOs() == 2 && params.getMiniType() == 2) {
            // 重新计算金额
            moneyRecharge = BigDecimal.valueOf(diamondCount).multiply(fastSettingSystemService.getDiamondMoney()).setScale(2, RoundingMode.HALF_UP);
        }
        actionLogService.log("tt_diamond", "重新计算后金额:" + moneyRecharge);
        params.setOrderType(orderType);
        params.setModelDetailId(modelDetailId);
        params.setCoinRecharge(coinRecharge);
        params.setCoinGive(coinGive);
        params.setCoinRechargeRemain(rechargeRemain);
        params.setCoinGiveRemain(coinGiveRemain);
        params.setMoneyRecharge(moneyRecharge);
        params.setAid("");
        params.setCid("");
        // 类型:1=充值金币;2=充值VIP
        if (modelDetail != null && modelDetail.getType() == 2) {
            params.setValidDate(modelDetail.getValidDate());
            params.setValidUnit(modelDetail.getValidUnit());
        }

        // 查询aid和cid，有就更新
        FastMemberLinkPO mParam = new FastMemberLinkPO();
        mParam.setMemberId(sessionVO.getMemberId());
        FastMemberLinkPO memberLink = memberLinkMapper.queryOne(mParam);
        if (memberLink != null && StrUtil.isNotEmpty(memberLink.getAid())) {
            params.setAid(memberLink.getAid());
            params.setCid(memberLink.getCid());
            params.setPromotionId(memberLink.getPromotionId());
            params.setProjectId(memberLink.getProjectId());
        }
        if (memberLink != null && StrUtil.isNotEmpty(memberLink.getPromotionId())) {
            params.setPromotionId(memberLink.getPromotionId());
            params.setProjectId(memberLink.getProjectId());
        }

        // 查询达人id，有就加入订单
        if (sessionVO.getXmanId() != null) {
            params.setXmanId(sessionVO.getXmanId());
        }
        // 查询用户的注册时间
        FastMemberPO memberPO = memberMapper.queryById(sessionVO.getMemberId());
        params.setRegisterTime(memberPO.getCreateTime());
        params.setExpireTime(yearEndDate);
        params.setId(fastIdOrderService.getOrderId());

        // 订单编号
        Long orderId = StrUtil.getRandomOrderId();
        params.setOrderNo((params.getPhoneOs() == 2 ? "AP" : "GP") + orderId);

        // 创建支付单
        UpayOrderLog upayLog = new UpayOrderLog();
        upayLog.setTermOrdId(orderId);
        upayLog.setPayChannel(UpayChannelEnum.APP.index);
        upayLog.setOrderType(UpayOrderTypeEnum.PAY.idnex);
        upayLog.setState(UpayOrderStatusEnum.WAITING.index);
        upayLog.setTransDate(DateUtil.format06Int(nowTime));
        upayLog.setTransTime(nowTime);
        upayLog.setPayForm(UpayFormEnum.APP.index);
        upayLog.setMiniId(memberPO.getMiniId());
        upayLog.setMemberId(memberPO.getId());
        upayLog.setRetailId(memberPO.getRetailId());
        upayLog.setOfficialId(memberPO.getOfficialId());
        if (params.getPhoneOs() == 2) {
            upayLog.setPayType(UpayAppPayTypeEnum.APPLE_PAY.index);
            upayLog.setPayChannelType(UpayChannelTypeEnum.APPLE_PAY.index);
        } else {
            upayLog.setPayType(UpayAppPayTypeEnum.GOOGLE_PAY.index);
            upayLog.setPayChannelType(UpayChannelTypeEnum.GOOGLE_PAY.index);
        }
        upayLog.setOrdAmt(moneyRecharge);
        upayLog.setHfMemberId("0");
        upayLog.setUpdateTime(nowTime);
        MethodVO methodVO = upayOrderLogService.insert(upayLog);
        if (methodVO.getCode() != 0) {
            transactionRollBack();
            return ResultVO.error(StaticStr.ADD_FAILED);
        }

        // 创建订单
        params.setPayLogId(upayLog.getId());
        if (rechargeMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return ResultVO.error(StaticStr.ADD_FAILED);
        }

        // 记录抖音小程序的钻石充值数量
        if (params.getPhoneOs() == 2 && params.getMiniType() == 2) {
            // 保存用户充值钻石数量
            FastMemberOrderRechargeDiamondPO diamondPO = new FastMemberOrderRechargeDiamondPO();
            diamondPO.setDiamond(diamondCount);
            diamondPO.setId(params.getId());
            diamondPO.setCreateTime(DateUtil.getNowDate());
            orderRechargeDiamondMapper.insertSelective(diamondPO);
        }
        // 添加支付挽留标志
        if (keepDetail != null) {
            actionLogService.log("update_keep_1102", "创建挽留标志" + sessionVO.getMemberId());
            FastMemberOrderRechargeFromPO rfPO = new FastMemberOrderRechargeFromPO();
            rfPO.setFromType(1);
            rfPO.setCreateTime(DateUtil.getNowDate());
            rfPO.setId(params.getId());
            fastMemberOrderRechargeFromMapper.insertSelective(rfPO);
        }
        // 添加关联的素材id
        FastMemberLinkAdvPO maPO = fastMemberLinkAdvMapper.queryById(sessionVO.getMemberId());
        FastMemberScenePO scenePO = fastMemberSceneMapper.queryById(sessionVO.getMemberId());
        if (maPO != null || scenePO != null || params.getSeriesNum() != null) {
            FastMemberOrderRechargeAdvPO morAPO = new FastMemberOrderRechargeAdvPO();
            morAPO.setId(params.getId());
            if (maPO != null && StrUtil.isNotEmpty(maPO.getMaterialId())) {
                morAPO.setMaterialId(maPO.getMaterialId());
            }
            if (params.getSeriesNum() != null) {
                morAPO.setSeriesNum(params.getSeriesNum());
            }
            if (scenePO != null && StrUtil.isNotEmpty(scenePO.getScene())) {
                morAPO.setScene(scenePO.getScene());
            }
            morAPO.setCreateTime(DateUtil.getNowDate());
            memberOrderRechargeAdvMapper.insertSelective(morAPO);
            actionLogService.log("order_scene", "正常插入关联场景" + JsonUtil.toString(morAPO));
        }
        actionLogService.log("update_0529", "创建预订单正常" + sessionVO.getMemberId());
        // 如果是活动订单
        if (params.getActivityId() != null && params.getActivityId() > 0) {
            FastActivityPayPO pay = new FastActivityPayPO();
            pay.setActivityId(params.getActivityId());
            pay.setState(0);
            pay.setMemberId(params.getMemberId());
            pay.setOrderId(params.getId());
            pay.setOrderType(params.getOrderType());
            pay.setMoneyRecharge(params.getMoneyRecharge());
            pay.setCreateTime(params.getCreateTime());
            pay.setCreateDate(DateUtil.format06Int(pay.getCreateTime()));
            if (activityPayMapper.insertSelective(pay) == 0) {
                transactionRollBack();
                return ResultVO.error(StaticStr.ADD_FAILED);
            }

            // 记录一次点击数据, 防止前面的click时间没有处理到
            FastActivityClickPO click = new FastActivityClickPO();
            click.setActivityId(params.getActivityId());
            click.setMemberId(params.getMemberId());
            activityClickService.click(click);
        }
        // 添加关联的demandId
        if (params.getLinkType() != null && params.getLinkType() == 3) {
            fastMemberOrderRechargeLoadService.addDependId(params.getId(), sessionVO.getMemberId());
        }
        // 组装返回数据
        FastMemberOrderRechargePO result = new FastMemberOrderRechargePO();
        result.setEncryptionId(encode(orderId));
        result.setProductId(params.getProductId());
        return ResultVO.success(result);
    }


    /**
     * 充值成功-支付回调
     *
     * @param orderLog
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void rechargeSuccess(UpayOrderLog orderLog) {
        log.info("充值成功，开始给用户追加K币, 支付单ID: {}, 外部订单ID: {}", orderLog.getId(), orderLog.getOutTransId());

        FastMiniVO miniVO = fastMiniService.queryInfoByRedis(orderLog.getMiniId());
        ContentTypeContext.setContentType(miniVO.getContentType());

        final Date nowTime = DateUtil.getNowDate();
        final Date yearEndDate = DateUtil.endOfYear();

        FastMemberOrderRechargePO rechargeOrder = rechargeMapper.queryByPayLogId(orderLog.getId());
        if (rechargeOrder == null) {
            log.info("支付单 {} 对应的充值订单不存在", orderLog.getId());
            throw new MyException(StaticStr.DATA_EMPTY);
        }
        MemberAccountVO accountDB = accountService.queryInfoByRedis(rechargeOrder.getMemberId());
        if (accountDB == null) {
            log.info("用户 {} 的账户信息不存在", rechargeOrder.getMemberId());
            throw new MyException(StaticStr.DATA_EMPTY);
        }
        ContentTypeContext.setContentType(rechargeOrder.getContentType());

        // 如果最后一次染色时间是今天则表示当前用户是新增用户
        Date lastLinkDate = memberLinkMapper.queryLastLinkDate(rechargeOrder.getMemberId());
        if (memberLinkService.queryMemberIsAdd(lastLinkDate)) {
            rechargeOrder.setAddState(1);
        } else {
            rechargeOrder.setAddState(0);
        }
        // 更新充值订单信息
        rechargeOrder.setUpdateTime(nowTime);
        rechargeOrder.setPayTime(orderLog.getEndTime());
        rechargeOrder.setPayDate(DateUtil.format06Int(rechargeOrder.getPayTime()));
        rechargeOrder.setState(1);
        rechargeOrder.setOutTransId(orderLog.getOutTransId());
        rechargeOrder.setLinkTime(lastLinkDate);
        // 当前订单是第N笔支付成功的
        FastMemberOrderRechargePO orderQ = new FastMemberOrderRechargePO();
        orderQ.setState(1);
        orderQ.setCoinChangeId(0);// 排除直接赠送金币的记录
        int paySequence = rechargeMapper.queryCount(orderQ);
        rechargeOrder.setPaySequence(++paySequence);
        orderQ.setLinkId(rechargeOrder.getLinkId());
        int payLinkSequence = rechargeMapper.queryCount(orderQ);
        rechargeOrder.setPayLinkSequence(++payLinkSequence);
        actionLogService.log("update_order", rechargeOrder.getId() + "准备更新" + payLinkSequence);
        rechargeOrder.setPayForm(orderLog.getPayForm());
        if (rechargeMapper.updateByIdVersion(rechargeOrder) == 0) {
            throw new MyException(StaticStr.UPDATE_FAILED);
        }
        log.info("充值订单状态更新成功, 订单ID: {}，订单类型: {}", rechargeOrder.getId(), rechargeOrder.getOrderType());

        // 订单类型:1=充值K币;2=充值VIP;3=赠送K币;4=充值剧卡
        if (rechargeOrder.getOrderType() == 1) {
            FastMemberAccountPO memberAccount = new FastMemberAccountPO();
            memberAccount.setMemberId(rechargeOrder.getMemberId());
            memberAccount.setUpdateTime(nowTime);
            memberAccount.setRechargeCount(1);// 累加1次充值次数
            memberAccount.setRechargeMoney(rechargeOrder.getMoneyRecharge());// 累加充值金额
            memberAccount.setCoinDeadTime(yearEndDate); // 赠送K币到期时间
            // 充值金币
            Integer coinRecharge = rechargeOrder.getCoinRecharge();
            memberAccount.setCoinRechargeAll(coinRecharge);
            memberAccount.setCoinRechargeRemain(coinRecharge);
            // 赠送金币
            Integer coinGive = rechargeOrder.getCoinGive();
            memberAccount.setCoinGiveAll(coinGive);
            memberAccount.setCoinGiveRemain(coinGive);
            // 总金币
            memberAccount.setCoinAll(coinRecharge + coinGive);
            memberAccount.setCoinRemain(coinRecharge + coinGive);
            // 分开写流水，所以这里不统一写
            memberAccount.setSyncFlow(false);
            MethodVO methodVO = accountService.updatePlusById(memberAccount);
            if (methodVO.getCode() != 0) {
                throw new MyException(StaticStr.UPDATE_FAILED);
            }
            log.info("给用户 {} 的账户上加K币成功，本次充值K币 {} 个，赠送K币 {} 个", rechargeOrder.getMemberId(), coinRecharge, coinGive);

            // 写流水，充值币和赠币分开
            String batchNo = StrUtil.getRandomInt(10);
            // 充值币
            FastMemberAccountFlowPO flow1 = new FastMemberAccountFlowPO();
            flow1.setMemberId(rechargeOrder.getMemberId());
            flow1.setType(FlowTypeEnum.IN.getCode());
            flow1.setCoin(coinRecharge);
            flow1.setCoinRecharge(coinRecharge);
            flow1.setRemark(StaticStr.RECHARGE_COINS);
            flow1.setBatchNo(batchNo);
            accountFlowService.insert(flow1);
            // 赠币
            FastMemberAccountFlowPO flow2 = new FastMemberAccountFlowPO();
            flow2.setMemberId(rechargeOrder.getMemberId());
            flow2.setType(FlowTypeEnum.IN.getCode());
            flow2.setCoin(coinGive);
            flow2.setCoinRecharge(coinGive);
            flow2.setRemark(StaticStr.RECHARGE_GIFT);
            flow2.setBatchNo(batchNo);
            accountFlowService.insert(flow2);

            // 抖音回传权益-coin代币
            if (fastMiniService.judgeThreeMini(rechargeOrder.getMiniId())) {
                actionLogService.log("tiktok_init_post_res", "准备回传权益-coin，orderId=" + rechargeOrder.getId());
            }
        } else if (rechargeOrder.getOrderType() == 2) {
            FastMemberAccountPO memberAccount = new FastMemberAccountPO();
            memberAccount.setMemberId(rechargeOrder.getMemberId());
            memberAccount.setUpdateTime(nowTime);
            memberAccount.setRechargeCount(1);// 累加1次充值次数
            memberAccount.setRechargeMoney(rechargeOrder.getMoneyRecharge());// 累加充值金额
            // 更新账户有效期
            Date startTime;
            if (accountDB.getDeadTime().compareTo(nowTime) <= 0) {
                // 已过期的账户从当前时间开始计时
                startTime = nowTime;
            } else {
                // 未过期的账户从将要过期的时间开始计时
                startTime = accountDB.getDeadTime();
            }
            // 单位:1=周;2=月;3=年;4=日
            switch (rechargeOrder.getValidUnit()) {
                case 1: {
                    memberAccount.setDeadTime(DateUtil.addWeeks(startTime, rechargeOrder.getValidDate()));
                    break;
                }
                case 2: {
                    memberAccount.setDeadTime(DateUtil.addMonths(startTime, rechargeOrder.getValidDate()));
                    break;
                }
                case 3: {
                    memberAccount.setDeadTime(DateUtil.addYears(startTime, rechargeOrder.getValidDate()));
                    break;
                }
                case 4: {
                    memberAccount.setDeadTime(DateUtil.addDays(startTime, rechargeOrder.getValidDate()));
                    break;
                }
            }
            MethodVO methodVO = accountService.update(memberAccount);
            if (methodVO.getCode() != 0) {
                throw new MyException(StaticStr.UPDATE_FAILED);
            }
            // 支付挽留vip可能存在赠币
            Integer coinGive = rechargeOrder.getCoinGive();
            if (biggerZero(coinGive)) {
                memberAccount = new FastMemberAccountPO();
                memberAccount.setMemberId(rechargeOrder.getMemberId());
                memberAccount.setUpdateTime(nowTime);
                memberAccount.setCoinDeadTime(yearEndDate); // 赠送K币到期
                memberAccount.setCoinAll(coinGive);
                memberAccount.setCoinGiveAll(coinGive);
                memberAccount.setCoinRemain(coinGive);
                memberAccount.setCoinGiveRemain(coinGive);
                methodVO = accountService.updatePlusById(memberAccount);
                if (methodVO.getCode() != 0) {
                    throw new MyException(StaticStr.UPDATE_FAILED);
                }
            }
            log.info("给用户 {} 充值VIP成功，Unit: {}，vip会员到期时间 {}，赠送K币 {} 个", rechargeOrder.getMemberId(), rechargeOrder.getValidUnit(), memberAccount.getDeadTime(), coinGive);
            // 更新会员vip类型对应限制
            if (rechargeOrder.getModelDetailId() != null && rechargeOrder.getModelDetailId() > 0) {
                // 查询模板
                FastFeeModelDetailPO mdPO = fastFeeModelDetailMapper.queryById(rechargeOrder.getModelDetailId());
                fastMemberVipOnlineService.doVip(mdPO, rechargeOrder, startTime, memberAccount.getDeadTime());
            }
            // 抖音回传权益-vip
            if (fastMiniService.judgeThreeMini(rechargeOrder.getMiniId())) {
                actionLogService.log("tiktok_init_post_res", "准备回传权益-vip，orderId=" + rechargeOrder.getId());
            }
        } else if (rechargeOrder.getOrderType() == 4) {
            // 剧卡充值成功，添加用户剧卡关联关系
            FastMemberOrderDramaPO odPO = new FastMemberOrderDramaPO();
            odPO.setMemberId(rechargeOrder.getMemberId());
            odPO.setDramaId(rechargeOrder.getDramaId());
            odPO.setCreateTime(DateUtil.getNowDate());
            odPO.setOrderId(rechargeOrder.getId());
            if (fastMemberOrderDramaMapper.insertSelective(odPO) == 0) {
                throw new MyException(StaticStr.ADD_FAILED);
            }
            // 清除缓存
            String keyDrama = StaticVar.MEMBER_ORDER_DRAMA_V + rechargeOrder.getMemberId() + "_" + rechargeOrder.getDramaId();
            RedisUtil.del(keyDrama);
            Integer coinGive = rechargeOrder.getCoinGive();
            if (biggerZero(coinGive)) {
                FastMemberAccountPO memberAccount = new FastMemberAccountPO();
                memberAccount.setMemberId(rechargeOrder.getMemberId());
                memberAccount.setUpdateTime(nowTime);
                memberAccount.setCoinDeadTime(yearEndDate); // 赠送K币到期
                memberAccount.setCoinAll(coinGive);
                memberAccount.setCoinGiveAll(coinGive);
                memberAccount.setCoinRemain(coinGive);
                memberAccount.setCoinGiveRemain(coinGive);
                MethodVO methodVO = accountService.updatePlusById(memberAccount);
                if (methodVO.getCode() != 0) {
                    throw new MyException(StaticStr.UPDATE_FAILED);
                }
            }
            // 抖音回传权益-vip
            if (fastMiniService.judgeThreeMini(rechargeOrder.getMiniId())) {
                actionLogService.log("tiktok_init_post_res", "准备回传权益-drama，orderId=" + rechargeOrder.getId());
            }
        }
        // 更新当日支付时间-异步处理,防止发生异常阻塞主进程
        FastMemberDramaFirstWatchPO watch = new FastMemberDramaFirstWatchPO();
        watch.setMemberId(rechargeOrder.getMemberId());
        watch.setDramaId(rechargeOrder.getDramaId());
        watch.setPayTime(rechargeOrder.getPayTime());
        watch.setPayDateStr(DateUtil.format09(rechargeOrder.getPayTime()));
        watch.setTodayPay(1);
        firstWatchService.updatePayState(watch);

        // 更新人群分组订单类型(仅快应用)
        if (miniVO.getType() != null && (miniVO.getType() == 5 || miniVO.getType() == 7)) {
            fastMemberGroupDetailService.updateMemberGroupRechargeType(rechargeOrder.getMemberId(), rechargeOrder.getOrderType());
        }
        // 支付成功后需要处理的数据
        // 1.回传充值付费到广告媒体-发送消息到MQ执行
        // fastBackRuleService.sendMesForBackToMedia(rechargeOrder.getId());

        // 2.清除用户账户资产缓存
        accountService.removeInfoByRedis(rechargeOrder.getMemberId(), rechargeOrder.getLinkId());

        // 3.处理活动订单
        activityPayService.dealActivityPaySuccess(rechargeOrder.getId(), rechargeOrder.getMoneyRecharge());

        // 4.VIP订单同步处理
        if (rechargeOrder.getOrderType() == 2) {
            dailyMoneyService.insertDailyMoney(nowTime, rechargeOrder.getId());
        }

        // 5.更新手动回传订单数量
        fastMemberBackHandService.updateOrderCount(rechargeOrder.getLinkId(), rechargeOrder.getMemberId());
        ContentTypeContext.clear();
        // 更新im支付
        orderRechargeImService.updateStateAsync(rechargeOrder.getId());

        // 填充支付单中的谷歌订单信息
        // THREAD_POOL_EXECUTOR.execute(() -> upayOrderLogService.fillGoogleProductInfo(orderLog.getId(), orderLog));
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderRechargePO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (rechargeMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public FastMemberOrderRechargePO queryByOrderNo(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }
        FastMemberOrderRechargePO item = new FastMemberOrderRechargePO();
        item.setOrderNo(orderNo);
        return queryOne(item);
    }

    public FastMemberOrderRechargePO queryByPayLogId(Long payLogId) {
        if (payLogId == null) {
            return null;
        }
        FastMemberOrderRechargePO item = new FastMemberOrderRechargePO();
        item.setPayLogId(payLogId);
        return queryOne(item);
    }
}