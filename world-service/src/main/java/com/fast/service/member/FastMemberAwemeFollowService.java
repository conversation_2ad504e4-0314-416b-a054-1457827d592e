/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberAwemeFollowMapper;
import com.fast.po.member.FastMemberAwemeFollowPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberAwemeFollowService extends BaseService {

    @Autowired
    private FastMemberAwemeFollowMapper fastMemberAwemeFollowMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberAwemeFollowPO queryById(FastMemberAwemeFollowPO item) {
        return fastMemberAwemeFollowMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberAwemeFollowPO queryById(Long id) {
        return fastMemberAwemeFollowMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberAwemeFollowPO queryOne(FastMemberAwemeFollowPO item) {
        return fastMemberAwemeFollowMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberAwemeFollowPO> queryList(FastMemberAwemeFollowPO item) {
        return fastMemberAwemeFollowMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberAwemeFollowPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberAwemeFollowPO> list = fastMemberAwemeFollowMapper.queryList(item);
        for (FastMemberAwemeFollowPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberAwemeFollowPO item) {
        return fastMemberAwemeFollowMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberAwemeFollowPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberAwemeFollowMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberAwemeFollowPO> list) {
        if (fastMemberAwemeFollowMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberAwemeFollowPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMemberAwemeFollowMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
