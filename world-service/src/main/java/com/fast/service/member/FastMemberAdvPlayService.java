/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberAdvPlayMapper;
import com.fast.po.member.FastMemberAdvPlayPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberAdvPlayService extends BaseService {

    @Autowired
    private FastMemberAdvPlayMapper fastMemberAdvPlayMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberAdvPlayPO queryById(FastMemberAdvPlayPO params) {
        return fastMemberAdvPlayMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberAdvPlayPO queryById(Integer id) {
        return fastMemberAdvPlayMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberAdvPlayPO queryOne(FastMemberAdvPlayPO params) {
        return fastMemberAdvPlayMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberAdvPlayPO> queryList(FastMemberAdvPlayPO params) {
        return fastMemberAdvPlayMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberAdvPlayPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberAdvPlayPO> list = fastMemberAdvPlayMapper.queryList(params);
        for (FastMemberAdvPlayPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberAdvPlayPO params) {
        return fastMemberAdvPlayMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberAdvPlayPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberAdvPlayMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberAdvPlayPO> list) {
        if (fastMemberAdvPlayMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberAdvPlayPO params) {
        if (fastMemberAdvPlayMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
