/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberVipOnlineMapper;
import com.fast.po.fee.FastFeeModelDetailPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.member.FastMemberVipOnlinePO;
import com.fast.service.base.BaseService;
import com.fast.service.drama.FastDramaService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.drama.FastDramaVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberVipOnlineService extends BaseService {

    @Autowired
    private FastMemberVipOnlineMapper fastMemberVipOnlineMapper;
    @Autowired
    private FastDramaService fastDramaService;

    /**
     * 判断vip是否对剧有效true：vip有效false：vip无效
     */
    public boolean checkOnline(SessionVO sessionVO, Integer dramaId) {
        String key = StaticVar.MEMBER_VIP_DRAMA_VALID + sessionVO.getMemberId() + "_" + dramaId;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            // 查询用户的vip限制
            FastMemberVipOnlinePO onPO = fastMemberVipOnlineMapper.queryById(sessionVO.getMemberId());
            if (onPO == null || onPO.getStatus() == 0) {
                // 不存在限制或限制失效
                res = StaticVar.OK;
            } else {
                Date nowDate = DateUtil.getNowDate();
                FastDramaVO dramaVO = fastDramaService.queryInfoByRedis(dramaId);
                if (onPO.getVipStart().before(nowDate) && dramaVO.getReleaseDate() != null && dramaVO.getReleaseDate().after(onPO.getOnlineDate())) {
                    res = StaticVar.FAIL;// vip无效
                    actionLogService.log("vip_online_check", "------限制上线日期--vip无效--memberId=" + sessionVO.getMemberId() + ",dramaId=" + dramaId);
                } else {
                    res = StaticVar.OK;
                }
            }
            // 缓存24小时
            RedisUtil.set(key, res, 60 * 60 * 24);
            actionLogService.log("vip_online_check", "限制上线日期--vip--res=" + res + ",memberId=" + sessionVO.getMemberId() + ",dramaId=" + dramaId);
        }
        return StaticVar.OK.equals(res);
    }

    /**
     * 支持成功后，处理订单vip类型对会员的影响，全场vip和在线vip
     */
    @Async
    public void doVip(FastFeeModelDetailPO mdPO, FastMemberOrderRechargePO morPO, Date startTime, Date vipEnd) {
        if (mdPO != null && morPO != null) {
            if (mdPO.getVipType() != null && mdPO.getVipType() == 1) {
                // 全场vip处理
                FastMemberVipOnlinePO onPO = new FastMemberVipOnlinePO();
                onPO.setId(morPO.getMemberId());
                onPO.setStatus(0);
                onPO.setUpdateTime(DateUtil.getNowDate());
                onPO.setVipEnd(vipEnd);
                fastMemberVipOnlineMapper.updateById(onPO);
                actionLogService.log("vip_type_buy", "全场-操作vip，memberId=" + morPO.getMemberId());
            } else if (mdPO.getVipType() != null && mdPO.getVipType() == 2) {
                // 查询当前配置
                FastMemberVipOnlinePO exist = queryByIdRedis(morPO.getMemberId());
                // 上线vip处理
                Date nowDate = DateUtil.getNowDate();
                FastMemberVipOnlinePO onPO = new FastMemberVipOnlinePO();
                onPO.setId(morPO.getMemberId());
                onPO.setStatus(1);
                onPO.setOnlineDate(nowDate);
                onPO.setUpdateTime(nowDate);
                onPO.setVipEnd(vipEnd);
                if (exist == null) {
                    // 新增
                    onPO.setVipStart(startTime);
                    onPO.setCreateTime(nowDate);
                    fastMemberVipOnlineMapper.insertSelective(onPO);
                } else {
                    // 更新
                    if (exist.getStatus() == 0) {
                        onPO.setVipStart(startTime);
                    }
                    fastMemberVipOnlineMapper.updateById(onPO);
                }
                actionLogService.log("vip_type_buy", "在线-操作vip，memberId=" + morPO.getMemberId());
            }
            // 清除缓存
            String key = StaticVar.MEMBER_VIP_TYPE + morPO.getMemberId();
            RedisUtil.del(key);
            // 清除解锁剧关联缓存
            List<Integer> dramaIdList = fastDramaService.queryAllIdsRedis();
            for (Integer dramaId : dramaIdList) {
                String keyItem = StaticVar.MEMBER_VIP_DRAMA_VALID + morPO.getMemberId() + "_" + dramaId;
                RedisUtil.del(keyItem);
            }
            actionLogService.log("vip_type_buy", "清缓存-操作vip，memberId=" + morPO.getMemberId());
        }
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberVipOnlinePO queryById(FastMemberVipOnlinePO params) {
        return fastMemberVipOnlineMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberVipOnlinePO queryById(Long id) {
        return fastMemberVipOnlineMapper.queryById(id);
    }

    public FastMemberVipOnlinePO queryByIdRedis(Long memberId) {
        String key = StaticVar.MEMBER_VIP_TYPE + memberId;
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            FastMemberVipOnlinePO onPO = fastMemberVipOnlineMapper.queryById(memberId);
            if (onPO != null) {
                res = JsonUtil.toString(onPO);
            } else {
                res = StaticVar.EMPTY_FLAG;
            }
            RedisUtil.set(key, res, 60 * 60);
        }
        if (StaticVar.EMPTY_FLAG.equals(res)) {
            return null;
        }
        return JsonUtil.toJavaObject(res, FastMemberVipOnlinePO.class);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberVipOnlinePO queryOne(FastMemberVipOnlinePO params) {
        return fastMemberVipOnlineMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberVipOnlinePO> queryList(FastMemberVipOnlinePO params) {
        return fastMemberVipOnlineMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberVipOnlinePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberVipOnlinePO> list = fastMemberVipOnlineMapper.queryList(params);
        for (FastMemberVipOnlinePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberVipOnlinePO params) {
        return fastMemberVipOnlineMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberVipOnlinePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberVipOnlineMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberVipOnlinePO> list) {
        if (fastMemberVipOnlineMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberVipOnlinePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberVipOnlineMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
