/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.ContentTypeEnum;
import com.fast.enums.upay.UpayTypeEnum;
import com.fast.mapper.member.FastMemberOrderContractRecordMapper;
import com.fast.po.member.FastMemberOrderContractRecordPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderContractRecordService extends BaseService {

    @Autowired
    private FastMemberOrderContractRecordMapper fastMemberOrderContractRecordMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;


    public List<FastMemberOrderContractRecordPO> query4PushList(FastMemberOrderContractRecordPO params) {
        return fastMemberOrderContractRecordMapper.query4PushList(params);
    }

    public List<FastMemberOrderContractRecordPO> query4DeductList(FastMemberOrderContractRecordPO params) {
        return fastMemberOrderContractRecordMapper.query4DeductList(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderContractRecordPO queryById(FastMemberOrderContractRecordPO params) {
        return fastMemberOrderContractRecordMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderContractRecordPO queryById(Long id) {
        return fastMemberOrderContractRecordMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderContractRecordPO queryOne(FastMemberOrderContractRecordPO params) {
        return fastMemberOrderContractRecordMapper.queryOne(params);
    }

    public FastMemberOrderContractRecordPO queryLastOne(FastMemberOrderContractRecordPO params) {
        return fastMemberOrderContractRecordMapper.queryLastOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderContractRecordPO> queryList(FastMemberOrderContractRecordPO params) {
        return fastMemberOrderContractRecordMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderContractRecordPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderContractRecordPO> list = fastMemberOrderContractRecordMapper.queryList(params);
        for (FastMemberOrderContractRecordPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getLinkType() != null && cur.getLinkType() == 3) {
                cur.setSubLinkId(cur.getLinkId());
                cur.setSubLinkName(cur.getLinkName());
                cur.setLinkId(null);
                cur.setLinkName("");
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderContractRecordPO params) {
        return fastMemberOrderContractRecordMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderContractRecordPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberOrderContractRecordMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderContractRecordPO> list) {
        if (fastMemberOrderContractRecordMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderContractRecordPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberOrderContractRecordMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateByOrderId(FastMemberOrderContractRecordPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberOrderContractRecordMapper.updateByOrderId(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }


    public ResultVO<?> exportList(SessionVO sessionVO, FastMemberOrderContractRecordPO params) {

        String key = StaticVar.EXPORT_ORDER_CONTRACT_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getUserId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        List<FastMemberOrderContractRecordPO> list = fastMemberOrderContractRecordMapper.queryList(params);
        for (FastMemberOrderContractRecordPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getLinkType() != null && cur.getLinkType() == 3) {
                cur.setSubLinkId(cur.getLinkId());
                cur.setSubLinkName(cur.getLinkName());
                cur.setLinkId(null);
                cur.setLinkName("");
            }
        }
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberOrderContractRecordPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getContractOrderId());
            CollUtil.addNoRepeat(rowHeadNames, "签约id");

            row.add(cur.getModelDetailName());
            CollUtil.addNoRepeat(rowHeadNames, "签约模版名称");

            row.add("'" + cur.getContractTemplateId());
            CollUtil.addNoRepeat(rowHeadNames, "签约模版id");

            if (Objects.nonNull(cur.getValidUnit())) {
                row.add(cur.getValidUnit() == 2 ? "月" : cur.getValidUnit() == 5 ? "季" : "");
            } else {
                row.add("");
            }
            CollUtil.addNoRepeat(rowHeadNames, "扣款周期");

            row.add(cur.getMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "首次扣款金额");

            row.add(cur.getContractMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "每期扣款金额");

            row.add(cur.getSumMoneyRecharge());
            CollUtil.addNoRepeat(rowHeadNames, "总计已代扣款金额");

            row.add(DateUtil.format07(cur.getPayTime()));
            CollUtil.addNoRepeat(rowHeadNames, "首次支付时间（签约时间）");

            row.add(cur.getExecCount());
            CollUtil.addNoRepeat(rowHeadNames, "执行代扣期数");

            row.add(cur.getSuccessCount());
            CollUtil.addNoRepeat(rowHeadNames, "代扣成功期数");

            row.add(DateUtil.format07(cur.getLastPayTime()));
            CollUtil.addNoRepeat(rowHeadNames, "最近一次代扣时间");

            row.add(DateUtil.format07(cur.getLastPayTime()));
            CollUtil.addNoRepeat(rowHeadNames, "下一次代扣时间");

            if (Objects.nonNull(cur.getContractState())) {
                row.add(cur.getContractState() == 0 ? "待签约" : cur.getContractState() == 1 ? "签约中" : "已解约");
            } else {
                row.add("");
            }
            CollUtil.addNoRepeat(rowHeadNames, "状态");

            row.add(cur.getRemark());
            CollUtil.addNoRepeat(rowHeadNames, "解约原因");

            row.add(cur.getMemberId());
            CollUtil.addNoRepeat(rowHeadNames, "用户id");

            row.add(DateUtil.format07(cur.getLinkTime()));
            CollUtil.addNoRepeat(rowHeadNames, "染色时间");

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "应用");

            row.add(cur.getDramaName());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "名称");

            row.add(cur.getSeriesNum());
            CollUtil.addNoRepeat(rowHeadNames, "充值剧集");
            if (Objects.nonNull(cur.getPayType())) {
                UpayTypeEnum upayTypeEnum = UpayTypeEnum.get(cur.getPayType());
                if (Objects.nonNull(upayTypeEnum)) {
                    row.add(upayTypeEnum.name);
                } else {
                    row.add("");
                }
            } else {
                row.add("");
            }
            CollUtil.addNoRepeat(rowHeadNames, "支付方式");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接");

            row.add(cur.getSubLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "挂载链接");

            dataList.add(row);
        }
        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }

        String title = "签约明细列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }
}
