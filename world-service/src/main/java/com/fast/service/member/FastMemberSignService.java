/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberSignMapper;
import com.fast.po.member.FastMemberSignPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberSignService extends BaseService {

    @Autowired
    private FastMemberSignMapper fastMemberSignMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberSignPO queryById(FastMemberSignPO item) {
        return fastMemberSignMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberSignPO queryById(Long id) {
        FastMemberSignPO itemParam = new FastMemberSignPO();
        itemParam.setId(id);
        return fastMemberSignMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberSignPO queryOne(FastMemberSignPO item) {
        return fastMemberSignMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberSignPO> queryList(FastMemberSignPO item) {
        return fastMemberSignMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberSignPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberSignPO> list = fastMemberSignMapper.queryList(item);
        for (FastMemberSignPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberSignPO item) {
        return fastMemberSignMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberSignPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberSignMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberSignPO> list) {
        if (fastMemberSignMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberSignPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastMemberSignMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
