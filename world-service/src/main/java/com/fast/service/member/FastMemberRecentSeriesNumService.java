/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.annotation.Master;
import com.fast.mapper.member.FastMemberRecentSeriesNumMapper;
import com.fast.po.member.FastMemberRecentSeriesNumPO;
import com.fast.service.base.BaseService;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberRecentSeriesNumService extends BaseService {

    @Autowired
    private FastMemberRecentSeriesNumMapper recentSeriesMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentSeriesNumPO queryById(FastMemberRecentSeriesNumPO item) {
        return recentSeriesMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberRecentSeriesNumPO queryById(Integer id) {
        return recentSeriesMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberRecentSeriesNumPO queryOne(FastMemberRecentSeriesNumPO item) {
        return recentSeriesMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberRecentSeriesNumPO> queryList(FastMemberRecentSeriesNumPO item) {
        return recentSeriesMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberRecentSeriesNumPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberRecentSeriesNumPO> list = recentSeriesMapper.queryList(item);
        for (FastMemberRecentSeriesNumPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberRecentSeriesNumPO item) {
        return recentSeriesMapper.queryCount(item);
    }

    /**
     * 查询maxId
     */
    @Slave
    public Long queryMaxId() {
        return recentSeriesMapper.queryMaxId();
    }

    /**
     * 新增
     */
    @Master
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(FastMemberRecentSeriesNumPO item) {
        return recentSeriesMapper.insertSelective(item);
    }

    /**
     * 更新
     */
    @Master
    @Transactional(rollbackFor = Exception.class)
    public int updateSeriesFinish(FastMemberRecentSeriesNumPO item) {
        return recentSeriesMapper.updateSeriesFinish(item);
    }
}
