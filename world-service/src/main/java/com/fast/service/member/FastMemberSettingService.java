/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberSettingMapper;
import com.fast.po.member.FastMemberSettingPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberSettingService extends BaseService {

    @Autowired
    private FastMemberSettingMapper fastMemberSettingMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberSettingPO queryById(FastMemberSettingPO params) {
        return fastMemberSettingMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberSettingPO queryById(Long id) {
        return fastMemberSettingMapper.queryById(id);
    }

    /**
     * 查询用户设置
     */
    public FastMemberSettingPO queryByIdRedis(SessionVO sessionVO) {
        String key = StaticVar.MEMBER_SETTING + sessionVO.getMemberId();
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            FastMemberSettingPO msPO = fastMemberSettingMapper.queryById(sessionVO.getMemberId());
            if (msPO == null) {
                // 不存在，则默认为连续播放
                msPO = new FastMemberSettingPO();
                msPO.setId(sessionVO.getMemberId());
                if (sessionVO.getMiniSubType() != null && sessionVO.getMiniSubType() == 3) {
                    // oppo，产品敏雨要求给默认关闭，20240914
                    msPO.setContinueFlag(0);
                } else {
                    msPO.setContinueFlag(1);
                }
            }
            res = JsonUtil.toString(msPO);
            RedisUtil.set(key, res, 60 * 60 * 6);
        }
        FastMemberSettingPO msPO = JsonUtil.toJavaObject(res, FastMemberSettingPO.class);
        return msPO;
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberSettingPO queryOne(FastMemberSettingPO params) {
        return fastMemberSettingMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberSettingPO> queryList(FastMemberSettingPO params) {
        return fastMemberSettingMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberSettingPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberSettingPO> list = fastMemberSettingMapper.queryList(params);
        for (FastMemberSettingPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberSettingPO params) {
        return fastMemberSettingMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberSettingPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberSettingMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberSettingPO> list) {
        if (fastMemberSettingMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberSettingPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberSettingMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateContinueFlag(FastMemberSettingPO params) {
        if (params.getId() == null) {
            return MethodVO.error("会员不存在");
        }
        // 查询是否存在记录
        FastMemberSettingPO msPO = fastMemberSettingMapper.queryById(params.getId());
        Date timeNow = DateUtil.getNowDate();
        if (msPO == null) {
            // 新增
            msPO = new FastMemberSettingPO();
            msPO.setContinueFlag(params.getContinueFlag());
            msPO.setId(params.getId());
            msPO.setCreateTime(timeNow);
            msPO.setUpdateTime(timeNow);
            if (fastMemberSettingMapper.insertSelective(msPO) > 0) {
                String key = StaticVar.MEMBER_SETTING + params.getId();
                RedisUtil.del(key);
                return MethodVO.success();
            }
        } else {
            // 更新
            msPO.setContinueFlag(params.getContinueFlag());
            msPO.setUpdateTime(timeNow);
            if (fastMemberSettingMapper.updateById(msPO) > 0) {
                String key = StaticVar.MEMBER_SETTING + params.getId();
                RedisUtil.del(key);
                return MethodVO.success();
            }
        }
        // 清除缓存

        return MethodVO.error("操作失败");
    }
}
