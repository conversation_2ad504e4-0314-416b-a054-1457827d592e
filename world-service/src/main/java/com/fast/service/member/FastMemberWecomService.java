/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberWecomMapper;
import com.fast.po.member.FastMemberWecomPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberWecomService extends BaseService {

    @Autowired
    private FastMemberWecomMapper fastMemberWecomMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberWecomPO queryById(FastMemberWecomPO item) {
        return fastMemberWecomMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberWecomPO queryById(Integer id) {
        return fastMemberWecomMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberWecomPO queryOne(FastMemberWecomPO item) {
        return fastMemberWecomMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberWecomPO> queryList(FastMemberWecomPO item) {
        return fastMemberWecomMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberWecomPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberWecomPO> list = fastMemberWecomMapper.queryList(item);
        for (FastMemberWecomPO cur : list) {
            cur.setEncryptionId(encode(cur.getMemberId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberWecomPO item) {
        return fastMemberWecomMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberWecomPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberWecomMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberWecomPO> list) {
        if (fastMemberWecomMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberWecomPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastMemberWecomMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
