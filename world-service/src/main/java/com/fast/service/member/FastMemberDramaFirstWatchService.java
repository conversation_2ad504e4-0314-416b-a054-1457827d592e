/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.fast.annotation.Master;
import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberDramaFirstWatchMapper;
import com.fast.po.member.FastMemberDramaFirstWatchPO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.thread.ContentTypeContext;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * C端用户首次观看日志
 *
 * <AUTHOR>
 */
@Service
public class FastMemberDramaFirstWatchService extends BaseService {

    @Autowired
    private FastMemberDramaFirstWatchMapper firstWatchMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberDramaFirstWatchPO queryById(FastMemberDramaFirstWatchPO item) {
        return firstWatchMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberDramaFirstWatchPO queryById(Integer id) {
        return firstWatchMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberDramaFirstWatchPO queryOne(FastMemberDramaFirstWatchPO item) {
        return firstWatchMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberDramaFirstWatchPO> queryList(FastMemberDramaFirstWatchPO item) {
        return firstWatchMapper.queryList(item);
    }

    /**
     * 查询全部
     */
    @Slave
    public List<FastMemberDramaFirstWatchPO> querySlaveSimpleList(FastMemberDramaFirstWatchPO item) {
        return firstWatchMapper.querySlaveSimpleList(item);
    }

    /**
     * 查询全部
     */
    public List<Long> queryMemberIds(FastMemberDramaFirstWatchPO item) {
        return firstWatchMapper.queryMemberIds(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberDramaFirstWatchPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberDramaFirstWatchPO> list = firstWatchMapper.queryList(item);
        for (FastMemberDramaFirstWatchPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberDramaFirstWatchPO item) {
        return firstWatchMapper.queryCount(item);
    }

    /**
     * 查询总数
     */
    public int queryMemberDramaCount(FastMemberDramaFirstWatchPO item) {
        return firstWatchMapper.queryMemberDramaCount(item);
    }

    /**
     * 新增
     */
    @Master
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberDramaFirstWatchPO item) {
        if (firstWatchMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberDramaFirstWatchPO item) {
        if (firstWatchMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新支付状态
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void updatePayState(FastMemberDramaFirstWatchPO item) {
        updateTodayPayState(item);
        updateH24PayState(item);
        updateH48PayState(item);
    }

    /**
     * 更新今日支付状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTodayPayState(FastMemberDramaFirstWatchPO item) {
        item.setTodayPay(1);
        item.setHour24Pay(1);
        item.setHour48Pay(1);
        firstWatchMapper.updateTodayPayState(item);
    }

    /**
     * 更新24小时支付状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateH24PayState(FastMemberDramaFirstWatchPO item) {
        item.setTodayPay(null);
        item.setHour24Pay(1);
        item.setHour48Pay(1);
        firstWatchMapper.updateH24PayState(item);
    }

    /**
     * 更新48小时支付状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateH48PayState(FastMemberDramaFirstWatchPO item) {
        item.setTodayPay(null);
        item.setHour24Pay(null);
        item.setHour48Pay(1);
        firstWatchMapper.updateH48PayState(item);
    }

    /**
     * C端用户首次观看日志清洗
     */
    @Async
    public void insertFirstWatchAsync(FastMemberRecentLogPO params) {
        ContentTypeContext.setContentType(params.getContentType());
        // C端用户首次观看日志清洗
        FastMemberDramaFirstWatchPO watch = new FastMemberDramaFirstWatchPO();
        BeanUtils.copyProperties(params, watch);
        watch.setCreateDate(watch.getCreateTime());

        try {
            if (queryMemberDramaCount(watch) == 0) {
                insert(watch);
            }
        } catch (Exception e) {
            log.error("首次观看日志清洗失败:", e);
        }
        ContentTypeContext.clear();
    }
}
