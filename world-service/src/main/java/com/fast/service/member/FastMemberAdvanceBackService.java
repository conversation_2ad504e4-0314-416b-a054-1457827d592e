/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberAdvanceBackMapper;
import com.fast.mapper.member.FastMemberLinkAdvMapper;
import com.fast.mapper.promote.FastMemberLinkMapper;
import com.fast.po.member.FastMemberAdvanceBackPO;
import com.fast.po.member.FastMemberLinkAdvPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.promote.FastMemberLinkPO;
import com.fast.service.base.BaseService;
import com.fast.service.promote.FastLinkService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberAdvanceBackService extends BaseService {

    @Autowired
    private FastMemberAdvanceBackMapper fastMemberAdvanceBackMapper;
    @Autowired
    private FastMemberLinkMapper fastMemberLinkMapper;
    @Autowired
    private FastLinkService fastLinkService;
    @Autowired
    private FastMemberLinkAdvMapper fastMemberLinkAdvMapper;


    /**
     * 批量预回传用户订单
     */
    public ResultVO doAdvanceBackBatch(List<FastMemberAdvanceBackPO> advanceList) {
        for (FastMemberAdvanceBackPO item : advanceList) {
            System.out.println(item.getMemberId() + "_" + item.getMoneyRecharge());
            FastMemberLinkPO mlParam = new FastMemberLinkPO();
            mlParam.setMemberId(item.getMemberId());
            FastMemberLinkPO mlPO = fastMemberLinkMapper.queryOne(mlParam);
            if (mlPO == null) {
                continue;
            }
            FastLinkPO fastLinkPO = fastLinkService.queryById(mlPO.getLastLinkId());
            if (fastLinkPO == null) {
                continue;
            }
            FastMemberLinkAdvPO mlaPO = fastMemberLinkAdvMapper.queryById(item.getMemberId());

            Date timeNow = DateUtil.getNowDate();
            // 添加预回传
            FastMemberAdvanceBackPO abPO = new FastMemberAdvanceBackPO();
            abPO.setAdvMediaId(fastLinkPO.getAdvMediaId());
            abPO.setAppType(fastLinkPO.getAppType());
            abPO.setMemberId(item.getMemberId());
            abPO.setLinkId(mlPO.getLastLinkId());
            abPO.setRechargeRateValue(item.getRechargeRateValue());
            abPO.setPromotionId(mlPO.getPromotionId());
            abPO.setProjectId(mlPO.getProjectId());
            abPO.setMoneyRecharge(item.getMoneyRecharge());
            if (mlaPO != null) {
                abPO.setMaterialId(mlaPO.getMaterialId());
            }
            abPO.setLikeRateValue(item.getLikeRateValue());
            abPO.setCreateTime(timeNow);
            fastMemberAdvanceBackMapper.insertSelective(abPO);

            FastMemberOrderRechargePO morPO = new FastMemberOrderRechargePO();
            morPO.setId(abPO.getId());
            morPO.setLinkId(mlPO.getLastLinkId());
            morPO.setMoneyRecharge(item.getMoneyRecharge());
            morPO.setAdvanceFlag(1);
            morPO.setMemberId(item.getMemberId());
            morPO.setPayTime(timeNow);

            // if (fastLinkPO.getAdvMediaId() == 1 && fastLinkPO.getAppType() == 1) {
            //     // 巨量广告媒体 ---> 微信小程序
            //     fastBackRuleService.backToMediaToutiao(morPO);
            // } else if (fastLinkPO.getAdvMediaId() == 1 && fastLinkPO.getAppType() == 2) {
            // 	// 巨量广告媒体 ---> 抖音小程序
            //     fastBackRuleService.backToMediaToutiaoTiktok(morPO);
            // }

        }
        return ResultVO.success();
    }


    /**
     * 通过id查询单个对象
     */
    public FastMemberAdvanceBackPO queryById(FastMemberAdvanceBackPO params) {
        return fastMemberAdvanceBackMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberAdvanceBackPO queryById(Integer id) {
        return fastMemberAdvanceBackMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberAdvanceBackPO queryOne(FastMemberAdvanceBackPO params) {
        return fastMemberAdvanceBackMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberAdvanceBackPO> queryList(FastMemberAdvanceBackPO params) {
        return fastMemberAdvanceBackMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberAdvanceBackPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberAdvanceBackPO> list = fastMemberAdvanceBackMapper.queryList(params);
        for (FastMemberAdvanceBackPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberAdvanceBackPO params) {
        return fastMemberAdvanceBackMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberAdvanceBackPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberAdvanceBackMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberAdvanceBackPO> list) {
        if (fastMemberAdvanceBackMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberAdvanceBackPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberAdvanceBackMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
