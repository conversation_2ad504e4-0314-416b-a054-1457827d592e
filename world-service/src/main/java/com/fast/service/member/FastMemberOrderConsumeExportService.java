/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.ContentTypeEnum;
import com.fast.enums.MiniTypeEnum;
import com.fast.po.member.FastMemberOrderConsumePO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 消费记录
 *
 * <AUTHOR>
 */
@Service
public class FastMemberOrderConsumeExportService extends BaseService {

    @Autowired
    private FastMemberOrderConsumeService dataService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 消费记录-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportMemberConsumeList(SessionVO sessionVO, FastMemberOrderConsumePO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_CONSUME_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        List<FastMemberOrderConsumePO> list = dataService.queryList(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberOrderConsumePO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getMemberId());
            CollUtil.addNoRepeat(rowHeadNames, "用户ID");

            row.add(cur.getMemberName());
            CollUtil.addNoRepeat(rowHeadNames, "昵称");

            row.add(cur.getDramaName());
            if (sessionVO.getContentType() == 3) {
                CollUtil.addNoRepeat(rowHeadNames, "书籍名称");
            } else {
                CollUtil.addNoRepeat(rowHeadNames, "消费" + ContentTypeEnum.getName(ObjectUtils.defaultIfNull(params.getContentType(), ContentTypeEnum.DRAMA.index)));
            }

            row.add("第" + cur.getSeriesNum() + "集");
            if (sessionVO.getContentType() == 3) {
                CollUtil.addNoRepeat(rowHeadNames, "章节名称");
            } else {
                CollUtil.addNoRepeat(rowHeadNames, "分集名称");
            }

            row.add("K币消费");
            CollUtil.addNoRepeat(rowHeadNames, "消费类型");

            row.add(cur.getCoinConsume());
            CollUtil.addNoRepeat(rowHeadNames, "消费K币数");

            row.add(cur.getLinkId());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接ID");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接名称");

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "应用");

            row.add(MiniTypeEnum.getName(cur.getMiniType()));
            CollUtil.addNoRepeat(rowHeadNames, "应用类型");

            row.add(cur.getOfficialName());
            CollUtil.addNoRepeat(rowHeadNames, "公众号");

            row.add(DateUtil.format07(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "消费时间");

            row.add(DateUtil.format07(cur.getRegisterTime()));
            CollUtil.addNoRepeat(rowHeadNames, "注册时间");

            row.add(cur.getRetailName());
            CollUtil.addNoRepeat(rowHeadNames, "归属分销商");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "消费记录";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }
}
