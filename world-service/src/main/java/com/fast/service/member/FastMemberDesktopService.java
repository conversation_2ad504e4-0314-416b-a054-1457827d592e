/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberDesktopMapper;
import com.fast.po.member.FastMemberDesktopPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberDesktopService extends BaseService {

    @Autowired
    private FastMemberDesktopMapper fastMemberDesktopMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberDesktopPO queryById(FastMemberDesktopPO params) {
        return fastMemberDesktopMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberDesktopPO queryById(Long id) {
        return fastMemberDesktopMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberDesktopPO queryOne(FastMemberDesktopPO params) {
        return fastMemberDesktopMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberDesktopPO> queryList(FastMemberDesktopPO params) {
        return fastMemberDesktopMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberDesktopPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberDesktopPO> list = fastMemberDesktopMapper.queryList(params);
        for (FastMemberDesktopPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberDesktopPO params) {
        return fastMemberDesktopMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(SessionVO sessionVO, FastMemberDesktopPO params) {
        // 判断是否已经存在
        String key = StaticVar.MEMBER_DESKTOP + sessionVO.getMemberId();
        String res = RedisUtil.get(key);
        Date timeNow = DateUtil.getNowDate();
        Long id = 0L;
        int existFlag = 0; // 是否存在0否1是
        if (StrUtil.isEmpty(res)) {
            // 库内查询
            FastMemberDesktopPO dParam = new FastMemberDesktopPO();
            dParam.setMemberId(sessionVO.getMemberId());
            FastMemberDesktopPO dPO = fastMemberDesktopMapper.queryOne(dParam);
            if (dPO != null && dPO.getMemberId() != null) {
                existFlag = 1;
                id = dPO.getId();
            }
        } else {
            id = Long.valueOf(res);
            existFlag = 1;
        }
        if (existFlag == 0) {
            // 新增
            FastMemberDesktopPO dPO = new FastMemberDesktopPO();
            dPO.setAddDate(DateUtil.format09(DateUtil.format07(timeNow)));
            dPO.setCreateTime(timeNow);
            dPO.setFirstAddTime(timeNow);
            dPO.setLastAddTime(timeNow);
            dPO.setLinkId(sessionVO.getLinkId());
            dPO.setMemberId(sessionVO.getMemberId());
            dPO.setMiniId(sessionVO.getMiniId());
            dPO.setOfficialId(sessionVO.getOfficialId());
            dPO.setRetailId(sessionVO.getRetailId());
            if (params.getState() != null) {
                dPO.setState(params.getState());
            }
            fastMemberDesktopMapper.insertSelective(dPO);
            RedisUtil.set(key, dPO.getId().toString(), 60 * 60 * 24 * 7);
        } else {
            // 更新
            FastMemberDesktopPO dPO = fastMemberDesktopMapper.queryById(id);
            if (dPO != null) {
                dPO.setLastAddTime(timeNow);
                dPO.setAddDate(DateUtil.format09(DateUtil.format07(timeNow)));
                if (params.getState() != null) {
                    dPO.setState(params.getState());
                }
                fastMemberDesktopMapper.updateById(dPO);
                // 延长缓存
                RedisUtil.set(key, dPO.getId().toString(), 60 * 60 * 24 * 7);
            } else {
                log.error("更新加桌记录，id=" + id + "加桌记录不存在");
            }
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberDesktopPO> list) {
        if (fastMemberDesktopMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

}
