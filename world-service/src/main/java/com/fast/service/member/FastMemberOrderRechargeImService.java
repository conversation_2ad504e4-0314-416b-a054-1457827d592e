/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberOrderRechargeImMapper;
import com.fast.mapper.setting.FastSettingSystemMapper;
import com.fast.po.member.FastMemberOrderRechargeImPO;
import com.fast.service.base.BaseService;
import com.fast.service.feishu.FeiShuService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.mini.FastMiniSettingService;
import com.fast.service.setting.FastSettingSystemService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderRechargeImService extends BaseService {

    @Autowired
    private FastMemberOrderRechargeImMapper fastMemberOrderRechargeImMapper;
    @Autowired
    private FastSettingSystemMapper fastSettingSystemMapper;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMiniSettingService fastMiniSettingService;
    @Autowired
    private FastSettingSystemService fastSettingSystemService;
    @Autowired
    private FeiShuService feiShuService;

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeImPO queryById(FastMemberOrderRechargeImPO params) {
        return fastMemberOrderRechargeImMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeImPO queryById(Integer id) {
        return fastMemberOrderRechargeImMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderRechargeImPO queryOne(FastMemberOrderRechargeImPO params) {
        return fastMemberOrderRechargeImMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderRechargeImPO> queryList(FastMemberOrderRechargeImPO params) {
        return fastMemberOrderRechargeImMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderRechargeImPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderRechargeImPO> list = fastMemberOrderRechargeImMapper.queryList(params);
        for (FastMemberOrderRechargeImPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderRechargeImPO params) {
        return fastMemberOrderRechargeImMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderRechargeImPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberOrderRechargeImMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    @Async
    public void insertAsync(FastMemberOrderRechargeImPO params) {
        actionLogService.log("im_pull", "待入库im表" + JsonUtil.toString(params));
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberOrderRechargeImMapper.insertSelective(params) > 0) {
            actionLogService.log("im_pull", "待入库im表,成功" + params.getMemberId());
        } else {
            actionLogService.log("im_pull", "待入库im表,失败" + params.getMemberId());
        }
    }

    @Async
    public void updateStateAsync(Long id) {
        FastMemberOrderRechargeImPO imPO = new FastMemberOrderRechargeImPO();
        imPO.setId(id);
        imPO.setState(1);
        imPO.setUpdateTime(DateUtil.getNowDate());
        fastMemberOrderRechargeImMapper.updateById(imPO);
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderRechargeImPO> list) {
        if (fastMemberOrderRechargeImMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderRechargeImPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberOrderRechargeImMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

}
