/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberBackDetailMapper;
import com.fast.po.member.FastMemberBackDetailPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberBackDetailService extends BaseService {

    @Autowired
    private FastMemberBackDetailMapper fastMemberBackDetailMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberBackDetailPO queryById(FastMemberBackDetailPO params) {
        return fastMemberBackDetailMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberBackDetailPO queryById(Integer id) {
        return fastMemberBackDetailMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberBackDetailPO queryOne(FastMemberBackDetailPO params) {
        return fastMemberBackDetailMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberBackDetailPO> queryList(FastMemberBackDetailPO params) {
        return fastMemberBackDetailMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberBackDetailPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberBackDetailPO> list = fastMemberBackDetailMapper.queryList(params);
        for (FastMemberBackDetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberBackDetailPO params) {
        return fastMemberBackDetailMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberBackDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberBackDetailMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberBackDetailPO> list) {
        if (fastMemberBackDetailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberBackDetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberBackDetailMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
