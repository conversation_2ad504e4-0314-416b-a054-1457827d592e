/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberPhoneMapper;
import com.fast.po.member.FastMemberPhonePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberPhoneService extends BaseService {

    @Autowired
    private FastMemberPhoneMapper fastMemberPhoneMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberPhonePO queryById(FastMemberPhonePO params) {
        return fastMemberPhoneMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberPhonePO queryById(Long id) {
        return fastMemberPhoneMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberPhonePO queryOne(FastMemberPhonePO params) {
        return fastMemberPhoneMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberPhonePO> queryList(FastMemberPhonePO params) {
        return fastMemberPhoneMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberPhonePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberPhonePO> list = fastMemberPhoneMapper.queryList(params);
        for (FastMemberPhonePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberPhonePO params) {
        return fastMemberPhoneMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberPhonePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberPhoneMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberPhonePO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberPhoneMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
