/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberFollowMapper;
import com.fast.po.member.FastMemberFollowPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberFollowService extends BaseService {

    @Autowired
    private FastMemberFollowMapper fastMemberFollowMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberFollowPO queryById(FastMemberFollowPO item) {
        return fastMemberFollowMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberFollowPO queryById(Integer id) {
        return fastMemberFollowMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberFollowPO queryOne(FastMemberFollowPO item) {
        return fastMemberFollowMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberFollowPO> queryList(FastMemberFollowPO item) {
        return fastMemberFollowMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberFollowPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberFollowPO> list = fastMemberFollowMapper.queryList(item);
        for (FastMemberFollowPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberFollowPO item) {
        return fastMemberFollowMapper.queryCount(item);
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberFollowPO> list) {
        if (fastMemberFollowMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberFollowPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberFollowMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
