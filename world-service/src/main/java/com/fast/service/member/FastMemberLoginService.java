/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberLoginMapper;
import com.fast.po.member.FastMemberLoginPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberLoginService extends BaseService {

    @Autowired
    private FastMemberLoginMapper fastMemberLoginMapper;

    /**
     * 更新用户登录状态
     */
    public int updateMemberLogin(Long memberId, Integer login) {
        Date nowTime = DateUtil.getNowDate();
        FastMemberLoginPO mlPO = new FastMemberLoginPO();
        mlPO.setId(memberId);
        mlPO.setLogin(login);
        mlPO.setCreateTime(nowTime);
        mlPO.setUpdateTime(nowTime);
        return fastMemberLoginMapper.insertSelective(mlPO);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberLoginPO queryById(FastMemberLoginPO params) {
        return fastMemberLoginMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberLoginPO queryById(Long id) {
        return fastMemberLoginMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberLoginPO queryOne(FastMemberLoginPO params) {
        return fastMemberLoginMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberLoginPO> queryList(FastMemberLoginPO params) {
        return fastMemberLoginMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberLoginPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberLoginPO> list = fastMemberLoginMapper.queryList(params);
        for (FastMemberLoginPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberLoginPO params) {
        return fastMemberLoginMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberLoginPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberLoginMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberLoginPO> list) {
        if (fastMemberLoginMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberLoginPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberLoginMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
