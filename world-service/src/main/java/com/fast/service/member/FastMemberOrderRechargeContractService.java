/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberOrderRechargeContractMapper;
import com.fast.po.member.FastMemberOrderRechargeContractPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderRechargeContractService extends BaseService {

    @Autowired
    private FastMemberOrderRechargeContractMapper fastMemberOrderRechargeContractMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeContractPO queryById(FastMemberOrderRechargeContractPO params) {
        return fastMemberOrderRechargeContractMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeContractPO queryById(Long id) {
        return fastMemberOrderRechargeContractMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderRechargeContractPO queryOne(FastMemberOrderRechargeContractPO params) {
        return fastMemberOrderRechargeContractMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderRechargeContractPO> queryList(FastMemberOrderRechargeContractPO params) {
        return fastMemberOrderRechargeContractMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderRechargeContractPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderRechargeContractPO> list = fastMemberOrderRechargeContractMapper.queryList(params);
        for (FastMemberOrderRechargeContractPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderRechargeContractPO params) {
        return fastMemberOrderRechargeContractMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderRechargeContractPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberOrderRechargeContractMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderRechargeContractPO> list) {
        if (fastMemberOrderRechargeContractMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderRechargeContractPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberOrderRechargeContractMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
