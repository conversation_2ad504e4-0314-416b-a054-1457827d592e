/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberOrderRechargeSdkBackMapper;
import com.fast.po.member.FastMemberOrderRechargeSdkBackPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderRechargeSdkBackService extends BaseService {

    @Autowired
    private FastMemberOrderRechargeSdkBackMapper fastMemberOrderRechargeSdkBackMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeSdkBackPO queryById(FastMemberOrderRechargeSdkBackPO params) {
        return fastMemberOrderRechargeSdkBackMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeSdkBackPO queryById(Integer id) {
        return fastMemberOrderRechargeSdkBackMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderRechargeSdkBackPO queryOne(FastMemberOrderRechargeSdkBackPO params) {
        return fastMemberOrderRechargeSdkBackMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderRechargeSdkBackPO> queryList(FastMemberOrderRechargeSdkBackPO params) {
        return fastMemberOrderRechargeSdkBackMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderRechargeSdkBackPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderRechargeSdkBackPO> list = fastMemberOrderRechargeSdkBackMapper.queryList(params);
        for (FastMemberOrderRechargeSdkBackPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderRechargeSdkBackPO params) {
        return fastMemberOrderRechargeSdkBackMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderRechargeSdkBackPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberOrderRechargeSdkBackMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderRechargeSdkBackPO> list) {
        if (fastMemberOrderRechargeSdkBackMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderRechargeSdkBackPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastMemberOrderRechargeSdkBackMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
