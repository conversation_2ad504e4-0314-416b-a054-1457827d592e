/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.member.FastMemberYoungResetMapper;
import com.fast.po.member.FastMemberYoungResetPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberYoungResetService extends BaseService {

    @Autowired
    private FastMemberYoungResetMapper fastMemberYoungMapper;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberYoungResetPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberYoungResetPO> list = fastMemberYoungMapper.queryList(params);
        for (FastMemberYoungResetPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 导出
     */
    public ResultVO<?> exportList(SessionVO sessionVO, FastMemberYoungResetPO item) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_REFUND_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        item.setLimitExport(StaticVar.MILLION);
        List<FastMemberYoungResetPO> list = fastMemberYoungMapper.queryList(item);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberYoungResetPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getOpenid());
            CollUtil.addNoRepeat(rowHeadNames, "openid");

            row.add(cur.getMemberId());
            CollUtil.addNoRepeat(rowHeadNames, "用户id");

            row.add(cur.getRetailName());
            CollUtil.addNoRepeat(rowHeadNames, "用户归属");

            row.add(cur.getOfficialName());
            CollUtil.addNoRepeat(rowHeadNames, "公众号");

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "归属应用");

            if (cur.getMiniType() == 1) {
                row.add("微信小程序");
            } else if (cur.getMiniType() == 2) {
                row.add("抖音小程序");
            } else if (cur.getMiniType() == 3) {
                row.add("H5");
            } else if (cur.getMiniType() == 4) {
                row.add("快手小程序");
            } else if (cur.getMiniType() == 5) {
                row.add("快应用小程序");
            }
            CollUtil.addNoRepeat(rowHeadNames, "应用类型");

            row.add(cur.getRemark());
            CollUtil.addNoRepeat(rowHeadNames, "备注");

            row.add(cur.getUserName());
            CollUtil.addNoRepeat(rowHeadNames, "操作员");

            row.add(DateUtil.format07(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "操作时间");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "重置青少年密码";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }
}
