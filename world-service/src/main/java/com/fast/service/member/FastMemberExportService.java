/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticMsg;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.po.member.FastMemberPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 导出
 *
 * <AUTHOR>
 */
@Service
public class FastMemberExportService extends BaseService {

    @Autowired
    private FastMemberService dataService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;

    /**
     * 用户记录-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    public ResultVO<?> exportMemberList(SessionVO sessionVO, FastMemberPO params) {
        // 判断导出次数是否超限
        String key = StaticVar.EXPORT_MEMBER_LIST + DateUtil.getNowTime06Str() + "_" + sessionVO.getRetailId();
        Integer value = RedisUtil.getInteger(key);
        if (value != null && value > StaticVar.EXPORT_NUMBER) {
            return ResultVO.error(StaticMsg.MSG_ERROR_29);
        }
        params.setLimitExport(StaticVar.MILLION);
        List<FastMemberPO> list = dataService.queryList(params);
        if (CollUtil.isEmpty(list)) {
            return ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getOpenid());
            CollUtil.addNoRepeat(rowHeadNames, "openid");

            row.add(cur.getId());
            CollUtil.addNoRepeat(rowHeadNames, "用户ID");

            row.add(cur.getMemberName());
            CollUtil.addNoRepeat(rowHeadNames, "昵称");

            if (cur.getSex() == null) {
                cur.setSex(0);
            }
            row.add(cur.getSex() == 1 ? "男" : cur.getSex() == 2 ? "女" : "未知");
            CollUtil.addNoRepeat(rowHeadNames, "性别");

            row.add(cur.getVipState() == 1 ? "是" : "否");
            CollUtil.addNoRepeat(rowHeadNames, "是否VIP");

            row.add(cur.getRechargeMoney());
            CollUtil.addNoRepeat(rowHeadNames, "充值金额");

            row.add(cur.getRechargeCount());
            CollUtil.addNoRepeat(rowHeadNames, "充值次数");

            row.add(cur.getCoinRemain());
            CollUtil.addNoRepeat(rowHeadNames, "K币余额");

            row.add(cur.getPlayCount());
            CollUtil.addNoRepeat(rowHeadNames, "播放次数");

            row.add(cur.getOfficialName());
            CollUtil.addNoRepeat(rowHeadNames, "归属公众号");

            row.add(cur.getOfficialFollowState() == 1 ? "已关注" : "未关注");
            CollUtil.addNoRepeat(rowHeadNames, "关注状态");

            row.add(cur.getPhoneOs() == 1 ? "Androids系统" : "IOS系统");
            CollUtil.addNoRepeat(rowHeadNames, "手机系统");

            row.add(DateUtil.format07(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "注册时间");

            row.add(cur.getRetailName());
            CollUtil.addNoRepeat(rowHeadNames, "归属分销商");

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "归属应用");

            row.add(cur.getLinkId());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接ID");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接名称");

            row.add(cur.getSubLinkId());
            CollUtil.addNoRepeat(rowHeadNames, "次归属链接ID");

            row.add(cur.getSubLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "次归属链接名称");

            // 微信视频号
            if (biggerZero(cur.getLinkSubType()) && cur.getLinkSubType() == 5) {
                row.add(cur.getWxFinderName());
                CollUtil.addNoRepeat(rowHeadNames, "视频号名称");

                row.add(cur.getWxDramaId());
                CollUtil.addNoRepeat(rowHeadNames, "视频号内容ID");

                row.add(cur.getWxPromotionId());
                CollUtil.addNoRepeat(rowHeadNames, "加热订单ID");

                row.add("微信视频号推广");
                CollUtil.addNoRepeat(rowHeadNames, "推广类型");
            }

            row.add(DateUtil.format07(cur.getLinkTime()));
            CollUtil.addNoRepeat(rowHeadNames, "染色时间");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "用户管理-用户列表";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        return ResultVO.success();
    }

}
