/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberCoinMoneyMonthMapper;
import com.fast.mapper.order.FastMemberOrderDailyLogMapper;
import com.fast.po.member.FastMemberCoinMoneyMonthPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.order.FastMemberOrderDailyLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberCoinMoneyMonthService extends BaseService {

    @Autowired
    private FastMemberOrderRechargeService dataRechargeService;
    @Autowired
    private FastMemberCoinMoneyMonthMapper coinMoneyMonthMapper;
    @Autowired
    private FastMemberOrderDailyLogMapper dailyLogMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberCoinMoneyMonthPO queryById(FastMemberCoinMoneyMonthPO item) {
        return coinMoneyMonthMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberCoinMoneyMonthPO queryById(Integer id) {
        return coinMoneyMonthMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberCoinMoneyMonthPO queryOne(FastMemberCoinMoneyMonthPO item) {
        return coinMoneyMonthMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberCoinMoneyMonthPO> queryList(FastMemberCoinMoneyMonthPO item) {
        return coinMoneyMonthMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberCoinMoneyMonthPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberCoinMoneyMonthPO> list = coinMoneyMonthMapper.queryList(item);
        for (FastMemberCoinMoneyMonthPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberCoinMoneyMonthPO item) {
        return coinMoneyMonthMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberCoinMoneyMonthPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (coinMoneyMonthMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    public void auto() {
        // 循环内容类型
        for (int type = 1; type <= 3; type++) {
            Date month = DateUtil.addMonths(DateUtil.getNowDate(), -1);
            FastMemberCoinMoneyMonthPO countQ = new FastMemberCoinMoneyMonthPO();
            countQ.setContentType(type);
            countQ.setMonth(DateUtil.format05Int(month));
            if (coinMoneyMonthMapper.queryCount(countQ) > 0) {
                return;
            }
            String s = DateUtil.format10(month);
            FastMemberOrderRechargePO params = new FastMemberOrderRechargePO();
            params.setContentType(type);
            params.setMonth(s);
            params.setCreateTimeStr(s + "-" + s);
            List<Date> date = DateUtil.getStartEndMonthDate(params.getCreateTimeStr());
            params.setCreateTimeS(date.get(0));
            params.setCreateTimeE(date.get(1));

            List<FastMemberOrderRechargePO> list = dataRechargeService.getMemberRechargeCoinListAuto(params);
            for (FastMemberOrderRechargePO cur : list) {
                FastMemberCoinMoneyMonthPO moneyMonth = new FastMemberCoinMoneyMonthPO();
                moneyMonth.setContentType(type);
                moneyMonth.setMonth(DateUtil.format05Int(month));
                moneyMonth.setMiniId(cur.getMiniId());
                moneyMonth.setMoneyRecharge(cur.getMoneyRecharge());
                moneyMonth.setCoinRecharge(cur.getCoinRecharge());
                moneyMonth.setCoinGive(cur.getCoinGive());
                moneyMonth.setCoinRechargeConsume(cur.getCoinRechargeConsume());
                moneyMonth.setCoinGiveConsume(cur.getCoinGiveConsume());
                moneyMonth.setMoneyVipRecharge(cur.getMoneyVipRecharge());

                // 计算VIP消耗金额
                FastMemberOrderDailyLogPO dailyLogPO = new FastMemberOrderDailyLogPO();
                dailyLogPO.setCreateTimeS(date.get(0));
                dailyLogPO.setCreateTimeE(date.get(1));
                dailyLogPO.setMiniId(cur.getMiniId());
                dailyLogPO.setContentType(type);
                BigDecimal consumeMoney = defaultIfNull(dailyLogMapper.querySumData(dailyLogPO), BigDecimal.ZERO);
                moneyMonth.setMoneyVipConsume(consumeMoney);

                coinMoneyMonthMapper.insertSelective(moneyMonth);

                // 计算当月的剩余金额/K币
                FastMemberCoinMoneyMonthPO monthQ = new FastMemberCoinMoneyMonthPO();
                monthQ.setMiniId(cur.getMiniId());
                monthQ.setMonthMax(DateUtil.format05Int(month));
                monthQ.setContentType(type);
                FastMemberCoinMoneyMonthPO sumData = coinMoneyMonthMapper.querySumData(monthQ);
                moneyMonth.setCoinRechargeRemain(sumData.getCoinRecharge() - sumData.getCoinRechargeConsume());
                moneyMonth.setCoinGiveRemain(sumData.getCoinGive() - sumData.getCoinGiveConsume());
                moneyMonth.setMoneyVipRemain(DoubleUtil.subB(sumData.getMoneyVipRecharge(), sumData.getMoneyVipConsume()));

                coinMoneyMonthMapper.updateRemainById(moneyMonth);
            }
        }
    }

    /**
     * 更新-全部-可废弃
     */
    public void auto_all() {
        // 循环内容类型
        for (int type = 1; type <= 3; type++) {
            Date first = DateUtil.format05("202209");
            FastMemberCoinMoneyMonthPO countQ = new FastMemberCoinMoneyMonthPO();
            countQ.setContentType(type);
            countQ.setMonth(DateUtil.format05Int(first));
            if (coinMoneyMonthMapper.queryCount(countQ) > 0) {
                return;
            }
            for (int i = 0; i < 5; i++) {
                Date month = DateUtil.addMonths(first, i);
                String s = DateUtil.format10(month);
                FastMemberOrderRechargePO params = new FastMemberOrderRechargePO();
                params.setContentType(type);
                params.setMonth(s);
                params.setCreateTimeStr(s + "-" + s);
                List<Date> date = DateUtil.getStartEndMonthDate(params.getCreateTimeStr());
                params.setCreateTimeS(date.get(0));
                params.setCreateTimeE(date.get(1));

                List<FastMemberOrderRechargePO> list = dataRechargeService.getMemberRechargeCoinListAuto(params);
                for (FastMemberOrderRechargePO cur : list) {
                    FastMemberCoinMoneyMonthPO moneyMonth = new FastMemberCoinMoneyMonthPO();
                    moneyMonth.setContentType(type);
                    moneyMonth.setMonth(DateUtil.format05Int(month));
                    moneyMonth.setMiniId(cur.getMiniId());
                    moneyMonth.setMoneyRecharge(cur.getMoneyRecharge());
                    moneyMonth.setCoinRecharge(cur.getCoinRecharge());
                    moneyMonth.setCoinGive(cur.getCoinGive());
                    moneyMonth.setCoinRechargeConsume(cur.getCoinRechargeConsume());
                    moneyMonth.setCoinGiveConsume(cur.getCoinGiveConsume());
                    moneyMonth.setMoneyVipRecharge(cur.getMoneyVipRecharge());
                    // moneyMonth.setMoneyVipConsume(cur.getMoneyVipConsume());

                    // 计算VIP消耗金额
                    FastMemberOrderDailyLogPO dailyLogPO = new FastMemberOrderDailyLogPO();
                    dailyLogPO.setCreateTimeS(date.get(0));
                    dailyLogPO.setCreateTimeE(date.get(1));
                    dailyLogPO.setMiniId(cur.getMiniId());
                    dailyLogPO.setContentType(type);
                    BigDecimal consumeMoney = defaultIfNull(dailyLogMapper.querySumData(dailyLogPO), BigDecimal.ZERO);
                    moneyMonth.setMoneyVipConsume(consumeMoney);

                    coinMoneyMonthMapper.insertSelective(moneyMonth);

                    // 计算当月的剩余金额/K币
                    FastMemberCoinMoneyMonthPO monthQ = new FastMemberCoinMoneyMonthPO();
                    monthQ.setMiniId(cur.getMiniId());
                    monthQ.setMonthMax(DateUtil.format05Int(month));
                    monthQ.setContentType(type);
                    FastMemberCoinMoneyMonthPO sumData = coinMoneyMonthMapper.querySumData(monthQ);
                    moneyMonth.setCoinRechargeRemain(sumData.getCoinRecharge() - sumData.getCoinRechargeConsume());
                    moneyMonth.setCoinGiveRemain(sumData.getCoinGive() - sumData.getCoinGiveConsume());
                    moneyMonth.setMoneyVipRemain(DoubleUtil.subB(sumData.getMoneyVipRecharge(), sumData.getMoneyVipConsume()));

                    coinMoneyMonthMapper.updateRemainById(moneyMonth);
                }
            }
        }
    }
}
