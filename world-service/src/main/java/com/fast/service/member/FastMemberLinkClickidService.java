/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberLinkClickidMapper;
import com.fast.po.member.FastMemberLinkClickidPO;
import com.fast.service.base.BaseService;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberLinkClickidService extends BaseService {

    @Autowired
    private FastMemberLinkClickidMapper fastMemberLinkClickidMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkClickidPO queryById(FastMemberLinkClickidPO item) {
        return fastMemberLinkClickidMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberLinkClickidPO queryById(Long id) {
        return fastMemberLinkClickidMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberLinkClickidPO queryOne(FastMemberLinkClickidPO item) {
        return fastMemberLinkClickidMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberLinkClickidPO> queryList(FastMemberLinkClickidPO item) {
        return fastMemberLinkClickidMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberLinkClickidPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberLinkClickidPO> list = fastMemberLinkClickidMapper.queryList(item);
        for (FastMemberLinkClickidPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberLinkClickidPO item) {
        return fastMemberLinkClickidMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberLinkClickidPO item) {
        if (fastMemberLinkClickidMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberLinkClickidPO> list) {
        if (fastMemberLinkClickidMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

}
