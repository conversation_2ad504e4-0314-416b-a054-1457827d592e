/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberLinkLoadMapper;
import com.fast.mapper.member.FastMemberOrderRechargeLoadMapper;
import com.fast.po.member.FastMemberLinkLoadPO;
import com.fast.po.member.FastMemberOrderRechargeLoadPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberOrderRechargeLoadService extends BaseService {

    @Autowired
    private FastMemberOrderRechargeLoadMapper fastMemberOrderRechargeLoadMapper;
    @Autowired
    private FastMemberLinkLoadMapper FastMemberLinkLoadMapper;


    public MethodVO addDependId(Long orderId, Long memberId) {
        // 查询用户是否存在depend
        FastMemberLinkLoadPO llPO = FastMemberLinkLoadMapper.queryById(memberId);
        if (llPO != null) {
            FastMemberOrderRechargeLoadPO rlPO = new FastMemberOrderRechargeLoadPO();
            rlPO.setId(orderId);
            rlPO.setDemandId(llPO.getDemandId());
            rlPO.setCreateTime(DateUtil.getNowDate());
            fastMemberOrderRechargeLoadMapper.insertSelective(rlPO);
        }
        return MethodVO.success();
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeLoadPO queryById(FastMemberOrderRechargeLoadPO params) {
        return fastMemberOrderRechargeLoadMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberOrderRechargeLoadPO queryById(Integer id) {
        return fastMemberOrderRechargeLoadMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberOrderRechargeLoadPO queryOne(FastMemberOrderRechargeLoadPO params) {
        return fastMemberOrderRechargeLoadMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastMemberOrderRechargeLoadPO> queryList(FastMemberOrderRechargeLoadPO params) {
        return fastMemberOrderRechargeLoadMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberOrderRechargeLoadPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberOrderRechargeLoadPO> list = fastMemberOrderRechargeLoadMapper.queryList(params);
        for (FastMemberOrderRechargeLoadPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberOrderRechargeLoadPO params) {
        return fastMemberOrderRechargeLoadMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberOrderRechargeLoadPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastMemberOrderRechargeLoadMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberOrderRechargeLoadPO> list) {
        if (fastMemberOrderRechargeLoadMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberOrderRechargeLoadPO params) {
        if (fastMemberOrderRechargeLoadMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
