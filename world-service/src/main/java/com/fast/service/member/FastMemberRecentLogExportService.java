/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticVar;
import com.fast.enums.ContentTypeEnum;
import com.fast.enums.MiniTypeEnum;
import com.fast.framework.thread.ExportThreadExecutor;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.service.base.BaseService;
import com.fast.service.common.ExcelExportAsyncService;
import com.fast.service.fee.FastFeeRuleService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.SessionVO;
import com.fast.vo.fee.FastFeeRuleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 观看记录
 *
 * <AUTHOR>
 */
@Service
public class FastMemberRecentLogExportService extends BaseService {

    @Autowired
    private FastMemberRecentLogService dataService;
    @Autowired
    private ExcelExportAsyncService excelExportAsyncService;
    @Autowired
    private FastFeeRuleService feeRuleService;

    /**
     * 观看记录-导出
     *
     * @param sessionVO
     * @param params
     * @return
     */
    @Async(ExportThreadExecutor.NAME)
    public void exportMemberRecentLogList(SessionVO sessionVO, FastMemberRecentLogPO params, String key) {
        // params.setLimitExport(StaticVar.MILLION);
        List<FastMemberRecentLogPO> list = new ArrayList<>(StaticVar.TEN_THOUSAND);
        List<FastMemberRecentLogPO> temp = new ArrayList<>(StaticVar.TEN_THOUSAND);
        for (int page = 1; page <= 100; page++) {
            temp.clear();
            startPage(page, StaticVar.TEN_THOUSAND);
            temp = dataService.queryList(params);
            if (temp.size() > 0) {
                list.addAll(temp);
            }
            if (temp.size() == 0 || temp.size() != StaticVar.TEN_THOUSAND) {
                break;
            }
        }

        if (CollUtil.isEmpty(list)) {
            return;// ResultVO.error(StaticMsg.MSG_ERROR_30); // 表示数据为空
        }
        List<List<Object>> dataList = new ArrayList<>(); // 用来接收表格里面的数据
        List<String> rowHeadNames = new ArrayList<>(); // 用来接收表格每行头部的数据
        for (FastMemberRecentLogPO cur : list) {
            List<Object> row = new ArrayList<>();// 导出的数据(一行)

            row.add(cur.getMemberId());
            CollUtil.addNoRepeat(rowHeadNames, "用户ID");

            row.add(cur.getMemberName());
            CollUtil.addNoRepeat(rowHeadNames, "昵称");

            row.add(cur.getDramaName());
            CollUtil.addNoRepeat(rowHeadNames, ContentTypeEnum.getName(sessionVO.getContentType()) + "名称");

            row.add("第" + cur.getSeriesNum() + "集");
            CollUtil.addNoRepeat(rowHeadNames, "分集名称");

            row.add(cur.getPayRule() == 0 ? "免费剧集" : "付费剧集");
            CollUtil.addNoRepeat(rowHeadNames, "分集付费规则");


            switch (cur.getPayResult()) {
                case 1:
                    row.add("消耗" + cur.getCoinConsume() + "K币");
                    break;
                case 2:
                    row.add("VIP卡免费解锁");
                    break;
                case 3:
                    row.add("解锁后重看");
                    break;
                case 4:
                    row.add("广告解锁观看");
                    break;
                case 5:
                    row.add("剧卡免费解锁");
                    break;
                default:
                    row.add("-");
                    break;
            }
            CollUtil.addNoRepeat(rowHeadNames, "付费情况");

            row.add(cur.getMiniName());
            CollUtil.addNoRepeat(rowHeadNames, "观看应用");

            row.add(MiniTypeEnum.getName(cur.getMiniType()));
            CollUtil.addNoRepeat(rowHeadNames, "应用类型");

            row.add(DateUtil.format07(cur.getCreateTime()));
            CollUtil.addNoRepeat(rowHeadNames, "观看时间");

            row.add(DateUtil.format07(cur.getRegisterTime()));
            CollUtil.addNoRepeat(rowHeadNames, "注册时间");

            row.add(cur.getLinkId());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接ID");

            row.add(cur.getLinkName());
            CollUtil.addNoRepeat(rowHeadNames, "推广链接名称");

            if (cur.getStartNum() == null) {
                FastFeeRuleVO vo = feeRuleService.queryInfoByRedis(0, cur.getDramaId());
                cur.setStartNum(vo.getStartNum());
            }
            row.add(defaultIfNull(cur.getStartNum(), 0));
            CollUtil.addNoRepeat(rowHeadNames, "付费起始集");

            dataList.add(row);
        }

        if (rowHeadNames.size() <= 1) {
            return;// ResultVO.error(StaticStr.NEED_SELECT_2_EXPORT_FIELD);// 您选择的导出列至少要有2项
        }
        String title = "观看记录";
        excelExportAsyncService.export(sessionVO, dataList, rowHeadNames, title, key);
        // 清空导出的频次限制
        RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
        // return;// ResultVO.success();
    }
}
