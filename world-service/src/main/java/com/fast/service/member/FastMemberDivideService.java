/*
 * Powered By fast.up
 */
package com.fast.service.member;

import com.fast.constant.StaticStr;
import com.fast.mapper.member.FastMemberDivideMapper;
import com.fast.po.member.FastMemberDividePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastMemberDivideService extends BaseService {

    @Autowired
    private FastMemberDivideMapper fastMemberDivideMapper;

    /**
     * 通过id查询单个对象
     */
    public FastMemberDividePO queryById(FastMemberDividePO item) {
        return fastMemberDivideMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastMemberDividePO queryById(Integer id) {
        return fastMemberDivideMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastMemberDividePO queryOne(FastMemberDividePO item) {
        return fastMemberDivideMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastMemberDividePO> queryList(FastMemberDividePO item) {
        return fastMemberDivideMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastMemberDividePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastMemberDividePO> list = fastMemberDivideMapper.queryList(item);
        for (FastMemberDividePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastMemberDividePO item) {
        return fastMemberDivideMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastMemberDividePO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastMemberDivideMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastMemberDividePO> list) {
        if (fastMemberDivideMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastMemberDividePO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastMemberDivideMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
