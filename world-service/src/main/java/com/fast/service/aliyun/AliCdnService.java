package com.fast.service.aliyun;

import com.fast.constant.StaticVar;
import com.fast.framework.config.AliYunCdnConfig;
import com.fast.mapper.drama.FastDramaMapper;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.mini.FastMiniPO;
import com.fast.service.base.BaseService;
import com.fast.service.oss.OssService;
import com.fast.utils.CalTime;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.aliyun.AliBpsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class AliCdnService extends BaseService {

    @Autowired
    private AliYunCdnConfig aliYunCdnConfig;
    @Autowired
    private FastMiniMapper fastMiniMapper;
    @Autowired
    private FastDramaMapper fastDramaMapper;
    @Autowired
    private OssService ossService;

    private static Long updateTime;// 上次更新cdn的时间
    private static BigDecimal domainBps;// 带宽

    private static Long updateTimeMini;// 小程序集合上次更新时间
    private static Set<Integer> miniWechatSet;// 微信小程序集合
    private static Set<Integer> miniTiktokSet;// 抖音小程序集合
    private static Set<Integer> dramaWechatSet;// 已经过审剧，开通微信cdn的集合
    private static Set<Integer> dramaTiktokSet;// 已经过审剧，开通抖音cdn的集合
    private static Set<Integer> dramaFoundationSet;// 打开基建开关的剧集合
    private static Set<Integer> dramaCloneSet;// 克隆剧集合

    /**
     * 图片获取cdn地址
     */
    public String getImgFullUrl(String path) {
        if (StaticVar.DEPLOY_PLATFORM == 1) {
            return getAliCdnFullUrl(path);
        } else if (path.startsWith("http")) {
            return path;
        } else {
            return ossService.getOssHost(false) + path;
        }
    }

    /**
     * 图片获取cdn地址
     */
    public String getImgFullUrl(String path, String process) {
        String url = getImgFullUrl(path);
        // 判断url是否带参数
        if (url.contains("?")) {
            return url + "&x-oss-process=image/" + process;
        } else {
            return url + "?x-oss-process=image/" + process;
        }
    }

    /**
     * 视频获取cdn地址（微信小程序，腾讯cdn）
     */
    public String getVideoFullUrl(String path, Integer miniId, Integer dramaId) {
        // log.info("获取腾讯剧集cdn地址miniId:{},dramaId:{}", miniId, dramaId);
        CalTime calTime = new CalTime();
        Double random = Math.random();
        if (StaticVar.DEPLOY_PLATFORM == 2 || miniId == null || miniId == 0 || dramaId == null || dramaId == 0) {
            return getAliCdnFullUrl(path);
        }
        initCdnSet();
        // 判断基建开关
        if (dramaFoundationSet.contains(dramaId)) {
            Integer seriesNum = StrUtil.getSeriesNumFromPath(path);// 剧集号
            if (seriesNum > 1) {
                return "nourl-jijian";
            }
        }
        // 判断是否微信小程序
        if (miniWechatSet.contains(miniId) && dramaWechatSet.contains(dramaId)) {
            log.info("微信小程序获取腾讯剧集cdn地址miniId:{},dramaId:{}", miniId, dramaId);
            actionLogService.log("get_drama_cost", dramaId + "," + random + "ste1" + calTime.getCostTime());
            // 微信cdn
            // 根据path获取mediaId
            String keyPath = StaticVar.CDN_PATH_MEDIA_3 + dramaId + "_" + path;
            String resMediaObjStr = RedisUtil.get(keyPath);
            if (StrUtil.isEmpty(resMediaObjStr)) {
                resMediaObjStr = "no";
                RedisUtil.set(keyPath, resMediaObjStr, 60 * 30);
            }
            actionLogService.log("cdn_tencent_url_0401", "nourl----miniId=" + miniId + ",dramaId=" + dramaId + ",path=" + path);
            return "nourl";
        } else {
            // log.info("默认阿里云腾讯剧集cdn地址miniId:{},dramaId:{}", miniId, dramaId);
            // 默认走阿里云
            // String aliCdnUrl = getAliCdnFullUrl(path);
            // TODO 为了测试
            String aliCdnUrl = getAliCdnFullUrl(path, dramaId);
            actionLogService.log("cdn_url_mini_0718", "0718:" + aliCdnUrl);
            return aliCdnUrl;
        }
    }


    // cdn集合初始化
    private synchronized void initCdnSet() {
        if (updateTimeMini == null) {
            updateTimeMini = System.currentTimeMillis();
        }
        Long curTime = System.currentTimeMillis();
        if (miniWechatSet == null || miniWechatSet.isEmpty() || (curTime - updateTimeMini) > 1000 * 60) { // 本地缓存1分钟
            updateTimeMini = curTime;
            // log.info("初始化cdn判断集合-----开始");
            if (miniWechatSet == null) {
                miniWechatSet = new HashSet<>();
                miniTiktokSet = new HashSet<>();
                dramaWechatSet = new HashSet<>();
                dramaTiktokSet = new HashSet<>();
                dramaFoundationSet = new HashSet<>();
                dramaCloneSet = new HashSet<>();
            }
            // 缓存查询
            String keyMini = StaticVar.CDN_MINI_LIST;
            String miniListStr = RedisUtil.get(keyMini);
            List<FastMiniPO> miniList = null;
            if (StrUtil.isEmpty(miniListStr)) {
                // 从数据库开始同步小程序列表
                FastMiniPO miniParam = new FastMiniPO();
                miniParam.setState(1);
                miniList = fastMiniMapper.queryList(miniParam);
                miniListStr = JsonUtil.toString(miniList);
                RedisUtil.set(keyMini, miniListStr, 60 * 30); //
            }
            if (StrUtil.isNotEmpty(miniListStr)) {
                miniList = JsonUtil.toList(miniListStr, FastMiniPO.class);
                if (miniList != null && miniList.size() > 0) {
                    if (miniWechatSet.size() > 0 || miniTiktokSet.size() > 0) {
                        miniWechatSet.clear();
                        miniTiktokSet.clear();
                    }
                    for (FastMiniPO item : miniList) {
                        if (item.getType() == 1) {
                            miniWechatSet.add(item.getId());
                        } else if (item.getType() == 2) {
                            miniTiktokSet.add(item.getId());
                        }
                    }
                }
            }
            String keyDrama = StaticVar.CDN_DRAMA_LIST;
            String dramaListStr = RedisUtil.get(keyDrama);
            List<FastDramaPO> dramaList = null;
            if (StrUtil.isEmpty(dramaListStr)) {
                // 从数据库开始同步小程序列表
                FastDramaPO dramaParam = new FastDramaPO();
                dramaParam.setDelFlag(0);
                dramaList = fastDramaMapper.querySimpleList(dramaParam);
                dramaListStr = JsonUtil.toString(dramaList);
                RedisUtil.set(keyDrama, dramaListStr, 60 * 30); // 剧列表缓存30分钟
            }
            if (StrUtil.isNotEmpty(dramaListStr)) {
                dramaList = JsonUtil.toList(dramaListStr, FastDramaPO.class);
                if (dramaList != null && dramaList.size() > 0) {
                    if (dramaWechatSet.size() > 0 || dramaTiktokSet.size() > 0) {
                        dramaWechatSet.clear();
                        dramaTiktokSet.clear();
                        dramaFoundationSet.clear();
                        dramaCloneSet.clear();
                    }
                    for (FastDramaPO item : dramaList) {
                        if (item.getCdnWechat() != null && item.getCdnWechat() == 1) {
                            dramaWechatSet.add(item.getId());
                        }
                        if (item.getCdnTiktok() != null && item.getCdnTiktok() == 1) {
                            dramaTiktokSet.add(item.getId());
                        }
                        // 基建剧集合
                        if (item.getFoundationState() != null && item.getFoundationState() == 1) {
                            dramaFoundationSet.add(item.getId());
                        }
                        // 克隆剧集合
                        if (biggerZero(item.getCloneFromId())) {
                            dramaCloneSet.add(item.getId());
                        }
                    }
                }
            }
            // log.info("初始化cdn判断集合-----结束");
        }
    }

    public String getAliCdnFullUrl(String path) {
        return getAliCdnFullUrl(path, null);
    }

    // 获取阿里云cdn完整地址
    public String getAliCdnFullUrl(String path, Integer dramaId) {
        if (StaticVar.DEPLOY_PLATFORM == 2) {
            if (path.startsWith("/")) {
                return ossService.getOssHost(false) + path.replaceFirst("/", "");
            }
            if (path.contains("http")) {
                return path;
            } else {
                return ossService.getOssHost(false) + path;
            }
        }
        if (path.contains("http")) {
            path = replaceHost(path);
        }
        if (path.contains("?") && path.contains("auth_key")) {
            path = path.substring(0, path.indexOf("?"));
        }
        StringBuffer fullUrl = new StringBuffer();
        String[] urlArray = aliYunCdnConfig.url.split(",");
        // 判断流量是否大，是否需要分流cdn
        Integer idx = getCdnIdx(urlArray);
        String bearHost = urlArray[idx];
        // TODO 为了测试
        if (StaticVar.isTestDrama(dramaId)) {
            bearHost = bearHost.replace("https://world-test.tos-ap-southeast-1.volces.com/", "https://fastup-cdn-prod.601book.com/");
        }
        fullUrl.append(bearHost);
        fullUrl.append(path);

        // 火山云暂时没开防盗链
        if (StaticVar.isTestDrama(dramaId)) {
            // 判断是否启用授权验证，不判断了，直接开启
            if (path.contains("?")) {
                fullUrl.append("&");
            } else {
                fullUrl.append("?");
            }
            fullUrl.append("auth_key=");
            // TODO 为了测试
            // fullUrl.append(getAuthKey("/" + path));
            fullUrl.append(getAuthKey("/" + path, dramaId));
        }

        // 添加授权
        return fullUrl.toString();
    }

    // 获取cdn地址
    public Integer getCdnIdx(String[] urlArray) {
        Integer idx = 0;
        try {
            // 无需去考虑切流量了，已经加入了白名单
            if (urlArray.length > 100) {
                // 实时带宽
                BigDecimal domainBpsNow = getDomainBps();
                // 默认限流上限设置为9G，可以通过缓存修改上限制
                Long limitBps = 9 * 1024L;
                String limitBpsStr = RedisUtil.get(StaticVar.ALI_BPS_LIMIT);
                if (StrUtil.isNotEmpty(limitBpsStr)) {
                    limitBps = Long.valueOf(limitBpsStr);
                }
                if (domainBpsNow.compareTo(BigDecimal.valueOf(limitBps)) > 0) {
                    idx = 1;
                }
            }
        } catch (Exception e) {
            log.error("获取cdnIdx异常----");
            log.error(e.getMessage());
        }
        return idx;
    }

    // 获取授权参数
    public String getAuthKey(String path, Integer dramaId) {
        Long timeNow = DateUtil.getTimeNowUnix();
        StringBuffer sb = new StringBuffer();
        sb.append(path);
        sb.append("-");
        sb.append(timeNow);
        sb.append("-0-0-");
        // TODO 为了测试
        // sb.append(aliYunCdnConfig.auth);
        if (StaticVar.isTestDrama(dramaId)) {
            sb.append("ytjzfzemscx8bq7j5bg5gydtqv6hb8kk");
        } else {
            sb.append(aliYunCdnConfig.auth);
        }
        String md5 = Md5Util.getMD5(sb.toString());
        String authStr = timeNow +
                "-0-0-" +
                md5;
        return authStr;
    }

    // 通过缓存查询实时带宽（没有缓存就默认为0，缓存有定时任务刷新）
    public BigDecimal getDomainBps() {
        if (updateTime == null) {
            updateTime = 0L;
        }
        Long timeNow = System.currentTimeMillis();
        if (timeNow - updateTime > 1000 * 60) {
            String bpsStr = RedisUtil.get(StaticVar.ALI_BPS);
            if (StrUtil.isNotEmpty(bpsStr)) {
                AliBpsVO bpsVO = JsonUtil.toJavaObject(bpsStr, AliBpsVO.class);
                if (timeNow - bpsVO.getStatTime().getTime() < 1000 * 60 * 10) {
                    // 统计时间在10分钟内有效
                    domainBps = bpsVO.getBps();
                    updateTime = timeNow;
                } else {
                    domainBps = BigDecimal.ZERO;
                    updateTime = timeNow;
                }
            } else {
                domainBps = BigDecimal.ZERO;
                updateTime = timeNow;
            }
        }
        if (domainBps == null) {
            domainBps = BigDecimal.ZERO;
        }
        return domainBps;
    }

    // 去除匹配cdn域名
    public String replaceHost(String urlStr) {
        if (StrUtil.isEmpty(urlStr)) {
            return "";
        }
        String[] urlArray = aliYunCdnConfig.url.split(",");
        for (String urlOne : urlArray) {
            urlStr = urlStr.replace(urlOne, "");
        }
        return urlStr;
    }


}
