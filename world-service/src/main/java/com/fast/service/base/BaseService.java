package com.fast.service.base;

import com.fast.base.BaseClass;
import com.fast.service.common.FastActionLogService;
import com.fast.service.common.FastOperationLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

public class BaseService extends BaseClass {

    protected Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    protected FastOperationLogService logOperatorService;
    @Autowired
    protected FastActionLogService actionLogService;

    /**
     * 事务回滚
     */
    protected boolean transactionRollBack() {
        try {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("Transaction Rollback Successful");
            return true;
        } catch (Exception e) {
            log.info(e.getMessage());
            return false;
        }
    }

    /**
     * 事务回滚
     */
    protected boolean transactionRollBack(String msg) {
        try {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("Transaction Rollback Successful:{}", msg);
            return true;
        } catch (Exception e) {
            log.info(e.getMessage());
            return false;
        }
    }

    /**
     * 休息会
     */
    protected void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

}
