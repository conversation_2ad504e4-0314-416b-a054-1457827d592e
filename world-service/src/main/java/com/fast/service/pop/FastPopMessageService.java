/*
 * Powered By fast.up
 */
package com.fast.service.pop;

import com.fast.constant.StaticStr;
import com.fast.mapper.pop.FastPopMessageMapper;
import com.fast.mapper.setting.FastSettingSystemMapper;
import com.fast.mapper.user.FastUserMapper;
import com.fast.po.pop.FastPopMessagePO;
import com.fast.po.setting.FastSettingSystemPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastPopMessageService extends BaseService {

    @Autowired
    private FastPopMessageMapper fastPopMessageMapper;
    @Autowired
    private FastUserMapper fastUserMapper;
    @Autowired
    private FastSettingSystemMapper fastSettingSystemMapper;

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> getPopMessage(SessionVO sessionVO) {
        Map<String, Object> results = ResultVO.getMap();
        // 查询用户角色
        FastUserPO userPO = fastUserMapper.queryById(sessionVO.getUserId());
        if (userPO == null) {
            results.put("popFlag", 0);// 不弹
            return ResultVO.success(results);
        }
        // 查询系统查看角色的配置
        FastSettingSystemPO ssParam = new FastSettingSystemPO();
        ssParam.setCode("pop_message_role_ids");
        FastSettingSystemPO ssPO = fastSettingSystemMapper.queryOne(ssParam);
        if (ssPO == null) {
            results.put("popFlag", 0);// 不弹
            return ResultVO.success(results);
        }
        String[] roleArray = ssPO.getContent().split(",");
        String[] userRoleArray = userPO.getRoleIds().split(",");
        Integer popFlag = 0;
        for (int i = 0; i < roleArray.length; i++) {
            for (int j = 0; j < userRoleArray.length; j++) {
                if (roleArray[i].equals(userRoleArray[j])) {
                    popFlag = 1;
                    break;
                }
            }
        }
        if (popFlag == 0) {
            results.put("popFlag", 0);// 不弹
            return ResultVO.success(results);
        }
        // 查询待处理的数量
        FastPopMessagePO pmParam = new FastPopMessagePO();
        pmParam.setHandleFlag(0);
        Integer mCount = fastPopMessageMapper.queryCount(pmParam);
        if (mCount > 0) {
            // 弹
            results.put("popFlag", 1);// 弹
            results.put("popMessageCount", mCount);
            return ResultVO.success(results);
        }
        // 没有需要处理的就不弹了
        results.put("popFlag", 0);// 不弹
        return ResultVO.success(results);
    }

    /**
     * 通过id查询单个对象
     */
    public FastPopMessagePO queryById(FastPopMessagePO params) {
        return fastPopMessageMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastPopMessagePO queryById(Integer id) {
        return fastPopMessageMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastPopMessagePO queryOne(FastPopMessagePO params) {
        return fastPopMessageMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastPopMessagePO> queryList(FastPopMessagePO params) {
        return fastPopMessageMapper.queryList(params);
    }


    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastPopMessagePO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastPopMessagePO> list = fastPopMessageMapper.queryList(params);
        for (FastPopMessagePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastPopMessagePO params) {
        return fastPopMessageMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastPopMessagePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastPopMessageMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastPopMessagePO> list) {
        if (fastPopMessageMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastPopMessagePO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastPopMessageMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
