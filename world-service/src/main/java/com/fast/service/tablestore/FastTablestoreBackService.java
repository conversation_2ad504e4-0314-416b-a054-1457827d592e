/*
 * Powered By fast.up
 */
package com.fast.service.tablestore;

import com.fast.constant.StaticStr;
import com.fast.mapper.tablestore.FastTablestoreBackMapper;
import com.fast.po.tablestore.FastTablestoreBackPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastTablestoreBackService extends BaseService {

    @Autowired
    private FastTablestoreBackMapper fastTablestoreBackMapper;

    public FastTablestoreBackPO queryByClickId(String clickId) {
        FastTablestoreBackPO params = new FastTablestoreBackPO();
        params.setClickId(clickId);
        return fastTablestoreBackMapper.queryOne(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastTablestoreBackPO queryById(FastTablestoreBackPO params) {
        return fastTablestoreBackMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastTablestoreBackPO queryById(Integer id) {
        return fastTablestoreBackMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastTablestoreBackPO queryOne(FastTablestoreBackPO params) {
        return fastTablestoreBackMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastTablestoreBackPO> queryList(FastTablestoreBackPO params) {
        return fastTablestoreBackMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastTablestoreBackPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastTablestoreBackPO> list = fastTablestoreBackMapper.queryList(params);
        for (FastTablestoreBackPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastTablestoreBackPO params) {
        return fastTablestoreBackMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastTablestoreBackPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastTablestoreBackMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastTablestoreBackPO> list) {
        if (fastTablestoreBackMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastTablestoreBackPO params) {
        if (fastTablestoreBackMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
