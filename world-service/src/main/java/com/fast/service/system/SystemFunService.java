/*
 * Powered By fast.up
 */
package com.fast.service.system;

import com.fast.constant.StaticStr;
import com.fast.mapper.system.SystemFunMapper;
import com.fast.po.system.SystemFunPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 系统权限
 *
 * <AUTHOR>
 */
@Service
public class SystemFunService extends BaseService {

    @Autowired
    private SystemFunMapper systemFunMapper;

    /**
     * 通过id查询单个对象
     */
    public SystemFunPO queryById(SystemFunPO params) {
        return systemFunMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public SystemFunPO queryById(Integer id) {
        return systemFunMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public SystemFunPO queryOne(SystemFunPO params) {
        return systemFunMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<SystemFunPO> queryList(SystemFunPO params) {
        return systemFunMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(SystemFunPO params, PageVO pageVO) {
        startPage(pageVO);
        List<SystemFunPO> list = systemFunMapper.queryList(params);
        // for (SystemFunPO cur : list) {
        // cur.setEncryptionId(encode(cur.getId()));
        // }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(SystemFunPO params) {
        return systemFunMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(SystemFunPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (systemFunMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }
}
