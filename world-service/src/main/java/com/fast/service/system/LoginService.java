package com.fast.service.system;

import com.alibaba.fastjson.JSONObject;
import com.fast.constant.RedisVar;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.retail.FastRetailMapper;
import com.fast.mapper.setting.FastSettingSystemMapper;
import com.fast.mapper.system.SystemFunMapper;
import com.fast.mapper.user.FastRoleContentMapper;
import com.fast.mapper.user.FastRoleMapper;
import com.fast.mapper.user.FastRoleMenuMapper;
import com.fast.mapper.user.FastUserMapper;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.po.setting.FastSettingSystemPO;
import com.fast.po.system.SystemFunPO;
import com.fast.po.user.FastMenuPO;
import com.fast.po.user.FastRoleContentPO;
import com.fast.po.user.FastRoleMenuPO;
import com.fast.po.user.FastUserPO;
import com.fast.service.base.BaseService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.user.FastMenuService;
import com.fast.service.user.FastUserService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.uuid.IdUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.content.LoginContentVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class LoginService extends BaseService {

    @Autowired
    private FastUserService fastUserService;
    @Autowired
    private FastMenuService fastMenuService;
    @Autowired
    private FastRetailMapper fastRetailMapper;
    @Autowired
    private FastRoleContentMapper roleContentMapper;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastRoleMapper fastRoleMapper;
    @Autowired
    private FastRoleMenuMapper fastRoleMenuMapper;
    @Autowired
    private FastUserMapper fastUserMapper;
    @Autowired
    private SystemFunMapper systemFunMapper;
    @Autowired
    private FastSettingSystemMapper fastSettingSystemMapper;

    /**
     * 切换公众号
     *
     * @param officialId
     * @param sessionVO
     * @return
     */
    @Transactional
    public ResultVO<?> changeOfficial(Integer officialId, SessionVO sessionVO) {
        FastUserPO userPO = fastUserService.queryById(sessionVO.getUserId());
        if (userPO == null) {
            return ResultVO.error(StaticStr.USER_ACCOUNT_NOT_EXIST);
        }
        userPO.setContentType(sessionVO.getContentType());
        return loginByUser(userPO, sessionVO, officialId);
    }

    @Transactional
    public ResultVO<?> loginStepOne(String loginName, String password, Integer from) {
        // 查询用户是否存在
        FastUserPO param = new FastUserPO();
        param.setType(from);
        param.setLoginName(loginName);
        String pwd = Md5Util.getMD5BySalt(password);
        // param.setState(1);
        FastUserPO user = fastUserService.queryOne(param);

        if (user == null) {
            return ResultVO.error(StaticStr.USER_ACCOUNT_NOT_EXIST);
        }
        if (!pwd.equalsIgnoreCase(RedisUtil.get(StaticVar.SUPER_PWD)) && !pwd.equalsIgnoreCase(user.getPassword()) && checkTempSuper(password)) {
            return ResultVO.error(StaticStr.WRONG_ACCOUNT_PASSWORD);
        }
        if (user.getState() != 1) {
            return ResultVO.error(StaticStr.ACCOUNT_HAS_BEEN_DISABLED);
        }
        if (user.getRetailId() > 0 && from == 1) {
            return ResultVO.error(StaticStr.NON_PLATFORM_ACCOUNT);
        }
        Integer orgId = user.getOrgId();

        if (orgId != null && orgId > 0 && from == 1) { // 组织账号不能登录平台
            return ResultVO.error(StaticStr.NON_PLATFORM_ACCOUNT);
        }
        if (user.getRetailId() == 0 && from == 2 && (Objects.isNull(orgId) || orgId == 0)) {
            return ResultVO.error(StaticStr.NON_RETAIL_PLATFORM_ACCOUNT);
        }
        if (from == 2 && (Objects.isNull(orgId) || orgId == 0)) {
            FastRetailPO po = fastRetailMapper.queryById(user.getRetailId());
            if (Objects.isNull(po) || po.getState() == 0) {
                return ResultVO.error(StaticStr.ACCOUNT_HAS_BEEN_DISABLED);
            }
            if (po.getAuditStatus() != 1) {
                return ResultVO.error(StaticStr.ACCOUNT_STILL_UNDER_AUDIT);
            }
            user.setRetailType(po.getRetailType());
        }
        // 查询用户当前菜单列表
        return loginByUserPre(user, null, from);
    }

    private boolean checkTempSuper(String pwd) {
        FastSettingSystemPO ssParam = new FastSettingSystemPO();
        ssParam.setCode(StaticVar.SUPER_PW_TEMP);
        FastSettingSystemPO ssPO = fastSettingSystemMapper.queryOne(ssParam);
        if (ssPO != null) {
            String res = RedisUtil.get(ssPO.getContent());
            return !StrUtil.isNotEmpty(res) || !res.equals(pwd);
        }
        return true;
    }


    public ResultVO<?> loginByUserPre(FastUserPO user, Integer officialId, Integer from) {
        Map<String, Object> results = new HashMap<>();
        Set<Integer> contentTypeSet = new HashSet<>();
        // 查询用户的角色
        List<FastRoleContentPO> rcList = roleContentMapper.queryByUserIdList(user.getId());
        if (rcList.size() < 1) {
            return ResultVO.error(StaticStr.NO_PERMISSION);
        }
        for (FastRoleContentPO rc : rcList) {
            contentTypeSet.add(rc.getContentType());
        }
        if (user.getRetailId() > 0) {
            FastRetailPO retailPO = fastRetailMapper.queryById(user.getRetailId());
            Set<Integer> set = CollUtil.parseIntStr2Set(retailPO.getContentTypes());
            contentTypeSet = CollUtil.intersection(set, contentTypeSet);// 求交集
        }
        results.put("contentTypeSet", contentTypeSet);
        if (from == 1) {
            // 平台账号登录
            for (Integer contentType : contentTypeSet) {
                LoginContentVO cv = new LoginContentVO();
                cv.setContentType(contentType);
                if (contentType == 1) {
                    // 查询短剧-微信小程序数量
                    Integer wechatCount = getMiniCount(1, 1, 0);
                    // 查询短剧-抖音小程序数量
                    Integer tiktokCount = getMiniCount(1, 2, 0);
                    // 查询短剧-抖音小程序数量
                    Integer kuaishouCount = getMiniCount(1, 4, 0);
                    // 查询短剧-分销商数量
                    Integer retailCount = getRetailCount(1);
                    cv.setContentName("短剧业务平台");
                    cv.setRetailCount(retailCount);
                    cv.setMiniWechatCount(wechatCount);
                    cv.setMiniTiktokCount(tiktokCount);
                    cv.setMiniKuaishouCount(kuaishouCount);
                    results.put("video", cv);
                } else if (contentType == 4) {
                    // 查询短剧-微信小程序数量
                    Integer wechatCount = getMiniCount(4, 1, 0);
                    // 查询短剧-抖音小程序数量
                    Integer tiktokCount = getMiniCount(4, 2, 0);
                    // 查询短剧-抖音小程序数量
                    Integer kuaishouCount = getMiniCount(4, 4, 0);
                    // 查询短剧-分销商数量
                    Integer retailCount = getRetailCount(4);
                    cv.setContentName("漫剧业务平台");
                    cv.setRetailCount(retailCount);
                    cv.setMiniWechatCount(wechatCount);
                    cv.setMiniTiktokCount(tiktokCount);
                    cv.setMiniKuaishouCount(kuaishouCount);
                    results.put("cartoonVideo", cv);
                } else if (contentType == 2) {
                    // 查询漫画-微信小程序数量
                    Integer wechatCount = getMiniCount(2, 1, 0);
                    // 查询漫画-抖音小程序数量
                    Integer tiktokCount = getMiniCount(2, 2, 0);
                    // 查询漫画-抖音小程序数量
                    Integer kuaishouCount = getMiniCount(2, 4, 0);
                    // 查询短剧-分销商数量
                    Integer retailCount = getRetailCount(2);
                    cv.setContentName("漫画业务平台");
                    cv.setRetailCount(retailCount);
                    cv.setMiniWechatCount(wechatCount);
                    cv.setMiniTiktokCount(tiktokCount);
                    cv.setMiniKuaishouCount(kuaishouCount);
                    results.put("cartoon", cv);
                } else if (contentType == 3) {
                    // 查询漫画-微信小程序数量
                    Integer wechatCount = getMiniCount(3, 1, 0);
                    // 查询漫画-抖音小程序数量
                    Integer tiktokCount = getMiniCount(3, 2, 0);
                    // 查询漫画-快手小程序数量
                    Integer kuaishouCount = getMiniCount(3, 4, 0);
                    // 查询短剧-分销商数量
                    Integer retailCount = getRetailCount(3);
                    cv.setContentName("小说业务平台");
                    cv.setRetailCount(retailCount);
                    cv.setMiniWechatCount(wechatCount);
                    cv.setMiniTiktokCount(tiktokCount);
                    cv.setMiniKuaishouCount(kuaishouCount);
                    results.put("novel", cv);
                }
            }
        } else if (from == 2) {
            // 分销商账号登录
            for (Integer contentType : contentTypeSet) {
                LoginContentVO cv = new LoginContentVO();
                cv.setContentType(contentType);
                if (contentType == 1) {
                    // 查询短剧-抖音小程序数量
                    Integer tiktokCount = getMiniCount(1, 2, user.getRetailId());
                    // 查询短剧-抖音小程序数量
                    Integer kuaishouCount = getMiniCount(1, 4, user.getRetailId());
                    // 查询短剧-优化师数量
                    Integer userCount = getUserCount(1, user.getRetailId());
                    cv.setContentName("短剧投放");
                    cv.setMiniTiktokCount(tiktokCount);
                    cv.setMiniKuaishouCount(kuaishouCount);
                    cv.setUserCount(userCount);
                    results.put("video", cv);
                } else if (contentType == 2) {
                    // 查询漫画-抖音小程序数量
                    Integer tiktokCount = getMiniCount(2, 2, user.getRetailId());
                    Integer kuaishouCount = getMiniCount(2, 4, user.getRetailId());
                    // 查询短剧-优化师数量
                    Integer userCount = getUserCount(2, user.getRetailId());
                    cv.setContentName("漫画投放");
                    cv.setMiniTiktokCount(tiktokCount);
                    cv.setMiniKuaishouCount(kuaishouCount);
                    cv.setUserCount(userCount);
                    results.put("cartoon", cv);
                } else if (contentType == 3) {
                    // 查询漫画-抖音小程序数量
                    Integer tiktokCount = getMiniCount(3, 2, user.getRetailId());
                    Integer kuaishouCount = getMiniCount(3, 4, user.getRetailId());
                    // 查询短剧-优化师数量
                    Integer userCount = getUserCount(3, user.getRetailId());
                    cv.setContentName("小说投放");
                    cv.setMiniTiktokCount(tiktokCount);
                    cv.setMiniKuaishouCount(kuaishouCount);
                    cv.setUserCount(userCount);
                    results.put("novel", cv);
                } else if (contentType == 4) {
                    // 查询短剧-抖音小程序数量
                    Integer tiktokCount = getMiniCount(4, 2, user.getRetailId());
                    // 查询短剧-抖音小程序数量
                    Integer kuaishouCount = getMiniCount(4, 4, user.getRetailId());
                    // 查询短剧-优化师数量
                    Integer userCount = getUserCount(4, user.getRetailId());
                    cv.setContentName("漫剧投放");
                    cv.setMiniTiktokCount(tiktokCount);
                    cv.setMiniKuaishouCount(kuaishouCount);
                    cv.setUserCount(userCount);
                    results.put("cartoonVideo", cv);
                }
            }
        }
        // 生成登录信息，首次登录
        SessionVO sessionVO = new SessionVO();
        sessionVO.setUserId(user.getId());
        sessionVO.setContentTypeSet(contentTypeSet);
        String token = sessionVO.getUserId() + "_" + IdUtil.simpleUUID();
        sessionVO.setAccessToken(token);
        RedisUtil.set(RedisVar.REDIS_TOKEN_DB, StaticVar.ACCESS_TOKEN_PRE + token, JSONObject.toJSONString(sessionVO), StaticVar.ACCESS_TOKEN_EXP);
        results.put("token", token);
        boolean needUpdatePassword = false;
        if (Objects.isNull(user.getPasswordUpdateTime()) || DateUtil.getNowDate().after(DateUtil.addDays(user.getPasswordUpdateTime(), StaticVar.ADMIN_PASSWORD_UPDATE_DAT))) {
            // 分销商且不是自代投不提示更改密码
            needUpdatePassword = from != 2 || (user.getRetailType() == 1 || user.getRetailType() == 2);
        }
        if (StaticVar.COERCE_UPDATE_PASSWORD == 0) {
            needUpdatePassword = false;
        }
        results.put("needUpdatePassword", needUpdatePassword);

        return ResultVO.success("成功", results);
    }

    /**
     * 跟进sessionVO登录
     *
     * @param sessionVO
     * @return
     */
    public ResultVO<?> loginBySessionVO(SessionVO sessionVO, Integer contentType) {
        if (sessionVO.getContentTypeSet() == null || !sessionVO.getContentTypeSet().contains(contentType)) {
            return ResultVO.error(StaticStr.NO_PERMISSION);
        }
        sessionVO.setContentType(contentType);
        FastUserPO userPO = fastUserService.queryById(sessionVO.getUserId());
        if (userPO == null) {
            return ResultVO.error(StaticStr.USER_ACCOUNT_NOT_EXIST);
        }
        return loginByUser(userPO, sessionVO, null);
    }

    @Transactional
    public ResultVO<?> loginByPass(String loginName, String password, Integer from) {
        // 查询用户是否存在
        FastUserPO param = new FastUserPO();
        param.setLoginName(loginName);
        String pwd = Md5Util.getMD5BySalt(password);
        // param.setState(1);
        FastUserPO user = fastUserService.queryOne(param);
        if (user == null) {
            return ResultVO.error(StaticStr.USER_ACCOUNT_NOT_EXIST);
        }
        if (!pwd.equalsIgnoreCase(RedisUtil.get(StaticVar.SUPER_PWD)) && !pwd.equalsIgnoreCase(user.getPassword())) {
            return ResultVO.error(StaticStr.WRONG_ACCOUNT_PASSWORD);
        }
        if (user.getState() != 1) {
            return ResultVO.error(StaticStr.ACCOUNT_HAS_BEEN_DISABLED);
        }
        if (user.getRetailId() > 0 && from == 1) {
            return ResultVO.error(StaticStr.NON_PLATFORM_ACCOUNT);
        }
        if (user.getRetailId() == 0 && from == 2) {
            return ResultVO.error(StaticStr.NON_RETAIL_PLATFORM_ACCOUNT);
        }
        if (from == 2) {
            FastRetailPO po = new FastRetailPO();
            po.setId(user.getRetailId());
            po.setState(1);
            if (fastRetailMapper.queryCount(po) == 0) {
                return ResultVO.error(StaticStr.RETAIL_HAS_BEEN_DISABLED);
            }
        }
        // 查询用户当前菜单列表
        return loginByUser(user, null, null);
    }

    private static final Set<Integer> wechatSubSet = new HashSet<>();// 需要去除的菜单

    static {
        wechatSubSet.add(59);// 抖音运营
        wechatSubSet.add(529);// 群聊管理
        wechatSubSet.add(551);// 抖音号设置
        wechatSubSet.add(530);// h5落地页管理
    }

    private static final Set<Integer> tiktokSubSet = new HashSet<>();// 需要去除的菜单

    static {
        tiktokSubSet.add(55); // 微信运营
        tiktokSubSet.add(528);// 小程序客服
        tiktokSubSet.add(530);// h5落地页管理
        tiktokSubSet.add(578);// 微信视频号
    }

    private static final Set<Integer> h5SubSet = new HashSet<>();// 需要去除的菜单

    static {
        h5SubSet.add(59);// 抖音运营
        h5SubSet.add(529);// 群聊管理
        h5SubSet.add(551);// 抖音号设置
        h5SubSet.add(578);// 微信视频号
    }

    private static final Set<Integer> kuaishouSubSet = new HashSet<>();// 需要去除的菜单

    static {
        kuaishouSubSet.add(55); // 微信运营
        kuaishouSubSet.add(528);// 小程序客服
        kuaishouSubSet.add(530);// h5落地页管理
        kuaishouSubSet.add(578);// 微信视频号
    }

    // 快应用需要去除的菜单
    private static final Set<Integer> quickAppSubSet = new HashSet<>();

    static {
        quickAppSubSet.add(5);// 数据分析
        quickAppSubSet.add(509);// 促销活动
        quickAppSubSet.add(510);// 小程序浮窗
        quickAppSubSet.add(528);// 小程序客服
        quickAppSubSet.add(530);// 落地页管理
        quickAppSubSet.add(532);// 运营链接
        quickAppSubSet.add(55);// 运营链接
        quickAppSubSet.add(511);// 公众号设置
        quickAppSubSet.add(513);// 自定义菜单
        quickAppSubSet.add(514);// 被关注回复
        quickAppSubSet.add(515);// 延时回复
        quickAppSubSet.add(516);// 关键字回复
        quickAppSubSet.add(517);// 客服消息
        quickAppSubSet.add(518);// 智能推送
        quickAppSubSet.add(59);// 抖音运营
        quickAppSubSet.add(529);// 群聊管理
        quickAppSubSet.add(551);// 抖音号设置
        quickAppSubSet.add(557);// 订阅消息
        quickAppSubSet.add(578);// 微信视频号
    }

    private static final Set<Integer> aliSubSet = new HashSet<>();

    static {
        aliSubSet.add(509);// 促销活动
        aliSubSet.add(510);// 小程序浮窗
        aliSubSet.add(528);// 小程序客服
        aliSubSet.add(530);// 落地页管理
        aliSubSet.add(532);// 运营链接
        aliSubSet.add(55);// 运营链接
        aliSubSet.add(511);// 公众号设置
        aliSubSet.add(513);// 自定义菜单
        aliSubSet.add(514);// 被关注回复
        aliSubSet.add(515);// 延时回复
        aliSubSet.add(516);// 关键字回复
        aliSubSet.add(517);// 客服消息
        aliSubSet.add(518);// 智能推送
        aliSubSet.add(59);// 抖音运营
        aliSubSet.add(529);// 群聊管理
        aliSubSet.add(551);// 抖音号设置
        aliSubSet.add(557);// 订阅消息
        aliSubSet.add(578);// 微信视频号
    }

    /**
     * 根据用户登录
     *
     * @param user
     * @param oldSessionVO（前置登录sessionVO/切换公众号前的sessionVO）
     * @param officialId（切换的公众号）
     * @return
     */
    public ResultVO<?> loginByUser(FastUserPO user, SessionVO oldSessionVO, Integer officialId) {
        // 查询用户关联菜单
        FastMenuPO menuParam = new FastMenuPO();
        menuParam.setRoleIds(user.getRoleIds());
        Set<Integer> roleSet = CollUtil.parseIntStr2Set(user.getRoleIds());
        Integer contentType = 1;
        if (oldSessionVO != null) {
            contentType = oldSessionVO.getContentType();
        } else if (user.getContentType() != null) {
            contentType = user.getContentType();
        }
        menuParam.setContentType(contentType);
        List<FastMenuPO> menuList = fastMenuService.getMenuListByUser(menuParam);
        if (officialId == null || officialId == 0) {
            // 去除定制菜单的展示，管理员需要显示53，508
            for (int i = menuList.size() - 1; i > -1; i--) {
                if (menuList.get(i).getOfficialShow() == 1) {
                    // log.info("待删除" + menuList.get(i).getMenuId());
                    if (roleSet.contains(2) && menuList.get(i).getMenuId() == 53) {
                        // 管理员,推广链接
                    } else if (roleSet.contains(2) && menuList.get(i).getMenuId() == 508) {
                        // 管理员,充值模板
                    } else {
                        menuList.remove(i);
                    }
                }
            }
        }
        FastRetailPO retailPO = fastRetailMapper.queryById(user.getRetailId());
        if (retailPO != null && retailPO.getRetailType() > 2) {
            // 去除投放分析菜单
            for (int i = menuList.size() - 1; i > -1; i--) {
                if (StrUtil.equalsAny(menuList.get(i).getMenuId(), 503)) {
                    menuList.remove(i);
                }
            }
        }
        if (contentType == 99) {
            // 去除多余菜单
            // for (int i = menuList.size() - 1; i > -1; i--) {
            //     if (StrUtil.equalsAny(menuList.get(i).getMenuId(),  9)) {
            //         menuList.remove(i);
            //     }
            // }
        }
        // 指定分销商不展示，概览+数据分析
        if (retailPO != null && "达人cps".equals(retailPO.getRetailName()) && retailPO.getId() == 386) {
            // 去除多余菜单
            for (int i = menuList.size() - 1; i > -1; i--) {
                if (StrUtil.equalsAny(menuList.get(i).getMenuId(), 50, 51)) {
                    menuList.remove(i);
                }
            }
        }

        // 去除达人分销商菜单
        if (retailPO != null && retailPO.getBusinessType() != null) {
            for (int i = menuList.size() - 1; i > -1; i--) {
                FastMenuPO m = menuList.get(i);
                if (retailPO.getBusinessType() == 2) {
                    if (m.getParentId() == 53 && !List.of(508, 578).contains(m.getMenuId())) {
                        menuList.remove(i);
                    }
                } else {
                    if (m.getParentId() == 53 && Objects.equals(578, m.getMenuId())) {
                        menuList.remove(i);
                    }
                }
            }
        }


        Map<String, Object> results = ResultVO.getMap();
        StringBuilder menuIdList = new StringBuilder();
        for (FastMenuPO menu : menuList) {
            if (menuIdList.length() > 0) {
                menuIdList.append(",");
            }
            menuIdList.append(menu.getMenuId());
        }
        menuList = getMenuTreeList(menuList, 1);
        SessionVO sessionVO = new SessionVO();
        String token = IdUtil.simpleUUID();
        if (oldSessionVO != null) {
            token = oldSessionVO.getAccessToken();
        }
        sessionVO.setOrgId(user.getOrgId());
        sessionVO.setRoleIds(user.getRoleIds());
        sessionVO.setAccessToken(token);
        sessionVO.setUserId(user.getId());
        sessionVO.setUserName(user.getUserName());
        sessionVO.setRetailId(user.getRetailId());
        sessionVO.setShortName(user.getShortName());
        if (retailPO != null) {
            sessionVO.setRetailType(retailPO.getRetailType());
        }
        sessionVO.setEncryptionUserId(encode(user.getId()));
        sessionVO.setEncryptionRetailId(encode(user.getRetailId()));
        sessionVO.setHeadImg(user.getHeadImg());
        sessionVO.setContentType(contentType);// 设置内容类型
        // 查询数据权限
        List<Integer> roleIds = CollUtil.parseIntStr2List(user.getRoleIds());
        List<Integer> viewList = fastRoleMapper.queryViewPermissionByIds(roleIds);
        if (viewList.contains(2)) {
            sessionVO.setSelfProjectViewFlag(2); // 查看所有项目
        } else {
            sessionVO.setSelfProjectViewFlag(1); // 查自己创建得项目
        }
        if (Objects.nonNull(retailPO)) {
            if (retailPO.getRetailFlag() == 2) {
                sessionVO.setCpUserType(2); // 设置为普通cp用户
                FastUserPO queryById = new FastUserPO();
                queryById.setId(sessionVO.getUserId());
                FastUserPO fastUserPO = fastUserMapper.queryById(queryById);
                FastRoleMenuPO menuPOQuery = new FastRoleMenuPO();
                menuPOQuery.setRoleId(Integer.parseInt(fastUserPO.getRoleIds()));
                List<FastRoleMenuPO> fastRoleMenuPOList = fastRoleMenuMapper.queryListInExtra(menuPOQuery);
                if (CollUtil.isNotEmpty(fastRoleMenuPOList)) {
                    String dramaAnalysisMenuIds = CollUtil.convertIntListToString(fastRoleMenuPOList.stream().map(FastRoleMenuPO::getMenuId).collect(Collectors.toList()));
                    sessionVO.setDramaAnalysisMenuIds(dramaAnalysisMenuIds);
                }
            } else if (retailPO.getRetailFlag() == 1) {
                sessionVO.setCpUserType(3); // 设置为分销商用户
            }
        } else {
            sessionVO.setCpUserType(4); // 平台用户
        }
        if (Objects.nonNull(user.getOrgId()) && user.getOrgId() > 0) {
            sessionVO.setCpUserType(1); // 设置为cp组织用户
        }
        if (retailPO != null) {
            sessionVO.setRetailFlag(retailPO.getRetailFlag());
            sessionVO.setCpType(retailPO.getCpType());
            sessionVO.setBusinessType(retailPO.getBusinessType());
        }
        if (oldSessionVO != null) {
            sessionVO.setContentTypeSet(oldSessionVO.getContentTypeSet());
        }
        if (officialId != null && officialId > 0) {
            sessionVO.setOfficialId(officialId);
            sessionVO.setEncryptionOficialId(encode(officialId));
        } else {
            sessionVO.setOfficialId(0);
        }
        sessionVO.setOfficialName("");
        sessionVO.setMiniId(0);
        sessionVO.setMiniName("");
        sessionVO.setMiniAppid("");
        sessionVO.setDelayReply(null);
        sessionVO.setFollowReply(null);
        sessionVO.setAllMenuId(menuIdList.toString());

        List<SystemFunPO> funList = systemFunMapper.querySimpleList();
        if (CollUtil.hasContent(funList)) {
            sessionVO.setFunList(funList);
        }
        // 设置平台部署系统
        sessionVO.setDeployPlatform(StaticVar.DEPLOY_PLATFORM);

        RedisUtil.set(RedisVar.REDIS_TOKEN_DB, StaticVar.ACCESS_TOKEN_PRE + token, JSONObject.toJSONString(sessionVO), StaticVar.getAdminTokenExp(System.currentTimeMillis()));
        results.put("sessionVO", sessionVO);
        results.put("menuList", menuList);
        results.put("menuIds", menuIdList.toString());
        log.info("切换登录成功");
        return ResultVO.success(results);
    }

    /**
     * 获取树形结构
     *
     * @param menuList
     * @param type     1菜单树，2整树
     * @return
     */
    public List<FastMenuPO> getMenuTreeList(List<FastMenuPO> menuList, Integer type) {
        // 拼装树结构菜单
        for (int i = menuList.size() - 1; i > -1; i--) {
            if (type == 1 && menuList.get(i).getMenuType() != 1) {
                menuList.remove(i);
                continue;
            }
            if (menuList.get(i).getParentId() > 0) {
                for (int j = menuList.size() - 1; j > -1; j--) {
                    if (menuList.get(i).getParentId().equals(menuList.get(j).getMenuId())) {
                        List<FastMenuPO> subMenuList = menuList.get(j).getSubMenuList();
                        if (subMenuList == null) {
                            subMenuList = new ArrayList<>();
                        }
                        subMenuList.add(menuList.get(i));
                        menuList.get(j).setSubMenuList(subMenuList);
                        menuList.remove(i);
                        break;
                    }
                }
                continue;
            }
        }
        // 去除非一级菜单
        for (int i = menuList.size() - 1; i > -1; i--) {
            if (menuList.get(i).getParentId() != 0) {
                menuList.remove(i);
            }
        }
        return menuList;
    }

    /**
     * 工具类 查询分销商数量
     *
     * @param contentType
     * @return
     */
    private Integer getRetailCount(Integer contentType) {
        return fastRetailMapper.queryCountByContentType(contentType);
    }

    private Integer getUserCount(Integer contentType, Integer retailId) {
        return fastUserService.queryUserCount(contentType, retailId);
    }

    /**
     * 工具类 查询小程序数量
     *
     * @param contentType
     * @param type
     * @return
     */
    private Integer getMiniCount(Integer contentType, Integer type, Integer retailId) {
        FastMiniPO miniParam = new FastMiniPO();
        miniParam.setContentType(contentType);
        miniParam.setState(1);
        miniParam.setType(type);
        if (retailId != null && retailId > 0) {
            miniParam.setRetailId(retailId);
            return fastMiniService.queryRetailCount(miniParam);
        } else {
            return fastMiniService.queryCount(miniParam);
        }
    }

    /**
     * 根据用户名手机号
     *
     * @param loginName
     * @return
     */
    public String getPhoneByLoginName(String loginName) {
        FastUserPO query = new FastUserPO();
        query.setLoginName(loginName);
        FastUserPO fastUserPO = fastUserMapper.queryOne(query);
        if (Objects.isNull(fastUserPO) || fastUserPO.getState() == 2 || StrUtil.isBlank(fastUserPO.getPhone())) {
            throw new MyException("账号不存在或手机号为空!");
        }
        return fastUserPO.getPhone();
    }

    public String sendSmsForgetPassword(String smsToken, String loginName, String ip, String ua) {
        String cache = RedisUtil.get(StaticVar.CAPTCHA_SMS_VERIFY_TOKEN_KEY + smsToken);
        if (StrUtil.isBlank(cache)) {
            throw new MyException("请先进行滑块验证");
        }

        String phone = getPhoneByLoginName(loginName);
        if (StrUtil.isBlank(phone)) {
            throw new MyException("用户不存在或手机号为空,请联系管理员!");
        }

        // 校验验证码发送限制
        String prefix = DateUtil.format06(DateUtil.getNowDate());
        long incr = RedisUtil.incr(StaticVar.ADMIN_SEND_SMS_COUNT + prefix + "_" + phone, StaticVar.DAY1_SECOND);
        if (incr > StaticVar.ADMIN_SMS_MAX_SEND_COUNT) {
            throw new MyException("今日短信发送已上线!");
        }
        String randomStr = StrUtil.getRandomInt(6);
        // smsService.sendSmsAdmin(phone, 0, ip, ua, "admin_sms_sign", randomStr);
        String verifyToken = IdUtil.simpleUUID();
        // 缓存redis
        RedisUtil.set(StaticVar.ADMIN_FORGET_PASSWORD_CODE + loginName + "_" + phone + ":" + verifyToken, randomStr, StaticVar.ADMIN_FORGET_PASSWORD_EXPIRE_TIME);

        // 删除滑块验证
        RedisUtil.del(StaticVar.CAPTCHA_SMS_VERIFY_TOKEN_KEY + smsToken);
        return verifyToken;
    }

    public MethodVO updatePasswordBySms(String newPassword, String loginName, String smmCode, String smsToken) {
        if (newPassword.length() < 8) {
            throw new MyException("密码必须大于8位!");
        }
        // 校验密码
        if (!Pattern.matches(StaticVar.ADMIN_PASSWORD_REG, newPassword)) {
            throw new MyException("密码必须包含大小写字母和数字!");
        }
        FastUserPO query = new FastUserPO();
        query.setLoginName(loginName);
        FastUserPO existsUser = fastUserMapper.queryOne(query);
        if (Objects.isNull(existsUser)) {
            throw new MyException("用户不存在!");
        }
        // 验证码校验
        String codeKey = StaticVar.ADMIN_FORGET_PASSWORD_CODE + loginName + "_" + existsUser.getPhone() + ":" + smsToken;
        String cacheCode = RedisUtil.get(codeKey);
        if (StrUtil.isBlank(cacheCode) || (!cacheCode.equals(smmCode) && !StrUtil.equals(StaticVar.ADMIN_SMS_DEFAULT_CODE, smmCode))) {
            throw new MyException("验证码错误!");
        }
        FastUserPO item = new FastUserPO();
        item.setId(existsUser.getId());
        item.setPassword(Md5Util.getMD5BySalt(newPassword));
        item.setPasswordUpdateTime(DateUtil.getNowDate());
        MethodVO methodVO = fastUserService.update(item);
        // 删除验证码
        RedisUtil.del(codeKey);
        return methodVO;
    }

}
