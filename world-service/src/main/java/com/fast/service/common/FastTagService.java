/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.LanguageEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.common.FastTagMapper;
import com.fast.po.common.FastTagI18nPO;
import com.fast.po.common.FastTagPO;
import com.fast.service.base.BaseService;
import com.fast.service.language.FastLanguageService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.thread.FastUserContext;
import com.fast.utils.thread.LanguageContext;
import com.fast.utils.thread.SysTypeContext;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.common.FastTagVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastTagService extends BaseService {

    @Autowired
    private FastTagMapper fastTagMapper;

    @Autowired
    private FastLanguageService fastLanguageService;

    @Autowired
    private FastTagI18nService fastTagI18nService;

    /**
     * 查询缓存信息
     */
    public FastTagVO queryInfoByRedis(FastTagPO item) {
        if (item.getId() == null) {
            return null;
        }
        // item.setDelFlag(StaticVar.NO); // 需要查询出已删除的标签
        FastTagVO vo = new FastTagVO();
        String key = StaticVar.TAG_INFO_ID + item.getId();
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            vo = JsonUtil.toJavaObject(value, FastTagVO.class);
        } else {
            FastTagPO po = queryById(item.getId(), false);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_10D);
                return null;
            } else {
                BeanUtils.copyProperties(po, vo);
                vo.setEncryptionId(encode(vo.getId()));
                RedisUtil.set(key, JsonUtil.toString(vo), RedisUtil.TIME_10D);
            }
        }
        // i18n
        fillI18n(vo);
        return vo;
    }

    /**
     * 查询缓存信息
     */
    public FastTagVO queryInfoByRedis(Integer id) {
        FastTagPO itemParam = new FastTagPO();
        itemParam.setId(id);
        return queryInfoByRedis(itemParam);
    }

    /**
     * 查询缓存信息
     */
    public List<FastTagVO> queryInfoByRedis(String ids) {
        List<FastTagVO> result = new ArrayList<>();
        if (notEmpty(ids)) {
            FastTagPO itemParam = new FastTagPO();
            List<Integer> idList = CollUtil.parseIntStr2List(ids);
            for (Integer id : idList) {
                itemParam.setId(id);
                FastTagVO vo = queryInfoByRedis(itemParam);
                if (vo != null) {
                    result.add(vo);
                }
            }
        }
        return result;
    }

    /**
     * 通过id查询单个对象
     */
    public FastTagPO queryById(FastTagPO item) {
        return fastTagMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastTagPO queryById(Integer id, boolean i18n) {
        FastTagPO itemParam = new FastTagPO();
        itemParam.setId(id);
        FastTagPO item = fastTagMapper.queryById(itemParam);
        if (item == null) {
            return null;
        }
        if (i18n) {
            fillI18n(item);
        }
        return item;
    }

    public void fillI18n(FastTagPO item) {
        if (item == null || item.getId() == null) {
            return;
        }
        Map<String, FastTagI18nPO> map = fastTagI18nService.getMapByIdForLang(item.getId());
        if (CollUtil.isEmpty(map)) {
            return;
        }
        if (SysTypeContext.isBackend()) {
            Map<String, String> nameMap = new LinkedHashMap<>(), remarkMap = new LinkedHashMap<>();
            map.forEach((k, v) -> {
                nameMap.put(k, v.getName());
                remarkMap.put(k, v.getRemark());
            });
            item.setTagNames(nameMap);
            item.setRemarks(remarkMap);
        }
        if (SysTypeContext.isApp()) {
            FastTagI18nPO i18nPO = map.get(LanguageContext.getLanguageType());
            if (i18nPO == null) {
                return;
            }
            item.setTagName(i18nPO.getName());
            item.setRemark(i18nPO.getRemark());
        }
    }

    private void fillI18n(FastTagVO item) {
        Map<String, FastTagI18nPO> map = fastTagI18nService.getMapByIdForLang(item.getId());
        if (CollUtil.isEmpty(map)) {
            return;
        }
        if (SysTypeContext.isBackend()) {
            Map<String, String> nameMap = new LinkedHashMap<>(), remarkMap = new LinkedHashMap<>();
            map.forEach((k, v) -> {
                nameMap.put(k, v.getName());
                remarkMap.put(k, v.getRemark());
            });
            item.setTagNames(nameMap);
            item.setRemarks(remarkMap);
        }
        if (SysTypeContext.isApp()) {
            FastTagI18nPO i18nPO = map.get(LanguageContext.getLanguageType());
            if (i18nPO == null) {
                return;
            }
            item.setTagName(i18nPO.getName());
            item.setRemark(i18nPO.getRemark());
        }
    }

    /**
     * 通过条件查询单个对象
     */
    public FastTagPO queryOne(FastTagPO item) {
        return fastTagMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastTagPO> queryList(FastTagPO item) {
        return fastTagMapper.queryList(item);
    }

    /**
     * 查询全部
     */
    public List<Integer> queryTagIds(FastTagPO item) {
        return fastTagMapper.queryTagIds(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastTagPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastTagPO> list = fastTagMapper.queryList(item);
        for (FastTagPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            // i18n
            fillI18n(cur);
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.summary(results, fastLanguageService.allCodeMapList());
    }

    /**
     * 查询总数
     */
    public int queryCount(FastTagPO item) {
        return fastTagMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastTagPO item) {
        List<FastTagI18nPO> i18ns = item.getI18ns();
        if (CollUtil.isEmpty(i18ns)) {
            return MethodVO.error("国际化内容不能为空");
        }
        String scContent = takeDefaultContent(i18ns);
        item.setTagName(scContent);
        item.setRemark(scContent);

        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastTagMapper.insertSelective(item) == 0) {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        i18ns.forEach(i18n -> {
            if (StrUtil.isBlank(i18n.getName())) {
                return;
            }
            if (StrUtil.isBlank(i18n.getRemark())) {
                i18n.setRemark(i18n.getName());
            }
            i18n.setTagId(item.getId());
            i18n.setCreateTime(nowTime);
            i18n.setCreatorId(FastUserContext.getUserId());
        });
        MethodVO methodVO = fastTagI18nService.insertBatch(i18ns);
        if (methodVO.getCode() != 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        return MethodVO.success();
    }

    private String takeDefaultContent(List<FastTagI18nPO> i18ns) {
        String defaulted = null;
        for (FastTagI18nPO i18n : i18ns) {
            if (Objects.equals(i18n.getLanguageCode(), LanguageEnum.ENGLISH.getCode())) {
                defaulted = i18n.getName();
                break;
            }
        }
        if (defaulted == null) {
            throw new MyException("英文是默认兜底版，不允许为空");
        }
        return defaulted;
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastTagPO> list) {
        if (fastTagMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastTagPO item) {
        List<FastTagI18nPO> i18ns = item.getI18ns();
        if (CollUtil.isEmpty(i18ns)) {
            return MethodVO.error("国际化内容不能为空");
        }
        String scContent = takeDefaultContent(i18ns);
        item.setTagName(scContent);
        item.setRemark(scContent);

        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastTagMapper.updateById(item) == 0) {
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }

        fastTagI18nService.deleteByTagId(item.getId());

        i18ns.forEach(i18n -> {
            if (StrUtil.isBlank(i18n.getName())) {
                return;
            }
            if (StrUtil.isBlank(i18n.getRemark())) {
                i18n.setRemark(i18n.getName());
            }
            i18n.setTagId(item.getId());
            i18n.setCreateTime(nowTime);
            i18n.setCreatorId(FastUserContext.getUserId());
        });
        MethodVO methodVO = fastTagI18nService.insertBatch(i18ns);
        if (methodVO.getCode() != 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDel(FastTagPO item) {
        FastTagPO del = new FastTagPO();
        del.setUpdatorId(item.getUpdatorId());
        del.setUpdateTime(DateUtil.getNowDate());
        del.setId(item.getId());
        del.setDelFlag(StaticVar.YES);
        if (fastTagMapper.updateById(del) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        fastTagI18nService.deleteByTagId(item.getId());
        return MethodVO.success();
    }

    public Map<Integer, List<FastTagPO>> queryByTagTypeIds(List<Integer> tagTypeIds) {
        if (CollUtil.isEmpty(tagTypeIds)) {
            return new HashMap<>();
        }
        FastTagPO tagPO = new FastTagPO();
        tagPO.setTagTypeIds(StrUtil.join(tagTypeIds, ","));
        List<FastTagPO> list = queryList(tagPO);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, List<FastTagPO>> map = new HashMap<>();
        list.forEach(cur -> {
            List<FastTagPO> tagList = map.get(cur.getTagTypeId());
            if (tagList == null) {
                tagList = new ArrayList<>();
            }
            tagList.add(cur);
            map.put(cur.getTagTypeId(), tagList);
        });
        return map;
    }
    
}
