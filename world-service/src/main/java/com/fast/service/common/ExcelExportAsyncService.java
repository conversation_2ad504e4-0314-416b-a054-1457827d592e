package com.fast.service.common;

import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.framework.thread.ExportThreadExecutor;
import com.fast.mapper.common.FastFileExportMapper;
import com.fast.po.common.FastFileExportPO;
import com.fast.service.base.BaseService;
import com.fast.service.oss.OssService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.file.FileUtil;
import com.fast.utils.poi.ExcelExportUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.SessionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * 通用导出
 */
@Service
public class ExcelExportAsyncService extends BaseService {

    @Autowired
    private FastFileExportMapper fileExportMapper;
    @Autowired
    private FastActionLogService fastActionLogService;
    @Autowired
    private OssService ossService;

    /**
     * 生成Excel文件
     *
     * @param sessionVO
     * @param dataList     表格每行内容
     * @param rowHeadNames 表格头行列属性
     * @param title        表格标题
     * @param key          redis缓存key
     * @return
     */
    @Async(ExportThreadExecutor.NAME)
    public void export(SessionVO sessionVO, List<List<Object>> dataList, List<String> rowHeadNames, String title, String key) {
        try {
            long startTime = System.currentTimeMillis();
            String fileName = create(dataList, rowHeadNames, title);
            fastActionLogService.log("excel_export", sessionVO.getExportKeyId() + "导出数据excel生成文件耗时:" + (System.currentTimeMillis() - startTime));
            if (StrUtil.notEmpty(fileName)) {
                File file = new File(fileName);
                if (!file.exists()) {
                    throw new MyException("导出文件不存在!");
                }
                // 上传oss
                long s = System.currentTimeMillis();
                String fileKey = ossService.uploadFile(false, sessionVO.getRetailId(), 4, file);
                long e = System.currentTimeMillis();
                log.info("导出的附件[{}]上传至OSS完成，耗时: {}s", fileKey, (e - s) / 1000f);
                // 写入db
                Date nowTime = DateUtil.getNowDate();
                FastFileExportPO fileExport = new FastFileExportPO();
                fileExport.setDelFlag(0);
                fileExport.setRetailId(sessionVO.getRetailId());
                fileExport.setUrl(fileKey);
                fileExport.setTitle(title);
                fileExport.setCreatorId(sessionVO.getUserId());
                fileExport.setCreateTime(nowTime);
                fileExport.setDeadTime(DateUtil.addDays(nowTime, 1));
                fileExportMapper.insertSelective(fileExport);

                file.delete();
                if (notEmpty(key)) {
                    RedisUtil.incr(key, RedisUtil.TIME_1D);
                }
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
        } catch (Exception e) {
            log.error("error:", e);
        }
    }


    /**
     * 生成Excel文件 cp月结算单
     *
     * @param sessionVO
     * @param dataList     表格每行内容
     * @param rowHeadNames 表格头行列属性
     * @param title        表格标题
     * @param key          redis缓存key
     * @return
     */
    @Async(ExportThreadExecutor.NAME)
    public void exportCpStatementMonth(SessionVO sessionVO, List<List<Object>> dataList, List<String> rowHeadNames, String title, String key) {
        try {
            long startTime = System.currentTimeMillis();
            String filePath = StaticVar.EXPORT_TEMP_PATH + "/";
            // log.info("开始创建Excel文件:{};行数:{}", title, dataList.size());
            FileUtil.newFolder(filePath);// 创建文件夹
            long start = System.currentTimeMillis();
            String export = ExcelExportUtil.exportCpStatementMonth(title, rowHeadNames, dataList, filePath);
            long end = System.currentTimeMillis();
            // log.info("创建Excel文件耗费时间:{}毫秒", (end - start));
            String fileName = filePath + export;
            fastActionLogService.log("excel_export", sessionVO.getExportKeyId() + "导出数据excel生成文件耗时:" + (System.currentTimeMillis() - startTime));
            if (StrUtil.notEmpty(fileName)) {
                File file = new File(fileName);
                if (!file.exists()) {
                    throw new MyException("导出文件不存在!");
                }
                // 上传oss
                String fileKey = ossService.uploadFile(false, sessionVO.getRetailId(), 4, file);
                Date nowTime = DateUtil.getNowDate();
                FastFileExportPO fileExport = new FastFileExportPO();
                fileExport.setDelFlag(0);
                fileExport.setRetailId(sessionVO.getRetailId());
                fileExport.setUrl(fileKey);
                fileExport.setTitle(title);
                fileExport.setCreatorId(sessionVO.getUserId());
                fileExport.setCreateTime(nowTime);
                fileExport.setDeadTime(DateUtil.addDays(nowTime, 1));
                fileExportMapper.insertSelective(fileExport);

                file.delete();
                if (notEmpty(key)) {
                    RedisUtil.incr(key, RedisUtil.TIME_1D);
                }
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
        } catch (Exception e) {
            log.error("error:", e);
        }
    }

    /**
     * 生成Excel文件
     *
     * @param sessionVO
     * @param dataList  表格每行内容
     * @param title     表格标题
     * @param key       redis缓存key
     * @return
     */
    @Async(ExportThreadExecutor.NAME)
    public void exportCp2(SessionVO sessionVO, List<List<Object>> dataList, List<List<Object>> dataList2, String title, String key, boolean isProducer, boolean isWb) {
        try {
            long startTime = System.currentTimeMillis();
            String filePath = StaticVar.EXPORT_TEMP_PATH + "/";
            // log.info("开始创建Excel文件:{};行数:{}", title, dataList.size());
            FileUtil.newFolder(filePath);// 创建文件夹
            long start = System.currentTimeMillis();
            String export = ExcelExportUtil.exportCpSettmentV2(title, dataList, dataList2, filePath, isProducer, isWb);
            long end = System.currentTimeMillis();
            // log.info("创建Excel文件耗费时间:{}毫秒", (end - start));
            String fileName = filePath + export;

            fastActionLogService.log("excel_export", sessionVO.getExportKeyId() + "导出数据excel生成文件耗时:" + (System.currentTimeMillis() - startTime));
            if (StrUtil.notEmpty(fileName)) {
                File file = new File(fileName);
                if (!file.exists()) {
                    throw new MyException("导出文件不存在!");
                }
                // 上传oss
                String fileKey = ossService.uploadFileByName(false, sessionVO.getRetailId(), 4, file, title);
                Date nowTime = DateUtil.getNowDate();
                FastFileExportPO fileExport = new FastFileExportPO();
                fileExport.setDelFlag(0);
                fileExport.setRetailId(sessionVO.getRetailId());
                fileExport.setUrl(fileKey);
                fileExport.setTitle(title);
                fileExport.setCreatorId(sessionVO.getUserId());
                fileExport.setCreateTime(nowTime);
                fileExport.setDeadTime(DateUtil.addDays(nowTime, 1));
                fileExportMapper.insertSelective(fileExport);
                file.delete();
                if (notEmpty(key)) {
                    RedisUtil.incr(key, RedisUtil.TIME_1D);
                }
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
        } catch (Exception e) {
            log.error("error:", e);
        }
    }


    public String exportCpNoAsync(SessionVO sessionVO, List<List<Object>> dataList, List<List<Object>> dataList2, String title, String key, boolean isProducer, boolean isWb) {
        try {
            long startTime = System.currentTimeMillis();
            String filePath = StaticVar.EXPORT_TEMP_PATH + "/";
            // log.info("开始创建Excel文件:{};行数:{}", title, dataList.size());
            FileUtil.newFolder(filePath);// 创建文件夹
            long start = System.currentTimeMillis();
            String export = ExcelExportUtil.exportCpSettmentV2(title, dataList, dataList2, filePath, isProducer, isWb);
            long end = System.currentTimeMillis();
            // log.info("创建Excel文件耗费时间:{}毫秒", (end - start));
            String fileName = filePath + export;

            fastActionLogService.log("excel_export", sessionVO.getExportKeyId() + "导出数据excel生成文件耗时:" + (System.currentTimeMillis() - startTime));
            if (StrUtil.notEmpty(fileName)) {
                File file = new File(fileName);
                if (!file.exists()) {
                    throw new MyException("导出文件不存在!");
                }
                return fileName;
            } else {
                return "";
            }
        } catch (Exception e) {
            log.error("error:", e);
        }
        return "";
    }

    /**
     * 生成Excel文件，同步生成，返回oss地址
     *
     * @param sessionVO
     * @param dataList     表格每行内容
     * @param rowHeadNames 表格头行列属性
     * @param title        表格标题
     * @param key          redis缓存key
     * @return
     */
    public String exportSync(SessionVO sessionVO, List<List<Object>> dataList, List<String> rowHeadNames, String title, String key) {
        try {
            String fileName = create(dataList, rowHeadNames, title);
            if (StrUtil.notEmpty(fileName)) {
                File file = new File(fileName);
                if (!file.exists()) {
                    throw new MyException("导出文件不存在!");
                }
                // 上传oss
                String fileKey = ossService.uploadFile(false, sessionVO.getRetailId(), 4, file);
                Date nowTime = DateUtil.getNowDate();
                FastFileExportPO fileExport = new FastFileExportPO();
                fileExport.setDelFlag(0);
                fileExport.setRetailId(sessionVO.getRetailId());
                fileExport.setUrl(fileKey);
                fileExport.setTitle(title);
                fileExport.setCreatorId(sessionVO.getUserId());
                fileExport.setCreateTime(nowTime);
                fileExport.setDeadTime(DateUtil.addDays(nowTime, 1));
                fileExportMapper.insertSelective(fileExport);

                file.delete();
                if (notEmpty(key)) {
                    RedisUtil.incr(key, RedisUtil.TIME_1D);
                }
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
        } catch (Exception e) {
            log.error("error:", e);
        }
        return null;
    }

    public void exportCpSettlemnt(SessionVO sessionVO, List<List<Object>> headDataList, List<List<Object>> dataList, List<String> rowHeadNames, String title, String key) {
        try {
            String fileName = createCpSettment(headDataList, dataList, rowHeadNames, title);
            if (StrUtil.notEmpty(fileName)) {
                File file = new File(fileName);
                if (!file.exists()) {
                    throw new MyException("导出文件不存在!");
                }
                // 上传oss
                String fileKey = ossService.uploadFile(false, sessionVO.getRetailId(), 4, file);
                Date nowTime = DateUtil.getNowDate();
                FastFileExportPO fileExport = new FastFileExportPO();
                fileExport.setDelFlag(0);
                fileExport.setRetailId(sessionVO.getRetailId());
                fileExport.setUrl(fileKey);
                fileExport.setTitle(title);
                fileExport.setCreatorId(sessionVO.getUserId());
                fileExport.setCreateTime(nowTime);
                fileExport.setDeadTime(DateUtil.addDays(nowTime, 1));
                fileExportMapper.insertSelective(fileExport);
                file.delete();
                if (notEmpty(key)) {
                    RedisUtil.incr(key, RedisUtil.TIME_1D);
                }
                // 清空导出的频次限制
                RedisUtil.del(StaticVar.EXPORT_RATE + sessionVO.getUserId());
            }
        } catch (Exception e) {
            log.error("error:", e);
        }
    }

    /**
     * 创建Excel文件
     *
     * @return 本地暂时存文件名(全地址)
     */
    private String create(List<List<Object>> dataList, List<String> rowHeadNames, String title) {
        String filePath = StaticVar.EXPORT_TEMP_PATH + "/";
        log.info("开始创建Excel文件:{};行数:{}", title, dataList.size());
        FileUtil.newFolder(filePath);// 创建文件夹
        long start = System.currentTimeMillis();
        String export = ExcelExportUtil.export(title, rowHeadNames, dataList, filePath);
        long end = System.currentTimeMillis();
        log.info("创建Excel文件耗费时间:{}毫秒", (end - start));
        return filePath + export;
    }

    private String createCpSettment(List<List<Object>> headDataList, List<List<Object>> dataList, List<String> rowHeadNames, String title) {
        String filePath = StaticVar.EXPORT_TEMP_PATH + "/";
        log.info("开始创建Excel文件:{};行数:{}", title, dataList.size());
        FileUtil.newFolder(filePath);// 创建文件夹
        long start = System.currentTimeMillis();
        String export = ExcelExportUtil.exportCpSettment(title, headDataList, rowHeadNames, dataList, filePath, null);
        long end = System.currentTimeMillis();
        log.info("创建Excel文件耗费时间:{}毫秒", (end - start));
        return filePath + export;
    }

    public String exportCpStatementMonthBatch(SessionVO sessionVO, List<List<Object>> dataList, List<String> rowHeadNames, String title) {
        try {
            long startTime = System.currentTimeMillis();
            String filePath = StaticVar.EXPORT_TEMP_PATH + "/";
            // log.info("开始创建Excel文件:{};行数:{}", title, dataList.size());
            FileUtil.newFolder(filePath);// 创建文件夹
            long start = System.currentTimeMillis();
            String export = ExcelExportUtil.exportCpStatementMonth(title, rowHeadNames, dataList, filePath);
            long end = System.currentTimeMillis();
            // log.info("创建Excel文件耗费时间:{}毫秒", (end - start));
            String fileName = filePath + export;
            fastActionLogService.log("excel_export", sessionVO.getExportKeyId() + "导出数据excel生成文件耗时:" + (System.currentTimeMillis() - startTime));
            return fileName;
        } catch (Exception e) {
            log.error("error:", e);
        }
        return "";
    }
}
