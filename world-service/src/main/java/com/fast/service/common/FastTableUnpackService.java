/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.common.FastTableUnpackMapper;
import com.fast.po.common.FastTableUnpackPO;
import com.fast.po.member.FastMemberRecentLogPO;
import com.fast.po.member.FastMemberRecentSeriesDayPO;
import com.fast.service.base.BaseService;
import com.fast.service.member.FastMemberRecentLogService;
import com.fast.service.member.FastMemberRecentSeriesDayService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastTableUnpackService extends BaseService {

    @Autowired
    private FastTableUnpackMapper tableUnpackMapper;
    @Autowired
    private FastMemberRecentLogService recentLogService;
    @Autowired
    private FastMemberRecentSeriesDayService recentSeriesDayService;

    /**
     * 通过id查询单个对象
     */
    public FastTableUnpackPO queryById(FastTableUnpackPO item) {
        return tableUnpackMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastTableUnpackPO queryById(Integer id) {
        return tableUnpackMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastTableUnpackPO queryOne(FastTableUnpackPO item) {
        return tableUnpackMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastTableUnpackPO> queryList(FastTableUnpackPO item) {
        return tableUnpackMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastTableUnpackPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastTableUnpackPO> list = tableUnpackMapper.queryList(item);
        for (FastTableUnpackPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastTableUnpackPO item) {
        return tableUnpackMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastTableUnpackPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (tableUnpackMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 自动同步数据-拆表
     */
    public void autoSyncTable() {
        FastTableUnpackPO unpackQuery = new FastTableUnpackPO();
        unpackQuery.setState(1);
        List<FastTableUnpackPO> unpackList = tableUnpackMapper.queryList(unpackQuery);
        if (CollUtil.isEmpty(unpackList)) {
            return;
        }
        for (FastTableUnpackPO unpack : unpackList) {
            // 判断新表是否存在
            if (tableUnpackMapper.queryTableCount(unpack) == 0) {
                tableUnpackMapper.createTable(unpack);
                switch (unpack.getTableName()) {
                    case "fast_member_recent_log":
                        tableUnpackMapper.updateTableIndex(unpack);
                        break;
                    case "fast_member_recent_series_day":
                        tableUnpackMapper.updateTableIndex2(unpack);
                        break;
                }
            }
            // 同步数据
            switch (unpack.getTableName()) {
                case "fast_member_recent_log": {
                    FastMemberRecentLogPO query = new FastMemberRecentLogPO();
                    List<FastMemberRecentLogPO> list = new ArrayList<>();
                    do {
                        list.clear();
                        Long maxId = defaultIfNull(recentLogService.queryTableUnpackList(unpack), 0L);
                        query.setDramaId(unpack.getTableColumValue());
                        query.setLimitExport(StaticVar.DATA_2K);
                        query.setMinId(++maxId);// 分表里最大值+1作为原始表数据的开始位置
                        list.addAll(recentLogService.querySlaveList(query));
                        if (list.size() > 0) {
                            try {
                                int r = recentLogService.insertBatchTableUnpack(list, unpack);
                                if (r == 0) {
                                    throw new MyException("批量新增-拆表-失败");
                                }
                            } catch (Exception e) {
                                log.error("批量新增-拆表-失败", e);
                                break;
                            }
                        }
                    } while (list.size() == StaticVar.DATA_2K);
                }
                break;
                case "fast_member_recent_series_day": {
                    FastMemberRecentSeriesDayPO query = new FastMemberRecentSeriesDayPO();
                    List<FastMemberRecentSeriesDayPO> list = new ArrayList<>();
                    do {
                        list.clear();
                        Long maxId = defaultIfNull(recentSeriesDayService.queryTableUnpackList(unpack), 0L);
                        query.setDramaId(unpack.getTableColumValue());
                        query.setLimitExport(StaticVar.DATA_2K);
                        query.setMinId(++maxId);// 分表里最大值+1作为原始表数据的开始位置
                        list.addAll(recentSeriesDayService.querySlaveList(query));
                        if (list.size() > 0) {
                            try {
                                int r = recentSeriesDayService.insertBatchTableUnpack(list, unpack);
                                if (r == 0) {
                                    throw new MyException("批量新增-拆表-失败");
                                }
                            } catch (Exception e) {
                                log.error("批量新增-拆表-失败", e);
                                break;
                            }
                        }
                    } while (list.size() == StaticVar.DATA_2K);
                }
                break;
            }
        }
    }

    /**
     * 短剧id同步到缓存-拆表
     */
    public void autoSyncTableId() {
        FastTableUnpackPO unpackQuery = new FastTableUnpackPO();
        unpackQuery.setState(1);
        LinkedHashSet<Integer> ids = tableUnpackMapper.queryValueIdList(unpackQuery);
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        String value = RedisUtil.get(StaticVar.TABLE_UNPACK_DRAMA_ID_KEY);
        String dbValue = StrUtil.join(ids);
        if (dbValue.equals(value)) {
            return;
        }
        RedisUtil.set(StaticVar.TABLE_UNPACK_DRAMA_ID_KEY, dbValue, 60 * 60);
    }
}
