/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.LanguageEnum;
import com.fast.framework.exception.MyException;
import com.fast.mapper.common.FastCornerMapper;
import com.fast.po.common.FastCornerI18nPO;
import com.fast.po.common.FastCornerPO;
import com.fast.service.base.BaseService;
import com.fast.service.language.FastLanguageService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.thread.FastUserContext;
import com.fast.utils.thread.LanguageContext;
import com.fast.utils.thread.SysTypeContext;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.common.FastCornerVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastCornerService extends BaseService {

    @Autowired
    private FastCornerMapper fastCornerMapper;

    @Autowired
    private FastCornerI18nService fastCornerI18nService;

    @Autowired
    private FastLanguageService fastLanguageService;

    /**
     * 查询缓存信息
     */
    public FastCornerVO queryInfoByRedis(FastCornerPO param) {
        if (param.getId() == null) {
            return null;
        }
        param.setDelFlag(StaticVar.NO);
        FastCornerVO vo = new FastCornerVO();
        String key = StaticVar.CORNER_INFO_ID + param.getId();
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            vo = JsonUtil.toJavaObject(value, FastCornerVO.class);
        } else {
            FastCornerPO po = queryById(param.getId(), false);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.ONE_MINUTE);
                return null;
            } else {
                BeanUtils.copyProperties(po, vo);
                vo.setEncryptionId(encode(vo.getId()));
                RedisUtil.set(key, JsonUtil.toString(vo), RedisUtil.TIME_7D);
            }
        }
        // i18n
        fillI18n(vo);
        return vo;
    }

    /**
     * 查询缓存信息
     */
    public FastCornerVO queryInfoByRedis(Integer id) {
        FastCornerPO itemParam = new FastCornerPO();
        itemParam.setId(id);
        return queryInfoByRedis(itemParam);
    }

    /**
     * 查询缓存信息
     */
    public List<FastCornerVO> queryInfoByRedis(String ids) {
        List<FastCornerVO> result = new ArrayList<>();
        if (notEmpty(ids)) {
            FastCornerPO itemParam = new FastCornerPO();
            List<Integer> idList = CollUtil.parseIntStr2List(ids);
            for (Integer id : idList) {
                itemParam.setId(id);
                FastCornerVO vo = queryInfoByRedis(itemParam);
                if (vo != null) {
                    result.add(vo);
                }
            }
        }
        return result;
    }

    /**
     * 通过id查询单个对象
     */
    public FastCornerPO queryById(FastCornerPO item) {
        return fastCornerMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastCornerPO queryById(Integer id, boolean i18n) {
        FastCornerPO itemParam = new FastCornerPO();
        itemParam.setId(id);
        FastCornerPO item = fastCornerMapper.queryById(itemParam);
        if (item == null) {
            return null;
        }
        if (i18n) {
            fillI18n(item);
        }
        return item;
    }

    private void fillI18n(FastCornerPO item) {
        Map<String, FastCornerI18nPO> map = fastCornerI18nService.getMapByIdForLang(item.getId());
        if (CollUtil.isEmpty(map)) {
            return;
        }
        if (SysTypeContext.isBackend()) {
            Map<String, String> nameMap = new LinkedHashMap<>(), remarkMap = new LinkedHashMap<>();
            map.forEach((k, v) -> {
                nameMap.put(k, v.getName());
                remarkMap.put(k, v.getRemark());
            });
            item.setCornerNames(nameMap);
            item.setRemarks(remarkMap);
        }
        if (SysTypeContext.isApp()) {
            String languageType = LanguageContext.getLanguageType();
            FastCornerI18nPO i18nPO = map.get(languageType);
            if (i18nPO == null) {
                return;
            }
            item.setCornerName(i18nPO.getName());
            item.setRemark(i18nPO.getRemark());
        }
    }

    private void fillI18n(FastCornerVO item) {
        Map<String, FastCornerI18nPO> map = fastCornerI18nService.getMapByIdForLang(item.getId());
        if (CollUtil.isEmpty(map)) {
            return;
        }
        if (SysTypeContext.isBackend()) {
            Map<String, String> nameMap = new LinkedHashMap<>(), remarkMap = new LinkedHashMap<>();
            map.forEach((k, v) -> {
                nameMap.put(k, v.getName());
                remarkMap.put(k, v.getRemark());
            });
            item.setCornerNames(nameMap);
            item.setRemarks(remarkMap);
        }
        if (SysTypeContext.isApp()) {
            String languageType = LanguageContext.getLanguageType();
            FastCornerI18nPO i18nPO = map.get(languageType);
            if (i18nPO == null) {
                return;
            }
            item.setCornerName(i18nPO.getName());
            item.setRemark(i18nPO.getRemark());
        }
    }

    /**
     * 通过条件查询单个对象
     */
    public FastCornerPO queryOne(FastCornerPO item) {
        return fastCornerMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastCornerPO> queryList(FastCornerPO item) {
        return fastCornerMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastCornerPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastCornerPO> list = fastCornerMapper.queryList(item);
        for (FastCornerPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            // i18n
            fillI18n(cur);
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.summary(results, fastLanguageService.allCodeMapList());
    }

    /**
     * 查询总数
     */
    public int queryCount(FastCornerPO item) {
        return fastCornerMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastCornerPO item) {
        List<FastCornerI18nPO> i18ns = item.getI18ns();
        if (CollUtil.isEmpty(i18ns)) {
            return MethodVO.error("国际化内容不能为空");
        }
        String scContent = takeDefaultContent(i18ns);
        item.setCornerName(scContent);
        item.setRemark(scContent);

        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastCornerMapper.insertSelective(item) == 0) {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        i18ns.forEach(i18n -> {
            if (StrUtil.isBlank(i18n.getName())) {
                return;
            }
            if (StrUtil.isBlank(i18n.getRemark())) {
                i18n.setRemark(i18n.getName());
            }
            i18n.setCornerId(item.getId());
            i18n.setCreateTime(nowTime);
            i18n.setCreatorId(FastUserContext.getUserId());
        });
        MethodVO methodVO = fastCornerI18nService.insertBatch(i18ns);
        if (methodVO.getCode() != 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        return MethodVO.success();
    }

    private String takeDefaultContent(List<FastCornerI18nPO> i18ns) {
        String defaulted = null;
        for (FastCornerI18nPO i18n : i18ns) {
            if (Objects.equals(i18n.getLanguageCode(), LanguageEnum.ENGLISH.getCode())) {
                defaulted = i18n.getName();
                break;
            }
        }
        if (defaulted == null) {
            throw new MyException("英文是默认兜底版，不允许为空");
        }
        return defaulted;
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastCornerPO> list) {
        if (fastCornerMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastCornerPO item) {
        List<FastCornerI18nPO> i18ns = item.getI18ns();
        if (CollUtil.isEmpty(i18ns)) {
            return MethodVO.error("国际化内容不能为空");
        }
        String scContent = takeDefaultContent(i18ns);
        item.setCornerName(scContent);
        item.setRemark(scContent);

        Date nowTime = DateUtil.getNowDate();
        item.setUpdateTime(nowTime);
        if (fastCornerMapper.updateById(item) == 0) {
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }

        fastCornerI18nService.deleteByCornerId(item.getId());

        i18ns.forEach(i18n -> {
            if (StrUtil.isBlank(i18n.getName())) {
                return;
            }
            if (StrUtil.isBlank(i18n.getRemark())) {
                i18n.setRemark(i18n.getName());
            }
            i18n.setCornerId(item.getId());
            i18n.setCreateTime(nowTime);
            i18n.setCreatorId(FastUserContext.getUserId());
        });
        MethodVO methodVO = fastCornerI18nService.insertBatch(i18ns);
        if (methodVO.getCode() != 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        return MethodVO.success();
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDel(FastCornerPO item) {
        FastCornerPO del = new FastCornerPO();
        del.setUpdatorId(item.getUpdatorId());
        del.setUpdateTime(DateUtil.getNowDate());
        del.setId(item.getId());
        del.setDelFlag(StaticVar.YES);
        if (fastCornerMapper.updateById(del) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        fastCornerI18nService.deleteByCornerId(item.getId());
        return MethodVO.success();
    }
}
