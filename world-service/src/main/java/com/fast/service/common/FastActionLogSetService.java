/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.mapper.common.FastActionLogSetMapper;
import com.fast.po.common.FastActionLogSetPO;
import com.fast.service.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastActionLogSetService extends BaseService {

    @Autowired
    private FastActionLogSetMapper fastActionLogSetMapper;

    /**
     * 通过id查询单个对象
     */
    public FastActionLogSetPO queryById(FastActionLogSetPO item) {
        return fastActionLogSetMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastActionLogSetPO queryById(Integer id) {
        return fastActionLogSetMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastActionLogSetPO queryOne(FastActionLogSetPO item) {
        return fastActionLogSetMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastActionLogSetPO> queryList(FastActionLogSetPO item) {
        return fastActionLogSetMapper.queryList(item);
    }

}
