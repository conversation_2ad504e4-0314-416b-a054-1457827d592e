/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticVar;
import com.fast.mapper.common.FastActionLogMapper;
import com.fast.mapper.common.FastActionLogSetMapper;
import com.fast.po.common.FastActionLogPO;
import com.fast.po.common.FastActionLogSetPO;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FastActionLogService {

    @Autowired
    private FastActionLogMapper fastActionLogMapper;
    @Autowired
    private FastActionLogSetMapper fastActionLogSetMapper;

    private static final Set<String> keywordsSet = new HashSet<>();
    private static int count;// 计数

    /**
     * 日志入库
     *
     * @param keywords
     * @param content
     */
    @Async
    public void log(String keywords, String content) {
        try {
            if (needToSave(keywords)) {
                log.info("{}_{}", keywords, content);
                if (StrUtil.isNotEmpty(content) && content.length() > 20000) {
                    content = content.substring(0, 20000);
                }
                FastActionLogPO alPO = new FastActionLogPO();
                alPO.setContent(content);
                alPO.setCreateTime(DateUtil.getNowDate());
                alPO.setKeywords(keywords);
                fastActionLogMapper.insertSelective(alPO);
            }
        } catch (Exception e) {
            String message = e.getMessage();
            if (StrUtil.isEmpty(message)) {
                message = e.toString();
            }
            log.error("记录日志异常--{}", message);
        }
    }

    public void error(String content) {
        log.error(content);
        try {
            FastActionLogPO alPO = new FastActionLogPO();
            alPO.setContent(content);
            alPO.setCreateTime(DateUtil.getNowDate());
            alPO.setKeywords("error");
            fastActionLogMapper.insertSelective(alPO);
        } catch (Exception e) {
            log.error("记录日志异常--{}", e.getMessage());
        }
    }

    public boolean needToSave(String keywords) {
        // count = count == null ? 0 : count;
        // keywordsSet = keywordsSet == null ? new HashSet<>() : keywordsSet;
        if (count <= 0) {
            // 从redis获取
            String cache = RedisUtil.get(StaticVar.ACTION_LOG_CACHE);
            if (StrUtil.isEmpty(cache) || "cache".equals(cache)) {
                // 重置cache
                keywordsSet.clear();
                FastActionLogSetPO alParam = new FastActionLogSetPO();
                alParam.setState(1);
                List<FastActionLogSetPO> list = fastActionLogSetMapper.queryList(alParam);
                for (FastActionLogSetPO item : list) {
                    keywordsSet.add(item.getKeywords());
                }
                RedisUtil.set(StaticVar.ACTION_LOG_CACHE, JsonUtil.toString(keywordsSet), 60 * 5);
            } else {
                // 使用cache
                keywordsSet.clear();
                keywordsSet.addAll(JsonUtil.toJavaObject(cache, HashSet.class));
            }
            count = 10;
        }
        count--;

        return keywordsSet.contains(keywords);
    }
}
