/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.mapper.common.FastFileExportMapper;
import com.fast.po.common.FastFileExportPO;
import com.fast.service.base.BaseService;
import com.fast.service.oss.OssService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastFileExportService extends BaseService {

    @Autowired
    private FastFileExportMapper fastFileExportMapper;
    @Autowired
    private OssService ossService;

    /**
     * 通过id查询单个对象
     */
    public FastFileExportPO queryById(FastFileExportPO item) {
        return fastFileExportMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastFileExportPO queryById(Integer id) {
        FastFileExportPO itemParam = new FastFileExportPO();
        itemParam.setId(id);
        return fastFileExportMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastFileExportPO queryOne(FastFileExportPO item) {
        return fastFileExportMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastFileExportPO> queryList(FastFileExportPO item) {
        return fastFileExportMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastFileExportPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastFileExportPO> list = fastFileExportMapper.queryList(item);
        for (FastFileExportPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (notBlank(cur.getUrl())) {
                cur.setUrl(ossService.getWholeUrl(false, cur.getUrl()));
            }
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastFileExportPO item) {
        return fastFileExportMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastFileExportPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastFileExportMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastFileExportPO> list) {
        if (fastFileExportMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastFileExportPO item) {
        if (fastFileExportMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateState(FastFileExportPO item) {
        fastFileExportMapper.updateState(item);
        return MethodVO.success();
    }
}
