/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.enums.OperationLogEnum;
import com.fast.mapper.common.FastOperationLogMapper;
import com.fast.po.common.FastLogOperationPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 操作日志
 *
 * <AUTHOR>
 */
@Service
public class FastOperationLogService extends BaseService {

    @Autowired
    private FastOperationLogMapper fastOperationLogMapper;

    /**
     * 添加操作日志
     *
     * @param userId
     * @param remark
     */
    public void log(Integer userId, Integer type, String remark) {
        FastLogOperationPO logPO = new FastLogOperationPO();
        logPO.setType(type);
        logPO.setUserId(userId);
        logPO.setRemark(remark);
        logPO.setCreateTime(DateUtil.getNowDate());
        fastOperationLogMapper.insertSelective(logPO);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLogOperationPO queryById(FastLogOperationPO item) {
        return fastOperationLogMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastLogOperationPO queryById(Integer id) {
        return fastOperationLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastLogOperationPO queryOne(FastLogOperationPO item) {
        return fastOperationLogMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastLogOperationPO> queryList(FastLogOperationPO item) {
        return fastOperationLogMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastLogOperationPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastLogOperationPO> list = fastOperationLogMapper.queryList(item);
        for (FastLogOperationPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastLogOperationPO item) {
        return fastOperationLogMapper.queryCount(item);
    }

    /**
     * 新增操作日志
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastLogOperationPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastOperationLogMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 操作日志统一入口
     */
    public void insertLog(FastLogOperationPO item) {
        for (OperationLogEnum ole : OperationLogEnum.values()) {
            if (item.getType() == ole.index) {
                log.info("保存操作日志：{}", ole.name);
            }
        }
        fastOperationLogMapper.insertSelective(item);
    }

    public static void main(String[] args) {

    }
}
