/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.common.FastTagMapper;
import com.fast.mapper.common.FastTagTypeMapper;
import com.fast.po.common.FastTagPO;
import com.fast.po.common.FastTagTypePO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastTagTypeService extends BaseService {

    @Autowired
    private FastTagTypeMapper fastTagTypeMapper;
    @Autowired
    private FastTagMapper fastTagMapper;
    @Autowired
    private FastTagService fastTagService;

    /**
     * 通过id查询单个对象
     */
    public FastTagTypePO queryById(FastTagTypePO item) {
        return fastTagTypeMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastTagTypePO queryById(Integer id) {
        FastTagTypePO itemParam = new FastTagTypePO();
        itemParam.setId(id);
        return fastTagTypeMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastTagTypePO queryOne(FastTagTypePO item) {
        return fastTagTypeMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastTagTypePO> queryList(FastTagTypePO item) {
        return fastTagTypeMapper.queryList(item);
    }

    /**
     * 查询全部
     */
    public List<Integer> queryIdList(FastTagTypePO item) {
        return fastTagTypeMapper.queryIdList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastTagTypePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastTagTypePO> list = fastTagTypeMapper.queryList(item);
        for (FastTagTypePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> getTreeList(FastTagTypePO item) {
        // 查询一级类别
        item.setPid(0);
        List<FastTagTypePO> pList = fastTagTypeMapper.queryList(item);
        if (CollUtil.isEmpty(pList)) {
            return ResultVO.success();
        }
        Set<Integer> pids = new HashSet<>();
        for (FastTagTypePO cur : pList) {
            cur.setEncryptionId(encode(cur.getId()));
            pids.add(cur.getId());
        }
        // 查询子级类别
        item.setPid(null);
        item.setPids(StrUtil.join(pids));
        List<FastTagTypePO> cList = fastTagTypeMapper.queryList(item);
        if (CollUtil.hasContent(cList)) {
            // 是否加载tag作为子级
            if (Objects.equals(item.getLoadTagAsType(), 1)) {
                List<Integer> ttids = cList.stream().map(FastTagTypePO::getId).collect(Collectors.toList());
                Map<Integer, List<FastTagPO>> listMap = fastTagService.queryByTagTypeIds(ttids);
                for (FastTagTypePO cur : cList) {
                    List<FastTagPO> pos = listMap.get(cur.getId());
                    if (CollUtil.isEmpty(pos)) {
                        continue;
                    }
                    List<FastTagTypePO> childs = new ArrayList<>();
                    pos.forEach(tag -> {
                        FastTagTypePO typePO = new FastTagTypePO();
                        typePO.setId(tag.getId());
                        typePO.setPid(cur.getId());
                        typePO.setTypeName(tag.getTagName());
                        typePO.setEncryptionId(encode(tag.getId()));
                        childs.add(typePO);
                    });
                    cur.setChildList(childs);
                }
            }
            Map<Integer, List<FastTagTypePO>> cMap = new HashMap<>();
            for (FastTagTypePO cur : cList) {
                List<FastTagTypePO> childList = cMap.get(cur.getPid());
                if (childList == null) {
                    childList = new ArrayList<>();
                }
                cur.setEncryptionId(encode(cur.getId()));
                childList.add(cur);
                cMap.put(cur.getPid(), childList);
            }
            for (FastTagTypePO cur : pList) {
                cur.setChildList(cMap.get(cur.getId()));
                int tagCount = cur.getTagCount();
                if (CollUtil.hasContent(cur.getChildList())) {
                    for (FastTagTypePO po : cur.getChildList()) {
                        tagCount += po.getTagCount();
                    }
                }
                cur.setTagCount(tagCount);
            }
        }
        Map<String, Object> results = new HashMap<>();
        results.put("list", pList);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastTagTypePO item) {
        return fastTagTypeMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastTagTypePO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        String[] typeNames = item.getTypeNames();
        for (String typeName : typeNames) {
            if (isBlank(typeName)) {
                return MethodVO.error("typeName必填");
            }
            if (typeName.length() > 4) {
                return MethodVO.error("typeName不能超过4字符");
            }
            FastTagTypePO insert = new FastTagTypePO();
            BeanUtils.copyProperties(item, insert);
            insert.setTypeName(typeName);
            insert.setDelFlag(StaticVar.NO);
            insert.setTagCount(0);

            fastTagTypeMapper.insertSelective(insert);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastTagTypePO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastTagTypeMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCount(Integer tagTypeId) {
        FastTagTypePO item = new FastTagTypePO();
        FastTagPO fastTagPO = new FastTagPO();
        fastTagPO.setTagTypeId(tagTypeId);
        fastTagPO.setDelFlag(StaticVar.NO);
        int count = fastTagMapper.queryCount(fastTagPO);
        item.setUpdateTime(DateUtil.getNowDate());
        item.setId(tagTypeId);
        item.setTagCount(count);
        if (fastTagTypeMapper.updateById(item) == 0) {
            transactionRollBack();
            throw new MyException(StaticStr.UPDATE_FAILED);
        }
    }

    /**
     * 删除
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateDel(FastTagTypePO item) {
        FastTagTypePO del = new FastTagTypePO();
        del.setUpdatorId(item.getUpdatorId());
        del.setUpdateTime(DateUtil.getNowDate());
        del.setId(item.getId());
        del.setDelFlag(StaticVar.YES);
        if (fastTagTypeMapper.updateById(del) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
