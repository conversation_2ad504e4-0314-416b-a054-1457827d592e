/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.mapper.common.FastDataPosMapper;
import com.fast.po.common.FastDataPosPO;
import com.fast.service.base.BaseService;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastDataPosService extends BaseService {

    @Autowired
    private FastDataPosMapper dataPosMapper;

    /**
     * 通过id查询单个对象
     */
    public FastDataPosPO queryById(FastDataPosPO item) {
        return dataPosMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastDataPosPO queryById(Integer id) {
        return dataPosMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastDataPosPO queryOne(FastDataPosPO item) {
        return dataPosMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastDataPosPO> queryList(FastDataPosPO item) {
        return dataPosMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastDataPosPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastDataPosPO> list = dataPosMapper.queryList(item);
        for (FastDataPosPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastDataPosPO item) {
        return dataPosMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastDataPosPO item) {
        if (dataPosMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastDataPosPO> list) {
        if (dataPosMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastDataPosPO item) {
        if (dataPosMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
