/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticVar;
import com.fast.utils.DateUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.SessionVO;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 在线用户
 *
 * <AUTHOR>
 */
@Service
public class FastOnlineService {

    /**
     * 添加在线用户
     */
    @Async
    public void addOnline(SessionVO sessionVO) {
        if (sessionVO == null || sessionVO.getMemberId() == null || sessionVO.getMemberId() <= 0) {
            return;
        }
        RedisUtil.zadd(StaticVar.ONLINE_MEMBER_KEY, (double) DateUtil.getNowTimeStampShort(), sessionVO.getMemberId().toString());
    }

    /**
     * 清理旧的在线数据
     */
    public void autoRemoveOnline() {
        long now = DateUtil.getNowTimeStampShort();
        RedisUtil.zremrangeByScore(StaticVar.ONLINE_MEMBER_KEY, 0, (double) (now - (StaticVar.ONLINE_TIME * 60)));
    }

}
