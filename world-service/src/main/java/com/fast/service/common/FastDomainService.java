/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.mapper.common.FastDomainMapper;
import com.fast.mapper.mini.FastMiniMapper;
import com.fast.po.common.FastDomainPO;
import com.fast.po.mini.FastMiniPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastDomainService extends BaseService {

    @Autowired
    private FastDomainMapper fastDomainMapper;
    @Autowired
    private FastMiniMapper fastMiniMapper;

    /**
     * 通过id查询单个对象
     */
    public FastDomainPO queryById(FastDomainPO item) {
        return fastDomainMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastDomainPO queryById(Integer id) {
        return fastDomainMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastDomainPO queryOne(FastDomainPO item) {
        return fastDomainMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastDomainPO> queryList(FastDomainPO item) {
        return fastDomainMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastDomainPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastDomainPO> list = fastDomainMapper.queryList(item);
        for (FastDomainPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (cur.getDomainType() == 1) {
                FastMiniPO count = new FastMiniPO();
                count.setDomainId(cur.getId());
                if (fastMiniMapper.queryCount(count) > 0) {
                    cur.setState(1);
                } else {
                    cur.setState(2);
                }
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastDomainPO item) {
        return fastDomainMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastDomainPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastDomainMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastDomainPO> list) {
        if (fastDomainMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastDomainPO params) {
        FastMiniPO miniQ = new FastMiniPO();
        miniQ.setDomainId(params.getId());
        if (fastMiniMapper.queryCount(miniQ) > 0) {
            throw new MyException("正在使用的域名无法操作");
        }
        if (params.getRetailId() != null) {
            params.setState(1);

            FastDomainPO itemQ = new FastDomainPO();
            itemQ.setRetailId(params.getRetailId());
            itemQ.setDomainType(1);
            itemQ.setNotId(params.getId());
            itemQ.setDelFlag(StaticVar.NO);
            if (fastDomainMapper.queryCount(itemQ) > 0) {
                throw new MyException("该分销商已经分配了域名");
            }
            FastDomainPO query = new FastDomainPO();
            query.setId(params.getId());
            FastDomainPO domainPO = fastDomainMapper.queryOne(query);
            if (domainPO != null && domainPO.getMiniId() != null) {
                FastMiniPO update = new FastMiniPO();
                update.setAppId(domainPO.getDomainName());
                update.setPrincipalName(params.getPrincipalName());
                fastMiniMapper.updateByAppId(update);
            }
        }
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastDomainMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
