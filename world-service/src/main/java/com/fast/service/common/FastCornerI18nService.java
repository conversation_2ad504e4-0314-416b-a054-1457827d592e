/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.mapper.common.FastCornerI18nMapper;
import com.fast.po.common.FastCornerI18nPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastCornerI18nService extends BaseService {

    @Autowired
    private FastCornerI18nMapper fastCornerI18nMapper;

    /**
     * 通过id查询单个对象
     */
    public FastCornerI18nPO queryById(FastCornerI18nPO params) {
        return fastCornerI18nMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastCornerI18nPO queryById(Integer id) {
        return fastCornerI18nMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastCornerI18nPO queryOne(FastCornerI18nPO params) {
        return fastCornerI18nMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastCornerI18nPO> queryList(FastCornerI18nPO params) {
        return fastCornerI18nMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastCornerI18nPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastCornerI18nPO> list = fastCornerI18nMapper.queryList(params);
        for (FastCornerI18nPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastCornerI18nPO params) {
        return fastCornerI18nMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastCornerI18nPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastCornerI18nMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastCornerI18nPO> list) {
        if (fastCornerI18nMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastCornerI18nPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastCornerI18nMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public Map<String, FastCornerI18nPO> getMapByIdForLang(Integer id) {
        if (id == null) {
            return null;
        }
        FastCornerI18nPO po = new FastCornerI18nPO();
        po.setCornerId(id);
        List<FastCornerI18nPO> list = queryList(po);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().collect(Collectors.toMap(FastCornerI18nPO::getLanguageCode, fastCornerI18nPO -> fastCornerI18nPO, (a, b) -> b));
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByCornerId(Integer cornerId) {
        if (cornerId == null) {
            return 0;
        }
        return fastCornerI18nMapper.deleteByCornerId(cornerId);
    }
}
