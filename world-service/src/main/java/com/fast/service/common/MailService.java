package com.fast.service.common;

import com.fast.constant.StaticVar;
import com.fast.framework.exception.MyException;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;

@Service
public class MailService {

    protected Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private JavaMailSender javaMailSender;

    @Autowired
    private FastCommonDictService fastCommonDictService;

    @Value("${spring.mail.username}")
    private String mainUserName;

    @Value("${spring.mail.nickname}")
    private String nickname;

    /**
     * 发送邮件，邮件内容是文本
     *
     * @param emails    email地址，多个以英文逗号分隔
     * @param ccEmails  抄送人email地址
     * @param bccEmails 隐秘抄送人email地址
     * @param subject   邮件主题（标题）
     * @param text      邮件正文（纯文本）
     */
    public void sendTextMail(String emails, String ccEmails, String bccEmails, String subject, String text) {
        try {
            // 构建一个邮件对象
            SimpleMailMessage message = new SimpleMailMessage();
            // 设置邮件主题
            message.setSubject(subject);
            // 设置邮件发送者，昵称+<邮箱地址>
            message.setFrom(nickname + '<' + mainUserName + '>');
            // 设置邮件接收者，可以有多个接收者，多个接受者参数需要数组形式
            message.setTo(emails.split(","));
            // 设置邮件抄送人，可以有多个抄送人
            if (StrUtil.isNotBlank(ccEmails)) {
                message.setCc(ccEmails.split(","));
            }
            // 设置隐秘抄送人，可以有多个
            if (StrUtil.isNotBlank(bccEmails)) {
                message.setBcc(bccEmails.split(","));
            }
            // 设置邮件发送日期
            message.setSentDate(DateUtil.getNowDate());
            // 设置邮件的正文
            message.setText(text);
            // 发送邮件
            javaMailSender.send(message);
        } catch (Exception e) {
            log.error("发送邮件失败: {}", ExceptionUtils.getStackTrace(e));
            throw new MyException("邮件发送失败");
        }
    }

    public void sendTextMail(String email, String subject, String text) {
        sendTextMail(email, "", "", subject, text);
    }

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱
     */
    public void sendVerifyCodeMail(String email) {
        String limitKey = StaticVar.EMAIL_VERIFY_LIMIT_PREFIX + email;
        String isLimit = RedisUtil.get(limitKey);
        if (Objects.equals("1", isLimit)) {
            throw new MyException("发送过于频繁，请稍后再试");
        }
        Map<String, String> map = fastCommonDictService.queryValueMapByCode("verify_code_email_tpl");
        if (MapUtils.isEmpty(map)) {
            throw new MyException("验证码邮件模板不存在");
        }

        String title = map.get("title");
        String content = map.get("content");
        String ttl = map.get("ttl");
        String limitTtl = map.get("limit_ttl");
        String codeLength = map.get("code_length");

        String verifyCode = generateVerifyCode(Integer.parseInt(codeLength));
        sendTextMail(email, title, content.replace("{vcode}", verifyCode).replace("{time}", ttl));

        RedisUtil.set(StaticVar.EMAIL_VERIFY_CODE_PREFIX + email, verifyCode, Integer.parseInt(ttl) * 60);
        RedisUtil.set(limitKey, "1", Integer.parseInt(limitTtl) * 60);

        log.info("邮件发送成功，emai: {}, code: {}", email, verifyCode);
    }

    private String generateVerifyCode(int length) {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < length; i++) {
            builder.append(ThreadLocalRandom.current().nextInt(10));
        }
        return builder.toString();
    }

    /**
     * 校验验证码
     *
     * @param email 邮箱
     * @param code  验证码
     * @return 是否验证通过
     */
    public boolean checkVerifyCode(String email, String code) {
        String key = StaticVar.EMAIL_VERIFY_CODE_PREFIX + email;
        String savedCode = RedisUtil.get(key);
        if (savedCode != null && savedCode.equals(code)) {
            RedisUtil.del(key);
            return true;
        }
        return false;
    }

}
