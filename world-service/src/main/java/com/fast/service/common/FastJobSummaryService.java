/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.mapper.common.FastJobSummaryMapper;
import com.fast.po.common.FastJobSummaryPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastJobSummaryService extends BaseService {

    @Autowired
    private FastJobSummaryMapper fastJobSummaryMapper;

    /**
     * 通过id查询单个对象
     */
    public FastJobSummaryPO queryById(FastJobSummaryPO params) {
        return fastJobSummaryMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastJobSummaryPO queryById(Integer id) {
        return fastJobSummaryMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastJobSummaryPO queryOne(FastJobSummaryPO params) {
        return fastJobSummaryMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastJobSummaryPO> queryList(FastJobSummaryPO params) {
        return fastJobSummaryMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastJobSummaryPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastJobSummaryPO> list = fastJobSummaryMapper.queryList(params);
        for (FastJobSummaryPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastJobSummaryPO params) {
        return fastJobSummaryMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastJobSummaryPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastJobSummaryMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastJobSummaryPO> list) {
        if (fastJobSummaryMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastJobSummaryPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastJobSummaryMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
