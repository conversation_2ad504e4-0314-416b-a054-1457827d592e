/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.framework.exception.MyException;
import com.fast.mapper.common.FastCustomFieldMapper;
import com.fast.po.common.FastCustomFieldPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastCustomFieldService extends BaseService {

    @Autowired
    private FastCustomFieldMapper customFieldMapper;

    /**
     * 通过id查询单个对象
     */
    public FastCustomFieldPO queryById(FastCustomFieldPO item) {
        return customFieldMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastCustomFieldPO queryById(Integer id) {
        return customFieldMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastCustomFieldPO queryOne(FastCustomFieldPO item) {
        return customFieldMapper.queryOne(item);
    }

    // 每个模块的字段长度,方便初始化
    private static final int[] MODULAR = {10, 9, 10, 11, 10, 5, 11, 12, 11, 11, 11, 10, 7};

    private static final List<Integer> MODULAR7 = Lists.newArrayList(1, 3, 9, 10, 11);

    /**
     * 查询用户自己配置的字段
     */
    public List<FastCustomFieldPO> queryCustomFieldList(FastCustomFieldPO params) {
        List<FastCustomFieldPO> list = customFieldMapper.queryList(params);
        Date nowTime = DateUtil.getNowDate();
        if (CollUtil.isEmpty(list)) {
            int fieldCount = MODULAR[params.getModular() - 1];
            for (int i = 1; i <= fieldCount; i++) {
                FastCustomFieldPO po = new FastCustomFieldPO();
                po.setCreateTime(nowTime);
                po.setCreatorId(params.getUserId());
                po.setUserId(params.getUserId());
                po.setRetailId(params.getRetailId());
                po.setModular(params.getModular());
                po.setFieldType(i);
                po.setSequence(i);

                // 默认值
                if (params.getModular() == 7 && !MODULAR7.contains(params.getFieldType())) {
                    params.setState(0);
                }

                customFieldMapper.insertSelective(po);
            }
            list = customFieldMapper.queryList(params);
        }
        return list;
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastCustomFieldPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastCustomFieldPO> list = customFieldMapper.queryList(item);
        for (FastCustomFieldPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastCustomFieldPO item) {
        return customFieldMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO save(FastCustomFieldPO params) {
        customFieldMapper.delete(params);
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        List<FastCustomFieldPO> list = params.getList();
        if (list.size() != MODULAR[list.get(0).getModular() - 1]) {
            throw new MyException("字段个数不合法");
        }
        int i = 1;
        for (FastCustomFieldPO po : list) {
            po.setCreateTime(nowTime);
            po.setCreatorId(params.getUserId());
            po.setUserId(params.getUserId());
            po.setRetailId(params.getRetailId());
            po.setSequence(i);
            customFieldMapper.insertSelective(po);
            i++;
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastCustomFieldPO> list) {
        if (customFieldMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }
}
