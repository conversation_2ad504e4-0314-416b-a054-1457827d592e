/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.framework.exception.MyException;
import com.fast.mapper.common.FastJobLogMapper;
import com.fast.mapper.common.FastJobSummaryMapper;
import com.fast.po.common.FastJobLogPO;
import com.fast.po.common.FastJobSummaryPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
public class FastJobLogService extends BaseService {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private FastJobLogMapper fastJobLogMapper;
    @Autowired
    private FastJobSummaryMapper fastJobSummaryMapper;

    /**
     * 任务开始
     */
    public FastJobLogPO jobStartLog(String jobCode, String jobName, String cron) {
        try {
            logger.info(jobName + "_开始");
            FastJobLogPO jobPO = new FastJobLogPO();
            jobPO.setJobCode(jobCode);
            jobPO.setJobName(jobName);
            jobPO.setStartTime(DateUtil.getNowDate());
            jobPO.setState(1);
            fastJobLogMapper.insertSelective(jobPO);
            updateLogSummary(jobPO, cron);
            return jobPO;
        } catch (Exception e) {
            return null;
        }
    }

    @Async
    public void jobEndLog(FastJobLogPO jobPO) {
        if (jobPO == null) {
            return;
        }
        try {
            logger.info(jobPO.getJobName() + "_结束");
            Date nowTime = DateUtil.getNowDate();
            jobPO.setEndTime(nowTime);
            jobPO.setState(2); // 正常结束
            fastJobLogMapper.updateById(jobPO);
            updateLogSummary(jobPO, null);
        } catch (Exception e) {
            // 异常
        }
    }

    @Async
    public void jobErrorLog(FastJobLogPO jobPO, Exception exception) {
        MyException.print(logger, exception);
        if (jobPO == null) {
            return;
        }
        try {
            logger.info(jobPO.getJobName() + "_异常");
            Date nowTime = DateUtil.getNowDate();
            Long timeCost = nowTime.getTime() - jobPO.getStartTime().getTime();
            jobPO.setEndTime(nowTime);
            jobPO.setTimeCost(timeCost.intValue());
            jobPO.setState(3); // 异常结束
            String mark = getStrFromException(exception);
            if (mark.length() > 4000) {
                mark = mark.substring(0, 4000);
            }
            jobPO.setMark(mark);
            fastJobLogMapper.updateById(jobPO);
            updateLogSummary(jobPO, null);
        } catch (Exception e) {
            // 异常
        }
    }

    /**
     * 更新缓存
     */
    private void updateLogSummary(FastJobLogPO jobCurrent, String cron) {

        FastJobSummaryPO jsParam = new FastJobSummaryPO();
        jsParam.setJobCode(jobCurrent.getJobCode());
        FastJobSummaryPO jsPO = fastJobSummaryMapper.queryOne(jsParam);

        if (jsPO == null) {
            // job列表不存在，新增一个
            jsPO = new FastJobSummaryPO();
            jsPO.setJobName(jobCurrent.getJobName());
            jsPO.setJobCode(jobCurrent.getJobCode());
            jsPO.setCurrentState(jobCurrent.getState());
            jsPO.setCurrentTimeCost(jobCurrent.getTimeCost());
            jsPO.setHisState(jobCurrent.getState());
            jsPO.setHisTimeCost(jobCurrent.getTimeCost());
            jsPO.setStartTimes(0);// 启动次数
            jsPO.setSuccessTimes(0);// 成功次数
            jsPO.setStartTime(DateUtil.getNowDate());
            if (jobCurrent.getState() != 1) {
                jsPO.setStartTimes(1);
            }
        }
        if (StrUtil.isNotEmpty(cron)) {
            jsPO.setCron(cron);
        }
        // 历史状态
        if (jobCurrent.getState() == 1) {
            jsPO.setStartTimes(jsPO.getStartTimes() + 1);
            if (jsPO.getLastRunTime() != null) {
                Long runStepTimeLong = DateUtil.getNowDate().getTime() - jsPO.getLastRunTime().getTime();
                runStepTimeLong = runStepTimeLong / 1000;
                jsPO.setRunStepTime(runStepTimeLong.intValue());
            }
            jsPO.setLastRunTime(DateUtil.getNowDate());
        }
        if (jobCurrent.getState() == 2) {
            // 执行成功
            jsPO.setSuccessTimes(jsPO.getSuccessTimes() + 1);
            jsPO.setCurrentTimeCost(jobCurrent.getTimeCost());
            Integer hisTimeCost = jsPO.getHisTimeCost();
            hisTimeCost = hisTimeCost == null ? 0 : hisTimeCost;
            if (hisTimeCost < jobCurrent.getTimeCost()) {
                jsPO.setHisTimeCost(jobCurrent.getTimeCost());
            }
        }
        if (jobCurrent.getState() == 3) {
            // 执行异常
            jsPO.setMark(jobCurrent.getMark());
        }
        jsPO.setCurrentState(jobCurrent.getState());
        if (jsPO.getHisState() < jobCurrent.getState()) {
            jsPO.setHisState(jobCurrent.getState());
        }
        if (jsPO.getId() != null) {
            // 更新
            fastJobSummaryMapper.updateById(jsPO);
        } else {
            // 新增
            fastJobSummaryMapper.insertSelective(jsPO);
        }

    }

    private static String getStrFromException(Exception e) {
        StackTraceElement[] stArray = e.getStackTrace();
        StringBuffer exBuffer = new StringBuffer();
        exBuffer.append(e.getClass().getName());
        exBuffer.append("\n");
        for (StackTraceElement te : stArray) {
            exBuffer.append(te.getClassName());
            exBuffer.append(",method=");
            exBuffer.append(te.getMethodName());
            exBuffer.append(",line=");
            exBuffer.append(te.getLineNumber());
            exBuffer.append("\n");
        }
        return exBuffer.toString();
    }

}
