/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.mapper.common.FastTagI18nMapper;
import com.fast.po.common.FastTagI18nPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastTagI18nService extends BaseService {

    @Autowired
    private FastTagI18nMapper fastTagI18nMapper;

    /**
     * 通过id查询单个对象
     */
    public FastTagI18nPO queryById(FastTagI18nPO params) {
        return fastTagI18nMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastTagI18nPO queryById(Integer id) {
        return fastTagI18nMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastTagI18nPO queryOne(FastTagI18nPO params) {
        return fastTagI18nMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastTagI18nPO> queryList(FastTagI18nPO params) {
        return fastTagI18nMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastTagI18nPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastTagI18nPO> list = fastTagI18nMapper.queryList(params);
        for (FastTagI18nPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastTagI18nPO params) {
        return fastTagI18nMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastTagI18nPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastTagI18nMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastTagI18nPO> list) {
        if (fastTagI18nMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastTagI18nPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastTagI18nMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public Map<String, FastTagI18nPO> getMapByIdForLang(Integer id) {
        if (id == null) {
            return null;
        }
        FastTagI18nPO po = new FastTagI18nPO();
        po.setTagId(id);
        List<FastTagI18nPO> list = queryList(po);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().collect(Collectors.toMap(FastTagI18nPO::getLanguageCode, fastTagI18nPO -> fastTagI18nPO, (a, b) -> b));
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteByTagId(Integer tagId) {
        if (tagId == null) {
            return 0;
        }
        return fastTagI18nMapper.deleteByTagId(tagId);
    }
}
