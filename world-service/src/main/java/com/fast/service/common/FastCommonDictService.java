/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.mapper.common.FastCommonDictMapper;
import com.fast.po.common.FastCommonDictPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FastCommonDictService extends BaseService {

    @Autowired
    private FastCommonDictMapper fastCommonDictMapper;

    /**
     * 通过id查询单个对象
     */
    public FastCommonDictPO queryById(FastCommonDictPO params) {
        return fastCommonDictMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastCommonDictPO queryById(Integer id) {
        return fastCommonDictMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastCommonDictPO queryOne(FastCommonDictPO params) {
        return fastCommonDictMapper.queryOne(params);
    }

    public Map<String, String> queryValueMapByCode(String code) {
        List<FastCommonDictPO> dicts = queryByCode(code);
        if (CollUtil.isEmpty(dicts)) {
            return null;
        }
        return dicts.stream().collect(Collectors.toMap(FastCommonDictPO::getKey, FastCommonDictPO::getValue, (a, b) -> b));
    }

    public List<FastCommonDictPO> queryByCode(String code) {
        if (StrUtil.isBlank(code)) {
            return null;
        }
        FastCommonDictPO params = new FastCommonDictPO();
        params.setCode(code);
        return queryList(params);
    }

    public FastCommonDictPO queryByCodeAndKey(String code, String key) {
        FastCommonDictPO params = new FastCommonDictPO();
        params.setCode(code);
        params.setKey(key);
        return queryOne(params);
    }

    public String queryValueByCodeAndKey(String code, String key) {
        FastCommonDictPO fastCommonDictPO = queryByCodeAndKey(code, key);
        return fastCommonDictPO == null ? null : fastCommonDictPO.getValue();
    }

    /**
     * 查询全部
     */
    public List<FastCommonDictPO> queryList(FastCommonDictPO params) {
        return fastCommonDictMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastCommonDictPO params) {
        List<FastCommonDictPO> list = fastCommonDictMapper.queryList(params);
        for (FastCommonDictPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(list);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastCommonDictPO params) {
        return fastCommonDictMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastCommonDictPO params) {
        if ("third_platform".equals(params.getCode())) {
            FastCommonDictPO countQuery = new FastCommonDictPO();
            countQuery.setCode("third_platform");
            countQuery.setValue(params.getValue());
            if (fastCommonDictMapper.queryCount(countQuery) > 0) {
                return MethodVO.error("三方分发平台已存在!");
            }
        }
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastCommonDictMapper.insertSelective(params) == 0) {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastCommonDictPO> list) {
        if (fastCommonDictMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastCommonDictPO params) {
        if (fastCommonDictMapper.updateById(params) == 0) {
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
