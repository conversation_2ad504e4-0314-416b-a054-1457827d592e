/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.common.FastVideoMapper;
import com.fast.po.common.FastVideoPO;
import com.fast.service.aliyun.AliCdnService;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.VideoUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastVideoService extends BaseService {

    @Autowired
    private FastVideoMapper fastVideoMapper;
    @Autowired
    private AliCdnService aliCdnService;

    /**
     * 查询缓存信息
     */
    public FastVideoPO queryInfoByRedis(FastVideoPO item) {
        if (item.getId() == null) {
            return null;
        }
        item.setDelFlag(StaticVar.NO);
        FastVideoPO po;
        String key = StaticVar.VIDEO_INFO_ID + item.getId();
        String value = RedisUtil.get(key);
        if (notEmpty(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            po = JsonUtil.toJavaObject(value, FastVideoPO.class);
        } else {
            item.setId(item.getId());
            po = fastVideoMapper.queryOne(item);
            if (po == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_10D);
                return null;
            } else {
                po.setEncryptionId(encode(po.getId()));
                RedisUtil.set(key, JsonUtil.toString(po), RedisUtil.TIME_10D);
            }
        }
        return po;
    }

    /**
     * 查询缓存信息
     */
    public FastVideoPO queryInfoByRedis(Integer id) {
        FastVideoPO itemParam = new FastVideoPO();
        itemParam.setId(id);
        return queryInfoByRedis(itemParam);
    }

    /**
     * 查询缓存信息
     */
    public List<FastVideoPO> queryInfoByRedis(String ids) {
        List<FastVideoPO> result = new ArrayList<>();
        if (notEmpty(ids)) {
            FastVideoPO itemParam = new FastVideoPO();
            List<Integer> idList = CollUtil.parseIntStr2List(ids);
            for (Integer id : idList) {
                itemParam.setId(id);
                FastVideoPO po = queryInfoByRedis(itemParam);
                if (po != null) {
                    result.add(po);
                }
                if (po != null && notEmpty(po.getVideoUrl())) {
//                    po.setVideoUrl(cdnConfig.getHost() + po.getVideoUrl());
                    po.setVideoUrl(aliCdnService.getVideoFullUrl(po.getVideoUrl(), 0, 0));
                }
            }
        }
        return result;
    }

    /**
     * 通过id查询单个对象
     */
    public FastVideoPO queryById(FastVideoPO item) {
        FastVideoPO po = fastVideoMapper.queryById(item);
        if (po != null && notEmpty(po.getVideoUrl())) {
            po.setVideoUrl(aliCdnService.getVideoFullUrl(po.getVideoUrl(), 0, 0));
        }
        return po;
    }

    /**
     * 通过id查询单个对象
     */
    public FastVideoPO queryById(Integer id) {
        return fastVideoMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastVideoPO queryOne(FastVideoPO item) {
        return fastVideoMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastVideoPO> queryList(FastVideoPO item) {
        return fastVideoMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastVideoPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastVideoPO> list = fastVideoMapper.queryList(item);
        for (FastVideoPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (notEmpty(cur.getVideoUrl())) {
                cur.setVideoUrl(aliCdnService.getVideoFullUrl(cur.getVideoUrl(), 0, 0));
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastVideoPO item) {
        return fastVideoMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastVideoPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (notEmpty(params.getVideoUrl())) {
            params.setVideoUrl(aliCdnService.replaceHost(params.getVideoUrl()));
        }
        int duration = VideoUtil.getM3u8Duration(aliCdnService.getVideoFullUrl(params.getVideoUrl(), 0, 0));
        if (duration <= 0) {
            return MethodVO.error("获取视频失败,请检查视频链接是否正确:" + params.getVideoUrl());
        }
        if (fastVideoMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastVideoPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (notEmpty(params.getVideoUrl())) {
            params.setVideoUrl(aliCdnService.replaceHost(params.getVideoUrl()));

            int duration = VideoUtil.getM3u8Duration(aliCdnService.getVideoFullUrl(params.getVideoUrl(), 0, 0));
            if (duration <= 0) {
                return MethodVO.error("获取视频失败,请检查视频链接是否正确:" + params.getVideoUrl());
            }
        }
        if (fastVideoMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
