/*
 * Powered By fast.up
 */
package com.fast.service.common;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.common.FastTextMapper;
import com.fast.po.common.FastTextPO;
import com.fast.po.mini.FastMiniSettingPO;
import com.fast.service.base.BaseService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.mini.FastMiniSettingService;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.mini.FastMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastTextService extends BaseService {

    @Autowired
    private FastTextMapper fastTextMapper;
    @Autowired
    private FastMiniService fastMiniService;
    @Autowired
    private FastMiniSettingService fastMiniSettingService;

    /**
     * 通过id查询单个对象
     */
    public FastTextPO queryById(FastTextPO item) {
        return fastTextMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastTextPO queryById(Integer id) {
        return fastTextMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastTextPO queryOne(FastTextPO item) {
        return fastTextMapper.queryOne(item);
    }

    public FastTextPO queryOneRedis(SessionVO sessionVO, FastTextPO item) {
        String key = StaticVar.TEXT_CODE + item.getCode();
        String res = RedisUtil.get(key);
        if (StrUtil.isEmpty(res)) {
            FastTextPO textPO = fastTextMapper.queryOne(item);
            if (textPO == null) {
                textPO = new FastTextPO();
                textPO.setContent("联系管理员配置");
            }
            res = JsonUtil.toString(textPO);
            RedisUtil.set(key, res, 60 * 30);
        }
        FastTextPO itemPO = JsonUtil.toJavaObject(res, FastTextPO.class);
        if (sessionVO == null) {
            return itemPO;
        }
        // 替换小程序名称{miniName}
        FastMiniVO miniVO = fastMiniService.queryInfoByRedis(sessionVO.getMiniId());
        // 替换字符
        String content = itemPO.getContent();
        if (miniVO != null) {
            content = content.replaceAll("\\{miniName\\}", miniVO.getMiniName());
        }

        if (Arrays.asList("recharge", "quick_recharge").contains(itemPO.getCode())) {
            // 用户充值协议
            FastMiniSettingPO msPO = fastMiniSettingService.getMiniSettingRedis(sessionVO.getMiniId(), item.getContVersionId());
            if (msPO != null) {
                content = content.replaceAll("\\{coinName\\}", msPO.getCoinName());
            }
        } else if ((Arrays.asList("member_service", "quick_agreement", "quick_privacy_policy", "quick_member_service", "quick_renewal", "quick_child_agreement")
                .contains(itemPO.getCode()))) {
            // 会员服务协议
            if (miniVO != null) {
                content = content.replaceAll("\\{principalName\\}", miniVO.getPrincipalName());
            }
        }
        itemPO.setContent(content);
        return itemPO;
    }

    public String queryOneHtmlRedis(FastTextPO item) {
        FastTextPO textPO = queryOneRedis(null, item);
        //        String jquery = RedisUtil.get(StaticVar.JS_JQUERY);
//        String apidoc = RedisUtil.get(StaticVar.JS_API);

        String html = "<html>" +
                "<head>" +
                "<title>" +
                textPO.getRemark() +
                "</title>" +
                "<script type='text/javascript'>" +
//        html.append(jquery);
//        html.append(apidoc);
                "</script>" +
                "<style type='text/css'>" +
                "body{padding:60px 30px 80px 30px;line-height:25px;}" +
                "</style>" +
                "</head>" +
                "<body>" +
                textPO.getContent() +
                "</body>" +
                "</html>";
        return html;
    }

    public FastTextPO queryByCode(String code) {
        FastTextPO item = new FastTextPO();
        item.setCode(code);
        return queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastTextPO> queryList(FastTextPO item) {
        return fastTextMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastTextPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastTextPO> list = fastTextMapper.queryList(item);
        for (FastTextPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastTextPO item) {
        return fastTextMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastTextPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastTextMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastTextPO> list) {
        if (fastTextMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastTextPO item) {
        if (fastTextMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
