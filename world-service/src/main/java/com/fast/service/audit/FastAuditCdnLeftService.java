/*
 * Powered By fast.up
 */
package com.fast.service.audit;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.mapper.audit.FastAuditCdnLeftMapper;
import com.fast.po.audit.FastAuditCdnLeftPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.DoubleUtil;
import com.fast.utils.StrUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastAuditCdnLeftService extends BaseService {

    @Autowired
    private FastAuditCdnLeftMapper fastAuditCdnLeftMapper;

    /**
     * 通过id查询单个对象
     */
    public FastAuditCdnLeftPO queryById(FastAuditCdnLeftPO item) {
        return fastAuditCdnLeftMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastAuditCdnLeftPO queryById(Integer id) {
        return fastAuditCdnLeftMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastAuditCdnLeftPO queryOne(FastAuditCdnLeftPO item) {
        return fastAuditCdnLeftMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastAuditCdnLeftPO> queryList(FastAuditCdnLeftPO item) {
        return fastAuditCdnLeftMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastAuditCdnLeftPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastAuditCdnLeftPO> list = fastAuditCdnLeftMapper.queryList(item);
        for (FastAuditCdnLeftPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询剩余cdn
     *
     * @return
     */
    public ResultVO queryCdnLeft(String appId) {
        if (StrUtil.isEmpty(appId)) {
            appId = StaticVar.AUDIT_APP_ID;
        }
        // 查询最后一条记录
        FastAuditCdnLeftPO acParam = new FastAuditCdnLeftPO();
        acParam.setAppId(appId);
        FastAuditCdnLeftPO leftPO = fastAuditCdnLeftMapper.queryLastOne(acParam);
        BigDecimal leftCdnVal = BigDecimal.ZERO;
        if (leftPO != null) {
            leftCdnVal = BigDecimal.valueOf(leftPO.getLeftValue());
        }
        // 转成G
        String leftCdnStr = "";
        BigDecimal kUnit = BigDecimal.valueOf(1000L);
        BigDecimal pUnit = DoubleUtil.mulB(kUnit, kUnit, kUnit, kUnit, kUnit);
        BigDecimal tUnit = DoubleUtil.mulB(kUnit, kUnit, kUnit, kUnit);
        BigDecimal gUnit = DoubleUtil.mulB(kUnit, kUnit, kUnit);
        BigDecimal mUnit = DoubleUtil.mulB(kUnit, kUnit);
        if (leftCdnVal.compareTo(pUnit) > 0) {
            leftCdnVal = DoubleUtil.divB(leftCdnVal, pUnit);
            leftCdnStr = leftCdnVal + "PB";
        } else if (leftCdnVal.compareTo(tUnit) > 0) {
            leftCdnVal = DoubleUtil.divB(leftCdnVal, tUnit);
            leftCdnStr = leftCdnVal + "TB";
        } else if (leftCdnVal.compareTo(gUnit) > 0) {
            leftCdnVal = DoubleUtil.divB(leftCdnVal, gUnit);
            leftCdnStr = leftCdnVal + "GB";
        } else if (leftCdnVal.compareTo(mUnit) > 0) {
            leftCdnVal = DoubleUtil.divB(leftCdnVal, mUnit);
            leftCdnStr = leftCdnVal + "MB";
        } else if (leftCdnVal.compareTo(kUnit) > 0) {
            leftCdnVal = DoubleUtil.divB(leftCdnVal, kUnit);
            leftCdnStr = leftCdnVal + "KB";
        } else {
            leftCdnStr = leftCdnVal + "B";
        }
        Map<String, Object> results = ResultVO.getMap();
        results.put("leftCdn", leftCdnStr);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastAuditCdnLeftPO item) {
        return fastAuditCdnLeftMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastAuditCdnLeftPO item) {
        if (fastAuditCdnLeftMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastAuditCdnLeftPO> list) {
        if (fastAuditCdnLeftMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastAuditCdnLeftPO item) {
        Date nowTime = DateUtil.getNowDate();
        if (fastAuditCdnLeftMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
