/*
 * Powered By fast.up
 */
package com.fast.service.audit;

import com.fast.constant.StaticStr;
import com.fast.mapper.audit.FastAuditMediaMapper;
import com.fast.po.audit.FastAuditMediaPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastAuditMediaService extends BaseService {

    @Autowired
    private FastAuditMediaMapper fastAuditMediaMapper;

    /**
     * 通过id查询单个对象
     */
    public FastAuditMediaPO queryById(FastAuditMediaPO params) {
        return fastAuditMediaMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastAuditMediaPO queryById(Integer id) {
        return fastAuditMediaMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastAuditMediaPO queryOne(FastAuditMediaPO params) {
        return fastAuditMediaMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastAuditMediaPO> queryList(FastAuditMediaPO params) {
        return fastAuditMediaMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastAuditMediaPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastAuditMediaPO> list = fastAuditMediaMapper.queryList(params);
        for (FastAuditMediaPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastAuditMediaPO params) {
        return fastAuditMediaMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastAuditMediaPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastAuditMediaMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastAuditMediaPO> list) {
        if (fastAuditMediaMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastAuditMediaPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastAuditMediaMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
