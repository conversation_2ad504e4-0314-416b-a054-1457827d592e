/*
 * Powered By fast.up
 */
package com.fast.service.audit;

import com.fast.constant.StaticStr;
import com.fast.enums.audit.AuditTypeEnum;
import com.fast.mapper.audit.FastAuditFlowMapper;
import com.fast.mapper.audit.FastAuditProcessMapper;
import com.fast.mapper.audit.FastAuditRecordMapper;
import com.fast.mapper.business.FastIncomeManualEntryMapper;
import com.fast.po.audit.FastAuditFlowPO;
import com.fast.po.audit.FastAuditProcessPO;
import com.fast.po.audit.FastAuditRecordPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FastAuditProcessService extends BaseService {

    @Autowired
    private FastAuditProcessMapper fastAuditProcessMapper;

    @Autowired
    private FastAuditRecordMapper fastAuditRecordMapper;

    @Autowired
    private FastIncomeManualEntryMapper fastIncomeManualEntryMapper;

    @Autowired
    private FastAuditFlowMapper fastAuditFlowMapper;

    /**
     * 通过id查询单个对象
     */
    public FastAuditProcessPO queryById(FastAuditProcessPO params) {
        return fastAuditProcessMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastAuditProcessPO queryById(Integer id) {
        return fastAuditProcessMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastAuditProcessPO queryOne(FastAuditProcessPO params) {
        return fastAuditProcessMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastAuditProcessPO> queryList(FastAuditProcessPO params) {
        return fastAuditProcessMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastAuditProcessPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastAuditProcessPO> list = fastAuditProcessMapper.queryList(params);
        for (FastAuditProcessPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastAuditProcessPO params) {
        return fastAuditProcessMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastAuditProcessPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastAuditProcessMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastAuditProcessPO> list) {
        if (fastAuditProcessMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastAuditProcessPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastAuditProcessMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public MethodVO audit(FastAuditProcessPO params) {

        for (String id : params.getDataIds().split(",")) {
            // 审核记录是否存在
            FastAuditProcessPO processQuery = new FastAuditProcessPO();
            processQuery.setDataId(id);
            processQuery.setAuditType(AuditTypeEnum.MONTH_INCOME.getType());
            FastAuditProcessPO fastAuditProcessPO = fastAuditProcessMapper.queryOne(processQuery);


            if (Objects.isNull(fastAuditProcessPO)) {
                return MethodVO.error("审核记录不存在!");
            }

            if (fastAuditProcessPO.getState() != 1) {
                return MethodVO.error("非审核中状态");
            }
            Integer currentLevel = fastAuditProcessPO.getLevel();

            FastAuditFlowPO flowQuery = new FastAuditFlowPO();
            flowQuery.setAuditType(fastAuditProcessPO.getAuditType());
            flowQuery.setDelFlag(0);
            int maxLevel = fastAuditFlowMapper.queryCount(flowQuery);

            flowQuery.setLevel(currentLevel);
            FastAuditFlowPO fastAuditFlowPO = fastAuditFlowMapper.queryOne(flowQuery);


            if (Objects.isNull(fastAuditFlowPO)) {
                return MethodVO.error("审批流程不存在!");
            }

            // 校验当前审核人
            if (!fastAuditFlowPO.getUserId().equals(params.getCreatorId())) {
                return MethodVO.error("当前用户不可审批!");
            }

            // 插入审核记录
            Date nowTime = DateUtil.getNowDate();
            params.setCreateTime(nowTime);

            FastAuditRecordPO fastAuditRecordPO = new FastAuditRecordPO();
            fastAuditRecordPO.setAuditProcessId(fastAuditProcessPO.getId());
            fastAuditRecordPO.setCreatorId(params.getCreatorId());
            fastAuditRecordPO.setCreateTime(DateUtil.getNowDate());
            fastAuditRecordPO.setRemark(params.getRemark());
            fastAuditRecordPO.setLevel(currentLevel);
            fastAuditRecordPO.setState(params.getState());

            if (fastAuditRecordMapper.insertSelective(fastAuditRecordPO) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
            // 更新审核进度
            if (params.getState() == 1) {
                FastAuditProcessPO processUpdate = new FastAuditProcessPO();
                if (maxLevel == currentLevel) {
                    // 审核通过
                    processUpdate.setState(2);
                }
                processUpdate.setUpdateTime(DateUtil.getNowDate());
                processUpdate.setUpdatorId(params.getUpdatorId());
                processUpdate.setId(fastAuditProcessPO.getId());
                processUpdate.setLevel(currentLevel + 1);
                int i = fastAuditProcessMapper.updateById(processUpdate);
                if (i == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            } else {
                FastAuditProcessPO processUpdate = new FastAuditProcessPO();
                processUpdate.setId(fastAuditProcessPO.getId());
                processUpdate.setState(3);
                processUpdate.setUpdateTime(DateUtil.getNowDate());
                processUpdate.setUpdatorId(params.getUpdatorId());
                int updateCount = fastAuditProcessMapper.updateById(processUpdate);
                if (updateCount == 0) {
                    transactionRollBack();
                    return MethodVO.error(StaticStr.ADD_FAILED);
                }
            }
        }


        return MethodVO.success();
    }
}
