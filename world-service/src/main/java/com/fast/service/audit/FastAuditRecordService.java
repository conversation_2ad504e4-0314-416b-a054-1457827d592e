/*
 * Powered By fast.up
 */
package com.fast.service.audit;

import com.fast.constant.StaticStr;
import com.fast.mapper.audit.FastAuditFlowMapper;
import com.fast.mapper.audit.FastAuditRecordMapper;
import com.fast.mapper.business.FastIncomeManualEntryMapper;
import com.fast.po.audit.FastAuditRecordPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastAuditRecordService extends BaseService {

    @Autowired
    private FastAuditRecordMapper fastAuditRecordMapper;

    @Autowired
    private FastIncomeManualEntryMapper fastIncomeManualEntryMapper;

    @Autowired
    private FastAuditFlowMapper fastAuditFlowMapper;

    /**
     * 通过id查询单个对象
     */
    public FastAuditRecordPO queryById(FastAuditRecordPO params) {
        return fastAuditRecordMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastAuditRecordPO queryById(Integer id) {
        return fastAuditRecordMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastAuditRecordPO queryOne(FastAuditRecordPO params) {
        return fastAuditRecordMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastAuditRecordPO> queryList(FastAuditRecordPO params) {
        return fastAuditRecordMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastAuditRecordPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastAuditRecordPO> list = fastAuditRecordMapper.queryList(params);
        for (FastAuditRecordPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastAuditRecordPO params) {
        return fastAuditRecordMapper.queryCount(params);
    }


    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastAuditRecordPO> list) {
        if (fastAuditRecordMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastAuditRecordPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastAuditRecordMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
