/*
 * Powered By fast.up
 */
package com.fast.service.audit;

import com.fast.constant.StaticStr;
import com.fast.mapper.audit.FastAuditFlowMapper;
import com.fast.mapper.audit.FastAuditProcessMapper;
import com.fast.mapper.business.FastIncomeManualEntryMapper;
import com.fast.po.audit.FastAuditFlowPO;
import com.fast.po.audit.FastAuditProcessPO;
import com.fast.service.base.BaseService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastAuditFlowService extends BaseService {

    @Autowired
    private FastAuditFlowMapper fastAuditFlowMapper;

    @Autowired
    private FastIncomeManualEntryMapper fastIncomeManualEntryMapper;

    @Autowired
    private FastAuditProcessMapper fastAuditProcessMapper;

    /**
     * 通过id查询单个对象
     */
    public FastAuditFlowPO queryById(FastAuditFlowPO params) {
        return fastAuditFlowMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastAuditFlowPO queryById(Integer id) {
        return fastAuditFlowMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastAuditFlowPO queryOne(FastAuditFlowPO params) {
        return fastAuditFlowMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastAuditFlowPO> queryList(FastAuditFlowPO params) {
        return fastAuditFlowMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastAuditFlowPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastAuditFlowPO> list = fastAuditFlowMapper.queryList(params);
        for (FastAuditFlowPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastAuditFlowPO params) {
        return fastAuditFlowMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastAuditFlowPO params) {

        // 查询历史流程列表


        FastAuditFlowPO deleteQuery = new FastAuditFlowPO();
        deleteQuery.setAuditType(params.getAuditType());
        deleteQuery.setDelFlag(0);

        List<FastAuditFlowPO> historyFlowList = fastAuditFlowMapper.queryList(deleteQuery);
        // 删除历史流程
        deleteQuery.setUpdateTime(DateUtil.getNowDate());
        deleteQuery.setUpdatorId(params.getCreatorId());
        deleteQuery.setDelFlag(1);
        int i = fastAuditFlowMapper.updateByAuditType(deleteQuery);


        // 新增流程
        List<FastAuditFlowPO> auditFlowList = ObjectUtils.defaultIfNull(params.getAuditFlowList(), new ArrayList<>());

        // 历史记录设置为审核通过
        int maxLevel = auditFlowList.size();


        FastAuditProcessPO fastAuditProcessPO = new FastAuditProcessPO();
        fastAuditProcessPO.setLevel(maxLevel);
        fastAuditProcessPO.setState(2);
        fastAuditProcessPO.setAuditType("month_income");
        fastAuditProcessPO.setUpdateTime(DateUtil.getNowDate());
        fastAuditProcessPO.setUpdatorId(params.getCreatorId());
        int updateCount = fastAuditProcessMapper.batchAuditSuccess(fastAuditProcessPO);


        Integer level = 1;
        for (FastAuditFlowPO fastAuditFlowPO : auditFlowList) {
            fastAuditFlowPO.setAuditType(params.getAuditType());
            fastAuditFlowPO.setCreateTime(DateUtil.getNowDate());
            fastAuditFlowPO.setCreatorId(params.getCreatorId());
            fastAuditFlowPO.setLevel(level++);
            fastAuditFlowPO.setDelFlag(0);
        }

        // 查询
        if (CollUtil.isNotEmpty(auditFlowList)) {
            if (fastAuditFlowMapper.insertBatch(auditFlowList) == 0) {
                transactionRollBack();
                return MethodVO.error(StaticStr.ADD_FAILED);
            }
        }

        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastAuditFlowPO> list) {
        if (fastAuditFlowMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastAuditFlowPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastAuditFlowMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public ResultVO<?> getAwaitAuditCount(FastAuditFlowPO params) {
        // 查询审核数量
        List<FastAuditFlowPO> list = ObjectUtils.defaultIfNull(params.getAuditFlowList(), new ArrayList<>());
        params.setLevel(list.size());
        int count = fastAuditProcessMapper.queryAwaitAuditCount(params);
        return ResultVO.success(count);
    }
}
