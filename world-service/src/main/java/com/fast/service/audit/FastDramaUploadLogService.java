/*
 * Powered By fast.up
 */
package com.fast.service.audit;

import com.fast.constant.StaticStr;
import com.fast.mapper.audit.FastDramaUploadLogMapper;
import com.fast.po.audit.FastDramaUploadLogPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastDramaUploadLogService extends BaseService {

    @Autowired
    private FastDramaUploadLogMapper fastDramaUploadLogMapper;

    /**
     * 通过id查询单个对象
     */
    public FastDramaUploadLogPO queryById(FastDramaUploadLogPO params) {
        return fastDramaUploadLogMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastDramaUploadLogPO queryById(Integer id) {
        return fastDramaUploadLogMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastDramaUploadLogPO queryOne(FastDramaUploadLogPO params) {
        return fastDramaUploadLogMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastDramaUploadLogPO> queryList(FastDramaUploadLogPO params) {
        return fastDramaUploadLogMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastDramaUploadLogPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastDramaUploadLogPO> list = fastDramaUploadLogMapper.queryList(params);
        for (FastDramaUploadLogPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastDramaUploadLogPO params) {
        return fastDramaUploadLogMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastDramaUploadLogPO params) {
        Date nowTime = DateUtil.getNowDate();
        if (fastDramaUploadLogMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastDramaUploadLogPO> list) {
        if (fastDramaUploadLogMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastDramaUploadLogPO params) {
        if (fastDramaUploadLogMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

}
