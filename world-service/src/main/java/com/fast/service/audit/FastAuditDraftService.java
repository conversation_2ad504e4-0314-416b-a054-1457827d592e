/*
 * Powered By fast.up
 */
package com.fast.service.audit;

import com.fast.constant.StaticStr;
import com.fast.mapper.audit.FastAuditDraftMapper;
import com.fast.po.audit.FastAuditDraftPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastAuditDraftService extends BaseService {

    @Autowired
    private FastAuditDraftMapper fastAuditDraftMapper;

    /**
     * 通过id查询单个对象
     */
    public FastAuditDraftPO queryById(FastAuditDraftPO params) {
        return fastAuditDraftMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastAuditDraftPO queryById(Integer id) {
        return fastAuditDraftMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastAuditDraftPO queryOne(FastAuditDraftPO params) {
        return fastAuditDraftMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastAuditDraftPO> queryList(FastAuditDraftPO params) {
        return fastAuditDraftMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastAuditDraftPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastAuditDraftPO> list = fastAuditDraftMapper.queryList(params);
        for (FastAuditDraftPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastAuditDraftPO params) {
        return fastAuditDraftMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastAuditDraftPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        params.setUpdateTime(nowTime);
        if (fastAuditDraftMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastAuditDraftPO> list) {
        if (fastAuditDraftMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastAuditDraftPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastAuditDraftMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }


    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateByUser(FastAuditDraftPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (fastAuditDraftMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }


}
