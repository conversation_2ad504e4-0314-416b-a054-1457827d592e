/*
 * Powered By fast.up
 */
package com.fast.service.retail;

import com.fast.constant.StaticStr;
import com.fast.mapper.retail.FastRetailInvoiceMapper;
import com.fast.po.retail.FastRetailInvoicePO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastRetailInvoiceService extends BaseService {

    @Autowired
    private FastRetailInvoiceMapper fastRetailInvoiceMapper;

    /**
     * 通过id查询单个对象
     */
    public FastRetailInvoicePO queryById(FastRetailInvoicePO item) {
        return fastRetailInvoiceMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastRetailInvoicePO queryById(Integer id) {
        FastRetailInvoicePO itemParam = new FastRetailInvoicePO();
        itemParam.setId(id);
        return fastRetailInvoiceMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastRetailInvoicePO queryOne(FastRetailInvoicePO item) {
        return fastRetailInvoiceMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastRetailInvoicePO> queryList(FastRetailInvoicePO item) {
        return fastRetailInvoiceMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastRetailInvoicePO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastRetailInvoicePO> list = fastRetailInvoiceMapper.queryList(item);
        for (FastRetailInvoicePO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastRetailInvoicePO item) {
        return fastRetailInvoiceMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastRetailInvoicePO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastRetailInvoiceMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastRetailInvoicePO> list) {
        if (fastRetailInvoiceMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastRetailInvoicePO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastRetailInvoiceMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
