/*
 * Powered By fast.up
 */
package com.fast.service.retail;

import com.fast.constant.StaticStr;
import com.fast.mapper.retail.FastRetailMiniMapper;
import com.fast.po.retail.FastRetailMiniPO;
import com.fast.service.base.BaseService;
import com.fast.vo.MethodVO;
import com.fast.vo.retail.RetailMiniVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FastRetailMiniService extends BaseService {

    @Autowired
    private FastRetailMiniMapper fastRetailMiniMapper;

    /**
     * 通过id查询单个对象
     */
    public FastRetailMiniPO queryById(FastRetailMiniPO item) {
        return fastRetailMiniMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastRetailMiniPO queryById(Integer id) {
        FastRetailMiniPO itemParam = new FastRetailMiniPO();
        itemParam.setId(id);
        return fastRetailMiniMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastRetailMiniPO queryOne(FastRetailMiniPO item) {
        return fastRetailMiniMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<RetailMiniVO> queryList(FastRetailMiniPO item) {
        return fastRetailMiniMapper.queryList(item);
    }

    /**
     * 查询全部分销商id
     */
    public List<Integer> queryRetailIdList(FastRetailMiniPO item) {
        return fastRetailMiniMapper.queryRetailIdList(item);
    }

//    /**
//     * 查询全部(分页)
//     */
//    public ResultVO<?> queryPageList(FastRetailMiniPO item, PageVO pageVO) {
//        startPage(pageVO);
//        List<FastRetailMiniPO> list = fastRetailMiniMapper.queryList(item);
//        for (FastRetailMiniPO cur : list) {
//            cur.setEncryptionId(encode(cur.getId()));
//        }
//        Map<String, Object> results = getPageList(list, pageVO);
//        return ResultVO.success(results);
//    }

    /**
     * 查询总数
     */
    public int queryCount(FastRetailMiniPO item) {
        return fastRetailMiniMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastRetailMiniPO item) {
        if (fastRetailMiniMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastRetailMiniPO> list) {
        if (fastRetailMiniMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastRetailMiniPO item) {
        if (fastRetailMiniMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public MethodVO updateAdDataFlag(FastRetailMiniPO params) {
        // 判断是否存在免费
        if (params.getAdDataFlag() == 1) {
            FastRetailMiniPO query = new FastRetailMiniPO();
            query.setMiniId(params.getMiniId());
            query.setAdDataFlag(1);
            query.setNonRetailId(params.getId());
            FastRetailMiniPO retailMiniPO = fastRetailMiniMapper.queryAdDataOne(query);
            if (retailMiniPO != null) {
                return MethodVO.error(String.format("该应用已被「%s」投放免费,无法关联", retailMiniPO.getRetailName()));
            }
        }
        // 判断是否存在免费
        if (fastRetailMiniMapper.updateAdDataFlag(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
