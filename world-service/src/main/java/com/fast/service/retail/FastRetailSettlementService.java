/*
 * Powered By fast.up
 */
package com.fast.service.retail;

import com.fast.constant.StaticStr;
import com.fast.mapper.retail.FastRetailSettlementMapper;
import com.fast.po.retail.FastRetailSettlementPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FastRetailSettlementService extends BaseService {

    @Autowired
    private FastRetailSettlementMapper fastRetailSettlementMapper;

    /**
     * 通过id查询单个对象
     */
    public FastRetailSettlementPO queryById(FastRetailSettlementPO item) {
        return fastRetailSettlementMapper.queryById(item);
    }

    /**
     * 通过id查询单个对象
     */
    public FastRetailSettlementPO queryById(Integer id) {
        FastRetailSettlementPO itemParam = new FastRetailSettlementPO();
        itemParam.setId(id);
        return fastRetailSettlementMapper.queryById(itemParam);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastRetailSettlementPO queryOne(FastRetailSettlementPO item) {
        return fastRetailSettlementMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastRetailSettlementPO> queryList(FastRetailSettlementPO item) {
        return fastRetailSettlementMapper.queryList(item);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastRetailSettlementPO item, PageVO pageVO) {
        startPage(pageVO);
        List<FastRetailSettlementPO> list = fastRetailSettlementMapper.queryList(item);
        for (FastRetailSettlementPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        Map<String, Object> results = getPageListData(list, pageVO);
        return ResultVO.success(results);
    }

    /**
     * 查询总数
     */
    public int queryCount(FastRetailSettlementPO item) {
        return fastRetailSettlementMapper.queryCount(item);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastRetailSettlementPO item) {
        Date nowTime = DateUtil.getNowDate();
        item.setCreateTime(nowTime);
        if (fastRetailSettlementMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastRetailSettlementPO> list) {
        if (fastRetailSettlementMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastRetailSettlementPO item) {
        item.setUpdateTime(DateUtil.getNowDate());
        if (fastRetailSettlementMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }
}
