/*
 * Powered By fast.up
 */
package com.fast.service.retail;

import com.fast.constant.StaticStr;
import com.fast.framework.exception.BusinessException;
import com.fast.mapper.retail.FastRetailExpertSettingMapper;
import com.fast.po.retail.FastRetailExpertSettingPO;
import com.fast.service.base.BaseService;
import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.bosskg.JsonUtils;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class FastRetailExpertSettingService extends BaseService {

    private final FastRetailExpertSettingMapper fastRetailExpertSettingMapper;

    /**
     * 通过id查询单个对象
     */
    public FastRetailExpertSettingPO queryById(FastRetailExpertSettingPO params) {
        return fastRetailExpertSettingMapper.queryById(params);
    }

    /**
     * 通过id查询单个对象
     */
    public FastRetailExpertSettingPO queryById(Integer id) {
        return fastRetailExpertSettingMapper.queryById(id);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastRetailExpertSettingPO queryOne(FastRetailExpertSettingPO params) {
        return fastRetailExpertSettingMapper.queryOne(params);
    }

    /**
     * 查询全部
     */
    public List<FastRetailExpertSettingPO> queryList(FastRetailExpertSettingPO params) {
        return fastRetailExpertSettingMapper.queryList(params);
    }

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastRetailExpertSettingPO params, PageVO pageVO) {
        startPage(pageVO);
        List<FastRetailExpertSettingPO> list = fastRetailExpertSettingMapper.queryList(params);
        for (FastRetailExpertSettingPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastRetailExpertSettingPO params) {
        return fastRetailExpertSettingMapper.queryCount(params);
    }

    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(FastRetailExpertSettingPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setCreateTime(nowTime);
        if (fastRetailExpertSettingMapper.insertSelective(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastRetailExpertSettingPO> list) {
        if (fastRetailExpertSettingMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastRetailExpertSettingPO params) {
        if (fastRetailExpertSettingMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        return MethodVO.success();
    }

    public FastRetailExpertSettingPO getExpertSetting(Integer retailId) {
        String key = "boss_kg_retail:" + retailId;
        String str = RedisUtil.get(key);
        if (StrUtil.isNotBlank(str)) {
            return JsonUtils.fromJson(str, FastRetailExpertSettingPO.class);
        }

        FastRetailExpertSettingPO setting = fastRetailExpertSettingMapper.getByRetailId(retailId);
        if (setting != null) {
            RedisUtil.set(key, JsonUtils.toJson(setting), DateUtil.todayLastSeconds());
            return setting;
        }
        throw new BusinessException("当前分销商未配置银行卡签约");
    }
}
