/*
 * Powered By fast.up
 */
package com.fast.service.retail;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.MiniTypeEnum;
import com.fast.mapper.retail.FastRetailInvoiceMapper;
import com.fast.mapper.retail.FastRetailMapper;
import com.fast.mapper.retail.FastRetailMiniMapper;
import com.fast.mapper.retail.FastRetailSettlementMapper;
import com.fast.mapper.user.FastRoleContentMapper;
import com.fast.mapper.user.FastRoleMapper;
import com.fast.mapper.user.FastRoleMenuMapper;
import com.fast.mapper.user.FastUserMapper;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.mini.FastMiniPO;
import com.fast.po.retail.FastRetailInvoicePO;
import com.fast.po.retail.FastRetailMiniPO;
import com.fast.po.retail.FastRetailPO;
import com.fast.po.retail.FastRetailSettlementPO;
import com.fast.po.user.FastRoleContentPO;
import com.fast.po.user.FastRoleMenuPO;
import com.fast.po.user.FastRolePO;
import com.fast.po.user.FastUserPO;
import com.fast.service.base.BaseService;
import com.fast.service.mini.FastMiniService;
import com.fast.service.user.FastUserService;
import com.fast.utils.CollUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.MethodVO;
import com.fast.vo.PageVO;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.mini.FastMiniVO;
import com.fast.vo.retail.RetailMiniVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class FastRetailService extends BaseService {

    @Autowired
    private FastRetailMapper retailMapper;
    @Autowired
    private FastRetailMiniMapper retailMiniMapper;
    @Autowired
    private FastRetailSettlementMapper retailSettlementMapper;
    @Autowired
    private FastRetailInvoiceMapper retailInvoiceMapper;
    @Autowired
    private FastUserService userService;
    @Autowired
    private FastUserMapper userMapper;
    @Autowired
    private FastMiniService miniService;
    @Autowired
    private FastRetailMiniService retailMiniService;
    @Autowired
    private FastRoleMapper fastRoleMapper;
    @Autowired
    private FastRoleMenuMapper fastRoleMenuMapper;
    @Autowired
    private FastRoleContentMapper fastRoleContentMapper;
    @Qualifier("fastRetailMiniMapper")
    @Autowired
    private FastRetailMiniMapper fastRetailMiniMapper;

    /**
     * 通过id查询单个对象
     */
    public FastRetailPO queryById(FastRetailPO item) {
        return retailMapper.queryById(item);
    }

    /**
     * 查询分销商信息
     *
     * @param item
     * @return
     */
    public ResultVO<?> queryDetail(FastRetailPO item) {
        // 查询基本信息
        FastRetailPO retail = retailMapper.queryById(item);
        // 管理员账号
        FastUserPO userParam = new FastUserPO();
        userParam.setRetailId(item.getId());
        userParam.setRoleId(2);
        FastUserPO userPO = userService.queryOne(userParam);
        userPO.setPassword("");
        // 关联小程序
        FastRetailMiniPO retailMiniParam = new FastRetailMiniPO();
        retailMiniParam.setRetailId(item.getId());
        List<RetailMiniVO> retailMiniList = retailMiniMapper.queryList(retailMiniParam);
        // 发票信息
        FastRetailInvoicePO invoiceParam = new FastRetailInvoicePO();
        invoiceParam.setRetailId(item.getId());
        List<FastRetailInvoicePO> invoiceList = retailInvoiceMapper.queryList(invoiceParam);
        // 结算信息
        FastRetailSettlementPO settlementParam = new FastRetailSettlementPO();
        settlementParam.setRetailId(item.getId());
        List<FastRetailSettlementPO> settlementList = retailSettlementMapper.queryList(settlementParam);
        Map<String, Object> results = ResultVO.getMap();
        results.put("retail", retail);
        results.put("userPO", userPO);
        results.put("retailMiniList", retailMiniList);
        results.put("invoiceList", invoiceList);
        results.put("settlementList", settlementList);
        return ResultVO.success(results);
    }

    /**
     * 通过条件查询单个对象
     */
    public FastRetailPO queryOne(FastRetailPO item) {
        return retailMapper.queryOne(item);
    }

    /**
     * 查询全部
     */
    public List<FastRetailPO> queryList(FastRetailPO item) {
        return retailMapper.queryList(item);
    }

    /**
     * 查询全部
     */
    public List<FastRetailPO> querySimpleList(FastRetailPO item) {
        return retailMapper.querySimpleList(item);
    }

    /**
     * 查询分销商id
     */
    public Set<Integer> queryRetailIds(FastRetailPO item) {
        return retailMapper.queryRetailIds(item);
    }

    /**
     * 根据类型查询分销商id
     *
     * @param retailType 类别:1=自投、2=代投、3=独立分销商
     */
    public Set<Integer> queryRetailIdsType(Integer retailType) {
        FastRetailPO item = new FastRetailPO();
        item.setState(1);
        item.setDelFlag(0);
        item.setRetailType(retailType);
        return retailMapper.queryRetailIds(item);
    }

    /**
     * 查询自/代投分销商id
     */
    public Set<Integer> queryRetailIdsType1_2() {
        FastRetailPO item = new FastRetailPO();
        item.setState(1);
        item.setDelFlag(0);
        item.setRetailTypes("1,2");
        return retailMapper.queryRetailIds(item);
    }

    /**
     * 查询独立分销商id
     */
    public Set<Integer> queryRetailIdsType3() {
        FastRetailPO item = new FastRetailPO();
        item.setState(1);
        item.setDelFlag(0);
        item.setRetailTypes("3");
        return retailMapper.queryRetailIds(item);
    }

    /**
     * 查询分销商名称
     */
    public List<FastRetailPO> queryRetailNames(FastRetailPO item) {
        return retailMapper.queryRetailNames(item);
    }

    /**
     * 查询分销商名称Map
     */
    public Map<Integer, String> queryRetailNameMap(FastRetailPO item) {
        List<FastRetailPO> list = retailMapper.queryRetailNames(item);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, String> map = new HashMap<>();
        list.forEach(cur -> map.put(cur.getId(), cur.getRetailName()));
        return map;
    }

    /**
     * 查询分销商Map
     */
    public Map<Integer, FastRetailPO> queryRetailMap(FastRetailPO item) {
        List<FastRetailPO> list = retailMapper.queryRetailNames(item);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        Map<Integer, FastRetailPO> map = new HashMap<>();
        list.forEach(cur -> map.put(cur.getId(), cur));
        return map;
    }

    private static final List<Integer> ID_12 = Arrays.asList(1, 2);

    /**
     * 查询全部(分页)
     */
    public ResultVO<?> queryPageList(FastRetailPO item, PageVO pageVO) {
        if (notBlank(item.getMiniIds())) {
            FastRetailMiniPO query = new FastRetailMiniPO();
            query.setMiniIds(item.getMiniIds());
            List<Integer> retailIds = retailMiniService.queryRetailIdList(query);
            if (CollUtil.isEmpty(retailIds)) {
                return ResultVO.success(getDefaultPageListData());
            }
            item.setRetailIds(StrUtil.joinNoRepeat(retailIds));
        }
        if (item.getRetailId() != null) {
            item.setId(item.getRetailId());
        }
        startPage(pageVO);
        List<FastRetailPO> list = retailMapper.queryList(item);
        for (FastRetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
            if (notBlank(cur.getMiniIds())) {
                List<FastMiniVO> fastMiniVOS = miniService.queryInfoByRedis(cur.getMiniIds());
                fastMiniVOS.forEach(fmv -> {
                    if (ID_12.contains(fmv.getType())) {
                        FastRetailMiniPO retailMiniQuery = new FastRetailMiniPO();
                        retailMiniQuery.setRetailId(cur.getId());
                        retailMiniQuery.setMiniId(fmv.getId());
                        FastRetailMiniPO retailMiniPO = fastRetailMiniMapper.queryOne(retailMiniQuery);
                        if (retailMiniPO != null) {
                            fmv.setAdDataFlag(retailMiniPO.getAdDataFlag());
                        }
                    }
                });
                cur.setMiniList(fastMiniVOS);
            }
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }


    /**
     * @param item
     * @param pageVO
     * @return
     */
    public ResultVO<?> getCpListFromOrg(FastRetailPO item, PageVO pageVO) {
        startPage(pageVO);
        FastRetailPO params = new FastRetailPO();
        params.setIds(item.getRetailIds());
        params.setCpTypes(item.getCpTypes());
        params.setRetailFlag(2);
        params.setState(1);
        params.setBelongType(item.getBelongType());
        List<FastRetailPO> list = retailMapper.queryList(params);
        for (FastRetailPO cur : list) {
            cur.setEncryptionId(encode(cur.getId()));
        }
        return ResultVO.success(getPageListData(list, pageVO));
    }


    /**
     * @param item
     * @param pageVO
     * @return
     */
    public ResultVO<?> getCpRetailList(FastRetailPO item, PageVO pageVO) {
        startPage(pageVO);
        FastRetailPO params = new FastRetailPO();
        params.setIds(item.getRetailIds());
        params.setCpTypes(item.getCpTypes());
        params.setRetailFlag(2);
        params.setState(1);
        params.setBelongType(item.getBelongType());
        params.setDelFlag(item.getDelFlag());
        params.setContentType(item.getContentType());
        List<FastRetailPO> list = retailMapper.queryList(params);
        // for (FastRetailPO cur : list) {
        //     if (cur.getRetailFlag() == 2) {
        //         List<CpOrgCpPO> orgList = cpOrgCpService.queryByCpId(cur.getId());
        //         cur.setCpOrgList(orgList);
        //     }
        //     cur.setEncryptionId(encode(cur.getId()));
        // }
        return ResultVO.success(getPageListData(list, pageVO));
    }

    /**
     * 查询总数
     */
    public int queryCount(FastRetailPO item) {
        return retailMapper.queryCount(item);
    }

    /**
     * 查询名称是否重复
     */
    public int queryNameIsRepeat(FastRetailPO item) {
        return retailMapper.queryNameIsRepeat(item);
    }


    /**
     * 新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insert(SessionVO sessionVO, FastRetailPO item) {
        List<FastMiniVO> fastMiniVOS = miniService.queryInfoByRedis(item.getMiniIds());
        for (FastMiniVO mini : fastMiniVOS) {
            if (mini.getType() == 2) {
                FastRetailMiniPO rmParam = new FastRetailMiniPO();
                rmParam.setMiniId(mini.getId());
                FastRetailMiniPO rmPO = retailMiniMapper.queryOne(rmParam);
                if (rmPO != null) {
                    transactionRollBack();
                    return MethodVO.error("抖音小程序:" + mini.getMiniName() + "，不能重复授权给分销商");
                }
            }
        }
        Date nowTime = DateUtil.getNowDate();
        Integer auditFlag = 0; // 是否需要审核
        item.setCreateTime(nowTime);
        if (retailMapper.insertSelective(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        // 新增视频号达人
        if (item.getBusinessType() != null && item.getBusinessType() == 2) {
            FastRolePO role = new FastRolePO();
            role.setState(1);
            role.setRetailId(item.getId());
            role.setRoleName(StaticVar.FINDER_ROLE_NAME);
            role.setRemark(StaticVar.FINDER_ROLE_NAME);
            role.setCreatorId(item.getCreatorId());
            role.setUpdatorId(item.getCreatorId());
            role.setCreateTime(item.getCreateTime());
            role.setUpdateTime(item.getCreateTime());
            if (fastRoleMapper.insertSelective(role) == 0) {
                return MethodVO.error("注册失败");
            }
        }


        // 新增管理员用户
        FastUserPO fastUser = new FastUserPO();
        fastUser.setRetailId(item.getId());
        fastUser.setLoginName(item.getLoginName());
        fastUser.setUserName(item.getUserName());
        fastUser.setPhone(item.getPhone());
        fastUser.setPassword(Md5Util.getMD5BySalt(StaticVar.DEFAULT_PASSWORD));
        fastUser.setParentId(0);
        if (item.getRetailFlag() != null && item.getRetailFlag() == 2) {
            Integer roleId = 0;
            if (item.getCpType() == 1) {
                roleId = 7;
            } else if (item.getCpType() == 2) {
                roleId = 8;
            } else if (item.getCpType() == 3) {
                roleId = 9;
            } else if (item.getCpType() == 4) {
                roleId = 9;
            } else if (item.getCpType() == 5) {
                roleId = 9;
            } else if (item.getCpType() == 6) {
                roleId = 9;
            } else if (item.getCpType() == 7) {
                roleId = 10;
            }
            FastRoleMenuPO queryByRole = new FastRoleMenuPO();
            queryByRole.setRoleId(roleId);
            queryByRole.setContentType(1);
            Set<Integer> menuIdSet = fastRoleMenuMapper.queryListExtra(queryByRole);
            // 1.创建cp角色
            FastRolePO fastRolePO = new FastRolePO();
            fastRolePO.setState(1);
            fastRolePO.setRetailId(item.getId());
            fastRolePO.setRoleName(item.getRetailName() + "管理员");
            fastRolePO.setCreatorId(sessionVO.getUserId());
            fastRolePO.setUpdatorId(sessionVO.getUserId());
            fastRolePO.setCreateTime(DateUtil.getNowDate());
            fastRolePO.setUpdateTime(DateUtil.getNowDate());
            fastRoleMapper.insert(fastRolePO);
            FastRoleContentPO insertContent = new FastRoleContentPO();
            insertContent.setRoleId(fastRolePO.getId());
            insertContent.setContentType(1);
            insertContent.setCreatorId(item.getCreatorId());
            insertContent.setCreateTime(DateUtil.getNowDate());
            fastRoleContentMapper.insertSelective(insertContent);
            // 绑定用户设置角色id
            fastUser.setRoleIds(String.valueOf(fastRolePO.getId()));
            // 2.同步菜单数据
            Set<Integer> menuIds = Sets.newHashSet();
            if (StrUtil.isNotEmpty(item.getMenuIds())) {
                List menuIdList = CollUtil.parseIntStr2List(item.getMenuIds());
                menuIds.addAll(menuIdList);
            }
            menuIds.addAll(menuIdSet);
            List<FastRoleMenuPO> fastRoleMenuPOList = Lists.newArrayList();
            for (Integer menuId : menuIds) {
                FastRoleMenuPO fastRoleMenuPO = new FastRoleMenuPO();
                fastRoleMenuPO.setRoleId(fastRolePO.getId());
                fastRoleMenuPO.setMenuId(menuId);
                fastRoleMenuPO.setContentType(1);
                fastRoleMenuPOList.add(fastRoleMenuPO);
            }
            fastRoleMenuMapper.insertBatch(fastRoleMenuPOList);
        } else {
            fastUser.setRoleIds("2");    // 分销商管理员
        }
        fastUser.setCreateTime(nowTime);
        fastUser.setCreatorId(item.getCreatorId());
        MethodVO methodVO = userService.insertRetailAdmin(fastUser);
        if (methodVO.getCode() != 0) {
            transactionRollBack();
            return methodVO;
        }

        // 更新审核状态
        FastRetailPO update = new FastRetailPO();
        update.setId(item.getId());
        update.setAdminId(fastUser.getId());
        if (auditFlag == 0) {// 不用审核
            update.setAuditStatus(1);
            retailMapper.updateById(update);
        } else {
            update.setAuditStatus(0); // 待审核
            retailMapper.updateById(update);
        }
        // 删除缓存
        delInfoRedis(update.getId());
        if (item.getRetailFlag() == 1 && notEmpty(item.getMiniIds())) {
            // 新增授权小程序的关系
            List<FastRetailMiniPO> miniList = new ArrayList<>();
            List<Integer> miniIdList = CollUtil.parseIntStr2List(item.getMiniIds());
            CollUtil.removeRepeat(miniIdList);
            for (Integer miniId : miniIdList) {
                FastRetailMiniPO mini = new FastRetailMiniPO();
                mini.setMiniId(miniId);
                mini.setRetailId(item.getId());

                miniList.add(mini);
            }
            if (CollUtil.hasContent(miniList)) {
                retailMiniMapper.insertBatch(miniList);
            }
        }

        // 新增结算信息
        FastRetailSettlementPO settlement = new FastRetailSettlementPO();
        settlement.setRetailId(item.getId());
        settlement.setUserName(item.getSettlementUserName());
        settlement.setAccount(item.getSettlementAccount());
        settlement.setBankName(item.getSettlementBankName());
        settlement.setCreateTime(nowTime);
        settlement.setCreatorId(item.getCreatorId());
        if (retailSettlementMapper.insertSelective(settlement) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }

        // 新增发票信息
        FastRetailInvoicePO invoice = new FastRetailInvoicePO();
        invoice.setRetailId(item.getId());
        invoice.setName(item.getInvoiceName());
        invoice.setDutyNum(item.getInvoiceDutyNum());
        invoice.setAddress(item.getInvoiceAddress());
        invoice.setPhone(item.getInvoicePhone());
        invoice.setAccount(item.getInvoiceAccount());
        invoice.setBankName(item.getInvoiceBankName());
        invoice.setCreateTime(nowTime);
        invoice.setCreatorId(item.getCreatorId());
        if (retailInvoiceMapper.insertSelective(invoice) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
        return MethodVO.success();
    }

    /**
     * 批量新增
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO insertBatch(List<FastRetailPO> list) {
        if (retailMapper.insertBatch(list) > 0) {
            return MethodVO.success();
        } else {
            return MethodVO.error(StaticStr.ADD_FAILED);
        }
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO update(FastRetailPO params) {
        Date nowTime = DateUtil.getNowDate();
        params.setUpdateTime(nowTime);
        if (retailMapper.updateById(params) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        // 删除缓存
        delInfoRedis(params.getId());
        FastUserPO query = new FastUserPO();
        query.setRetailId(params.getId());
        query.setDelFlag(StaticVar.NO);
        FastUserPO fastUser = userMapper.queryOne(query);
        if (fastUser == null) {
            return MethodVO.error("分销商管理员不存在");
        }

        fastUser.setLoginName(params.getLoginName());
        if (userMapper.queryNameIsRepeat(fastUser) > 0) {
            return MethodVO.error("登录账户不能重复");
        }

        // 更新管理员用户
        fastUser.setUserName(params.getUserName());
        fastUser.setPhone(params.getPhone());
        fastUser.setUpdateTime(nowTime);
        fastUser.setUpdatorId(params.getUpdatorId());
        if (userMapper.updateById(fastUser) == 0) {
            transactionRollBack();
            return MethodVO.error("更新管理员用户失败");
        }
        // 更新cp端所属组织
        FastRetailPO retailPO = retailMapper.queryById(params.getId());
        if (Objects.nonNull(retailPO) && retailPO.getRetailFlag().equals(2)) {
            // 1.修改组织信息
            Integer auditStatus = retailPO.getAuditStatus();// 获取审核状态
            if (auditStatus == 1) {// 审核通过
                if (!params.getMenuIds().equals(retailPO.getMenuIds())) {
                    applyAudit(params, fastUser);
                }
            } else if (auditStatus == 2) {// 审核不通过
                applyAudit(params, fastUser);
            }

        }
        // 更新授权小程序的关系
        FastRetailMiniPO delete = new FastRetailMiniPO();
        delete.setRetailId(params.getId());
        retailMiniMapper.deleteByRetailId(delete);
        List<FastRetailMiniPO> miniList = new ArrayList<>();
        List<Integer> miniIdList = CollUtil.parseIntStr2List(params.getMiniIds());
        CollUtil.removeRepeat(miniIdList);
        if (miniIdList != null && miniIdList.size() > 0) {
            for (Integer miniId : miniIdList) {
                FastRetailMiniPO mini = new FastRetailMiniPO();
                mini.setMiniId(miniId);
                mini.setRetailId(params.getId());
                miniList.add(mini);
                // 验证如果是抖音小程序，只能授权给一个分销商
                FastMiniPO miniPO = miniService.queryById(miniId);
                if (miniPO.getType() == 2) {
                    FastRetailMiniPO rmParam = new FastRetailMiniPO();
                    rmParam.setMiniId(miniId);
                    FastRetailMiniPO rmPO = retailMiniMapper.queryOne(rmParam);
                    if (rmPO != null) {
                        transactionRollBack();
                        return MethodVO.error("抖音小程序:" + miniPO.getMiniName() + "，不能重复授权给分销商");
                    }
                } else if (miniPO.getType() == 4) {
                    FastRetailMiniPO rmParam = new FastRetailMiniPO();
                    rmParam.setMiniId(miniId);
                    FastRetailMiniPO rmPO = retailMiniMapper.queryOne(rmParam);
                    if (rmPO != null) {
                        transactionRollBack();
                        return MethodVO.error("快手小程序:" + miniPO.getMiniName() + "，不能重复授权给分销商");
                    }
                } else if (miniPO.getType() == MiniTypeEnum.ALI_MINI.index) {
                    FastRetailMiniPO rmParam = new FastRetailMiniPO();
                    rmParam.setMiniId(miniId);
                    FastRetailMiniPO rmPO = retailMiniMapper.queryOne(rmParam);
                    if (rmPO != null) {
                        transactionRollBack();
                        return MethodVO.error("支付宝小程序:" + miniPO.getMiniName() + "，不能重复授权给分销商");
                    }
                }
            }
        }
        if (CollUtil.hasContent(miniList)) {
            retailMiniMapper.insertBatch(miniList);
        }
        // 更新结算信息
        FastRetailSettlementPO settlement = new FastRetailSettlementPO();
        settlement.setRetailId(params.getId());
        settlement.setUserName(params.getSettlementUserName());
        settlement.setAccount(params.getSettlementAccount());
        settlement.setBankName(params.getSettlementBankName());
        settlement.setCreateTime(nowTime);
        settlement.setCreatorId(params.getUpdatorId());
        settlement.setUpdateTime(nowTime);
        settlement.setUpdatorId(params.getUpdatorId());
        if (retailSettlementMapper.updateByRetailId(settlement) == 0) {
            transactionRollBack();
            return MethodVO.error("更新结算信息失败");
        }
        // 更新发票信息
        FastRetailInvoicePO invoice = new FastRetailInvoicePO();
        invoice.setRetailId(params.getId());
        invoice.setName(params.getInvoiceName());
        invoice.setDutyNum(params.getInvoiceDutyNum());
        invoice.setAddress(params.getInvoiceAddress());
        invoice.setPhone(params.getInvoicePhone());
        invoice.setAccount(params.getInvoiceAccount());
        invoice.setBankName(params.getInvoiceBankName());
        invoice.setCreateTime(nowTime);
        invoice.setCreatorId(params.getUpdatorId());
        invoice.setUpdateTime(nowTime);
        invoice.setUpdatorId(params.getUpdatorId());
        if (retailInvoiceMapper.updateByRetailId(invoice) == 0) {
            transactionRollBack();
            return MethodVO.error("更新发票信息失败");
        }
        return MethodVO.success();
    }

    /**
     * 更新
     */
    @Transactional(rollbackFor = Exception.class)
    public MethodVO updateState(FastRetailPO item) {
        Date nowTime = DateUtil.getNowDate();
        FastDramaPO state = new FastDramaPO();
        state.setUpdatorId(item.getUpdatorId());
        state.setUpdateTime(nowTime);
        state.setId(item.getId());
        state.setOpenState(item.getState());
        if (retailMapper.updateById(item) == 0) {
            transactionRollBack();
            return MethodVO.error(StaticStr.UPDATE_FAILED);
        }
        // 删除缓存
        delInfoRedis(item.getId());
        return MethodVO.success();
    }

    public List<FastRetailPO> getCPRetailList(String name) {
        return retailMapper.getCPRetailList(name);
    }

    /**
     * 修改cp账号重新申请审核
     *
     * @param item
     * @param fastUser
     */
    public void applyAudit(FastRetailPO item, FastUserPO fastUser) {
        // 1.创建角色、创建账号
        Integer cpRoleId = null;
        if (item.getCpType() == 1) {
            cpRoleId = 7;
        } else if (item.getCpType() == 2) {
            cpRoleId = 8;
        } else if (item.getCpType() == 3) {
            cpRoleId = 9;
        } else if (item.getCpType() == 4) {
            cpRoleId = 9;
        } else if (item.getCpType() == 5) {
            cpRoleId = 9;
        } else if (item.getCpType() == 6) {
            cpRoleId = 9;
        }
        FastRoleMenuPO queryByRole = new FastRoleMenuPO();
        queryByRole.setRoleId(cpRoleId);
        queryByRole.setContentType(1);
        Set<Integer> menuIdSet = fastRoleMenuMapper.queryListExtra(queryByRole);
        // 先删除改cp账号下的所有角色
        fastRoleMapper.deleteByRetailId(item.getId());
        // 重新写入一个角色
        FastRolePO fastRolePO = new FastRolePO();
        fastRolePO.setState(1);
        fastRolePO.setRetailId(item.getId());
        fastRolePO.setRoleName(item.getRetailName() + "管理员");
        fastRolePO.setCreatorId(item.getUpdatorId());
        fastRolePO.setUpdatorId(item.getUpdatorId());
        fastRolePO.setCreateTime(DateUtil.getNowDate());
        fastRolePO.setUpdateTime(DateUtil.getNowDate());
        fastRoleMapper.insert(fastRolePO);
        // 给角色赋予内容权限
        FastRoleContentPO insertContent = new FastRoleContentPO();
        insertContent.setRoleId(fastRolePO.getId());
        insertContent.setContentType(1);
        insertContent.setCreatorId(item.getUpdatorId());
        insertContent.setCreateTime(DateUtil.getNowDate());
        fastRoleContentMapper.insertSelective(insertContent);
        // 更新用户的角色
        fastUser.setRoleIds(String.valueOf(fastRolePO.getId()));
        userMapper.updateById(fastUser);
        // 2.重新更新菜单
        Set<Integer> menuIds = Sets.newHashSet();
        if (StrUtil.isNotEmpty(item.getMenuIds())) {
            List menuIdList = CollUtil.parseIntStr2List(item.getMenuIds());
            menuIds.addAll(menuIdList);
        }
        menuIds.addAll(menuIdSet);
        List<FastRoleMenuPO> roleMenuPOList = Lists.newArrayList();
        for (Integer menuId : menuIds) {
            FastRoleMenuPO fastRoleMenuPO = new FastRoleMenuPO();
            fastRoleMenuPO.setMenuId(menuId);
            fastRoleMenuPO.setRoleId(fastRolePO.getId());
            fastRoleMenuPO.setContentType(1);
            roleMenuPOList.add(fastRoleMenuPO);
        }
        fastRoleMenuMapper.insertBatch(roleMenuPOList);
        // 3.查询审核模板，判断是否需要审核
        Integer auditStatus = 1;
        // 4.修改审核状态
        FastRetailPO updateById = new FastRetailPO();
        updateById.setId(item.getId());
        updateById.setAdminId(fastUser.getId());
        updateById.setAuditStatus(auditStatus);
        retailMapper.updateById(updateById);
        // 删除缓存
        delInfoRedis(item.getId());
        // 5.强制下线
        RedisUtil.set(StaticVar.CHANGE_ROLE_ID + ":" + fastUser.getId(), String.valueOf(fastUser.getId()));
    }

    public FastRetailPO queryInfoByRedis(Integer retailId) {
        FastRetailPO retail = new FastRetailPO();
        retail.setId(retailId);
        return queryInfoByRedis(retail);
    }

    /**
     * 缓存分销商
     *
     * @param item
     * @return
     */
    public FastRetailPO queryInfoByRedis(FastRetailPO item) {
        if (item.getId() == null) {
            return null;
        }
        FastRetailPO retailPO = new FastRetailPO();
        String key = StaticVar.RETAIL_INFO_ID + item.getId();
        String value = RedisUtil.get(key);
        if (notBlank(value)) {
            if (StaticVar.EMPTY_FLAG.equals(value)) {
                return null;
            }
            retailPO = JsonUtil.toJavaObject(value, FastRetailPO.class);
        } else {
            item.setState(StaticVar.YES);
            FastRetailPO retail = retailMapper.queryOne(item);
            if (retail == null) {
                RedisUtil.set(key, StaticVar.EMPTY_FLAG, RedisUtil.TIME_30D);
                return null;
            } else {
                BeanUtils.copyProperties(retail, retailPO);
                retailPO.setEncryptionId(encode(retail.getId()));
                RedisUtil.set(key, JsonUtil.toString(retailPO), RedisUtil.TIME_30D);
            }
        }
        return retailPO;
    }

    public void delInfoRedis(Integer retailId) {
        String key = StaticVar.RETAIL_INFO_ID + retailId;
        RedisUtil.del(key);
    }
}
