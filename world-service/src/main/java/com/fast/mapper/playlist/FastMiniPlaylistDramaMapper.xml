<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.playlist.FastMiniPlaylistDramaMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMiniPlaylistDrama_columns">
		select t.`id`,t.`type`,t.`mini_id`,t.`playlist_id`,t.`drama_id`,t.`seq`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastMiniPlaylistDramaPO">
		<include refid="FastMiniPlaylistDrama_columns" />
	    from `fast_mini_playlist_drama` t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMiniPlaylistDramaPO" resultType="FastMiniPlaylistDramaPO">
		<include refid="FastMiniPlaylistDrama_columns" />
	    from `fast_mini_playlist_drama` t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMiniPlaylistDramaPO" resultType="FastMiniPlaylistDramaPO">
		<include refid="FastMiniPlaylistDrama_columns" />
	    from `fast_mini_playlist_drama` t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`seq`
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMiniPlaylistDramaPO" resultType="int">
		select count(*)
	    from `fast_mini_playlist_drama` t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="type != null">
			and t.`type` = #{type}
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="playlistId != null">
			and t.`playlist_id` = #{playlistId}
		</if>
		<if test="playlistIds != null and playlistIds.size() > 0">
			and t.`playlist_id` in
			<foreach collection="playlistIds" item="plid" open="(" separator="," close=")">
				#{plid}
			</foreach>
		</if>
		<if test="dramaId != null">
			and t.`drama_id` = #{dramaId}
		</if>
		<if test="seq != null">
			and t.`seq` = #{seq}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniPlaylistDramaPO">
        insert into `fast_mini_playlist_drama`
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="type != null">`type`,</if>
	        <if test="miniId != null">`mini_id`,</if>
	        <if test="playlistId != null">`playlist_id`,</if>
	        <if test="dramaId != null">`drama_id`,</if>
	        <if test="seq != null">`seq`,</if>
	        <if test="createTime != null">`create_time`,</if>
	        <if test="updateTime != null">`update_time`</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="type != null">#{type},</if>
	        <if test="miniId != null">#{miniId},</if>
	        <if test="playlistId != null">#{playlistId},</if>
	        <if test="dramaId != null">#{dramaId},</if>
	        <if test="seq != null">#{seq},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastMiniPlaylistDramaPO">
        update `fast_mini_playlist_drama`
        <set>
         	<if test="type != null" >
               `type` = #{type},
            </if>
         	<if test="miniId != null" >
               `mini_id` = #{miniId},
            </if>
         	<if test="playlistId != null" >
               `playlist_id` = #{playlistId},
            </if>
         	<if test="dramaId != null" >
               `drama_id` = #{dramaId},
            </if>
         	<if test="seq != null" >
               `seq` = #{seq},
            </if>
         	<if test="createTime != null" >
               `create_time` = #{createTime},
            </if>
         	<if test="updateTime != null" >
               `update_time` = #{updateTime},
            </if>
	    </set>
        where `id` = #{id}  
	</update>
    
	<delete id="deleteBy">
		delete from `fast_mini_playlist_drama` 
		<where>
			<if test="type != null" >
				`type` = #{type}
			</if>
			<if test="miniId != null" >
				`mini_id` = #{miniId}
			</if>
			<if test="playlistId != null" >
				`playlist_id` = #{playlistId}
			</if>
			<if test="dramaId != null" >
				`drama_id` = #{dramaId}
			</if>
			<if test="seq != null" >
				`seq` = #{seq}
			</if>
		</where>
    </delete>
    
	<delete id="deleteByMiniId">
		delete from `fast_mini_playlist_drama` where `mini_id` = #{miniId}
    </delete>
</mapper>
