<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.playlist.FastMiniPlaylistMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMiniPlaylist_columns">
		select t.`id`,t.`type`,t.`mini_id`,t.`cont_version_id`,t.`title`,t.`sub_title`,t.`remark`,t.`cover`,t.`seq`,t.`show`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastMiniPlaylistPO">
		<include refid="FastMiniPlaylist_columns" />
	    from `fast_mini_playlist` t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMiniPlaylistPO" resultType="FastMiniPlaylistPO">
		<include refid="FastMiniPlaylist_columns" />
	    from `fast_mini_playlist` t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMiniPlaylistPO" resultType="FastMiniPlaylistPO">
		<include refid="FastMiniPlaylist_columns" />
	    from `fast_mini_playlist` t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`seq` asc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMiniPlaylistPO" resultType="int">
		select count(*)
	    from `fast_mini_playlist` t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="ids != null and ids.size() > 0">
			and t.`id` in
			<foreach collection="ids" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="type != null">
			and t.`type` = #{type}
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="contVersionId != null">
			and t.`cont_version_id` = #{contVersionId}
		</if>
		<if test="title != null">
			and t.`title` = #{title}
		</if>
		<if test="subTitle != null">
			and t.`sub_title` = #{subTitle}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
		<if test="cover != null">
			and t.`cover` = #{cover}
		</if>
		<if test="seq != null">
			and t.`seq` = #{seq}
		</if>
		<if test="show != null">
			and t.`show` = #{show}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniPlaylistPO">
        insert into `fast_mini_playlist`
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="type != null">`type`,</if>
	        <if test="miniId != null">`mini_id`,</if>
	        <if test="contVersionId != null">`cont_version_id`,</if>
	        <if test="title != null">`title`,</if>
	        <if test="subTitle != null">`sub_title`,</if>
	        <if test="remark != null">`remark`,</if>
	        <if test="cover != null">`cover`,</if>
	        <if test="seq != null">`seq`,</if>
	        <if test="show != null">`show`,</if>
			<if test="creatorId != null">`creator_id`,</if>
			<if test="updatorId != null">`updator_id`,</if>
	        <if test="createTime != null">`create_time`,</if>
	        <if test="updateTime != null">`update_time`</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="type != null">#{type},</if>
	        <if test="miniId != null">#{miniId},</if>
	        <if test="contVersionId != null">#{contVersionId},</if>
	        <if test="title != null">#{title},</if>
	        <if test="subTitle != null">#{subTitle},</if>
	        <if test="remark != null">#{remark},</if>
	        <if test="cover != null">#{cover},</if>
	        <if test="seq != null">#{seq},</if>
	        <if test="show != null">#{show},</if>
			<if test="creatorId != null">#{creatorId},</if>
			<if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastMiniPlaylistPO">
        update `fast_mini_playlist`
        <set>
         	<if test="type != null" >
               `type` = #{type},
            </if>
         	<if test="miniId != null" >
               `mini_id` = #{miniId},
            </if>
			<if test="contVersionId != null">
				`cont_version_id` = #{contVersionId},
			</if>
         	<if test="title != null" >
               `title` = #{title},
            </if>
         	<if test="subTitle != null" >
               `sub_title` = #{subTitle},
            </if>
         	<if test="remark != null" >
               `remark` = #{remark},
            </if>
         	<if test="cover != null" >
               `cover` = #{cover},
            </if>
         	<if test="seq != null" >
               `seq` = #{seq},
            </if>
         	<if test="show != null" >
               `show` = #{show},
            </if>
			<if test="updatorId != null" >
				updator_id = #{updatorId},
			</if>
         	<if test="updateTime != null" >
               `update_time` = #{updateTime},
            </if>
	    </set>
        where `id` = #{id}  
	</update>
	
	<select id="getMaxSeqByMiniId" resultType="java.lang.Integer">
		select max(`seq`) from `fast_mini_playlist`
		where `mini_id` = #{miniId}
		order by `seq` desc
		limit 1
	</select>
	
	<delete id="deleteByMiniId">
		delete from `fast_mini_playlist` where `mini_id` = #{miniId}
	</delete>
</mapper>
