/*
 * Powered By fast.up
 */
package com.fast.mapper.playlist;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fast.po.playlist.FastMiniPlaylistI18nPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMiniPlaylistI18nMapper extends BaseMapper<FastMiniPlaylistI18nPO> {

    // 通过id查询单个对象
    FastMiniPlaylistI18nPO queryById(FastMiniPlaylistI18nPO entity);

    // 通过id查询单个对象
    FastMiniPlaylistI18nPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastMiniPlaylistI18nPO queryOne(FastMiniPlaylistI18nPO entity);

    // 查询全部
    List<FastMiniPlaylistI18nPO> queryList(FastMiniPlaylistI18nPO entity);

    // 查询总数
    int queryCount(FastMiniPlaylistI18nPO entity);

    // 可选新增
    int insertSelective(FastMiniPlaylistI18nPO entity);

    // 批量新增
    int insertBatch(List<FastMiniPlaylistI18nPO> list);

    // 更新
    int updateById(FastMiniPlaylistI18nPO entity);

    // 删除
    int deleteById(@Param("id") Integer id);

    int deleteByPlaylistId(@Param("playlistId") Integer playlistId);
}
