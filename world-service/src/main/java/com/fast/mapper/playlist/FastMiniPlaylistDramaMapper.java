/*
 * Powered By fast.up
 */
package com.fast.mapper.playlist;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fast.po.playlist.FastMiniPlaylistDramaPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMiniPlaylistDramaMapper extends BaseMapper<FastMiniPlaylistDramaPO> {

    // 通过id查询单个对象
    FastMiniPlaylistDramaPO queryById(FastMiniPlaylistDramaPO entity);

    // 通过id查询单个对象
    FastMiniPlaylistDramaPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastMiniPlaylistDramaPO queryOne(FastMiniPlaylistDramaPO entity);

    // 查询全部
    List<FastMiniPlaylistDramaPO> queryList(FastMiniPlaylistDramaPO entity);

    // 查询总数
    int queryCount(FastMiniPlaylistDramaPO entity);

    // 可选新增
    int insertSelective(FastMiniPlaylistDramaPO entity);

    // 更新
    int updateById(FastMiniPlaylistDramaPO entity);

    int deleteBy(FastMiniPlaylistDramaPO entity);

    int deleteByMiniId(@Param("miniId") Integer miniId);

}
