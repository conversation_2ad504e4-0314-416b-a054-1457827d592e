<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.playlist.FastMiniPlaylistI18nMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMiniPlaylistI18n_columns">
		select t.`id`,t.`playlist_id`,t.`mini_id`,t.`language_code`,t.`title`,t.`sub_title`,t.`remark`,t.`cover`,t.`del_flag`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastMiniPlaylistI18nPO">
		<include refid="FastMiniPlaylistI18n_columns" />
	    from fast_mini_playlist_i18n t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMiniPlaylistI18nPO" resultType="FastMiniPlaylistI18nPO">
		<include refid="FastMiniPlaylistI18n_columns" />
	    from fast_mini_playlist_i18n t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMiniPlaylistI18nPO" resultType="FastMiniPlaylistI18nPO">
		<include refid="FastMiniPlaylistI18n_columns" />
		,l.`seq`
	    from fast_mini_playlist_i18n t
		left join fast_language l on t.language_code = l.`code`
        <where>
        	<include refid="whereSQL" />
        </where>
		order by l.`seq`
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMiniPlaylistI18nPO" resultType="int">
		select count(*)
	    from fast_mini_playlist_i18n t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="playlistId != null">
			and t.`playlist_id` = #{playlistId}
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="languageCode != null">
			and t.`language_code` = #{languageCode}
		</if>
		<if test="title != null">
			and t.`title` = #{title}
		</if>
		<if test="subTitle != null">
			and t.`sub_title` = #{subTitle}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
		<if test="cover != null">
			and t.`cover` = #{cover}
		</if>
		<if test="delFlag != null">
			and t.`del_flag` = #{delFlag}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniPlaylistI18nPO">
        insert into fast_mini_playlist_i18n
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="playlistId != null">`playlist_id`,</if>
            <if test="miniId != null">`mini_id`,</if>
            <if test="languageCode != null">`language_code`,</if>
            <if test="title != null">`title`,</if>
            <if test="subTitle != null">`sub_title`,</if>
            <if test="remark != null">`remark`,</if>
            <if test="cover != null">`cover`,</if>
            <if test="delFlag != null">`del_flag`,</if>
            <if test="creatorId != null">`creator_id`,</if>
            <if test="updatorId != null">`updator_id`,</if>
            <if test="createTime != null">`create_time`,</if>
            <if test="updateTime != null">`update_time`</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="playlistId != null">#{playlistId},</if>
	        <if test="miniId != null">#{miniId},</if>
	        <if test="languageCode != null">#{languageCode},</if>
	        <if test="title != null">#{title},</if>
	        <if test="subTitle != null">#{subTitle},</if>
	        <if test="remark != null">#{remark},</if>
	        <if test="cover != null">#{cover},</if>
	        <if test="delFlag != null">#{delFlag},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniPlaylistI18nPO">
        insert into fast_mini_playlist_i18n (
         `playlist_id`, `mini_id`, `language_code`, `title`, `sub_title`, `remark`, `cover`, `creator_id`, `create_time`
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.playlistId}, #{item.miniId}, #{item.languageCode}, #{item.title}, #{item.subTitle}, #{item.remark}, #{item.cover}, #{item.creatorId}, #{item.createTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastMiniPlaylistI18nPO">
        update fast_mini_playlist_i18n
        <set>
         	<if test="playlistId != null" >
                `playlist_id` =#{playlistId},
            </if>
         	<if test="miniId != null" >
                `mini_id` =#{miniId},
            </if>
         	<if test="languageCode != null" >
                `language_code` =#{languageCode},
            </if>
         	<if test="title != null" >
                `title` =#{title},
            </if>
         	<if test="subTitle != null" >
                `sub_title` =#{subTitle},
            </if>
         	<if test="remark != null" >
                `remark` =#{remark},
            </if>
         	<if test="cover != null" >
                `cover` =#{cover},
            </if>
         	<if test="delFlag != null" >
                `del_flag` =#{delFlag},
            </if>
         	<if test="creatorId != null" >
                `creator_id` =#{creatorId},
            </if>
         	<if test="updatorId != null" >
                `updator_id` =#{updatorId},
            </if>
         	<if test="createTime != null" >
                `create_time` =#{createTime},
            </if>
         	<if test="updateTime != null" >
                `update_time` =#{updateTime},
            </if>
	    </set>
        where `id` = #{id}  
	</update>

	<!-- 删除 -->
	<delete id="deleteById">
		delete from `fast_mini_playlist_i18n` where `id` = #{id}
	</delete>
    
	<delete id="deleteByPlaylistId">
		delete from `fast_mini_playlist_i18n` where `playlist_id` = #{playlistId}
    </delete>
</mapper>
