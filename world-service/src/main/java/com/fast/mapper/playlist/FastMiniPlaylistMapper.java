/*
 * Powered By fast.up
 */
package com.fast.mapper.playlist;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fast.po.playlist.FastMiniPlaylistPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMiniPlaylistMapper extends BaseMapper<FastMiniPlaylistPO> {

    // 通过id查询单个对象
    FastMiniPlaylistPO queryById(FastMiniPlaylistPO entity);

    // 通过id查询单个对象
    FastMiniPlaylistPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastMiniPlaylistPO queryOne(FastMiniPlaylistPO entity);

    // 查询全部
    List<FastMiniPlaylistPO> queryList(FastMiniPlaylistPO entity);

    // 查询总数
    int queryCount(FastMiniPlaylistPO entity);

    // 可选新增
    int insertSelective(FastMiniPlaylistPO entity);

    // 更新
    int updateById(FastMiniPlaylistPO entity);

    Integer getMaxSeqByMiniId(@Param("miniId") Integer miniId);

    int deleteByMiniId(@Param("miniId") Integer miniId);

}
