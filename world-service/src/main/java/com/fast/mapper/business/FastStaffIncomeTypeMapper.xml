<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffIncomeTypeMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStaffIncomeType_columns">
		select t.`id`,t.`title`,t.`field_name`,t.`remark`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStaffIncomeTypePO">
		<include refid="FastStaffIncomeType_columns" />
	    from fast_staff_income_type t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStaffIncomeTypePO" resultType="FastStaffIncomeTypePO">
		<include refid="FastStaffIncomeType_columns" />
	    from fast_staff_income_type t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStaffIncomeTypePO" resultType="FastStaffIncomeTypePO">
		<include refid="FastStaffIncomeType_columns" />
	    from fast_staff_income_type t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id`
		<choose>
			<when test="sort!=null">${sort}</when>
			<otherwise>desc</otherwise>
		</choose>
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStaffIncomeTypePO" resultType="int">
		select count(*)
	    from fast_staff_income_type t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="title != null">
			and t.`title` = #{title}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffIncomeTypePO">
        insert into fast_staff_income_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="title != null">title,</if>
	        <if test="remark != null">remark,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="title != null">#{title},</if>
	        <if test="remark != null">#{remark},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffIncomeTypePO">
        insert into fast_staff_income_type (
         title, `remark`, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.title}, #{item.remark}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStaffIncomeTypePO">
        update fast_staff_income_type
        <set>
         	<if test="title != null" >
               title = #{title},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

    <!-- 删除 -->
	<delete id="deleteById" parameterType="FastStaffIncomeTypePO">
        delete from fast_staff_income_type  where id = #{id}
	</delete>

</mapper>
