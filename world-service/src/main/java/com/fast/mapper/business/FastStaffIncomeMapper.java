/*
 * Powered By fast.up
 */
package com.fast.mapper.business;

import com.fast.po.business.FastStaffIncomePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStaffIncomeMapper {

    // 通过id查询单个对象
    FastStaffIncomePO queryById(FastStaffIncomePO entity);

    // 通过id查询单个对象
    FastStaffIncomePO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStaffIncomePO queryOne(FastStaffIncomePO entity);

    // 查询全部
    List<FastStaffIncomePO> queryList(FastStaffIncomePO entity);

    FastStaffIncomePO queryListTotal(FastStaffIncomePO entity);

    // 查询总数
    int queryCount(FastStaffIncomePO entity);

    // 可选新增
    int insertSelective(FastStaffIncomePO entity);

    // 批量新增
    int insertBatch(List<FastStaffIncomePO> list);

    // 更新
    int updateById(FastStaffIncomePO entity);

    int updateByAudit(FastStaffIncomePO entity);

    // 删除
    int deleteById(FastStaffIncomePO entity);

}
