<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffGroupAmortizationMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStaffGroupAmortization_columns">
		select t.`id`,t.`month`,t.`group_id`,t.`group_name`,t.`manage_cost`,t.`operation_cost`,t.`technical_cost`,t.`material_cost`,t.`external_technical_cost`,t.`external_material_cost`,t.`reimbursement_cost`,t.`company_manage_cost`,t.`company_administrative_cost`,t.`creator_id`,t.`updator_id`,t.`remark`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStaffGroupAmortizationPO">
		<include refid="FastStaffGroupAmortization_columns" />
	    from fast_staff_group_amortization t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStaffGroupAmortizationPO" resultType="FastStaffGroupAmortizationPO">
		<include refid="FastStaffGroupAmortization_columns" />
	    from fast_staff_group_amortization t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStaffGroupAmortizationPO" resultType="FastStaffGroupAmortizationPO">
		<include refid="FastStaffGroupAmortization_columns" />
	    from fast_staff_group_amortization t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStaffGroupAmortizationPO" resultType="int">
		select count(*)
	    from fast_staff_group_amortization t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="month != null">
			and t.`month` = #{month}
		</if>
		<if test="groupId != null">
			and t.`group_id` = #{groupId}
		</if>
		<if test="groupName != null">
			and t.`group_name` = #{groupName}
		</if>
		<if test="manageCost != null">
			and t.`manage_cost` = #{manageCost}
		</if>
		<if test="operationCost != null">
			and t.`operation_cost` = #{operationCost}
		</if>
		<if test="technicalCost != null">
			and t.`technical_cost` = #{technicalCost}
		</if>
		<if test="materialCost != null">
			and t.`material_cost` = #{materialCost}
		</if>
		<if test="externalTechnicalCost != null">
			and t.`external_technical_cost` = #{externalTechnicalCost}
		</if>
		<if test="externalMaterialCost != null">
			and t.`external_material_cost` = #{externalMaterialCost}
		</if>
		<if test="reimbursementCost != null">
			and t.`reimbursement_cost` = #{reimbursementCost}
		</if>
		<if test="companyManageCost != null">
			and t.`company_manage_cost` = #{companyManageCost}
		</if>
		<if test="companyAdministrativeCost != null">
			and t.`company_administrative_cost` = #{companyAdministrativeCost}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffGroupAmortizationPO">
        insert into fast_staff_group_amortization
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="month != null">month,</if>
	        <if test="groupId != null">group_id,</if>
	        <if test="groupName != null">group_name,</if>
	        <if test="manageCost != null">manage_cost,</if>
	        <if test="operationCost != null">operation_cost,</if>
	        <if test="technicalCost != null">technical_cost,</if>
	        <if test="materialCost != null">material_cost,</if>
	        <if test="externalTechnicalCost != null">external_technical_cost,</if>
	        <if test="externalMaterialCost != null">external_material_cost,</if>
	        <if test="reimbursementCost != null">reimbursement_cost,</if>
	        <if test="companyManageCost != null">company_manage_cost,</if>
	        <if test="companyAdministrativeCost != null">company_administrative_cost,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="remark != null">remark,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="month != null">#{month},</if>
	        <if test="groupId != null">#{groupId},</if>
	        <if test="groupName != null">#{groupName},</if>
	        <if test="manageCost != null">#{manageCost},</if>
	        <if test="operationCost != null">#{operationCost},</if>
	        <if test="technicalCost != null">#{technicalCost},</if>
	        <if test="materialCost != null">#{materialCost},</if>
	        <if test="externalTechnicalCost != null">#{externalTechnicalCost},</if>
	        <if test="externalMaterialCost != null">#{externalMaterialCost},</if>
	        <if test="reimbursementCost != null">#{reimbursementCost},</if>
	        <if test="companyManageCost != null">#{companyManageCost},</if>
	        <if test="companyAdministrativeCost != null">#{companyAdministrativeCost},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="remark != null">#{remark},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffGroupAmortizationPO">
        insert into fast_staff_group_amortization (
         month, group_id, group_name, manage_cost, operation_cost, technical_cost, material_cost, external_technical_cost, external_material_cost, reimbursement_cost, company_manage_cost, company_administrative_cost, creator_id, updator_id, `remark`, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.month}, #{item.groupId}, #{item.groupName}, #{item.manageCost}, #{item.operationCost}, #{item.technicalCost}, #{item.materialCost}, #{item.externalTechnicalCost}, #{item.externalMaterialCost}, #{item.reimbursementCost}, #{item.companyManageCost}, #{item.companyAdministrativeCost}, #{item.creatorId}, #{item.updatorId}, #{item.remark}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertUpdateBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffGroupAmortizationPO">
        insert into fast_staff_group_amortization (
         month, group_id, group_name, manage_cost, operation_cost, technical_cost, material_cost, external_technical_cost, external_material_cost, reimbursement_cost, company_manage_cost, company_administrative_cost, creator_id, updator_id, `remark`, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.month}, #{item.groupId}, #{item.groupName}, #{item.manageCost}, #{item.operationCost}, #{item.technicalCost}, #{item.materialCost}, #{item.externalTechnicalCost}, #{item.externalMaterialCost}, #{item.reimbursementCost}, #{item.companyManageCost}, #{item.companyAdministrativeCost}, #{item.creatorId}, #{item.updatorId}, #{item.remark}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
		ON DUPLICATE KEY UPDATE manage_cost=values(manage_cost),`operation_cost`=values(operation_cost),`technical_cost`=values(technical_cost),`material_cost`=values(material_cost),`external_technical_cost`=values(external_technical_cost),`external_material_cost`=values(external_material_cost),`reimbursement_cost`=values(reimbursement_cost),`company_manage_cost`=values(company_manage_cost),`company_administrative_cost`=values(company_administrative_cost),`updator_id`=values(updator_id),`update_time`=values(update_time);
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStaffGroupAmortizationPO">
        update fast_staff_group_amortization
        <set>
         	<if test="month != null" >
               month = #{month},
            </if>
         	<if test="groupId != null" >
               group_id = #{groupId},
            </if>
         	<if test="groupName != null" >
               group_name = #{groupName},
            </if>
         	<if test="manageCost != null" >
               manage_cost = #{manageCost},
            </if>
         	<if test="operationCost != null" >
               operation_cost = #{operationCost},
            </if>
         	<if test="technicalCost != null" >
               technical_cost = #{technicalCost},
            </if>
         	<if test="materialCost != null" >
               material_cost = #{materialCost},
            </if>
         	<if test="externalTechnicalCost != null" >
               external_technical_cost = #{externalTechnicalCost},
            </if>
         	<if test="externalMaterialCost != null" >
               external_material_cost = #{externalMaterialCost},
            </if>
         	<if test="reimbursementCost != null" >
               reimbursement_cost = #{reimbursementCost},
            </if>
         	<if test="companyManageCost != null" >
               company_manage_cost = #{companyManageCost},
            </if>
         	<if test="companyAdministrativeCost != null" >
               company_administrative_cost = #{companyAdministrativeCost},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
