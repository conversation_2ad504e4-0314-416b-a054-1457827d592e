/*
 * Powered By fast.up
 */
package com.fast.mapper.business;

import com.fast.po.business.FastStaffCostIncomeMonthPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStaffCostIncomeMonthMapper {

    // 通过id查询单个对象
    FastStaffCostIncomeMonthPO queryById(FastStaffCostIncomeMonthPO entity);

    // 通过id查询单个对象
    FastStaffCostIncomeMonthPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStaffCostIncomeMonthPO queryOne(FastStaffCostIncomeMonthPO entity);

    // 查询全部
    List<FastStaffCostIncomeMonthPO> queryList(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> queryListByGroup(FastStaffCostIncomeMonthPO entity);

    // 查询总数
    int queryCount(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> mediaRechargeData(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> mediaRechargeOtherData(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> launchGroupDataList(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> launchGroupWeekDataList(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> launchGroupMediaWeekDataList(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> launchGroupMediaDataList(FastStaffCostIncomeMonthPO entity);

    FastStaffCostIncomeMonthPO launchGroupMediaDataTotal(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> launchGroupMediaMonthDataList(FastStaffCostIncomeMonthPO entity);

    FastStaffCostIncomeMonthPO launchGroupMediaMonthDataTotal(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> launchGroupStaffMonthDataList(FastStaffCostIncomeMonthPO entity);

    FastStaffCostIncomeMonthPO launchGroupStaffMonthDataTotal(FastStaffCostIncomeMonthPO entity);

    // 分销
    List<FastStaffCostIncomeMonthPO> launchRetailMonthList(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> launchRetailWeekList(FastStaffCostIncomeMonthPO entity);

    FastStaffCostIncomeMonthPO launchRetailMonthTotal(FastStaffCostIncomeMonthPO entity);

    Map<String, Object> mediaRechargeOverview(FastStaffCostIncomeMonthPO entity);

    List<Map<String, Object>> mediaRechargeMonthList(FastStaffCostIncomeMonthPO entity);

    Map<String, Object> mediaRechargeMonthTotal(FastStaffCostIncomeMonthPO entity);

    List<FastStaffCostIncomeMonthPO> launchGroupUserList(FastStaffCostIncomeMonthPO entity);

    // 批量新增
    int insertUpdateBatch(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    int updateGroup(FastStaffCostIncomeMonthPO entity);
}
