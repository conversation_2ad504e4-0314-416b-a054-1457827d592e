<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffCostIncomeMonthMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStaffCostIncomeMonth_columns">
		select t.`month`,t.`user_id`,t.`media_type`,t.`user_group_id`,t.`user_group_name`,t.`cost`,t.`cash_cost`,t.`cash_cost_back`,t.`money_recharge`,t.`money_profit`,t.`virtual_recharge`,t.`virtual_profit`,t.`virtual_refund`,t.`normal_recharge`,t.`normal_profit`,t.`normal_refund`,t.`create_time`,t.`update_time`
	</sql>
	<sql id="tableTmp">
		<include refid="FastStaffCostIncomeMonth_columns" />,
		if(i.income is null, 0, i.income) staffIncome,
		b.virtual_ratio,b.normal_ratio,
		c.company_ratio,c.staff_ratio,
		if(t.media_type!=501,
			if(money_recharge>0,
			money_recharge - cash_cost_back - money_recharge *(virtual_recharge / money_recharge * virtual_ratio + normal_recharge / money_recharge *(0.01+0.99 * normal_ratio)),
			money_recharge - cash_cost_back),
			if(t.user_id=0,0,if(money_recharge>0, money_profit-cash_cost_back, money_recharge - cash_cost_back))
		) statementMoney,
		if(t.media_type=501 and t.user_id>0,(select if(sum(if( user_id > 0, 1, 0))>0,sum(if( user_id = 0 and money_profit > 0, money_profit, 0 ))/sum(if( user_id > 0, 1, 0)),0) money from fast_staff_cost_income_month where `month` = t.`month` and media_type = 501), 0) otherRecharge
		FROM `fast_staff_cost_income_month` t
		left join fast_setting_media_settlement b on t.media_type=b.media_type and b.`effect_month` = t.`month` and b.state=0
		left join fast_setting_media_profit c on t.media_type=c.media_type and c.`effect_month` = t.`month` and c.state=0
		left join (
			<include refid="userIncomeTable" />
		) i on t.`month` = i.`month` and t.user_id=i.user_id and t.media_type=i.media_type
		<where>
			<include refid="whereSQL" />
		</where>
	</sql>

	<sql id="userIncomeTable">
		select i.`user_id`, i.`month`, im.media_type, round(income*(mediaRecharge/totalRecharge), 4) income
		from (
			select `user_id`, `month`, sum(income) income from fast_staff_income i
			<where> i.state=1 <include refid="userWhereSQL" /></where>
			group by `month`,`user_id`
		) i left join (
			select `month`, `user_id`, i.media_type,
		    	(select sum(money_recharge-cash_cost_back-money_recharge*(0.2*0.15+0.8*0.165))
				from fast_staff_cost_income_month where `month`=i.`month` and user_id=i.user_id and media_type=i.media_type) totalRecharge,
				CASE i.media_type
					WHEN '1' THEN money_recharge-cash_cost_back-money_recharge*(0.2*b.normal_ratio+0.8*b.virtual_ratio)
					WHEN '2' THEN money_recharge-cash_cost_back-money_recharge*(0.2*b.normal_ratio+0.8*b.virtual_ratio)
					WHEN '4' THEN money_recharge-cash_cost_back-money_recharge*(0.2*b.normal_ratio+0.8*b.virtual_ratio)
					WHEN '5' THEN money_recharge-cash_cost_back-money_recharge*(0.2*b.normal_ratio+0.8*b.virtual_ratio)
					WHEN '102' THEN money_recharge-cash_cost_back-money_recharge*(0.2*b.normal_ratio+0.8*b.virtual_ratio)
					WHEN '501' THEN money_recharge-cash_cost_back-money_recharge*(0.2*b.normal_ratio+0.8*b.virtual_ratio)
					WHEN '502' THEN money_recharge-cash_cost_back-money_recharge*(0.2*b.normal_ratio+0.8*b.virtual_ratio)
				ELSE 0 END AS mediaRecharge
			from fast_staff_cost_income_month i
			left join fast_setting_media_settlement b on i.media_type=b.media_type and b.`effect_month` = i.`month` and b.state=0
			<where><include refid="userWhereSQL"/></where>
			group by `month`,`user_id`, i.media_type
		) im on i.user_id=im.user_id and i.`month`=im.`month`
		<where><include refid="userWhereSQL" /></where>
		group by `month`,`user_id`,im.media_type
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStaffCostIncomeMonthPO">
		<include refid="FastStaffCostIncomeMonth_columns" />
	    from fast_staff_cost_income_month t
        <where>
	        t.`month` = #{month} and
	        t.`user_id` = #{userId} and
	        t.`media_type` = #{mediaType}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStaffCostIncomeMonthPO" resultType="FastStaffCostIncomeMonthPO">
		<include refid="FastStaffCostIncomeMonth_columns" />
	    from fast_staff_cost_income_month t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStaffCostIncomeMonthPO" resultType="FastStaffCostIncomeMonthPO">
		<include refid="FastStaffCostIncomeMonth_columns" />
	    from fast_staff_cost_income_month t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`create_time` desc
	</select>

	<!--分页查询集合  -->
	<select id="queryListByGroup" parameterType="FastStaffCostIncomeMonthPO" resultType="FastStaffCostIncomeMonthPO">
		<include refid="FastStaffCostIncomeMonth_columns" />
		from fast_staff_cost_income_month t
		join fast_user u on t.user_id=u.id
		join fast_retail r on r.id=u.retail_id
		where `month`=#{month}
			and t.user_group_id = #{userGroupId}
			and r.retail_type in (1,2)
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStaffCostIncomeMonthPO" resultType="int">
		select count(*)
	    from fast_staff_cost_income_month t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="userWhereSQL">
		<if test="month != null">
			and i.`month` = #{month}
		</if>
		<if test="monthStart != null">
			and i.`month` &gt;= #{monthStart}
		</if>
		<if test="monthEnd != null">
			and i.`month` &lt;= #{monthEnd}
		</if>
		<if test="userId != null">
			and i.`user_id` = #{userId}
		</if>
	</sql>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="month != null">
			and t.`month` = #{month}
		</if>
		<if test="monthStart != null">
			and t.`month` &gt;= #{monthStart}
		</if>
		<if test="monthEnd != null">
			and t.`month` &lt;= #{monthEnd}
		</if>
		<if test="userId != null">
			and t.`user_id` = #{userId}
		</if>
		<if test="mediaType != null">
			and t.`media_type` = #{mediaType}
		</if>
		<if test="userGroupId != null">
			and t.`user_group_id` = #{userGroupId}
		</if>
		<if test="userGroupName != null">
			and t.`user_group_name` = #{userGroupName}
		</if>
		<if test="cost != null">
			and t.`cost` = #{cost}
		</if>
		<if test="cashCost != null">
			and t.`cash_cost` = #{cashCost}
		</if>
		<if test="moneyRecharge != null">
			and t.`money_recharge` = #{moneyRecharge}
		</if>
		<if test="moneyProfit != null">
			and t.`money_profit` = #{moneyProfit}
		</if>
		<if test="virtualRecharge != null">
			and t.`virtual_recharge` = #{virtualRecharge}
		</if>
		<if test="virtualProfit != null">
			and t.`virtual_profit` = #{virtualProfit}
		</if>
		<if test="normalRecharge != null">
			and t.`normal_recharge` = #{normalRecharge}
		</if>
		<if test="normalProfit != null">
			and t.`normal_profit` = #{normalProfit}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
		<if test="contentType != null">
			and t.`content_type` = #{contentType}
		</if>
	</sql>

	<!-- 获取自带投分销类型充值数据 -->
	<select id="mediaRechargeData" resultType="FastStaffCostIncomeMonthPO">
		select t.`month`, r.retail_type, sum(t.`money_profit`) moneyProfit
		from fast_staff_cost_income_month t
			 left join fast_user u on t.user_id=u.id
			 left join fast_retail r on r.id=u.retail_id
		where t.`month` = #{month}
		  and t.media_type &lt; 100
		  and r.retail_type in (1,2,3)
		group by t.`month`,r.`retail_type`
	</select>

	<!-- 获取媒体类型充值数据 -->
	<select id="mediaRechargeOtherData" resultType="FastStaffCostIncomeMonthPO">
		select t.`month`,t.media_type, sum(t.`money_profit`) moneyProfit
		from fast_staff_cost_income_month t
		where t.`month` = #{month}
		  and t.media_type in (${mediaTypes})
		group by t.`month`,t.`media_type`
	</select>

	<!-- 概览月数据-->
	<select id="mediaRechargeOverview" resultType="Map">
		select `month`,DATE_FORMAT(concat(`month`, '01'),'%Y-%m') monthStr,
			   sum(money_profit) moneyProfit, sum(money_recharge) moneyRecharge,
			   sum(if(type=1, money_profit, 0)) usProfit,
			   (select sum(money_profit) from fast_staff_retail_recharge_month where `month`=a.`month` and retail_type=2) agentProfit,
			   sum(if(type=3, money_profit, 0)) aloneProfit,
			   sum(if(type=102, money_profit, 0)) douyinMountProfit,
			   sum(if(type=501, money_profit, 0)) nativeProfit,
			   sum(if(type=502, money_profit, 0)) kuaishouMountProfit
		from (
			 select
				 t.`month`,
				 if(t.media_type in (102,501,502), t.media_type, r.`retail_type`) type,
				 t.`money_recharge`,
				 t.`money_profit`
			 from
				 fast_staff_cost_income_month t
					 left join fast_user u on t.user_id = u.id
					 left join fast_retail r on r.id = u.retail_id
			 where t.`month` = #{month}
		) a where type is not null
	</select>

	<!-- 概览月列表数据充值数据 -->
	<select id="mediaRechargeMonthList" resultType="Map">
		select `month`,DATE_FORMAT(concat(`month`, '01'),'%Y-%m') monthStr,
			   sum(money_profit) moneyProfit, sum(money_recharge) moneyRecharge,
			   sum(if(type=1, money_profit, 0)) usProfit,
			   (select sum(money_profit) from fast_staff_retail_recharge_month where `month`=a.`month` and retail_type=2) agentProfit,
			   sum(if(type=3, money_profit, 0)) aloneProfit,
			   sum(if(type=102, money_profit, 0)) douyinMountProfit,
			   sum(if(type=501, money_profit, 0)) nativeProfit,
			   sum(if(type=502, money_profit, 0)) kuaishouMountProfit
		from (
			 select
				 t.`month`,
				 if(t.media_type in (102,501,502), t.media_type, r.`retail_type`) type,
				 t.`money_recharge`,
				 t.`money_profit`
			 from
				 fast_staff_cost_income_month t
					 left join fast_user u on t.user_id = u.id
					 left join fast_retail r on r.id = u.retail_id
			 where t.`month` &gt;= #{monthStart}
			   and t.`month` &lt;= #{monthEnd}
		<if test="contentType != null">
			and t.content_type = #{contentType}
		</if>
			 ) a where type is not null
		group by `month`
		order by `month` desc
	</select>

	<!-- 概览月列表数据充值数据 -->
	<select id="mediaRechargeMonthTotal" resultType="Map">
		select 0 'month','累计' monthStr,
			   sum(money_profit) moneyProfit, sum(money_recharge) moneyRecharge,
			   sum(if(type=1, money_profit, 0)) usProfit,
			   (select sum(money_profit) from fast_staff_retail_recharge_month where `month`=a.`month` and retail_type=2) agentProfit,
			   sum(if(type=3, money_profit, 0)) aloneProfit,
			   sum(if(type=102, money_profit, 0)) douyinMountProfit,
			   sum(if(type=501, money_profit, 0)) nativeProfit,
			   sum(if(type=502, money_profit, 0)) kuaishouMountProfit
		from (
			 select
				 t.`month`,
				 if(t.media_type in (102,501,502), t.media_type, r.`retail_type`) type,
				 t.`money_recharge`,
				 t.`money_profit`
			 from
				 fast_staff_cost_income_month t
					 left join fast_user u on t.user_id = u.id
					 left join fast_retail r on r.id = u.retail_id
			 where t.`month` &gt;= #{monthStart}
			   and t.`month` &lt;= #{monthEnd}
		<if test="contentType != null">
			and t.content_type = #{contentType}
		</if>
		 ) a where type is not null
	</select>

	<!-- 周列表数据充值数据 -->
	<select id="launchGroupWeekDataList" resultType="FastStaffCostIncomeMonthPO">
		select user_group_id, user_group_name,
			sum(money_recharge) weekMoneyRecharge,
			sum(money_profit) weekMoneyProfit,
			sum(cash_cost) weekCashCost
		from `fast_staff_cost_income_day`
		where `date` &gt;= #{dateStart}
		  and `date` &lt;= #{dateEnd}
		<if test="contentType != null">
			and content_type = #{contentType}
		</if>
		<if test="mediaType != null">
			and `media_type` = #{mediaType}
		</if>
		group by user_group_id
	</select>

	<!-- 概览月列表数据充值数据 -->
	<select id="launchGroupDataList" resultType="FastStaffCostIncomeMonthPO">
		SELECT
			user_group_id, media_type, user_group_name,
			count(distinct t.user_id) user_group_nums,
			sum(money_recharge) moneyRecharge,
			sum(money_profit) moneyProfit,
			sum(virtual_recharge) virtualRecharge,
			sum(virtual_profit) virtualProfit,
			sum(normal_recharge) normalRecharge,
			sum(normal_profit) normalProfit,
			sum(cash_cost) cashCost,
			sum(cash_cost_back) cashCostBack,
			sum(staffIncome) staffIncome,
			sum(if(i0.income is null,0,i0.income)) staffIncomePreMonth,
			round(sum(statementMoney)+sum(otherRecharge),4) statementMoney,
			round((sum(statementMoney)+sum(otherRecharge))*company_ratio,4) companyProfit,
			round((sum(statementMoney)+sum(otherRecharge))*staff_ratio,4) staffProfit
		from (<include refid="tableTmp"></include>) t
		left join (
			select user_id,sum(income) income from fast_staff_income
			where `month`=DATE_FORMAT(DATE_SUB(concat(#{month},"01"), INTERVAL 1 MONTH),'%Y%m') and state=1
			group by user_id
		) i0 on t.user_id=i0.user_id
		left join fast_user u on t.user_id=u.id
		left join fast_retail r on r.id=u.retail_id
		where t.`month`=#{month}
		  and t.media_type not in (102,502)
		<if test="mediaType != null">
			and t.`media_type` = #{mediaType}
		</if>
		<if test="retailTypes != null">
			and r.retail_type in (${retailTypes})
			<if test="mediaType == null">
				or (t.media_type=501 and r.retail_type is null)
			</if>
		</if>
		group by user_group_id
		order by moneyRecharge desc
	</select>

	<!-- 周列表数据充值数据 -->
	<select id="launchGroupMediaWeekDataList" resultType="FastStaffCostIncomeMonthPO">
		select media_type, user_group_name,
			sum(money_recharge) weekMoneyRecharge,
			sum(money_profit) weekMoneyProfit,
			sum(cash_cost) weekCashCost
		from `fast_staff_cost_income_day`
		where `date` &gt;= #{dateStart}
		  and `date` &lt;= #{dateEnd}
		<if test="contentType != null">
			and content_type = #{contentType}
		</if>
		<if test="userGroupId != null">
		  and user_group_id = #{userGroupId}
		</if>
		<if test="userId != null">
		  and `user_id` = #{userId}
		</if>
		group by media_type
	</select>

	<!-- 投放组媒体列表数据充值数据 -->
	<select id="launchGroupMediaDataList" resultType="FastStaffCostIncomeMonthPO">
		SELECT
			media_type,
			sum(money_recharge) moneyRecharge,
			sum(money_profit) moneyProfit,
			sum(virtual_recharge) virtualRecharge,
			sum(normal_recharge) normalRecharge,
			sum(cash_cost) cashCost,
			sum(cash_cost_back) cashCostBack,
			sum(staffIncome) staffIncome,
			sum(if(i0.income is null,0,i0.income)) staffIncomePreMonth,
			round(sum(statementMoney)+sum(otherRecharge),4) statementMoney,
			round((sum(statementMoney)+sum(otherRecharge))*company_ratio,4) companyProfit,
			round((sum(statementMoney)+sum(otherRecharge))*staff_ratio,4) staffProfit
		from (<include refid="tableTmp"></include>) t
		left join (
			select user_id,sum(income) income from fast_staff_income
			where `month`=DATE_FORMAT(DATE_SUB(concat(#{month},"01"), INTERVAL 1 MONTH),'%Y%m') and state=1
			group by user_id
		) i0 on t.user_id=i0.user_id
		<if test="retailTypes != null">
			left join fast_user u on t.user_id=u.id
			left join fast_retail r on r.id=u.retail_id
			where r.retail_type in (${retailTypes}) or (t.media_type=501 and r.retail_type is null)
		</if>
		group by media_type
		order by moneyRecharge desc
	</select>

	<!-- 投放组媒体列表数据充值数据 -->
	<select id="launchGroupMediaDataTotal" resultType="FastStaffCostIncomeMonthPO">
		SELECT
			-1 media_type,
			sum(money_recharge) moneyRecharge,
			sum(money_profit) moneyProfit,
			sum(virtual_recharge) virtualRecharge,
			sum(normal_recharge) normalRecharge,
			sum(cash_cost) cashCost,
			sum(cash_cost_back) cashCostBack,
			sum(staffIncome) staffIncome,
			sum(if(i0.income is null,0,i0.income)) staffIncomePreMonth,
			round(sum(statementMoney)+sum(otherRecharge),4) statementMoney,
			round((sum(statementMoney)+sum(otherRecharge))*company_ratio,4) companyProfit,
			round((sum(statementMoney)+sum(otherRecharge))*staff_ratio,4) staffProfit
		from (<include refid="tableTmp"></include>) t
		left join (
			select user_id,sum(income) income from fast_staff_income
			where `month`=DATE_FORMAT(DATE_SUB(concat(#{month},"01"), INTERVAL 1 MONTH),'%Y%m') and state=1
			group by user_id
		) i0 on t.user_id=i0.user_id
		<if test="retailTypes != null">
			left join fast_user u on t.user_id=u.id
			left join fast_retail r on r.id=u.retail_id
			where r.retail_type in (${retailTypes}) or (t.media_type=501 and r.retail_type is null)
		</if>
	</select>

	<!-- 投放月列表数据充值数据 -->
	<select id="launchGroupMediaMonthDataList" resultType="FastStaffCostIncomeMonthPO">
		SELECT
			`month`,
		    DATE_FORMAT(concat(t.`month`, '01'),'%Y-%m') monthStr,
			sum(money_recharge) moneyRecharge,
			sum(money_profit) moneyProfit,
			sum(virtual_recharge) virtualRecharge,
			sum(normal_recharge) normalRecharge,
			sum(cash_cost) cashCost,
			sum(cash_cost_back) cashCostBack,
			sum(staffIncome) staffIncome,
			round(sum(statementMoney)+sum(otherRecharge),4) statementMoney,
			round((sum(statementMoney)+sum(otherRecharge))*company_ratio,4) companyProfit,
			round((sum(statementMoney)+sum(otherRecharge))*staff_ratio,4) staffProfit
		from (<include refid="tableTmp"></include>) t
		<if test="retailTypes != null">
			left join fast_user u on t.user_id=u.id
			left join fast_retail r on r.id=u.retail_id
			where r.retail_type in (${retailTypes}) or (t.media_type=501 and r.retail_type is null)
		</if>
		group by `month`
	</select>

	<!-- 投放月列表数据充值数据汇总 -->
	<select id="launchGroupMediaMonthDataTotal" resultType="FastStaffCostIncomeMonthPO">
		SELECT
			0 `month`,
			'累计' monthStr,
			sum(money_recharge) moneyRecharge,
			sum(money_profit) moneyProfit,
			sum(virtual_recharge) virtualRecharge,
			sum(normal_recharge) normalRecharge,
			sum(cash_cost) cashCost,
			sum(cash_cost_back) cashCostBack,
			sum(staffIncome) staffIncome,
			round(sum(statementMoney)+sum(otherRecharge), 4) statementMoney,
			round((sum(statementMoney)+sum(otherRecharge))*company_ratio, 4) companyProfit,
			round((sum(statementMoney)+sum(otherRecharge))*staff_ratio, 4) staffProfit
		from (<include refid="tableTmp"></include>) t
		<if test="retailTypes != null">
			left join fast_user u on t.user_id=u.id
			left join fast_retail r on r.id=u.retail_id
			where r.retail_type in (${retailTypes}) or (t.media_type=501 and r.retail_type is null)
		</if>
	</select>

	<!-- 投放月列表数据充值数据 -->
	<select id="launchGroupStaffMonthDataList" resultType="FastStaffCostIncomeMonthPO">
		select t.*,u.user_name from (<include refid="tableTmp"></include>) t
		left join fast_user u on t.user_id=u.id
	</select>

	<!-- 投放月列表数据充值数据汇总 -->
	<select id="launchGroupStaffMonthDataTotal" resultType="FastStaffCostIncomeMonthPO">
		select
		    0 'userId', '累计' userName,
			sum(money_recharge) moneyRecharge,
			sum(money_profit) moneyProfit,
			sum(virtual_recharge) virtualRecharge,
			sum(normal_recharge) normalRecharge,
			sum(cash_cost) cashCost,
			sum(cash_cost_back) cashCostBack,
			sum(staffIncome) staffIncome,
			round(sum(statementMoney)+sum(otherRecharge),4) statementMoney,
			round((sum(statementMoney)+sum(otherRecharge))*company_ratio,4) companyProfit,
			round((sum(statementMoney)+sum(otherRecharge))*staff_ratio,4) staffProfit
		from (<include refid="tableTmp"></include>) t
	</select>

	<!-- 投放数据-分销 -->
	<!-- 经营数据-分销月-数据 -->
	<select id="launchRetailMonthUserList" resultType="FastStaffCostIncomeMonthPO">
		select *,(moneyProfit-moneyRefund-retailProfit-processCost) companyProfit
		from (
		    SELECT
				<choose>
					<when test="groupField != null">
						${groupField},
					</when>
					<otherwise>
						t.month,DATE_FORMAT(concat(`month`, '01'),'%Y-%m') monthStr,
						u.retail_id,r.retail_name,
					</otherwise>
				</choose>
				sum(money_recharge) moneyRecharge,
				sum(money_profit) moneyProfit,
				sum(virtual_recharge) virtualRecharge,
				sum(virtual_profit) virtualProfit,
				sum(virtual_refund) virtualRefund,
				sum(normal_recharge) normalRecharge,
				sum(normal_profit) normalProfit,
				sum(normal_refund) normalRefund,
				sum(virtual_refund+normal_refund) moneyRefund,
				sum((normal_profit-normal_refund)*0.85+(virtual_recharge-virtual_refund)*0.8) retailProfit,
				sum(money_profit)*0.01 processCost
			FROM  (
				(select `month`,media_type,user_id,money_recharge,money_profit,virtual_recharge,virtual_profit,virtual_refund,
				normal_recharge,normal_profit,normal_refund from `fast_staff_cost_income_month` a) UNION all
				(select DATE_FORMAT(`date`,'%Y%m') `month`,media_type,user_id,money_recharge,money_profit,virtual_recharge,virtual_profit,virtual_refund,
				normal_recharge,normal_profit,normal_refund
				from fast_staff_cost_income_day a
				where `date`=DATE_FORMAT(NOW(),'%Y-%m-%d'))
			 ) t
			 join fast_user u on t.user_id=u.id
			 join fast_retail r on u.retail_id=r.id
			where r.retail_type = 3
			<if test="retailId != null">
			  and r.`id` = #{retailId}
			</if>
		    <if test="month != null">
			  and t.`month` = #{month}
			</if>
			<if test="monthStart != null">
			  and t.`month` &gt;= #{monthStart}
			</if>
			<if test="monthEnd != null">
			  and t.`month` &lt;= #{monthEnd}
			</if>
			<if test="mediaType != null">
			  and t.`media_type` = #{mediaType}
			</if>
		    <choose>
			  <when test="groupField != null">
				  group by ${groupField}
			  </when>
			  <otherwise>
				group by u.retail_id,t.month
			  </otherwise>
		   </choose>
		) t
		order by moneyRecharge desc
	</select>

	<select id="launchRetailMonthUserTotal" resultType="FastStaffCostIncomeMonthPO">
		select *,(moneyProfit-moneyRefund-retailProfit-processCost) companyProfit
		from (
			SELECT
				0 retailId, '累计' retailName,
				sum(money_recharge) moneyRecharge,
				sum(money_profit) moneyProfit,
				sum(virtual_recharge) virtualRecharge,
				sum(virtual_profit) virtualProfit,
				sum(virtual_refund) virtualRefund,
				sum(normal_recharge) normalRecharge,
				sum(normal_profit) normalProfit,
				sum(normal_refund) normalRefund,
				sum(virtual_refund+normal_refund) moneyRefund,
				sum((normal_profit-normal_refund)*0.85+(virtual_recharge-virtual_refund)*0.8) retailProfit,
				sum(money_profit)*0.01 processCost
				FROM (
				(select `month`,media_type,user_id,money_recharge,money_profit,virtual_recharge,virtual_profit,virtual_refund,
				normal_recharge,normal_profit,normal_refund, '' as date from `fast_staff_cost_income_month` a) UNION all
				(select DATE_FORMAT(`date`,'%Y%m') `month`,media_type,user_id,money_recharge,money_profit,virtual_recharge,virtual_profit,virtual_refund,
				normal_recharge,normal_profit,normal_refund,`date`
				from fast_staff_cost_income_day a
				where `date`=DATE_FORMAT(NOW(),'%Y-%m-%d'))
			) t
			join fast_user u on t.user_id=u.id
			join fast_retail r on u.retail_id=r.id
			where r.retail_type=3
			<if test="retailId != null">
				and r.`id` = #{retailId}
			</if>
			<if test="month != null">
				and t.`month` = #{month}
			</if>
			<if test="monthStart != null">
				and t.`month` &gt;= #{monthStart}
			</if>
			<if test="monthEnd != null">
				and t.`month` &lt;= #{monthEnd}
			</if>
			<if test="mediaType != null">
				and t.`media_type` = #{mediaType}
			</if>
		) t
	</select>

	<!-- 经营数据-分销周-数据 -->
	<select id="launchRetailWeekUserList" resultType="FastStaffCostIncomeMonthPO">
		SELECT u.retail_id,r.retail_name,
			   sum(money_recharge) weekMoneyRecharge,
			   sum(money_profit) weekMoneyProfit
		FROM `fast_staff_cost_income_day` t
				 join fast_user u on t.user_id=u.id
				 join fast_retail r on u.retail_id=r.id
		where t.`date` &gt;= #{dateStart}
		  and t.`date` &lt;= #{dateEnd}
		  and r.retail_type=3
		group by u.retail_id
	</select>

	<select id="launchRetailMonthList" resultType="FastStaffCostIncomeMonthPO">
		select *,(moneyProfit-moneyRefund-retailProfit-processCost) companyProfit
		from (
		    SELECT
				<choose>
					<when test="groupField != null">
						${groupField},
						<if test="groupField = 't.`month`'">
							DATE_FORMAT(concat(`month`, '01'),'%Y-%m') monthStr,
						</if>
					</when>
					<otherwise>
						t.month,DATE_FORMAT(concat(`month`, '01'),'%Y-%m') monthStr,
						t.retail_id,r.retail_name,
					</otherwise>
				</choose>
				sum(money_recharge) moneyRecharge,
				sum(money_profit) moneyProfit,
				sum(virtual_recharge) virtualRecharge,
				sum(virtual_profit) virtualProfit,
				sum(virtual_refund) virtualRefund,
				sum(normal_recharge) normalRecharge,
				sum(normal_profit) normalProfit,
				sum(normal_refund) normalRefund,
				sum(virtual_refund+normal_refund) moneyRefund,
				sum((CAST(normal_profit as SIGNED)-CAST(normal_refund AS SIGNED))*0.85+(CAST(virtual_recharge as SIGNED)-CAST(virtual_refund AS SIGNED))*0.8) retailProfit,
				sum(money_profit)*0.01 processCost
			FROM  (
				(select `month`,retail_id,media_type,money_recharge,money_profit,virtual_recharge,virtual_profit,virtual_refund,
				normal_recharge,normal_profit,normal_refund from `fast_staff_retail_recharge_month` a
				<where>
					<if test="contentType != null">
						and a.content_type = #{contentType}
					</if>
				</where>
		) UNION all
				(select DATE_FORMAT(`date`,'%Y%m') `month`,retail_id,media_type,money_recharge,money_profit,virtual_recharge,virtual_profit,virtual_refund,
				normal_recharge,normal_profit,normal_refund
				from fast_staff_retail_recharge_day a
				where `date`=DATE_FORMAT(NOW(),'%Y-%m-%d')
				<if test="contentType != null">
					and a.content_type = #{contentType}
				</if>
		)
			 ) t
			 join fast_retail r on t.retail_id=r.id
			where r.retail_type = 3
			<if test="retailId != null">
			  and r.`id` = #{retailId}
			</if>
		    <if test="month != null">
			  and t.`month` = #{month}
			</if>
			<if test="monthStart != null">
			  and t.`month` &gt;= #{monthStart}
			</if>
			<if test="monthEnd != null">
			  and t.`month` &lt;= #{monthEnd}
			</if>
			<if test="mediaType != null">
			  and t.`media_type` = #{mediaType}
			</if>
		    <choose>
			  <when test="groupField != null">
				  group by ${groupField}
			  </when>
			  <otherwise>
				group by t.retail_id,t.month
			  </otherwise>
		   </choose>
		) t
		order by moneyRecharge desc
	</select>

	<select id="launchRetailMonthTotal" resultType="FastStaffCostIncomeMonthPO">
		select *,(moneyProfit-moneyRefund-retailProfit-processCost) companyProfit
		from (
			SELECT
				0 retailId, '累计' retailName,
				sum(money_recharge) moneyRecharge,
				sum(money_profit) moneyProfit,
				sum(virtual_recharge) virtualRecharge,
				sum(virtual_profit) virtualProfit,
				sum(virtual_refund) virtualRefund,
				sum(normal_recharge) normalRecharge,
				sum(normal_profit) normalProfit,
				sum(normal_refund) normalRefund,
				sum(virtual_refund+normal_refund) moneyRefund,
				sum((CAST(normal_profit AS SIGNED) - CAST(normal_refund AS SIGNED))*0.85+(virtual_recharge-virtual_refund)*0.8) retailProfit,
				sum(money_profit)*0.01 processCost
			FROM (
				(select `month`,`retail_id`,media_type,money_recharge,money_profit,virtual_recharge,virtual_profit,virtual_refund,
				normal_recharge,normal_profit,normal_refund, '' as date from `fast_staff_retail_recharge_month` a
		<where>
			<if test="contentType != null">
				and a.content_type = #{contentType}
			</if>
		</where>
		) UNION all
				(select DATE_FORMAT(`date`,'%Y%m') `month`,`retail_id`,media_type,money_recharge,money_profit,virtual_recharge,virtual_profit,virtual_refund,
				normal_recharge,normal_profit,normal_refund,`date`
				from fast_staff_retail_recharge_day a
				where `date`=DATE_FORMAT(NOW(),'%Y-%m-%d')
				<if test="contentType != null">
					and a.content_type = #{contentType}
				</if>
				)
			) t
			join fast_retail r on t.retail_id=r.id
			where r.retail_type=3
			<if test="retailId != null">
				and r.`id` = #{retailId}
			</if>
			<if test="month != null">
				and t.`month` = #{month}
			</if>
			<if test="monthStart != null">
				and t.`month` &gt;= #{monthStart}
			</if>
			<if test="monthEnd != null">
				and t.`month` &lt;= #{monthEnd}
			</if>
			<if test="mediaType != null">
				and t.`media_type` = #{mediaType}
			</if>
		) t
	</select>

	<!-- 经营数据-分销周-数据 -->
	<select id="launchRetailWeekList" resultType="FastStaffCostIncomeMonthPO">
		SELECT t.retail_id,r.retail_name,
			sum(money_recharge) weekMoneyRecharge,
			sum(money_profit) weekMoneyProfit
		FROM `fast_staff_retail_recharge_day` t
		 join fast_retail r on t.retail_id=r.id
		where t.`date` &gt;= #{dateStart}
		  and t.`date` &lt;= #{dateEnd}
		  and r.retail_type=3
		<if test="contentType != null">
			and t.content_type = #{contentType}
		</if>
		group by t.retail_id
	</select>


	<!-- 经营数据-分销周-数据 -->
	<select id="launchGroupUserList" resultType="FastStaffCostIncomeMonthPO">
		SELECT r.id retail_id,r.retail_name,r.retail_type,t.user_id,u.user_name,t.user_group_id,t.user_group_name
		FROM (
		    select user_id,user_group_id,user_group_name
		    from `fast_staff_cost_income_month`
		    <where>
				<if test="month != null">
					and `month`=#{month}
				</if>
				<if test="year != null">
					and `month` like '${year}%'
				</if>
				<if test="userGroupId != null">
					and user_group_id=#{userGroupId}
				</if>
			</where>
		    group by user_id
		) t
		join fast_user u on u.id=t.user_id
		join fast_retail r on u.retail_id=r.id
		<where>
			<if test="retailTypes != null">
				and retail_type in (${retailTypes})
			</if>
		</where>
	</select>

	<!-- 批量新增 -->
	<insert id="insertUpdateBatch">
		insert into fast_staff_cost_income_month(
			 `month`,user_id,media_type,user_group_id,user_group_name,content_type,cost,cash_cost,cash_cost_back,money_recharge, money_profit, virtual_recharge, virtual_profit, virtual_refund, normal_recharge, normal_profit, normal_refund,create_time,update_time
		) (SELECT DATE_FORMAT(#{startDate},'%Y%m') `month`,user_id,media_type,user_group_id,user_group_name,content_type
				,sum(cost) cost
				,sum(cash_cost) cash_cost
				,sum(cash_cost/(1+back_ratio/100)) cash_cost_back
				,sum(money_recharge) money_recharge
				,sum(money_profit) money_profit
				,sum(virtual_recharge) virtual_recharge
				,sum(virtual_profit) virtual_profit
				,sum(virtual_refund) virtual_refund
				,sum(normal_recharge) normal_recharge
				,sum(normal_profit) normal_profit
				,sum(normal_refund) normal_refund
				,NOW() create_time
				,NOW() update_time
		   FROM `fast_staff_cost_income_day`
		   where `date` &gt;= #{startDate} and `date` &lt;= #{endDate}
		   group by user_id,media_type,content_type)
			ON DUPLICATE KEY UPDATE user_group_id=values(user_group_id), user_group_name=values(user_group_name), money_recharge=values(money_recharge),`money_profit`=values(money_profit),`virtual_recharge`=values(virtual_recharge), `virtual_profit`=values(virtual_profit), `virtual_refund`=values(virtual_refund), `normal_recharge`=values(normal_recharge), `normal_profit`=values(normal_profit), `normal_refund`=values(normal_refund), `cost`=values(cost), `cash_cost`=values(cash_cost), `cash_cost_back`=values(cash_cost_back),`update_time`=values(update_time);
	</insert>

	<!-- 更新 -->
	<update id="updateGroup" parameterType="FastStaffCostIncomeMonthPO">
		update fast_staff_cost_income_month set
		  user_group_id = #{userGroupId},
		  user_group_name = #{userGroupName}
		where
			`month` = #{month}
		  AND user_id = #{userId}
		  AND media_type = #{mediaType}
	</update>
</mapper>
