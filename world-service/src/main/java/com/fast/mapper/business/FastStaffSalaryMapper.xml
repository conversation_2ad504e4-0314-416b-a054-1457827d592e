<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffSalaryMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStaffSalary_columns">
		select t.`id`,t.`month`,t.`staff_id`,t.`real_name`,t.`salary`,t.`salary_other`,t.`creator_id`,t.`updator_id`,t.`remark`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStaffSalaryPO">
		<include refid="FastStaffSalary_columns" />
	    from fast_staff_salary t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStaffSalaryPO" resultType="FastStaffSalaryPO">
		<include refid="FastStaffSalary_columns" />
	    from fast_staff_salary t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStaffSalaryPO" resultType="FastStaffSalaryPO">
		<include refid="FastStaffSalary_columns" />
	    from fast_staff_salary t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStaffSalaryPO" resultType="int">
		select count(*)
	    from fast_staff_salary t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="month != null">
			and t.`month` = #{month}
		</if>
		<if test="staffId != null">
			and t.`staff_id` = #{staffId}
		</if>
		<if test="realName != null">
			and t.`real_name` = #{realName}
		</if>
		<if test="salary != null">
			and t.`salary` = #{salary}
		</if>
		<if test="salaryOther != null">
			and t.`salary_other` = #{salaryOther}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffSalaryPO">
        insert into fast_staff_salary
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="month != null">month,</if>
	        <if test="staffId != null">staff_id,</if>
	        <if test="realName != null">real_name,</if>
	        <if test="salary != null">salary,</if>
	        <if test="salaryOther != null">salary_other,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="remark != null">remark,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="month != null">#{month},</if>
	        <if test="staffId != null">#{staffId},</if>
	        <if test="realName != null">#{realName},</if>
	        <if test="salary != null">#{salary},</if>
	        <if test="salaryOther != null">#{salaryOther},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="remark != null">#{remark},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffSalaryPO">
        insert into fast_staff_salary (
         month, staff_id, real_name, salary, salary_other, creator_id, updator_id, `remark`, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.month}, #{item.staffId}, #{item.realName}, #{item.salary}, #{item.salaryOther}, #{item.creatorId}, #{item.updatorId}, #{item.remark}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertUpdateBatch" parameterType="FastStaffSalaryPO">
        insert into fast_staff_salary (
         month, staff_id, real_name, salary, salary_other, creator_id, updator_id, `remark`, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.month}, #{item.staffId}, #{item.realName}, #{item.salary}, #{item.salaryOther}, #{item.creatorId}, #{item.updatorId}, #{item.remark}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
		ON DUPLICATE KEY UPDATE salary=values(salary),`salary_other`=values(salary_other),`remark`=values(remark),`updator_id`=values(updator_id),`update_time`=values(update_time);
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStaffSalaryPO">
        update fast_staff_salary
        <set>
         	<if test="month != null" >
               month = #{month},
            </if>
         	<if test="staffId != null" >
               staff_id = #{staffId},
            </if>
         	<if test="realName != null" >
               real_name = #{realName},
            </if>
         	<if test="salary != null" >
               salary = #{salary},
            </if>
         	<if test="salaryOther != null" >
               salary_other = #{salaryOther},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

	<!-- 删除 -->
	<delete id="deleteByRealName">
		DELETE from fast_staff_salary where real_name = #{realName}
	</delete>
</mapper>
