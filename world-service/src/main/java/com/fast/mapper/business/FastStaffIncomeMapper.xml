<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffIncomeMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStaffIncome_columns">
		select t.`id`,t.`title`,t.`month`,t.`user_id`,t.`real_name`,t.`income`,t.`state`,t.`is_audit`,t.`audit_user`,t.`audit_time`,t.`creator_id`,t.`creator_name`,t.`updator_id`,t.`remark`,t.`audit_remark`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStaffIncomePO">
		<include refid="FastStaffIncome_columns" />
	    from fast_staff_income t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStaffIncomePO" resultType="FastStaffIncomePO">
		<include refid="FastStaffIncome_columns" />
	    from fast_staff_income t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStaffIncomePO" resultType="FastStaffIncomePO">
		select distinct t.`id`,t.`title`,t.`month`,t.`user_id`,t.`real_name`,t.`income`,t.`state`,t.`is_audit`,t.`audit_user`,t.`audit_time`,t.`creator_id`,t.`creator_name`,t.`updator_id`,t.`remark`,t.`audit_remark`,t.`create_time`,t.`update_time`
	    from fast_staff_income t
		<if test="incomeType != null or incomeTypes != null">
			left join fast_staff_income_detail d on t.id=d.staff_income_id
		</if>
        <where>
        	<include refid="whereSQL" />
			<if test="realNames != null">
				and t.real_name in (#{realNames})
			</if>
			<if test="creatorIds != null">
				and t.creator_id in (#{creatorIds})
			</if>
			<if test="incomeTypes != null">
				and d.income_type in (#{incomeTypes})
			</if>
			<if test="incomeType != null">
				and d.income_type=#{incomeType}
			</if>
        </where>
		order by t.`month` desc, t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStaffIncomePO" resultType="int">
		select count(*)
	    from fast_staff_income t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!--分页查询集合  -->
	<select id="queryListTotal" parameterType="FastStaffIncomePO" resultType="FastStaffIncomePO">
		select sum(income) incomeTotal,
		       sum(if(state=1, income, 0)) incomeTotalPass,
			   sum(if(state=2, income, 0)) incomeTotalNotPass
	    from (
		    select distinct t.`id`,t.`month`,t.`income`,t.`state`
			from fast_staff_income t
			<if test="incomeType != null or incomeTypes != null">
				left join fast_staff_income_detail d on t.id=d.staff_income_id
			</if>
			<where>
				<include refid="whereSQL" />
				<if test="realNames != null">
					and t.real_name in (#{realNames})
				</if>
				<if test="creatorIds != null">
					and t.creator_id in (#{creatorIds})
				</if>
				<if test="incomeTypes != null">
					and d.income_type in (#{incomeTypes})
				</if>
				<if test="incomeType != null">
					and d.income_type=#{incomeType}
				</if>
			</where>
		) a
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="title != null">
			and t.`title` = #{title}
		</if>
		<if test="month != null">
			and t.`month` = #{month}
		</if>
		<if test="userId != null">
			and t.`user_id` = #{userId}
		</if>
		<if test="realName != null">
			and t.`real_name` = #{realName}
		</if>
		<if test="income != null">
			and t.`income` = #{income}
		</if>
		<if test="state != null">
			and t.`state` = #{state}
		</if>
		<if test="isAudit != null">
			and t.`is_audit` = #{isAudit}
		</if>
		<if test="auditUser != null">
			and t.`audit_user` = #{auditUser}
		</if>
		<if test="auditTime != null">
			and t.`audit_time` = #{auditTime}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="creatorName != null">
			and t.`creator_name` = #{creatorName}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffIncomePO">
        insert into fast_staff_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="title != null">title,</if>
	        <if test="month != null">month,</if>
	        <if test="userId != null">user_id,</if>
	        <if test="realName != null">real_name,</if>
	        <if test="income != null">income,</if>
	        <if test="state != null">`state`,</if>
	        <if test="isAudit != null">is_audit,</if>
	        <if test="auditUser != null">audit_user,</if>
	        <if test="auditTime != null">audit_time,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="creatorName != null">creator_name,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="remark != null">remark,</if>
	        <if test="auditRemark != null">audit_remark,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="title != null">#{title},</if>
	        <if test="month != null">#{month},</if>
	        <if test="userId != null">#{userId},</if>
	        <if test="realName != null">#{realName},</if>
	        <if test="income != null">#{income},</if>
	        <if test="state != null">#{state},</if>
	        <if test="isAudit != null">#{isAudit},</if>
	        <if test="auditUser != null">#{auditUser},</if>
	        <if test="auditTime != null">#{auditTime},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="creatorName != null">#{creatorName},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="remark != null">#{remark},</if>
	        <if test="auditRemark != null">#{auditRemark},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffIncomePO">
        insert into fast_staff_income (
         title, month, user_id, real_name, income, `state`, is_audit, audit_user, audit_time, creator_id,`creator_name`, updator_id, `remark`, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.title}, #{item.month}, #{item.userId}, #{item.realName}, #{item.income}, #{item.state}, #{item.isAudit}, #{item.auditUser}, #{item.auditTime}, #{item.creatorId}, #{item.creatorName}, #{item.updatorId}, #{item.remark}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateByAudit" parameterType="FastStaffIncomePO">
		update fast_staff_income set is_audit=#{isAudit} where state = 0
	</update>

	<update id="updateById" parameterType="FastStaffIncomePO">
        update fast_staff_income
        <set>
         	<if test="title != null" >
               title = #{title},
            </if>
         	<if test="month != null" >
               month = #{month},
            </if>
         	<if test="userId != null" >
               user_id = #{userId},
            </if>
         	<if test="realName != null" >
               real_name = #{realName},
            </if>
         	<if test="income != null" >
               income = #{income},
            </if>
         	<if test="state != null" >
               `state` = #{state},
            </if>
         	<if test="isAudit != null" >
               is_audit = #{isAudit},
            </if>
         	<if test="auditUser != null" >
               audit_user = #{auditUser},
            </if>
         	<if test="auditTime != null" >
               audit_time = #{auditTime},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
         	<if test="auditRemark != null" >
				audit_remark = #{auditRemark},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

	<!-- 删除 -->
	<delete id="deleteById" parameterType="FastStaffIncomePO">
		delete from fast_staff_income  where id = #{id}
	</delete>
</mapper>
