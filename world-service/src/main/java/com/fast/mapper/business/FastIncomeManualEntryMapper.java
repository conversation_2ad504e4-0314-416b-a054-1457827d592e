/*
 * Powered By fast.up
 */
package com.fast.mapper.business;

import com.fast.po.business.FastIncomeManualEntryPO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastIncomeManualEntryMapper {

    // 通过id查询单个对象
    FastIncomeManualEntryPO queryById(FastIncomeManualEntryPO entity);

    // 通过id查询单个对象
    FastIncomeManualEntryPO queryById(@Param("id") Integer id);

    FastIncomeManualEntryPO queryJoinTypeById(FastIncomeManualEntryPO entity);

    // 通过条件查询单个对象
    FastIncomeManualEntryPO queryOne(FastIncomeManualEntryPO entity);

    // 查询全部
    List<FastIncomeManualEntryPO> queryList(FastIncomeManualEntryPO entity);

    List<FastIncomeManualEntryPO> queryListJoinType(FastIncomeManualEntryPO entity);

    FastIncomeManualEntryPO querySumIncome(FastIncomeManualEntryPO entity);

    FastIncomeManualEntryPO querySumIncomeMonth(FastIncomeManualEntryPO entity);

    // 获取数据
    List<FastIncomeManualEntryPO> queryIncome(FastIncomeManualEntryPO entity);

    @MapKey("month")
    Map<Integer, FastIncomeManualEntryPO> queryIncomeMonth(FastIncomeManualEntryPO entity);

    FastIncomeManualEntryPO queryIncomeMonthTotal(FastIncomeManualEntryPO entity);

    // 查询总数
    int queryCount(FastIncomeManualEntryPO entity);

    // 可选新增
    int insertSelective(FastIncomeManualEntryPO entity);

    // 批量新增
    int insertBatch(List<FastIncomeManualEntryPO> list);

    int updateBatch(List<FastIncomeManualEntryPO> list);

    // 更新
    int updateById(FastIncomeManualEntryPO entity);

    int updateJoinCpSettleById(FastIncomeManualEntryPO entity);

    int updateStateByCreateTime(FastIncomeManualEntryPO entity);

    int deleteById(FastIncomeManualEntryPO entity);

    int batchConfirmByTimeTypeAndDate(FastIncomeManualEntryPO params);

    BigDecimal queryReportIncomeByIncomeTypeId(FastIncomeManualEntryPO query);

    List<FastIncomeManualEntryPO> queryMonthList(FastIncomeManualEntryPO params);
}
