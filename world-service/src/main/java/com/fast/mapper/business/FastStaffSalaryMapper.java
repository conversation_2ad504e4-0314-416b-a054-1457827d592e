/*
 * Powered By fast.up
 */
package com.fast.mapper.business;

import com.fast.po.business.FastStaffSalaryPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStaffSalaryMapper {

    // 通过id查询单个对象
    FastStaffSalaryPO queryById(FastStaffSalaryPO entity);

    // 通过id查询单个对象
    FastStaffSalaryPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStaffSalaryPO queryOne(FastStaffSalaryPO entity);

    // 查询全部
    List<FastStaffSalaryPO> queryList(FastStaffSalaryPO entity);

    // 查询总数
    int queryCount(FastStaffSalaryPO entity);

    // 可选新增
    int insertSelective(FastStaffSalaryPO entity);

    // 批量新增
    int insertBatch(List<FastStaffSalaryPO> list);

    int insertUpdateBatch(List<FastStaffSalaryPO> list);

    // 更新
    int updateById(FastStaffSalaryPO entity);

    int deleteByRealName(@Param("realName") String realName);
}
