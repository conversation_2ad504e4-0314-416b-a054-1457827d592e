/*
 * Powered By fast.up
 */
package com.fast.mapper.business;

import com.fast.po.business.FastStaffGroupAmortizationPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStaffGroupAmortizationMapper {

    // 通过id查询单个对象
    FastStaffGroupAmortizationPO queryById(FastStaffGroupAmortizationPO entity);

    // 通过id查询单个对象
    FastStaffGroupAmortizationPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStaffGroupAmortizationPO queryOne(FastStaffGroupAmortizationPO entity);

    // 查询全部
    List<FastStaffGroupAmortizationPO> queryList(FastStaffGroupAmortizationPO entity);

    // 查询总数
    int queryCount(FastStaffGroupAmortizationPO entity);

    // 可选新增
    int insertSelective(FastStaffGroupAmortizationPO entity);

    // 批量新增
    int insertBatch(List<FastStaffGroupAmortizationPO> list);

    int insertUpdateBatch(List<FastStaffGroupAmortizationPO> list);

    // 更新
    int updateById(FastStaffGroupAmortizationPO entity);

}
