<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffRetailRechargeMonthMapper">

	<!-- 批量新增 -->
	<insert id="insertUpdateBatch">
		insert into fast_staff_retail_recharge_month(
			`month`,retail_id,media_type,retail_name,retail_type,
			money_recharge, money_profit,
		    virtual_recharge, virtual_profit, virtual_refund,
			normal_recharge, normal_profit, normal_refund,
			money_refund, create_time,update_time,content_type
		) (SELECT DATE_FORMAT(#{startDate},'%Y%m') 'month',
			  retail_id, media_type, retail_name, retail_type,
			  sum(money_recharge) money_recharge,
			  sum(money_profit) money_profit,
			  sum(virtual_recharge) virtual_recharge,
			  sum(virtual_profit) virtual_profit,
			  sum(virtual_refund) virtual_refund,
			  sum(normal_recharge) normal_recharge,
			  sum(normal_profit) normal_profit,
			  sum(normal_refund) normal_refund,
			  sum(money_refund) money_refund,
			  NOW() create_time,
			  NOW() update_time
		   FROM fast_staff_retail_recharge_day
		   where `date` BETWEEN #{startDate} and #{endDate}
		   GROUP BY retail_id,content_type
		) ON DUPLICATE KEY UPDATE money_recharge=values(money_recharge),
		   `money_profit`=values(money_profit),
		   `virtual_recharge`=values(virtual_recharge),
	       `virtual_profit`=values(virtual_profit),
		   `virtual_refund`=values(virtual_refund),
		   `normal_recharge`=values(normal_recharge),
		   `normal_profit`=values(normal_profit),
		   `normal_refund`=values(normal_refund),
		   `money_refund`=values(money_refund),
		   `update_time`=values(update_time);
	</insert>

</mapper>
