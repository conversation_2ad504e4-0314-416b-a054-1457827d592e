/*
 * Powered By fast.up
 */
package com.fast.mapper.business;

import com.fast.po.business.FastStaffIncomeTypePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStaffIncomeTypeMapper {

    // 通过id查询单个对象
    FastStaffIncomeTypePO queryById(FastStaffIncomeTypePO entity);

    // 通过id查询单个对象
    FastStaffIncomeTypePO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStaffIncomeTypePO queryOne(FastStaffIncomeTypePO entity);

    // 查询全部
    List<FastStaffIncomeTypePO> queryList(FastStaffIncomeTypePO entity);

    // 查询总数
    int queryCount(FastStaffIncomeTypePO entity);

    // 可选新增
    int insertSelective(FastStaffIncomeTypePO entity);

    // 批量新增
    int insertBatch(List<FastStaffIncomeTypePO> list);

    // 更新
    int updateById(FastStaffIncomeTypePO entity);

    // 删除
    int deleteById(FastStaffIncomeTypePO entity);

}
