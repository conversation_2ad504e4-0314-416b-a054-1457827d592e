<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastIncomeManualEntryMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastIncomeManualEntry_columns">
		select t.`id`,t.`income_type_id`,t.`effect_date`,t.`income`,t.`drama_id`,t.`drama_name`,t.`state`,t.`time_type`,t.`audit_user`,t.`audit_time`,
			   t.`creator_id`,t.`creator_name`,t.`remark`,t.`create_time`,t.`update_time`,t.`is_renovate`,t.`effect_month`,t.`join_cp_settle`,t.`income_channel`,t.`statement_ratio`,t.`principal_name`,t.`source_income`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastIncomeManualEntryPO">
		<include refid="FastIncomeManualEntry_columns" />
	    from fast_income_manual_entry t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

    <!-- 通过id查询单个对象 -->
    <select id="queryJoinTypeById" resultType="FastIncomeManualEntryPO">
		<include refid="FastIncomeManualEntry_columns" />, i.title, i.type
		from fast_income_manual_entry t
		left join fast_setting_income_type i on t.income_type_id = i.id
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastIncomeManualEntryPO" resultType="FastIncomeManualEntryPO">
		<include refid="FastIncomeManualEntry_columns" />
	    from fast_income_manual_entry t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastIncomeManualEntryPO" resultType="FastIncomeManualEntryPO">
		<include refid="FastIncomeManualEntry_columns" />
	    from fast_income_manual_entry t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--分页查询集合  -->
	<select id="queryListJoinType" parameterType="FastIncomeManualEntryPO" resultType="FastIncomeManualEntryPO">
		<include refid="FastIncomeManualEntry_columns" />, i.title, i.type,b.state auditState
	    from fast_income_manual_entry t
		left join fast_setting_income_type i on t.income_type_id = i.id
		left join fast_audit_process b on t.month_group_id = b.data_id and b.audit_type = "month_income"
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastIncomeManualEntryPO" resultType="int">
		select count(*)
	    from fast_income_manual_entry t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!--查询总数  -->
	<select id="querySumIncome" parameterType="FastIncomeManualEntryPO" resultType="FastIncomeManualEntryPO">
		select sum(source_income) incomeTotal, sum(if(state=1, source_income, 0)) incomeTotal1
	    from fast_income_manual_entry t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!--查询总数  -->
	<select id="queryIncome" parameterType="FastIncomeManualEntryPO" resultType="FastIncomeManualEntryPO">
		select effect_date,income_type_id,sum(income) income
		from fast_income_manual_entry t
		<where>
			<include refid="whereSQL" />
		</where>
		group by effect_date,income_type_id
	</select>

	<!-- 月概览列表数据 -->
	<select id="queryIncomeMonth" parameterType="FastIncomeManualEntryPO" resultType="FastIncomeManualEntryPO">
		select `month`,
			   sum(if(income_type_id=1,income,0)) douyinOriginProfit,
			   sum(if(income_type_id=2,income,0)) kuaishouOriginProfit,
			   sum(if(income_type_id=3,income,0)) kuaishouJuxingProfit
		from (
			SELECT DATE_FORMAT(t.effect_date,'%Y%m') `month`,
				t.effect_date, t.income_type_id, sum(t.income) income
			FROM `fast_income_manual_entry` t
			<where>
				<include refid="whereSQL" />
			</where>
			group by t.income_type_id,t.effect_date
		) t GROUP BY `month`
	</select>

	<select id="queryIncomeMonthTotal" parameterType="FastIncomeManualEntryPO" resultType="FastIncomeManualEntryPO">
		SELECT
			sum(if(t.income_type_id=1,t.income,0)) douyinOriginProfit,
			sum(if(t.income_type_id=2,t.income,0)) kuaishouOriginProfit,
			sum(if(t.income_type_id=3,t.income,0)) kuaishouJuxingProfit
		FROM `fast_income_manual_entry` t
		<where>
			<include refid="whereSQL" />
		</where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="incomeTypeId != null">
			and t.`income_type_id` = #{incomeTypeId}
		</if>
		<if test="incomeTypeIds != null">
			income_type_id in (${incomeTypeIds})
		</if>
		<if test="effectDate != null">
			and t.`effect_date` = #{effectDate}
		</if>
		<if test="income != null">
			and t.`income` = #{income}
		</if>
		<if test="dramaId != null">
			and t.`drama_id` = #{dramaId}
		</if>
		<if test="dramaIds != null and dramaIds.length > 0">
			and t.`drama_id` in (${dramaIds})
		</if>
		<if test="dramaName != null and dramaName.length > 0">
			and t.drama_name like concat('%',#{dramaName},'%')
		</if>
		<if test="state != null">
			and t.`state` = #{state}
		</if>
		<if test="timeType != null">
			and t.`time_type` = #{timeType}
		</if>
		<if test="auditUser != null">
			and t.`audit_user` = #{auditUser}
		</if>
		<if test="auditTime != null">
			and t.`audit_time` = #{auditTime}
		</if>
		<if test="auditTimeS != null and auditTimeE != null">
			and t.`audit_time` between #{auditTimeS} and #{auditTimeE}
		</if>
		<if test="joinCpSettle != null">
			and t.`join_cp_settle` = #{joinCpSettle}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="creatorName != null">
			and t.`creator_name` = #{creatorName}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
		<if test="effectDateStart != null">
			and t.`effect_date` &gt;= #{effectDateStart}
		</if>
		<if test="effectDateEnd != null">
			and t.`effect_date` &lt;= #{effectDateEnd}
		</if>
		<if test="createTimeStart != null">
			and t.`create_time` &gt;= #{createTimeStart}
		</if>
		<if test="createTimeEnd != null">
			and t.`create_time` &lt;= #{createTimeEnd}
		</if>
		<if test="effectMonth != null">
			and t.`effect_month` = #{effectMonth}
		</if>
		<if test="incomeChannel != null">
			and t.income_channel = #{incomeChannel}
		</if>
		<if test="principalName != null and principalName != ''">
			and t.principal_name = #{principalName}
		</if>
		<if test="thirdAccountName != null and thirdAccountName != ''">
			and t.third_account_name = #{thirdAccountName}
		</if>
		<if test="monthGroupId != null and monthGroupId != ''">
			and t.month_group_id = #{monthGroupId}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastIncomeManualEntryPO">
        insert into fast_income_manual_entry
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="incomeTypeId != null">income_type_id,</if>
	        <if test="effectDate != null">effect_date,</if>
	        <if test="effectMonth != null">effect_month,</if>
	        <if test="income != null">income,</if>
	        <if test="dramaId != null">drama_id,</if>
	        <if test="dramaName != null">drama_name,</if>
	        <if test="state != null">`state`,</if>
	        <if test="timeType != null">time_type,</if>
	        <if test="auditUser != null">audit_user,</if>
	        <if test="auditTime != null">audit_time,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="creatorName != null">creator_name,</if>
	        <if test="remark != null">remark,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="incomeTypeId != null">#{incomeTypeId},</if>
	        <if test="effectDate != null">#{effectDate},</if>
	        <if test="effectMonth != null">#{effectMonth},</if>
	        <if test="income != null">#{income},</if>
	        <if test="dramaId != null">#{dramaId},</if>
	        <if test="dramaName != null">#{dramaName},</if>
	        <if test="state != null">#{state},</if>
	        <if test="timeType != null">#{timeType},</if>
	        <if test="auditUser != null">#{auditUser},</if>
	        <if test="auditTime != null">#{auditTime},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="creatorName != null">#{creatorName},</if>
	        <if test="remark != null">#{remark},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastIncomeManualEntryPO">
        insert into fast_income_manual_entry (
         income_type_id, effect_date, effect_month, income, drama_id, drama_name, `state`, time_type, audit_user, audit_time,
		 creator_id, creator_name, `remark`, create_time, update_time, is_renovate,income_channel,statement_ratio,principal_name,source_income,third_account_name,month_group_id
        ) values
        <foreach collection="list" item="item" separator=",">
        (
            #{item.incomeTypeId}, #{item.effectDate}, #{item.effectMonth}, #{item.income}, #{item.dramaId}, #{item.dramaName}, #{item.state}, #{item.timeType},
			#{item.auditUser}, #{item.auditTime}, #{item.creatorId}, #{item.creatorName}, #{item.remark}, #{item.createTime}, #{item.updateTime}, #{item.isRenovate},
			#{item.incomeChannel},#{item.statementRatio},#{item.principalName},#{item.sourceIncome},#{item.thirdAccountName},#{item.monthGroupId}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastIncomeManualEntryPO">
        update fast_income_manual_entry
        <set>
         	<if test="incomeTypeId != null" >
				income_type_id = #{incomeTypeId},
            </if>
         	<if test="effectDate != null" >
                effect_date = #{effectDate},
            </if>
         	<if test="effectMonth != null" >
				effect_month = #{effectMonth},
            </if>
         	<if test="income != null" >
               income = #{income},
            </if>
         	<if test="dramaId != null" >
               drama_id = #{dramaId},
            </if>
         	<if test="dramaName != null" >
               drama_name = #{dramaName},
            </if>
         	<if test="state != null" >
               `state` = #{state},
            </if>
         	<if test="timeType != null" >
               time_type = #{timeType},
            </if>
         	<if test="auditUser != null" >
               audit_user = #{auditUser},
            </if>
         	<if test="auditTime != null" >
               audit_time = #{auditTime},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="creatorName != null" >
               creator_name = #{creatorName},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
			<if test="statementRatio != null" >
				statement_ratio = #{statementRatio},
			</if>
			<if test="monthGroupId != null and monthGroupId != ''" >
				month_group_id = #{monthGroupId},
			</if>

	    </set>
        where id = #{id}
	</update>

    <!-- 更新 -->
	<update id="updateJoinCpSettleById" parameterType="FastIncomeManualEntryPO">
        update fast_income_manual_entry
        <set>
         	<if test="joinCpSettle != null" >
				join_cp_settle = #{joinCpSettle},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where id = #{id}
	</update>

	<!-- 更新 -->
	<update id="updateBatch" parameterType="FastIncomeManualEntryPO">
		<foreach collection="list" item="item" separator=";">
        update fast_income_manual_entry
        <set>
         	<if test="item.incomeTypeId != null" >
				income_type_id = #{item.incomeTypeId},
            </if>
         	<if test="item.effectDate != null" >
               effect_date = #{item.effectDate},
            </if>
         	<if test="item.income != null" >
               income = #{item.income},
            </if>
         	<if test="item.dramaId != null" >
               drama_id = #{item.dramaId},
            </if>
         	<if test="item.dramaName != null" >
               drama_name = #{item.dramaName},
            </if>
         	<if test="item.state != null" >
               state = #{item.state},
            </if>
         	<if test="item.timeType != null" >
               time_type = #{item.timeType},
            </if>
         	<if test="item.auditUser != null" >
               audit_user = #{item.auditUser},
            </if>
         	<if test="item.auditTime != null" >
               audit_time = #{item.auditTime},
            </if>
         	<if test="item.creatorId != null" >
               creator_id = #{item.creatorId},
            </if>
         	<if test="item.creatorName != null" >
               creator_name = #{item.creatorName},
            </if>
         	<if test="item.remark != null" >
               remark = #{item.remark},
            </if>
         	<if test="item.isRenovate != null" >
				is_renovate = #{item.isRenovate},
            </if>
         	<if test="item.createTime != null" >
               create_time = #{item.createTime},
            </if>
         	<if test="item.updateTime != null" >
               update_time = #{item.updateTime},
            </if>
			<if test="item.incomeChannel != null and item.incomeChannel != ''" >
				income_channel= #{item.incomeChannel},
			</if>
			<if test="item.statementRatio != null" >
				statement_ratio= #{item.statementRatio},
			</if>
			<if test="item.principalName != null and item.principalName != ''" >
				principal_name= #{item.principalName},
			</if>
			<if test="item.sourceIncome != null">
				source_income = #{item.sourceIncome},
			</if>
			<if test="item.monthGroupId != null and item.monthGroupId != ''">
				`month_group_id` = #{item.monthGroupId},
			</if>
	    </set>
        where
        	id = #{item.id}
		</foreach>
	</update>

	<!-- 更新 -->
	<update id="updateStateByCreateTime" parameterType="FastIncomeManualEntryPO">
        update fast_income_manual_entry
        <set>
         	<if test="incomeTypeId != null" >
				income_type_id = #{incomeTypeId},
            </if>
         	<if test="effectDate != null" >
               effect_date = #{effectDate},
            </if>
         	<if test="income != null" >
               income = #{income},
            </if>
         	<if test="dramaId != null" >
               drama_id = #{dramaId},
            </if>
         	<if test="dramaName != null" >
               drama_name = #{dramaName},
            </if>
         	<if test="state != null" >
               `state` = #{state},
            </if>
         	<if test="timeType != null" >
               time_type = #{timeType},
            </if>
         	<if test="auditUser != null" >
               audit_user = #{auditUser},
            </if>
         	<if test="auditTime != null" >
               audit_time = #{auditTime},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="creatorName != null" >
               creator_name = #{creatorName},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
		<where>
			<if test="state != null and state==1">
				and `state` = 0
			</if>
			<if test="state != null and state==0">
				and `state` = 1
			</if>
			<if test="createTimeStart != null">
				and `create_time` &gt;= #{createTimeStart}
			</if>
			<if test="createTimeEnd != null">
				and `create_time` &lt;= #{createTimeEnd}
			</if>
			<if test="timeType != null">
				and `time_type` = #{timeType}
			</if>
		</where>
	</update>

	<delete id="deleteById" parameterType="java.lang.Integer">
		delete from fast_income_manual_entry where id = #{id}
	</delete>

	<update id="batchConfirmByTimeTypeAndDate">
		update fast_income_manual_entry
		set state = 1, update_time = #{updateTime}
		where state = 0 and time_type = #{timeType}
	</update>

	<select id="queryReportIncomeByIncomeTypeId" parameterType="FastIncomeManualEntryPO" resultType="java.math.BigDecimal">
		SELECT
			ifnull(sum(income),0.00)
		FROM
			`fast_income_manual_entry` t
		WHERE
			drama_id IN ( ${dramaIds} )
			AND income_type_id = #{incomeTypeId}
			AND state = 1
			AND exists (select 1 from fast_setting_income_type where id = t.income_type_id and cp_devide_flag = 1)
			<if test="createTimeStart != null and createTimeEnd != null">
                AND create_time &gt;= #{createTimeStart} AND create_time &lt;= #{createTimeEnd}
			</if>
			<if test="timeTypes != null and timeTypes.length()>0">
				AND time_type in (${timeTypes})
			</if>
	</select>

	<select id="queryMonthList" resultType="com.fast.po.business.FastIncomeManualEntryPO">
		select
			(select `value` from fast_common_dict where fast_common_dict.id = a.income_channel) thirdPlatformName
			 ,a.*
			,b.state auditState
			,c.user_id auditUserId
		,b.id auditProcessId
		from (
						  select
								t.id,
							  i.title,
							  principal_name,
							  third_account_name,
							  sum(source_income) source_income,
							  effect_date,
							  effect_month,
							  income_channel,
							  GROUP_CONCAT(t.state) states,
								month_group_id
						  from fast_income_manual_entry t
								   left join fast_setting_income_type i on t.income_type_id = i.id
						  where t.time_type = 3
		<if test="thirdAccountNameLike != null and thirdAccountNameLike != ''">
			and t.third_account_name like concat("%",#{thirdAccountNameLike},"%")
		</if>
		<if test="principalNameLike != null and principalNameLike != ''">
			and t.principal_name like concat("%",#{principalNameLike},"%")
		</if>
		<if test="effectMonth != null">
			and t.effect_month = #{effectMonth}
		</if>
		<if test="effectDate != null">
			and t.effect_date = #{effectDate}
		</if>
		<if test="incomeTypeId != null">
			and t.income_type_id = #{incomeTypeId}
		</if>
		<if test="incomeChannelIds != null and incomeChannelIds != ''">
			and t.income_channel in (${incomeChannelIds})
		</if>
						  group by income_type_id,effect_month,effect_date,principal_name,third_account_name,income_channel
					  ) a left join fast_audit_process b on a.month_group_id = b.data_id and b.audit_type = "month_income"
						left join fast_audit_flow c on b.audit_type = c.audit_type and b.level = c.level and c.del_flag = 0
		<where>
			<if test="needAudit != null">
				<if test="needAudit == 1">
					and c.user_id = #{auditUserId}
				</if>
				<if test="needAudit == 0">
					and (c.user_id is null or c.user_id != #{auditUserId})
				</if>
			</if>
			<if test="state != null">
				<if test="state == 1">
					and find_in_set(0, states) = 0
				</if>
				<if test="state == 0">
					and find_in_set(0, states)
					and (b.state is null or b.state = 2)
				</if>
				<if test="state == 2">
					and b.state = 1
				</if>
				<if test="state == 3">
					and b.state = 3
				</if>
			</if>
		</where>
		order by a.effect_date desc ,a.id desc
	</select>

	<select id="querySumIncomeMonth" resultType="com.fast.po.business.FastIncomeManualEntryPO">
		select sum(source_income) incomeTotal, sum(a.incomeTotal1) incomeTotal1
		from (
		select
		t.id,
		i.title,
		principal_name,
		third_account_name,
		sum(source_income) source_income,
		effect_date,
		effect_month,
		income_channel,
		GROUP_CONCAT(t.state) states,
		sum(if(t.state=1, source_income, 0)) incomeTotal1,
		month_group_id
		from fast_income_manual_entry t
		left join fast_setting_income_type i on t.income_type_id = i.id
		where t.time_type = 3
		<if test="thirdAccountNameLike != null and thirdAccountNameLike != ''">
			and t.third_account_name like concat("%",#{thirdAccountNameLike},"%")
		</if>
		<if test="principalNameLike != null and principalNameLike != ''">
			and t.principal_name like concat("%",#{principalNameLike},"%")
		</if>
		<if test="effectMonth != null">
			and t.effect_month = #{effectMonth}
		</if>
		<if test="effectDate != null">
			and t.effect_date = #{effectDate}
		</if>
		<if test="incomeTypeId != null">
			and t.income_type_id = #{incomeTypeId}
		</if>
		<if test="incomeChannelIds != null and incomeChannelIds != ''">
			and t.income_channel in (${incomeChannelIds})
		</if>
		group by income_type_id,effect_month,effect_date,principal_name,third_account_name,income_channel
		) a left join fast_audit_process b on a.month_group_id = b.data_id and b.audit_type = "month_income"
		left join fast_audit_flow c on b.audit_type = c.audit_type and b.level = c.level and c.del_flag = 0
		<where>
			<if test="needAudit != null">
				<if test="needAudit == 1">
					and c.user_id = #{auditUserId}
				</if>
				<if test="needAudit == 0">
					and (c.user_id is null or c.user_id != #{auditUserId})
				</if>
			</if>
			<if test="state != null">
				<if test="state == 1">
					and find_in_set(0, states) = 0
				</if>
				<if test="state == 0">
					and find_in_set(0, states)
					and (b.state is null or b.state = 2)
				</if>
				<if test="state == 2">
					and b.state = 1
				</if>
				<if test="state == 3">
					and b.state = 3
				</if>
			</if>
		</where>
	</select>
</mapper>
