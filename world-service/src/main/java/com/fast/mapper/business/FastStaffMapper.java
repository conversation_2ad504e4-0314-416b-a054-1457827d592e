/*
 * Powered By fast.up
 */
package com.fast.mapper.business;

import com.fast.po.business.FastStaffPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStaffMapper {

    // 通过id查询单个对象
    FastStaffPO queryById(FastStaffPO entity);

    // 通过id查询单个对象
    FastStaffPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStaffPO queryOne(FastStaffPO entity);

    // 查询全部
    List<FastStaffPO> queryList(FastStaffPO entity);

    List<FastStaffPO> queryUserList(FastStaffPO entity);

    List<FastStaffPO> queryStaffSalaryList(FastStaffPO entity);

    // 查询总数
    int queryCount(FastStaffPO entity);

    // 可选新增
    int insertSelective(FastStaffPO entity);

    // 批量新增
    int insertBatch(List<FastStaffPO> list);

    // 更新
    int updateById(FastStaffPO entity);

    int deleteById(FastStaffPO entity);

}
