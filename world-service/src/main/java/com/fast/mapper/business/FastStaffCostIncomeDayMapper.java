/*
 * Powered By fast.up
 */
package com.fast.mapper.business;

import com.fast.annotation.Slave;
import com.fast.po.business.FastStaffCostIncomeDayPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStaffCostIncomeDayMapper {

    // 通过id查询单个对象
    FastStaffCostIncomeDayPO queryById(FastStaffCostIncomeDayPO entity);

    // 通过id查询单个对象
    FastStaffCostIncomeDayPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStaffCostIncomeDayPO queryOne(FastStaffCostIncomeDayPO entity);

    // 查询全部
    List<FastStaffCostIncomeDayPO> queryList(FastStaffCostIncomeDayPO entity);

    List<FastStaffCostIncomeDayPO> queryListByGroup(FastStaffCostIncomeDayPO entity);

    List<FastStaffCostIncomeDayPO> mediaRechargeData(FastStaffCostIncomeDayPO entity);

    List<FastStaffCostIncomeDayPO> mediaRechargeOtherData(FastStaffCostIncomeDayPO entity);

    // 按日期查询概览数据
    @Slave
    Map<String, Object> mediaRechargeOverviewData(FastStaffCostIncomeDayPO entity);

    @Slave
    Map<String, Object> mediaRechargeOverviewDataMonth(FastStaffCostIncomeDayPO entity);

    @Slave
    List<FastStaffCostIncomeDayPO> queryRechargeList(FastStaffCostIncomeDayPO entity);

    @Slave
    FastStaffCostIncomeDayPO queryKuaishouRechargeList(FastStaffCostIncomeDayPO entity);

    @Slave
    FastStaffCostIncomeDayPO queryXingtuRechargeList(FastStaffCostIncomeDayPO entity);

    // 查询总数
    int queryCount(FastStaffCostIncomeDayPO entity);

    // 可选新增
    int insertSelective(FastStaffCostIncomeDayPO entity);

    // 批量新增
    int insertBatch(List<FastStaffCostIncomeDayPO> list);

    int insertUpdateBatch(List<FastStaffCostIncomeDayPO> list);

    // 更新
    int updateById(FastStaffCostIncomeDayPO entity);

    int updateGroup(FastStaffCostIncomeDayPO entity);

}