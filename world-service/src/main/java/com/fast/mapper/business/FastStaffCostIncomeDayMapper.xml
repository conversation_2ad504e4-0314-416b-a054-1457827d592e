<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffCostIncomeDayMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStaffCostIncome_columns">
		select t.`date`,t.`user_id`,t.`user_group_id`,t.`user_group_name`,t.`media_type`,t.`money_recharge`,t.`money_profit`,t.`virtual_recharge`,t.`virtual_profit`,t.`virtual_refund`,t.`normal_recharge`,t.`normal_profit`,t.`normal_refund`,t.`cost`,t.`cash_cost`,t.`back_ratio`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStaffCostIncomeDayPO">
		<include refid="FastStaffCostIncome_columns" />
	    from fast_staff_cost_income_day t
        <where>
	        t.`date` = #{date} and
	        t.`user_id` = #{userId}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStaffCostIncomeDayPO" resultType="FastStaffCostIncomeDayPO">
		<include refid="FastStaffCostIncome_columns" />
	    from fast_staff_cost_income_day t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStaffCostIncomeDayPO" resultType="FastStaffCostIncomeDayPO">
		<include refid="FastStaffCostIncome_columns" />
	    from fast_staff_cost_income_day t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`date`, t.`user_id` desc
	</select>

	<!--分页查询集合  -->
	<select id="queryListByGroup" parameterType="FastStaffCostIncomeDayPO" resultType="FastStaffCostIncomeDayPO">
		<include refid="FastStaffCostIncome_columns" />
	    from fast_staff_cost_income_day t
		join fast_user u on t.user_id=u.id
		join fast_retail r on r.id=u.retail_id
		where t.date>=date_sub(CURDATE(),INTERVAL 7 DAY)
			and t.user_group_id = #{userGroupId}
			and r.retail_type in (1,2)
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStaffCostIncomeDayPO" resultType="int">
		select count(*)
	    from fast_staff_cost_income_day t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 获取自带投分销类型充值数据 -->
	<select id="mediaRechargeData" resultType="FastStaffCostIncomeDayPO">
		select t.`date`,r.retail_type, sum(t.`money_profit`) moneyProfit
		from fast_staff_cost_income_day t
		left join fast_user u on t.user_id=u.id
		left join fast_retail r on r.id=u.retail_id
		where t.`date` = #{dateStr}
		  and t.media_type &lt; 100
		  and r.retail_type in (1,2,3)
		group by t.`date`,r.`retail_type`
	</select>

	<!-- 获取媒体类型充值数据 -->
	<select id="mediaRechargeOtherData" resultType="FastStaffCostIncomeDayPO">
		select t.`date`,t.media_type, sum(t.`money_profit`) moneyProfit
		from fast_staff_cost_income_day t
		where t.`date` = #{dateStr}
		  and t.media_type in (${mediaTypes})
		group by t.`date`,t.`media_type`
	</select>

	<!-- 获取媒体类型充值数据 -->
	<select id="mediaRechargeOverviewData" resultType="Map">
		select `date`,
			   sum(money_profit) moneyProfit, sum(money_recharge) moneyRecharge,
			   sum(if(type=1, money_profit, 0)) usProfit,
			   (select sum(money_profit) from fast_staff_retail_recharge_day where `date`=a.`date` and retail_type=2) agentProfit,
			   sum(if(type=3, money_profit, 0)) aloneProfit,
			   sum(if(type=102, money_profit, 0)) douyinMountProfit,
			   sum(if(type=501, money_profit, 0)) nativeProfit,
			   sum(if(type=502, money_profit, 0)) kuaishouMountProfit
		from (
			 select
				 t.`date`,
				 if(t.media_type in (102,501,502), t.media_type, r.`retail_type`) type,
				 t.`money_recharge`,
				 t.`money_profit`
			 from
				 fast_staff_cost_income_day t
					 left join fast_user u on t.user_id = u.id
					 left join fast_retail r on r.id = u.retail_id
			 where t.`date` = #{dateStr}
		and t.content_type = #{contentType}
		) a where type is not null
		group by `date`
	</select>

	<!-- 获取媒体类型充值数据 -->
	<select id="mediaRechargeOverviewDataMonth" resultType="Map">
		select `date`,
			   sum(money_profit) moneyProfit, sum(money_recharge) moneyRecharge,
			   sum(if(type=1, money_profit, 0)) usProfit,
			   (select sum(money_profit) from fast_staff_retail_recharge_day where `date` like '${month}%' and retail_type=2) agentProfit,
			   sum(if(type=3, money_profit, 0)) aloneProfit,
			   sum(if(type=102, money_profit, 0)) douyinMountProfit,
			   sum(if(type=501, money_profit, 0)) nativeProfit,
			   sum(if(type=502, money_profit, 0)) kuaishouMountProfit
		from (
			 select
				 t.`date`,
				 if(t.media_type in (102,501,502), t.media_type, r.`retail_type`) type,
				 t.`money_recharge`,
				 t.`money_profit`
			 from
				 fast_staff_cost_income_day t
					 left join fast_user u on t.user_id = u.id
					 left join fast_retail r on r.id = u.retail_id
			 where t.`date` like '${month}%'
		and t.content_type = #{contentType}
		) a where type is not null
	</select>

	<!--查询总数  -->
	<select id="queryRechargeList" parameterType="FastStaffCostIncomeDayPO" resultType="FastStaffCostIncomeDayPO">
		SELECT c.id userId,c.user_name,c.real_name,
		    b.adv_media_id 'mediaType',
		    sum(money_recharge) 'moneyRecharge',
		    sum(money_profit) 'moneyProfit',
		    sum(if(pay_form=8,money_recharge,0)) 'virtualRecharge',
		    sum(if(pay_form=8,money_profit,0)) 'virtualProfit',
		    sum(if(pay_form=8,0,money_recharge)) 'normalRecharge',
		    sum(if(pay_form=8,0,money_profit)) 'normalProfit',
		    sum(if(pay_form=8,money_refund,0)) 'virtualRefund',
		    sum(if(pay_form=8,0,money_refund)) 'normalRefund'
		FROM (
			(select id,money_recharge,money_profit,pay_form,link_id,retail_id,mini_id,pay_time,0 money_refund from `fast_member_order_recharge` force index(pay_time)
			where pay_time BETWEEN #{payTimeStart} and #{payTimeEnd} and state=1) union all
			(select a.id,0 money_recharge, 0 money_profit,a.pay_form,a.link_id,a.retail_id,a.mini_id,a.pay_time,r.money_refund
			from `fast_member_order_recharge` a join fast_member_order_refund r on a.id=r.order_id
			where refund_time BETWEEN #{payTimeStart} and #{payTimeEnd} and a.state=1)
		) a
		join fast_link b on a.link_id=b.id
		join fast_user c on b.adv_user_id=c.id
		where
			b.adv_media_id=#{mediaType}
		  and a.mini_id!=77
		  and b.link_sub_type not in (1,2,3)
		and b.content_type = #{contentType}
		GROUP BY c.id
	</select>

	<!-- 快手挂载充值  -->
	<select id="queryKuaishouRechargeList" parameterType="FastStaffCostIncomeDayPO" resultType="FastStaffCostIncomeDayPO">
		SELECT 0 userId,"" userName, "" realName,
		    502 'mediaType',
		    sum(money_recharge) 'moneyRecharge',
		    sum(money_profit) 'moneyProfit',
		    sum(if(pay_form=8,money_recharge,0)) 'virtualRecharge',
		    sum(if(pay_form=8,money_profit,0)) 'virtualProfit',
		    sum(if(pay_form=8,0,money_recharge)) 'normalRecharge',
		    sum(if(pay_form=8,0,money_profit)) 'normalProfit',
			sum(if(pay_form=8,money_refund,0)) 'virtualRefund',
			sum(if(pay_form=8,0,money_refund)) 'normalRefund'
		FROM (
			 (select id,money_recharge,money_profit,pay_form,link_id,mini_id,pay_time,0 money_refund from `fast_member_order_recharge` force index(pay_time)
			  where pay_time BETWEEN #{payTimeStart} and #{payTimeEnd}
			      and state=1 and mini_id=77
		and content_type = #{contentType}
			 ) union all
			 (select a.id,0 money_recharge, 0 money_profit,a.pay_form,a.link_id,a.mini_id,a.pay_time,r.money_refund
			  from `fast_member_order_recharge` a join fast_member_order_refund r on a.id=r.order_id
			  where refund_time BETWEEN #{payTimeStart} and #{payTimeEnd}
			    and a.state=1 and a.mini_id=77
		and a.content_type = #{contentType}
			 )
		 ) a
	</select>

	<!-- 抖音星图挂载充值 -->
	<select id="queryXingtuRechargeList" parameterType="FastStaffCostIncomeDayPO" resultType="FastStaffCostIncomeDayPO">
		SELECT 0 userId,"" userName, "" realName,
		    102 'mediaType',
		    sum(money_recharge) 'moneyRecharge',
		    sum(money_profit) 'moneyProfit',
		    sum(if(pay_form=8,money_recharge,0)) 'virtualRecharge',
		    sum(if(pay_form=8,money_profit,0)) 'virtualProfit',
		    sum(if(pay_form=8,0,money_recharge)) 'normalRecharge',
		    sum(if(pay_form=8,0,money_profit)) 'normalProfit',
			sum(if(pay_form=8,money_refund,0)) 'virtualRefund',
			sum(if(pay_form=8,0,money_refund)) 'normalRefund'
		FROM (
			(select id,money_recharge,money_profit,pay_form,retail_id,link_id,mini_id,pay_time,0 money_refund from `fast_member_order_recharge` force index(pay_time)
			where pay_time BETWEEN #{payTimeStart} and #{payTimeEnd} and state=1 <if test="contentType != null">and content_type = #{contentType}</if> ) union all
			(select a.id,0 money_recharge, 0 money_profit,a.pay_form,a.retail_id,a.link_id,a.mini_id,a.pay_time,r.money_refund
			from `fast_member_order_recharge` a join fast_member_order_refund r on a.id=r.order_id
			where refund_time BETWEEN #{payTimeStart} and #{payTimeEnd} and a.state=1 and a.content_type = #{contentType})
		) a
		left join fast_link l on a.link_id=l.id
		where l.link_sub_type in (1,2,3) or (
			a.retail_id in (select id from fast_retail where retail_type = 1)
			and a.link_id=0
			and a.mini_id in (select id from fast_mini where type = 2)
		)
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="date != null">
			and t.`date` = #{date}
		</if>
		<if test="userId != null">
			and t.`user_id` = #{userId}
		</if>
		<if test="userGroupId != null">
			and t.`user_group_id` = #{userGroupId}
		</if>
		<if test="userGroupName != null">
			and t.`user_group_name` = #{userGroupName}
		</if>
		<if test="mediaType != null">
			and t.`media_type` = #{mediaType}
		</if>
		<if test="moneyRecharge != null">
			and t.`money_recharge` = #{moneyRecharge}
		</if>
		<if test="moneyProfit != null">
			and t.`money_profit` = #{moneyProfit}
		</if>
		<if test="virtualRecharge != null">
			and t.`virtual_recharge` = #{virtualRecharge}
		</if>
		<if test="virtualProfit != null">
			and t.`virtual_profit` = #{virtualProfit}
		</if>
		<if test="normalRecharge != null">
			and t.`normal_recharge` = #{normalRecharge}
		</if>
		<if test="normalProfit != null">
			and t.`normal_profit` = #{normalProfit}
		</if>
		<if test="cost != null">
			and t.`cost` = #{cost}
		</if>
		<if test="cashCost != null">
			and t.`cash_cost` = #{cashCost}
		</if>
		<if test="backRatio != null">
			and t.`back_ratio` = #{backRatio}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="date" parameterType="FastStaffCostIncomeDayPO">
        insert into fast_staff_cost_income_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="date != null">date,</if>
	        <if test="userGroupId != null">user_group_id,</if>
	        <if test="userGroupName != null">user_group_name,</if>
	        <if test="mediaType != null">media_type,</if>
	        <if test="moneyRecharge != null">money_recharge,</if>
	        <if test="moneyProfit != null">money_profit,</if>
	        <if test="virtualRecharge != null">virtual_recharge,</if>
	        <if test="virtualProfit != null">virtual_profit,</if>
	        <if test="virtualRefund != null">virtual_refund,</if>
	        <if test="normalRecharge != null">normal_recharge,</if>
	        <if test="normalProfit != null">normal_profit,</if>
	        <if test="normalRefund != null">normal_refund,</if>
	        <if test="cost != null">cost,</if>
	        <if test="cashCost != null">cash_cost,</if>
	        <if test="backRatio != null">back_ratio,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="date != null">#{date},</if>
	        <if test="userGroupId != null">#{userGroupId},</if>
	        <if test="userGroupName != null">#{userGroupName},</if>
	        <if test="mediaType != null">#{mediaType},</if>
	        <if test="moneyRecharge != null">#{moneyRecharge},</if>
	        <if test="moneyProfit != null">#{moneyProfit},</if>
	        <if test="virtualRecharge != null">#{virtualRecharge},</if>
	        <if test="virtualProfit != null">#{virtualProfit},</if>
	        <if test="virtualRefund != null">#{virtualRefund},</if>
	        <if test="normalRecharge != null">#{normalRecharge},</if>
	        <if test="normalProfit != null">#{normalProfit},</if>
	        <if test="normalRefund != null">#{normalRefund},</if>
	        <if test="cost != null">#{cost},</if>
	        <if test="cashCost != null">#{cashCost},</if>
	        <if test="backRatio != null">#{backRatio},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="date" parameterType="FastStaffCostIncomeDayPO">
        insert into fast_staff_cost_income_day (
         date, user_id, user_group_id, user_group_name, media_type, money_recharge, money_profit, virtual_recharge, virtual_profit, virtual_refund, normal_recharge, normal_profit, normal_refund, cost, cash_cost, back_ratio, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.date}, #{item.userId}, #{item.userGroupId}, #{item.userGroupName}, #{item.mediaType}, #{item.moneyRecharge}, #{item.moneyProfit}, #{item.virtualRecharge}, #{item.virtualProfit}, #{item.virtualRefund}, #{item.normalRecharge}, #{item.normalProfit}, #{item.normalRefund}, #{item.cost}, #{item.cashCost}, #{item.backRatio}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertUpdateBatch" parameterType="FastStaffCostIncomeDayPO">
        insert into fast_staff_cost_income_day (
         date, user_id, user_group_id, user_group_name, media_type, money_recharge, money_profit, virtual_recharge, virtual_profit, virtual_refund, normal_recharge, normal_profit, normal_refund, cost, cash_cost, back_ratio, create_time, update_time, content_type
        ) values
        <foreach collection="list" item="item" separator=",">
        (
		#{item.date}, #{item.userId}, #{item.userGroupId}, #{item.userGroupName}, #{item.mediaType}, #{item.moneyRecharge}, #{item.moneyProfit}, #{item.virtualRecharge}, #{item.virtualProfit}, #{item.virtualRefund}, #{item.normalRecharge}, #{item.normalProfit}, #{item.normalRefund}, #{item.cost}, #{item.cashCost}, #{item.backRatio}, #{item.createTime}, #{item.updateTime}, #{item.contentType}
        )
        </foreach>
		ON DUPLICATE KEY UPDATE money_recharge=values(money_recharge),`money_profit`=values(money_profit),`virtual_recharge`=values(virtual_recharge), `virtual_profit`=values(virtual_profit), `virtual_refund`=values(virtual_refund), `normal_recharge`=values(normal_recharge), `normal_profit`=values(normal_profit), `normal_refund`=values(normal_refund), `cost`=values(cost), `cash_cost`=values(cash_cost), `back_ratio`=values(back_ratio),`update_time`=values(update_time),`content_type`=values(content_type);
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStaffCostIncomeDayPO">
        update fast_staff_cost_income_day
        <set>
         	<if test="userGroupId != null" >
               user_group_id = #{userGroupId},
            </if>
         	<if test="userGroupName != null" >
               user_group_name = #{userGroupName},
            </if>
         	<if test="mediaType != null" >
               media_type = #{mediaType},
            </if>
         	<if test="moneyRecharge != null" >
               money_recharge = #{moneyRecharge},
            </if>
         	<if test="moneyProfit != null" >
               money_profit = #{moneyProfit},
            </if>
         	<if test="virtualRecharge != null" >
				virtual_recharge = #{virtualRecharge},
            </if>
         	<if test="virtualProfit != null" >
				virtual_profit = #{virtualProfit},
            </if>
         	<if test="virtualRefund != null" >
				virtual_refund = #{virtualRefund},
            </if>
         	<if test="normalRecharge != null" >
				normal_recharge = #{normalRecharge},
            </if>
         	<if test="normalProfit != null" >
				normal_profit = #{normalProfit},
            </if>
         	<if test="normalRefund != null" >
				normal_refund = #{normalRefund},
            </if>
         	<if test="cost != null" >
               cost = #{cost},
            </if>
         	<if test="cashCost != null" >
               cash_cost = #{cashCost},
            </if>
         	<if test="backRatio != null" >
				back_ratio = #{backRatio},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	date = #{date}  AND  user_id = #{userId}  
	</update>

    <!-- 更新 -->
	<update id="updateGroup" parameterType="FastStaffCostIncomeDayPO">
        update fast_staff_cost_income_day set
		   user_group_id = #{userGroupId},
		   user_group_name = #{userGroupName}
        where
        	`date` = #{date}
            AND user_id = #{userId}
            AND media_type = #{mediaType}
	</update>

</mapper>
