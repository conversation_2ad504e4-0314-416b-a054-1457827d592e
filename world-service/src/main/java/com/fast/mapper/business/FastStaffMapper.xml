<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStaff_columns">
		select t.`id`,t.`real_name`,t.`role_id`,t.`role_name`,t.`state`,t.`remark`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStaffPO">
		<include refid="FastStaff_columns" />
	    from fast_staff t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStaffPO" resultType="FastStaffPO">
		<include refid="FastStaff_columns" />
	    from fast_staff t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStaffPO" resultType="FastStaffPO">
		<include refid="FastStaff_columns" />
	    from fast_staff t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--分页查询集合  -->
	<select id="queryUserList" parameterType="FastStaffPO" resultType="FastStaffPO">
		<include refid="FastStaff_columns" />,count(u.id) userNum
	    from fast_staff t
	    left join fast_user u on t.real_name=u.real_name
        <where>
        	<include refid="whereSQL" />
        </where>
	    group by t.real_name
	</select>

	<!--分页查询集合  -->
	<select id="queryStaffSalaryList" parameterType="FastStaffPO" resultType="FastStaffPO">
		select s.id,s.real_name,s.role_name,u.id userId,u.user_group_id,u.user_group_name,
		       sa.salary,sa.salary_other,
			   g.manage_cost,g.operation_cost,g.technical_cost,g.material_cost,
			   g.external_technical_cost,g.external_material_cost,g.reimbursement_cost,
			   g.company_manage_cost,g.company_administrative_cost
		from fast_staff s
			 left join fast_staff_salary sa on s.real_name=sa.real_name and sa.`month`=#{month}
			 left join (
				select u.id,u.real_name,m.user_group_id,m.user_group_name
				from fast_user u
				join fast_staff_cost_income_month m on u.id=m.user_id
				where m.`month`=#{month} and u.real_name != ''
			 ) u on s.real_name=u.real_name
			 left join fast_staff_group_amortization g on u.user_group_name=g.group_name and g.`month`=#{month}
		<where>
			<if test="updateTime != null">
				and sa.`update_time` = #{updateTime}
			</if>
		</where>
		group by s.real_name
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStaffPO" resultType="int">
		select count(*)
	    from fast_staff t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="realName != null">
			and t.`real_name` = #{realName}
		</if>
		<choose>
			<when test="roleId!=null"> and t.`role_id` = #{roleId}</when>
			<otherwise> and t.`role_id` > 0</otherwise>
		</choose>
		<if test="roleName != null">
			and t.`role_name` = #{roleName}
		</if>
		<if test="state != null">
			and t.`state` = #{state}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffPO">
        insert into fast_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="realName != null">real_name,</if>
	        <if test="roleId != null">role_id,</if>
	        <if test="roleName != null">role_name,</if>
	        <if test="state != null">`state`,</if>
	        <if test="remark != null">remark,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="realName != null">#{realName},</if>
	        <if test="roleId != null">#{roleId},</if>
	        <if test="roleName != null">#{roleName},</if>
	        <if test="state != null">#{state},</if>
	        <if test="remark != null">#{remark},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffPO">
        insert into fast_staff (
         real_name, role_id, role_name, `state`, `remark`, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.realName}, #{roleId}, #{roleName}, #{item.state}, #{item.remark}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStaffPO">
        update fast_staff
        <set>
         	<if test="realName != null" >
               real_name = #{realName},
            </if>
         	<if test="roleName != null" >
               role_name = #{roleName},
            </if>
         	<if test="roleId != null" >
               role_id = #{roleId},
            </if>
         	<if test="state != null" >
               `state` = #{state},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

	<!-- 删除 -->
	<update id="deleteById" parameterType="FastStaffPO">
		DELETE from fast_staff where id = #{id}
	</update>
</mapper>
