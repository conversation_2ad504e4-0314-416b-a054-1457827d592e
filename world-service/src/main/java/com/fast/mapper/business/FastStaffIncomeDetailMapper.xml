<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffIncomeDetailMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStaffIncomeDetail_columns">
		select t.`id`,t.`staff_income_id`,t.`month`,t.`user_id`,t.`income`,t.`income_type`,t.`state`,t.`create_time`,t.`update_time`,t.`remark`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStaffIncomeDetailPO">
		<include refid="FastStaffIncomeDetail_columns" />
	    from fast_staff_income_detail t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStaffIncomeDetailPO" resultType="FastStaffIncomeDetailPO">
		<include refid="FastStaffIncomeDetail_columns" />
	    from fast_staff_income_detail t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStaffIncomeDetailPO" resultType="FastStaffIncomeDetailPO">
		<include refid="FastStaffIncomeDetail_columns" />
	    from fast_staff_income_detail t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--分页查询集合  -->
	<select id="queryIncomeList" parameterType="FastStaffIncomeDetailPO" resultType="FastStaffIncomeDetailPO">
		select t.id,it.title,t.`income`,t.`income_type`
	    from fast_staff_income_detail t
		left join fast_staff_income_type it on t.income_type=it.id
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStaffIncomeDetailPO" resultType="int">
		select count(*)
	    from fast_staff_income_detail t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="staffIncomeId != null">
			and t.`staff_income_id` = #{staffIncomeId}
		</if>
		<if test="month != null">
			and t.`month` = #{month}
		</if>
		<if test="userId != null">
			and t.`user_id` = #{userId}
		</if>
		<if test="income != null">
			and t.`income` = #{income}
		</if>
		<if test="incomeType != null">
			and t.`income_type` = #{incomeType}
		</if>
		<if test="state != null">
			and t.`state` = #{state}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffIncomeDetailPO">
        insert into fast_staff_income_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="staffIncomeId != null">staff_income_id,</if>
	        <if test="month != null">month,</if>
	        <if test="userId != null">user_id,</if>
	        <if test="income != null">income,</if>
	        <if test="incomeType != null">income_type,</if>
	        <if test="state != null">`state`,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time,</if>
	        <if test="remark != null">remark</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="staffIncomeId != null">#{staffIncomeId},</if>
	        <if test="month != null">#{month},</if>
	        <if test="userId != null">#{userId},</if>
	        <if test="income != null">#{income},</if>
	        <if test="incomeType != null">#{incomeType},</if>
	        <if test="state != null">#{state},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime},</if>
	        <if test="remark != null">#{remark}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffIncomeDetailPO">
        insert into fast_staff_income_detail (
         staff_income_id, month, user_id, income, income_type, `state`, create_time, update_time, remark
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.staffIncomeId}, #{item.month}, #{item.userId}, #{item.income}, #{item.incomeType}, #{item.state}, #{item.createTime}, #{item.updateTime}, #{item.remark}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStaffIncomeDetailPO">
        update fast_staff_income_detail
        <set>
         	<if test="staffIncomeId != null" >
               staff_income_id = #{staffIncomeId},
            </if>
         	<if test="month != null" >
               month = #{month},
            </if>
         	<if test="userId != null" >
               user_id = #{userId},
            </if>
         	<if test="income != null" >
               income = #{income},
            </if>
         	<if test="incomeType != null" >
               income_type = #{incomeType},
            </if>
         	<if test="state != null" >
               `state` = #{state},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

    <!-- 更新 -->
	<update id="updateByStaffIncomeId" parameterType="FastStaffIncomeDetailPO">
        update fast_staff_income_detail
        <set>
         	<if test="month != null" >
               month = #{month},
            </if>
         	<if test="userId != null" >
               user_id = #{userId},
            </if>
         	<if test="income != null" >
               income = #{income},
            </if>
         	<if test="incomeType != null" >
               income_type = #{incomeType},
            </if>
         	<if test="state != null" >
               `state` = #{state},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
	    </set>
        where
			staff_income_id = #{staffIncomeId}
	</update>

	<!-- 删除 -->
	<delete id="deleteById" parameterType="FastStaffIncomeDetailPO">
		delete from fast_staff_income_detail
		<where>
			<if test="id != null">
				and id = #{id}
			</if>
			<if test="staffIncomeId != null">
				and `staff_income_id` = #{staffIncomeId}
			</if>
		</where>
	</delete>
</mapper>
