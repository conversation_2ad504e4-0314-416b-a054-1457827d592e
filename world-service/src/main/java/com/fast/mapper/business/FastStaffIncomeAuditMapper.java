/*
 * Powered By fast.up
 */
package com.fast.mapper.business;

import com.fast.po.business.FastStaffIncomeAuditPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStaffIncomeAuditMapper {

    // 通过id查询单个对象
    FastStaffIncomeAuditPO queryById(FastStaffIncomeAuditPO entity);

    // 通过id查询单个对象
    FastStaffIncomeAuditPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStaffIncomeAuditPO queryOne(FastStaffIncomeAuditPO entity);

    // 查询全部
    List<FastStaffIncomeAuditPO> queryList(FastStaffIncomeAuditPO entity);

    // 查询总数
    int queryCount(FastStaffIncomeAuditPO entity);

    // 可选新增
    int insertSelective(FastStaffIncomeAuditPO entity);

    // 批量新增
    int insertBatch(List<FastStaffIncomeAuditPO> list);

    // 更新
    int updateById(FastStaffIncomeAuditPO entity);

}
