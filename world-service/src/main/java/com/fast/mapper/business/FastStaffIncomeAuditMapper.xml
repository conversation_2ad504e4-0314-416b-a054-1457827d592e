<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffIncomeAuditMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStaffIncomeAudit_columns">
		select t.`id`,t.`is_audit`,t.`audit_user`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStaffIncomeAuditPO">
		<include refid="FastStaffIncomeAudit_columns" />
	    from fast_staff_income_audit t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStaffIncomeAuditPO" resultType="FastStaffIncomeAuditPO">
		<include refid="FastStaffIncomeAudit_columns" />
	    from fast_staff_income_audit t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStaffIncomeAuditPO" resultType="FastStaffIncomeAuditPO">
		<include refid="FastStaffIncomeAudit_columns" />
	    from fast_staff_income_audit t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStaffIncomeAuditPO" resultType="int">
		select count(*)
	    from fast_staff_income_audit t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="isAudit != null">
			and t.`is_audit` = #{isAudit}
		</if>
		<if test="auditUser != null">
			and t.`audit_user` = #{auditUser}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffIncomeAuditPO">
        insert into fast_staff_income_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="isAudit != null">is_audit,</if>
	        <if test="auditUser != null">audit_user,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="isAudit != null">#{isAudit},</if>
	        <if test="auditUser != null">#{auditUser},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastStaffIncomeAuditPO">
        insert into fast_staff_income_audit (
         is_audit, audit_user, creator_id, updator_id, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.isAudit}, #{item.auditUser}, #{item.creatorId}, #{item.updatorId}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStaffIncomeAuditPO">
        update fast_staff_income_audit
        <set>
         	<if test="isAudit != null" >
               is_audit = #{isAudit},
            </if>
         	<if test="auditUser != null" >
               audit_user = #{auditUser},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
