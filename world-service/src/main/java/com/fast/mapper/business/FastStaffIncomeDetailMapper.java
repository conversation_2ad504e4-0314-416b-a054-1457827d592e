/*
 * Powered By fast.up
 */
package com.fast.mapper.business;

import com.fast.po.business.FastStaffIncomeDetailPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStaffIncomeDetailMapper {

    // 通过id查询单个对象
    FastStaffIncomeDetailPO queryById(FastStaffIncomeDetailPO entity);

    // 通过id查询单个对象
    FastStaffIncomeDetailPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStaffIncomeDetailPO queryOne(FastStaffIncomeDetailPO entity);

    // 查询全部
    List<FastStaffIncomeDetailPO> queryList(FastStaffIncomeDetailPO entity);

    List<FastStaffIncomeDetailPO> queryIncomeList(FastStaffIncomeDetailPO entity);

    // 查询总数
    int queryCount(FastStaffIncomeDetailPO entity);

    // 可选新增
    int insertSelective(FastStaffIncomeDetailPO entity);

    // 批量新增
    int insertBatch(List<FastStaffIncomeDetailPO> list);

    // 更新
    int updateById(FastStaffIncomeDetailPO entity);

    int updateByStaffIncomeId(FastStaffIncomeDetailPO entity);

    // 删除
    int deleteById(FastStaffIncomeDetailPO entity);

}
