<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.business.FastStaffRetailRechargeDayMapper">

	<!-- 批量新增 -->
	<insert id="insertUpdateBatch">
		insert into fast_staff_retail_recharge_day(
			`date`,retail_id,content_type,media_type,retail_name,retail_type,money_recharge, money_profit, virtual_recharge, virtual_profit,
			normal_recharge, normal_profit, virtual_refund, normal_refund, money_refund,
		    create_time,update_time
		) (SELECT DATE_FORMAT(#{startDate},'%Y-%m-%d') 'date',
			  if(r.id is null, 0, r.id) retail_id,
			  mediaType,
			  if(r.retail_name is null, '', r.retail_name) retail_name,
			  if(r.retail_type is null, 0, r.retail_type) retail_type,
			  sum(money_recharge) money_recharge,
			  sum(money_profit) money_profit,
			  sum(if(pay_form=8,money_recharge,0)) virtual_recharge,
			  sum(if(pay_form=8,money_profit,0)) virtual_profit,
			  sum(if(pay_form=8,0,money_recharge)) normal_recharge,
			  sum(if(pay_form=8,0,money_profit)) normal_profit,
			  sum(if(a.virtualRefund is null, 0, a.virtualRefund)) virtual_refund,
			  sum(if(a.normalRefund is null, 0, a.normalRefund)) normal_refund,
			  sum(if(a.moneyRefund is null, 0, a.moneyRefund)) money_refund,
			  NOW() create_time,
			  NOW() update_time,
		a.content_type
		   FROM (
				(select a.retail_id,
		a.content_type,
					if(l.adv_media_id is null,0,l.adv_media_id) mediaType,
					money_recharge, money_profit,
					0 virtualRefund, 0 normalRefund, 0 moneyRefund, pay_form
				from `fast_member_order_recharge` a force index(pay_time)
				left join fast_link l on a.link_id=l.id
				 where pay_time BETWEEN #{startDate} and #{endDate} and a.state=1) union all
				(select a.retail_id,a.content_type,if(l.adv_media_id is null,0,l.adv_media_id) mediaType,
				        0 money_recharge, 0 money_profit,
						if(pay_form=8, r.money_refund, 0) virtualRefund,
				        if(pay_form=8, 0, r.money_refund) normalRefund,
				        r.money_refund moneyRefund,
				        a.pay_form
				 from `fast_member_order_recharge` a
				     join fast_member_order_refund r on a.id=r.order_id
					 left join fast_link l on a.link_id=l.id
				 where refund_time BETWEEN #{startDate} and #{endDate}
				   and a.state=1)
			) a left join fast_retail r on a.retail_id=r.id
		   GROUP BY a.retail_id,a.mediaType,a.content_type)
		   ON DUPLICATE KEY UPDATE money_recharge=values(money_recharge),
			 `money_profit`=values(money_profit),
			 `virtual_recharge`=values(virtual_recharge),
			 `virtual_profit`=values(virtual_profit),
			 `virtual_refund`=values(virtual_refund),
			 `normal_recharge`=values(normal_recharge),
			 `normal_profit`=values(normal_profit),
			 `normal_refund`=values(normal_refund),
			 `money_refund`=values(money_refund),
		     `update_time`=values(update_time);
	</insert>

</mapper>
