<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.analysis.DramaDetailAnalysisMapper">

    <!--用户注册人数分析  -->
    <select id="queryRegisterMemberCountList" parameterType="DramaAnalysisPO" resultType="Integer">
        select count(*)
        from fast_member t
        left join fast_member_link ml on ml.member_id=t.id
        left join fast_link l on l.id=ml.first_link_id
        <where>
            <if test="dramaId != null">
                and l.`drama_id` = #{dramaId}
            </if>
            <if test="linkId != null">
                and ml.`last_link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and ml.`last_link_id` in (${linkIds})
            </if>
            <if test="contentType != null">
                and t.`content_type` = #{contentType}
            </if>
            <if test="retailIds != null and retailIds.length()>0">
                and t.`retail_id` in (${retailIds})
            </if>
            <if test="miniIds != null and miniIds.length()>0">
                and t.`mini_id` in (${miniIds})
            </if>
            <if test="createTimeS != null">
                and t.`create_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`create_time` &lt;= #{createTimeE}
            </if>
            <if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
                and l.`link_type` != 3
            </if>
        </where>
    </select>

    <select id="queryRegisterMemberCountListV1" parameterType="DramaAnalysisPO" resultType="Integer">
        SELECT
            IFNULL(sum(t1.num_day),0)
        FROM
            fast_statis_link t1
            LEFT JOIN fast_link t2 ON t1.link_id = t2.id
            <where>
                <if test="dramaId != null">
                    AND t2.drama_id = #{dramaId}
                </if>
                <if test="statisDate != null">
                    AND t1.statis_date = #{statisDate}
                </if>
                <if test="contentType != null">
                    AND t2.content_type = #{contentType}
                </if>
                <if test="retailIds != null and retailIds.length()>0">
                    AND t2.retail_id in (${retailIds})
                </if>
                <if test="linkIds != null and linkIds.length()>0">
                    AND t2.id in (${linkIds})
                </if>
                <if test="advUserIds != null and advUserIds.length()>0">
                    AND t2.adv_user_id in (${advUserIds})
                </if>
                <if test="miniIds != null and miniIds.length()>0">
                    AND t2.mini_id in (${miniIds})
                </if>
        </where>
    </select>

    <!--观看人数分析  -->
    <select id="queryWatchMemberCountList" parameterType="DramaAnalysisPO" resultType="Integer">
        select count(distinct t.member_id)
        from fast_member_recent_log t
        <where>
            <include refid="whereSQL"/>
            <if test="dramaId != null and @com.fast.utils.thread.DramaIdContext@getDramaId() == null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="seriesNum != null">
                and t.`series_num` = #{seriesNum}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            <if test="memberIds != null and memberIds.length()>0">
                and t.`member_id` in (${memberIds})
            </if>
            <if test="createTimeS != null">
                and t.`create_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`create_time` &lt;= #{createTimeE}
            </if>
        </where>
    </select>

    <!--观看人数分析  -->
    <select id="queryWatchMemberCountListSd" parameterType="DramaAnalysisPO" resultType="Integer">
        select count( t.member_id)
        from fast_member_recent_series_day t
        <where>
            <include refid="whereSQL"/>
            <if test="dramaId != null and @com.fast.utils.thread.DramaIdContext@getDramaId() == null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="seriesNum != null">
                and t.`series_num` = #{seriesNum}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            <if test="memberIds != null and memberIds.length()>0">
                and t.`member_id` in (${memberIds})
            </if>
            <if test="createTimeSI != null">
                and t.`create_date` &gt;= #{createTimeSI}
            </if>
            <if test="createTimeEI != null">
                and t.`create_date` &lt;= #{createTimeEI}
            </if>
        </where>
    </select>

    <!--K币消费值分析  -->
    <select id="queryCoinConsumeList" parameterType="DramaAnalysisPO" resultType="Long">
        select sum(t.coin_recharge_consume+t.coin_give_consume)
        from fast_member_order_consume t
        left join fast_link t1 on t.link_id = t1.id
        <where>
            t.coin_change_id=0
            <include refid="whereSQL"/>
            <if test="dramaId != null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            <if test="createTimeS != null">
                and t.`create_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`create_time` &lt;= #{createTimeE}
            </if>
            <if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
                and t1.`link_type` != 3
            </if>
            <if test="advUserIds != null and advUserIds.length()>0">
                and t1.adv_user_id in (${advUserIds})
            </if>
        </where>
    </select>

    <!--短剧完播人数分析  -->
    <select id="queryWatchFinishMemberCountV2" parameterType="DramaAnalysisPO" resultType="int">
        select count(distinct t.member_id) watchFinishMemberCount
        from fast_member_recent_drama_finish t
        left join fast_link t1 on t.link_id = t1.id
        <where>
            <include refid="whereSQL"/>
            <if test="memberIds != null and memberIds.length()>0">
                and t.`member_id` in (${memberIds})
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            <if test="miniIds != null and miniIds.length()>0">
                and t.`mini_id` in (${miniIds})
            </if>
            <if test="dramaId != null ">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="dramaIds != null and dramaIds.length()>0">
                and t.`drama_id` in (${dramaIds})
            </if>
            <if test="finishDateS != null">
                and t.`finish_date` &gt;= #{finishDateS}
            </if>
            <if test="finishDateE != null">
                and t.`finish_date` &lt;= #{finishDateE}
            </if>
            <if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
                and t1.`link_type` != 3
            </if>
        </where>
    </select>

    <!--短剧完播人数分析  -->
    <select id="queryWatchFinishMemberCountList" parameterType="DramaAnalysisPO" resultType="Integer">
        select count(*)
        from (
            select t.drama_id dramaId,t.`member_id` memberId
                ,count(distinct t.series_num) watchCount <!--单个用户看某一个剧的剧集数-->
                ,(select o.series_num_update from fast_drama o where o.id = t.drama_id) seriesNumUpdate
            from fast_member_recent_log t
            <where>
                <include refid="whereSQL"/>
                <if test="memberIds != null and memberIds.length()>0">
                    and t.`member_id` in (${memberIds})
                </if>
                <if test="dramaId != null and @com.fast.utils.thread.DramaIdContext@getDramaId() == null">
                    and t.`drama_id` = #{dramaId}
                </if>
                <!--<if test="watchTimeS != null">
                    and t.`create_time` &gt;= #{watchTimeS}
                </if>-->
                <!--在指定时间之前看完的都算完播-->
                <if test="watchTimeE != null">
                    and t.`create_time` &lt;= #{watchTimeE}
                </if>
                <if test="seriesNum != null">
                    and t.`series_num` = #{seriesNum}
                </if>
                <if test="playState != null">
                    and t.`play_state` = #{playState}
                </if>
            </where>
            group by t.`drama_id`, t.`member_id`
            having watchCount = seriesNumUpdate
        ) t1
    </select>

    <!--指定时间段看过某一集的用户id  -->
    <select id="queryWatchMemberList" parameterType="DramaAnalysisPO" resultType="Long">
        select t.`member_id` memberId
        from fast_member_recent_log t
        <where>
            <include refid="whereSQL"/>
            <if test="dramaId != null and @com.fast.utils.thread.DramaIdContext@getDramaId() == null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
            <if test="seriesNum != null">
                and t.`series_num` = #{seriesNum}
            </if>
            <if test="playState != null">
                and t.`play_state` = #{playState}
            </if>
        </where>
    </select>

    <!--用户充值金额分析  -->
    <select id="queryRechargeMoneyList" parameterType="DramaAnalysisPO" resultType="java.math.BigDecimal">
        select sum(t.money_recharge)
        from fast_member_order_recharge t
        <where>
            t.state=1 and t.coin_change_id=0
            <include refid="whereSQL"/>
            <if test="dramaId != null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            <if test="createTimeS != null">
                and t.`pay_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`pay_time` &lt;= #{createTimeE}
            </if>
            <if test="addState != null">
                and t.`add_state` = #{addState}
            </if>
        </where>
    </select>

    <!--用户充值金额/人数分析  -->
    <select id="queryRechargeDataList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select sum(t.money_recharge) rechargeMoneySum, count(distinct t.member_id) rechargeMemberCount
        from fast_member_order_recharge t
        <where>
            t.state=1 and t.coin_change_id=0
            <include refid="whereSQL"/>
            <if test="dramaId != null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            <if test="createTimeS != null">
                and t.`pay_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`pay_time` &lt;= #{createTimeE}
            </if>
            <if test="addState != null">
                and t.`add_state` = #{addState}
            </if>
            <if test="contentType != null">
            AND t.content_type=#{contentType}
            </if>
            <if test = "retailTypes != null">
             AND EXISTS (SELECT * FROM fast_retail WHERE id = t.retail_id AND retail_flag = 1 AND del_flag = 0 AND FIND_IN_SET( retail_type, #{retailTypes} ))
            </if>
        </where>
    </select>

    <!--用户充值人数分析  -->
    <select id="queryRechargeMemberCountList" parameterType="DramaAnalysisPO" resultType="Integer">
        select count(distinct t.member_id)
        from fast_member_order_recharge t
        <where>
            t.state=1 and t.coin_change_id=0
            <include refid="whereSQL"/>
            <if test="dramaId != null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            <if test="createTimeS != null">
                and t.`pay_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`pay_time` &lt;= #{createTimeE}
            </if>
            <if test="addState != null">
                and t.`add_state` = #{addState}
            </if>
        </where>
    </select>

    <!--用户充值人数分析  -->
    <select id="queryRechargeMemberGroupList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t.member_id
            ,group_concat(t.money_profit order by t.id) rechargeMoneyStr
            ,group_concat(t.order_type order by t.id) rechargeTypeStr
            ,count(*) rechargeCount
        from fast_member_order_recharge t
        left join fast_member_order_recharge_adv t1 on t.id = t1.id
        left join fast_link t2 on t.link_id = t2.id
        <where>
            t.state=1 and t.coin_change_id=0
            <include refid="whereSQL"/>
            <if test="dramaId != null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            <if test="memberIds != null and memberIds.length()>0">
                and t.`member_id` in (${memberIds})
            </if>
            <if test="createTimeS != null">
                and t.`pay_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`pay_time` &lt;= #{createTimeE}
            </if>
            <if test="contentType != null">
                and t.content_type=#{contentType}
            </if>
            <if test="registerTimeS != null">
               and t.register_time &gt;= #{registerTimeS}
            </if>
            <if test="registerTimeE != null">
               and t.register_time &lt;= #{registerTimeE}
            </if>
            <if test="removeMountLinkFlag != null and removeMountLinkFlag==1">
                and t2.link_type != 3
            </if>
        </where>
        group by t.member_id
        having rechargeCount > 0
    </select>

    <!--剧集分析-留存分析  -->
    <select id="queryDramaRemainGroupList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t.series_num, count(distinct t.`member_id`) watchMemberCount
        from fast_member_recent_log t
        <where>
            <include refid="whereSQL"/>
            <if test="dramaId != null and @com.fast.utils.thread.DramaIdContext@getDramaId() == null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            <if test="memberIds != null and memberIds.length()>0">
                and t.`member_id` in (${memberIds})
            </if>
            <if test="officialIds != null and officialIds.length()>0">
                and t.`official_id` in (${officialIds})
            </if>
            <if test="officialId != null">
                and t.`official_id` = #{officialId}
            </if>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
        </where>
        group by t.series_num
        having watchMemberCount > 0
    </select>

    <!--剧集分析-跳出分析  -->
    <select id="queryDramaSkipGroupList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.playSecond, count(t1.`member_id`) skipMemberCount
        from (
            select t.member_id, max(t.`play_second`) playSecond
            from fast_member_recent_log t
            <where>
                <include refid="whereSQL"/>
                <if test="dramaId != null and @com.fast.utils.thread.DramaIdContext@getDramaId() == null">
                    and t.`drama_id` = #{dramaId}
                </if>
                <if test="linkId != null">
                    and t.`link_id` = #{linkId}
                </if>
                <if test="seriesNum != null">
                    and t.`series_num` = #{seriesNum}
                </if>
                <if test="linkIds != null and linkIds.length() > 0">
                    and t.`link_id` in (${linkIds})
                </if>
                <if test="memberIds != null and memberIds.length() > 0">
                    and t.`member_id` in (${memberIds})
                </if>
                <if test="watchTimeS != null">
                    and t.`create_time` &gt;= #{watchTimeS}
                </if>
                <if test="watchTimeE != null">
                    and t.`create_time` &lt;= #{watchTimeE}
                </if>
            </where>
            group by t.member_id
            having playSecond > 0
        ) t1 group by t1.playSecond
    </select>

    <!--剧集分析-剧集数据明细  -->
    <select id="queryDramaDetailSeriesGroupList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.series_num, count(t1.`member_id`) watchMemberCount, sum(if(t1.`play_state`=1,1,0)) watchFinishMemberCount from (
            select t.series_num
            ,t.`member_id`
            ,max(t.`play_state`) play_state
            from fast_member_recent_log t
            <where>
                <include refid="whereSQL"/>
                <if test="linkId != null">
                    and t.`link_id` = #{linkId}
                </if>
                <if test="dramaId != null and @com.fast.utils.thread.DramaIdContext@getDramaId() == null">
                    and t.`drama_id` = #{dramaId}
                </if>
                <if test="linkIds != null and linkIds.length() > 0">
                    and t.`link_id` in (${linkIds})
                </if>
                <if test="memberIds != null and memberIds.length() > 0">
                    and t.`member_id` in (${memberIds})
                </if>
                <if test="officialIds != null and officialIds.length() > 0">
                    and t.`official_id` in (${officialIds})
                </if>
                <if test="officialId != null">
                    and t.`official_id` = #{officialId}
                </if>
                <if test="watchTimeS != null">
                    and t.`create_time` &gt;= #{watchTimeS}
                </if>
                <if test="watchTimeE != null">
                    and t.`create_time` &lt;= #{watchTimeE}
                </if>
            </where>
            group by t.series_num, t.`member_id`
        ) t1 group by t1.series_num
    </select>

    <!--每集消费K币赠送分析 -->
    <select id="querySeriesCoinConsumeList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t.series_num
            ,sum(t.coin_give_consume) coinGiveConsume
            ,sum(t.coin_recharge_consume) coinRechargeConsume
        from fast_member_order_consume t
        <where>
            t.coin_change_id=0
            <include refid="whereSQL"/>
            <if test="dramaId != null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="memberIds != null and memberIds.length()>0">
                and t.`member_id` in (${memberIds})
            </if>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            group by t.series_num
        </where>
    </select>

    <!-- 公共条件  -->
    <sql id="whereSQL">
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="retailIds != null and retailIds.length()>0">
            and t.`retail_id` in (${retailIds})
        </if>
        <if test="miniId != null">
            and t.`mini_id` = #{miniId}
        </if>
        <if test="miniIds != null and miniIds.length()>0">
            and t.`mini_id` in (${miniIds})
        </if>
    </sql>

    <select id="queryRechargeDataListGroupByRetailType" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        SELECT
            ROUND(IFNULL(sum( tmp.usRechargeMoney ),0.00),2) as usRechargeMoney,
            IFNULL(sum( tmp.usRecharegeMemberCount ),0) as usRecharegeMemberCount,
            ROUND(IFNULL(sum( tmp.aloneRechargeMoney ),0.00),2) as aloneRechargeMoney,
            IFNULL(sum( tmp.aloneRechargeMemberCount ),0) as aloneRechargeMemberCount
            from  (SELECT
            retail.retail_type,
            IF
            ( retail.retail_type IN ( 1, 2 ), sum( t.money_profit ), 0.00 ) usRechargeMoney,
            IF
            ( retail.retail_type IN ( 1, 2 ), count( DISTINCT member_id ), 0.00 ) usRecharegeMemberCount,
            IF
            ( retail.retail_type = 3, sum( t.money_profit ), 0.00 ) aloneRechargeMoney,
            IF
            ( retail.retail_type = 3, count( DISTINCT member_id ), 0.00 ) aloneRechargeMemberCount
        from fast_member_order_recharge t
           left join fast_retail retail on t.retail_id = retail.id
           left join fast_link link on t.link_id = link.id
        <where>
            t.state=1 and t.coin_change_id=0
            <include refid="whereSQL"/>
            <if test="dramaId != null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="linkIds != null and linkIds.length()>0">
                and t.`link_id` in (${linkIds})
            </if>
            <if test="advUserIds != null and advUserIds.length()>0">
                and t.`adv_user_id` in (${advUserIds})
            </if>
            <if test="createTimeS != null">
                and t.`pay_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`pay_time` &lt;= #{createTimeE}
            </if>
            <if test="addState != null">
                and t.`add_state` = #{addState}
            </if>
            <if test="contentType != null">
                AND t.content_type=#{contentType}
            </if>
            <if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
                AND link.link_type != 3
            </if>
        </where>
        GROUP BY
        retail.retail_type)tmp
    </select>
</mapper>
