<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.analysis.RetailIndexMapper">

    <!--充值金额 -->
    <select id="getRecharge" parameterType="RetailIndexPO" resultType="java.math.BigDecimal">
        select ifnull(sum(t.money_recharge),0)
        from fast_member_order_recharge t
        left join fast_link l on l.id = t.link_id
        <include refid="getRechargeWhere"/>
    </select>

    <!--充值金额 -->
    <select id="getRechargeProfit" parameterType="RetailIndexPO" resultType="java.math.BigDecimal">
        select ifnull(sum(t.money_profit),0)
        from fast_member_order_recharge t
        left join fast_link l on l.id = t.link_id
        <include refid="getRechargeWhere"/>
    </select>

    <sql id="getRechargeWhere">
        <where>
            and t.`state` = 1 and t.coin_change_id=0 and t.link_id > 0
            <if test="createTimeS != null">
                and t.`pay_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`pay_time` &lt;= #{createTimeE}
            </if>
            <if test="advMediaId != null">
                and l.adv_media_id = #{advMediaId}
            </if>
            <if test="officialId != null">
                and l.official_id = #{officialId}
            </if>
            <if test="officialIds != null and officialIds.length()>0">
                and l.`official_id` in (${officialIds})
            </if>
            <if test="contentType != null">
                and t.`content_type` = #{contentType}
            </if>
            <include refid="commonSql"/>
        </where>
    </sql>

    <!--总充值金额和笔数 -->
    <select id="getRechargeOrderMoney" parameterType="RetailIndexPO" resultType="RetailIndexPO">
        select count(*) rechargeOrderSum
            ,ifnull(sum(t.money_recharge),0) rechargeMoneySum
            ,ifnull(sum(t.money_profit),0) rechargeMoneyProfitSum
        from fast_member_order_recharge t
        left join fast_link l on l.id = t.link_id
        <where>
            t.`state` = 1 and t.coin_change_id=0 and t.link_id > 0
            <if test="createTimeS != null">
                and t.`pay_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`pay_time` &lt;= #{createTimeE}
            </if>
            <if test="advMediaId != null">
                and l.adv_media_id = #{advMediaId}
            </if>
            <if test="officialId != null">
                and t.official_id = #{officialId}
            </if>
            <if test="officialIds != null and officialIds.length()>0">
                and t.`official_id` in (${officialIds})
            </if>
            <if test="contentType != null">
                and t.`content_type` = #{contentType}
            </if>
            <include refid="commonSql"/>
        </where>
    </select>

    <!--消耗金额 -->
    <select id="getConsume" parameterType="RetailIndexPO" resultType="java.math.BigDecimal">
        select ifnull(sum(t.cost_day),0)
        from fast_statis_link t
        left join fast_link l on l.id = t.link_id
        <where>
            <if test="createTimeS != null">
                and t.`statis_date` &gt;= #{createTimeS, jdbcType=DATE}
            </if>
            <if test="createTimeE != null">
                and t.`statis_date` &lt;= #{createTimeE, jdbcType=DATE}
            </if>
            <if test="advMediaId != null">
                and l.adv_media_id = #{advMediaId}
            </if>
            <if test="officialId != null">
                and l.official_id = #{officialId}
            </if>
            <if test="officialIds != null and officialIds.length()>0">
                and l.`official_id` in (${officialIds})
            </if>
            <if test="contentType != null">
                and l.`content_type` = #{contentType}
            </if>
            <include refid="commonSql"/>
        </where>
    </select>

    <!--核心新增类数据 -->
    <select id="getCoreAddData" parameterType="RetailIndexPO" resultType="RetailIndexPO">
        select
            ifnull(sum(t.num_day),0) numDay
            ,ifnull(sum(t.num_day_rech),0) numDayRech
            ,ifnull(sum(t.num_day_order),0) numDayOrder
            ,ifnull(sum(t.money_day),0) moneyDay
            ,ifnull(sum(ifnull(t.money_profit_day,t.money_day)),0) moneyProfitDay
        from fast_statis_link t
        left join fast_link l on l.id = t.link_id
        <where>
            <if test="createTimeS != null">
                and t.`statis_date` &gt;= #{createTimeS, jdbcType=DATE}
            </if>
            <if test="createTimeE != null">
                and t.`statis_date` &lt;= #{createTimeE, jdbcType=DATE}
            </if>
            <if test="officialId != null">
                and l.official_id = #{officialId}
            </if>
            <if test="officialIds != null and officialIds.length()>0">
                and l.`official_id` in (${officialIds})
            </if>
            <if test="contentType != null">
                and l.`content_type` = #{contentType}
            </if>
            <include refid="commonSql"/>
        </where>
    </select>

    <!--K币消费数据 -->
    <select id="getCoinConsume" parameterType="RetailIndexPO" resultType="RetailIndexPO">
        select
            ifnull(sum(t.coin_recharge_consume),0) coinRechargeConsume
            ,ifnull(sum(t.coin_give_consume),0) coinGiveConsume
        from fast_member_order_consume t
        left join fast_drama d on d.id = t.drama_id
        <where>
            <if test="createTimeS != null">
                and t.`create_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`create_time` &lt;= #{createTimeE}
            </if>
            <if test="officialId != null">
                and t.official_id = #{officialId}
            </if>
            <if test="officialIds != null and officialIds.length()>0">
                and t.`official_id` in (${officialIds})
            </if>
            <if test="contentType != null">
                and d.`content_type` = #{contentType}
            </if>
            <include refid="commonSql"/>
        </where>
    </select>

    <sql id="commonSql">
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="retailIds != null and retailIds.length()>0">
            and t.`retail_id` in (${retailIds})
        </if>
    </sql>

</mapper>
