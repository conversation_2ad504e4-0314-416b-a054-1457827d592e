/*
 * Powered By fast.up
 */
package com.fast.mapper.analysis;

import com.fast.po.analysis.RetailIndexPO;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;

/**
 * 分销商首页
 *
 * <AUTHOR>
 */
@Mapper
public interface RetailIndexMapper {

    BigDecimal getRecharge(RetailIndexPO params);

    BigDecimal getRechargeProfit(RetailIndexPO params);

    RetailIndexPO getRechargeOrderMoney(RetailIndexPO params);

    BigDecimal getConsume(RetailIndexPO params);

    RetailIndexPO getCoreAddData(RetailIndexPO params);

    RetailIndexPO getCoinConsume(RetailIndexPO params);
}
