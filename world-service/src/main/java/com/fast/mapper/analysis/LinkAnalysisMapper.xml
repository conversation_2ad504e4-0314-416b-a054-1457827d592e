<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.analysis.LinkAnalysisMapper">

    <!--各类数据汇总 -->
    <select id="queryCoreAllAnalysisList" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        select f.statis_date
            <if test="groupByColum == 1">,f.link_id id,t.retail_id,t.adv_user_id</if><!--链接-->
            <if test="groupByColum == 2">,t.retail_id id</if>       <!--分销商-->
            <if test="groupByColum == 3">,t.adv_user_id id</if>     <!--优化师-->
            <if test="groupByColum == 4">,t.drama_id id</if>        <!--短剧-->
            <if test="groupByColum == 5">,t.road_id id</if>         <!--链路-->
            ,sum(if(f.num_day>0,1,0)) validLinkCount                    <!--有效链接数-->
            ,group_concat(if(f.num_day>0,f.link_id,'')) linkIds         <!--linkIds-->
            ,ifnull(sum(f.num_day),0) memberCountAddAll                 <!--当日新增用户-->
            ,ifnull(sum(f.num_day_rech),0) rechargeMemberCountAdd       <!--当日充值用户-->
            ,ifnull(sum(f.num_dAll_rech),0) rechargeMemberCountAddAll   <!--累计充值用户-->
            ,ifnull(sum(ifnull(f.money_profit_all_day,f.money_all_day)),0) rechargeMoneySumAddAll      <!--累计充值金额-->
            ,ifnull(sum(ifnull(f.money_profit_day,f.money_day)),0) rechargeMoneySumAdd             <!--当日充值金额-->
            ,ifnull(sum(f.cost_day),0) adMoneyConsume                   <!--总成本/消耗-->
            ,ifnull(group_concat(f.num_d60_rech SEPARATOR '#'), 0) numD60Rech      <!--D0-D60充值人数-->
            ,ifnull(group_concat(ifnull(f.money_profit_d60_rech,f.money_d60_rech) SEPARATOR '#'), 0) moneyD60Rech  <!--D0-D60充值金额-->
        from fast_link t INNER JOIN fast_statis_link f ON t.id = f.link_id
        left join fast_user u on u.id = t.adv_user_id
        <where>
            <include refid="whereSQLCost"/>
        </where>
        group by
        <if test="groupByColum == 0">f.statis_date</if>     <!--整体-->
        <if test="groupByColum == 1">f.link_id</if>         <!--链接-->
        <if test="groupByColum == 2">t.retail_id</if>       <!--分销商-->
        <if test="groupByColum == 3">t.adv_user_id</if>     <!--优化师-->
        <if test="groupByColum == 4">t.drama_id</if>        <!--短剧-->
        <if test="groupByColum == 5">t.road_id</if>         <!--链路-->
        <if test="noLaunchZero == 1 or noAddMemberZero == 1"> having 1=1
            <if test="noLaunchZero == 1">and adMoneyConsume>0</if>
            <if test="noAddMemberZero == 1">and memberCountAddAll>0</if>
        </if>
    </select>

    <!--新染色用户 -->
    <select id="queryAddColorMemberSumList" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        select DATE_FORMAT(h.create_time,'%Y-%m-%d') statisDate
            <if test="groupByColum == 1">,h.link_id id,t.retail_id,t.adv_user_id</if><!--链接-->
            <if test="groupByColum == 2">,t.retail_id id</if>       <!--分销商-->
            <if test="groupByColum == 3">,t.adv_user_id id</if>     <!--优化师-->
            <if test="groupByColum == 4">,t.drama_id id</if>        <!--短剧-->
            <if test="groupByColum == 5">,t.road_id id</if>         <!--链路-->
            ,count(h.member_id) memberCountAddColor
        from fast_member_link_his h
        left join fast_link t on h.link_id=t.id
        <where>
            <include refid="whereSQLNew"/>
        </where>
        group by
        <if test="groupByColum == 0">DATE_FORMAT(h.create_time,'%Y-%m-%d')</if>     <!--整体-->
        <if test="groupByColum == 1">h.link_id</if>         <!--链接-->
        <if test="groupByColum == 2">t.retail_id</if>       <!--分销商-->
        <if test="groupByColum == 3">t.adv_user_id</if>     <!--优化师-->
        <if test="groupByColum == 4">t.drama_id</if>        <!--短剧-->
        <if test="groupByColum == 5">t.road_id</if>         <!--链路-->
    </select>

    <!--查询创建的链接数 -->
    <select id="queryAllLinkCount" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        select count(*) allLinkCount
            ,group_concat(t.id) linkIds                             <!--linkIds-->
            <if test="groupByColum == 2">,t.retail_id id</if>       <!--分销商-->
            <if test="groupByColum == 3">,t.adv_user_id id</if>     <!--优化师-->
            <if test="groupByColum == 4">,t.drama_id id</if>        <!--短剧-->
            <if test="groupByColum == 5">,t.road_id id</if>         <!--链路-->
        from fast_link t
        <where>
            <if test="createTimeS != null">
                and t.`create_time` &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                and t.`create_time` &lt;= #{createTimeE}
            </if>
            <if test="dramaId != null">
                and t.`drama_id`  =  #{dramaId}
            </if>
            <if test="dramaIds != null and dramaIds.length()>0">
                and t.`drama_id` in (${dramaIds})
            </if>
            <if test="retailId != null">
                and t.`retail_id` = #{retailId}
            </if>
            <if test="retailIds != null and retailIds.length()>0">
                and t.`retail_id` in (${retailIds})
            </if>
            <if test="advUserId != null">
                and t.`adv_user_id` = #{advUserId}
            </if>
            <if test="advUserIds != null and advUserIds.length()>0">
                and t.`adv_user_id` in (${advUserIds})
            </if>
            <if test="miniId != null">
                and t.`mini_id` = #{miniId}
            </if>
            <if test="miniIds != null and miniIds.length()>0">
                and t.`mini_id` in (${miniIds})
            </if>
            <if test="advMediaId != null">
                and t.`adv_media_id` = #{advMediaId}
            </if>
            <if test="appType != null">
                and t.`app_type` = #{appType}
            </if>
            <if test="contentType != null">AND t.content_type=#{contentType}</if>
        </where>
        group by
        <if test="groupByColum == 2">t.retail_id</if>       <!--分销商-->
        <if test="groupByColum == 3">t.adv_user_id</if>     <!--优化师-->
        <if test="groupByColum == 4">t.drama_id</if>        <!--短剧-->
        <if test="groupByColum == 5">t.road_id</if>         <!--链路-->
    </select>

    <!--每日数据汇总 -->
    <select id="queryCorePerDayAnalysisList" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        select f.statis_date,f.link_id,t.retail_id,t.adv_user_id,t.drama_id,t.road_id
            ,sum(if(f.num_day>0,1,0)) validLinkCount                    <!--有效链接数-->
            ,ifnull(sum(f.num_day),0) memberCountAdd                    <!--当日新增用户-->
            ,ifnull(sum(f.num_day_rech),0) rechargeMemberCountAdd       <!--当日充值用户-->
            ,ifnull(sum(f.num_dAll_rech),0) rechargeMemberCountAddAll   <!--累计充值用户-->
            ,ifnull(sum(f.money_profit_day),0) rechargeMoneySumAddAll          <!--当日充值金额-->
            ,ifnull(sum(f.cost_day),0) adMoneyConsume                   <!--总成本/消耗-->
            ,group_concat(f.num_d60_rech SEPARATOR '#') numD60Rech      <!--D0-D60充值人数-->
            ,group_concat(f.money_profit_d60_rech SEPARATOR '#') moneyD60Rech  <!--D0-D60充值金额-->
        from fast_link t INNER JOIN fast_statis_link f ON t.id = f.link_id
        left join fast_user u on u.id = t.adv_user_id
        <where>
            <include refid="whereSQLCost"/>
            and f.statis_date >= DATE_FORMAT(t.create_time,'%Y-%m-%d')
        </where>
        group by f.statis_date
        <if test="noLaunchZero == 1 or noAddMemberZero == 1"> having 1=1
            <if test="noLaunchZero == 1">and adMoneyConsume>0</if>
            <if test="noAddMemberZero == 1">and memberCountAdd>0</if>
        </if>
    </select>

    <!--今日数据汇总 -->
    <select id="queryTodayDataAnalysisList" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        select f.statis_date dataDay
            <if test="groupByColum == 1">,f.link_id id</if>         <!--链接-->
            <if test="groupByColum == 2">,t.retail_id id</if>       <!--分销商-->
            <if test="groupByColum == 3">,t.adv_user_id id</if>     <!--优化师-->
            <if test="groupByColum == 4">,t.drama_id id</if>        <!--短剧-->
            <if test="groupByColum == 5">,t.road_id id</if>         <!--链路-->
            ,ifnull(sum(cost_day),0) adMoneyConsumeToday                <!--当日投放成本-->
            ,ifnull(sum(num_day),0) memberCountAddToday                 <!--当日新增用户-->
            ,ifnull(sum(num_day_rech),0) rechargeMemberCountAddToday    <!--当日新增充值用户-->
        from fast_link t INNER JOIN fast_statis_link f ON t.id = f.link_id
        left join fast_user u on u.id = t.adv_user_id
        <where>
            <include refid="whereSQLCost"/>
        </where>
        group by
        <if test="groupByColum == 1">t.id</if>              <!--链接-->
        <if test="groupByColum == 2">t.retail_id</if>       <!--分销商-->
        <if test="groupByColum == 3">t.adv_user_id</if>     <!--优化师-->
        <if test="groupByColum == 4">t.drama_id</if>        <!--短剧-->
        <if test="groupByColum == 5">t.road_id</if>         <!--链路-->
    </select>

    <!--累计数据 -->
    <select id="queryDetailSummaryAnalysisList" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        select t.link_name, t.create_time, t.retail_id
            <if test="groupByColum == 1">,f.link_id id</if>         <!--链接-->
            <if test="groupByColum == 2">,t.retail_id id</if>       <!--分销商-->
            <if test="groupByColum == 3">,t.adv_user_id id</if>     <!--优化师-->
            <if test="groupByColum == 4">,t.drama_id id</if>        <!--短剧-->
            <if test="groupByColum == 5">,t.road_id id</if>         <!--链路-->
            ,ifnull(sum(f.num_day),0) memberCountAddAll                 <!--累计新增用户-->
            ,ifnull(sum(f.num_dAll_rech),0) rechargeMemberCountAddAll   <!--累计充值用户-->
            ,ifnull(sum(f.money_profit_dAll_rech),0) rechargeMoneySumAddAll    <!--累计充值金额-->
            ,ifnull(sum(f.num_all_day_order),0) rechargeOrderAddSumAll  <!--累计充值笔数-->
            ,ifnull(sum(f.cost_day),0) adMoneyConsume                   <!--总成本/消耗-->
        from fast_link t INNER JOIN fast_statis_link f ON t.id = f.link_id
        left join fast_user u on u.id = t.adv_user_id
        <where>
            <include refid="whereSQLCost"/>
        </where>
        group by
        <if test="groupByColum == 1">t.id</if>              <!--链接-->
        <if test="groupByColum == 2">t.retail_id</if>       <!--分销商-->
        <if test="groupByColum == 3">t.adv_user_id</if>     <!--优化师-->
        <if test="groupByColum == 4">t.drama_id</if>        <!--短剧-->
        <if test="groupByColum == 5">t.road_id</if>         <!--链路-->
    </select>

    <!--今日数据监测 -->
    <select id="queryDetailTodayAnalysisList" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        select f.statis_date,f.link_id,t.link_name,t.create_time,t.retail_id,t.adv_user_id,t.drama_id,t.road_id
            ,ifnull(sum(f.num_day),0) memberCountAdd                 <!--今日新增用户-->
            ,ifnull(sum(f.num_day_rech),0) rechargeMemberCountAdd    <!--今日充值用户-->
            ,ifnull(sum(f.money_profit_day),0) rechargeMoneySumAdd          <!--今日充值金额-->
            ,ifnull(sum(f.num_day_order),0) rechargeCount            <!--今日充值笔数-->
            ,ifnull(sum(f.cost_day),0) adMoneyConsume                <!--今日成本/消耗-->
        from fast_link t INNER JOIN fast_statis_link f ON t.id = f.link_id
        left join fast_user u on u.id = t.adv_user_id
        <where>
            <include refid="whereSQLCost"/>
        </where>
        group by
        <if test="groupByColum == 1">t.id</if>              <!--链接-->
        <if test="groupByColum == 2">t.retail_id</if>       <!--分销商-->
        <if test="groupByColum == 3">t.adv_user_id</if>     <!--优化师-->
        <if test="groupByColum == 4">t.drama_id</if>        <!--短剧-->
        <if test="groupByColum == 5">t.road_id</if>         <!--链路-->
    </select>

    <!--新染色用户 -->
    <select id="queryAddColorMemberSum" parameterType="LinkAnalysisPO" resultType="int">
        select count(*)
        from fast_link t INNER JOIN fast_member_link_his h ON t.id = h.link_id
        <where>
            <include refid="whereSQLNew"/>
        </where>
    </select>

    <!--今日新染色用户 -->
    <select id="queryTodayAddColorMember" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        select
            <if test="groupByColum == 1">t.id id</if>              <!--链接-->
            <if test="groupByColum == 2">t.retail_id id</if>       <!--分销商-->
            <if test="groupByColum == 3">t.adv_user_id id</if>     <!--优化师-->
            <if test="groupByColum == 4">t.drama_id id</if>        <!--短剧-->
            <if test="groupByColum == 5">t.road_id id</if>         <!--链路-->
            ,count(*) dataCount
        from fast_link t INNER JOIN fast_member_link_his h ON t.id = h.link_id
        <where>
            <include refid="whereSQLNew"/>
        </where>
        group by
        <if test="groupByColum == 1">t.id</if>              <!--链接-->
        <if test="groupByColum == 2">t.retail_id</if>       <!--分销商-->
        <if test="groupByColum == 3">t.adv_user_id</if>     <!--优化师-->
        <if test="groupByColum == 4">t.drama_id</if>        <!--短剧-->
        <if test="groupByColum == 5">t.road_id</if>         <!--链路-->
    </select>

    <!--查询注册用户数 -->
    <select id="queryAddNewMemberSum" parameterType="LinkAnalysisPO" resultType="int">
        select count(*)
        from fast_member_link l
        <where>
            <if test="statisDate != null">
                and l.`first_link_time` between CONCAT(#{statisDate},' 00:00:00') AND CONCAT(#{statisDate},' 23:59:59')
            </if>
            <if test="linkId != null">
                and l.`first_link_id` = #{linkId}
            </if>
        </where>
    </select>

    <!--查询今日统计数 -->
    <select id="queryTodayRechargeSum" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        select sum(t.money_profit) rechargeMoneySum
            ,count(distinct t.member_id) rechargeMemberCount
            ,count(*) rechargeCount
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <where>
            t.state=1 and t.coin_change_id=0
            <if test="addState != null">
                and t.add_state = #{addState}
            </if>
            <if test="dramaId != null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="linkId != null">
                and t.`link_id` = #{linkId}
            </if>
            <if test="advUserId != null">
                and t.`adv_user_id` = #{advUserId}
            </if>
            <if test="retailId != null">
                and t.`retail_id` = #{retailId}
            </if>
            <if test="orderType != null">
                and t.`order_type` = #{orderType}
            </if>
            <if test="statisDate != null and statisDate.length()>0">
                and t.`pay_time` between CONCAT(#{statisDate},' 00:00:00') AND CONCAT(#{statisDate},' 23:59:59')
            </if>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
        </where>
    </select>

    <!-- 公共条件  -->
    <sql id="whereSQLNew">
        <if test="createTimeS != null">
            and h.`create_time` &gt;= #{createTimeS}
        </if>
        <if test="createTimeE != null">
            and h.`create_time` &lt;= #{createTimeE}
        </if>
        <if test="statisDate != null">
            and h.`create_time` between CONCAT(#{statisDate},' 00:00:00') AND CONCAT(#{statisDate},' 23:59:59')
        </if>
        <if test="linkId != null">
            and t.`id` = #{linkId}
        </if>
        <if test="linkIds != null and linkIds.length()>0">
            and t.`id` in (${linkIds})
        </if>
        <if test="dramaId != null">
            and t.`drama_id`  =  #{dramaId}
        </if>
        <if test="dramaIds != null and dramaIds.length()>0">
            and t.`drama_id` in (${dramaIds})
        </if>
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="retailIds != null and retailIds.length()>0">
            and t.`retail_id` in (${retailIds})
        </if>
        <if test="advUserIds != null and advUserIds.length()>0">
            and t.`adv_user_id` in (${advUserIds})
        </if>
        <if test="advUserId != null">
            and t.`adv_user_id` = #{advUserId}
        </if>
        <if test="roadId != null">
            and t.`road_id` = #{roadId}
        </if>
        <if test="roadIds != null and roadIds.length()>0">
            and t.`road_id` in (${roadIds})
        </if>
        <if test="miniId != null">
            and t.`mini_id` = #{miniId}
        </if>
        <if test="miniIds != null and miniIds.length()>0">
            and t.`mini_id` in (${miniIds})
        </if>
        <if test="advMediaId != null">
            and t.`adv_media_id` = #{advMediaId}
        </if>
        <if test="appType != null">
            and t.`app_type` = #{appType}
        </if>
        <if test="contentType != null">
            and t.`content_type` = #{contentType}
        </if>
    </sql>

    <!-- 公共条件  -->
    <sql id="whereSQLCost">
        <if test="linkIds != null and linkIds.length()>0">
            and t.`id` in (${linkIds})
        </if>
        <if test="linkId != null">
            and t.`id`  =  #{linkId}
        </if>
        <if test="statisDateS != null">
            and f.`statis_date` &gt;= #{statisDateS}
        </if>
        <if test="statisDateE != null">
            and f.`statis_date` &lt;= #{statisDateE}
        </if>
        <if test="statisDate != null">
            and f.`statis_date` = #{statisDate}
        </if>
        <if test="dramaId != null">
            and t.`drama_id`  =  #{dramaId}
        </if>
        <if test="dramaIds != null and dramaIds.length()>0">
            and t.`drama_id` in (${dramaIds})
        </if>
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="retailIds != null and retailIds.length()>0">
            and t.`retail_id` in (${retailIds})
        </if>
        <if test="advUserId != null">
            and t.`adv_user_id` = #{advUserId}
        </if>
        <if test="advUserIds != null and advUserIds.length()>0">
            and t.`adv_user_id` in (${advUserIds})
        </if>
        <if test="roadId != null">
            and t.`road_id` = #{roadId}
        </if>
        <if test="roadIds != null and roadIds.length()>0">
            and t.`road_id` in (${roadIds})
        </if>
        <if test="miniId != null">
            and t.`mini_id` = #{miniId}
        </if>
        <if test="miniIds != null and miniIds.length()>0">
            and t.`mini_id` in (${miniIds})
        </if>
        <if test="advMediaId != null">
            and t.`adv_media_id` = #{advMediaId}
        </if>
        <if test="appType != null">
            and t.`app_type` = #{appType}
        </if>
        <if test="contentType != null">
            and t.content_type=#{contentType}
        </if>
        <if test="feeFlag != null">
            and t.fee_flag = #{feeFlag}
        </if>
    </sql>

    <select id="queryFreeLinkROISummaryAnalysis" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        SELECT
           <include refid="wholeQuerySql"/>
        FROM
            fast_statis_link_free t
            left join fast_link t1 on t.link_id = t1.id
            left join fast_retail t2 on t1.retail_id = t2.id
            LEFT  JOIN fast_statis_link t3 on t.link_id = t3.link_id and t.statis_date = t3.statis_date
        <include refid="whereSQLFreeLink"/>
        GROUP BY
            t.statis_date
        ORDER BY t.statis_date DESC
    </select>

    <select id="queryFreeLinkROIAnalysis" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        SELECT
                <include refid="querygroupBylink"/>
          FROM
                 fast_statis_link_free t
                LEFT JOIN ( SELECT * FROM fast_link ) t1 ON t.link_id = t1.id
                LEFT JOIN fast_retail t2 on t1.retail_id = t2.id
                LEFT JOIN fast_statis_link t3 on t.link_id = t3.link_id and t.statis_date = t3.statis_date
         <include refid="whereSQLFreeLink"/>
         group by t.link_id
         ORDER BY t.link_id DESC
    </select>

    <select id="queryFreeLinkROISummaryAnalysisSummary" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
        select
               sum(tmp.validLinkCount) validLinkCount,
               sum(adMoneyConsume) adMoneyConsume,
               sum(memberCountAdd) memberCountAdd,
               sum(memberCountAddColor) memberCountAddColor,
               sum(convertMemberCount) convertMemberCount,
               sum(convertMemeberCountAd) convertMemeberCountAd,
               sum(convertMemberCountRecharge) convertMemberCountRecharge,
               sum(incomeRecharge) incomeRecharge,
               sum(incomeAd) incomeAd,
               group_concat( tmp.addConvertMemberD60 SEPARATOR "#" ) addConvertMemberD60,
               group_concat( tmp.addConvertMoneyDayD60 SEPARATOR "#" ) addConvertMoneyDayD60
        from
        ( SELECT
            <include refid="wholeQuerySql"/>
        FROM
        fast_statis_link_free t
        left join fast_link t1 on t.link_id = t1.id
        left join fast_retail t2 on t1.retail_id = t2.id
        LEFT  JOIN fast_statis_link t3 on t.link_id = t3.link_id and t.statis_date = t3.statis_date
        <include refid="whereSQLFreeLink"/>
        GROUP BY
        t.statis_date ) tmp
    </select>

    <select id="queryFreeLinkROIAnalysisSummary" parameterType="LinkAnalysisPO" resultType="LinkAnalysisPO">
       select
               sum(tmp.adMoneyConsumeToday) as adMoneyConsumeToday,
               sum(tmp.adMoneyConsume) as adMoneyConsume,
               sum(tmp.memberCountAddToday) as memberCountAddToday,
               sum(tmp.memberCountAddAll) as memberCountAddAll,
               sum(tmp.memberCountAddColorToday) as memberCountAddColorToday,
               sum(tmp.memberCountAddColor) as memberCountAddColor,
               sum(tmp.convertMemberCountToday) as convertMemberCountToday,
               sum(tmp.convertMemberCount) as convertMemberCount,
               sum(tmp.incomeAd) as incomeAd,
               sum(tmp.incomeRecharge) as incomeRecharge,
               GROUP_CONCAT(tmp.addConvertMemberD60 separator "#") as addConvertMemberD60,
               GROUP_CONCAT(tmp.addConvertMoneyDayD60 separator "#") as addConvertMoneyDayD60
       from
        ( SELECT
              <include refid="querygroupBylink"/>
        FROM
        fast_statis_link_free t
        LEFT JOIN fast_link t1 ON t.link_id = t1.id
        LEFT JOIN fast_retail t2 on t1.retail_id = t2.id
        LEFT JOIN fast_statis_link t3 on t.link_id = t3.link_id and t.statis_date = t3.statis_date
        <include refid="whereSQLFreeLink"/>
        group by t.link_id) tmp
    </select>

    <sql id="whereSQLFreeLink">
        <where>
                t.link_id >0
            <if test="advMediaId != null ">
                and t1.adv_media_id = #{advMediaId}
            </if>
            <if test="retailTypes != null and retailTypes.length() > 0">
                and t2.retail_type in (${retailTypes})
            </if>
            <if test="retailIds != null and retailIds.length() > 0">
                and t1.retail_id in (${retailIds})
            </if>
            <if test="advUserIds != null and advUserIds.length() > 0">
                and t1.adv_user_id in (${advUserIds})
            </if>
            <if test="linkIds != null and linkIds.length() > 0">
                and t.link_id in (${linkIds})
            </if>
            <if test="miniIds != null and miniIds.length() > 0">
                and t1.mini_id in (${miniIds})
            </if>
            <if test="dramaIds != null and dramaIds.length() > 0">
                and t1.drama_id in (${dramaIds})
            </if>
            <if test="statisDateS != null and statisDateE != null">
                and t.statis_date &lt;= #{statisDateE} and t.statis_date &gt;= #{statisDateS}
            </if>
            <if test="noLaunchZero != null and noLaunchZero == 1">
                and t3.cost_day &gt; 0
            </if>
            <if test="noAddMemberZero != null and noAddMemberZero == 1">
                and t3.num_day &gt; 0
            </if>
            <if test="contentType != null">
                and t1.content_type = #{contentType}
            </if>
        </where>
    </sql>

    <sql id="wholeQuerySql">
        t.statis_date AS statisDate,
		sum(if(t3.num_day>0,1,0)) AS validLinkCount,
		IFNULL( sum( t.color_member_day ), 0.00 ) AS memberCountAddColor,
		IFNULL( sum( t.convert_member_day ), 0.00 ) AS convertMemberCount,
		IFNULL( sum( t.add_member_watch_amount ), 0.00 ) AS convertMemeberCountAd,
		IFNULL( sum( t.ad_income ), 0.00 ) AS incomeAd,
		IFNULL( sum( t.add_member_recharge ), 0.00 ) AS addMemberRecharge,
		IFNULL( sum( t.add_member_inocme ), 0.00 ) AS addMemberIncome,
		GROUP_CONCAT(t.ad_watch_member_d60 separator "#") as addConvertMemberD60,
        GROUP_CONCAT(t.ad_income_d60 separator "#") as addConvertMoneyDayD60,
        IFNULL( sum( t3.num_day ), 0.00 ) AS memberCountAdd,
        IFNULL( sum( t3.num_day_rech ), 0.00 ) AS convertMemberCountRecharge,
		IFNULL( sum( t3.money_day ), 0.00 ) AS incomeRecharge,
	    IFNULL(sum(t3.cost_day),0.00) as adMoneyConsume
    </sql>

    <sql id="querygroupBylink">
                t.link_id as linkId,
                (select num_day from fast_statis_link where link_id = t.link_id and statis_date = DATE_FORMAT(now(),"%Y-%m-%d")) as memberCountAddToday,
                (select IFNULL(sum(num_day),0) from fast_statis_link where link_id = t.link_id and statis_date &lt;= #{statisDateE} and statis_date &gt;= #{statisDateS}) as memberCountAddAll,
                (select color_member_day from fast_statis_link_free where link_id = t.link_id and statis_date = DATE_FORMAT(now(),"%Y-%m-%d")) as memberCountAddColorToday,
                sum(t.color_member_day) as memberCountAddColor,
                (select convert_member_day from fast_statis_link_free where link_id = t.link_id and statis_date = DATE_FORMAT(now(),"%Y-%m-%d")) as convertMemberCountToday,
                sum(t.convert_member_day) as convertMemberCount,
                sum(t.add_member_inocme) as incomeAd,
                GROUP_CONCAT(ad_watch_member_d60 separator "#") as addConvertMemberD60,
                GROUP_CONCAT(ad_income_d60 separator "#") as addConvertMoneyDayD60,
                t1.link_name,
                ( SELECT user_name FROM fast_user WHERE id = t1.adv_user_id ) as advUserName,
                (select retail_name from fast_retail where id = t1.retail_id) as retailName,
                t1.create_time as linkCreatTime,
                IFNULL(sum(t3.cost_day),0.00) as adMoneyConsume,
                (select IFNULL(cost_day,0.00) from fast_statis_link where link_id = t.link_id and statis_date = DATE_FORMAT(now(),"%Y-%m-%d") ) as adMoneyConsumeToday,
                IFNULL(sum(t3.money_day),0.00) as incomeRecharge,
                min(t.statis_date) as statisDate
    </sql>

</mapper>
