/*
 * Powered By fast.up
 */
package com.fast.mapper.analysis;

import com.fast.annotation.Slave;
import com.fast.po.analysis.DramaAnalysisPO;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 短剧整体分析
 *
 * <AUTHOR>
 */
@Mapper
public interface DramaDetailAnalysisMapper {

    /**
     * 用户注册人数分析
     */
    int queryRegisterMemberCountList(DramaAnalysisPO po);

    /**
     * 用户注册人数分析(从报表查)
     */
    @Slave
    int queryRegisterMemberCountListV1(DramaAnalysisPO po);

    /**
     * 观看人数分析
     */
    int queryWatchMemberCountList(DramaAnalysisPO po);

    int queryWatchMemberCountListSd(DramaAnalysisPO po);

    /**
     * K币消费值分析
     */
    @Slave
    Long queryCoinConsumeList(DramaAnalysisPO po);

    /**
     * 短剧完播人数分析
     */
    int queryWatchFinishMemberCountList(DramaAnalysisPO po);

    /**
     * 短剧完播人数分析
     */
    int queryWatchFinishMemberCountV2(DramaAnalysisPO po);

    /**
     * 指定时间段看过某一集的用户id
     */
    Set<Long> queryWatchMemberList(DramaAnalysisPO po);

    /**
     * 用户充值数据分析
     */
    DramaAnalysisPO queryRechargeDataList(DramaAnalysisPO po);


    DramaAnalysisPO queryRechargeDataListGroupByRetailType(DramaAnalysisPO po);

    /**
     * 用户充值金额分析
     */
    BigDecimal queryRechargeMoneyList(DramaAnalysisPO po);

    /**
     * 用户充值人数分析
     */
    int queryRechargeMemberCountList(DramaAnalysisPO po);

    /**
     * 用户充值人数分析
     */
    List<DramaAnalysisPO> queryRechargeMemberGroupList(DramaAnalysisPO po);

    /**
     * 剧集分析-留存分析
     */
    List<DramaAnalysisPO> queryDramaRemainGroupList(DramaAnalysisPO po);

    /**
     * 剧集分析-跳出分析
     */
    List<DramaAnalysisPO> queryDramaSkipGroupList(DramaAnalysisPO po);

    /**
     * 剧集分析-剧集数据明细
     */
    List<DramaAnalysisPO> queryDramaDetailSeriesGroupList(DramaAnalysisPO po);

    /**
     * 每集消费K币分析
     */
    List<DramaAnalysisPO> querySeriesCoinConsumeList(DramaAnalysisPO po);
}
