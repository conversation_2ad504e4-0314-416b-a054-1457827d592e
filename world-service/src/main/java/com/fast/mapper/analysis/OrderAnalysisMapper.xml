<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.analysis.OrderAnalysisMapper">

    <!--充值排名分析  -->
    <select id="queryRechargeRankList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            <if test="sortType == 1">count(*) rechargeOrderCount</if>
            <if test="sortType == 2">count(distinct t.member_id) rechargeMemberCount</if>
            <if test="sortType == 3">ifnull(sum(t.money_recharge),0) rechargeMoneySum</if>
            <if test="sortType == 4">ifnull(sum(t.money_profit),0) rechargeMoneySum</if>
            <if test="groupByColum == 1">,t.retail_id id</if>
            <if test="groupByColum == 2">,t.adv_user_id id</if>
            <if test="groupByColum == 3">,t.link_id id</if>
            <if test="groupByColum == 4">,t.drama_id id</if>
            <if test="groupByColum == 5">,DATE_FORMAT(t.`pay_time`,'%Y-%m') name</if>
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">
                and link.`adv_media_id` = #{advMediaId}
            </if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
        <if test="groupByColum == 1">group by t.retail_id having id > 0</if>
        <if test="groupByColum == 2">group by t.adv_user_id having id > 0</if>
        <if test="groupByColum == 3">group by t.link_id having id > 0</if>
        <if test="groupByColum == 4">group by t.drama_id having id > 0</if>
        <if test="groupByColum == 5">group by DATE_FORMAT(t.`pay_time`,'%Y-%m')</if>
    </select>
    
    <select id="queryRechargeRankAllList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            	count(*) rechargeOrderCount,
            	ifnull(sum(t.money_recharge),0) rechargeMoneySum,
            	ifnull(sum(t.money_profit),0) rechargeMoneyProfitSum,
            	count(distinct t.member_id) rechargeMemberCount
            <if test="groupByColum == 1">,t.retail_id id</if>
            <if test="groupByColum == 2">,t.adv_user_id id</if>
            <if test="groupByColum == 3">,t.link_id id</if>
            <if test="groupByColum == 4">,t.drama_id id</if>
            <if test="groupByColum == 5">,DATE_FORMAT(t.`pay_time`,'%Y-%m') name</if>
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">
                and link.`adv_media_id` = #{advMediaId}
            </if>
        </where>
        <if test="groupByColum == 1">group by t.retail_id having id > 0</if>
        <if test="groupByColum == 2">group by t.adv_user_id having id > 0</if>
        <if test="groupByColum == 3">group by t.link_id having id > 0</if>
        <if test="groupByColum == 4">group by t.drama_id having id > 0</if>
        <if test="groupByColum == 5">group by DATE_FORMAT(t.`pay_time`,'%Y-%m')</if>
    </select>

    <!--充值人数，通过品牌进行统计  -->
    <select id="queryRechargePhoneBrandCount" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            m.phone_brand,
            count(m.phone_brand) data
        from fast_member_order_recharge t
        left join fast_member m on m.id = t.member_id
        where m.phone_brand is not null
        <include refid="whereSQLRecharge"/>
        group by m.phone_brand
    </select>

    <!--充值人数  -->
    <select id="queryRechargeTimeCount" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        SELECT
        <if test="period == 1">
            DATE(t.pay_time) as `dataDay`,
        </if>
        <if test="period == 2">
            DATE_FORMAT(t.pay_time, '%Y-%m-%d %H:00:00') as `dataDay`,
        </if>
        COUNT(t.member_id) as `data`
        FROM fast_member_order_recharge t
        <where>
            <include refid="whereSQLRecharge"/>
        </where>
        <if test="period == 1">
            GROUP BY DATE(t.pay_time)
        </if>
        <if test="period == 2">
            GROUP BY DATE_FORMAT(t.pay_time, '%Y-%m-%d %H:00:00')
        </if>
        ORDER BY `dataDay`
    </select>

    <!--充值人数  -->
    <select id="queryRechargeMemberCount" parameterType="OrderAnalysisPO" resultType="int">
        select
            count(distinct t.member_id)
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            <if test="state != null">and t.`state` = #{state}</if>
            <if test="dataType!=null and dataType==5">and t.`drama_id` >0</if>
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
    </select>

    <!--充值人数  -->
    <select id="queryRechargeMemberCountDayList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            count(distinct t.member_id) data,DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') dataDay
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            <if test="state != null">and t.`state` = #{state}</if>
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
        group by t.pay_date
    </select>

    <!--支付订单数  -->
    <select id="queryRechargeOrderCount" parameterType="OrderAnalysisPO" resultType="int">
        select
            count(*)
        from fast_member_order_recharge t
        <if test="advMediaId != null or advVersion != null">
            left join fast_link link on link.id=t.link_id
        </if>
        <if test="dramaPerformType != null">
            left join fast_drama d on d.id=t.drama_id
        </if>
        <where>
            <if test="orderTypes != null and orderTypes.length()>0">and t.`order_type`in (${orderTypes})</if>
            <if test="state != null">and t.`state` = #{state}</if>
            <if test="launchFlag != null and launchFlag == 1">and t.`link_id` >0</if>
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="advVersion != null">and link.`adv_version` = #{advVersion}</if>
            <if test="dramaPerformType != null">and d.drama_perform_type = #{dramaPerformType}</if>
        </where>
    </select>

    <!--支付订单数  -->
    <select id="queryRechargeOrderCountDayList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            count(*) data,DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') dataDay
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            <if test="state != null">and t.`state` = #{state}</if>
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
        group by t.pay_date
    </select>

    <!--支付金额数  -->
    <select id="queryRechargeMoneySum" parameterType="OrderAnalysisPO" resultType="java.math.BigDecimal">
        select
            ifnull(sum(t.money_recharge),0)
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
            left join fast_drama d on d.id=t.drama_id
        </if>
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="officialIds != null and officialIds != ''">
                and t.`official_id` in (${officialIds})
            </if>
            <if test="dramaPerformType != null">and d.drama_perform_type = #{dramaPerformType}</if>
        </where>
    </select>

    <!--支付金额数  -->
    <select id="queryRechargeMoneyProfitSum" parameterType="OrderAnalysisPO" resultType="java.math.BigDecimal">
        select
            ifnull(sum(t.money_profit),0)
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
            left join fast_drama d on d.id=t.drama_id
        </if>
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="officialIds != null and officialIds != ''">
                and t.`official_id` in (${officialIds})
            </if>
            
            <if test="dramaPerformType != null">and d.drama_perform_type = #{dramaPerformType}</if>
        </where>
    </select>

    <!--新增用户充值金额  -->
    <select id="queryRechargeMoneyAddSum" parameterType="OrderAnalysisPO" resultType="java.math.BigDecimal">
        select
            ifnull(sum(t.money_recharge),0)
        from fast_member_order_recharge t
        <where>
            t.`state` = 1
            and DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') = DATE_FORMAT(t.`link_time`,'%Y-%m-%d')
            <include refid="whereSQLRecharge"/>
            <if test="officialIds != null and officialIds != ''">
                and t.`official_id` in (${officialIds})
            </if>
        </where>
    </select>

    <!--新增用户充值金额  -->
    <select id="queryRechargeMoneyProfitAddSum" parameterType="OrderAnalysisPO" resultType="java.math.BigDecimal">
        select
            ifnull(sum(t.money_profit),0)
        from fast_member_order_recharge t
        <where>
            t.`state` = 1
            and DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') = DATE_FORMAT(t.`link_time`,'%Y-%m-%d')
            <include refid="whereSQLRecharge"/>
            <if test="officialIds != null and officialIds != ''">
                and t.`official_id` in (${officialIds})
            </if>
        </where>
    </select>

    <!--注册用户充值金额  -->
    <select id="queryRechargeMoneyNewSum" parameterType="OrderAnalysisPO" resultType="java.math.BigDecimal">
        select
            ifnull(sum(t.money_recharge),0)
        from fast_member_order_recharge t
        <where>
            t.`state` = 1
            and DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') = DATE_FORMAT(t.`register_time`,'%Y-%m-%d')
            <include refid="whereSQLRecharge"/>
            <if test="officialIds != null and officialIds != ''">
                and t.`official_id` in (${officialIds})
            </if>
        </where>
    </select>

    <!--注册用户充值金额  -->
    <select id="queryRechargeMoneyProfitNewSum" parameterType="OrderAnalysisPO" resultType="java.math.BigDecimal">
        select
            ifnull(sum(t.money_profit),0)
        from fast_member_order_recharge t
        <where>
            t.`state` = 1
            and DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') = DATE_FORMAT(t.`register_time`,'%Y-%m-%d')
            <include refid="whereSQLRecharge"/>
            <if test="officialIds != null and officialIds != ''">
                and t.`official_id` in (${officialIds})
            </if>
        </where>
    </select>

    <!--首充用户数  -->
    <select id="queryRechargeFirstMemberCount" parameterType="OrderAnalysisPO" resultType="int">
        select
        count(distinct t.member_id) rechargeMemberCount
        from fast_member_order_recharge t
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="officialIds != null and officialIds != ''">
                and t.`official_id` in (${officialIds})
            </if>
        </where>
    </select>

    <!--支付金额数  -->
    <select id="queryRechargeMoneySumDayList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            ifnull(sum(t.money_recharge),0) data, DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') dataDay
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
        group by t.pay_date
    </select>

    <!--到账金额数  -->
    <select id="queryRechargeMoneyProfitSumDayList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            ifnull(sum(t.money_profit),0) data, DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') dataDay
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
        group by t.pay_date
    </select>

    <!--订单统计  -->
    <select id="queryStatisticsOrderSumGroupList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select t0.dataDay
            ,ifnull(sum(if(t0.orderType=1,t0.money_recharge,0)),0) rechargeCoinMoneySum
            ,ifnull(sum(if(t0.orderType=1,t0.money_profit,0)),0) rechargeCoinMoneyProfitSum
            ,ifnull(sum(if(t0.orderType=1,1,0)),0) rechargeCoinMemberCount
            ,ifnull(sum(if(t0.orderType=2,t0.money_recharge,0)),0) rechargeVipMoneySum
            ,ifnull(sum(if(t0.orderType=2,t0.money_profit,0)),0) rechargeVipMoneyProfitSum
            ,ifnull(sum(if(t0.orderType=2,1,0)),0) rechargeVipMemberCount
            ,ifnull(sum(if(t0.orderType=4,t0.money_recharge,0)),0) rechargeDramaCardMoneySum
            ,ifnull(sum(if(t0.orderType=4,t0.money_profit,0)),0) rechargeDramaCardMoneyProfitSum
            ,ifnull(sum(if(t0.orderType=4,1,0)),0) rechargeDramaCardMemberCount
            ,count(distinct t0.memberId) rechargeMemberCount
            ,count(*) rechargeOrderCount
            ,ifnull(sum(if(t0.payDate=t0.linkTime,t0.money_recharge,0)),0) rechargeMoneyAddSum
            ,ifnull(sum(if(t0.payDate=t0.linkTime,t0.money_profit,0)),0) rechargeMoneyProfitAddSum
            ,ifnull(sum(if(t0.payDate=t0.linkTime,1,0)),0) rechargeMemberCountAdd
            ,ifnull(sum(if(t0.payDate=t0.registerTime,t0.money_recharge,0)),0) rechargeMoneyNewSum
            ,ifnull(sum(if(t0.payDate=t0.registerTime,t0.money_profit,0)),0) rechargeMoneyProfitNewSum
            ,ifnull(sum(if(t0.payDate=t0.registerTime,1,0)),0) rechargeMemberCountNew
            ,ifnull(sum(if(t0.payDate=t0.linkTime and t0.payDate!=t0.registerTime,t0.money_recharge,0)),0) rechargeMoneyColorSum
            ,ifnull(sum(if(t0.payDate=t0.linkTime and t0.payDate!=t0.registerTime,t0.money_profit,0)),0) rechargeMoneyProfitColorSum
            ,ifnull(sum(if(t0.payDate=t0.linkTime and t0.payDate!=t0.registerTime,1,0)),0) rechargeMemberCountColor
            ,ifnull(sum(if(t0.backState=2,1,0)),0) backSuccessCount
        from (
            select
                t.money_recharge money_recharge
                ,t.money_profit money_profit
                ,t.order_type orderType
                ,t.back_state backState
                ,t.member_id memberId
                <if test="dataType==1">,DATE_FORMAT(t.`pay_time`,'%Y-%m') dataDay</if>
                <if test="dataType==2">,DATE_FORMAT(t.`pay_time`,'%Y-%m-%d') dataDay</if>
                <if test="dataType==3">,t.`retail_id` dataDay</if>
                <if test="dataType==4">,t.`adv_user_id` dataDay</if>
                <if test="dataType==5">,t.`drama_id` dataDay</if>
                <if test="dataType==1">,DATE_FORMAT(t.`pay_date`,'%Y-%m') payDate</if>
                <if test="dataType >1">,DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') payDate</if>
                <if test="dataType==1">,DATE_FORMAT(t.`link_time`,'%Y-%m') linkTime</if>
                <if test="dataType >1">,DATE_FORMAT(t.`link_time`,'%Y-%m-%d') linkTime</if>
                <if test="dataType==1">,DATE_FORMAT(t.`register_time`,'%Y-%m') registerTime</if>
                <if test="dataType >1">,DATE_FORMAT(t.`register_time`,'%Y-%m-%d') registerTime</if>
            from fast_member_order_recharge t
            <if test="dramaPerformType != null">
            	left join fast_drama d on d.id = t.drama_id
            </if>
            <where>
                t.`state` = 1
                <include refid="whereSQLRecharge"/>
                <if test="dataType==3">and t.`retail_id` >0</if>
                <if test="dataType==4">and t.`adv_user_id` >0</if>
                <if test="dataType==5">and t.`drama_id` >0</if>
	            <if test="dramaPerformType != null">
	            	and d.drama_perform_type = #{dramaPerformType}
	            </if>
            </where>
        ) t0
        group by t0.dataDay
    </select>

    <!--充值档位  -->
    <select id="queryRechargeMoneyGroupList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            ifnull(t.money_recharge,0) rechargeMoney, count(*) rechargeOrderCount
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
        group by t.money_recharge
    </select>

    <!--充值次数每用户  -->
    <select id="queryRechargeCountMemberList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            count(*) rechargeCount, group_concat(t.money_profit order by t.id) rechargeMoneyStr, t.member_id
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
        group by t.member_id
    </select>

    <!--充值金额每用户  -->
    <select id="queryRechargeCountMoneyList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            ifnull(sum(t.money_profit),0) rechargeMoney, t.member_id
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="moneySearchType != null">
                <if test="moneySearchType == 1">
                    and t.money_profit &lt; 20
                </if>
                <if test="moneySearchType == 2">
                    and t.money_profit &gt;= 20
                </if>
            </if>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
        group by t.member_id
    </select>

    <!--回传分析-失败占比  -->
    <select id="queryBackFailReasonList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            count(*) rechargeCount, t.back_type
        from fast_member_order_recharge t
        <if test="advMediaId != null or advVersion != null">
            left join fast_link link on link.id=t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            t.`state` = 1 and t.back_type > 1
            <include refid="whereSQLRecharge"/>
            <if test="launchFlag != null and launchFlag == 1">and t.`link_id` >0</if>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="advVersion != null">and link.`adv_version` = #{advVersion}</if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
        group by t.back_type
    </select>

    <!--查询当天的支付人数  -->
    <select id="queryMemberPayCountDayList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
            DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') dataDay, count(distinct t.member_id) rechargeMemberCount
        from fast_member_order_recharge t
        <if test="advMediaId != null">
            left join fast_link link on link.id = t.link_id
        </if>
        <if test="dramaPerformType != null">
        	left join fast_drama d on d.id = t.drama_id
        </if>
        <where>
            t.`state` = 1
            <include refid="whereSQLRecharge"/>
            <if test="rechargeRateHour == 1"><!--当日-->
                and DATE_FORMAT(t.`pay_date`,'%Y-%m-%d') = DATE_FORMAT(t.`register_time`,'%Y-%m-%d')
            </if>
            <if test="rechargeRateHour == 24"><!--24小时-->
                and t.`pay_time` &lt;= ADDTIME(t.`register_time`,'24:0:0')
            </if>
            <if test="rechargeRateHour == 48"><!--48小时-->
                and t.`pay_time` &lt;= ADDTIME(t.`register_time`,'48:0:0')
            </if>
            <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
            <if test="dramaPerformType != null">
            	and d.drama_perform_type = #{dramaPerformType}
            </if>
        </where>
        group by t.pay_date
    </select>

    <!--查询每天的注册人数  -->
    <select id="queryMemberCountDayList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select count(*) registerCount, t0.dataDay from (
            select
                t.id, DATE_FORMAT(t.`create_time`,'%Y-%m-%d') dataDay
            from fast_member t
            left join fast_member_link ml on ml.member_id=t.id
            left join fast_link link on link.id=ml.first_link_id
            
	        <if test="dramaPerformType != null">
	        	left join fast_drama d on d.id = link.drama_id
	        </if>
            <where>
                <if test="contentType != null">AND t.content_type=#{contentType}</if>
                <if test="retailId != null">
                    and t.`retail_id` = #{retailId}
                </if>
                <if test="retailIds != null and retailIds.length()>0">
                    and t.`retail_id` in (${retailIds})
                </if>
                <if test="linkId != null">
                    and ml.`first_link_id` = #{linkId}
                </if>
                <if test="linkIds != null and linkIds.length()>0">
                    and ml.`first_link_id` in (${linkIds})
                </if>
                <if test="miniId != null">
                    and t.`mini_id` = #{miniId}
                </if>
                <if test="miniIds != null and miniIds.length()>0">
                    and t.`mini_id` in (${miniIds})
                </if>
                <if test="dramaId != null ">
                    and link.`drama_id` = #{dramaId}
                </if>
                <if test="dramaIds != null and dramaIds.length()>0">
                    and link.`drama_id` in (${dramaIds})
                </if>
                <if test="advUserIds != null and advUserIds.length()>0">
                    and link.`adv_user_id` in (${advUserIds})
                </if>
                <if test="advMediaId != null">and link.`adv_media_id` = #{advMediaId}</if>
                <if test="linkTimeS != null">
                    and ml.`first_link_time` &gt;= #{linkTimeS}
                </if>
                <if test="linkTimeE != null">
                    and ml.`first_link_time` &lt;= #{linkTimeE}
                </if>
                <if test="registerTimeS != null">
                    and t.`create_time` &gt;= #{registerTimeS}
                </if>
                <if test="registerTimeE != null">
                    and t.`create_time` &lt;= #{registerTimeE}
                </if>
                <if test="dramaPerformType != null">
	            	and d.drama_perform_type = #{dramaPerformType}
	            </if>
            </where>
        ) t0
        group by dataDay
    </select>

    <!--查询每天的首次观看人数  -->
    <select id="queryFirstWatchMemberCountDayList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select count(*) watchCount, t0.dataDay from (
            select
                t.id, DATE_FORMAT(t.`create_time`,'%Y-%m-%d') dataDay
            from fast_member_drama_first_watch t
            left join fast_member_link ml on ml.member_id=t.member_id
            left join fast_link link on link.id=ml.first_link_id
            <where>
                <include refid="whereSQLFirstWatch"/>
            </where>
        ) t0
        group by dataDay
    </select>

    <!--查询当天的首次观看支付人数  -->
    <select id="queryFirstWatchMemberPayCountDayList" parameterType="OrderAnalysisPO" resultType="OrderAnalysisPO">
        select
        DATE_FORMAT(t.`create_date`,'%Y-%m-%d') dataDay, count(distinct t.member_id) rechargeMemberCount
        from fast_member_drama_first_watch t
        left join fast_member_link ml on ml.member_id=t.member_id
        left join fast_link link on link.id=ml.first_link_id
        <where>
            <include refid="whereSQLFirstWatch"/>
            <if test="rechargeRateHour == 1"><!--当日-->
                and today_pay=1
            </if>
            <if test="rechargeRateHour == 24"><!--24小时-->
                and hour24_pay=1
            </if>
            <if test="rechargeRateHour == 48"><!--48小时-->
                and hour48_pay=1
            </if>
        </where>
        group by t.create_date
    </select>

    <sql id="whereSQLFirstWatch">
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="retailIds != null and retailIds.length()>0">
            and t.`retail_id` in (${retailIds})
        </if>
        <if test="linkId != null">
            and ml.`first_link_id` = #{linkId}
        </if>
        <if test="linkIds != null and linkIds.length()>0">
            and ml.`first_link_id` in (${linkIds})
        </if>
        <if test="miniId != null">
            and t.`mini_id` = #{miniId}
        </if>
        <if test="miniIds != null and miniIds.length()>0">
            and t.`mini_id` in (${miniIds})
        </if>
        <if test="dramaId != null ">
            and link.`drama_id` = #{dramaId}
        </if>
        <if test="dramaIds != null and dramaIds.length()>0">
            and link.`drama_id` in (${dramaIds})
        </if>
        <if test="advUserIds != null and advUserIds.length()>0">
            and link.`adv_user_id` in (${advUserIds})
        </if>
        <if test="linkTimeS != null">
            and ml.`first_link_time` &gt;= #{linkTimeS}
        </if>
        <if test="linkTimeE != null">
            and ml.`first_link_time` &lt;= #{linkTimeE}
        </if>
        <if test="watchTimeS != null">
            and t.`create_time` &gt;= #{watchTimeS}
        </if>
        <if test="watchTimeE != null">
            and t.`create_time` &lt;= #{watchTimeE}
        </if>
    </sql>

    <!-- 公共条件  -->
    <sql id="whereSQLRecharge">
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="retailIds != null and retailIds.length()>0">
            and t.`retail_id` in (${retailIds})
        </if>
        <if test="linkId != null">
            and t.`link_id` = #{linkId}
        </if>
        <if test="linkIds != null and linkIds.length()>0">
            and t.`link_id` in (${linkIds})
        </if>
        <if test="miniId != null">
            and t.`mini_id` = #{miniId}
        </if>
        <if test="miniIds != null and miniIds.length()>0">
            and t.`mini_id` in (${miniIds})
        </if>
        <if test="dramaId != null ">
            and t.`drama_id` = #{dramaId}
        </if>
        <if test="dramaIds != null and dramaIds.length()>0">
            and t.`drama_id` in (${dramaIds})
        </if>
        <if test="advUserIds != null and advUserIds.length()>0">
            and t.`adv_user_id` in (${advUserIds})
        </if>
        <if test="payTimeS != null">
            and t.`pay_time` &gt;= #{payTimeS}
        </if>
        <if test="payTimeE != null">
            and t.`pay_time` &lt;= #{payTimeE}
        </if>
        <if test="linkTimeS != null">
            and t.`link_time` &gt;= #{linkTimeS}
        </if>
        <if test="linkTimeE != null">
            and t.`link_time` &lt;= #{linkTimeE}
        </if>
        <if test="registerTimeS != null">
            and t.`register_time` &gt;= #{registerTimeS}
        </if>
        <if test="registerTimeE != null">
            and t.`register_time` &lt;= #{registerTimeE}
        </if>
        and t.coin_change_id=0
        <if test="contentType != null">AND t.content_type=#{contentType}</if>
        <if test="phoneOs != null">AND t.phone_os=#{phoneOs}</if>
        <if test="orderType != null">AND t.order_type=#{orderType}</if>
        <if test="backState != null">AND t.back_state=#{backState}</if>
        <if test="backAuto != null">AND t.back_auto=#{backAuto}</if>
        <if test="payForm != null">AND t.pay_form=#{payForm}</if>
    </sql>

</mapper>
