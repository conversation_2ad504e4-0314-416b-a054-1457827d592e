/*
 * Powered By fast.up
 */
package com.fast.mapper.analysis;

import com.fast.po.analysis.OrderAnalysisPO;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单分析
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderAnalysisMapper {

    // 充值排名分析
    List<OrderAnalysisPO> queryRechargeRankList(OrderAnalysisPO po);

    // 充值排名（返回全部列，供下载）
    List<OrderAnalysisPO> queryRechargeRankAllList(OrderAnalysisPO po);

    // 充值人数
    int queryRechargeMemberCount(OrderAnalysisPO po);

    List<OrderAnalysisPO> queryRechargeMemberCountDayList(OrderAnalysisPO po);

    // 支付订单数
    int queryRechargeOrderCount(OrderAnalysisPO po);

    List<OrderAnalysisPO> queryRechargeOrderCountDayList(OrderAnalysisPO po);

    // 支付金额数
    BigDecimal queryRechargeMoneySum(OrderAnalysisPO po);

    BigDecimal queryRechargeMoneyProfitSum(OrderAnalysisPO po);

    List<OrderAnalysisPO> queryRechargeMoneySumDayList(OrderAnalysisPO po);

    List<OrderAnalysisPO> queryRechargeMoneyProfitSumDayList(OrderAnalysisPO po);

    // 订单统计
    List<OrderAnalysisPO> queryStatisticsOrderSumGroupList(OrderAnalysisPO po);

    // 新增用户充值金额
    BigDecimal queryRechargeMoneyAddSum(OrderAnalysisPO po);

    BigDecimal queryRechargeMoneyProfitAddSum(OrderAnalysisPO po);

    // 注册用户充值金额
    BigDecimal queryRechargeMoneyNewSum(OrderAnalysisPO po);

    BigDecimal queryRechargeMoneyProfitNewSum(OrderAnalysisPO po);

    // 首充用户数
    int queryRechargeFirstMemberCount(OrderAnalysisPO po);

    // 充值档位
    List<OrderAnalysisPO> queryRechargeMoneyGroupList(OrderAnalysisPO po);

    // 充值次数每用户
    List<OrderAnalysisPO> queryRechargeCountMemberList(OrderAnalysisPO po);

    // 充值金额每用户
    List<OrderAnalysisPO> queryRechargeCountMoneyList(OrderAnalysisPO po);

    // 回传分析-失败占比
    List<OrderAnalysisPO> queryBackFailReasonList(OrderAnalysisPO po);

    // 查询每天的注册人数
    List<OrderAnalysisPO> queryMemberCountDayList(OrderAnalysisPO po);

    // 查询当天的支付人数
    List<OrderAnalysisPO> queryMemberPayCountDayList(OrderAnalysisPO po);

    // 查询每天的首次观看人数
    List<OrderAnalysisPO> queryFirstWatchMemberCountDayList(OrderAnalysisPO po);

    // 查询当天的首次观看支付人数
    List<OrderAnalysisPO> queryFirstWatchMemberPayCountDayList(OrderAnalysisPO po);

    List<OrderAnalysisPO> queryRechargePhoneBrandCount(OrderAnalysisPO po);

    List<OrderAnalysisPO> queryRechargeTimeCount(OrderAnalysisPO po);
}
