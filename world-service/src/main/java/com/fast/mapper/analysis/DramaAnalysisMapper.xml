<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.analysis.DramaAnalysisMapper">

    <!--用户充值人数分析  -->
    <select id="queryRechargeMemberCountList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,drama.drama_name dramaName
        ,drama.release_date releaseDate
        from (
        select t.drama_id dramaId
        ,count(distinct t.member_id) rechargeMemberCount
        from fast_member_order_recharge t
        left join fast_retail fr on fr.id = t.retail_id
        <where>
            <if test="incomeUser != null">
                and fr.retail_type in (${incomeUser})
            </if>
            <if test="payTimeS != null">
                and t.`pay_time` &gt;= #{payTimeS}
            </if>
            <if test="payTimeE != null">
                and t.`pay_time` &lt;= #{payTimeE}
            </if>
            and t.`state` = 1 and t.coin_change_id=0
            and t.`drama_id` > 0
            <if test="contentType != null">AND t.content_type=#{contentType}</if>
        </where>
        group by t.`drama_id`
        ) t1 left join fast_drama drama on drama.id = t1.dramaId and drama.del_flag=0
    </select>

    <!--用户广告收益人数分析  -->
    <select id="queryAdIncomeMemberCountListOrderBy" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        from(
            select t.drama_id dramaId
            ,count(distinct t.member_id) adIncomeMemberCount
            from fast_ecpm_data_tt t
            left join fast_drama fd on fd.id = t.drama_id
            <where>
                <include refid="whereSQL"/>
                <if test="adCostTimeS != null and adCostTimeE != null">
                    and t.`create_time` between #{adCostTimeS} and  #{adCostTimeE}
                </if>
                <if test="contentType != null">
                    <if test="contentType == 1">
                        and fd.drama_perform_type = 1
                    </if>
                    <if test="contentType == 4">
                        and fd.drama_perform_type = 2
                    </if>
                </if>
                and t.`drama_id` > 0
            </where>
            group by t.`drama_id`
        ) t1
        order by adIncomeMemberCount <if test="sortOrder==1">desc</if>, dramaId desc
    </select>

    <!--用户充值人数分析  -->
    <select id="queryRechargeMemberCountListOrderBy" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        from(
        <include refid="rechargeMemberCountSQL"/>
        ) t1
        order by rechargeMemberCount <if test="sortOrder==1">desc</if>, dramaId desc
    </select>

    <!--用户充值人数分析  -->
    <sql id="rechargeMemberCountSQL">
        select t.drama_id dramaId
        ,count(distinct t.member_id) rechargeMemberCount
        from fast_member_order_recharge t
        <where>
            <include refid="whereSQL"/>
            <if test="payTimeS != null">
                and t.`pay_time` &gt;= #{payTimeS}
            </if>
            <if test="payTimeE != null">
                and t.`pay_time` &lt;= #{payTimeE}
            </if>
            <if test="addState != null">
                and t.`add_state` = #{addState}
            </if>
            and t.`state` = 1 and t.coin_change_id=0
            and t.`drama_id` > 0
            <if test="contentType != null">AND t.content_type=#{contentType}</if>
        </where>
        group by t.`drama_id`
    </sql>

    <!--用户充值人数分析  -->
    <select id="queryRechargeMemberCountDayList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,drama.drama_name dramaName
        ,drama.release_date releaseDate
        from (
        <include refid="rechargeMemberCountDaySQL"/>
        ) t1 left join fast_drama drama on drama.id = t1.dramaId and drama.del_flag=0
    </select>

    <!--用户充值人数分析  -->
    <sql id="rechargeMemberCountDaySQL">
        select t.drama_id dramaId
        ,count(distinct t.member_id) rechargeMemberCount
        ,DATE_FORMAT(t.pay_time,'%Y-%m-%d') dataDay
        from fast_member_order_recharge t
        <where>
            <include refid="whereSQL"/>
            <if test="payTimeS != null">
                and t.`pay_time` &gt;= #{payTimeS}
            </if>
            <if test="payTimeE != null">
                and t.`pay_time` &lt;= #{payTimeE}
            </if>
            <if test="addState != null">
                and t.`add_state` = #{addState}
            </if>
            <if test="contentType != null">AND t.content_type=#{contentType}</if>
            and t.`state` = 1 and t.coin_change_id=0
            and t.`drama_id` > 0
        </where>
        group by dataDay
    </sql>

    <!-- 短剧解锁人数 -->
    <select id="queryUnlockMemberCountDayList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        SELECT
        drama_id AS dramaId,
        sum( t1.unlock_count ) unlockCount,
        DATE_FORMAT( t1.report_date, '%Y-%m-%d' ) dataDay,
        t2.drama_name dramaName,
        t2.release_date releaseDate
        FROM
        fast_report_member_unlock_day t1
        LEFT JOIN fast_drama t2 ON t1.drama_id = t2.id AND t2.del_flag = 0
        <where>
            <if test="dramaId != null">
                t1.drama_id = #{dramaId}
            </if>
            <if test="createTimeS != null and createTimeE != null">
                <![CDATA[
                  AND t1.report_date >= #{createTimeS}
                  AND t1.report_date <= #{createTimeE}
                  ]]>
            </if>
            <if test="contentType != null">
                AND t2.content_type = 1
            </if>
        </where>
        GROUP BY
        dataDay
    </select>

    <!-- 短剧解锁金额 -->
    <select id="queryUnlockMoneyCountDayList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        SELECT
            t.drama_id as dramaId,
            DATE_FORMAT( t.create_time, '%Y-%m-%d' ) dataDay,
            sum( coin_consume ) unlockCoin,
            t1.drama_name dramaName,
            t1.release_date releaseDate
        FROM
            cp_report_consume t
        LEFT JOIN fast_drama t1 ON t.drama_id = t1.id
        AND t1.del_flag = 0
        <where>
            <if test="payTimeS != null and payTimeE != null">
                 t.report_date &lt;= #{payTimeE} AND t.report_date &gt;= #{payTimeS}
            </if>
            <if test="contentType != null">
                AND t1.content_type = #{contentType}
            </if>
            <if test="dramaId != null">
                AND t.drama_id = #{dramaId}
            </if>
        </where>
        GROUP BY dataDay
    </select>

    <!--用户充值金额分析  -->
    <select id="queryRechargeMoneyList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t.drama_id dramaId
        <!--,sum(t.money_recharge) rechargeMoneySum-->
        <!--,sum(t.money_profit) rechargeMoneyProfitSum-->
        ,sum(if(l.fee_flag=1,t.money_recharge,0)) rechargeMoneySumPay
        ,sum(if(l.fee_flag=1,t.money_profit,0)) rechargeMoneyProfitSumPay
        ,sum(if(l.fee_flag=2,t.money_recharge,0)) rechargeMoneySumFree
        ,sum(if(l.fee_flag=2,t.money_profit,0)) rechargeMoneyProfitSumFree
        <!--,count(distinct t.member_id) rechargeMemberCount-->
        from fast_member_order_recharge t
        left join fast_link l on l.id = t.link_id
        <where>
            <include refid="whereSQL"/>
            <if test="payTimeS != null">
                and t.`pay_time` &gt;= #{payTimeS}
            </if>
            <if test="payTimeE != null">
                and t.`pay_time` &lt;= #{payTimeE}
            </if>
            <if test="addState != null">
                and t.`add_state` = #{addState}
            </if>
            <if test="contentType != null">AND t.content_type=#{contentType}</if>
            and t.`drama_id` > 0
            and t.`state` = 1 and t.coin_change_id=0
        </where>
        group by t.`drama_id`
    </select>

    <!--用户充值金额分析-单日明细表  -->
    <select id="queryRechargeMoneyDayListNew" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t.drama_id dramaId
        <!--,sum(t.money_recharge) rechargeMoneySum-->
        <!--,sum(t.money_profit) rechargeMoneyProfitSum-->
        ,sum(if(l.fee_flag=1,t.money_recharge,0)) rechargeMoneySumPay
        ,sum(if(l.fee_flag=1,t.money_profit,0)) rechargeMoneyProfitSumPay
        ,sum(if(l.fee_flag=2,t.money_recharge,0)) rechargeMoneySumFree
        ,sum(if(l.fee_flag=2,t.money_profit,0)) rechargeMoneyProfitSumFree
        ,DATE_FORMAT(t.pay_time,'%Y-%m-%d') dataDay
        from fast_member_order_recharge t
        left join fast_link l on l.id = t.link_id
        <where>
            <include refid="whereSQL"/>
            <if test="payTimeS != null">
                and t.`pay_time` &gt;= #{payTimeS}
            </if>
            <if test="payTimeE != null">
                and t.`pay_time` &lt;= #{payTimeE}
            </if>
            <if test="addState != null">
                and t.`add_state` = #{addState}
            </if>
            <if test="contentType != null">AND t.content_type=#{contentType}</if>
            and t.`state` = 1 and t.coin_change_id=0
            and t.`drama_id` > 0
        </where>
        group by dataDay
    </select>

    <!--用户充值金额分析  -->
    <select id="queryRechargeMoneyDayList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,drama.drama_name dramaName
        ,drama.release_date releaseDate
        from (
        <include refid="rechargeMoneyDaySQL"/>
        ) t1 left join fast_drama drama on drama.id = t1.dramaId and drama.del_flag=0
    </select>

    <!--用户充值金额分析  -->
    <sql id="rechargeMoneyDaySQL">
        select t.drama_id dramaId
        ,sum(t.money_recharge) rechargeMoneySum
        ,sum(t.money_profit) rechargeMoneyProfitSum
        ,count(distinct t.member_id) rechargeMemberCount
        ,DATE_FORMAT(t.pay_time,'%Y-%m-%d') dataDay
        from fast_member_order_recharge t
        <where>
            <include refid="whereSQL"/>
            <if test="payTimeS != null">
                and t.`pay_time` &gt;= #{payTimeS}
            </if>
            <if test="payTimeE != null">
                and t.`pay_time` &lt;= #{payTimeE}
            </if>
            <if test="addState != null">
                and t.`add_state` = #{addState}
            </if>
            <if test="contentType != null">AND t.content_type=#{contentType}</if>
            and t.`state` = 1 and t.coin_change_id=0
            and t.`drama_id` > 0
        </where>
        group by dataDay
    </sql>

    <!--查询免费广告收入  -->
    <select id="queryAdIncomeMoneyList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t.drama_id dramaId
        ,sum(t.ecpm_cost) adIncomeMoneySum
        ,sum(if(t.`add_state`=1,t.ecpm_cost,0)) adIncomeMoneySumAdd
        from fast_member_unlock_start_log t
        <where>
            <include refid="whereSQL"/>
            <if test="payTimeS != null and payTimeE != null">
                and t.`cost_date` between #{payTimeS, jdbcType=DATE} and #{payTimeE, jdbcType=DATE}
            </if>
        </where>
        group by t.`drama_id`
    </select>

    <!--查询免费广告收入  -->
    <select id="queryAdIncomeMoneyDayList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t.drama_id dramaId
        ,sum(t.ecpm_cost) adIncomeMoneySum
        ,sum(if(t.`add_state`=1,t.ecpm_cost,0)) adIncomeMoneySumAdd
        ,DATE_FORMAT(t.cost_date,'%Y-%m-%d') dataDay
        from fast_member_unlock_start_log t
        <where>
            <include refid="whereSQL"/>
            <if test="payTimeS != null and payTimeE != null">
                and t.`cost_date` between #{payTimeS, jdbcType=DATE} and #{payTimeE, jdbcType=DATE}
            </if>
        </where>
        group by dataDay
    </select>

    <!--用户观看分析  -->
    <select id="queryWatchMemberCountList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select
        t.drama_id,
        count(distinct(t.member_id)) watchMemberCount
        from fast_report_member_recent_day t
        <where>
            <if test="watchTimeS != null and watchTimeE!= null">
                t.`report_date` &gt;= #{watchTimeS}
                and t.`report_date` &lt;= #{watchTimeE}
            </if>
            <if test="dramaId != null">
                and t.drama_id = #{dramaId}
            </if>
        </where>
        group by t.drama_id
    </select>


    <!--用户观看分析  -->
    <select id="queryWatchMemberCountList2" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select
            t1.*
        from (select
            t.drama_id dramaId,
            count(distinct(t.member_id)) watchMemberCount
            from fast_report_member_recent_day t
            <where>
                <if test="watchTimeS != null and watchTimeE!= null">
                    t.`report_date` &gt;= #{watchTimeS}
                    and t.`report_date` &lt;= #{watchTimeE}
                </if>
                <if test="dramaId != null">
                    and t.drama_id = #{dramaId}
                </if>
            </where>
        group by t.drama_id) t1
        left join fast_drama fd on t1.dramaId = fd.id
        <where>
            <if test="contentType != null">
                <if test="contentType == 1">
                    and fd.drama_perform_type = 1
                </if>
                <if test="contentType == 4">
                    and fd.drama_perform_type = 2
                </if>
            </if>
        </where>
    </select>

    <!--用户观看分析  -->
    <select id="queryWatchMemberCountListRight" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.watchMemberCount
        from (
        <include refid="watchMemberCountSQL"/>
        ) t1
        <where>
            <if test="dramaId != null">
                and drama.id = #{dramaId}
            </if>
        </where>
    </select>

    <!--用户观看分析  -->
    <select id="queryWatchMemberCountListOrderBy" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        from (
        <include refid="watchMemberCountSQL"/>
        ) t1
        order by watchMemberCount <if test="sortOrder==1">desc</if>, dramaId desc
    </select>


    <!--用户观看分析  -->
    <sql id="watchMemberCountSQL">
        select t.drama_id dramaId
        ,count(distinct t.member_id) watchMemberCount
        from fast_member_recent_log t
        left join fast_member m ON m.`id`=t.`member_id`
        <if test="contentType != null">
            left join fast_drama fd on fd.id = t.drama_id
        </if>
        <where>
            <include refid="whereSQL"/>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
            <if test="registerTimeS != null">
                and m.`create_time` &gt;= #{registerTimeS}
            </if>
            <if test="registerTimeE != null">
                and m.`create_time` &lt;= #{registerTimeE}
            </if>
            <if test="seriesNum != null">
                and t.`series_num` = #{seriesNum}
            </if>
            <if test="playState != null">
                and t.`play_state` = #{playState}
            </if>
            <if test="contentType != null">
                <if test="contentType == 1">
                    and fd.drama_perform_type = 1
                </if>
                <if test="contentType == 4">
                    and fd.drama_perform_type = 2
                </if>
            </if>
            and t.`drama_id` > 0
        </where>
        group by t.`drama_id`
    </sql>

    <select id="queryUnlockMemberCountList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select
        ud.drama_id,
        count(distinct(ud.member_id)) unlockMemberCount
        from
        fast_report_member_unlock_day ud
        <where>
            <if test="watchTimeS != null and watchTimeE != null">
                ud.report_date &gt;= #{watchTimeS}
                and ud.report_date &lt;= #{watchTimeE}
            </if>
            <if test="dramaId != null">
                and ud.drama_id = #{dramaId}
            </if>
        </where>
        GROUP BY ud.drama_id
    </select>

    <select id="queryUnlockRechargeList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        SELECT
        rc.drama_id,
        rc.retail_id,
        r.retail_type,
        ifnull(sum(coin_consume),0) coinConsume
        FROM
        cp_report_consume rc
        left join fast_retail r on r.id = rc.retail_id
        <where>
            <if test="watchTimeS != null and watchTimeE!= null">
                rc.report_date &gt;= #{watchTimeS} AND rc.report_date &lt;= #{watchTimeE}
            </if>
            <if test="incomeUser != null">
                and r.retail_type in (${incomeUser})
            </if>
            <if test="dramaId != null">
                and rc.drama_id = #{dramaId}
            </if>
        </where>
        and content_type = #{contentType}
        GROUP BY rc.drama_id,r.retail_type
    </select>

    <!--用户观看分析  -->
    <select id="queryWatchMemberCountDayList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.watchMemberCount
        ,t1.dataDay
        ,drama.id dramaId
        ,drama.drama_name dramaName
        ,drama.release_date releaseDate
        from (
        <include refid="watchMemberCountDaySQL"/>
        ) t1 left join fast_drama drama on drama.id = t1.dramaId
        <where>
            <if test="dramaId != null">
                and drama.id = #{dramaId}
            </if>
        </where>
    </select>

    <!--用户观看分析  -->
    <sql id="watchMemberCountDaySQL">
        select t.drama_id dramaId
        ,count(distinct t.member_id) watchMemberCount
        ,DATE_FORMAT(t.create_time,'%Y-%m-%d') dataDay
        from fast_member_recent_log t
        <where>
            <include refid="whereSQL"/>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
            <if test="seriesNum != null">
                and t.`series_num` = #{seriesNum}
            </if>
            <if test="playState != null">
                and t.`play_state` = #{playState}
            </if>
            and t.`drama_id` > 0
        </where>
        group by dataDay
    </sql>


    <!--短剧完播人数  -->
    <select id="queryWatchFinishMemberCountListNew" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t.drama_id dramaId
        ,count(t.member_id) watchFinishMemberCount
        from fast_member_recent_drama_finish t
        <where>
            <include refid="whereSQL"/>
            <if test="finishDateS != null">
                and t.`finish_date` &gt;= #{finishDateS}
            </if>
            <if test="finishDateE != null">
                and t.`finish_date` &lt;= #{finishDateE}
            </if>
        </where>
        group by t.drama_id
    </select>
    <!--短剧完播人数  -->
    <select id="queryWatchFinishMemberCountList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,count(*) watchFinishMemberCount
        from (
        <include refid="watchFinishMemberCountSQL"/>
        ) t1
        group by t1.dramaId
    </select>

    <!--短剧完播人数  -->
    <select id="queryWatchFinishMemberCountListOrderBy" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,drama.drama_name dramaName
        ,drama.release_date releaseDate
        ,sum(if(t1.watchCount = t1.seriesNumUpdate,1,0)) watchFinishMemberCount
        from (
        <include refid="watchFinishMemberCountSQL"/>
        ) t1 left join fast_drama drama on drama.id = t1.dramaId
        group by t1.dramaId
        order by watchFinishMemberCount <if test="sortOrder==1">desc</if>, dramaId desc
    </select>

    <!--短剧完播人数  -->
    <sql id="watchFinishMemberCountSQL">
        select t.drama_id dramaId,t.`member_id` memberId
        ,count(distinct t.series_num) watchCount <!--单个用户看某一个剧的剧集数-->
        ,(select o.series_num_update from fast_drama o where o.id = t.drama_id) seriesNumUpdate
        from fast_member_recent_log t
        <where>
            <include refid="whereSQL"/>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
            <if test="seriesNum != null">
                and t.`series_num` = #{seriesNum}
            </if>
            <if test="playState != null">
                and t.`play_state` = #{playState}
            </if>
            and t.`drama_id` > 0
        </where>
        group by t.`drama_id`, t.`member_id`
        having watchCount = seriesNumUpdate
    </sql>

    <!--短剧完播人数  -->
    <select id="queryWatchFinishMemberCountDayListV2" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select count(t.member_id) watchFinishMemberCount
        ,DATE_FORMAT(t.finish_date,'%Y-%m-%d') dataDay
        from fast_member_recent_drama_finish t
        <where>
            <include refid="whereSQL"/>
            <if test="finishDateS != null">
                and t.`finish_date` &gt;= #{finishDateS}
            </if>
            <if test="finishDateE != null">
                and t.`finish_date` &lt;= #{finishDateE}
            </if>
        </where>
        group by dataDay
    </select>

    <!--短剧完播人数  -->
    <select id="queryWatchFinishMemberCountDayList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,count(*) watchFinishMemberCount
        from (
        <include refid="watchFinishMemberCountDaySQL"/>
        ) t1
        group by t1.dataDay
    </select>

    <!--短剧完播人数  -->
    <sql id="watchFinishMemberCountDaySQL">
        select t.drama_id dramaId,t.`member_id` memberId
        ,count(distinct t.series_num) watchCount <!--单个用户某天看某一个剧的剧集数-->
        ,(select o.series_num_update from fast_drama o where o.id = t.drama_id) seriesNumUpdate
        ,DATE_FORMAT(t.create_time,'%Y-%m-%d') dataDay
        from fast_member_recent_log t
        <where>
            <include refid="whereSQL"/>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
            <if test="seriesNum != null">
                and t.`series_num` = #{seriesNum}
            </if>
            <if test="playState != null">
                and t.`play_state` = #{playState}
            </if>
            and t.`drama_id` > 0
        </where>
        group by dataDay, t.`drama_id`, t.`member_id`
        having watchCount = seriesNumUpdate
    </sql>


    <!--用户K币消费分析  -->
    <select id="queryCoinConsumeList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,(t1.coinRechargeConsume+t1.coinGiveConsume) coinConsume
        from (
        <include refid="coinConsumeSQL"/>
        ) t1
    </select>
    <!--用户K币消费分析  -->
    <select id="queryCoinConsumeListNew" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,(t1.coinRechargeConsume+t1.coinGiveConsume) coinConsume
        from (
            select t.drama_id dramaId
            ,ifnull(sum(t.cost_coin),0) coinRechargeConsume
            ,ifnull(sum(t.cost_give_coin),0) coinGiveConsume
            from fast_report_drama_day t
            <where>
                <include refid="whereSQL"/>
                <if test="watchTimeS != null and watchTimeE != null">
                    and t.`report_date` between #{watchTimeS, jdbcType=DATE} and #{watchTimeE, jdbcType=DATE}
                </if>
                and t.`drama_id` > 0
            </where>
            group by t.`drama_id`
        ) t1
    </select>

    <!--用户K币消费分析  -->
    <select id="queryCoinConsumeListOrderBy" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,(t1.coinRechargeConsume+t1.coinGiveConsume) coinConsume
        from (
        <include refid="coinConsumeSQL"/>
        ) t1
        order by coinConsume <if test="sortOrder==1">desc</if>, dramaId desc
    </select>

    <!--用户K币消费分析  -->
    <sql id="coinConsumeSQL">
        select t.drama_id dramaId
        ,ifnull(sum(t.coin_recharge_consume),0) coinRechargeConsume
        ,ifnull(sum(t.coin_give_consume),0) coinGiveConsume
        from fast_member_order_consume t
        left join fast_drama fd on t.drama_id = fd.id
        <where>
            <include refid="whereSQL"/>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
            <if test="contentType != null">
                <if test="contentType == 1">
                    and fd.drama_perform_type = 1
                </if>
                <if test="contentType == 4">
                    and fd.drama_perform_type = 2
                </if>
            </if>
            and t.`coin_change_id` = 0
            and t.`drama_id` > 0
        </where>
        group by t.`drama_id`
    </sql>

    <!--用户K币消费分析  -->
    <select id="queryCoinConsumeDayList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,(t1.coinRechargeConsume+t1.coinGiveConsume) coinConsume
        ,drama.drama_name dramaName
        ,drama.release_date releaseDate
        from (
        <include refid="coinConsumeDaySQL"/>
        ) t1 left join fast_drama drama on drama.id = t1.dramaId
    </select>

    <!--用户K币消费分析  -->
    <sql id="coinConsumeDaySQL">
        select t.drama_id dramaId
        ,ifnull(sum(t.coin_recharge_consume),0) coinRechargeConsume
        ,ifnull(sum(t.coin_give_consume),0) coinGiveConsume
        ,DATE_FORMAT(t.create_time,'%Y-%m-%d') dataDay
        from fast_member_order_consume t
        <where>
            <include refid="whereSQL"/>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
            and t.`coin_change_id` = 0
            and t.`drama_id` > 0
        </where>
        group by dataDay
    </sql>

    <!-- 公共条件  -->
    <sql id="whereSQL">
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="retailIds != null and retailIds.length()>0">
            and t.`retail_id` in (${retailIds})
        </if>
        <if test="memberIds != null and memberIds.length()>0">
            and t.`member_id` in (${memberIds})
        </if>
        <if test="linkId != null">
            and t.`link_id` = #{linkId}
        </if>
        <if test="linkIds != null and linkIds.length()>0">
            and t.`link_id` in (${linkIds})
        </if>
        <if test="miniId != null">
            and t.`mini_id` = #{miniId}
        </if>
        <if test="miniIds != null and miniIds.length()>0">
            and t.`mini_id` in (${miniIds})
        </if>
        <if test="dramaId != null ">
            and t.`drama_id` = #{dramaId}
        </if>
        <if test="dramaIds != null and dramaIds.length()>0">
            and t.`drama_id` in (${dramaIds})
        </if>
        <if test="officialId != null">
            and t.official_id = #{officialId}
        </if>
        <if test="officialIds != null and officialIds.length()>0">
            and t.`official_id` in (${officialIds})
        </if>
    </sql>

</mapper>
