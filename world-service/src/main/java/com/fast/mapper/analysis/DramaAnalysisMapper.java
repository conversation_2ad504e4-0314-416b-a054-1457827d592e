/*
 * Powered By fast.up
 */
package com.fast.mapper.analysis;

import com.fast.annotation.JingFenDataLake;
import com.fast.annotation.Slave;
import com.fast.po.analysis.DramaAnalysisPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 短剧分析
 *
 * <AUTHOR>
 */
@Mapper
public interface DramaAnalysisMapper {

    /**
     * 用户充值人数分析
     *
     * @param po
     * @return
     */
    List<DramaAnalysisPO> queryRechargeMemberCountList(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryRechargeMemberCountDayList(DramaAnalysisPO po);

    @JingFenDataLake
    List<DramaAnalysisPO> queryRechargeMemberCountListOrderBy(DramaAnalysisPO po);

    @JingFenDataLake
    List<DramaAnalysisPO> queryAdIncomeMemberCountListOrderBy(DramaAnalysisPO po);

    /**
     * 用户充值金额分析
     *
     * @param po
     * @return
     */
    List<DramaAnalysisPO> queryRechargeMoneyList(DramaAnalysisPO po);

    // 查询免费广告收入
    List<DramaAnalysisPO> queryAdIncomeMoneyList(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryAdIncomeMoneyDayList(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryRechargeMoneyDayList(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryRechargeMoneyDayListNew(DramaAnalysisPO po);

    /**
     * 用户解锁分析
     *
     * @param po
     * @return
     */
    List<DramaAnalysisPO> queryUnlockMemberCountList(DramaAnalysisPO po);

    /**
     * 用户解锁订阅金额
     *
     * @param po
     * @return
     */
    List<DramaAnalysisPO> queryUnlockRechargeList(DramaAnalysisPO po);

    /**
     * 用户观看分析
     *
     * @param po
     * @return
     */
    List<DramaAnalysisPO> queryWatchMemberCountList(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryWatchMemberCountList2(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryWatchMemberCountListRight(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryWatchMemberCountDayList(DramaAnalysisPO po);

    @JingFenDataLake
    List<DramaAnalysisPO> queryWatchMemberCountListOrderBy(DramaAnalysisPO po);

    /**
     * 用户K币消费分析
     *
     * @param po
     * @return
     */
    List<DramaAnalysisPO> queryCoinConsumeList(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryCoinConsumeListNew(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryCoinConsumeDayList(DramaAnalysisPO po);

    @JingFenDataLake
    List<DramaAnalysisPO> queryCoinConsumeListOrderBy(DramaAnalysisPO po);

    /**
     * 短剧完播人数
     *
     * @param po
     * @return
     */
    List<DramaAnalysisPO> queryWatchFinishMemberCountList(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryWatchFinishMemberCountListNew(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryWatchFinishMemberCountDayList(DramaAnalysisPO po);

    List<DramaAnalysisPO> queryWatchFinishMemberCountDayListV2(DramaAnalysisPO po);

    @JingFenDataLake
    List<DramaAnalysisPO> queryWatchFinishMemberCountListOrderBy(DramaAnalysisPO po);

    /**
     * 短剧解锁人数
     *
     * @param query
     * @return
     */
    @Slave
    List<DramaAnalysisPO> queryUnlockMemberCountDayList(DramaAnalysisPO query);

    /**
     * 短剧解锁金额
     *
     * @param query
     * @return
     */
    @Slave
    List<DramaAnalysisPO> queryUnlockMoneyCountDayList(DramaAnalysisPO query);

}
