/*
 * Powered By fast.up
 */
package com.fast.mapper.analysis;

import com.fast.po.analysis.LinkAnalysisPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 渠道分析
 *
 * <AUTHOR>
 */
@Mapper
public interface LinkAnalysisMapper {

    // 各类数据汇总
    List<LinkAnalysisPO> queryCoreAllAnalysisList(LinkAnalysisPO po);

    // 新染色用户
    List<LinkAnalysisPO> queryAddColorMemberSumList(LinkAnalysisPO po);

    // 查询创建的链接数
    List<LinkAnalysisPO> queryAllLinkCount(LinkAnalysisPO po);

    // 每日数据汇总
    List<LinkAnalysisPO> queryCorePerDayAnalysisList(LinkAnalysisPO po);

    // 今日数据汇总
    List<LinkAnalysisPO> queryTodayDataAnalysisList(LinkAnalysisPO po);

    // 新染色用户
    int queryAddColorMemberSum(LinkAnalysisPO po);

    // 查询注册用户数
    int queryAddNewMemberSum(LinkAnalysisPO po);

    // 今日新染色用户
    List<LinkAnalysisPO> queryTodayAddColorMember(LinkAnalysisPO po);

    // 今日统计数据
    LinkAnalysisPO queryTodayRechargeSum(LinkAnalysisPO po);

    // 累计数据
    List<LinkAnalysisPO> queryDetailSummaryAnalysisList(LinkAnalysisPO po);

    // 今日数据监测
    List<LinkAnalysisPO> queryDetailTodayAnalysisList(LinkAnalysisPO po);

    List<LinkAnalysisPO> queryFreeLinkROISummaryAnalysis(LinkAnalysisPO params);

    List<LinkAnalysisPO> queryFreeLinkROIAnalysis(LinkAnalysisPO params);

    LinkAnalysisPO queryFreeLinkROISummaryAnalysisSummary(LinkAnalysisPO params);

    LinkAnalysisPO queryFreeLinkROIAnalysisSummary(LinkAnalysisPO params);
}
