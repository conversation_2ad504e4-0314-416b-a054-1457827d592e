/*
 * Powered By fast.up
 */
package com.fast.mapper.language;

import com.fast.po.language.FastLanguagePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastLanguageMapper {

    // 通过id查询单个对象
    FastLanguagePO queryById(FastLanguagePO entity);

    // 通过id查询单个对象
    FastLanguagePO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastLanguagePO queryOne(FastLanguagePO entity);

    // 查询全部
    List<FastLanguagePO> queryList(FastLanguagePO entity);

    // 查询总数
    int queryCount(FastLanguagePO entity);

    // 可选新增
    int insertSelective(FastLanguagePO entity);

    // 批量新增
    int insertBatch(List<FastLanguagePO> list);

    // 更新
    int updateById(FastLanguagePO entity);

    // 删除
    int deleteById(@Param("id") Integer id);
}
