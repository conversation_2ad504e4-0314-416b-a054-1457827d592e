<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.language.FastLanguageMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastLanguage_columns">
		select t.`id`,t.`name`,t.`code`,t.`seq`,t.`show`,t.`del_flag`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastLanguagePO">
		<include refid="FastLanguage_columns" />
	    from fast_language t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastLanguagePO" resultType="FastLanguagePO">
		<include refid="FastLanguage_columns" />
	    from fast_language t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastLanguagePO" resultType="FastLanguagePO">
		<include refid="FastLanguage_columns" />
	    from fast_language t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`seq`
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastLanguagePO" resultType="int">
		select count(*)
	    from fast_language t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="name != null">
			and t.`name` = #{name}
		</if>
		<if test="code != null">
			and t.`code` = #{code}
		</if>
		<if test="codes != null and codes.size() > 0">
			and t.`code` in
			<foreach collection="codes" item="code" open="(" separator="," close=")">
				#{code}
			</foreach>
		</if>
		<if test="seq != null">
			and t.`seq` = #{seq}
		</if>
		<if test="show != null">
			and t.`show` = #{show}
		</if>
		<if test="delFlag != null">
			and t.`del_flag` = #{delFlag}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastLanguagePO">
        insert into fast_language
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="name != null">name,</if>
	        <if test="code != null">code,</if>
	        <if test="seq != null">seq,</if>
	        <if test="show != null">show,</if>
	        <if test="delFlag != null">del_flag,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="name != null">#{name},</if>
	        <if test="code != null">#{code},</if>
	        <if test="seq != null">#{seq},</if>
	        <if test="show != null">#{show},</if>
	        <if test="delFlag != null">#{delFlag},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastLanguagePO">
        insert into fast_language (
         name, code, seq, show, del_flag, creator_id, updator_id, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.name}, #{item.code}, #{item.seq}, #{item.show}, #{item.delFlag}, #{item.creatorId}, #{item.updatorId}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastLanguagePO">
        update fast_language
        <set>
         	<if test="name != null" >
               name = #{name},
            </if>
         	<if test="code != null" >
               code = #{code},
            </if>
         	<if test="seq != null" >
               seq = #{seq},
            </if>
         	<if test="show != null" >
               show = #{show},
            </if>
         	<if test="delFlag != null" >
               del_flag = #{delFlag},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where id = #{id}  
	</update>
	
	<delete id="deleteById">
		delete from fast_language where id = #{id}
	</delete>
</mapper>
