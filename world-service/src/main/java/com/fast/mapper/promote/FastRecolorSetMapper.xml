<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastRecolorSetMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastRecolorSet_columns">
		select t.`id`,t.`type`,t.active,t.`app_type`,t.`recolor_hours`,t.`media_type`,t.`bridge`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastRecolorSetPO">
		<include refid="FastRecolorSet_columns" />
	    from fast_recolor_set t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastRecolorSetPO" resultType="FastRecolorSetPO">
		<include refid="FastRecolorSet_columns" />
	    from fast_recolor_set t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastRecolorSetPO" resultType="FastRecolorSetPO">
		<include refid="FastRecolorSet_columns" />
		,lr.road_name
		,m.mini_name
	    from fast_recolor_set t
	    left join fast_link_road lr on lr.id = t.road_id
	    left join fast_mini m on m.app_id = t.app_id
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastRecolorSetPO" resultType="int">
		select count(*)
	    from fast_recolor_set t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="type != null">
			and t.`type` = #{type}
		</if>
		<if test="appType != null">
			and t.`app_type` = #{appType}
		</if>
		<if test="active != null">
			and t.`active` = #{active}
		</if>
		<if test="recolorHours != null">
			and t.`recolor_hours` = #{recolorHours}
		</if>
		<if test="mediaType != null">
			and t.`media_type` = #{mediaType}
		</if>
		<if test="bridge != null">
			and t.`bridge` = #{bridge}
		</if>
		<if test="roadId != null">
			and t.`road_id` = #{roadId}
		</if>
		<if test="appId != null and appId.length > 0 ">
			and t.`app_id` = #{appId}
		</if>
		<if test="contentType != null">
			and t.`content_type` = #{contentType}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastRecolorSetPO">
        insert into fast_recolor_set
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="type != null">`type`,</if>
	        <if test="appType != null">app_type,</if>
	        <if test="active != null">active,</if>
	        <if test="contentType != null">content_type,</if>
	        <if test="roadId != null">road_id,</if>
	        <if test="recolorHours != null">recolor_hours,</if>
	        <if test="mediaType != null">media_type,</if>
	        <if test="bridge != null">bridge,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="appId != null">app_id,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="type != null">#{type},</if>
	        <if test="appType != null">#{appType},</if>
	        <if test="active != null">#{active},</if>
	        <if test="contentType != null">#{contentType},</if>
	        <if test="roadId != null">#{roadId},</if>
	        <if test="recolorHours != null">#{recolorHours},</if>
	        <if test="mediaType != null">#{mediaType},</if>
	        <if test="bridge != null">#{bridge},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="appId != null">#{appId},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastRecolorSetPO">
        insert into fast_recolor_set (
         type, app_type, recolor_hours, media_type, bridge, creator_id, updator_id, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.type}, #{item.appType}, #{item.recolorHours}, #{item.mediaType}, #{item.bridge}, #{item.creatorId}, #{item.updatorId}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastRecolorSetPO">
        update fast_recolor_set
        <set>
         	<if test="type != null" >
               `type` = #{type},
            </if>
         	<if test="appType != null" >
               app_type = #{appType},
            </if>
         	<if test="recolorHours != null" >
               recolor_hours = #{recolorHours},
            </if>
         	<if test="active != null" >
               active = #{active},
            </if>
         	<if test="mediaType != null" >
               media_type = #{mediaType},
            </if>
         	<if test="bridge != null" >
               bridge = #{bridge},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
         	<if test="appId != null" >
               app_id = #{appId},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
