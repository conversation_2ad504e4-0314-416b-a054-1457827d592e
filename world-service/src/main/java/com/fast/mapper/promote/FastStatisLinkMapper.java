/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.annotation.JingFenDataLake;
import com.fast.annotation.Slave;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.po.promote.FastStatisLinkFreePO;
import com.fast.po.promote.FastStatisLinkPO;
import com.fast.vo.promote.FastLinkQueryVO;
import com.fast.vo.promote.FastStatisLinkVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStatisLinkMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastStatisLinkPO
     * @return
     */
    FastStatisLinkPO queryById(FastStatisLinkPO fastStatisLinkPO);

    /**
     * 通过条件查询单个对象
     *
     * @param fastStatisLinkPO
     * @return
     */
    FastStatisLinkPO queryOne(FastStatisLinkPO fastStatisLinkPO);

    FastStatisLinkPO querySumByDayDramaId(FastStatisLinkPO fastStatisLinkPO);

    /**
     * 查询全部
     *
     * @param fastStatisLinkPO
     * @return
     */
    List<FastStatisLinkPO> queryList(FastStatisLinkPO fastStatisLinkPO);

    List<FastStatisLinkPO> queryListCostPlus(FastStatisLinkPO fastStatisLinkPO);

    /**
     * 查询全部
     *
     * @param fastStatisLinkPO
     * @return
     */
    List<FastStatisLinkVO> queryDayRoiList(FastLinkQueryVO fastStatisLinkPO);

    /**
     * 查询总数
     *
     * @param fastStatisLinkPO
     * @return
     */
    int queryCount(FastStatisLinkPO fastStatisLinkPO);

    /**
     * 查询当天新增用户数
     *
     * @param fastStatisLinkVO
     * @return
     */
    @Slave
    int queryNumDay(FastStatisLinkVO fastStatisLinkVO);

    @Slave
    int queryNumDayPlus(FastStatisLinkVO fastStatisLinkVO);

    @Slave
    int querySubLinkNumDayPlus(FastStatisLinkVO fastStatisLinkVO);

    /**
     * 查询累计新增用户数
     *
     * @param fastStatisLinkVO
     * @return
     */
    @Slave
    FastStatisLinkVO queryNumAll(FastStatisLinkVO fastStatisLinkVO);

    /**
     * 根据日期查询链接id
     */
    @Slave
    Set<Integer> queryStaticDataLinkIds(@Param("statisDate") String statisDate);

    /**
     * 查询新增用户充值数据
     *
     * @param fastStatisLinkVO
     * @return
     */
    List<FastStatisLinkVO> queryDramaRecharge(FastStatisLinkVO fastStatisLinkVO);

    /**
     * 查询新增用户充值数据
     *
     * @param fastStatisLinkVO
     * @return
     */
    List<FastStatisLinkVO> queryDramaDayRecharge(FastStatisLinkVO fastStatisLinkVO);

    /**
     * 查询新增用户充值数据
     *
     * @param fastStatisLinkVO
     * @return
     */
    FastStatisLinkVO queryOneDramaRecharge(FastStatisLinkVO fastStatisLinkVO);

    /**
     * 查询当天新增用户-充值人数、笔数、金额
     *
     * @param fastStatisLinkVO
     * @return
     */
    // @Slave
    FastStatisLinkVO queryNumDayRech(FastStatisLinkVO fastStatisLinkVO);

    /**
     * 查询当天新增用户-第N天充值
     *
     * @param fastStatisLinkVO
     * @return
     */
    @Slave
    FastStatisLinkVO queryMoneyD60Rech(FastStatisLinkVO fastStatisLinkVO);

    /**
     * 查询当天新增用户-累计充值
     *
     * @param fastStatisLinkVO
     * @return
     */
    @Slave
    FastStatisLinkVO queryMoneyDAllRech(FastStatisLinkVO fastStatisLinkVO);

    /**
     * 查询新增用户当天-充值人数、笔数、金额
     *
     * @param fastStatisLinkVO
     * @return
     */
    // @Slave
    FastStatisLinkVO queryNumAllDayRech(FastStatisLinkVO fastStatisLinkVO);

    // @Slave
    FastStatisLinkVO queryNumAllDayRechPlus(FastStatisLinkVO fastStatisLinkVO);

    /**
     * 查询新增用户累计-充值人数、笔数、金额
     *
     * @param fastStatisLinkVO
     * @return
     */
    // @Slave
    @JingFenDataLake
    FastStatisLinkVO queryNumAllRech(FastStatisLinkVO fastStatisLinkVO);

    @JingFenDataLake
    FastStatisLinkVO queryNumAllRechSimple(FastStatisLinkVO fastStatisLinkVO);

    @JingFenDataLake
    List<FastStatisLinkVO> queryNumAllRechList(FastStatisLinkVO fastStatisLinkVO);

    /**
     * 新增
     *
     * @param fastStatisLinkPO
     * @return
     */
    int insert(FastStatisLinkPO fastStatisLinkPO);

    /**
     * 可选新增
     *
     * @param fastStatisLinkPO
     * @return
     */
    int insertSelective(FastStatisLinkPO fastStatisLinkPO);

    /**
     * 更新
     *
     * @param fastStatisLinkPO
     * @return
     */
    int updateById(FastStatisLinkPO fastStatisLinkPO);

    FastStatisLinkVO getLinkOrderStatisDay(FastStatisLinkPO fastStatisLinkPO);

    FastStatisLinkVO getLinkOrderStatisFreeDay(FastStatisLinkPO fastStatisLinkPO);

    @Slave
    FastLinkQueryVO queryFreeData(FastLinkQueryVO fastLinkQueryVO);

    @Slave
    FastStatisLinkFreePO getFreeImmeMonitorData(FastStatisLinkFreePO params);

    BigDecimal queryUsAgentCost(DramaAnalysisPO statisLinkQuery);

    List<FastStatisLinkPO> queryImportList(FastStatisLinkPO queryRecent);
}
