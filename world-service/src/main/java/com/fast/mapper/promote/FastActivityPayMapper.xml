<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastActivityPayMapper">
    <!-- 用于select查询公用抽取的列 -->
    <sql id="FastPromoteActivityPay_columns">
        select t.`id`, t.`activity_id`, t.`order_id`, t.`member_id`, t.`state`, t.`order_type`, t.`money_recharge`,
               t.`create_time`, t.`create_date`
    </sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" parameterType="FastActivityPayPO" resultType="FastActivityPayPO">
        <include refid="FastPromoteActivityPay_columns"/>
        from fast_promote_activity_pay t
        <where>
            t.`id` = #{id}
        </where>
    </select>

    <!-- 通过id查询单个对象 -->
    <select id="queryByOrderId" resultType="FastActivityPayPO">
        <include refid="FastPromoteActivityPay_columns"/>
        from fast_promote_activity_pay t
        <where>
            t.`order_id` = #{orderId}
        </where>
    </select>

    <!--通过条件查询单个对象  -->
    <select id="queryOne" parameterType="FastActivityPayPO" resultType="FastActivityPayPO">
        <include refid="FastPromoteActivityPay_columns"/>
        from fast_promote_activity_pay t
        <where>
            <include refid="whereSQL"/>
        </where>
        limit 1
    </select>

    <!--分页查询集合  -->
    <select id="queryList" parameterType="FastActivityPayPO" resultType="FastActivityPayPO">
        <include refid="FastPromoteActivityPay_columns"/>
        from fast_promote_activity_pay t
        <where>
            <include refid="whereSQL"/>
        </where>
        order by t.`id` desc
    </select>

    <!--分页查询集合  -->
    <select id="queryListGroupDate" parameterType="FastActivityPayPO" resultType="FastActivityPayPO">
        select t.create_date
            ,t.`activity_id`
            ,count(DISTINCT(t.`member_id`)) payMemberCount
            ,count(*) payCount
            ,sum(t.`money_recharge`) moneyRecharge
        from fast_promote_activity_pay t
        <where>
            <include refid="whereSQL"/>
        </where>
        group by t.`create_date`
        order by t.`create_date` desc
    </select>

    <!--分页查询集合  -->
    <select id="queryListGroupMember" parameterType="FastActivityPayPO" resultType="FastActivityPayPO">
        select t1.`activity_id`
            ,count(*) payMemberCount
            ,sum(t1.payCount) payCount
            ,sum(t1.moneyRecharge) moneyRecharge from (
                select t.`activity_id`
                    ,1 payMemberCount
                    ,count(*) payCount
                    ,sum(t.`money_recharge`) moneyRecharge
                from fast_promote_activity_pay t
                <where>
                    <include refid="whereSQL"/>
                </where>
                group by t.`member_id`
            ) t1
    </select>

    <!--查询总数  -->
    <select id="queryCount" parameterType="FastActivityPayPO" resultType="int">
        select count(*)
        from fast_promote_activity_pay t
        <where>
            <include refid="whereSQL"/>
        </where>
    </select>

    <!-- 公共条件  -->
    <sql id="whereSQL">
        <if test="id != null">
            and t.`id` = #{id}
        </if>
        <if test="activityId != null">
            and t.`activity_id` = #{activityId}
        </if>
        <if test="orderId != null">
            and t.`order_id` = #{orderId}
        </if>
        <if test="memberId != null">
            and t.`member_id` = #{memberId}
        </if>
        <if test="state != null">
            and t.`state` = #{state}
        </if>
        <if test="orderType != null">
            and t.`order_type` = #{orderType}
        </if>
        <if test="moneyRecharge != null">
            and t.`money_recharge` = #{moneyRecharge}
        </if>
        <if test="createTime != null">
            and t.`create_time` = #{createTime}
        </if>
        <if test="createDate != null">
            and t.`create_date` = #{createDate}
        </if>
        <if test="activityTimeS != null">
            and t.`create_date` &gt;= #{activityTimeS}
        </if>
        <if test="activityTimeE != null">
            and t.`create_date` &lt;= #{activityTimeE}
        </if>
    </sql>

    <!-- 可选新增 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastActivityPayPO">
        insert into fast_promote_activity_pay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">
                activity_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="memberId != null">
                member_id,
            </if>
            <if test="orderType != null">
                order_type,
            </if>
            <if test="moneyRecharge != null">
                money_recharge,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createDate != null">
                create_date
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">
                #{activityId},
            </if>
            <if test="orderId != null">
                #{orderId},
            </if>
            <if test="memberId != null">
                #{memberId},
            </if>
            <if test="orderType != null">
                #{orderType},
            </if>
            <if test="moneyRecharge != null">
                #{moneyRecharge},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="createDate != null">
                #{createDate}
            </if>
        </trim>
    </insert>

    <!-- 批量新增 -->
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastActivityPayPO">
        insert into fast_promote_activity_pay (activity_id, order_id, member_id, order_type, money_recharge,
                                               create_time, create_date) values
        <foreach collection="list" item="item" separator=",">
            (#{item.activityId}, #{item.orderId}, #{item.memberId}, #{item.orderType}, #{item.moneyRecharge},
             #{item.createTime}, #{item.createDate})
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="FastActivityPayPO">
        update fast_promote_activity_pay
        <set>
            <if test="activityId != null">
                activity_id = #{activityId},
            </if>
            <if test="orderId != null">
                order_id = #{orderId},
            </if>
            <if test="memberId != null">
                member_id = #{memberId},
            </if>
            <if test="state != null">
                `state` = #{state},
            </if>
            <if test="orderType != null">
                order_type = #{orderType},
            </if>
            <if test="moneyRecharge != null">
                money_recharge = #{moneyRecharge},
            </if>
        </set>
        where id = #{id}
    </update>

    <!-- 更新 -->
    <update id="updateByOrderId" parameterType="FastActivityPayPO">
        update fast_promote_activity_pay
        <set>
            state = #{state}
        </set>
        where order_id = #{orderId}
    </update>
</mapper>
