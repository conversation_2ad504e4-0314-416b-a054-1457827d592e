<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastMemberLinkHisMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMemberLinkHis_columns">
		select 
			t.`id`,
			t.`member_id`,
			t.`mini_id`,
			t.`official_id`,
			t.`retail_id`,
			t.`link_id`,
			t.`create_time`
	</sql>

	<!-- 通过id查询单个对象 -->
	<select id="queryById" resultType="FastMemberLinkHisPO">
		<include refid="FastMemberLinkHis_columns" />
		from fast_member_link_his t
		<where>
			t.`id` = #{id}
		</where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMemberLinkHisPO" resultType="FastMemberLinkHisPO">
		<include refid="FastMemberLinkHis_columns" />
		from fast_member_link_his t
		<where>
			<include refid="whereSQL" />
		</where>
		limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMemberLinkHisPO" resultType="FastMemberLinkHisPO">
		select 
			t.`id`,
			t.`member_id`,
			t.`mini_id`,
			t.`official_id`,
			t.`retail_id`,
			t.`link_id`,
			t.link_id_org,
			t.color_in,
			t.`create_time`,
			r.retail_name,
			l.link_name,
			m.mini_name,
			m.type miniType
		from fast_member_link_his t
		left join fast_retail r on r.id = t.retail_id
		left join fast_link l on l.id = t.link_id_org
		left join fast_mini m on m.id = t.mini_id
		<where>
			<if test="memberId != null">
				and t.`member_id` = #{memberId}
			</if>
			<if test="miniId != null">
				and t.`mini_id` = #{miniId}
			</if>
			<if test="officialId != null">
				and t.`official_id` = #{officialId}
			</if>
			<if test="retailId != null">
				and t.`retail_id` = #{retailId}
			</if>
			<if test="linkId != null">
				and t.`link_id` = #{linkId}
			</if>
			<if test="colorTimeStart != null">
				and t.`create_time` &gt;= #{colorTimeStart}
			</if>
			<if test="colorTimeEnd != null">
				and t.`create_time` &lt;= #{colorTimeEnd}
			</if>
			<if test="contentType != null">
				and m.content_type = #{contentType}
			</if>
		</where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMemberLinkHisPO" resultType="int">
		select count(*)
		from fast_member_link_his t
		<where>
			<include refid="whereSQL" />
		</where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="memberId != null">
			and t.`member_id` = #{memberId}
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="officialId != null">
			and t.`official_id` = #{officialId}
		</if>
		<if test="retailId != null">
			and t.`retail_id` = #{retailId}
		</if>
		<if test="linkId != null">
			and t.`link_id` = #{linkId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMemberLinkHisPO">
		insert into fast_member_link_his
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="memberId != null">member_id,</if>
			<if test="miniId != null">mini_id,</if>
			<if test="officialId != null">official_id,</if>
			<if test="retailId != null">retail_id,</if>
			<if test="linkId != null">link_id,</if>
			<if test="linkIdOrg != null">link_id_org,</if>
			<if test="colorIn != null">color_in,</if>
			<if test="createTime != null">create_time</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="memberId != null">#{memberId},</if>
			<if test="miniId != null">#{miniId},</if>
			<if test="officialId != null">#{officialId},</if>
			<if test="retailId != null">#{retailId},</if>
			<if test="linkId != null">#{linkId},</if>
			<if test="linkIdOrg != null">#{linkIdOrg},</if>
			<if test="colorIn != null">#{colorIn},</if>
			<if test="createTime != null">#{createTime}</if>
		</trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastMemberLinkHisPO">
		insert into fast_member_link_his (
		member_id, mini_id, official_id, retail_id, link_id, create_time
		) values
		<foreach collection="list" item="item" separator=",">
			(
			#{item.memberId}, #{item.miniId}, #{item.officialId}, #{item.retailId}, #{item.linkId}, #{item.createTime}
			)
		</foreach>
	</insert>

	<!-- 更新 -->
	<update id="updateById" parameterType="FastMemberLinkHisPO">
		update fast_member_link_his
		<set>
			<if test="memberId != null" >
				member_id = #{memberId},
			</if>
			<if test="miniId != null" >
				mini_id = #{miniId},
			</if>
			<if test="officialId != null" >
				official_id = #{officialId},
			</if>
			<if test="retailId != null" >
				retail_id = #{retailId},
			</if>
			<if test="linkId != null" >
				link_id = #{linkId},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime},
			</if>
		</set>
		where id = #{id}
	</update>

</mapper>
