/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.po.promote.FastMemberLinkHisTablestorePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMemberLinkHisTablestoreMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastMemberLinkHisTablestorePO
     * @return
     */
    FastMemberLinkHisTablestorePO queryById(FastMemberLinkHisTablestorePO fastMemberLinkHisTablestorePO);

    /**
     * 通过id查询单个对象
     *
     * @param id
     * @return
     */
    FastMemberLinkHisTablestorePO queryById(@Param("id") Integer id);

    /**
     * 通过条件查询单个对象
     *
     * @param fastMemberLinkHisTablestorePO
     * @return
     */
    FastMemberLinkHisTablestorePO queryOne(FastMemberLinkHisTablestorePO fastMemberLinkHisTablestorePO);

    /**
     * 查询全部
     *
     * @param fastMemberLinkHisTablestorePO
     * @return
     */
    List<FastMemberLinkHisTablestorePO> queryList(FastMemberLinkHisTablestorePO fastMemberLinkHisTablestorePO);

    /**
     * 查询总数
     *
     * @param fastMemberLinkHisTablestorePO
     * @return
     */
    int queryCount(FastMemberLinkHisTablestorePO fastMemberLinkHisTablestorePO);

    /**
     * 可选新增
     *
     * @param fastMemberLinkHisTablestorePO
     * @return
     */
    int insertSelective(FastMemberLinkHisTablestorePO fastMemberLinkHisTablestorePO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastMemberLinkHisTablestorePO> list);

    /**
     * 更新
     *
     * @param fastMemberLinkHisTablestorePO
     * @return
     */
    int updateById(FastMemberLinkHisTablestorePO fastMemberLinkHisTablestorePO);

}
