<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastActivityMapper">
    <!-- 用于select查询公用抽取的列 -->
    <sql id="FastPromoteActivity_columns">
        select t.`id`, t.`del_flag`, t.`state`, t.`retail_id`, t.`official_id`, t.`title`, t.`content`, t.`start_time`,
               t.`end_time`, t.`recharge_type`, t.`model_detail_id`, t.`limit_type`, t.`limit_num`, t.`template_id`,
               t.`recharge_money`, t.`recharge_count`, t.`creator_id`, t.`updator_id`, t.`create_time`, t.`update_time`
    </sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" parameterType="FastActivityPO" resultType="FastActivityPO">
        <include refid="FastPromoteActivity_columns"/>
            ,d.money_recharge moneyRecharge
        from fast_promote_activity t
        left join fast_fee_model_detail d on d.id=t.model_detail_id
        <where>
            t.`id` = #{id}
        </where>
    </select>

    <!--通过条件查询单个对象  -->
    <select id="queryOne" parameterType="FastActivityPO" resultType="FastActivityPO">
        <include refid="FastPromoteActivity_columns"/>
        from fast_promote_activity t
        <where>
            <include refid="whereSQL"/>
        </where>
        limit 1
    </select>

    <!--分页查询集合  -->
    <select id="queryList" parameterType="FastActivityPO" resultType="FastActivityPO">
        <include refid="FastPromoteActivity_columns"/>
            ,d.money_recharge moneyRecharge
        from fast_promote_activity t
        left join fast_fee_model_detail d on d.id=t.model_detail_id
        <where>
            <include refid="whereSQL"/>
        </where>
        order by t.`id` desc
    </select>

    <!--查询总数  -->
    <select id="queryCount" parameterType="FastActivityPO" resultType="int">
        select count(*)
        from fast_promote_activity t
        <where>
            <include refid="whereSQL"/>
        </where>
    </select>

    <!-- 公共条件  -->
    <sql id="whereSQL">
        <if test="id != null">
            and t.`id` = #{id}
        </if>
        <if test="delFlag != null">
            and t.`del_flag` = #{delFlag}
        </if>
        <if test="state != null">
            and t.`state` = #{state}
        </if>
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="officialId != null">
            and t.`official_id` = #{officialId}
        </if>
        <if test="title != null">
            and t.`title` = #{title}
        </if>
        <if test="titleLike != null and titleLike.length()>0">
            and t.`title` LIKE concat('%',#{titleLike},'%')
        </if>
        <if test="content != null">
            and t.`content` = #{content}
        </if>
        <if test="startTime != null">
            and t.`start_time` = #{startTime}
        </if>
        <if test="endTime != null">
            and t.`end_time` = #{endTime}
        </if>
        <if test="rechargeType != null">
            and t.`recharge_type` = #{rechargeType}
        </if>
        <if test="modelDetailId != null">
            and t.`model_detail_id` = #{modelDetailId}
        </if>
        <if test="limitType != null">
            and t.`limit_type` = #{limitType}
        </if>
        <if test="limitNum != null">
            and t.`limit_num` = #{limitNum}
        </if>
        <if test="templateId != null">
            and t.`template_id` = #{templateId}
        </if>
        <if test="rechargeMoney != null">
            and t.`recharge_money` = #{rechargeMoney}
        </if>
        <if test="rechargeCount != null">
            and t.`recharge_count` = #{rechargeCount}
        </if>
        <if test="creatorId != null">
            and t.`creator_id` = #{creatorId}
        </if>
        <if test="updatorId != null">
            and t.`updator_id` = #{updatorId}
        </if>
        <if test="createTime != null">
            and t.`create_time` = #{createTime}
        </if>
        <if test="updateTime != null">
            and t.`update_time` = #{updateTime}
        </if>
        <if test="activityState != null and activityState == 1">
            and t.`start_time` &gt; now()
        </if>
        <if test="activityState != null and activityState == 2">
            and t.`start_time` &lt;= now() and t.`end_time` &gt;= now()
        </if>
        <if test="activityState != null and activityState == 3">
            and t.`end_time` &lt; now()
        </if>
    </sql>

    <!-- 可选新增 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastActivityPO">
        insert into fast_promote_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="retailId != null">
                retail_id,
            </if>
            <if test="officialId != null">
                official_id,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="rechargeType != null">
                recharge_type,
            </if>
            <if test="modelDetailId != null">
                model_detail_id,
            </if>
            <if test="limitType != null">
                limit_type,
            </if>
            <if test="limitNum != null">
                limit_num,
            </if>
            <if test="templateId != null">
                template_id,
            </if>
            <if test="rechargeMoney != null">
                recharge_money,
            </if>
            <if test="rechargeCount != null">
                recharge_count,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="updatorId != null">
                updator_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="state != null">
                #{state},
            </if>
            <if test="retailId != null">
                #{retailId},
            </if>
            <if test="officialId != null">
                #{officialId},
            </if>
            <if test="title != null">
                #{title},
            </if>
            <if test="content != null">
                #{content},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="rechargeType != null">
                #{rechargeType},
            </if>
            <if test="modelDetailId != null">
                #{modelDetailId},
            </if>
            <if test="limitType != null">
                #{limitType},
            </if>
            <if test="limitNum != null">
                #{limitNum},
            </if>
            <if test="templateId != null">
                #{templateId},
            </if>
            <if test="rechargeMoney != null">
                #{rechargeMoney},
            </if>
            <if test="rechargeCount != null">
                #{rechargeCount},
            </if>
            <if test="creatorId != null">
                #{creatorId},
            </if>
            <if test="updatorId != null">
                #{updatorId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="FastActivityPO">
        update fast_promote_activity
        <set>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="state != null">
                `state` = #{state},
            </if>
            <if test="retailId != null">
                retail_id = #{retailId},
            </if>
            <if test="officialId != null">
                official_id = #{officialId},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="rechargeType != null">
                recharge_type = #{rechargeType},
            </if>
            <if test="modelDetailId != null">
                model_detail_id = #{modelDetailId},
            </if>
            <if test="limitType != null">
                limit_type = #{limitType},
            </if>
            <if test="limitNum != null">
                limit_num = #{limitNum},
            </if>
            <if test="templateId != null">
                template_id = #{templateId},
            </if>
            <if test="rechargeMoney != null">
                recharge_money = #{rechargeMoney},
            </if>
            <if test="rechargeCount != null">
                recharge_count = #{rechargeCount},
            </if>
            <if test="updatorId != null">
                updator_id = #{updatorId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!-- 更新 -->
    <update id="updatePlusById" parameterType="FastActivityPO">
        update fast_promote_activity
        <set>
            <if test="rechargeMoney != null">
                recharge_money = recharge_money + #{rechargeMoney},
            </if>
            <if test="rechargeCount != null">
                recharge_count = recharge_count + #{rechargeCount},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
