/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.po.promote.FastLinkRoadPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastLinkRoadMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastLinkRoadPO
     * @return
     */
    FastLinkRoadPO queryById(FastLinkRoadPO fastLinkRoadPO);

    /**
     * 通过id查询单个对象
     *
     * @param id
     * @return
     */
    FastLinkRoadPO queryById(@Param("id") Integer id);

    /**
     * 通过条件查询单个对象
     *
     * @param fastLinkRoadPO
     * @return
     */
    FastLinkRoadPO queryOne(FastLinkRoadPO fastLinkRoadPO);

    /**
     * 查询全部
     *
     * @param fastLinkRoadPO
     * @return
     */
    List<FastLinkRoadPO> queryList(FastLinkRoadPO fastLinkRoadPO);

    /**
     * 查询全部
     *
     * @param fastLinkRoadPO
     * @return
     */
    List<FastLinkRoadPO> queryLinkRoadName(FastLinkRoadPO fastLinkRoadPO);

    /**
     * 查询总数
     *
     * @param fastLinkRoadPO
     * @return
     */
    int queryCount(FastLinkRoadPO fastLinkRoadPO);

    /**
     * 可选新增
     *
     * @param fastLinkRoadPO
     * @return
     */
    int insertSelective(FastLinkRoadPO fastLinkRoadPO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastLinkRoadPO> list);

    /**
     * 更新
     *
     * @param fastLinkRoadPO
     * @return
     */
    int updateById(FastLinkRoadPO fastLinkRoadPO);

}
