/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.po.promote.FastMiniLinkPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMiniLinkMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastMiniLinkPO
     * @return
     */
    FastMiniLinkPO queryById(FastMiniLinkPO fastMiniLinkPO);

    /**
     * 通过条件查询单个对象
     *
     * @param fastMiniLinkPO
     * @return
     */
    FastMiniLinkPO queryOne(FastMiniLinkPO fastMiniLinkPO);

    /**
     * 查询全部
     *
     * @param fastMiniLinkPO
     * @return
     */
    List<FastMiniLinkPO> queryList(FastMiniLinkPO fastMiniLinkPO);

    /**
     * 查询总数
     *
     * @param fastMiniLinkPO
     * @return
     */
    int queryCount(FastMiniLinkPO fastMiniLinkPO);

    /**
     * 新增
     *
     * @param fastMiniLinkPO
     * @return
     */
    int insert(FastMiniLinkPO fastMiniLinkPO);

    /**
     * 可选新增
     *
     * @param fastMiniLinkPO
     * @return
     */
    int insertSelective(FastMiniLinkPO fastMiniLinkPO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastMiniLinkPO> list);

    /**
     * 更新
     *
     * @param fastMiniLinkPO
     * @return
     */
    int updateById(FastMiniLinkPO fastMiniLinkPO);

}
