<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastLinkRoadMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastLinkRoad_columns">
		select 
			t.`id`,
			t.`content_type`,
			t.`road_name`,
			t.`app_type`,
			t.`media_type`,
			t.`adv_mode`,
			t.`fans_type`,
			t.fee_flag,
			t.`create_time`,
			t.`update_time`,
			t.`creator_id`,
			t.`updator_id`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastLinkRoadPO">
		<include refid="FastLinkRoad_columns" />
	    from fast_link_road t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastLinkRoadPO" resultType="FastLinkRoadPO">
		<include refid="FastLinkRoad_columns" />
	    from fast_link_road t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastLinkRoadPO" resultType="FastLinkRoadPO">
		<include refid="FastLinkRoad_columns" />
	    from fast_link_road t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--分页查询集合  -->
	<select id="queryLinkRoadName" parameterType="FastLinkRoadPO" resultType="FastLinkRoadPO">
		select t.id, t.road_name
		from fast_link_road t
		<where>
			<include refid="whereSQL"/>
		</where>
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastLinkRoadPO" resultType="int">
		select count(*)
	    from fast_link_road t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="ids != null and ids.length()>0">
			and t.`id` in (${ids})
		</if>
		<if test="contentType != null">
			and t.`content_type` = #{contentType}
		</if>
		<if test="roadName != null">
			and t.`road_name` = #{roadName}
		</if>
		<if test="appType != null">
			and t.`app_type` = #{appType}
		</if>
		<if test="mediaType != null">
			and t.`media_type` = #{mediaType}
		</if>
		<if test="advMode != null">
			and t.`adv_mode` = #{advMode}
		</if>
		<if test="fansType != null">
			and t.`fans_type` = #{fansType}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="feeFlag != null">
			and t.`fee_flag` = #{feeFlag}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastLinkRoadPO">
        insert into fast_link_road
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="contentType != null">content_type,</if>
	        <if test="roadName != null">road_name,</if>
	        <if test="appType != null">app_type,</if>
	        <if test="mediaType != null">media_type,</if>
	        <if test="advMode != null">adv_mode,</if>
	        <if test="fansType != null">fans_type,</if>
	        <if test="feeFlag != null">fee_flag,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="contentType != null">#{contentType},</if>
	        <if test="roadName != null">#{roadName},</if>
	        <if test="appType != null">#{appType},</if>
	        <if test="mediaType != null">#{mediaType},</if>
	        <if test="advMode != null">#{advMode},</if>
	        <if test="fansType != null">#{fansType},</if>
	        <if test="feeFlag != null">#{feeFlag},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastLinkRoadPO">
        insert into fast_link_road (
         content_type, road_name, app_type, media_type, adv_mode, fans_type, create_time, update_time, creator_id, updator_id
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.contentType}, #{item.roadName}, #{item.appType}, #{item.mediaType}, #{item.advMode}, #{item.fansType}, #{item.createTime}, #{item.updateTime}, #{item.creatorId}, #{item.updatorId}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastLinkRoadPO">
        update fast_link_road
        <set>
         	<if test="contentType != null" >
               content_type = #{contentType},
            </if>
         	<if test="roadName != null" >
               road_name = #{roadName},
            </if>
         	<if test="appType != null" >
               app_type = #{appType},
            </if>
         	<if test="mediaType != null" >
               media_type = #{mediaType},
            </if>
         	<if test="advMode != null" >
               adv_mode = #{advMode},
            </if>
         	<if test="fansType != null" >
               fans_type = #{fansType},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
