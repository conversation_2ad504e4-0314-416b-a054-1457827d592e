/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.po.promote.FastMemberLinkHisPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMemberLinkHisMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastMemberLinkHisPO
     * @return
     */
    FastMemberLinkHisPO queryById(FastMemberLinkHisPO fastMemberLinkHisPO);

    /**
     * 通过条件查询单个对象
     *
     * @param fastMemberLinkHisPO
     * @return
     */
    FastMemberLinkHisPO queryOne(FastMemberLinkHisPO fastMemberLinkHisPO);

    /**
     * 查询全部
     *
     * @param fastMemberLinkHisPO
     * @return
     */
    List<FastMemberLinkHisPO> queryList(FastMemberLinkHisPO fastMemberLinkHisPO);

    /**
     * 查询总数
     *
     * @param fastMemberLinkHisPO
     * @return
     */
    int queryCount(FastMemberLinkHisPO fastMemberLinkHisPO);

    /**
     * 新增
     *
     * @param fastMemberLinkHisPO
     * @return
     */
    int insert(FastMemberLinkHisPO fastMemberLinkHisPO);

    /**
     * 可选新增
     *
     * @param fastMemberLinkHisPO
     * @return
     */
    int insertSelective(FastMemberLinkHisPO fastMemberLinkHisPO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastMemberLinkHisPO> list);

    /**
     * 更新
     *
     * @param fastMemberLinkHisPO
     * @return
     */
    int updateById(FastMemberLinkHisPO fastMemberLinkHisPO);


}
