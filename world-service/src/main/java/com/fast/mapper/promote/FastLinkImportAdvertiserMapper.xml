<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastLinkImportAdvertiserMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastLinkImportAdvertiser_columns">
		select t.`id`,t.`batch_import_id`,t.`account_id`,t.`create_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastLinkImportAdvertiserPO">
		<include refid="FastLinkImportAdvertiser_columns" />
	    from fast_link_import_advertiser t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastLinkImportAdvertiserPO" resultType="FastLinkImportAdvertiserPO">
		<include refid="FastLinkImportAdvertiser_columns" />
	    from fast_link_import_advertiser t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastLinkImportAdvertiserPO" resultType="FastLinkImportAdvertiserPO">
		<include refid="FastLinkImportAdvertiser_columns" />
	    from fast_link_import_advertiser t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastLinkImportAdvertiserPO" resultType="int">
		select count(*)
	    from fast_link_import_advertiser t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="batchImportId != null">
			and t.`batch_import_id` = #{batchImportId}
		</if>
		<if test="accountId != null">
			and t.`account_id` = #{accountId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastLinkImportAdvertiserPO">
        insert into fast_link_import_advertiser
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="batchImportId != null">batch_import_id,</if>
	        <if test="accountId != null">account_id,</if>
	        <if test="createTime != null">create_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="batchImportId != null">#{batchImportId},</if>
	        <if test="accountId != null">#{accountId},</if>
	        <if test="createTime != null">#{createTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastLinkImportAdvertiserPO">
        insert into fast_link_import_advertiser (
         batch_import_id, account_id, create_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.batchImportId}, #{item.accountId}, #{item.createTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastLinkImportAdvertiserPO">
        update fast_link_import_advertiser
        <set>
         	<if test="batchImportId != null" >
               batch_import_id = #{batchImportId},
            </if>
         	<if test="accountId != null" >
               account_id = #{accountId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
