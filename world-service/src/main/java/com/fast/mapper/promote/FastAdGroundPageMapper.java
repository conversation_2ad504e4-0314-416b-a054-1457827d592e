/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.po.promote.FastAdGroundPagePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastAdGroundPageMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastAdGroundPagePO
     * @return
     */
    FastAdGroundPagePO queryById(FastAdGroundPagePO fastAdGroundPagePO);

    /**
     * 通过id查询单个对象
     *
     * @param id
     * @return
     */
    FastAdGroundPagePO queryById(@Param("id") Integer id);

    /**
     * 通过条件查询单个对象
     *
     * @param fastAdGroundPagePO
     * @return
     */
    FastAdGroundPagePO queryOne(FastAdGroundPagePO fastAdGroundPagePO);

    /**
     * 查询全部
     *
     * @param fastAdGroundPagePO
     * @return
     */
    List<FastAdGroundPagePO> queryList(FastAdGroundPagePO fastAdGroundPagePO);

    /**
     * 查询总数
     *
     * @param fastAdGroundPagePO
     * @return
     */
    int queryCount(FastAdGroundPagePO fastAdGroundPagePO);

    /**
     * 可选新增
     *
     * @param fastAdGroundPagePO
     * @return
     */
    int insertSelective(FastAdGroundPagePO fastAdGroundPagePO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastAdGroundPagePO> list);

    /**
     * 更新
     *
     * @param fastAdGroundPagePO
     * @return
     */
    int updateById(FastAdGroundPagePO fastAdGroundPagePO);

    /**
     * 更新
     *
     * @param fastAdGroundPagePO
     * @return
     */
    int deleteByAdGroundId(FastAdGroundPagePO fastAdGroundPagePO);

}
