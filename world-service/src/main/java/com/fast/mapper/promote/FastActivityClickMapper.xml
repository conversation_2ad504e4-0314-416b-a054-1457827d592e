<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastActivityClickMapper">
    <!-- 用于select查询公用抽取的列 -->
    <sql id="FastPromoteActivityClick_columns">
        select t.`id`, t.`activity_id`, t.`member_id`, t.`create_time`, t.`create_date`
    </sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastActivityClickPO">
        <include refid="FastPromoteActivityClick_columns"/>
        from fast_promote_activity_click t
        <where>
            t.`id` = #{id}
        </where>
    </select>

    <!--通过条件查询单个对象  -->
    <select id="queryOne" parameterType="FastActivityClickPO" resultType="FastActivityClickPO">
        <include refid="FastPromoteActivityClick_columns"/>
        from fast_promote_activity_click t
        <where>
            <include refid="whereSQL"/>
        </where>
        limit 1
    </select>

    <!--分页查询集合  -->
    <select id="queryList" parameterType="FastActivityClickPO" resultType="FastActivityClickPO">
        <include refid="FastPromoteActivityClick_columns"/>
        from fast_promote_activity_click t
        <where>
            <include refid="whereSQL"/>
        </where>
        order by t.`id` desc
    </select>

    <!--分页查询集合  -->
    <select id="queryListGroupDate" parameterType="FastActivityClickPO" resultType="FastActivityClickPO">
        select t.create_date, t.`activity_id`, count(DISTINCT(t.`member_id`)) clickCount
        from fast_promote_activity_click t
        <where>
            <include refid="whereSQL"/>
        </where>
        group by t.`create_date`
        order by t.`create_date` desc
    </select>

    <!--分页查询集合  -->
    <select id="queryListGroupMember" parameterType="FastActivityClickPO" resultType="FastActivityClickPO">
        select t1.`activity_id`, count(*) clickCount from (
            select t.`activity_id`, t.`member_id`
            from fast_promote_activity_click t
            <where>
                <include refid="whereSQL"/>
            </where>
            group by t.`member_id`
        ) t1
    </select>

    <!--分页查询集合  -->
    <select id="queryMemberIds" parameterType="FastActivityClickPO" resultType="Long">
        select t.`member_id`
        from fast_promote_activity_click t
        <where>
            <include refid="whereSQL"/>
        </where>
    </select>

    <!--查询总数  -->
    <select id="queryCount" parameterType="FastActivityClickPO" resultType="int">
        select count(*)
        from fast_promote_activity_click t
        <where>
            <include refid="whereSQL"/>
        </where>
    </select>

    <!-- 公共条件  -->
    <sql id="whereSQL">
        <if test="id != null">
            and t.`id` = #{id}
        </if>
        <if test="activityId != null">
            and t.`activity_id` = #{activityId}
        </if>
        <if test="memberId != null">
            and t.`member_id` = #{memberId}
        </if>
        <if test="createTime != null">
            and t.`create_time` = #{createTime}
        </if>
        <if test="createDate != null">
            and t.`create_date` = #{createDate}
        </if>
        <if test="activityTimeS != null">
            and t.`create_date` &gt;= #{activityTimeS}
        </if>
        <if test="activityTimeE != null">
            and t.`create_date` &lt;= #{activityTimeE}
        </if>
    </sql>

    <!-- 可选新增 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastActivityClickPO">
        insert into fast_promote_activity_click
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">
                activity_id,
            </if>
            <if test="memberId != null">
                member_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createDate != null">
                create_date
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">
                #{activityId},
            </if>
            <if test="memberId != null">
                #{memberId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="createDate != null">
                #{createDate}
            </if>
        </trim>
    </insert>

    <!-- 批量新增 -->
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastActivityClickPO">
        insert into fast_promote_activity_click (activity_id, member_id, create_time, create_date) values
        <foreach collection="list" item="item" separator=",">
            (#{item.activityId}, #{item.memberId}, #{item.createTime}, #{item.createDate})
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="FastActivityClickPO">
        update fast_promote_activity_click
        <set>
            <if test="activityId != null">
                activity_id = #{activityId},
            </if>
            <if test="memberId != null">
                member_id = #{memberId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
