/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.annotation.Slave;
import com.fast.po.promote.FastStatisLinkFreeHourPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStatisLinkFreeHourMapper {

    // 通过id查询单个对象
    FastStatisLinkFreeHourPO queryById(FastStatisLinkFreeHourPO entity);

    // 通过id查询单个对象
    FastStatisLinkFreeHourPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStatisLinkFreeHourPO queryOne(FastStatisLinkFreeHourPO entity);

    // 查询全部
    List<FastStatisLinkFreeHourPO> queryList(FastStatisLinkFreeHourPO entity);

    // 查询总数
    int queryCount(FastStatisLinkFreeHourPO entity);

    // 可选新增
    int insertSelective(FastStatisLinkFreeHourPO entity);

    // 批量新增
    int insertBatch(List<FastStatisLinkFreeHourPO> list);

    // 更新
    int updateById(FastStatisLinkFreeHourPO entity);

    int insertUpdateBatch(List<FastStatisLinkFreeHourPO> list);

    // 免费注册用户数统计
    @Slave
    List<FastStatisLinkFreeHourPO> queryStatData(FastStatisLinkFreeHourPO entity);

    // 免费充值数据统计
    @Slave
    List<FastStatisLinkFreeHourPO> queryStatHour(FastStatisLinkFreeHourPO entity);
}
