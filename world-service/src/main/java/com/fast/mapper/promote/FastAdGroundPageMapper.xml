<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastAdGroundPageMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastAdGroundPage_columns">
		select t.`id`,t.`ad_ground_id`,t.`pic_url`,t.`height`,t.`sequence`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastAdGroundPagePO">
		<include refid="FastAdGroundPage_columns" />
	    from fast_ad_ground_page t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastAdGroundPagePO" resultType="FastAdGroundPagePO">
		<include refid="FastAdGroundPage_columns" />
	    from fast_ad_ground_page t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastAdGroundPagePO" resultType="FastAdGroundPagePO">
		<include refid="FastAdGroundPage_columns" />
	    from fast_ad_ground_page t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`sequence`,t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastAdGroundPagePO" resultType="int">
		select count(*)
	    from fast_ad_ground_page t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="adGroundId != null">
			and t.`ad_ground_id` = #{adGroundId}
		</if>
		<if test="picUrl != null">
			and t.`pic_url` = #{picUrl}
		</if>
		<if test="height != null">
			and t.`height` = #{height}
		</if>
		<if test="sequence != null">
			and t.`sequence` = #{sequence}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastAdGroundPagePO">
        insert into fast_ad_ground_page
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="adGroundId != null">ad_ground_id,</if>
	        <if test="picUrl != null">pic_url,</if>
	        <if test="height != null">height,</if>
	        <if test="sequence != null">sequence</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="adGroundId != null">#{adGroundId},</if>
	        <if test="picUrl != null">#{picUrl},</if>
	        <if test="height != null">#{height},</if>
	        <if test="sequence != null">#{sequence}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastAdGroundPagePO">
        insert into fast_ad_ground_page (
         ad_ground_id, pic_url, height, sequence
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.adGroundId}, #{item.picUrl}, #{item.height}, #{item.sequence}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastAdGroundPagePO">
        update fast_ad_ground_page
        <set>
         	<if test="adGroundId != null" >
               ad_ground_id = #{adGroundId},
            </if>
         	<if test="picUrl != null" >
               pic_url = #{picUrl},
            </if>
         	<if test="height != null" >
               height = #{height},
            </if>
         	<if test="sequence != null" >
               `sequence` = #{sequence},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

    <!-- 更新 -->
	<delete id="deleteByAdGroundId" parameterType="FastAdGroundPagePO">
		delete from fast_ad_ground_page
        where ad_ground_id = #{adGroundId}
	</delete>

</mapper>
