<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastMiniLinkMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMiniLink_columns">
		select t.`id`,t.`mini_link_name`,t.`mini_link_url`,t.`content_type`,t.type,t.`sort`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" parameterType="FastMiniLinkPO" resultType="FastMiniLinkPO">
		<include refid="FastMiniLink_columns" />
	    from fast_mini_link t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMiniLinkPO" resultType="FastMiniLinkPO">
		<include refid="FastMiniLink_columns" />
	    from fast_mini_link t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMiniLinkPO" resultType="FastMiniLinkPO">
		<include refid="FastMiniLink_columns" />
	    from fast_mini_link t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMiniLinkPO" resultType="int">
		select count(*)
	    from fast_mini_link t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="miniLinkName != null">
			and t.`mini_link_name` = #{miniLinkName}
		</if>
		<if test="miniLinkUrl != null">
			and t.`mini_link_url` = #{miniLinkUrl}
		</if>
		<if test="contentType != null">
			and t.`content_type` = #{contentType}
		</if>
		<if test="sort != null">
			and t.`sort` = #{sort}
		</if>
		<if test="type != null">
			and t.`type` = #{type}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 新增 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniLinkPO">
        insert into fast_mini_link (
         mini_link_name, mini_link_url, sort, create_time, update_time
        ) values (
         #{miniLinkName}, #{miniLinkUrl}, #{sort}, #{createTime}, #{updateTime}
        )
	</insert>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniLinkPO">
        insert into fast_mini_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="miniLinkName != null">mini_link_name,</if>
	        <if test="miniLinkUrl != null">mini_link_url,</if>
	        <if test="contentType != null">content_type,</if>
	        <if test="sort != null">sort,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="miniLinkName != null">#{miniLinkName},</if>
	        <if test="miniLinkUrl != null">#{miniLinkUrl},</if>
	        <if test="contentType != null">#{contentType},</if>
	        <if test="sort != null">#{sort},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniLinkPO">
        insert into fast_mini_link (
         mini_link_name, mini_link_url, sort, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.miniLinkName}, #{item.miniLinkUrl}, #{item.sort}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastMiniLinkPO">
        update fast_mini_link
        <set>
         	<if test="miniLinkName != null" >
               mini_link_name = #{miniLinkName},
            </if>
         	<if test="miniLinkUrl != null" >
               mini_link_url = #{miniLinkUrl},
            </if>
         	<if test="contentType != null" >
				content_type = #{contentType},
            </if>
         	<if test="sort != null" >
               sort = #{sort},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
