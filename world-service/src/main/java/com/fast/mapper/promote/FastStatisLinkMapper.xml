<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastStatisLinkMapper">
	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStatisLink_columns">
		select t.`id`, t.`retail_id`, t.`link_id`, t.`statis_date`, t.`num_day`, t.`num_day_rech`, t.`num_day_order`,
			   t.`num_all_day`, t.`num_all_day_rech`, t.`num_all_day_order`, t.`num_all`, t.`num_all_rech`,
			   t.`num_all_order`, t.`money_day`, t.`money_day_common`, t.`money_day_vip`, t.`money_all_day`,
			   t.`money_all_day_common`, t.`money_all_day_vip`, t.`money_all`, t.`money_all_common`, t.`money_all_vip`,
			   t.`money_profit_day`, t.`money_profit_day_common`, t.`money_profit_day_vip`, t.`money_profit_all_day`,
			   t.`money_profit_all_day_common`, t.`money_profit_all_day_vip`, t.`money_profit_all`,
			   t.`money_profit_all_common`, t.`money_profit_all_vip`, t.`cost_day`, t.`cost_day_plus`, t.`cost_all`,
			   t.`num_d60_rech`, t.`money_d60_rech`, t.`money_profit_d60_rech`, t.`money_d60_cost`,
			   t.`money_d60_cost_plus`, t.`create_time`, t.`update_time`, t.`num_all_day_common_rech`,
			   t.`num_all_day_common_order`, t.`num_all_day_vip_rech`, t.`num_all_day_vip_order`, t.`num_dAll_rech`,
			   t.`money_dAll_rech`, t.`money_profit_dAll_rech`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" parameterType="FastStatisLinkPO" resultType="FastStatisLinkPO">
		<include refid="FastStatisLink_columns" />
	    from fast_statis_link t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStatisLinkPO" resultType="FastStatisLinkPO">
		<include refid="FastStatisLink_columns" />
	    from fast_statis_link t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--通过条件查询单个对象  -->
	<select id="querySumByDayDramaId" parameterType="FastStatisLinkPO" resultType="FastStatisLinkPO">
		select sum(t.cost_day) costDay
	    from fast_statis_link t
		left join fast_link l on l.id = t.link_id
        <where>
        	<include refid="whereSQL" />
			<if test="dramaId != null ">
				and l.`drama_id` = #{dramaId}
			</if>
			<if test="retailId != null">
				and l.`retail_id` = #{retailId}
			</if>
			<if test="retailIds != null and retailIds.length()>0">
				and l.`retail_id` in (${retailIds})
			</if>
        </where>
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStatisLinkPO" resultType="FastStatisLinkPO">
		<include refid="FastStatisLink_columns" />
	    from fast_statis_link t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--分页查询集合  -->
	<select id="queryListCostPlus" parameterType="FastStatisLinkPO" resultType="FastStatisLinkPO">
		select id,link_id,statis_date,cost_day_plus,money_d60_cost_plus,money_d60_cost
	    from fast_statis_link
        where link_id=#{linkId}
          and statis_date <![CDATA[ <= ]]> #{statisDate}
          and statis_date <![CDATA[ >= ]]> '2023-01-01'
		order by `statis_date` desc limit 61
	</select>

	<!--查询-推广数据详情页-每日回收数据列表  -->
	<select id="queryDayRoiList" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		<include refid="FastStatisLink_columns" />
	    from fast_statis_link t
        <where>
        	<include refid="whereDayRoiListSQL" />
        </where>
		order by t.`statis_date` desc
	</select>

	<!-- 查询条件-推广数据详情页-每日回收数据列表  -->
	<sql id="whereDayRoiListSQL">
		<if test="linkId != null">
			and t.`link_id` = #{linkId}
		</if>
		<if test="isAll != null">
			and num_day <![CDATA[ >= ]]> #{isAll}
		</if>
		<if test="beginTime != null">
			and t.`statis_date` <![CDATA[ >= ]]> #{beginTime}
		</if>
		<if test="endTime != null">
			and t.`statis_date` <![CDATA[ <= ]]> #{endTime}
		</if>
	</sql>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStatisLinkPO" resultType="int">
		select count(*)
	    from fast_statis_link t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="linkId != null">
			and t.`link_id` = #{linkId}
		</if>
		<if test="statisDate != null">
			and t.`statis_date` = #{statisDate}
		</if>
		<if test="numDay != null">
			and t.`num_day` = #{numDay}
		</if>
		<if test="numDayRech != null">
			and t.`num_day_rech` = #{numDayRech}
		</if>
		<if test="numDayOrder != null">
			and t.`num_day_order` = #{numDayOrder}
		</if>
		<if test="numAllDay != null">
			and t.`num_all_day` = #{numAllDay}
		</if>
		<if test="numAllDayRech != null">
			and t.`num_all_day_rech` = #{numAllDayRech}
		</if>
		<if test="numAllDayOrder != null">
			and t.`num_all_day_order` = #{numAllDayOrder}
		</if>
		<if test="numAll != null">
			and t.`num_all` = #{numAll}
		</if>
		<if test="numAllRech != null">
			and t.`num_all_rech` = #{numAllRech}
		</if>
		<if test="numAllOrder != null">
			and t.`num_all_order` = #{numAllOrder}
		</if>
		<if test="moneyDay != null">
			and t.`money_day` = #{moneyDay}
		</if>
		<if test="moneyDayCommon != null">
			and t.`money_day_common` = #{moneyDayCommon}
		</if>
		<if test="moneyDayVip != null">
			and t.`money_day_vip` = #{moneyDayVip}
		</if>
		<if test="moneyAllDay != null">
			and t.`money_all_day` = #{moneyAllDay}
		</if>
		<if test="moneyAllDayCommon != null">
			and t.`money_all_day_common` = #{moneyAllDayCommon}
		</if>
		<if test="moneyAllDayVip != null">
			and t.`money_all_day_vip` = #{moneyAllDayVip}
		</if>
		<if test="moneyAll != null">
			and t.`money_all` = #{moneyAll}
		</if>
		<if test="moneyAllCommon != null">
			and t.`money_all_common` = #{moneyAllCommon}
		</if>
		<if test="moneyAllVip != null">
			and t.`money_all_vip` = #{moneyAllVip}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
	</sql>

	<!--查询当天新增用户数  -->
	<select id="queryNumDayPlus" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="int">
		select count(DISTINCT(t.member_id))
		from fast_member_link t
		<where>
			(
				(t.`first_link_id` = #{lastLinkId}
			    and t.`first_link_time` &gt;= concat( #{statisDate},' 00:00:00')
			    and t.`first_link_time` &lt;= concat( #{statisDate},' 23:59:59'))
			or
				(t.`last_link_id` = #{lastLinkId}
				and t.`last_link_time` &gt;= concat( #{statisDate},' 00:00:00')
				and t.`last_link_time` &lt;= concat( #{statisDate},' 23:59:59'))
			)
		</where>
	</select>

	<!--查询当天新增用户数  -->
	<select id="querySubLinkNumDayPlus" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="int">
		select count(DISTINCT(t.id))
		from fast_member_sub_link t
		<where>
			t.`sub_link_id` = #{lastLinkId}
			and t.`sub_link_time` &gt;= concat( #{statisDate},' 00:00:00')
			and t.`sub_link_time` &lt;= concat( #{statisDate},' 23:59:59')
		</where>
	</select>

	<select id="queryNumDay" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="int">
		select count(DISTINCT(t.member_id))
		from fast_member_link t  LEFT JOIN fast_member_order_recharge f on   f.`link_id` = #{lastLinkId} and t.member_id = f.member_id
		<where>
			<include refid="whereSQLNumDay" />
		</where>
	</select>

	<!-- 查询当天新增用户数-条件  -->
	<sql id="whereSQLNumDay">
		<if test="lastLinkId != null">
			and (
			( t.`first_link_id` = #{lastLinkId} and t.`first_link_time` <![CDATA[ >= ]]> concat( #{statisDate},' 00:00:00') and t.`first_link_time` <![CDATA[ <= ]]> concat('', #{statisDate},' 23:59:59'))
			or
			(t.`last_link_id` = #{lastLinkId} and t.`last_link_time` <![CDATA[ >= ]]> concat( #{statisDate},' 00:00:00') and t.`last_link_time` <![CDATA[ <= ]]> concat('', #{statisDate},' 23:59:59'))
			)
		</if>
	</sql>

	<!--查询当天新增用户数-充值人数、笔数、金额  -->
	<select id="queryNumDayRech" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			count(DISTINCT(f.member_id)) as numDayRech
			,count(*) numDayOrder
			,sum(f.money_recharge) as moneyDay
			,sum(f.money_profit) as moneyProfitDay
		from fast_member_order_recharge f
		INNER JOIN fast_member_link t on t.member_id = f.member_id
		where
			f.`link_id` = #{lastLinkId} and f.state = 1
			and f.`pay_time` &gt;= concat( #{statisDate},' 00:00:00')
			and f.`pay_time` &lt;= concat( #{statisDate},' 23:59:59')
			and
			(
				( t.`first_link_id` = #{lastLinkId}
				  and t.`first_link_time` &gt;= concat( #{statisDate},' 00:00:00')
				  and t.`first_link_time` &lt;= concat('', #{statisDate},' 23:59:59')
				)
				or
				( t.`last_link_id` = #{lastLinkId}
				  and t.`last_link_time` &gt;= concat( #{statisDate},' 00:00:00')
				  and t.`last_link_time` &lt;= concat('', #{statisDate},' 23:59:59')
				 )
				or
				( f.`link_time` &gt;= concat( #{statisDate},' 00:00:00')
				  and f.`link_time` &lt;= concat('', #{statisDate},' 23:59:59')
				 )
			)
	</select>

	<!--查询当天新增用户数-第N天充值  -->
	<select id="queryMoneyD60Rech" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			count(DISTINCT(f.member_id)) as numDayRech,
			count(*) as numDayOrder,
			sum(f.money_recharge) as moneyDay,
			sum(f.money_profit) as moneyProfitDay
		from fast_member_order_recharge f INNER JOIN fast_member_link t on t.member_id = f.member_id
		<where>
			<include refid="whereSQLMoneyD60Rech" />
		</where>
	</select>

	<!-- 查询当天新增用户数-第N天充值-条件  -->
	<sql id="whereSQLMoneyD60Rech">
		<if test="lastLinkId != null">
			and f.`link_id` = #{lastLinkId} and f.state = 1
			and f.`pay_time` <![CDATA[ >= ]]> concat( #{dnDate},' 00:00:00')
			and f.`pay_time` <![CDATA[ <= ]]> concat( #{dnDate},' 23:59:59')
			and (
					( t.`first_link_id` = #{lastLinkId}
					and t.`first_link_time` <![CDATA[ >= ]]> concat( #{statisDate},' 00:00:00')
					and t.`first_link_time` <![CDATA[ <= ]]> concat('', #{statisDate},' 23:59:59'))
				or
					(t.`last_link_id` = #{lastLinkId}
					and t.`last_link_time` <![CDATA[ >= ]]> concat( #{statisDate},' 00:00:00')
					and t.`last_link_time` <![CDATA[ <= ]]> concat('', #{statisDate},' 23:59:59'))
				or
					(f.`link_time` <![CDATA[ >= ]]> concat( #{statisDate},' 00:00:00')
					and f.`link_time` <![CDATA[ <= ]]> concat('', #{statisDate},' 23:59:59'))
			)
		</if>
	</sql>

	<!--查询当天新增用户数-累计充值  -->
	<select id="queryMoneyDAllRech" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			count(DISTINCT(f.member_id)) as numDAllRech,
			sum(f.money_recharge) as moneyDAllRech,
			sum(f.money_profit) as moneyProfitDAllRech
		from fast_member_order_recharge f INNER JOIN fast_member_link t on t.member_id = f.member_id
		<where>
			<include refid="whereSQLMoneyDAllRech" />
		</where>
	</select>

	<!-- 查询当天新增用户数-累计充值-条件  -->
	<sql id="whereSQLMoneyDAllRech">
		<if test="lastLinkId != null">
			and f.`link_id` = #{lastLinkId} and f.state = 1
			and  f.`pay_time` <![CDATA[ >= ]]> concat( #{statisDate},' 00:00:00')
			and (
			( t.`first_link_id` = #{lastLinkId} and t.`first_link_time` <![CDATA[ >= ]]> concat( #{statisDate},' 00:00:00') and t.`first_link_time` <![CDATA[ <= ]]> concat('', #{statisDate},' 23:59:59'))
			or
			(t.`last_link_id` = #{lastLinkId} and t.`last_link_time` <![CDATA[ >= ]]> concat( #{statisDate},' 00:00:00') and t.`last_link_time` <![CDATA[ <= ]]> concat('', #{statisDate},' 23:59:59'))
			or
			(f.`link_time` <![CDATA[ >= ]]> concat( #{statisDate},' 00:00:00') and f.`link_time` <![CDATA[ <= ]]> concat('', #{statisDate},' 23:59:59'))
			)
		</if>
	</sql>


	<!--查询所有新增用户当天-充值人数、笔数、金额 ，普通充值人数、笔数、金额，用户充值人数、笔数、金额 -->
	<select id="queryNumAllDayRechPlus" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			count(DISTINCT (f.member_id)) as numAllDayRech,
			count(*) as numAllDayOrder,
			count(DISTINCT(CASE WHEN (f.order_type = 1) then f.member_id else NULL end)) as numAllDayCommonRech,
			SUM(CASE WHEN (f.order_type = 1) then 1 else 0 end) as numAllDayCommonOrder,
			count(DISTINCT(CASE WHEN (f.order_type = 2) then f.member_id else NULL end)) as numAllDayVipRech,
			SUM(CASE WHEN (f.order_type = 2) then 1 else 0 end) as numAllDayVipOrder,
			sum(f.money_recharge) as moneyAllDay,
			sum(f.money_profit) as moneyProfitAllDay,
			SUM(CASE WHEN (f.order_type = 1) then f.money_recharge else 0 end) as moneyAllDayCommon,
			SUM(CASE WHEN (f.order_type = 1) then f.money_profit else 0 end) as moneyProfitAllDayCommon,
			SUM(CASE WHEN (f.order_type = 2) then f.money_recharge else 0 end) as moneyAllDayVip,
			SUM(CASE WHEN (f.order_type = 2) then f.money_profit else 0 end) as moneyProfitAllDayVip
		from fast_member_order_recharge f
		INNER JOIN fast_member_link t on t.member_id = f.member_id
		where f.`link_id` = #{lastLinkId} and f.state = 1
		and f.`pay_time` &gt;= concat(#{statisDate},' 00:00:00')
		and f.`pay_time` &lt;= concat(#{statisDate},' 23:59:59')
	</select>

	<!--查询所有新增用户当天-充值人数、笔数、金额 ，普通充值人数、笔数、金额，用户充值人数、笔数、金额 -->
	<select id="queryNumAllDayRech" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			count(DISTINCT (f.member_id)) as numAllDayRech,
			count(*) as numAllDayOrder,
			count(DISTINCT(CASE WHEN (f.order_type = 1) then f.member_id else NULL end)) as numAllDayCommonRech,
			SUM(CASE WHEN (f.order_type = 1) then 1 else 0 end) as numAllDayCommonOrder,
			count(DISTINCT(CASE WHEN (f.order_type = 2) then f.member_id else NULL end)) as numAllDayVipRech,
			SUM(CASE WHEN (f.order_type = 2) then 1 else 0 end) as numAllDayVipOrder,
			sum(f.money_recharge) as moneyAllDay,
			SUM(CASE WHEN (f.order_type = 1) then f.money_recharge else 0 end) as moneyAllDayCommon,
			SUM(CASE WHEN (f.order_type = 2) then f.money_recharge else 0 end) as moneyAllDayVip
		from fast_member_order_recharge f INNER JOIN fast_member_link t on t.member_id = f.member_id
		<where>
			<include refid="whereSQLNumAllDayRech" />
		</where>
	</select>


	<!-- 查询所有新增用户当天-充值人数、笔数、金额  -->
	<sql id="whereSQLNumAllDayRech">
		<if test="lastLinkId != null">
			and f.`link_id` = #{lastLinkId}
			and f.state = 1
			and f.`pay_time` <![CDATA[ >= ]]> concat( #{statisDate},' 00:00:00') and f.`pay_time` <![CDATA[ <= ]]> concat('', #{statisDate},' 23:59:59')
		</if>
	</sql>

	<!--查询新增用户累计-充值人数、笔数、金额  -->
	<select id="queryNumAllRech" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
		count(DISTINCT(f.member_id)) as numAllRech,
		count(*) numAllOrder,sum(f.money_recharge) as moneyAll,
		count(*) numAllOrder,sum(f.money_profit) as moneyProfitAll
		from fast_member_order_recharge f 
		<where>
			and f.state = 1 and f.`pay_time` <![CDATA[ <= ]]> concat('', #{statisDate},' 23:59:59')
			<include refid="whereSQLNumAllRech" />
		</where>
	</select>
	
	<select id="queryNumAllRechSimple" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			link_id linkId,
			num_all_rech numAllRech,
			num_all_order numAllOrder,
			money_all moneyAll,
			money_profit_all moneyProfitAll,
			report_date
		from jingfen_statis_link_order_day
			where link_id = #{lastLinkId} 
			and report_date = #{statisDate}
	</select>
	
	<select id="queryNumAllRechList" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			link_id linkId,
			num_all_rech numAllRech,
			num_all_order numAllOrder,
			money_all moneyAll,
			money_profit_all moneyProfitAll,
			report_date
		from jingfen_statis_link_order_day
			where 
			report_date = #{statisDate} 
			and link_id &gt;= #{linkId}
			order by link_id asc 
			limit ${limit}
	</select>

	<select id="getLinkOrderStatisDay" parameterType="FastStatisLinkPO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			count(*) orderCount,
			ifnull(sum(money_recharge),0) moneyRecharge
		from fast_member_order_recharge
		where
			state = 1
			and link_id = #{linkId}
			and pay_time &gt;= #{beginTime}
			and pay_time &lt;= #{endTime}
	</select>
	
	<select id="getLinkOrderStatisFreeDay" parameterType="FastStatisLinkPO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			count(*) orderCount,
			ifnull(sum(cost_dec),0) moneyRecharge
		from fast_ecpm_data_tt
		where
			link_id = #{linkId}
			and create_time &gt;= #{beginTime}
			and create_time &lt;= #{endTime}
	</select>


	<!-- 查询新增用户累计-充值人数、笔数、金额  -->
	<sql id="whereSQLNumAllRech">
		<if test="lastLinkId != null">
			and f.`link_id` = #{lastLinkId}
		</if>
	</sql>

	<!--查询累计新增用户数、累计总成本  -->
	<select id="queryNumAll" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			sum(t.cost_day) as costAll,
			sum(t.num_day) as numAll
		from fast_statis_link t
		<where>
			<include refid="whereSQLNumAll" />
		</where>
	</select>

	<!-- 查询累计新增用户数、累计总成本-条件  -->
	<sql id="whereSQLNumAll">
		<if test="lastLinkId != null">
			and t.`link_id` = #{lastLinkId}
		</if>
		<if test="statisDate != null">
			and t.`statis_date` <![CDATA[ <= ]]> #{statisDate}
		</if>
	</sql>

	<!--根据日期查询链接id  -->
	<select id="queryStaticDataLinkIds" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="Integer">
		select t.link_id
		from fast_statis_link t
		left join fast_link link on link.id=t.link_id
		<where>
			t.`statis_date` = #{statisDate}
		</where>
	</select>

	<!--查询新增用户充值数据  -->
	<select id="queryDramaRecharge" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			sum(t.num_day_rech) as numDayRech
			,sum(t.money_day) as moneyDay
			,link.drama_id dramaId
		from fast_statis_link t
		left join fast_link link on link.id=t.link_id
		<where>
			<if test="dramaId != null">
				and link.drama_id = #{dramaId}
			</if>
			<if test="statisDateS != null and statisDateS.length()>0">
				and t.`statis_date` &lt;= #{statisDateS}
			</if>
			<if test="statisDateE != null and statisDateE.length()>0">
				and t.`statis_date` &gt;= #{statisDateE}
			</if>
		</where>
		group by link.drama_id
	</select>

	<!--查询新增用户充值数据  -->
	<select id="queryDramaDayRecharge" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			sum(t.num_day_rech) as numDayRech
			,sum(t.money_day) as moneyDay
			,t.`statis_date` dataDay
		from fast_statis_link t
		left join fast_link link on link.id=t.link_id
		<where>
			<if test="dramaId != null">
				and link.drama_id = #{dramaId}
			</if>
			<if test="statisDateS != null and statisDateS.length()>0">
				and t.`statis_date` &lt;= #{statisDateS}
			</if>
			<if test="statisDateE != null and statisDateE.length()>0">
				and t.`statis_date` &gt;= #{statisDateE}
			</if>
		</where>
		GROUP BY t.`statis_date`
	</select>

	<!--查询新增用户充值数据  -->
	<select id="queryOneDramaRecharge" parameterType="com.fast.vo.promote.FastStatisLinkVO" resultType="com.fast.vo.promote.FastStatisLinkVO">
		select
			ifnull(sum(t.num_day_rech),0) as numDayRech
			,ifnull(sum(t.money_day),0) as moneyDay
		from fast_statis_link t
		left join fast_link link on link.id=t.link_id
		<where>
			<if test="dramaId != null">
				and link.drama_id = #{dramaId}
			</if>
			<if test="statisDateS != null and statisDateS.length()>0">
				and t.`statis_date` &lt;= #{statisDateS}
			</if>
			<if test="statisDateE != null and statisDateE.length()>0">
				and t.`statis_date` &gt;= #{statisDateE}
			</if>
			<if test="retailIds != null and retailIds.length()>0">
				and link.`retail_id` in (${retailIds})
			</if>
			<if test="miniIds != null and miniIds.length()>0">
				and link.`mini_id` in (${miniIds})
			</if>
			<if test="linkIds != null and linkIds.length()>0">
				and link.`id` in (${linkIds})
			</if>
		</where>
	</select>

	<!-- 新增 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="FastStatisLinkPO">
        insert into fast_statis_link (
         link_id, statis_date, num_day, num_day_rech, num_day_order, num_all_day, num_all_day_rech, num_all_day_order, num_all, num_all_rech, num_all_order, money_day, money_day_common, money_day_vip, money_all_day, money_all_day_common, money_all_day_vip, money_all, money_all_common, money_all_vip, update_time, create_time
        ) values (
         #{linkId}, #{statisDate}, #{numDay}, #{numDayRech}, #{numDayOrder}, #{numAllDay}, #{numAllDayRech}, #{numAllDayOrder}, #{numAll}, #{numAllRech}, #{numAllOrder}, #{moneyDay}, #{moneyDayCommon}, #{moneyDayVip}, #{moneyAllDay}, #{moneyAllDayCommon}, #{moneyAllDayVip}, #{moneyAll}, #{moneyAllCommon}, #{moneyAllVip}, #{updateTime}, #{createTime}
        )
	</insert>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastStatisLinkPO">
        insert into fast_statis_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="linkId != null">link_id,</if>
	        <if test="retailId != null">retail_id,</if>
	        <if test="statisDate != null">statis_date,</if>
	        <if test="numDay != null">num_day,</if>
	        <if test="numDayRech != null">num_day_rech,</if>
	        <if test="numDayOrder != null">num_day_order,</if>
	        <if test="numAllDay != null">num_all_day,</if>
	        <if test="numAllDayRech != null">num_all_day_rech,</if>
	        <if test="numAllDayOrder != null">num_all_day_order,</if>
	        <if test="numAll != null">num_all,</if>
	        <if test="numAllRech != null">num_all_rech,</if>
	        <if test="numAllOrder != null">num_all_order,</if>
	        <if test="moneyDay != null">money_day,</if>
	        <if test="moneyDayCommon != null">money_day_common,</if>
	        <if test="moneyDayVip != null">money_day_vip,</if>
	        <if test="moneyAllDay != null">money_all_day,</if>
	        <if test="moneyAllDayCommon != null">money_all_day_common,</if>
	        <if test="moneyAllDayVip != null">money_all_day_vip,</if>
	        <if test="moneyAll != null">money_all,</if>
	        <if test="moneyAllCommon != null">money_all_common,</if>
	        <if test="moneyAllVip != null">money_all_vip,</if>
			<if test="moneyProfitDay != null">money_profit_day,</if>
			<if test="moneyProfitDayCommon != null">money_profit_day_common,</if>
			<if test="moneyProfitDayVip != null">money_profit_day_vip,</if>
			<if test="moneyProfitAllDay != null">money_profit_all_day,</if>
			<if test="moneyProfitAllDayCommon != null">money_profit_all_day_common,</if>
			<if test="moneyProfitAllDayVip != null">money_profit_all_day_vip,</if>
			<if test="moneyProfitAll != null">money_profit_all,</if>
			<if test="moneyProfitAllCommon != null">money_profit_all_common,</if>
			<if test="moneyProfitAllVip != null">money_profit_all_vip,</if>
	        <if test="costDay != null">cost_day,</if>
	        <if test="costDayPlus != null">cost_day_plus,</if>
	        <if test="costAll != null">cost_all,</if>
	        <if test="numD60Rech != null">num_d60_rech,</if>
	        <if test="moneyD60Rech != null">money_d60_rech,</if>
			<if test="moneyProfitD60Rech != null">money_profit_d60_rech,</if>
	        <if test="moneyD60Cost != null">money_d60_cost,</if>
	        <if test="moneyD60CostPlus != null">money_d60_cost_plus,</if>
	        <if test="numAllDayCommonRech != null">num_all_day_common_rech,</if>
	        <if test="numAllDayCommonOrder != null">num_all_day_common_order,</if>
	        <if test="numAllDayVipRech != null">num_all_day_vip_rech,</if>
	        <if test="numAllDayVipOrder != null">num_all_day_vip_order,</if>
	        <if test="moneyDAllRech != null">money_dAll_rech,</if>
	        <if test="numDAllRech != null">num_dAll_rech,</if>
			<if test="moneyProfitDAllRech != null">money_profit_dAll_rech,</if>
	        <if test="updateTime != null">update_time,</if>
	        <if test="createTime != null">create_time,</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="linkId != null">#{linkId},</if>
	        <if test="retailId != null">#{retailId},</if>
	        <if test="statisDate != null">#{statisDate},</if>
	        <if test="numDay != null">#{numDay},</if>
	        <if test="numDayRech != null">#{numDayRech},</if>
	        <if test="numDayOrder != null">#{numDayOrder},</if>
	        <if test="numAllDay != null">#{numAllDay},</if>
	        <if test="numAllDayRech != null">#{numAllDayRech},</if>
	        <if test="numAllDayOrder != null">#{numAllDayOrder},</if>
	        <if test="numAll != null">#{numAll},</if>
	        <if test="numAllRech != null">#{numAllRech},</if>
	        <if test="numAllOrder != null">#{numAllOrder},</if>
	        <if test="moneyDay != null">#{moneyDay},</if>
	        <if test="moneyDayCommon != null">#{moneyDayCommon},</if>
	        <if test="moneyDayVip != null">#{moneyDayVip},</if>
	        <if test="moneyAllDay != null">#{moneyAllDay},</if>
	        <if test="moneyAllDayCommon != null">#{moneyAllDayCommon},</if>
	        <if test="moneyAllDayVip != null">#{moneyAllDayVip},</if>
	        <if test="moneyAll != null">#{moneyAll},</if>
	        <if test="moneyAllCommon != null">#{moneyAllCommon},</if>
	        <if test="moneyAllVip != null">#{moneyAllVip},</if>
			<if test="moneyProfitDay != null">#{moneyProfitDay},</if>
			<if test="moneyProfitDayCommon != null">#{moneyProfitDayCommon},</if>
			<if test="moneyProfitDayVip != null">#{moneyProfitDayVip},</if>
			<if test="moneyProfitAllDay != null">#{moneyProfitAllDay},</if>
			<if test="moneyProfitAllDayCommon != null">#{moneyProfitAllDayCommon},</if>
			<if test="moneyProfitAllDayVip != null">#{moneyProfitAllDayVip},</if>
			<if test="moneyProfitAll != null">#{moneyProfitAll},</if>
			<if test="moneyProfitAllCommon != null">#{moneyProfitAllCommon},</if>
			<if test="moneyProfitAllVip != null">#{moneyProfitAllVip},</if>
	        <if test="costDay != null">#{costDay},</if>
	        <if test="costDayPlus != null">#{costDayPlus},</if>
	        <if test="costAll != null">#{costAll},</if>
	        <if test="numD60Rech != null">#{numD60Rech},</if>
	        <if test="moneyD60Rech != null">#{moneyD60Rech},</if>
			<if test="moneyProfitD60Rech != null">#{moneyProfitD60Rech},</if>
	        <if test="moneyD60Cost != null">#{moneyD60Cost},</if>
	        <if test="moneyD60CostPlus != null">#{moneyD60CostPlus},</if>
	        <if test="numAllDayCommonRech != null">#{numAllDayCommonRech},</if>
	        <if test="numAllDayCommonOrder != null">#{numAllDayCommonOrder},</if>
	        <if test="numAllDayVipRech != null">#{numAllDayVipRech},</if>
	        <if test="numAllDayVipOrder != null">#{numAllDayVipOrder},</if>
	        <if test="moneyDAllRech != null">#{moneyDAllRech},</if>
	        <if test="numDAllRech != null">#{numDAllRech},</if>
			<if test="moneyProfitDAllRech != null">#{moneyProfitDAllRech},</if>
			<if test="updateTime != null">#{updateTime},</if>
	        <if test="createTime != null">#{createTime},</if>
        </trim>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStatisLinkPO">
        update fast_statis_link
        <set>
         	<if test="linkId != null" >
               link_id = #{linkId},
            </if>
         	<if test="statisDate != null" >
               statis_date = #{statisDate},
            </if>
         	<if test="numDay != null" >
               num_day = #{numDay},
            </if>
         	<if test="numDayRech != null" >
               num_day_rech = #{numDayRech},
            </if>
         	<if test="numDayOrder != null" >
               num_day_order = #{numDayOrder},
            </if>
         	<if test="numAllDay != null" >
               num_all_day = #{numAllDay},
            </if>
         	<if test="numAllDayRech != null" >
               num_all_day_rech = #{numAllDayRech},
            </if>
         	<if test="numAllDayOrder != null" >
               num_all_day_order = #{numAllDayOrder},
            </if>
         	<if test="numAll != null" >
               num_all = #{numAll},
            </if>
         	<if test="numAllRech != null" >
               num_all_rech = #{numAllRech},
            </if>
         	<if test="numAllOrder != null" >
               num_all_order = #{numAllOrder},
            </if>
         	<if test="moneyDay != null" >
               money_day = #{moneyDay},
            </if>
         	<if test="moneyDayCommon != null" >
               money_day_common = #{moneyDayCommon},
            </if>
         	<if test="moneyDayVip != null" >
               money_day_vip = #{moneyDayVip},
            </if>
         	<if test="moneyAllDay != null" >
               money_all_day = #{moneyAllDay},
            </if>
         	<if test="moneyAllDayCommon != null" >
               money_all_day_common = #{moneyAllDayCommon},
            </if>
         	<if test="moneyAllDayVip != null" >
               money_all_day_vip = #{moneyAllDayVip},
            </if>
         	<if test="moneyAll != null" >
               money_all = #{moneyAll},
            </if>
         	<if test="moneyAllCommon != null" >
               money_all_common = #{moneyAllCommon},
            </if>
         	<if test="moneyAllVip != null" >
               money_all_vip = #{moneyAllVip},
            </if>
			<if test="moneyProfitDay != null" >
				money_profit_day = #{moneyProfitDay},
			</if>
			<if test="moneyProfitDayCommon != null" >
				money_profit_day_common = #{moneyProfitDayCommon},
			</if>
			<if test="moneyProfitDayVip != null" >
				money_profit_day_vip = #{moneyProfitDayVip},
			</if>
			<if test="moneyProfitAllDay != null" >
				money_profit_all_day = #{moneyProfitAllDay},
			</if>
			<if test="moneyProfitAllDayCommon != null" >
				money_profit_all_day_common = #{moneyProfitAllDayCommon},
			</if>
			<if test="moneyProfitAllDayVip != null" >
				money_profit_all_day_vip = #{moneyProfitAllDayVip},
			</if>
			<if test="moneyProfitAll != null" >
				money_profit_all = #{moneyProfitAll},
			</if>
			<if test="moneyProfitAllCommon != null" >
				money_profit_all_common = #{moneyProfitAllCommon},
			</if>
			<if test="moneyProfitAllVip != null" >
				money_profit_all_vip = #{moneyProfitAllVip},
			</if>
         	<if test="costDay != null" >
				cost_day = #{costDay},
            </if>
         	<if test="costDayPlus != null" >
				cost_day_plus = #{costDayPlus},
            </if>
         	<if test="costAll != null" >
				cost_all = #{costAll},
            </if>
         	<if test="numD60Rech != null" >
				num_d60_rech = #{numD60Rech},
            </if>
         	<if test="moneyD60Rech != null" >
				money_d60_rech = #{moneyD60Rech},
            </if>
			<if test="moneyProfitD60Rech != null" >
				money_profit_d60_rech = #{moneyProfitD60Rech},
			</if>
         	<if test="moneyD60Cost != null" >
				money_d60_cost = #{moneyD60Cost},
            </if>
         	<if test="moneyD60CostPlus != null" >
				money_d60_cost_plus = #{moneyD60CostPlus},
            </if>
         	<if test="numAllDayCommonRech != null" >
				num_all_day_common_rech = #{numAllDayCommonRech},
            </if>
         	<if test="numAllDayCommonOrder != null" >
				num_all_day_common_order = #{numAllDayCommonOrder},
            </if>
         	<if test="numAllDayVipRech != null" >
				num_all_day_vip_rech = #{numAllDayVipRech},
            </if>
         	<if test="numAllDayVipOrder != null" >
				num_all_day_vip_order = #{numAllDayVipOrder},
            </if>
         	<if test="moneyDAllRech != null" >
				money_dAll_rech = #{moneyDAllRech},
            </if>
         	<if test="numDAllRech != null" >
				num_dAll_rech = #{numDAllRech},
            </if>
			<if test="moneyProfitDAllRech != null" >
				money_profit_dAll_rech = #{moneyProfitDAllRech},
			</if>
			<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where id = #{id}
	</update>

	<select id="queryFreeData" parameterType="com.fast.vo.promote.FastLinkQueryVO"
			                   resultType="com.fast.vo.promote.FastLinkQueryVO">
        select
            t1.id as id,
            t1.link_name as linkName,
			t1.create_time as createTime,
			t1.link_type,
			(select drama_name from fast_drama where id = t1.drama_id) as dramaName,
			(select series_num from fast_drama_series where id = t1.series_id) as seriesNum,
			IFNULL(t2.total_watch_member_num,0) AS adWatchMemberNum,
			IFNULL(t2.total_watch_num,0) AS adWatchNum,
			IFNULL(t2.total_unlock_member_num,0) AS adUnlockMemberNum,
			IFNULL(t2.total_unlock_num,0) AS adUnlockNum,
			IFNULL(t2.total_ad_income,0) AS adIncomeAll,
			IFNULL(t2.ad_watch_member_num,0) AS adWatchMemberNumDay,
			IFNULL(t2.ad_watch_num,0) AS adWatchNumDay,
			IFNULL(t2.ad_unlock_member_num,0) AS adUnlockMemberNumDay,
			IFNULL(t2.ad_unlock_num,0) AS adUnlockNumDay,
			IFNULL(t2.ad_income,0) AS adIncomeDay,
			IFNULL(t2.total_back_user,0) as backUserNum,
			IFNULL(t2.color_member_day,0) as colorMemberDay,
			IFNULL(t2.color_member_all,0) as colorMemberAll,
			IFNULL(t3.num_day,0) as numDay,
			IFNULL(t3.num_all,0) AS numAll,
            IFNULL(t3.money_day,0) as rechargeMoney,
			IFNULL(t3.num_day_order,0) as rechargeNum,
			IFNULL(t3.num_day_rech,0) as rechargeMemberNum,
			IFNULL(t3.money_all,0) as totalRechargeMoney,
			IFNULL(t3.num_all_order,0) as totalRechargeNum,
			IFNULL(t3.num_all_rech,0) as totalRechargeMemberNum,
            IFNULL(t3.cost_day,0.00) AS costDay,
			IFNULL(t3.cost_all,0) as costAll,
			u.user_name 
        from fast_link t1
        LEFT JOIN fast_statis_link_free t2 ON t1.id = t2.link_id
        LEFT JOIN fast_statis_link t3 ON t2.link_id = t3.link_id and t2.statis_date = t3.statis_date
        left join fast_user u on u.id = t1.creator_id
        where t2.link_id = #{id} and t2.statis_date = #{statisDate}
	</select>

	<select id="getFreeImmeMonitorData" parameterType="com.fast.po.promote.FastStatisLinkFreePO"
										resultType="com.fast.po.promote.FastStatisLinkFreePO">
		SELECT
			l.link_type,
			l.link_sub_type,
			t1.statis_date as statisDate,
			IFNULL(t1.add_member_inocme,0.00) as addMemberInocme,
			IFNULL(t1.add_member_watch_amount,0) as addMemberWatchAmount,
			IFNULL(t1.add_member_watch_num,0) as addMemberWatchNum,
			IFNULL(t1.ad_income_d60,0) as adIncomeD60,
			IFNULL(t1.color_member_day,0) as colorMemberDay,
			IFNULL(t2.money_day,0) as addMemberRecharge,
			IFNULL(t2.num_day_rech,0) as rechargeMemberNum,
			IFNULL(t2.money_d60_rech,0.00) as moneyD60Rech,
			IFNULL(t2.cost_day,0.00) as totalCost,
			IFNULL(t2.num_day,0) as addMemberNum,
		    (SELECT sum(money_recharge) FROM fast_member_order_recharge WHERE link_id = t1.link_id AND state = 1 AND coin_change_id = 0 AND create_time &lt;= #{statisDateEnd} AND create_time >= #{statisDateBegin}) as rechargeMoney
		FROM
			fast_statis_link_free t1
		    left join fast_statis_link t2 on t1.link_id = t2.link_id and t1.statis_date = t2.statis_date
		    left join fast_link l on l.id = t1.link_id
		where t1.link_id = #{linkId}
		  and t1.statis_date = #{statisDate}
	</select>

	<select id="queryUsAgentCost" resultType="java.math.BigDecimal" parameterType="com.fast.po.analysis.DramaAnalysisPO">
		SELECT IFNULL(sum( cost_day ),0.00)
		FROM fast_statis_link t1
		LEFT JOIN fast_link t2 ON t1.link_id = t2.id
		<where>
		    exists (select 1 from fast_retail where t1.retail_id = id and retail_type in (1,2))
			<if test="dramaId != null">
				AND t2.drama_id = #{dramaId}
			</if>
			<if test="statisDate != null">
				AND t1.statis_date = #{statisDate}
			</if>
			<if test="contentType != null">
				AND t2.content_type = #{contentType}
			</if>
			<if test="retailIds != null and retailIds.length()>0">
				AND t2.retail_id in (${retailIds})
			</if>
			<if test="linkIds != null and linkIds.length()>0">
				AND t2.id in (${linkIds})
			</if>
			<if test="advUserIds != null and advUserIds.length()>0">
				AND t2.adv_user_id in (${advUserIds})
			</if>
			<if test="miniIds != null and miniIds.length()>0">
				AND t2.mini_id in (${miniIds})
			</if>
		</where>
	</select>

	<select id="queryImportList" parameterType="FastStatisLinkPO" resultType="FastStatisLinkPO">
        SELECT
            t.*,
            t3.series_id as link_series_id,
            t3.drama_id as link_drama_id,
            t3.official_id as link_official_id,
            t3.adv_media_id as link_adv_media_id,
            t3.app_type as link_app_type,
            t3.mini_id as link_mini_id,
            t3.retail_id as link_retail_id,
            t3.road_id as link_road_id,
            t3.adv_user_id as link_adv_user_id,
            t3.fee_flag as link_fee_flag,
            t3.link_type as link_type,
            t3.link_sub_type as link_sub_type,
            t4.retail_type as retail_type,
            t4.retail_name as retail_name,
            t4.retail_flag as retail_flag
        FROM `fast_statis_link` t
            left join fast_link t3 on t.link_id =  t3.id
            left join fast_retail t4 on t.retail_id= t4.id
        where t.id > #{startId}
        and t.statis_date &lt;= CONCAT(DATE_SUB(CURRENT_DATE(),INTERVAL 1 DAY)," 23:59:59")
        order by t.id limit #{step}
    </select>

</mapper>
