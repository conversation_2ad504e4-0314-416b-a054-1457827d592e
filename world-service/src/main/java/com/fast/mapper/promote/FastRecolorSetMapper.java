/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.po.promote.FastRecolorSetPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastRecolorSetMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastRecolorSetPO
     * @return
     */
    FastRecolorSetPO queryById(FastRecolorSetPO fastRecolorSetPO);

    /**
     * 通过id查询单个对象
     *
     * @param id
     * @return
     */
    FastRecolorSetPO queryById(@Param("id") Integer id);

    /**
     * 通过条件查询单个对象
     *
     * @param fastRecolorSetPO
     * @return
     */
    FastRecolorSetPO queryOne(FastRecolorSetPO fastRecolorSetPO);

    /**
     * 查询全部
     *
     * @param fastRecolorSetPO
     * @return
     */
    List<FastRecolorSetPO> queryList(FastRecolorSetPO fastRecolorSetPO);

    /**
     * 查询总数
     *
     * @param fastRecolorSetPO
     * @return
     */
    int queryCount(FastRecolorSetPO fastRecolorSetPO);

    /**
     * 可选新增
     *
     * @param fastRecolorSetPO
     * @return
     */
    int insertSelective(FastRecolorSetPO fastRecolorSetPO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastRecolorSetPO> list);

    /**
     * 更新
     *
     * @param fastRecolorSetPO
     * @return
     */
    int updateById(FastRecolorSetPO fastRecolorSetPO);

}
