<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastMemberLinkHisTablestoreMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMemberLinkHisTablestore_columns">
		select t.`id`,t.`member_id`,t.`mini_id`,t.`official_id`,t.`retail_id`,t.`link_id`,t.`link_time`,t.`create_time`,t.`ip`,t.`ua`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastMemberLinkHisTablestorePO">
		<include refid="FastMemberLinkHisTablestore_columns" />
	    from fast_member_link_his_tablestore t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMemberLinkHisTablestorePO" resultType="FastMemberLinkHisTablestorePO">
		<include refid="FastMemberLinkHisTablestore_columns" />
	    from fast_member_link_his_tablestore t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMemberLinkHisTablestorePO" resultType="FastMemberLinkHisTablestorePO">
		<include refid="FastMemberLinkHisTablestore_columns" />
	    from fast_member_link_his_tablestore t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMemberLinkHisTablestorePO" resultType="int">
		select count(*)
	    from fast_member_link_his_tablestore t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="memberId != null">
			and t.`member_id` = #{memberId}
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="officialId != null">
			and t.`official_id` = #{officialId}
		</if>
		<if test="retailId != null">
			and t.`retail_id` = #{retailId}
		</if>
		<if test="linkId != null">
			and t.`link_id` = #{linkId}
		</if>
		<if test="linkTime != null">
			and t.`link_time` = #{linkTime}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="ip != null">
			and t.`ip` = #{ip}
		</if>
		<if test="ua != null">
			and t.`ua` = #{ua}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMemberLinkHisTablestorePO">
        insert into fast_member_link_his_tablestore
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="memberId != null">member_id,</if>
	        <if test="miniId != null">mini_id,</if>
	        <if test="officialId != null">official_id,</if>
	        <if test="retailId != null">retail_id,</if>
	        <if test="linkId != null">link_id,</if>
	        <if test="linkTime != null">link_time,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="ip != null">ip,</if>
	        <if test="ua != null">ua</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="memberId != null">#{memberId},</if>
	        <if test="miniId != null">#{miniId},</if>
	        <if test="officialId != null">#{officialId},</if>
	        <if test="retailId != null">#{retailId},</if>
	        <if test="linkId != null">#{linkId},</if>
	        <if test="linkTime != null">#{linkTime},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="ip != null">#{ip},</if>
	        <if test="ua != null">#{ua}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastMemberLinkHisTablestorePO">
        insert into fast_member_link_his_tablestore (
         member_id, mini_id, official_id, retail_id, link_id, link_time, create_time, ip, ua
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.memberId}, #{item.miniId}, #{item.officialId}, #{item.retailId}, #{item.linkId}, #{item.linkTime}, #{item.createTime}, #{item.ip}, #{item.ua}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastMemberLinkHisTablestorePO">
        update fast_member_link_his_tablestore
        <set>
         	<if test="memberId != null" >
               member_id = #{memberId},
            </if>
         	<if test="miniId != null" >
               mini_id = #{miniId},
            </if>
         	<if test="officialId != null" >
               official_id = #{officialId},
            </if>
         	<if test="retailId != null" >
               retail_id = #{retailId},
            </if>
         	<if test="linkId != null" >
               link_id = #{linkId},
            </if>
         	<if test="linkTime != null" >
               link_time = #{linkTime},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="ip != null" >
               ip = #{ip},
            </if>
         	<if test="ua != null" >
               ua = #{ua},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
