<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastShortLinkMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastShortLink_columns">
		select t.`id`,t.`shorts`,t.`short_link`,t.`long_link`,t.`flag`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" parameterType="FastShortLinkPO" resultType="FastShortLinkPO">
		<include refid="FastShortLink_columns" />
	    from fast_short_link t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastShortLinkPO" resultType="FastShortLinkPO">
		<include refid="FastShortLink_columns" />
	    from fast_short_link t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastShortLinkPO" resultType="FastShortLinkPO">
		<include refid="FastShortLink_columns" />
	    from fast_short_link t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastShortLinkPO" resultType="int">
		select count(*)
	    from fast_short_link t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="shorts != null">
			and t.`shorts` = #{shorts}
		</if>
		<if test="shortLink != null">
			and t.`short_link` = #{shortLink}
		</if>
		<if test="longLink != null">
			and t.`long_link` = #{longLink}
		</if>
		<if test="flag != null">
			and t.`flag` = #{flag}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 新增 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="FastShortLinkPO">
        insert into fast_short_link (
         shorts, short_link, long_link, flag, creator_id, updator_id, create_time, update_time
        ) values (
         #{shorts}, #{shortLink}, #{longLink}, #{flag}, #{creatorId}, #{updatorId}, #{createTime}, #{updateTime}
        )
	</insert>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastShortLinkPO">
        insert into fast_short_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="shorts != null">shorts,</if>
	        <if test="shortLink != null">short_link,</if>
	        <if test="longLink != null">long_link,</if>
	        <if test="flag != null">flag,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="shorts != null">#{shorts},</if>
	        <if test="shortLink != null">#{shortLink},</if>
	        <if test="longLink != null">#{longLink},</if>
	        <if test="flag != null">#{flag},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastShortLinkPO">
        insert into fast_short_link (
         shorts, short_link, long_link, flag, creator_id, updator_id, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.shorts}, #{item.shortLink}, #{item.longLink}, #{item.flag}, #{item.creatorId}, #{item.updatorId}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastShortLinkPO">
        update fast_short_link
        <set>
         	<if test="shorts != null" >
               shorts = #{shorts},
            </if>
         	<if test="shortLink != null" >
               short_link = #{shortLink},
            </if>
         	<if test="longLink != null" >
               long_link = #{longLink},
            </if>
         	<if test="flag != null" >
               flag = #{flag},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>


</mapper>
