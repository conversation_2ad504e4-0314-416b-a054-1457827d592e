<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastLinkMapper">
    <!-- 用于select查询公用抽取的列 -->
    <sql id="FastLink_columns">
        select
        t.`id`,
        t.`link_name`,
        t.`promote_link`,
        t.`short_link`,
        t.`drama_id`,
        t.`series_id`,
        t.`back_type`,
        t.`back_rule`,
        t.`baidu_event_type`,
        t.`pay_type`,
        t.`pay_rule`,
        t.`tt_pay_type`,
        t.`tt_pay_rule`,
        t.`mini_id`,
        t.`mini_app_id`,
        t.`official_id`,
        t.`adv_user_id`,
        t.`adv_media_id`,
        t.adv_version,
        t.`app_type`,
        t.`link_title`,
        t.`road_id`,
        t.`retail_id`,
        t.`creator_id`,
        t.`updator_id`,
        t.`create_time`,
        t.`update_time`,
        t.`ad_ground_id`,
        t.`ad_ground_domain`,
        t.`content_type`,
        t.start_num_global,
        t.coin_per_global,
        t.follow_num_global,
        t.link_type,
        t.link_sub_type,
        t.link_platform,
        t.accord_plan,
        t.keep_type,
        t.keep_id,
        t.`tt_split_platform`,
        t.`tt_split_xman`,
        t.`tt_split_mcn`,
        t.`fee_flag`,
        t.mix_flag,
        t.wx_finder_id,
        t.tt_bind_flag,
		t.adv_unlock_flag,
		t.adv_unlock_num,
		t.skip_series
    </sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" parameterType="FastLinkPO" resultType="FastLinkPO">
		<include refid="FastLink_columns" />
			,ext.an_unlock_num anUnlockNum
			,ext.ios_unlock_num iosUnlockNum
	    from fast_link t
	    left join fast_link_ext ext on ext.id = t.id
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

    <!-- 通过id查询单个对象 -->
    <select id="queryLinkSubTypeById" resultType="Integer">
		select link_sub_type
	    from fast_link t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

    <select id="queryMoreById" parameterType="FastLinkPO" resultType="FastLinkPO">
		<include refid="FastLink_columns" />
		,u.user_name
		,m.mini_name
	    from fast_link t
	    left join fast_mini m on m.id = t.mini_id
	    left join fast_user u on u.id = t.creator_id
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastLinkPO" resultType="FastLinkPO">
		<include refid="FastLink_columns" />
	    from fast_link t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryAdvUserIds" parameterType="FastLinkPO" resultType="Integer">
		select t.adv_user_id
	    from fast_link t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!--分页查询集合  -->
	<select id="queryLinkIds" parameterType="FastLinkPO" resultType="Integer">
		select t.id
	    from fast_link t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!--根据短剧的起始付费集查询链接id  -->
	<select id="queryLinkIdsByStartNum" resultType="Integer">
		select t.id
	    from fast_link t
		left join fast_fee_rule fu on ( (fu.official_id=t.official_id and fu.drama_id=t.drama_id) or fu.link_id = t.id )
        <where>
        	fu.start_num=#{startNum} and fu.drama_id=#{dramaId}
        </where>
	</select>

	<!--根据短剧的起始付费集查询链接id  -->
	<select id="queryLinkIdsByStartNumDef" resultType="Integer">
		select t.id
		from fast_link t
		left join fast_fee_rule def on def.drama_id=t.drama_id and def.retail_id=0 and def.official_id=0
		<where>
			def.start_num=#{startNum} and def.drama_id=#{dramaId}
		</where>
	</select>

	<!--分页查询集合  -->
	<select id="queryLinkIdsByAdvMediaId" resultType="Integer">
		select t.id
	    from fast_link t
        <where>
			t.`adv_media_id` = #{advMediaId}
        </where>
	</select>

	<!--分页查询集合  -->
	<select id="queryLinkNames" parameterType="FastLinkPO" resultType="FastLinkPO">
		select t.id, t.link_name, t.create_time, u.user_name userName
	    from fast_link t
		left join fast_user u on u.id=t.adv_user_id
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!--分页查询集合  -->
	<select id="queryOfficialIds" parameterType="FastLinkPO" resultType="Integer">
		select t.official_id
	    from fast_link t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!--分页查询集合  -->
	<select id="querySimpleList" parameterType="FastLinkPO" resultType="FastLinkPO">
		<include refid="FastLink_columns" />
	    from fast_link t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>
	
	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastLinkPO" resultType="FastLinkPO">
 		<include refid="FastLink_columns" />
	 	    from fast_link t
	<where>
		<include refid="whereSQL" />
		<if test="retailIds != null and retailIds.length > 0">
		and t.retail_id in (${retailIds})
		</if>
	</where>
	order by t.`id` desc
 	</select>

	<!--分页查询集合  -->
	<select id="queryTiktokBackList" parameterType="FastLinkPO" resultType="FastLinkPO">
		<include refid="FastLink_columns" />
		,m.app_id
	    from fast_link t
        join fast_mini m on m.def_official_id = t.official_id and m.type = 2
		where 
			t.tt_bind_flag = #{ttBindFlag}
		limit ${limit}
	</select>
	
	<select id="querySynPVList" parameterType="FastLinkPO" resultType="FastLinkPO">
		select id
	    from fast_link t
	    where id &gt; #{id}
		order by t.`id` 
		limit ${limit}
	</select>

	<!--分页查询集合  -->
	<select id="getSimpleList" parameterType="FastLinkPO" resultType="FastLinkPO">
		select t.`id`, t.`link_name`, t.`drama_id`, t.`mini_id`, t.`retail_id`
	    from fast_link t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
		<if test="limitExport != null">limit ${limitExport}</if>
	</select>

	<!--查询没有配置链路的链接  -->
	<select id="queryLinkOfficialList" parameterType="FastLinkPO" resultType="FastLinkPO">
		select t.id, t.official_id
	    from fast_link t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!--分页查询集合  -->
	<select id="queryListV1" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="com.fast.vo.promote.FastLinkQueryVO">
		<include refid="FastLink_columns" />
		,f.user_name as advUserName
		,fr.start_num
		,fr.follow_num
		,fr.coin_per
		,m.mini_name
		,frkb.batch_name keep_batch_name
	    from fast_link t
	    INNER JOIN fast_user f ON t.adv_user_id = f.id
	    left join fast_drama d on d.id = t.drama_id
	    left join fast_fee_rule fr on fr.link_id = t.id
	    left join fast_mini m on m.id = t.mini_id
		left join fast_fee_keep_batch frkb on frkb.id = t.keep_id
        <where>
			t.fee_flag = 1
        	<include refid="whereSQLV1" />
			<if test="userIds != null and userIds.length > 0">
				and f.id in (${userIds})
			</if>
			<if test="dramaName != null and dramaName.length > 0">
				and d.drama_name like concat('%',#{dramaName},'%')
			</if>
			<if test="dramaIds != null and dramaIds.length > 0">
				and find_in_set(t.drama_id,#{dramaIds})
			</if>
        </where>
		<if test="queryType eq null"><!-- 排序方式-1倒序 2正序 按创建时间  -->
			order by t.`id` desc
		</if>
		<if test="queryType != null and queryType eq 1"><!-- 排序方式-1倒序 2正序 按创建时间  -->
			order by t.`create_time` desc
		</if>
		<if test="queryType != null and queryType eq 2"><!-- 排序方式-1倒序 2正序 按创建时间  -->
			order by t.`create_time` ASC
		</if>
	</select>

	<!--分页查询集合  按条件查询 -投放平台-数据采集-链接列表-->
	<select id="queryListV1601Drama" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="com.fast.vo.promote.FastLinkQueryVO">
		<include refid="FastLink_columns" />
		,f.user_name as advUserName
		,r.retail_type as retailType
	    from fast_link t INNER JOIN fast_user f ON t.adv_user_id = f.id INNER JOIN fast_retail r ON t.retail_id = r.id
		<where>
			(r.`retail_type` = 1 or r.`retail_type` = 2 )
        	<include refid="whereSQLV1" />
        </where>
		<if test="queryType eq null"><!-- 排序方式-1倒序 2正序 按创建时间  -->
			order by t.`id` desc
		</if>
		<if test="queryType != null and queryType eq 1"><!-- 排序方式-1倒序 2正序 按创建时间  -->
			order by t.`create_time` desc
		</if>
		<if test="queryType != null and queryType eq 2"><!-- 排序方式-1倒序 2正序 按创建时间  -->
			order by t.`create_time` ASC
		</if>
		<if test="queryType != null and queryType eq 11"><!-- 排序方式 按更新时间  -->
			order by t.`update_time` desc
		</if>
		<if test="queryType != null and queryType eq 12"><!-- 排序方式 按更新时间  -->
			order by t.`update_time` ASC
		</if>
		<if test="contentType != null">AND t.content_type=#{contentType}</if>
	</select>

	<!--查询全部ids  -->
	<select id="queryLinkIdsForStatis" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="FastLinkPO">
		select t.`id`
		from fast_link t
		order by t.`id` desc
	</select>

	<!--查询全部ids ,根据条件 -->
	<select id="queryLinkIdsForStatisV1" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="FastLinkPO">
		select t.`id`
		from fast_link t
		<where>
			<if test="beginTime != null">
				and t.`create_time`  <![CDATA[ >= ]]>  #{beginTime}
			</if>
			<if test="endTime != null">
				and t.`create_time`  <![CDATA[ < ]]>  #{endTime}
			</if>
			<if test="contentType != null">AND t.content_type=#{contentType}</if>
		</where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastLinkPO" resultType="int">
		select count(*)
	    from fast_link t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 条件  -->
	<sql id="whereSQLV1">
		<if test="beginTime != null and beginTime.length()>0">
			and t.`create_time`  <![CDATA[ >= ]]>  #{beginTime}
		</if>
		<if test="endTime != null and endTime.length()>0">
			and t.`create_time`  <![CDATA[ <= ]]>  #{endTime}
		</if>
		<if test="beginUpTime != null and endUpTime != null">
			and t.update_time between #{beginUpTime} and #{endUpTime}
		</if>
		<if test="linkName != null">
			<if test="id != null">
				and (t.`id` = #{id} or t.`link_name`  like concat('%',#{linkName},'%'))
			</if>
			<if test="id == null">
				and t.`link_name`  like concat('%',#{linkName},'%')
			</if>
		</if>
		<if test="officialId != null">
			and t.`official_id` = #{officialId}
		</if>
		<if test="retailId != null">
			and t.`retail_id` = #{retailId}
		</if>
		<if test="advUserId != null">
			and t.`adv_user_id` = #{advUserId}
		</if>
		<if test="adGroundId != null">
			and t.`ad_ground_id` = #{adGroundId}
		</if>
		<if test="contentType != null">AND t.content_type=#{contentType}</if>
       	<if test="linkType != null">AND t.link_type=#{linkType}</if>
       	<if test="linkSubType != null">AND t.link_sub_type=#{linkSubType}</if>
       	<if test="linkPlatform != null">AND t.link_platform=#{linkPlatform}</if>
	</sql>

	<!-- 短剧消耗金额 -->
	<select id="queryListDramaCostSum" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="java.math.BigDecimal">
		select sum(f.cost_day)
		from fast_link t INNER JOIN fast_statis_link f ON t.id = f.link_id
		<where>
			<include refid="whereSQLDramaCost" />
		</where>
	</select>

	<!--分页查询集合 短剧消耗金额 -->
	<select id="queryListDramaCost" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="com.fast.vo.promote.FastLinkQueryVO">
		select t.drama_id
			,sum(f.cost_day) as adMoneyConsume
			,sum(if(t.fee_flag=1,f.cost_day,0)) as adMoneyConsumePay
			,sum(if(t.fee_flag=2,f.cost_day,0)) as adMoneyConsumeFree
		from fast_link t INNER JOIN fast_statis_link f ON t.id = f.link_id
		<where>
			<include refid="whereSQLDramaCost" />
			<if test="contentType != null">
				and t.content_type = #{contentType}
			</if>
		</where>
		GROUP BY t.`drama_id`
	</select>

	<!--分页查询集合 短剧消耗金额 -->
	<select id="queryListDramaCostDay" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="com.fast.vo.promote.FastLinkQueryVO">
		select f.statis_date dataDay
			,sum(f.cost_day) as adMoneyConsume
			,sum(if(t.fee_flag=1,f.cost_day,0)) as adMoneyConsumePay
			,sum(if(t.fee_flag=2,f.cost_day,0)) as adMoneyConsumeFree
		from fast_link t INNER JOIN fast_statis_link f ON t.id = f.link_id
		<where>
			<include refid="whereSQLDramaCost" />
		</where>
		GROUP BY f.`statis_date`
	</select>

	<!-- 条件 短剧消耗金额-->
	<sql id="whereSQLDramaCost">
		<if test="ids != null and ids.length() > 0">
			and t.`id` in (${ids})
		</if>
		<if test="beginTime != null">
			and f.`statis_date`  <![CDATA[ >= ]]>  #{beginTime}
		</if>
		<if test="endTime != null">
			and f.`statis_date`  <![CDATA[ <= ]]>  #{endTime}
		</if>
		<if test="dramaId != null">
			and t.`drama_id`  =  #{dramaId}
		</if>
		<if test="retailId != null">
			and t.`retail_id` = #{retailId}
		</if>
		<if test="retailIds != null and retailIds.length()>0">
			and t.`retail_id` in (${retailIds})
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="miniIds != null and miniIds.length()>0">
			and t.`mini_id` in (${miniIds})
		</if>
		<if test="advUserIds != null and advUserIds.length()>0">
			AND t.adv_user_id in (${advUserIds})
		</if>
		<if test="officialId != null">
			and t.official_id = #{officialId}
		</if>
		<if test="officialIds != null and officialIds.length()>0">
			and t.`official_id` in (${officialIds})
		</if>
		<if test="contentType != null">
		    AND t.content_type=#{contentType}
		</if>
		<if test="feeFlag != null">
		    AND t.fee_flag=#{feeFlag}
		</if>
		<if test="retailTypes != null">
			AND EXISTS (SELECT * FROM fast_retail WHERE id = f.retail_id AND retail_flag = 1 AND del_flag = 0 AND FIND_IN_SET( retail_type, #{retailTypes} ))
		</if>
		<if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
			and t.`link_type` != 3
		</if>
	</sql>

	<select id="querySubListAdmin" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="com.fast.vo.promote.FastLinkQueryVO">
		SELECT
			l.link_sub_type,
			sum( mor.money_recharge ) money_recharge,
			sum( money_profit ) money_profit
		FROM
			fast_member_order_recharge mor
			JOIN fast_link l ON l.id = mor.link_id
			AND l.link_type = 3
		WHERE
			mor.state = 1
			<if test="advMediaId != null">
				and l.adv_media_id = #{advMediaId}
			</if>
			<if test="miniIds != null and miniIds.length > 0">
				and mor.mini_id in (${miniIds})
			</if>
			<if test="dramaIds != null and dramaIds.length > 0">
				and mor.drama_id in (${dramaIds})
			</if>
			<if test="beginTime != null and beginTime.length > 0">
				and mor.pay_time &gt;= #{beginTime}
			</if>
			<if test="endTime != null and endTime.length > 0">
				and mor.pay_time &lt;= #{endTime}
			</if>
			<if test="contentType != null">
				and l.content_type = #{contentType}
			</if>
		GROUP BY
			l.link_sub_type
	</select>
	<!--分页查询集合  -->
	<select id="queryListV2" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="com.fast.vo.promote.FastLinkQueryVO">
		<include refid="FastLink_columns" />
			,f.user_name as advUserName
			,r.`retail_name` as retailName
			,m.mini_name miniName
			,m.type miniType
			,fr.start_num
			,fr.follow_num
			,fr.coin_per
		from fast_link t
		INNER JOIN fast_user f ON t.adv_user_id = f.id
		LEFT JOIN fast_statis_link l ON t.id = l.link_id and l.statis_date = #{statisDate}
		LEFT JOIN fast_drama d ON t.drama_id = d.id
		LEFT JOIN fast_drama_series s ON t.series_id = s.id
		LEFT JOIN fast_retail r ON t.retail_id = r.id
		left join fast_mini m on m.id = t.mini_id
		left join fast_fee_rule fr on fr.link_id = t.id
		<where>
		    t.fee_flag = 1
			<include refid="whereSQLV2" />
		</where>
		<if test="orderNumAll != null and orderNumAll eq 1">
			order by l.`num_all` desc
		</if>
		<if test="orderNumAll != null and orderNumAll eq 2">
			order by l.`num_all` ASC
		</if>
		<if test="orderNumAll == null">
			<if test="orderMoneyAll != null and orderMoneyAll eq 1">
				order by l.`money_all` desc
			</if>
			<if test="orderMoneyAll != null and orderMoneyAll eq 2">
				order by l.`money_all` ASC
			</if>
			<if test="orderMoneyAll == null">
				<if test="orderMoneyAllDay != null and orderMoneyAllDay eq 1">
					order by l.`money_all_day` desc
				</if>
				<if test="orderMoneyAllDay != null and orderMoneyAllDay eq 2">
					order by l.`money_all_day` ASC
				</if>
				<if test="orderMoneyAllDay == null">
					<if test="orderProfitAll != null and orderProfitAll eq 1">
						order by l.`money_all`-l.`cost_all` desc
					</if>
					<if test="orderProfitAll != null and orderProfitAll eq 2">
						order by l.`money_all`-l.`cost_all` ASC
					</if>
					<if test="orderProfitAll == null">
						<if test="queryType eq null"><!-- 排序方式-1倒序 2正序 按创建时间  -->
							order by t.`id` desc
						</if>
						<if test="queryType != null and queryType eq 1"><!-- 排序方式-1倒序 2正序 按创建时间  -->
							order by t.`create_time` desc
						</if>
						<if test="queryType != null and queryType eq 2"><!-- 排序方式-1倒序 2正序 按创建时间  -->
							order by t.`create_time` ASC
						</if>
					</if>
				</if>
			</if>
		</if>

		<if test="limitExport != null and limitExport > 0">
			limit ${limitExport}
		</if>
	</select>

    <select id="queryMediaRetailConsume" resultType="com.fast.po.promote.FastLinkPO" parameterType="com.fast.po.promote.FastLinkPO">
		SELECT
			ifnull(t3.project_id,0) as project_id,
			t4.retail_type,
			t2.adv_media_id,
			sum( cost_day ) consumeMoney
		FROM
		fast_statis_link t1
		LEFT JOIN fast_link t2 ON t1.link_id = t2.id
		LEFT JOIN fast_drama t3 on t3.id = t2.drama_id
		LEFT JOIN fast_retail t4 ON t2.retail_id = t4.id
		WHERE
			t1.statis_date = #{reportDate}
			AND t2.link_type = 1
			AND t4.retail_type in (1,2)
		GROUP BY
			t3.project_id,
			t4.retail_type,
			t2.adv_media_id
	</select>

	<select id="queryMediaMountTTConsume" resultType="com.fast.po.promote.FastLinkPO" parameterType="com.fast.po.promote.FastLinkPO">
		SELECT
			tmp.project_id AS projectId,
			tmp.app_type AS appType ,
			tmp.link_sub_type AS linkSubType,
			sum( tmp.money_recharge ) as consumeMoney,
			sum(tmp.consumeMoneyShare) as consumeMoneyShare
		FROM
		(
			SELECT
			ifnull(t4.project_id,0) AS project_id ,
			ifnull( t2.app_type, 2 ) AS app_type,
			ifnull( t2.link_sub_type, 1 ) AS link_sub_type,
			sum( t1.money_profit ) AS money_profit,
			sum( t1.money_recharge ) AS money_recharge,
			sum( t1.money_recharge *( IFNULL(t2.tt_split_mcn,0) +ifnull(t2.tt_split_platform,0) + ifnull(t2.tt_split_xman,0)) ) consumeMoneyShare
			FROM
			fast_member_order_recharge t1
			LEFT JOIN fast_link t2 ON t1.link_id = t2.id
			LEFT JOIN fast_drama t4 ON t1.drama_id = t4.id
			WHERE
			t1.coin_change_id = 0
			AND t1.state = 1
			AND t1.pay_time &gt;= #{startPayTime}
			AND t1.pay_time &lt;= #{endPayTime}
			  AND (t1.link_id = 0 AND EXISTS ( SELECT * FROM fast_retail WHERE id = t1.retail_id AND retail_type = 1 AND id != 14)
					OR ( t2.link_type = 3 ))
			  AND EXISTS ( SELECT * FROM fast_mini WHERE id = t1.mini_id AND type = 2 )
			  AND t1.content_type = 1
			GROUP BY
			t4.project_id,
			t2.app_type,
			t2.link_sub_type
			) tmp
		GROUP BY
		tmp.project_id,
		tmp.app_type,
		tmp.link_sub_type
	</select>

	<select id="queryMediaMountKSConsume" resultType="com.fast.po.promote.FastLinkPO" parameterType="com.fast.po.promote.FastLinkPO">
		select  tmp.projectId,
				tmp.appType,
				tmp.linkSubType,
				sum( tmp.moneyRecharge ) as consumeMoney,
				sum( tmp.consumeMoneyShare ) as  consumeMoneyShare
		from ( SELECT
				   ifnull(t4.project_id,0) as projectId,
				   ifnull(t2.app_type,4) as  appType,
				   4 as linkSubType,
				   sum( t1.money_profit ) AS moneyProfit,
				   sum( t1.money_recharge ) AS moneyRecharge,
				   sum( t1.money_recharge *( IFNULL(t2.tt_split_mcn,0) +ifnull(t2.tt_split_platform,0) + ifnull(t2.tt_split_xman,0)) ) consumeMoneyShare
			   FROM
				   fast_member_order_recharge t1
					   LEFT JOIN fast_link t2 ON t1.link_id = t2.id
					   LEFT JOIN fast_drama t4 ON t1.drama_id = t4.id
					   LEFT JOIN fast_mini t5 on t5.id = t1.mini_id
			   WHERE
				   t1.coin_change_id = 0
				 AND t1.state = 1
				 AND t1.pay_time &lt;= #{endPayTime}
				 AND t1.pay_time &gt;= #{startPayTime}
				 AND ((t2.link_type = 3 AND t5.type = 4)
				OR (t1.mini_id = 77 AND EXISTS ( SELECT * FROM fast_retail WHERE id = t1.retail_id AND retail_type = 1 AND id != 14)))
			   GROUP BY
				   t4.project_id,
				   t2.app_type,
				   t2.link_sub_type) tmp
		group by tmp.projectId,
				 tmp.appType,
				 tmp.linkSubType
	</select>


    <!-- 条件  -->
	<sql id="whereSQLV2">
		<if test="advMediaId != null">
			and t.`adv_media_id`  =   #{advMediaId}
		</if>
		<if test="beginTime != null">
			and t.`create_time`  <![CDATA[ >= ]]>  #{beginTime}
		</if>
		<if test="endTime != null">
			and t.`create_time`  <![CDATA[ <= ]]>  #{endTime}
		</if>
		<if test="inPage != null">
			and concat(d.`drama_name`,'-第',s.`series_num` ,'集')  like concat('%',#{inPage},'%')
		</if>
		<if test="linkName != null">
			<if test="id != null">
				and (t.`id` = #{id} or t.`link_name`  like concat('%',#{linkName},'%'))
			</if>
			<if test="id == null">
				and t.`link_name`  like concat('%',#{linkName},'%')
			</if>
		</if>
		<if test="retailName != null and retailName != ''">
				and r.`retail_name`  like concat('%',#{retailName},'%')
		</if>
		<if test="advUserName != null and advUserName != ''">
				and f.`user_name`  like concat('%',#{advUserName},'%')
		</if>
		<if test="officialId != null">
		    <if test="officialId != 0">
			    and t.`official_id` = #{officialId}
			</if>
		</if>
		<if test="miniIds != null and miniIds.length > 0">
			    and t.`mini_id` in (${miniIds})
		</if>
		<if test="dramaIds != null and dramaIds.length > 0">
			    and t.`drama_id` in (${dramaIds})
		</if>
		<if test="linkSubTypes != null and linkSubTypes.length > 0">
			    and t.`link_sub_type` in (${linkSubTypes})
		</if>
		<if test="contentType != null">AND t.content_type=#{contentType}</if>
		<if test="linkType != null">AND t.link_type=#{linkType}</if>
		<if test="linkPlatform != null">AND t.link_platform=#{linkPlatform}</if>
	</sql>


	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="ids != null and ids.length() > 0">
			and t.`id` in (${ids})
		</if>
		<if test="contentType != null">
			and t.content_type=#{contentType}
		</if>
		<if test="linkName != null and linkName.length() > 0">
			and t.`link_name` = #{linkName}
		</if>
		<if test="linkNameLike != null and linkNameLike.length() > 0">
			and (t.`link_name` like concat('%', #{linkNameLike}, '%') or t.id = #{linkNameLike})
		</if>
		<if test="promoteLink != null">
			and t.`promote_link` = #{promoteLink}
		</if>
		<if test="shortLink != null">
			and t.`short_link` = #{shortLink}
		</if>
		<if test="dramaId != null">
			and t.`drama_id` = #{dramaId}
		</if>
		<if test="dramaIds != null and dramaIds.length() > 0">
			and t.`drama_id` in (${dramaIds})
		</if>
		<if test="seriesId != null">
			and t.`series_id` = #{seriesId}
		</if>
		<if test="backType != null">
			and t.`back_type` = #{backType}
		</if>
		<if test="backRule != null">
			and t.`back_rule` = #{backRule}
		</if>
		<if test="payType != null">
			and t.`pay_type` = #{payType}
		</if>
		<if test="payRule != null">
			and t.`pay_rule` = #{payRule}
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="miniIds != null and miniIds.length() > 0">
			and t.`mini_id` in (${miniIds})
		</if>
		<if test="miniAppId != null">
			and t.`mini_app_id` = #{miniAppId}
		</if>
		<if test="officialId != null">
			and t.`official_id` = #{officialId}
		</if>
		<if test="advUserId != null">
			and t.`adv_user_id` = #{advUserId}
		</if>
		<if test="advUserIds != null and advUserIds.length() > 0">
			and t.`adv_user_id` in (${advUserIds})
		</if>
		<if test="advMediaId != null">
			and t.`adv_media_id` = #{advMediaId}
		</if>
		<if test="appType != null">
			and t.`app_type` = #{appType}
		</if>
		<if test="linkTitle != null">
			and t.`link_title` = #{linkTitle}
		</if>
		<if test="roadId != null">
			and t.`road_id` = #{roadId}
		</if>
		<if test="retailId != null">
			and t.`retail_id` = #{retailId}
		</if>
		<if test="retailIds != null and retailIds.length() > 0">
			and t.retail_id in (${retailIds})
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
		<if test="keepType != null">
			and t.`keep_type` = #{keepType}
		</if>
		<if test="keepId != null">
			and t.`keep_id` = #{keepId}
		</if>
		<if test="feeFlag != null">
			and t.`fee_flag` = #{feeFlag}
		</if>
		<if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
			and t.`link_type` != 3
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastLinkPO">
        insert into fast_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="contentType != null">content_type,</if>
	        <if test="linkName != null">link_name,</if>
	        <if test="promoteLink != null">promote_link,</if>
	        <if test="shortLink != null">short_link,</if>
	        <if test="dramaId != null">drama_id,</if>
	        <if test="seriesId != null">series_id,</if>
	        <if test="backType != null">back_type,</if>
	        <if test="backRule != null">back_rule,</if>
	        <if test="payType != null">pay_type,</if>
	        <if test="payRule != null">pay_rule,</if>
	        <if test="ttPayType != null">tt_pay_type,</if>
	        <if test="ttPayRule != null">tt_pay_rule,</if>
	        <if test="miniId != null">mini_id,</if>
	        <if test="miniAppId != null">mini_app_id,</if>
	        <if test="officialId != null">official_id,</if>
	        <if test="advUserId != null">adv_user_id,</if>
	        <if test="advMediaId != null">adv_media_id,</if>
	        <if test="advVersion != null">adv_version,</if>
	        <if test="appType != null">app_type,</if>
	        <if test="linkTitle != null">link_title,</if>
	        <if test="retailId != null">retail_id,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time,</if>
	        <if test="adGroundId != null">ad_ground_id,</if>
	        <if test="linkType != null">link_type,</if>
	        <if test="linkSubType != null">link_sub_type,</if>
	        <if test="linkPlatform != null">link_platform,</if>
	        <if test="keepType != null">keep_type,</if>
	        <if test="keepId != null">keep_id,</if>
	        <if test="startNumGlobal != null">start_num_global,</if>
	        <if test="coinPerGlobal != null">coin_per_global,</if>
	        <if test="followNumGlobal != null">follow_num_global,</if>
	        <if test="baiduEventType != null">baidu_event_type,</if>
	        <if test="accordPlan != null">accord_plan,</if>
			<if test="ttSplitPlatform != null">tt_split_platform,</if>
			<if test="ttSplitXman != null">tt_split_xman,</if>
			<if test="ttSplitMcn != null">tt_split_mcn,</if>
			<if test="feeFlag != null">fee_flag,</if>
			<if test="mixFlag != null">mix_flag,</if>
			<if test="wxFinderId != null and wxFinderId.length()>0">wx_finder_id,</if>
			<if test="advUnlockFlag != null">adv_unlock_flag,</if>
			<if test="advUnlockNum != null">adv_unlock_num,</if>
			<if test="skipSeries != null">skip_series,</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="contentType != null">#{contentType},</if>
	        <if test="linkName != null">#{linkName},</if>
	        <if test="promoteLink != null">#{promoteLink},</if>
	        <if test="shortLink != null">#{shortLink},</if>
	        <if test="dramaId != null">#{dramaId},</if>
	        <if test="seriesId != null">#{seriesId},</if>
	        <if test="backType != null">#{backType},</if>
	        <if test="backRule != null">#{backRule},</if>
	        <if test="payType != null">#{payType},</if>
	        <if test="payRule != null">#{payRule},</if>
	        <if test="ttPayType != null">#{ttPayType},</if>
	        <if test="ttPayRule != null">#{ttPayRule},</if>
	        <if test="miniId != null">#{miniId},</if>
	        <if test="miniAppId != null">#{miniAppId},</if>
	        <if test="officialId != null">#{officialId},</if>
	        <if test="advUserId != null">#{advUserId},</if>
	        <if test="advMediaId != null">#{advMediaId},</if>
	        <if test="advVersion != null">#{advVersion},</if>
	        <if test="appType != null">#{appType},</if>
	        <if test="linkTitle != null">#{linkTitle},</if>
	        <if test="retailId != null">#{retailId},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime},</if>
	        <if test="adGroundId != null">#{adGroundId},</if>
	        <if test="linkType != null">#{linkType},</if>
	        <if test="linkSubType != null">#{linkSubType},</if>
	        <if test="linkPlatform != null">#{linkPlatform},</if>
	        <if test="keepType != null">#{keepType},</if>
	        <if test="keepId != null">#{keepId},</if>
	        <if test="startNumGlobal != null">#{startNumGlobal},</if>
	        <if test="coinPerGlobal != null">#{coinPerGlobal},</if>
	        <if test="followNumGlobal != null">#{followNumGlobal},</if>
	        <if test="baiduEventType != null">#{baiduEventType},</if>
	        <if test="accordPlan != null">#{accordPlan},</if>
			<if test="ttSplitPlatform != null">#{ttSplitPlatform},</if>
			<if test="ttSplitXman != null">#{ttSplitXman},</if>
			<if test="ttSplitMcn != null">#{ttSplitMcn},</if>
			<if test="feeFlag != null">#{feeFlag},</if>
			<if test="mixFlag != null">#{mixFlag},</if>
			<if test="wxFinderId != null and wxFinderId.length()>0">#{wxFinderId},</if>
			<if test="advUnlockFlag != null">#{advUnlockFlag},</if>
			<if test="advUnlockNum != null">#{advUnlockNum},</if>
			<if test="skipSeries != null">#{skipSeries},</if>
        </trim>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastLinkPO">
        update fast_link
        <set>
         	<if test="contentType != null" >
				content_type = #{contentType},
            </if>
         	<if test="linkName != null" >
               link_name = #{linkName},
            </if>
         	<if test="promoteLink != null" >
               promote_link = #{promoteLink},
            </if>
         	<if test="shortLink != null" >
               short_link = #{shortLink},
            </if>
         	<if test="dramaId != null" >
               drama_id = #{dramaId},
            </if>
         	<if test="seriesId != null" >
               series_id = #{seriesId},
            </if>
         	<if test="backType != null" >
               back_type = #{backType},
            </if>
         	<if test="backRule != null" >
               back_rule = #{backRule},
            </if>
         	<if test="payType != null" >
               pay_type = #{payType},
            </if>
         	<if test="payRule != null" >
               pay_rule = #{payRule},
            </if>
         	<if test="ttPayType != null" >
               tt_pay_type = #{ttPayType},
            </if>
         	<if test="ttPayRule != null" >
               tt_pay_rule = #{ttPayRule},
            </if>
         	<if test="miniId != null" >
               mini_id = #{miniId},
            </if>
         	<if test="miniAppId != null" >
               mini_app_id = #{miniAppId},
            </if>
         	<if test="officialId != null" >
               official_id = #{officialId},
            </if>
         	<if test="advUserId != null" >
               adv_user_id = #{advUserId},
            </if>
         	<if test="advMediaId != null" >
               adv_media_id = #{advMediaId},
            </if>
         	<if test="appType != null" >
               app_type = #{appType},
            </if>
         	<if test="linkTitle != null" >
               link_title = #{linkTitle},
            </if>
         	<if test="roadId != null" >
				road_id = #{roadId},
            </if>
         	<if test="retailId != null" >
               retail_id = #{retailId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
         	<if test="adGroundId != null" >
				ad_ground_id = #{adGroundId},
            </if>
         	<if test="startNumGlobal != null" >
				start_num_global = #{startNumGlobal},
            </if>
         	<if test="coinPerGlobal != null" >
				coin_per_global = #{coinPerGlobal},
            </if>
         	<if test="followNumGlobal != null" >
				follow_num_global = #{followNumGlobal},
            </if>
         	<if test="baiduEventType != null" >
				baidu_event_type = #{baiduEventType},
            </if>
         	<if test="accordPlan != null" >
				accord_plan = #{accordPlan},
            </if>
         	<if test="keepType != null" >
				keep_type = #{keepType},
            </if>
         	<if test="keepId != null" >
				keep_id = #{keepId},
            </if>
			<if test="ttSplitPlatform != null" >
				`tt_split_platform` = #{ttSplitPlatform},
			</if>
			<if test="ttSplitXman != null" >
				`tt_split_xman` = #{ttSplitXman},
			</if>
			<if test="ttSplitMcn != null" >
				`tt_split_mcn` = #{ttSplitMcn},
			</if>
			<if test="feeFlag != null" >
				`fee_flag` = #{feeFlag},
			</if>
			<if test="mixFlag != null" >
				`mix_flag` = #{mixFlag},
			</if>
			<if test="wxFinderId != null and wxFinderId.length()>0" >
				`wx_finder_id` = #{wxFinderId},
			</if>
			<if test="advUnlockFlag != null" >
				`adv_unlock_flag` = #{advUnlockFlag},
			</if>
			<if test="advUnlockNum != null" >
				`adv_unlock_num` = #{advUnlockNum},
			</if>
			<if test="skipSeries != null" >
				`skip_series` = #{skipSeries},
			</if>
	    </set>
        where id = #{id}
	</update>
	
	<update id="updateTtBindById" parameterType="FastLinkPO">
        update fast_link
        set tt_bind_flag = #{ttBindFlag}
        ,update_time = #{updateTime}
        where
        	id = #{id}
	</update>


	<select id="queryFreeLinkList" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="com.fast.vo.promote.FastLinkQueryVO">
		<include refid="FastLink_columns" />
			,(select start_num from fast_fee_rule where link_id = t.id) as start_num,
			(select follow_num from fast_fee_rule where link_id = t.id) as follow_num,
			(select coin_per from fast_fee_rule where link_id = t.id) as coin_per,
			(select mini_name from fast_mini where id = t.mini_id ) as mini_name,
			(select type from fast_mini where id = t.mini_id ) as type,
			(select an_unlock_num from fast_link_ext where id = t.id) as an_unlock_num,
			(select ios_unlock_num from fast_link_ext where id = t.id) as ios_unlock_num,
			( SELECT drama_name FROM fast_drama WHERE id = t.drama_id ) AS dramaName,
			( SELECT series_num FROM fast_drama_series WHERE id = t.series_id ) AS seriesNum,
			( SELECT user_name FROM fast_user WHERE id = t.adv_user_id ) AS advUserName,
			( SELECT retail_name FROM fast_retail WHERE id = t.retail_id ) AS retailName,
			IFNULL(t2.total_watch_member_num,0) AS adWatchMemberNum,
			IFNULL(t2.total_watch_num,0) AS adWatchNum,
			IFNULL(t2.total_unlock_member_num,0) AS adUnlockMemberNum,
			IFNULL(t2.total_unlock_num,0) AS adUnlockNum,
			IFNULL(t2.ad_watch_member_num,0) AS adWatchMemberNumDay,
			IFNULL(t2.ad_watch_num,0) AS adWatchNumDay,
			IFNULL(t2.ad_unlock_member_num,0) AS adUnlockMemberNumDay,
			IFNULL(t2.ad_unlock_num,0) AS adUnlockNumDay,
			IFNULL(t2.total_back_user,0) as backUserNum,
			IFNULL(t2.color_member_day,0) as colorMemberDay,
			IFNULL(t2.color_member_all,0) as colorMemberAll,
  		    IFNULL(t3.num_all,0) as numAll,
		    IFNULL(t3.num_day,0) as numDay,
			IFNULL(t3.money_day,0) as rechargeMoney,
			IFNULL(t3.num_day_order,0) as rechargeNum,
			IFNULL(t3.num_day_rech,0) as rechargeMemberNum,
			IFNULL(t3.money_all,0) as totalRechargeMoney,
			IFNULL(t3.num_all_order,0) as totalRechargeNum,
			IFNULL(t3.num_all_rech,0) as totalRechargeMemberNum,
			IFNULL(t3.cost_day,0.00) AS costDay,
			IFNULL(t3.cost_all,0) as costAll,
			kb.batch_name keepBatchName
		FROM
		fast_link t
		LEFT JOIN (select * from fast_statis_link_free where statis_date = #{statisDate} ) t2 ON t.id = t2.link_id
        LEFT JOIN fast_statis_link t3 on t2.link_id = t3.link_id and t2.statis_date = t3.statis_date
        left join fast_fee_keep_batch kb on kb.id = t.keep_id
		<where>
			t.fee_flag = 2
			<if test="beginTime != null and endTime != null">
				and t.`create_time` &gt;= #{beginTime} and t.`create_time` &lt;= #{endTime}
			</if>
			<if test="inPageDramaName != null and inPageDramaName != ''">
				and exists (select * from fast_drama where id = t.drama_id and drama_name like CONCAT('%',#{inPageDramaName},'%') )
			</if>
			<if test="linkName != null">
				<if test="id != null">
					and (t.`id` = #{id} or t.`link_name`  like concat('%',#{linkName},'%'))
				</if>
				<if test="id == null">
					and t.`link_name`  like concat('%',#{linkName},'%')
				</if>
			</if>
			<if test="retailName != null and retailName != ''">
				and exists (select * from fast_retail where id = t.retail_id and retail_name = #{retailName} )
			</if>
			<if test="advUserName != null and advUserName != ''">
				and exists (select * from fast_user where id = t.adv_user_id and user_name = #{advUserName} )
			</if>
			<if test="advUserIds != null and advUserIds != ''">
				and exists (select * from fast_user where id = t.adv_user_id and find_in_set(id,#{advUserIds}) )
			</if>
			<if test="advUserId != null and advUserId !=0">
				and t.adv_user_id = #{advUserId}
			</if>
			<if test="officialId != null and officialId != 0">
				and t.`official_id` = #{officialId}
			</if>
			<if test="retailId != null and retailId != 0">
				and t.`retail_id` = #{retailId}
			</if>
			<if test="miniIds != null and miniIds.length > 0">
				and find_in_set(t.mini_id,#{miniIds})
			</if>
			<if test="dramaIds != null and dramaIds.length > 0">
				and find_in_set(t.drama_id,#{dramaIds})
			</if>
			<if test="linkSubTypes != null and linkSubTypes.length > 0">
				and find_in_set(t.link_sub_type,#{linkSubTypes})
			</if>
			<if test="contentType != null">
				and t.content_type = #{contentType}
			</if>
			<if test="linkType != null">
				and t.link_type = #{linkType}
			</if>
			<if test="linkPlatform != null">
				and t.link_platform = #{linkPlatform}
			</if>
			<if test="advMediaId != null">
				and t.adv_media_id = #{advMediaId}
			</if>
		</where>
		group by t.id
		<if test="orderNumAll != null and orderNumAll eq 1">
			order by t3.num_all desc
		</if>
		<if test="orderNumAll != null and orderNumAll eq 2">
			order by t3.num_all ASC
		</if>
		<if test="orderNumAll == null">
			<if test="orderMoneyAll != null and orderMoneyAll eq 1">
				order by t2.total_ad_income desc
			</if>
			<if test="orderMoneyAll != null and orderMoneyAll eq 2">
				order by t2.total_ad_income ASC
			</if>
			<if test="orderMoneyAll == null">
				<if test="orderMoneyAllDay != null and orderMoneyAllDay eq 1">
					order by t2.ad_income desc
				</if>
				<if test="orderMoneyAllDay != null and orderMoneyAllDay eq 2">
					order by t2.ad_income ASC
				</if>
				<if test="orderMoneyAllDay == null">
					<if test="orderProfitAll != null and orderProfitAll eq 1">
						order by (t2.total_ad_income-t3.cost_all) desc
					</if>
					<if test="orderProfitAll != null and orderProfitAll eq 2">
						order by (t2.total_ad_income-t3.cost_all) ASC
					</if>
					<if test="orderProfitAll == null">
						<if test="queryType eq null"><!-- 排序方式-1倒序 2正序 按创建时间  -->
							order by t.`create_time` desc
						</if>
						<if test="queryType != null and queryType eq 1"><!-- 排序方式-1倒序 2正序 按创建时间  -->
							order by t.`create_time` desc
						</if>
						<if test="queryType != null and queryType eq 2"><!-- 排序方式-1倒序 2正序 按创建时间  -->
							order by t.`create_time` ASC
						</if>
					</if>
				</if>
			</if>
		</if>
	</select>

   <select id="queryAddMemberIds" parameterType="FastLinkPO" resultType="Long">
	   select DISTINCT(t.member_id)
	   from fast_member_link t
	   <where>
		   (
		   (t.`first_link_id` = #{id}
		   and t.`first_link_time` &gt;= #{statisDateStart}
		   and t.`first_link_time` &lt;= #{statisDateEnd} )
		   or
		   (t.`last_link_id` = #{id}
		   and t.`last_link_time` &gt;= #{statisDateStart}
		   and t.`last_link_time` &lt;= #{statisDateEnd} )
		   )
	   </where>
   </select>

	<select id="queryTotalAddMemberCount" parameterType="FastLinkPO" resultType="Integer">
		select count(DISTINCT(t.member_id))
		from fast_member_link t
		<where>
			((t.`first_link_id` = #{id}) or (t.`last_link_id` = #{id}))
		</where>
   </select>

	<select id="queryFreeLinkStatisList" parameterType = "com.fast.po.promote.FastLinkPO"
			resultType="com.fast.po.promote.FastLinkPO">
		<include refid="FastLink_columns"/>
		,(select IFNULL(cost_day,0.00) from fast_statis_link where statis_date = #{statisDate} and link_id = t.id) as costDay
		from fast_link t
		where t.fee_flag = 2
	</select>

	<select id="queryAddLinkList" resultType="com.fast.po.promote.FastLinkPO">
		<include refid="FastLink_columns"/>
		FROM
		   fast_link t
		WHERE t.id > #{id}
		limit 1000;
	</select>

	<select id="queryChangedLinkList" resultType="com.fast.po.promote.FastLinkPO">
		<include refid="FastLink_columns"/>
		FROM
		fast_link t
		WHERE find_in_set(t.id,#{changeLinkIds})
	</select>

</mapper>
