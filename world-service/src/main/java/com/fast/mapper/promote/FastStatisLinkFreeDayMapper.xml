<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastStatisLinkFreeDayMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStatisLinkFreeDay_columns">
		select t.`stat_date`,t.`link_id`,t.`register_new`,t.`register_user`,t.`click_ad_user`,t.`recharge_new`,t.`recharge_new_back`,t.`key_action_rate`,t.`back_rate`,t.`money_day_new`,t.`money_day`,t.`arpu`,t.`arppu`,t.`num_recharge`,t.`cost`,t.`ad_income_show_num`, t.`ad_income_member_num`, t.`ad_show_member_num`, t.`ad_show_num`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStatisLinkFreeDayPO">
		<include refid="FastStatisLinkFreeDay_columns" />
	    from fast_statis_link_free_day t
        <where>
	        t.`stat_date` = #{statDate} and
	        t.`link_id` = #{linkId}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStatisLinkFreeDayPO" resultType="FastStatisLinkFreeDayPO">
		<include refid="FastStatisLinkFreeDay_columns" />
	    from fast_statis_link_free_day t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStatisLinkFreeDayPO" resultType="FastStatisLinkFreeDayPO">
		<include refid="FastStatisLinkFreeDay_columns" />
		,if(l.adv_media_id is null, 0, l.adv_media_id) mediaType
	    from fast_statis_link_free_day t
		left join fast_link l on t.link_id=l.id
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`stat_date` desc, t.`link_id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStatisLinkFreeDayPO" resultType="int">
		select count(*)
	    from fast_statis_link_free_day t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="statDate != null">
			and t.`stat_date` = #{statDate}
		</if>
		<if test="linkId != null">
			and t.`link_id` = #{linkId}
		</if>
		<if test="registerNew != null">
			and t.`register_new` = #{registerNew}
		</if>
		<if test="clickAdUser != null">
			and t.`click_ad_user` = #{clickAdUser}
		</if>
		<if test="rechargeNew != null">
			and t.`recharge_new` = #{rechargeNew}
		</if>
		<if test="rechargeNewBack != null">
			and t.`recharge_new_back` = #{rechargeNewBack}
		</if>
		<if test="keyActionRate != null">
			and t.`key_action_rate` = #{keyActionRate}
		</if>
		<if test="backRate != null">
			and t.`back_rate` = #{backRate}
		</if>
		<if test="moneyDayNew != null">
			and t.`money_day_new` = #{moneyDayNew}
		</if>
		<if test="moneyDay != null">
			and t.`money_day` = #{moneyDay}
		</if>
		<if test="arpu != null">
			and t.`arpu` = #{arpu}
		</if>
		<if test="arppu != null">
			and t.`arppu` = #{arppu}
		</if>
		<if test="numRecharge != null">
			and t.`num_recharge` = #{numRecharge}
		</if>
		<if test="cost != null">
			and t.`cost` = #{cost}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 渠道新染色用户统计 -->
	<select id="queryStatRegister" resultType="FastStatisLinkFreeDayPO">
		SELECT last_link_id linkId, count(*) registerNew,
		       count(DISTINCT if(m.create_time &gt;= #{statDateStr} and m.create_time &lt; DATE_ADD(#{statDateStr},INTERVAL 1 day), member_id, null)) registerUser
		FROM `fast_member_link` m force index(last_link_time)
			left join fast_link l on m.last_link_id=l.id
		where last_link_time &gt;= #{statDateStr}
	      and last_link_time &lt; DATE_ADD(#{statDateStr},INTERVAL 1 day)
		  and last_link_id > 0
		  and l.fee_flag = #{feeFlag}
		GROUP BY last_link_id
	</select>

	<!-- 渠道充值统计 -->
	<select id="queryStatRecharge" resultType="FastStatisLinkFreeDayPO">
		select
			t.link_id,
			count(DISTINCT t.member_id) numRecharge,
			count(DISTINCT if(DATE_FORMAT(l.link_time, '%Y-%m-%d') = #{statDateStr} and l.back_state=2,l.member_id,null)) rechargeNewBack,
			count(DISTINCT if(DATE_FORMAT(l.link_time, '%Y-%m-%d') = #{statDateStr}, l.member_id,null)) clickAdUser,
			count(DISTINCT if(DATE_FORMAT(l.link_time, '%Y-%m-%d') = #{statDateStr} and l.ecpm_cost>0,l.member_id,null)) rechargeNew,
			sum(if(DATE_FORMAT(t.link_time, '%Y-%m-%d') = #{statDateStr},t.cost_dec,0)) moneyDayNew,
			sum(t.cost_dec) moneyDay,
			count(if(t.cost_dec>0,t.member_id,null)) adIncomeShowNum,
			count(DISTINCT if(t.cost_dec>0,t.member_id,null)) adIncomeMemberNum,
			count(DISTINCT l.member_id) adShowMemberNum,
			count(l.member_id) adShowNum
		from fast_ecpm_data_tt t left join fast_member_unlock_start_log l on t.start_log_id=l.id
		where t.create_time &gt;= #{statDateStr} 
			and t.create_time &lt; DATE_ADD(#{statDateStr},INTERVAL 1 day)
		GROUP BY t.link_id
	</select>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastStatisLinkFreeDayPO">
        insert into fast_statis_link_free_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="statDate != null">stat_date,</if>
	        <if test="linkId != null">link_id,</if>
	        <if test="registerNew != null">register_new,</if>
	        <if test="clickAdUser != null">click_ad_user,</if>
	        <if test="rechargeNew != null">recharge_new,</if>
	        <if test="rechargeNewBack != null">recharge_new_back,</if>
	        <if test="keyActionRate != null">key_action_rate,</if>
	        <if test="backRate != null">back_rate,</if>
	        <if test="moneyDayNew != null">money_day_new,</if>
	        <if test="moneyDay != null">money_day,</if>
	        <if test="arpu != null">arpu,</if>
	        <if test="arppu != null">arppu,</if>
	        <if test="numRecharge != null">num_recharge,</if>
	        <if test="cost != null">cost,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="statDate != null">#{statDate},</if>
	        <if test="linkId != null">#{linkId},</if>
	        <if test="registerNew != null">#{registerNew},</if>
	        <if test="clickAdUser != null">#{clickAdUser},</if>
	        <if test="rechargeNew != null">#{rechargeNew},</if>
	        <if test="rechargeNewBack != null">#{rechargeNewBack},</if>
	        <if test="keyActionRate != null">#{keyActionRate},</if>
	        <if test="backRate != null">#{backRate},</if>
	        <if test="moneyDayNew != null">#{moneyDayNew},</if>
	        <if test="moneyDay != null">#{moneyDay},</if>
	        <if test="arpu != null">#{arpu},</if>
	        <if test="arppu != null">#{arppu},</if>
	        <if test="numRecharge != null">#{numRecharge},</if>
	        <if test="cost != null">#{cost},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastStatisLinkFreeDayPO">
        insert into fast_statis_link_free_day (
         stat_date, link_id, register_new, click_ad_user, recharge_new, recharge_new_back, key_action_rate, back_rate, money_day_new, money_day, arpu, arppu, num_recharge, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.statDate}, #{item.linkId}, #{item.registerNew}, #{item.clickAdUser}, #{item.rechargeNew}, #{item.rechargeNewBack}, #{item.keyActionRate}, #{item.backRate}, #{item.moneyDayNew}, #{item.moneyDay}, #{item.arpu}, #{item.arppu}, #{item.numRecharge}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStatisLinkFreeDayPO">
        update fast_statis_link_free_day
        <set>
         	<if test="statDate != null" >
               stat_date = #{statDate},
            </if>
         	<if test="linkId != null" >
               link_id = #{linkId},
            </if>
         	<if test="registerNew != null" >
               register_new = #{registerNew},
            </if>
         	<if test="clickAdUser != null" >
               click_ad_user = #{clickAdUser},
            </if>
         	<if test="rechargeNew != null" >
               recharge_new = #{rechargeNew},
            </if>
         	<if test="rechargeNewBack != null" >
               recharge_new_back = #{rechargeNewBack},
            </if>
         	<if test="keyActionRate != null" >
               key_action_rate = #{keyActionRate},
            </if>
         	<if test="backRate != null" >
               back_rate = #{backRate},
            </if>
         	<if test="moneyDayNew != null" >
               money_day_new = #{moneyDayNew},
            </if>
         	<if test="moneyDay != null" >
               money_day = #{moneyDay},
            </if>
         	<if test="arpu != null" >
               arpu = #{arpu},
            </if>
         	<if test="arppu != null" >
               arppu = #{arppu},
            </if>
         	<if test="numRecharge != null" >
               num_recharge = #{numRecharge},
            </if>
         	<if test="cost != null" >
               cost = #{cost},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
		    `stat_date` = #{statDate} and
		    `link_id` = #{linkId}
	</update>

	<!-- 批量新增 -->
	<insert id="insertUpdateBatch" parameterType="FastStatisLinkFreeDayPO">
		insert into fast_statis_link_free_day (
		stat_date, link_id, register_new, register_user, click_ad_user, recharge_new, recharge_new_back, key_action_rate, back_rate, money_day_new, money_day, arpu, arppu, num_recharge, ad_income_show_num, ad_income_member_num, ad_show_member_num, ad_show_num, create_time, update_time
		) values
		<foreach collection="list" item="item" separator=",">
			(
			#{item.statDate}, #{item.linkId}, #{item.registerNew}, #{item.registerUser}, #{item.clickAdUser}, #{item.rechargeNew}, #{item.rechargeNewBack}, #{item.keyActionRate}, #{item.backRate}, #{item.moneyDayNew}, #{item.moneyDay}, #{item.arpu}, #{item.arppu}, #{item.numRecharge}, #{item.adIncomeShowNum}, #{item.adIncomeMemberNum}, #{item.adShowMemberNum}, #{item.adShowNum}, #{item.createTime}, #{item.updateTime}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE
		`register_new`=values(register_new),
		`register_user`=values(register_user),
		`click_ad_user`=values(click_ad_user),
		`recharge_new`=values(recharge_new),
		`recharge_new_back`=values(recharge_new_back),
		`key_action_rate`=values(key_action_rate),
		`back_rate`=values(back_rate),
		`money_day_new`=values(money_day_new),
		`money_day`=values(money_day),
		`arpu`=values(arpu),
		`arppu`=values(arppu),
		`num_recharge`=values(num_recharge),
		`cost`=values(cost),
		`ad_income_show_num`=values(ad_income_show_num),
		`ad_income_member_num`=values(ad_income_member_num),
		`ad_show_member_num`=values(ad_show_member_num),
		`ad_show_num`=values(ad_show_num),
		`update_time`=values(update_time)
	</insert>
</mapper>
