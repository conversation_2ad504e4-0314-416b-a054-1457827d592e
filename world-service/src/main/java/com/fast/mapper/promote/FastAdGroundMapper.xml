<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastAdGroundMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastAdGround_columns">
		select t.`id`,t.`state`,t.`del_flag`,t.`page_name`,t.`button_type`,t.`button_text`,t.`text_text`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastAdGroundPO">
		<include refid="FastAdGround_columns" />
	    from fast_ad_ground t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastAdGroundPO" resultType="FastAdGroundPO">
		<include refid="FastAdGround_columns" />
	    from fast_ad_ground t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastAdGroundPO" resultType="FastAdGroundPO">
		<include refid="FastAdGround_columns" />
			,(select count(*) from fast_link l where l.ad_ground_id = t.id) linkCount
			,(select p.pic_url from fast_ad_ground_page p where p.ad_ground_id = t.id and p.sequence=0 limit 1) pageFirst
			,(select u.user_name from fast_user u where u.id = t.creator_id) creatorName
	    from fast_ad_ground t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastAdGroundPO" resultType="int">
		select count(*)
	    from fast_ad_ground t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="state != null">
			and t.`state` = #{state}
		</if>
		<if test="delFlag != null">
			and t.`del_flag` = #{delFlag}
		</if>
		<if test="pageName != null">
			and t.`page_name` = #{pageName}
		</if>
		<if test="buttonType != null">
			and t.`button_type` = #{buttonType}
		</if>
		<if test="buttonText != null">
			and t.`button_text` = #{buttonText}
		</if>
		<if test="textText != null">
			and t.`text_text` = #{textText}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastAdGroundPO">
        insert into fast_ad_ground
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="state != null">`state`,</if>
	        <if test="delFlag != null">del_flag,</if>
	        <if test="pageName != null">page_name,</if>
	        <if test="buttonType != null">button_type,</if>
	        <if test="buttonText != null">button_text,</if>
	        <if test="textText != null">text_text,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="state != null">#{state},</if>
	        <if test="delFlag != null">#{delFlag},</if>
	        <if test="pageName != null">#{pageName},</if>
	        <if test="buttonType != null">#{buttonType},</if>
	        <if test="buttonText != null">#{buttonText},</if>
	        <if test="textText != null">#{textText},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastAdGroundPO">
        insert into fast_ad_ground (
         state, del_flag, page_name, button_type, button_text, text_text, creator_id, updator_id, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.state}, #{item.delFlag}, #{item.pageName}, #{item.buttonType}, #{item.buttonText}, #{item.textText}, #{item.creatorId}, #{item.updatorId}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastAdGroundPO">
        update fast_ad_ground
        <set>
         	<if test="state != null" >
               `state` = #{state},
            </if>
         	<if test="delFlag != null" >
               del_flag = #{delFlag},
            </if>
         	<if test="pageName != null" >
               page_name = #{pageName},
            </if>
         	<if test="buttonType != null" >
               button_type = #{buttonType},
            </if>
         	<if test="buttonText != null" >
               button_text = #{buttonText},
            </if>
         	<if test="textText != null" >
               text_text = #{textText},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
