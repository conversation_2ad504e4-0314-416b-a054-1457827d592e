/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.po.promote.FastActivityClickPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastActivityClickMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastActivityClickPO
     * @return
     */
    FastActivityClickPO queryById(FastActivityClickPO fastActivityClickPO);

    /**
     * 通过id查询单个对象
     *
     * @param id
     * @return
     */
    FastActivityClickPO queryById(@Param("id") Integer id);

    /**
     * 通过条件查询单个对象
     *
     * @param fastActivityClickPO
     * @return
     */
    FastActivityClickPO queryOne(FastActivityClickPO fastActivityClickPO);

    /**
     * 查询全部
     *
     * @param fastActivityClickPO
     * @return
     */
    List<FastActivityClickPO> queryList(FastActivityClickPO fastActivityClickPO);

    /**
     * 查询全部
     *
     * @param fastActivityClickPO
     * @return
     */
    List<FastActivityClickPO> queryListGroupDate(FastActivityClickPO fastActivityClickPO);

    /**
     * 查询全部
     *
     * @param fastActivityClickPO
     * @return
     */
    FastActivityClickPO queryListGroupMember(FastActivityClickPO fastActivityClickPO);

    /**
     * 查询全部
     *
     * @param fastActivityClickPO
     * @return
     */
    Set<Long> queryMemberIds(FastActivityClickPO fastActivityClickPO);

    /**
     * 查询总数
     *
     * @param fastActivityClickPO
     * @return
     */
    int queryCount(FastActivityClickPO fastActivityClickPO);

    /**
     * 可选新增
     *
     * @param fastActivityClickPO
     * @return
     */
    int insertSelective(FastActivityClickPO fastActivityClickPO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastActivityClickPO> list);

    /**
     * 更新
     *
     * @param fastActivityClickPO
     * @return
     */
    int updateById(FastActivityClickPO fastActivityClickPO);

}
