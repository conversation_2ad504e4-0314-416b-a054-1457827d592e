/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.annotation.Slave;
import com.fast.po.promote.FastMemberLinkPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMemberLinkMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastMemberLinkPO
     * @return
     */
    FastMemberLinkPO queryById(FastMemberLinkPO fastMemberLinkPO);

    FastMemberLinkPO queryAdvByMemberId(FastMemberLinkPO fastMemberLinkPO);

    /**
     * 通过条件查询单个对象
     *
     * @param fastMemberLinkPO
     * @return
     */
    FastMemberLinkPO queryOne(FastMemberLinkPO fastMemberLinkPO);

    /**
     * 查询最后一次染色时间
     *
     * @param memberId
     * @return
     */
    Integer queryLastLinkTime(@Param("memberId") Long memberId);

    Date queryLastLinkTimeDate(@Param("memberId") Long memberId);

    /**
     * 查询最后一次染色时间
     *
     * @param memberId
     * @return
     */
    Date queryLastLinkDate(@Param("memberId") Long memberId);

    /**
     * 查询全部
     *
     * @param fastMemberLinkPO
     * @return
     */
    List<FastMemberLinkPO> queryList(FastMemberLinkPO fastMemberLinkPO);

    @Slave
    List<FastMemberLinkPO> queryActiveLinkList(FastMemberLinkPO fastMemberLinkPO);

    @Slave
    List<FastMemberLinkPO> queryActiveSubLinkList(FastMemberLinkPO fastMemberLinkPO);

    /**
     * 查询总数
     *
     * @param fastMemberLinkPO
     * @return
     */
    int queryCount(FastMemberLinkPO fastMemberLinkPO);

    /**
     * 可选新增
     *
     * @param fastMemberLinkPO
     * @return
     */
    int insertSelective(FastMemberLinkPO fastMemberLinkPO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastMemberLinkPO> list);

    /**
     * 更新
     *
     * @param fastMemberLinkPO
     * @return
     */
    int updateById(FastMemberLinkPO fastMemberLinkPO);


}
