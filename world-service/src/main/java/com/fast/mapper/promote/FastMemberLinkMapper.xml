<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastMemberLinkMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMemberLink_columns">
		select t.`id`, t.`member_id`, t.`ip`, t.`ua`,t.sys_phone, t.`official_id`, t.`first_link_id`, t.`first_link_time`,
			   t.`last_link_id`,t.aid,t.cid,t.promotion_id,t.project_id, t.`last_link_time`, t.`click_id`, t.`create_time`, t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" parameterType="FastMemberLinkPO" resultType="FastMemberLinkPO">
		<include refid="FastMemberLink_columns" />
	    from fast_member_link t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>
	
    <select id="queryAdvByMemberId" parameterType="FastMemberLinkPO" resultType="FastMemberLinkPO">
		select
			t.promotion_id,
			t.project_id,
			la.material_id
		from 
			fast_member_link t
		left join fast_member_link_adv la on la.id = t.member_id
		where t.member_id = #{memberId}
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMemberLinkPO" resultType="FastMemberLinkPO">
		<include refid="FastMemberLink_columns" />
	    from fast_member_link t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--查询最后一次染色时间  -->
	<select id="queryLastLinkTime" parameterType="FastMemberLinkPO" resultType="Integer">
		select DATE_FORMAT(t.`last_link_time`,'%Y%m%d')
	    from fast_member_link t
        <where>
			t.`member_id` = #{memberId}
        </where>
        limit 1
	</select>

	<!--查询最后一次染色时间  -->
	<select id="queryLastLinkTimeDate" parameterType="FastMemberLinkPO" resultType="java.util.Date">
		select t.`last_link_time`
	    from fast_member_link t
        <where>
			t.`member_id` = #{memberId}
        </where>
        limit 1
	</select>

	<!--查询最后一次染色时间  -->
	<select id="queryLastLinkDate" parameterType="FastMemberLinkPO" resultType="Date">
		select t.`last_link_time`
	    from fast_member_link t
        <where>
			t.`member_id` = #{memberId}
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMemberLinkPO" resultType="FastMemberLinkPO">
		<include refid="FastMemberLink_columns" />
	    from fast_member_link t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>
	
	<select id="queryActiveLinkList" parameterType="FastMemberLinkPO" resultType="FastMemberLinkPO">
		select DISTINCT(m.last_link_id)  lastLinkId
		from fast_member_link m where m.last_link_time > #{lastLinkTime}
	</select>

	<select id="queryActiveSubLinkList" parameterType="FastMemberLinkPO" resultType="FastMemberLinkPO">
		select DISTINCT(m.sub_link_id)  lastLinkId
		from fast_member_sub_link m where m.sub_link_time > #{lastLinkTime}
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMemberLinkPO" resultType="int">
		select count(*)
	    from fast_member_link t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="memberId != null">
			and t.`member_id` = #{memberId}
		</if>
		<if test="ip != null">
			and t.`ip` = #{ip}
		</if>
		<if test="ua != null">
			and t.`ua` = #{ua}
		</if>
		<if test="officialId != null">
			and t.`official_id` = #{officialId}
		</if>
		<if test="firstLinkId != null">
			and t.`first_link_id` = #{firstLinkId}
		</if>
		<if test="firstLinkTime != null">
			and t.`first_link_time` = #{firstLinkTime}
		</if>
		<if test="lastLinkId != null">
			and t.`last_link_id` = #{lastLinkId}
		</if>
		<if test="lastLinkTime != null">
			and t.`last_link_time` = #{lastLinkTime}
		</if>
		<if test="clickId != null">
			and t.`click_id` = #{clickId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMemberLinkPO">
        insert ignore into fast_member_link
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="memberId != null">member_id,</if>
	        <if test="ip != null">ip,</if>
	        <if test="ua != null">ua,</if>
	        <if test="sysPhone != null and sysPhone.length > 0">sys_phone,</if>
	        <if test="officialId != null">official_id,</if>
	        <if test="firstLinkId != null">first_link_id,</if>
	        <if test="firstLinkTime != null">first_link_time,</if>
	        <if test="lastLinkId != null">last_link_id,</if>
	        <if test="lastLinkTime != null">last_link_time,</if>
	        <if test="clickId != null">click_id,</if>
	        <if test="aid != null">aid,</if>
	        <if test="cid != null">cid,</if>
	        <if test="promotionId != null">promotion_id,</if>
	        <if test="projectId != null">project_id,</if>
	        <if test="ctype != null">ctype,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="memberId != null">#{memberId},</if>
	        <if test="ip != null">#{ip},</if>
	        <if test="ua != null">#{ua},</if>
	        <if test="sysPhone != null and sysPhone.length > 0">#{sysPhone},</if>
	        <if test="officialId != null">#{officialId},</if>
	        <if test="firstLinkId != null">#{firstLinkId},</if>
	        <if test="firstLinkTime != null">#{firstLinkTime},</if>
	        <if test="lastLinkId != null">#{lastLinkId},</if>
	        <if test="lastLinkTime != null">#{lastLinkTime},</if>
	        <if test="clickId != null">#{clickId},</if>
	        <if test="aid != null">#{aid},</if>
	        <if test="cid != null">#{cid},</if>
	        <if test="promotionId != null">#{promotionId},</if>
	        <if test="projectId != null">#{projectId},</if>
	        <if test="ctype != null">#{ctype},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastMemberLinkPO">
		insert into fast_member_link (member_id, ip, ua, official_id, first_link_id, first_link_time, last_link_id,
									  last_link_time, click_id, create_time, update_time) values
		<foreach collection="list" item="item" separator=",">
			(#{item.memberId}, #{item.ip}, #{item.ua}, #{item.officialId}, #{item.firstLinkId}, #{item.firstLinkTime},
			 #{item.lastLinkId}, #{item.lastLinkTime}, #{item.clickId}, #{item.createTime}, #{item.updateTime})
		</foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastMemberLinkPO">
        update fast_member_link
        <set>
         	<if test="memberId != null" >
               member_id = #{memberId},
            </if>
         	<if test="ip != null" >
               ip = #{ip},
            </if>
         	<if test="ua != null" >
               ua = #{ua},
            </if>
         	<if test="officialId != null" >
               official_id = #{officialId},
            </if>
         	<if test="firstLinkId != null" >
               first_link_id = #{firstLinkId},
            </if>
         	<if test="firstLinkTime != null" >
               first_link_time = #{firstLinkTime},
            </if>
         	<if test="lastLinkId != null" >
               last_link_id = #{lastLinkId},
            </if>
         	<if test="lastLinkTime != null" >
               last_link_time = #{lastLinkTime},
            </if>
         	<if test="clickId != null" >
               click_id = #{clickId},
            </if>
         	<if test="aid != null and aid.length > 0" >
               aid = #{aid},
            </if>
         	<if test="cid != null and cid.length > 0 " >
               cid = #{cid},
            </if>
         	<if test="promotionId != null and promotionId.length > 0 " >
               promotion_id = #{promotionId},
            </if>
         	<if test="projectId != null and projectId.length > 0 " >
               project_id = #{projectId},
            </if>
         	<if test="ctype != null and ctype.length > 0" >
               ctype = #{ctype},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where id = #{id}
	</update>


</mapper>
