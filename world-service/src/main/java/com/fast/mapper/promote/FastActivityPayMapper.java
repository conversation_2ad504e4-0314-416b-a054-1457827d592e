/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.po.promote.FastActivityPayPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastActivityPayMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastActivityPayPO
     * @return
     */
    FastActivityPayPO queryById(FastActivityPayPO fastActivityPayPO);

    /**
     * 通过id查询单个对象
     *
     * @param orderId
     * @return
     */
    FastActivityPayPO queryByOrderId(@Param("orderId") Long orderId);

    /**
     * 通过条件查询单个对象
     *
     * @param fastActivityPayPO
     * @return
     */
    FastActivityPayPO queryOne(FastActivityPayPO fastActivityPayPO);

    /**
     * 查询全部
     *
     * @param fastActivityPayPO
     * @return
     */
    List<FastActivityPayPO> queryList(FastActivityPayPO fastActivityPayPO);

    /**
     * 查询全部
     *
     * @param fastActivityPayPO
     * @return
     */
    List<FastActivityPayPO> queryListGroupDate(FastActivityPayPO fastActivityPayPO);

    /**
     * 查询全部
     *
     * @param fastActivityPayPO
     * @return
     */
    FastActivityPayPO queryListGroupMember(FastActivityPayPO fastActivityPayPO);

    /**
     * 查询总数
     *
     * @param fastActivityPayPO
     * @return
     */
    int queryCount(FastActivityPayPO fastActivityPayPO);

    /**
     * 可选新增
     *
     * @param fastActivityPayPO
     * @return
     */
    int insertSelective(FastActivityPayPO fastActivityPayPO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastActivityPayPO> list);

    /**
     * 更新
     *
     * @param fastActivityPayPO
     * @return
     */
    int updateById(FastActivityPayPO fastActivityPayPO);

    /**
     * 更新
     *
     * @param fastActivityPayPO
     * @return
     */
    int updateByOrderId(FastActivityPayPO fastActivityPayPO);

}
