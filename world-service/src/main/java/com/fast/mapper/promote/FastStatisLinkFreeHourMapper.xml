<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastStatisLinkFreeHourMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStatisLinkFreeHour_columns">
		select t.`stat_date`,t.`link_id`,t.`register_new`,t.`recharge_new`,t.`recharge_new_back`,t.`back_rate`,t.`money_day`,t.`num_recharge`,t.`back_users_24h`,t.`money_users_24h`,t.`cost`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastStatisLinkFreeHourPO">
		<include refid="FastStatisLinkFreeHour_columns" />
	    from fast_statis_link_free_hour t
        <where>
	        t.`stat_date` = #{statDate} and
	        t.`link_id` = #{linkId}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastStatisLinkFreeHourPO" resultType="FastStatisLinkFreeHourPO">
		<include refid="FastStatisLinkFreeHour_columns" />
	    from fast_statis_link_free_hour t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastStatisLinkFreeHourPO" resultType="FastStatisLinkFreeHourPO">
		<include refid="FastStatisLinkFreeHour_columns" />
		,if(l.adv_media_id is null, 0, l.adv_media_id) mediaType
	    from fast_statis_link_free_hour t
		left join fast_link l on t.link_id=l.id
		<where>
        	<include refid="whereSQL" />
        </where>
		order by t.`stat_date` desc, t.`link_id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastStatisLinkFreeHourPO" resultType="int">
		select count(*)
	    from fast_statis_link_free_hour t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="statDate != null">
			and t.`stat_date` = #{statDate}
		</if>
		<if test="statDateStr != null and statDateStr.length()>0">
			and t.`stat_date` like '${statDateStr}%'
		</if>
		<if test="linkId != null">
			and t.`link_id` = #{linkId}
		</if>
		<if test="registerNew != null">
			and t.`register_new` = #{registerNew}
		</if>
		<if test="rechargeNew != null">
			and t.`recharge_new` = #{rechargeNew}
		</if>
		<if test="rechargeNewBack != null">
			and t.`recharge_new_back` = #{rechargeNewBack}
		</if>
		<if test="backRate != null">
			and t.`back_rate` = #{backRate}
		</if>
		<if test="moneyDay != null">
			and t.`money_day` = #{moneyDay}
		</if>
		<if test="numRecharge != null">
			and t.`num_recharge` = #{numRecharge}
		</if>
		<if test="backUsers24h != null">
			and t.`back_users_24h` = #{backUsers24h}
		</if>
		<if test="moneyUsers24h != null">
			and t.`money_users_24h` = #{moneyUsers24h}
		</if>
		<if test="cost != null">
			and t.`cost` = #{cost}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
		<if test="updateTimeStart != null">
			and t.`update_time` &gt;= #{updateTimeStart}
		</if>
		<if test="updateTimeEnd != null">
			and t.`update_time` &lt;= #{updateTimeEnd}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="statDate" parameterType="FastStatisLinkFreeHourPO">
        insert into fast_statis_link_free_hour
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="registerNew != null">register_new,</if>
	        <if test="rechargeNew != null">recharge_new,</if>
	        <if test="rechargeNewBack != null">recharge_new_back,</if>
	        <if test="backRate != null">back_rate,</if>
	        <if test="moneyDay != null">money_day,</if>
	        <if test="numRecharge != null">num_recharge,</if>
	        <if test="backUsers24h != null">back_users_24h,</if>
	        <if test="moneyUsers24h != null">money_users_24h,</if>
	        <if test="cost != null">cost,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="registerNew != null">#{registerNew},</if>
	        <if test="rechargeNew != null">#{rechargeNew},</if>
	        <if test="rechargeNewBack != null">#{rechargeNewBack},</if>
	        <if test="backRate != null">#{backRate},</if>
	        <if test="moneyDay != null">#{moneyDay},</if>
	        <if test="numRecharge != null">#{numRecharge},</if>
	        <if test="backUsers24h != null">#{backUsers24h},</if>
	        <if test="moneyUsers24h != null">#{moneyUsers24h},</if>
	        <if test="cost != null">#{cost},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="statDate" parameterType="FastStatisLinkFreeHourPO">
        insert into fast_statis_link_free_hour (
         register_new, recharge_new, recharge_new_back, back_rate, money_day, num_recharge, back_users_24h, money_users_24h, cost, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.registerNew}, #{item.rechargeNew}, #{item.rechargeNewBack}, #{item.backRate}, #{item.moneyDay}, #{item.numRecharge}, #{item.backUsers24h}, #{item.moneyUsers24h}, #{item.cost}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertUpdateBatch" parameterType="FastStatisLinkFreeHourPO">
        insert into fast_statis_link_free_hour (
			stat_date, link_id, register_new, recharge_new, recharge_new_back, back_rate, money_day, num_recharge, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.statDate}, #{item.linkId}, #{item.registerNew}, #{item.rechargeNew}, #{item.rechargeNewBack}, #{item.backRate}, #{item.moneyDay}, #{item.numRecharge}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
		ON DUPLICATE KEY UPDATE
		`register_new`=values(register_new),
		`recharge_new`=values(recharge_new),
		`recharge_new_back`=values(recharge_new_back),
		`back_rate`=values(back_rate),
		`money_day`=values(money_day),
		`num_recharge`=values(num_recharge),
		`cost`=values(cost),
		`update_time`=values(update_time)
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStatisLinkFreeHourPO">
        update fast_statis_link_free_hour
        <set>
         	<if test="registerNew != null" >
               register_new = #{registerNew},
            </if>
         	<if test="rechargeNew != null" >
               recharge_new = #{rechargeNew},
            </if>
         	<if test="rechargeNewBack != null" >
               recharge_new_back = #{rechargeNewBack},
            </if>
         	<if test="backRate != null" >
               back_rate = #{backRate},
            </if>
         	<if test="moneyDay != null" >
               money_day = #{moneyDay},
            </if>
         	<if test="numRecharge != null" >
               num_recharge = #{numRecharge},
            </if>
         	<if test="backUsers24h != null" >
               back_users_24h = #{backUsers24h},
            </if>
         	<if test="moneyUsers24h != null" >
               money_users_24h = #{moneyUsers24h},
            </if>
         	<if test="cost != null" >
               cost = #{cost},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where stat_date = #{statDate} AND link_id = #{linkId}
	</update>

	<!-- 渠道新染色用户统计 -->
	<select id="queryStatData" resultType="FastStatisLinkFreeHourPO">
		select t.statDate,t.linkId,t.registerNew,t1.moneyDay,t1.rechargeNewBack,t1.rechargeNew,t1.numRecharge
		from (
			SELECT DATE_FORMAT(last_link_time, '%Y-%m-%d %H:00:00') statDate,
				 last_link_id linkId,
				 count(DISTINCT m.member_id) registerNew
			FROM `fast_member_link` m join fast_link l on m.last_link_id=l.id and l.fee_flag=2
			where last_link_time &gt;= #{statDateStr}
			  and last_link_time &lt; DATE_ADD(#{statDateStr},INTERVAL 1 day)
			GROUP BY last_link_id,DATE_FORMAT(last_link_time, '%Y-%m-%d %H:00:00')
		) t join (
			select t.link_id linkId,DATE_FORMAT(t.link_time, '%Y-%m-%d %H:00:00') statDate,
				sum(if(t.cost_dec>0,cost_dec,0)) moneyDay,
				count(DISTINCT if(l.back_state=2,l.member_id,null)) rechargeNewBack,
				count(DISTINCT if(t.cost_dec>0,t.member_id,null)) rechargeNew,
				count(DISTINCT t.member_id) numRecharge
			from fast_ecpm_data_tt t 
			left join fast_member_unlock_start_log l on t.start_log_id=l.id
			where t.link_time &gt;= #{statDateStr}
			and t.link_time &lt; DATE_ADD(#{statDateStr},INTERVAL 1 day)
			and t.create_time &lt; DATE_ADD(#{statDateStr},INTERVAL 1 day)
			group by t.link_id,DATE_FORMAT(link_time, '%Y-%m-%d %H:00:00')
		) t1 on t.linkId=t1.linkId and t.statDate=t1.statDate
	</select>

	<!-- 渠道新染色用户统计 -->
	<select id="queryStatHour" resultType="FastStatisLinkFreeHourPO">
		select t.link_id linkId, DATE_FORMAT(t.create_time, '%H') h,
			count(DISTINCT if(l.back_state=2,l.member_id,null)) rechargeNewBack,
			sum(if(t.cost_dec>0,t.cost_dec,0)) moneyHour
		from fast_ecpm_data_tt t 
		left join fast_member_unlock_start_log l on t.start_log_id=l.id
		where t.link_time &gt;= #{statDate}
			and t.link_time &lt; DATE_ADD(DATE_FORMAT(#{statDate},'%Y-%m-%d %H:00:00'), INTERVAL 1 hour)
			and t.link_id=#{linkId}
			and t.create_time &lt; DATE_ADD(#{statDate}, INTERVAL 1 day)
		group by DATE_FORMAT(t.create_time, '%Y-%m-%d %H')
	</select>
</mapper>
