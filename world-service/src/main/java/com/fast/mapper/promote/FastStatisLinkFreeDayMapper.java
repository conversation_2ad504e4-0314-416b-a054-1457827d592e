/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.annotation.Slave;
import com.fast.po.promote.FastStatisLinkFreeDayPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStatisLinkFreeDayMapper {

    // 通过id查询单个对象
    FastStatisLinkFreeDayPO queryById(FastStatisLinkFreeDayPO entity);

    // 通过条件查询单个对象
    FastStatisLinkFreeDayPO queryOne(FastStatisLinkFreeDayPO entity);

    // 查询全部
    List<FastStatisLinkFreeDayPO> queryList(FastStatisLinkFreeDayPO entity);

    // 查询总数
    int queryCount(FastStatisLinkFreeDayPO entity);

    // 免费注册用户数统计
    @Slave
    List<FastStatisLinkFreeDayPO> queryStatRegister(FastStatisLinkFreeDayPO entity);

    // 免费充值数据统计
    @Slave
    List<FastStatisLinkFreeDayPO> queryStatRecharge(FastStatisLinkFreeDayPO entity);

    int insertUpdateBatch(List<FastStatisLinkFreeDayPO> list);
}
