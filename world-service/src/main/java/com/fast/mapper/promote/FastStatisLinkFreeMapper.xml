<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastStatisLinkFreeMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastStatisLinkFree_columns">
		select  t.id,
			    t.statis_date,
				t.link_id,
				t.ad_income,
				t.ad_watch_member_num,
				t.ad_watch_num,
				t.ad_unlock_member_num,
				t.ad_unlock_num,
				t.total_watch_member_num,
				t.total_unlock_member_num,
				t.total_watch_num,
				t.total_unlock_num,
				t.total_ad_income,
				t.add_member_inocme,
				t.add_member_watch_num,
				t.add_member_watch_amount,
				t.add_member_recharge,
				t.today_back_user,
				t.total_back_user,
				t.ad_watch_member_d60,
				t.ad_income_d60,
				t.return_ratio_d60,
				t.color_member_day,
				t.color_member_all,
				t.convert_member_day,
				t.convert_member_all
</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="com.fast.po.promote.FastStatisLinkFreePO">
		<include refid="FastStatisLinkFree_columns" />
	    from fast_statis_link_free t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="com.fast.po.promote.FastStatisLinkFreePO" resultType="com.fast.po.promote.FastStatisLinkFreePO">
		<include refid="FastStatisLinkFree_columns" />
	    from fast_statis_link_free t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="com.fast.po.promote.FastStatisLinkFreePO" resultType="com.fast.po.promote.FastStatisLinkFreePO">
		<include refid="FastStatisLinkFree_columns" />
	    from fast_statis_link_free t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="com.fast.po.promote.FastStatisLinkFreePO" resultType="int">
		select count(*)
	    from fast_statis_link_free t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="statisDate != null">
			and t.`statis_date` = #{statisDate}
		</if>
		<if test="linkId != null">
			and t.`link_id` = #{linkId}
		</if>
		<if test="adIncome != null">
			and t.`ad_income` = #{adIncome}
		</if>
		<if test="adWatchMemberNum != null">
			and t.`ad_watch_member_num` = #{adWatchMemberNum}
		</if>
		<if test="adWatchNum != null">
			and t.`ad_watch_num` = #{adWatchNum}
		</if>
		<if test="adUnlockMemberNum != null">
			and t.`ad_unlock_member_num` = #{adUnlockMemberNum}
		</if>
		<if test="adUnlockNum != null">
			and t.`ad_unlock_num` = #{adUnlockNum}
		</if>
		<if test="totalWatchMemberNum != null">
			and t.`total_watch_member_num` = #{totalWatchMemberNum}
		</if>
		<if test="totalUnlockMemberNum != null">
			and t.`total_unlock_member_num` = #{totalUnlockMemberNum}
		</if>
		<if test="addMemberInocme != null">
			and t.`add_member_inocme` = #{addMemberInocme}
		</if>
		<if test="addMemberWatchNum != null">
			and t.`add_member_watch_num` = #{addMemberWatchNum}
		</if>
		<if test="addMemberWatchAmount != null">
			and t.`add_member_watch_amount` = #{addMemberWatchAmount}
		</if>
		<if test="adWatchMemberD60 != null">
			and t.`ad_watch_member_d60` = #{adWatchMemberD60}
		</if>
		<if test="adIncomeD60 != null">
			and t.`ad_income_d60` = #{adIncomeD60}
		</if>
		<if test="returnRatioD60 != null">
			and t.`return_ratio_d60` = #{returnRatioD60}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.fast.po.promote.FastStatisLinkFreePO">
        insert into fast_statis_link_free
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="statisDate != null">statis_date,</if>
	        <if test="linkId != null">link_id,</if>
	        <if test="adIncome != null">ad_income,</if>
	        <if test="adWatchMemberNum != null">ad_watch_member_num,</if>
	        <if test="adWatchNum != null">ad_watch_num,</if>
	        <if test="adUnlockMemberNum != null">ad_unlock_member_num,</if>
	        <if test="adUnlockNum != null">ad_unlock_num,</if>
	        <if test="totalWatchMemberNum != null">total_watch_member_num,</if>
	        <if test="totalUnlockMemberNum != null">total_unlock_member_num,</if>
			<if test="totalWatchNum != null">total_watch_num,</if>
			<if test="totalUnlockNum != null">total_unlock_num,</if>
			<if test="totalAdIncome != null">total_ad_income,</if>
			<if test="addMemberInocme != null">add_member_inocme,</if>
			<if test="addMemberWatchNum != null">add_member_watch_num,</if>
			<if test="addMemberWatchAmount != null">add_member_watch_amount,</if>
			<if test="addMemberRecharge != null">add_member_recharge,</if>
			<if test="todayBackUser != null">today_back_user,</if>
			<if test="totalBackUser != null">total_back_user,</if>
	        <if test="adWatchMemberD60 != null">ad_watch_member_d60,</if>
	        <if test="adIncomeD60 != null">ad_income_d60,</if>
	        <if test="returnRatioD60 != null">return_ratio_d60,</if>
            <if test="colorMemberDay != null">color_member_day,</if>
			<if test="colorMemberAll != null">color_member_all,</if>
			<if test="convertMemberDay != null">convert_member_day,</if>
			<if test="convertMemberAll != null">convert_member_all,</if>
		</trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="statisDate != null">#{statisDate},</if>
			<if test="linkId != null">#{linkId},</if>
			<if test="adIncome != null">#{adIncome},</if>
			<if test="adWatchMemberNum != null">#{adWatchMemberNum},</if>
			<if test="adWatchNum != null">#{adWatchNum},</if>
			<if test="adUnlockMemberNum != null">#{adUnlockMemberNum},</if>
			<if test="adUnlockNum != null">#{adUnlockNum},</if>
			<if test="totalWatchMemberNum != null">#{totalWatchMemberNum},</if>
			<if test="totalUnlockMemberNum != null">#{totalUnlockMemberNum},</if>
			<if test="totalWatchNum != null">#{totalWatchNum},</if>
			<if test="totalUnlockNum != null">#{totalUnlockNum},</if>
			<if test="totalAdIncome != null">#{totalAdIncome},</if>
			<if test="addMemberInocme != null">#{addMemberInocme},</if>
			<if test="addMemberWatchNum != null">#{addMemberWatchNum},</if>
			<if test="addMemberWatchAmount != null">#{addMemberWatchAmount},</if>
			<if test="addMemberRecharge != null">#{addMemberRecharge},</if>
			<if test="todayBackUser != null">#{todayBackUser},</if>
			<if test="totalBackUser != null">#{totalBackUser},</if>
			<if test="adWatchMemberD60 != null">#{adWatchMemberD60},</if>
			<if test="adIncomeD60 != null">#{adIncomeD60},</if>
			<if test="returnRatioD60 != null">#{returnRatioD60},</if>
			<if test="colorMemberDay != null">#{colorMemberDay},</if>
			<if test="colorMemberAll != null">#{colorMemberAll},</if>
			<if test="convertMemberDay != null">#{convertMemberDay},</if>
			<if test="convertMemberAll != null">#{convertMemberAll}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastStatisLinkFreePO">
        insert into fast_statis_link_free (
         statis_date, link_id, ad_income,  ad_watch_member_num, ad_watch_num, ad_unlock_member_num, ad_unlock_num, total_watch_member_num, total_unlock_member_num, add_member_inocme, add_member_watch_num, add_member_watch_amount, ad_watch_member_d60, ad_income_d60, return_ratio_d60
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.statisDate}, #{item.linkId}, #{item.adIncome},  #{item.adWatchMemberNum}, #{item.adWatchNum}, #{item.adUnlockMemberNum}, #{item.adUnlockNum}, #{item.totalWatchMemberNum}, #{item.totalUnlockMemberNum}, #{item.addMemberInocme}, #{item.addMemberWatchNum}, #{item.addMemberWatchAmount}, #{item.adWatchMemberD60}, #{item.adIncomeD60}, #{item.returnRatioD60}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastStatisLinkFreePO">
        update fast_statis_link_free
        <set>
         	<if test="statisDate != null" >
               statis_date = #{statisDate},
            </if>
         	<if test="linkId != null" >
               link_id = #{linkId},
            </if>
         	<if test="adIncome != null" >
               ad_income = #{adIncome},
            </if>
         	<if test="adWatchMemberNum != null" >
               ad_watch_member_num = #{adWatchMemberNum},
            </if>
         	<if test="adWatchNum != null" >
               ad_watch_num = #{adWatchNum},
            </if>
         	<if test="adUnlockMemberNum != null" >
               ad_unlock_member_num = #{adUnlockMemberNum},
            </if>
         	<if test="adUnlockNum != null" >
               ad_unlock_num = #{adUnlockNum},
            </if>
         	<if test="totalWatchMemberNum != null" >
               total_watch_member_num = #{totalWatchMemberNum},
            </if>
         	<if test="totalUnlockMemberNum != null" >
               total_unlock_member_num = #{totalUnlockMemberNum},
            </if>
			<if test="totalWatchNum != null" >
				total_watch_num = #{totalWatchNum},
			</if>
			<if test="totalUnlockNum != null" >
				total_unlock_num = #{totalUnlockNum},
			</if>
			<if test="totalAdIncome != null" >
				total_ad_income = #{totalAdIncome},
			</if>
         	<if test="addMemberInocme != null" >
               add_member_inocme = #{addMemberInocme},
            </if>
         	<if test="addMemberWatchNum != null" >
               add_member_watch_num = #{addMemberWatchNum},
            </if>
         	<if test="addMemberWatchAmount != null" >
               add_member_watch_amount = #{addMemberWatchAmount},
            </if>
			<if test="addMemberRecharge != null">
				add_member_recharge = #{addMemberRecharge},
            </if>
			<if test="todayBackUser != null" >
				today_back_user = #{todayBackUser},
			</if>
			<if test="totalBackUser != null" >
				total_back_user = #{totalBackUser},
			</if>
			<if test="adWatchMemberD60 != null" >
				ad_watch_member_d60 = #{adWatchMemberD60},
			</if>
         	<if test="adIncomeD60 != null" >
               ad_income_d60 = #{adIncomeD60},
            </if>
         	<if test="returnRatioD60 != null" >
               return_ratio_d60 = #{returnRatioD60},
            </if>
			<if test="colorMemberDay != null">
				color_member_day = #{colorMemberDay},
			</if>
			<if test="colorMemberAll != null">
				color_member_all = #{colorMemberAll},
			</if>
			<if test="convertMemberDay != null">
				convert_member_day = #{convertMemberDay},
			</if>
			<if test="convertMemberAll != null">
				convert_member_all = #{convertMemberAll},
			</if>
	    </set>
        where
        	id = #{id}  
	</update>

	<select id="addMemberFreeLinkList" parameterType="com.fast.po.promote.FastStatisLinkFreePO" resultType="com.fast.po.promote.FastStatisLinkFreePO">
		SELECT
			t1.link_id as linkId,
			ifnull( ROUND( sum( t1.ecpm_cost ), 2 ), 0.00 ) AS addMemberInocme,
			count( DISTINCT member_id ) AS addMemberWatchAmount,
			count(*) AS  addMemberWatchNum
		FROM
			fast_member_unlock_start_log t1
		WHERE
			t1.member_id in ( SELECT member_id FROM fast_member_link WHERE first_link_id = t1.link_id  AND create_time &gt;=#{beginStatis} AND create_time &lt;= #{endStatis} )
		AND t1.create_time &gt;=#{beginStatis} AND t1.create_time &lt;= #{endStatis}
		GROUP BY
			t1.link_id
	</select>

	<select id="addMemberFreeLinkRecharge" parameterType="com.fast.po.promote.FastStatisLinkFreePO" resultType="com.fast.po.promote.FastStatisLinkFreePO">
		SELECT
			t1.link_id AS linkId,
			sum( t1.money_recharge ) addMemberRecharge
		FROM
			fast_member_order_recharge t1
		WHERE
		EXISTS ( SELECT id FROM fast_link WHERE fee_flag = 2 AND id = t1.link_id )
		AND t1.member_id in ( SELECT member_id FROM fast_member_link WHERE first_link_id = t1.link_id AND create_time &gt;=#{beginStatis} AND create_time &lt;= #{endStatis})
		AND t1.create_time &gt;=#{beginStatis} AND t1.create_time &lt;= #{endStatis}
		AND t1.coin_change_id = 0
		AND t1.state = 1
		GROUP BY
		t1.link_id
	</select>

	<select id="getFreeDayRoiList" parameterType="com.fast.po.promote.FastStatisLinkFreePO" resultType="com.fast.po.promote.FastStatisLinkFreePO">
		<include refid="FastStatisLinkFree_columns"/>
            ,IFNULL(t1.cost_day,0.00) as totalCost
            ,IFNULL(t1.num_day,0) as addMemberNum
		FROM
			fast_statis_link_free t
		    left join fast_statis_link t1 on t.link_id = t1.link_id and t.statis_date = t1.statis_date
		where t.link_id = #{linkId}
		  <if test="beginStatis != null and endStatis != null">
			  and t.statis_date &gt;= #{beginStatis} and t.statis_date &lt;= #{endStatis}
		  </if>
        order by t.statis_date desc
	</select>

	<select id="queryAddMemberIds" parameterType="com.fast.po.promote.FastStatisLinkFreePO" resultType="string" >
	 select
	        GROUP_CONCAT(distinct member_id)
	 from fast_member_link
	 where
	       first_link_id =#{linkId}
	       and create_time &gt;=#{beginStatis}
	       AND create_time &lt;= #{endStatis}
	</select>

	<select id="queryD60" parameterType="com.fast.po.promote.FastStatisLinkFreePO" resultType="com.fast.po.promote.FastStatisLinkFreePO">
		select IFNULL(sum(ecpm_cost),0.00) AS d60AdIncome ,
		       COUNT(DISTINCT member_id) AS d60AddMember
		from fast_member_unlock_start_log where
		     member_id in (${addMemberIds})
		     and create_time &gt;=#{beginStatis}
		     AND create_time &lt;= #{endStatis}
		     and link_id = #{linkId}
	</select>

	<select id="queryFreeListForPage" parameterType="com.fast.po.promote.FastStatisLinkFreePO" resultType="com.fast.po.promote.FastStatisLinkFreePO">
		SELECT
			id as linkId,
			link_type,
			link_sub_type,
			(select id from fast_statis_link where link_id = t.id and statis_date = #{statisDate} limit 1) AS statisId,
			(select count(distinct member_id)  from fast_member_link_his where link_id = t.id ) as colorMemberAll,
			(select count(distinct member_id)  from fast_member_link_his where link_id = t.id and create_time &gt;=#{createTimeBegin} AND create_time &lt;= #{createTimeEnd}) as colorMemberDay,
			(select count(distinct member_id)  from fast_member_sub_link_his where sub_link_id = t.id ) as loadColorMemberAll,
			(select count(distinct member_id)  from fast_member_sub_link_his where sub_link_id = t.id and create_time &gt;=#{createTimeBegin} AND create_time &lt;= #{createTimeEnd}) as loadColorMemberDay
		FROM
			fast_link t
		WHERE
			t.fee_flag = 2 and DATE_FORMAT(create_time,"%Y-%m-%d") &lt;= #{createTimeBegin}
			limit #{start},#{pageSize}
	</select>

	<select id="queryTotalMemberIds" resultType="String">
		SELECT
			group_concat(distinct member_id)
		FROM
			fast_member_order_recharge
		WHERE
			 state = 2
		  AND coin_change_id = 0
		  AND link_id = #{linkId}
	</select>



</mapper>
