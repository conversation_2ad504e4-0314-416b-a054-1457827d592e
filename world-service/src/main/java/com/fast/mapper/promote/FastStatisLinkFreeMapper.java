/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.annotation.Slave;
import com.fast.po.promote.FastStatisLinkFreePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastStatisLinkFreeMapper {

    // 通过id查询单个对象
    FastStatisLinkFreePO queryById(FastStatisLinkFreePO entity);

    // 通过id查询单个对象
    FastStatisLinkFreePO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastStatisLinkFreePO queryOne(FastStatisLinkFreePO entity);

    // 查询全部
    List<FastStatisLinkFreePO> queryList(FastStatisLinkFreePO entity);

    // 查询总数
    int queryCount(FastStatisLinkFreePO entity);

    // 可选新增
    int insertSelective(FastStatisLinkFreePO entity);

    // 批量新增
    int insertBatch(List<FastStatisLinkFreePO> list);

    // 更新
    int updateById(FastStatisLinkFreePO entity);

    @Slave
    List<FastStatisLinkFreePO> addMemberFreeLinkList(FastStatisLinkFreePO query);

    @Slave
    List<FastStatisLinkFreePO> addMemberFreeLinkRecharge(FastStatisLinkFreePO query);

    @Slave
    List<FastStatisLinkFreePO> getFreeDayRoiList(FastStatisLinkFreePO query);

    @Slave
    String queryAddMemberIds(FastStatisLinkFreePO querAddMemberIds);

    @Slave
    FastStatisLinkFreePO queryD60(FastStatisLinkFreePO queryD60);

    List<FastStatisLinkFreePO> queryFreeListForPage(FastStatisLinkFreePO queryLink);

    String queryTotalMemberIds(@Param("linkId") Integer linkId);

}
