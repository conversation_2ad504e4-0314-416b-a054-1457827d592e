/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.po.promote.FastShortLinkPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastShortLinkMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastShortLinkPO
     * @return
     */
    FastShortLinkPO queryById(FastShortLinkPO fastShortLinkPO);

    /**
     * 通过条件查询单个对象
     *
     * @param fastShortLinkPO
     * @return
     */
    FastShortLinkPO queryOne(FastShortLinkPO fastShortLinkPO);

    /**
     * 查询全部
     *
     * @param fastShortLinkPO
     * @return
     */
    List<FastShortLinkPO> queryList(FastShortLinkPO fastShortLinkPO);

    /**
     * 查询总数
     *
     * @param fastShortLinkPO
     * @return
     */
    int queryCount(FastShortLinkPO fastShortLinkPO);

    /**
     * 新增
     *
     * @param fastShortLinkPO
     * @return
     */
    int insert(FastShortLinkPO fastShortLinkPO);

    /**
     * 可选新增
     *
     * @param fastShortLinkPO
     * @return
     */
    int insertSelective(FastShortLinkPO fastShortLinkPO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastShortLinkPO> list);

    /**
     * 更新
     *
     * @param fastShortLinkPO
     * @return
     */
    int updateById(FastShortLinkPO fastShortLinkPO);


}
