<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.promote.FastLinkExtMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastLinkExt_columns">
		select t.`id`,t.`content_type`,t.`an_unlock_num`,t.`ios_unlock_num`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastLinkExtPO">
		<include refid="FastLinkExt_columns" />
	    from fast_link_ext t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastLinkExtPO" resultType="FastLinkExtPO">
		<include refid="FastLinkExt_columns" />
	    from fast_link_ext t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastLinkExtPO" resultType="FastLinkExtPO">
		<include refid="FastLinkExt_columns" />
	    from fast_link_ext t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastLinkExtPO" resultType="int">
		select count(*)
	    from fast_link_ext t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="contentType != null">
			and t.`content_type` = #{contentType}
		</if>
		<if test="anUnlockNum != null">
			and t.`an_unlock_num` = #{anUnlockNum}
		</if>
		<if test="iosUnlockNum != null">
			and t.`ios_unlock_num` = #{iosUnlockNum}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastLinkExtPO">
        insert into fast_link_ext
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="id != null">id,</if>
	        <if test="contentType != null">content_type,</if>
	        <if test="anUnlockNum != null">an_unlock_num,</if>
	        <if test="iosUnlockNum != null">ios_unlock_num</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="id != null">#{id},</if>
	        <if test="contentType != null">#{contentType},</if>
	        <if test="anUnlockNum != null">#{anUnlockNum},</if>
	        <if test="iosUnlockNum != null">#{iosUnlockNum}</if>
        </trim>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastLinkExtPO">
        update fast_link_ext
        <set>
         	<if test="contentType != null" >
               content_type = #{contentType},
            </if>
         	<if test="anUnlockNum != null" >
               an_unlock_num = #{anUnlockNum},
            </if>
         	<if test="iosUnlockNum != null" >
               ios_unlock_num = #{iosUnlockNum},
            </if>
	    </set>
        where id = #{id}
	</update>

</mapper>
