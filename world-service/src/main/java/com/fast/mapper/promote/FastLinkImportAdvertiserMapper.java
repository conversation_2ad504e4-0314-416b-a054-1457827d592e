/*
 * Powered By fast.up
 */
package com.fast.mapper.promote;

import com.fast.po.promote.FastLinkImportAdvertiserPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastLinkImportAdvertiserMapper {

    // 通过id查询单个对象
    FastLinkImportAdvertiserPO queryById(FastLinkImportAdvertiserPO entity);

    // 通过id查询单个对象
    FastLinkImportAdvertiserPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastLinkImportAdvertiserPO queryOne(FastLinkImportAdvertiserPO entity);

    // 查询全部
    List<FastLinkImportAdvertiserPO> queryList(FastLinkImportAdvertiserPO entity);

    // 查询总数
    int queryCount(FastLinkImportAdvertiserPO entity);

    // 可选新增
    int insertSelective(FastLinkImportAdvertiserPO entity);

    // 批量新增
    int insertBatch(List<FastLinkImportAdvertiserPO> list);

    // 更新
    int updateById(FastLinkImportAdvertiserPO entity);

}
