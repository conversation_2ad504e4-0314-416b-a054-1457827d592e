package com.fast.mapper.datalake;

import com.fast.annotation.JingFenDataLake;
import com.fast.po.analysis.DramaAnalysisPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * k币消耗(datalake)
 */
@Mapper
@JingFenDataLake
public interface DataLakeOrderConsumeMapper {

    /**
     * 每集消费K币赠送分析
     */
    List<DramaAnalysisPO> querySeriesCoinConsumeList(DramaAnalysisPO po);

    /**
     * K币消费值分析
     */
    Long queryCoinConsumeList(DramaAnalysisPO po);

    /**
     * K币消耗分析
     */
    @JingFenDataLake
    List<DramaAnalysisPO> queryCoinConsumeListPlus(DramaAnalysisPO po);

}
