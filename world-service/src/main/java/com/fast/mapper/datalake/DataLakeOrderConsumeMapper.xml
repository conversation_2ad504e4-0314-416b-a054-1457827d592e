<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.datalake.DataLakeOrderConsumeMapper">

    <select id="querySeriesCoinConsumeList" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t.series_num
        ,sum(t.coin_give_consume) coinGiveConsume
        ,sum(t.coin_recharge_consume) coinRechargeConsume
        from fast_member_order_consume t
        left join fast_link t1 on t.link_id = t1.id
        <if test="linkTimeSearch==1">
            left join fast_member_link ml on ml.member_id = t.member_id
        </if>
        <where>
            t.coin_change_id=0
            <include refid="whereSQL"/>
            <if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
                and t1.`link_type` != 3
            </if>
            <if test="advUserIds != null and advUserIds.length()>0">
                and t1.adv_user_id in (${advUserIds})
            </if>
            <if test="linkTimeSearch==1 and linkTimeS != null and linkTimeE != null">
                and ml.`last_link_time` between #{linkTimeS} and #{linkTimeE}
            </if>
        </where>
        group by t.series_num
    </select>

    <select id="queryCoinConsumeList" parameterType="DramaAnalysisPO" resultType="Long">
        select sum(t.coin_recharge_consume+t.coin_give_consume)
        from fast_member_order_consume t
        left join fast_link t1 on t.link_id = t1.id
        <where>
            t.coin_change_id=0
            <include refid="whereSQL"/>
            <if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
                and t1.`link_type` != 3
            </if>
            <if test="advUserIds != null and advUserIds.length()>0">
                and t1.adv_user_id in (${advUserIds})
            </if>
        </where>
    </select>

    <!--用户K币消费分析  -->
    <select id="queryCoinConsumeListPlus" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        select t1.*
        ,(t1.coinRechargeConsume+t1.coinGiveConsume) coinConsume
        from (
        <include refid="coinConsumeSQL"/>
        ) t1
        left join fast_drama fd on t1.dramaId = fd.id
        <where>
            <if test="contentType != null">
                <if test="contentType == 1">
                    and fd.drama_perform_type = 1
                </if>
                <if test="contentType == 4">
                    and fd.drama_perform_type = 2
                </if>
            </if>

        </where>
    </select>

    <!--用户K币消费分析  -->
    <sql id="coinConsumeSQL">
        select t.drama_id dramaId
        ,ifnull(sum(t.coin_recharge_consume),0) coinRechargeConsume
        ,ifnull(sum(t.coin_give_consume),0) coinGiveConsume
        from fast_member_order_consume t
        <where>
            <include refid="whereSQL"/>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
            and t.`coin_change_id` = 0
            and t.`drama_id` > 0
        </where>
        group by t.`drama_id`
    </sql>

    <sql id="whereSQL">
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="retailIds != null and retailIds.length()>0">
            and t.`retail_id` in (${retailIds})
        </if>
        <if test="miniId != null">
            and t.`mini_id` = #{miniId}
        </if>
        <if test="miniIds != null and miniIds.length()>0">
            and t.`mini_id` in (${miniIds})
        </if>
        <if test="dramaId != null">
            and t.`drama_id` = #{dramaId}
        </if>
        <if test="linkId != null">
            and t.`link_id` = #{linkId}
        </if>
        <if test="linkIds != null and linkIds.length()>0">
            and t.`link_id` in (${linkIds})
        </if>

        <if test="createTimeS != null">
            and t.`create_time` &gt;= #{createTimeS}
        </if>
        <if test="createTimeE != null">
            and t.`create_time` &lt;= #{createTimeE}
        </if>
    </sql>

</mapper>
