<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.datalake.DataLakeUnlockStartLogMapper">

    <select id="statisLinkFreeGroupbyLinkId" parameterType="FastStatisLinkFreePO" resultType="FastStatisLinkFreePO">
        select
        link_id as linkId,
        count(distinct member_id) as adWatchMemberNum,
        IFNULL(sum(ecpm_cost),0.00) as adIncome,
        count(*) as adWatchNum
        from
        fast_member_unlock_start_log
        <where>
            <if test="createTimeBegin != null and createTimeEnd != null">
                create_time &gt;= #{createTimeBegin}
                and create_time &lt;= #{createTimeEnd}
            </if>
        </where>
        group by link_id
    </select>

    <select id="backUserGroupbyLinkId" parameterType="FastStatisLinkFreePO" resultType="FastStatisLinkFreePO">
        select
        link_id as linkId,
        count(distinct member_id) as todayBackUser
        FROM
        fast_member_unlock_start_log
        WHERE
        back_state = 2
        AND ecpm_cost > 0
        <if test="createTimeBegin != null and createTimeEnd != null">
            AND create_time &gt;= #{createTimeBegin}
            AND create_time &lt;= #{createTimeEnd}
        </if>
        group by link_id
    </select>

    <select id="queryAddmemberCount" resultType="int" parameterType="com.fast.po.promote.FastStatisLinkFreePO">
        select
            count(distinct member_id)
        FROM
            fast_member_unlock_start_log
        WHERE
        link_time &gt;= #{statisDateBegin}
          AND  link_time &lt;= #{statisDateEnd}
          AND  link_id = #{linkId}
    </select>

    <select id="queryAddmemberIncomeTotal" resultType="java.math.BigDecimal" parameterType="com.fast.po.promote.FastStatisLinkFreePO">
        select
            ROUND(IFNULL(sum(ecpm_cost),0.00),2) as addMemberInocme
        FROM
            fast_member_unlock_start_log
        WHERE
            cost_time &lt;= #{createTimeEnd}
          AND link_id = #{linkId}
          AND member_id in (${addMemberIds})
    </select>

    <select id="queryD60" parameterType="com.fast.po.promote.FastStatisLinkFreePO" resultType="com.fast.po.promote.FastStatisLinkFreePO">
        select ROUND(IFNULL(sum(ecpm_cost),0.00),2) AS d60AdIncome ,
               COUNT(DISTINCT member_id) AS d60AddMember
        from fast_member_unlock_start_log
        where
             cost_time &gt;=#{createTimeBegin}
            AND cost_time &lt;=#{createTimeEnd}
            AND link_time &gt;=#{statisDateBegin}
            AND link_time &lt;=#{statisDateEnd}
            and link_id = #{linkId}
    </select>

    <select id="queryAddMemberIds" parameterType="FastLinkPO" resultType="Long">
        SELECT
            DISTINCT member_id
        FROM
            fast_member_unlock_start_log
        WHERE
        link_time  &gt;= #{statisDateStart}
        AND link_time &lt;= #{statisDateEnd}
        AND link_id = #{id}
    </select>

    <select id="queryAddMemberIdsByDate" parameterType="FastLinkPO" resultType="Long">
        SELECT
            DISTINCT member_id
        FROM
            fast_member_unlock_start_log
        WHERE
            link_time  &gt;= #{statisDateStart}
          AND link_time &lt;= #{statisDateEnd}
    </select>

    <select id="queryAddmemberIncomeByDate" resultType="com.fast.po.promote.FastStatisLinkFreePO" parameterType="com.fast.po.promote.FastStatisLinkFreePO">
        select
            ROUND(IFNULL(sum(ecpm_cost),0.00),2) as addMemberInocme,
            groupArray(distinct member_id) as adMemberIds
        FROM
            fast_member_unlock_start_log
        WHERE
            cost_time &gt;= #{createTimeBegin}
          AND cost_time &lt;= #{createTimeEnd}
          AND link_time &gt;= #{statisDateBegin}
          AND link_time &lt;= #{statisDateEnd}
    </select>

    <select id="queryTodayIncome" resultType="FastStatisLinkFreePO" parameterType="FastStatisLinkFreePO">
        select
            ROUND(IFNULL(sum(ecpm_cost),0.00),2) as adIncome,
            count(distinct member_id) as adWatchMemberNum,
            count(*) as adWatchNum
        FROM
            fast_member_unlock_start_log
        WHERE
            create_time &gt;= #{createTimeBegin}
          AND create_time &lt;= #{createTimeEnd}
          AND link_id = #{linkId}
    </select>

    <update id="updateById" parameterType="FastMemberUnlockStartLogPO">
        update fast_member_unlock_start_log
        <set>
            <if test="miniId != null" >
                mini_id = #{miniId},
            </if>
            <if test="adUnitId != null" >
                ad_unit_id = #{adUnitId},
            </if>
            <if test="enterType != null" >
                enter_type = #{enterType},
            </if>
            <if test="linkId != null" >
                link_id = #{linkId},
            </if>
            <if test="memberId != null" >
                member_id = #{memberId},
            </if>
            <if test="dramaId != null" >
                drama_id = #{dramaId},
            </if>
            <if test="retailId != null" >
                retail_id = #{retailId},
            </if>
            <if test="officialId != null" >
                official_id = #{officialId},
            </if>
            <if test="state != null" >
                `state` = #{state},
            </if>
            <if test="phoneOs != null" >
                phone_os = #{phoneOs},
            </if>
            <if test="watchSeq != null" >
                watch_seq = #{watchSeq},
            </if>
            <if test="ecpmCost != null" >
                ecpm_cost = #{ecpmCost},
            </if>
            <if test="backState != null" >
                back_state = #{backState},
            </if>
            <if test="backInfo != null" >
                back_info = #{backInfo},
            </if>
            <if test="backType != null" >
                back_type = #{backType},
            </if>
            <if test="backAuto != null" >
                back_auto = #{backAuto},
            </if>
            <if test="addState != null" >
                add_state = #{addState},
            </if>
            <if test="aid != null" >
                aid = #{aid},
            </if>
            <if test="cid != null" >
                cid = #{cid},
            </if>
            <if test="promotionId != null" >
                promotion_id = #{promotionId},
            </if>
            <if test="projectId != null" >
                project_id = #{projectId},
            </if>
            <if test="advUserId != null" >
                adv_user_id = #{advUserId},
            </if>
            <if test="linkTime != null" >
                link_time = #{linkTime},
            </if>
            <if test="registerTime != null" >
                register_time = #{registerTime},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate, jdbcType=DATE},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime},
            </if>
            <if test="costTime != null" >
                cost_time = #{costTime},
            </if>
            <if test="costDate != null" >
                cost_date = #{costDate, jdbcType=DATE},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="queryD60Statis" resultType="FastStatisLinkFreePO">
        select
            link_id AS linkId,
            date_format(link_time, '%Y-%m-%d') AS linkTime,
            date_format(cost_time,'%Y-%m-%d') AS costTime,
            IFNULL(sum(ecpm_cost),0.00) AS d60AdIncome,
            COUNT(DISTINCT member_id) AS d60AddMember
        from fast_member_unlock_start_log
        where link_time &gt;= #{startTime}
        and link_time &lt;= #{endTime} and cost_time != null
        group by link_id, date_format(link_time,'%Y-%m-%d'), date_format(cost_time,'%Y-%m-%d')
    </select>

    <select id="statisLinkAddMemberGroupbyLinkId" resultType="FastStatisLinkFreePO" parameterType="FastStatisLinkFreePO">
        select
            link_id as linkId,
            count(distinct member_id) as addMemberWatchAmount,
            ROUND(IFNULL(sum(ecpm_cost),0.00),2) as addMemberInocme,
            count(*) as addMemberWatchNum,
            count(distinct member_id) as convertMemberDay
        FROM fast_member_unlock_start_log
        WHERE cost_time &gt;= #{createTimeBegin}
          AND cost_time &lt;= #{createTimeEnd}
          AND link_time &gt;= #{statisDateBegin}
          AND link_time &lt;= #{statisDateEnd}
        group by link_id
    </select>

    <select id="queryTotalMemberIdsGroupByLinkId" resultType="FastStatisLinkFreePO" parameterType="FastStatisLinkFreePO">
        select
            link_id AS linkId,
            count(distinct member_id) AS convertMemberAll
        FROM fast_member_unlock_start_log
        group by link_id
    </select>

    <select id="queryFreeDataIncomeAnalysis" parameterType="FastMemberUnlockStartLogPO" resultType="com.fast.vo.statistics.FreeDataIncomeAnalysisVO">
        select tmp.totalEcpmCost,
        tmp.addEcpmCost,
        tmp.ecpmCount,
        if(tmp.ecpmCount=0,0,ROUND(tmp.totalEcpmCost/tmp.ecpmCount*1000,2)) as totalEcpmAvgCost
        from
        (SELECT
        IFNULL(ROUND(sum( ecpm_cost ),2),0.00) totalEcpmCost,
        IFNULL(ROUND(sum(if(link_time BETWEEN #{createTimeS} and #{createTimeE},ecpm_cost,0)),2),0.00) addEcpmCost,
        IFNULL(sum(IF(ecpm_cost>0,1,0)),0) ecpmCount
        FROM
        fast_member_unlock_start_log
        WHERE
        retail_id = #{retailId}
        <if test="miniId != null and miniId >0">
            AND mini_id = #{miniId}
        </if>
        AND cost_time &lt;= #{createTimeE} AND cost_time &gt;= #{createTimeS}
        )tmp
    </select>

    <select id="queryRetailAddEcpm" parameterType="com.fast.po.analysis.DramaAnalysisPO" resultType="com.fast.po.member.FastMemberOrderRechargePO">
        select IFNULL(sum(tmp.usAgent),0.00) usAgentProfit,
        IFNULL(sum(tmp.alone),0.00) aloneProfit,
        IFNULL(sum(usAgentAdd),0.00) usAgentAddProfit,
        IFNULL(sum(tmp.aloneAdd),0.00) aloneAddProfit
        from (select
        if(retail_type in (1,2),sum(ecpm_cost),0) usAgent,
        if(retail_type =3,sum(ecpm_cost),0) alone,
        if(retail_type in (1,2) and add_state=1,sum(ecpm_cost),0) usAgentAdd,
        if(retail_type =3 and add_state=1,sum(ecpm_cost),0) aloneAdd
        from
        fast_member_unlock_start_log
        <where>
            <if test="retailIds!=null and retailIds.length()>0">
                retail_id IN (${retailIds})
            </if>
            <if test="miniIds!=null and miniIds.length()>0">
                AND mini_id IN (${miniIds})
            </if>
            <if test="createTimeS != null">
                AND create_time &gt;= #{createTimeS}
            </if>
            <if test="createTimeE != null">
                AND create_time &lt;= #{createTimeE}
            </if>
            <if test="dramaId!=null">
                AND drama_id = #{dramaId}
            </if>
            <if test="advUserIds!=null and advUserIds.length()>0">
                AND adv_user_id IN (${advUserIds})
            </if>
        </where>
        group by
        retail_type,
        add_state
        having retail_type >0) tmp
    </select>

    <select id="queryEcpmNum" parameterType="com.fast.po.analysis.DramaAnalysisPO" resultType="int">
        select count(distinct member_id)
        from fast_member_unlock_start_log cusl
        <where>
            ecpm_cost >0
            <if test="reportDateStr!=null">
                AND create_date = #{reportDateStr}
            </if>
            <if test="miniIds!=null and miniIds.length()>0">
                AND mini_id IN (${miniIds})
            </if>
            <if test="advUserIds != null and advUserIds.length()>0">
                AND link_adv_user_id IN (${advUserIds})
            </if>
            <if test="retailIds!=null and retailIds.length()>0">
                AND retail_id IN (${retailIds})
            </if>
            <if test="dramaId!=null">
                AND drama_id = #{dramaId}
            </if>
        </where>
    </select>

    <select id="queryAdMemberGroupByHour" parameterType="com.fast.po.analysis.DramaAnalysisPO"
            resultType="com.fast.po.analysis.DramaAnalysisPO">
        SELECT
        HOUR(register_time) AS watchHour,
        COUNT(distinct member_id) AS memberCount
        FROM
        fast_member_unlock_start_log
        <where>
            <include refid="whereSQL"/>
        </where>
        GROUP BY HOUR(register_time)
        ORDER BY HOUR(register_time) ASC
    </select>

    <select id="queryAdMemberGroupByDate" parameterType="com.fast.po.analysis.DramaAnalysisPO"
            resultType="com.fast.po.analysis.DramaAnalysisPO">
        SELECT
        date_format(register_time,'%Y-%m-%d')  AS watchDate,
        COUNT(distinct member_id) AS memberCount
        FROM
        fast_member_unlock_start_log
        <where>
            <include refid="whereSQL"/>
        </where>
        GROUP BY date_format(register_time,'%Y-%m-%d')
        ORDER BY date_format(register_time,'%Y-%m-%d')  ASC
    </select>

    <select id="queryUnlockSuccessListGroupbyLinkId" parameterType="FastStatisLinkFreePO" resultType="FastStatisLinkFreePO">
        select
        link_id AS linkId,
        count(*) AS adUnlockNum ,
        count(distinct member_id) AS adUnlockMemberNum
        from fast_member_unlock_start_log
        <where>
            link_id &gt; 0 and state = 1
            <if test="createTimeBegin != null and createTimeEnd != null">
                AND create_time &gt;= #{createTimeBegin}
                AND create_time &lt;= #{createTimeEnd}
            </if>
        </where>
        group by link_id
    </select>

    <sql id="whereSQL">
        <if test = "dramaId != null">
            drama_id = #{dramaId}
        </if>
        <if test = "retailId != null">
            AND retail_id = #{retailId}
        </if>
        <if test = "linkId != null">
            AND link_id = #{linkId}
        </if>
        <if test = "advUserId != null">
            AND adv_user_id = #{advUserId}
        </if>
        <if test = "miniId != null">
            AND mini_id = #{miniId}
        </if>
        <if test = "retailIds != null and retailIds.length()>0">
            AND retail_id in (${retailIds})
        </if>
        <if test = "linkIds != null and linkIds.length()>0">
            AND link_id in (${linkIds})
        </if>
        <if test = "advUserIds != null and advUserIds.length()>0 ">
            AND adv_user_id in (${advUserIds})
        </if>
        <if test = "miniIds != null and miniIds.length()>0">
            AND mini_id in (${miniIds})
        </if>
        <if test = "registerTimeStart != null and registerTimeStart.length()>0">
            AND register_time &gt;= #{registerTimeStart}
        </if>
        <if test = "registerTimeEnd != null and registerTimeEnd.length()>0">
            AND register_time &lt;= #{registerTimeEnd}
        </if>
        <if test = "watchTimeStart != null and watchTimeStart.length()>0">
            AND create_time &gt;= #{watchTimeStart}
        </if>
        <if test = "watchTimeEnd != null and watchTimeEnd.length()>0">
            AND create_time &lt;= #{watchTimeEnd}
        </if>
        <if test = "costFlag">
            AND ecpm_cost &gt; 0
        </if>
    </sql>

</mapper>
