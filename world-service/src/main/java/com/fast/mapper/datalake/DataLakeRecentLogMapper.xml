<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.datalake.DataLakeRecentLogMapper">
	<sql id="whereSQL">
		<if test="dramaId != null">
			and t.`drama_id` = #{dramaId}
		</if>
		<if test="seriesNum != null">
			and t.`series_num` = #{seriesNum}
		</if>
		<if test="memberId != null">
			and t.`member_id` = #{memberId}
		</if>
		<if test="memberIds != null and memberIds.length()>0">
			and t.`member_id` in (${memberIds})
		</if>
		<if test="payRule != null">
			and t.`pay_rule` = #{payRule}
		</if>
		<if test="payResult != null">
			and t.`pay_result` = #{payResult}
		</if>
		<if test="payResultExt != null and payResultExt == 1">
			and t.`pay_result` != #{payResultExt}
		</if>
		<if test="payResultExt != null and payResultExt == 2">
			and t.`pay_result` not in (2,5)
		</if>
		<if test="linkId != null">
			and t.`link_id` = #{linkId}
		</if>
		<if test="linkIds != null and linkIds.length()>0">
			and t.`link_id` in (${linkIds})
		</if>
		<if test="dramaIds != null and dramaIds.length()>0">
			and t.`drama_id` in (${dramaIds})
		</if>
		<if test="retailId != null">
			and t.`retail_id` = #{retailId}
		</if>
		<if test="retailIds != null and retailIds.length()>0">
			and t.`retail_id` in (${retailIds})
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="miniIds != null and miniIds.length()>0">
			and t.`mini_id` in (${miniIds})
		</if>
		<if test="officialId != null">
			and t.`official_id` = #{officialId}
		</if>
		<if test="officialIds != null and officialIds.length()>0">
			and t.`official_id` in (${officialIds})
		</if>
		<if test="playSecond != null">
			and t.`play_second` = #{playSecond}
		</if>
		<if test="playSecondAccurate != null">
			and t.`play_second_accurate` = #{playSecondAccurate}
		</if>
		<if test="playState != null">
			and t.`play_state` = #{playState}
		</if>
		<if test="coinConsume != null">
			and t.`coin_consume` = #{coinConsume}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="createTimeS != null">
			and t.`create_time` &gt;= #{createTimeS}
		</if>
		<if test="createTimeE != null">
			and t.`create_time` &lt;= #{createTimeE}
		</if>
		<if test="registerTimeS != null and registerTimeE != null">
			and EXISTS ( SELECT 1 FROM fast_member WHERE create_time &gt;= #{registerTimeS} AND create_time &lt;= #{registerTimeE} AND id = t.member_id)
			<if test="createTimeS == null">
				and t.create_time &gt;= #{registerTimeS}
			</if>
		</if>
	</sql>

	<select id="queryRecentLogPage" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="com.fast.po.member.FastMemberRecentLogPO">
		select * from fast_member_recent_log t
		left join fast_drama fd on t.drama_id = fd.id
		<where>
			<include refid="whereSQL"/>
			<if test="contentType != null">
				<if test="contentType == 1">
					and fd.drama_perform_type = 1
				</if>
				<if test="contentType == 4">
					and fd.drama_perform_type = 2
				</if>
			</if>
		</where>
		order by t.id DESC
	</select>

	<select id="queryDramaSkipGroupList" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="com.fast.po.member.FastMemberRecentLogPO">
		select t1.playSecond, count(t1.`member_id`) watchCount
		from (
		select t.member_id, max(t.`play_second`) playSecond
		from fast_member_recent_log t
		<where>
			and play_second > 0
			<include refid="whereSQL"/>
			<if test="seriesNum != null">
				and t.`series_num` = #{seriesNum}
			</if>
			<if test="linkIds != null and linkIds.length() > 0">
				and t.`link_id` in (${linkIds})
			</if>
			<if test="memberIds != null and memberIds.length() > 0">
				and t.`member_id` in (${memberIds})
			</if>
		</where>
		group by t.member_id
		) t1 group by t1.playSecond
	</select>


	<select id="queryWatchCountByDramaId" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="Integer">
		select count(distinct member_id) from fast_member_recent_log t
		<where>
			<include refid="whereSQL"/>
		</where>
	</select>


	<select id="queryWatchCountBatch" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="com.fast.po.member.FastMemberRecentLogPO">
		select drama_id,COUNT(DISTINCT(member_id)) watchCount from fast_member_recent_log t
		<where>
			<include refid="whereSQL"/>
			<if test="limit != null and size != null">
				and t.drama_id in (select drama_id from fast_member_recent_log t group by drama_id order by count(*) desc limit #{limit},#{size})
			</if>
		</where>
		group by drama_id
		order by watchCount DESC

	</select>


	<select id="queryWatchCountGroupBySeriesNum" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="com.fast.po.member.FastMemberRecentLogPO">
		select t.series_num,count(distinct t.member_id) watchCount
		from fast_member_recent_log t
		<where>
			<include refid="whereSQL"/>
			<if test="linkIds != null and linkIds.length() > 0">
				and t.`link_id` in (${linkIds})
			</if>
			<if test="memberIds != null and memberIds.length() > 0">
				and t.`member_id` in (${memberIds})
			</if>
			<if test="officialIds != null and officialIds.length() > 0">
				and t.`official_id` in (${officialIds})
			</if>
			<if test="retailId != null">
				and t.`retail_id` = #{retailId}
			</if>
			<if test="retailIds != null and retailIds != ''">
				and t.`retail_id` in (${retailIds})
			</if>
		</where>
		group by t.series_num
		order by watchCount DESC
	</select>

	<select id="queryMemberRecentCount" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="java.lang.Integer">
		select count(*)
		from fast_member_recent_log
		where member_id = #{memberId}
		  and drama_id = #{dramaId}
	</select>

	<select id="queryWatchRank" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="com.fast.po.member.FastMemberRecentLogPO">
		select drama_id, count(*) watchCount
		from fast_member_recent_log t
		<where>
			<include refid="whereSQL"/>
		</where>
		group by drama_id
		order by watchCount DESC
		<if test="limit != null and size != null">
			limit #{limit}, #{size}
		</if>
	</select>

	<select id="queryWatchMemberList" parameterType="DramaAnalysisPO" resultType="Long">
        select t.`member_id` memberId
        from fast_member_recent_log t
        <where>
            <if test="retailId != null">
                and t.`retail_id` = #{retailId}
            </if>
            <if test="retailIds != null and retailIds.length() > 0">
                and t.`retail_id` in (${retailIds})
            </if>
            <if test="miniId != null">
                and t.`mini_id` = #{miniId}
            </if>
            <if test="miniIds != null and miniIds.length() > 0">
                and t.`mini_id` in (${miniIds})
            </if>
            <if test="dramaId != null and @com.fast.utils.thread.DramaIdContext@getDramaId() == null">
                and t.`drama_id` = #{dramaId}
            </if>
            <if test="watchTimeS != null">
                and t.`create_time` &gt;= #{watchTimeS}
            </if>
            <if test="watchTimeE != null">
                and t.`create_time` &lt;= #{watchTimeE}
            </if>
            <if test="seriesNum != null">
                and t.`series_num` = #{seriesNum}
            </if>
            <if test="playState != null">
                and t.`play_state` = #{playState}
            </if>
        </where>
    </select>

	<select id="queryWatchMemberCountByDramaId" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="int">
		SELECT
			count(DISTINCT t1.member_id)
		FROM
			`fast_member_recent_log` t1
			left join `fast_link` t2 on t1.link_id = t2.id
			left join `fast_member` t3 on t1.member_id = t3.id
		<where>
			<include refid="whereSQLV2"/>
		</where>
	</select>

	<select id="queryWatchCountGroupBySeriesNumV2"  parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="com.fast.po.member.FastMemberRecentLogPO">
		select t1.series_num,count(distinct t1.member_id) watchCount
		from `fast_member_recent_log` t1
		left join `fast_link` t2 on t1.link_id = t2.id
		left join `fast_member` t3 on t1.member_id = t3.id
		<where>
			<include refid="whereSQLV2"/>
		</where>
		group by t1.series_num
		order by watchCount DESC
	</select>

	<select id="queryDramaDetailSeriesGroupList" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="com.fast.po.member.FastMemberRecentLogPO">
		select
			p.series_num,
			count(p.`member_id`) watchCount,
			sum(if(p.`play_state`=1,1,0)) watchFinishMemberCount
		from (
			select t1.series_num
			,t1.`member_id`
			,max(t1.`play_state`) play_state
		from `fast_member_recent_log` t1
		left join `fast_link` t2 on t1.link_id = t2.id
		left join `fast_member` t3 on t1.member_id = t3.id
		<where>
			<include refid="whereSQLV2"/>
		</where>
		group by t1.series_num, t1.`member_id`
		) p group by p.series_num order by p.series_num
	</select>

	<select id="queryPushMember" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="com.fast.po.member.FastMemberRecentLogPO">
		SELECT
			mrl.member_id,
			max( mrl.create_time ) createTime
		FROM
			fast_member_recent_log mrl
		WHERE
			mrl.create_time &gt; #{createTimeS} and mrl.mini_id = #{miniId}
		GROUP BY
			mrl.member_id
		HAVING
			createTime &lt; #{createTimeE}
	</select>

	<select id="queryLastRecent" parameterType="com.fast.po.member.FastMemberRecentLogPO" resultType="com.fast.po.member.FastMemberRecentLogPO">
			select
				member_id,
				drama_id,
				series_num
			from fast_member_recent_log
			where member_id = #{memberId} order by id desc limit 1
	</select>

	<select id="queryCount" parameterType="FastMemberUnlockRewardLogPO" resultType="int">
		select count(distinct t.`member_id`)
		from fast_member_recent_log t
		<where>
			<include refid="whereSQL" />
		</where>
	</select>

	<!--短剧完播人数分析  -->
	<select id="queryWatchFinishMemberCountV2" parameterType="DramaAnalysisPO" resultType="int">
		select count(distinct t.member_id) watchFinishMemberCount
		from fast_member_recent_log t
		left join fast_link t1 on t.link_id = t1.id
		<where>
			<if test="retailId != null">
				and t.`retail_id` = #{retailId}
			</if>
			<if test="retailIds != null and retailIds.length()>0">
				and t.`retail_id` in (${retailIds})
			</if>
			<if test="miniId != null">
				and t.`mini_id` = #{miniId}
			</if>
			<if test="miniIds != null and miniIds.length()>0">
				and t.`mini_id` in (${miniIds})
			</if>
			<if test="linkId != null">
				and t.`link_id` = #{linkId}
			</if>
			<if test="linkIds != null and linkIds.length()>0">
				and t.`link_id` in (${linkIds})
			</if>
			<if test="dramaId != null ">
				and t.`drama_id` = #{dramaId}
			</if>
			<if test="dramaIds != null and dramaIds.length()>0">
				and t.`drama_id` in (${dramaIds})
			</if>
			<if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
				and t1.`link_type` != 3
			</if>
			<if test="advUserIds != null and advUserIds.length()>0">
				and t1.adv_user_id in (${advUserIds})
			</if>
			<if test="playState != null">
				and t.play_state = #{playState}
			</if>
			<if test="seriesNum != null">
				and t.series_num = #{seriesNum}
			</if>
			<if test="watchTimeS != null">
				and t.create_time &gt;= #{watchTimeS}
			</if>
			<if test="watchTimeE != null">
				and t.create_time &lt;= #{watchTimeE}
			</if>
		</where>
	</select>

	<sql id="whereSQLV2">
		<if test="dramaId != null">
			t1.drama_id = #{dramaId}
		</if>
		<if test="retailId != null">
			and t1.retail_id = #{retailId}
		</if>
		<if test="retailIds != null and retailIds.length()>0">
			and t1.retail_id in (${retailIds})
		</if>
		<if test="linkId != null">
			and t1.link_id = #{linkId}
		</if>
		<if test="linkIds != null and linkIds.length()>0">
			and t1.link_id in (${linkIds})
		</if>
		<if test="advUserIds != null and advUserIds.length()>0">
			and t2.adv_user_id in (${advUserIds})
		</if>
		<if test="miniIds != null and miniIds.length()>0">
			and t1.mini_id in (${miniIds})
		</if>
		<if test="removeMountLinkFlag != null and removeMountLinkFlag == 1 ">
			and t2.link_type != 3
		</if>
		<if test="createTimeS != null">
			and t1.create_time &gt;= #{createTimeS}
		</if>
		<if test="createTimeE != null">
			and t1.create_time &lt;= #{createTimeE}
		</if>
		<if test="linkTimeSearch==1 and linkTimeS != null and linkTimeE != null">
			and t1.`link_time` between #{linkTimeS} and #{linkTimeE}
		</if>
		<if test="registerTimeS != null">
			and t3.create_time &gt;= #{registerTimeS}
		</if>
		<if test="registerTimeE != null">
			and t3.create_time &lt;= #{registerTimeE}
		</if>
		<if test="feeFlag != null">
			and t2.fee_flag = #{feeFlag}
		</if>
		<if test="officialIds != null and officialIds.length()>0">
			and t1.official_id in (${officialIds})
		</if>
		<if test="memberIds != null and memberIds.length()>0">
			and t1.member_id in (${memberIds})
		</if>
		<if test="payResultExt != null and payResultExt == 1">
			and t1.`pay_result` != #{payResultExt}
		</if>
		<if test="payResultExt != null and payResultExt == 2">
			and t1.`pay_result` not in (2,5)
		</if>
		<if test="payResult != null">
			and t1.`pay_result` = #{payResult}
		</if>
	</sql>

	<select id="queryWatchMemberCountByDramaId2" resultType="java.lang.Integer">
		SELECT count(DISTINCT t1.member_id)
		FROM `fast_member_recent_log` t1
			left join `fast_link` t2 on t1.link_id = t2.id
		<where>
			<if test="dramaId != null">
				t1.drama_id = #{dramaId}
			</if>
			<if test="retailId != null">
				and t1.retail_id = #{retailId}
			</if>
			<if test="retailIds != null and retailIds.length() > 0">
				and t1.retail_id in (${retailIds})
			</if>
			<if test="linkId != null">
				and t1.link_id = #{linkId}
			</if>
			<if test="linkIds != null and linkIds.length() > 0">
				and t1.link_id in (${linkIds})
			</if>
			<if test="advUserIds != null and advUserIds.length() > 0">
				and t2.adv_user_id in (${advUserIds})
			</if>
			<if test="miniIds != null and miniIds.length() > 0">
				and t1.mini_id in (${miniIds})
			</if>
			<if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
				and t2.link_type != 3
			</if>
			<if test="createTimeS != null">
				and t1.create_time &gt;= #{createTimeS}
			</if>
			<if test="createTimeE != null">
				and t1.create_time &lt;= #{createTimeE}
			</if>
			<if test="seriesNum != null">
				and t1.series_num = #{seriesNum}
			</if>
			<if test="playState != null">
				and t1.play_state = #{playState}
			</if>
		</where>

	</select>

	<select id="queryWatchCountByDramaIds" resultType="com.fast.po.drama.FastDramaPO">
		select count(distinct t.`member_id`) watchCount,t.drama_id
		from fast_member_recent_log t
		<where>
			<include refid="whereSQL" />
		</where>
		group by t.drama_id
	</select>
</mapper>
