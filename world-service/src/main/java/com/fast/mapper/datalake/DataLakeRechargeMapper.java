package com.fast.mapper.datalake;

import com.fast.annotation.JingFenDataLake;
import com.fast.po.analysis.DramaAnalysisPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单充值表(datalake)
 */
@JingFenDataLake
@Mapper
public interface DataLakeRechargeMapper {

    /**
     * 短剧详情-核心数据分析-自代投充值金额
     *
     * @param po
     * @return
     */
    DramaAnalysisPO queryRechargeDataListGroupByRetailType(DramaAnalysisPO po);

    /**
     * 短剧详情-付费分析-充值次数分析
     *
     * @param query
     * @return
     */
    List<DramaAnalysisPO> queryListGroupByRechargeCount(DramaAnalysisPO query);

}
