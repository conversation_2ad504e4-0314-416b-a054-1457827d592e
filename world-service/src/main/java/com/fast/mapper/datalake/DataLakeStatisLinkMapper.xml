<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.datalake.DataLakeStatisLinkMapper">

    <select id="queryListDramaCostSum" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="java.math.BigDecimal">
        select sum(f.cost_day)
        from fast_link t INNER JOIN fast_statis_link f ON t.id = f.link_id
        <where>
            <include refid="whereSQLDramaCost" />
        </where>
    </select>

    <select id="queryLinkAddMemberNum" parameterType="com.fast.vo.promote.FastLinkQueryVO" resultType="int">
        select sum(f.num_day)
        from fast_link t INNER JOIN fast_statis_link f ON t.id = f.link_id
        <where>
            <include refid="whereSQLDramaCost" />
        </where>
    </select>

    <sql id="whereSQLDramaCost">
        <if test="ids != null and ids.length() > 0">
            and t.`id` in (${ids})
        </if>
        <if test="beginTime != null">
            and f.`statis_date` &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            and f.`statis_date` &lt;= #{endTime}
        </if>
        <if test="dramaId != null">
            and t.`drama_id` = #{dramaId}
        </if>
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="retailIds != null and retailIds.length()>0">
            and t.`retail_id` in (${retailIds})
        </if>
        <if test="miniId != null">
            and t.`mini_id` = #{miniId}
        </if>
        <if test="miniIds != null and miniIds.length()>0">
            and t.`mini_id` in (${miniIds})
        </if>
        <if test="advUserIds != null and advUserIds.length()>0">
            AND t.adv_user_id in (${advUserIds})
        </if>
        <if test="officialId != null">
            and t.official_id = #{officialId}
        </if>
        <if test="officialIds != null and officialIds.length()>0">
            and t.`official_id` in (${officialIds})
        </if>
        <if test="contentType != null">
            AND t.content_type = #{contentType}
        </if>
        <if test="feeFlag != null">
            AND t.fee_flag = #{feeFlag}
        </if>
        <if test="retailTypes != null">
            AND EXISTS (SELECT * FROM fast_retail WHERE id = f.retail_id AND retail_flag = 1 AND del_flag = 0 AND FIND_IN_SET( retail_type, #{retailTypes} ))
        </if>
        <if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
            and t.`link_type` != 3
        </if>
    </sql>

</mapper>
