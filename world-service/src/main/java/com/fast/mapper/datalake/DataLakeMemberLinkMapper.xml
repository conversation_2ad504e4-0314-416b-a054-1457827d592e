<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.datalake.DataLakeMemberLinkMapper">

	<select id="queryRegisterMemberCountList" resultType="int">
		select count(*)
		from fast_link l
				join fast_member_link ml ON l.id = ml.first_link_id
				join fast_member t ON ml.member_id = t.id
		<where>
			<if test="dramaId != null">
				and l.`drama_id` = #{dramaId}
			</if>
			<if test="linkId != null">
				and ml.`last_link_id` = #{linkId}
			</if>
			<if test="linkIds != null and linkIds.length() > 0">
				and ml.`last_link_id` in (${linkIds})
			</if>
			<if test="advUserIds != null and advUserIds.length() > 0">
				and l.adv_user_id in (${advUserIds})
			</if>
			<if test="retailIds != null and retailIds.length() > 0">
				and t.`retail_id` in (${retailIds})
			</if>
			<if test="miniIds != null and miniIds.length() > 0">
				and t.`mini_id` in (${miniIds})
			</if>
			<if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
				and l.`link_type` != 3
			</if>
			<if test="registerTimeS != null">
				and t.`create_time` &gt;= #{registerTimeS}
			</if>
			<if test="registerTimeE != null">
				and t.`create_time` &lt;= #{registerTimeE}
			</if>
		</where>
	</select>
</mapper>
