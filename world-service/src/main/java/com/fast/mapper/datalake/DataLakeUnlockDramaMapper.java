package com.fast.mapper.datalake;

import com.fast.annotation.JingFenDataLake;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.member.FastMemberUnlockDramaPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 解锁短剧(datalake)
 */
@Mapper
@JingFenDataLake
public interface DataLakeUnlockDramaMapper {

    /**
     * 单部短剧查询解锁人数
     *
     * @param query
     * @return
     */
    Integer queryUnlockCountByDramaId(FastMemberUnlockDramaPO query);

    /**
     * 批量短剧查询解锁人数
     *
     * @param query
     * @return
     */
    List<FastMemberUnlockDramaPO> queryUnlockCountBatch(FastMemberUnlockDramaPO query);

    /**
     * 根据剧集号分组查询每集的解锁人数
     *
     * @param query
     * @return
     */
    List<FastMemberUnlockDramaPO> queryUnlockCountGroupBySeriesNum(FastMemberUnlockDramaPO query);

    /**
     * 查询排名
     *
     * @param unlockQuery
     * @return
     */
    List<FastMemberUnlockDramaPO> queryUnlockRank(FastMemberUnlockDramaPO unlockQuery);


    List<FastDramaPO> queryUnlockCountByDramaIds(FastMemberUnlockDramaPO queryUnlock);
}
