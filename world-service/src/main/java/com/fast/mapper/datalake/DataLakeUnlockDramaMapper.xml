<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.datalake.DataLakeUnlockDramaMapper">

    <select id="queryUnlockCountByDramaId" parameterType="com.fast.po.member.FastMemberUnlockDramaPO" resultType="Integer">
        select count(distinct member_id) from fast_member_unlock_drama t
        <where>
            <include refid="whereSQL"/>
        </where>
    </select>

    <select id="queryUnlockCountBatch" parameterType="com.fast.po.member.FastMemberUnlockDramaPO" resultType="com.fast.po.member.FastMemberUnlockDramaPO">
        select drama_id,count(distinct member_id) unlockCount from fast_member_unlock_drama t
        <where>
            <include refid="whereSQL"/>
        </where>
        group by drama_id
        order by unlockCount DESC
        <if test="limit != null and size != null">
            limit #{limit},#{size}
        </if>
    </select>

    <sql id = "whereSQL">
        <if test="dramaId != null">
            t.drama_id = #{dramaId}
        </if>
        <if test="seriesNum != null">
            AND t.series_num = #{seriesNum}
        </if>
        <if test="createTimeE != null">
            AND t.create_time &lt;= #{createTimeE}
        </if>
        <if test="createTimeS != null">
            AND t.create_time &gt;= #{createTimeS}
        </if>
        <if test="dramaIds != null">
            AND t.drama_id in (${dramaIds})
        </if>
        <if test="memberIds != null">
            AND t.member_id in (${memberIds})
        </if>
    </sql>

    <select id="queryUnlockCountGroupBySeriesNum"  parameterType="com.fast.po.member.FastMemberUnlockDramaPO" resultType="com.fast.po.member.FastMemberUnlockDramaPO">
        select t.series_num,count(distinct t.member_id) unlockCount
        from fast_member_unlock_drama t
        <if test="linkTimeSearch==1">
            left join fast_member_link ml on ml.member_id = t.member_id
        </if>
        <where>
            <include refid="whereSQL"/>
            <if test="linkTimeSearch==1 and linkTimeS != null and linkTimeE != null">
                and ml.`last_link_time` between #{linkTimeS} and #{linkTimeE}
            </if>
        </where>
        group by t.series_num
        order by t.series_num ASC
    </select>

    <select id="queryUnlockRank" parameterType="FastMemberUnlockDramaPO" resultType="FastMemberUnlockDramaPO">
        select drama_id,count(*) unlockCount from fast_member_unlock_drama t
        <where>
            <include refid="whereSQL"/>
        </where>
        group by drama_id
        order by unlockCount DESC
        <if test="limit != null and size != null">
            limit #{limit},#{size}
        </if>
    </select>

    <select id="queryUnlockCountByDramaIds" resultType="com.fast.po.drama.FastDramaPO">
        select count(distinct member_id) unlockCount,t.drama_id from fast_member_unlock_drama t
        <where>
            <include refid="whereSQL"/>
        </where>
        group by t.drama_id
    </select>
</mapper>
