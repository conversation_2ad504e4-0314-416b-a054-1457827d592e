<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.datalake.DataLakeRechargeMapper">

    <select id="queryRechargeDataListGroupByRetailType" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        SELECT
        ROUND(IFNULL(sum( tmp.usRechargeMoney ),0.00),2) as usRechargeMoney,
        IFNULL(sum( tmp.usRecharegeMemberCount ),0) as usRecharegeMemberCount,
        ROUND(IFNULL(sum( tmp.aloneRechargeMoney ),0.00),2) as aloneRechargeMoney,
        IFNULL(sum( tmp.aloneRechargeMemberCount ),0) as aloneRechargeMemberCount
        from  (SELECT
        retail.retail_type,
        IF
        ( retail.retail_type IN ( 1, 2 ), sum( t.money_profit ), 0.00 ) usRechargeMoney,
        IF
        ( retail.retail_type IN ( 1, 2 ), count( DISTINCT member_id ), 0.00 ) usRecharegeMemberCount,
        IF
        ( retail.retail_type = 3, sum( t.money_profit ), 0.00 ) aloneRechargeMoney,
        IF
        ( retail.retail_type = 3, count( DISTINCT member_id ), 0.00 ) aloneRechargeMemberCount
        from fast_member_order_recharge t
        left join fast_retail retail on t.retail_id = retail.id
        left join fast_link link on t.link_id = link.id
        <where>
            t.state=1 and t.coin_change_id=0
            <include refid="whereSQL"/>

            <if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
                AND link.link_type != 3
            </if>
        </where>
        GROUP BY
        retail.retail_type)tmp
    </select>

    <select id="queryListGroupByRechargeCount" parameterType="DramaAnalysisPO" resultType="DramaAnalysisPO">
        SELECT
            rechargeCount,
            ROUND(sum( rechargeMoney ),2) as rechargeMoneySum,
            count( member_id ) as rechargeMemberNum,
            sum(firstRechargeOrderType) as firstVipRechargeNum
        FROM
            (
            SELECT
                t.member_id,
                SUBSTRING_INDEX(group_concat(t.money_profit order by t.id),',',-1) AS rechargeMoney,
                count(*) rechargeCount,
                IF(SUBSTRING_INDEX(group_concat(t.order_type order by t.id),',',1)=2,1,0) firstRechargeOrderType
            FROM
                fast_member_order_recharge t
                LEFT JOIN fast_link t2 ON t.link_id = t2.id
            WHERE
                 t.state=1 and t.coin_change_id=0
                <if test="retailId != null">
                    and t.`retail_id` = #{retailId}
                </if>
                <if test="retailIds != null and retailIds.length()>0">
                    and t.`retail_id` in (${retailIds})
                </if>
                <if test="miniId != null">
                    and t.`mini_id` = #{miniId}
                </if>
                <if test="miniIds != null and miniIds.length()>0">
                    and t.`mini_id` in (${miniIds})
                </if>
                <if test="dramaId != null">
                    and t.`drama_id` = #{dramaId}
                </if>
                <if test="linkId != null">
                    and t.`link_id` = #{linkId}
                </if>
                <if test="linkIds != null and linkIds.length()>0">
                    and t.`link_id` in (${linkIds})
                </if>
                <if test="memberIds != null and memberIds.length()>0">
                    and t.`member_id` in (${memberIds})
                </if>
                <if test="createTimeS != null">
                    and t.`pay_time` &gt;= #{createTimeS}
                </if>
                <if test="createTimeE != null">
                    and t.`pay_time` &lt;= #{createTimeE}
                </if>
                <if test="contentType != null">
                    and t.content_type=#{contentType}
                </if>
                <if test="registerTimeS != null">
                    and t.register_time &gt;= #{registerTimeS}
                </if>
                <if test="registerTimeE != null">
                    and t.register_time &lt;= #{registerTimeE}
                </if>
                <if test="removeMountLinkFlag != null and removeMountLinkFlag==1">
                    and t2.link_type != 3
                </if>
            GROUP BY
                t.member_id
            HAVING
                rechargeCount >0
            ) tmp
        GROUP BY
            tmp.rechargeCount
        ORDER BY
            tmp.rechargeCount ASC
    </select>

    <!-- 公共条件  -->
    <sql id="whereSQL">
        <if test="dramaId != null">
            and t.`drama_id` = #{dramaId}
        </if>
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="retailIds != null and retailIds.length()>0">
            and t.`retail_id` in (${retailIds})
        </if>
        <if test="miniId != null">
            and t.`mini_id` = #{miniId}
        </if>
        <if test="miniIds != null and miniIds.length()>0">
            and t.`mini_id` in (${miniIds})
        </if>
        <if test="linkId != null">
            and t.`link_id` = #{linkId}
        </if>
        <if test="linkIds != null and linkIds.length()>0">
            and t.`link_id` in (${linkIds})
        </if>
        <if test="advUserIds != null and advUserIds.length()>0">
            and t.`adv_user_id` in (${advUserIds})
        </if>
        <if test="createTimeS != null">
            and t.`pay_time` &gt;= #{createTimeS}
        </if>
        <if test="createTimeE != null">
            and t.`pay_time` &lt;= #{createTimeE}
        </if>
        <if test="addState != null">
            and t.`add_state` = #{addState}
        </if>
        <if test="contentType != null">
            AND t.content_type=#{contentType}
        </if>
    </sql>

</mapper>
