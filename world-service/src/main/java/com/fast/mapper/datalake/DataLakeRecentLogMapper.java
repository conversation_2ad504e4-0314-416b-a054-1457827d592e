package com.fast.mapper.datalake;

import com.fast.annotation.JingFenDataLake;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.po.drama.FastDramaPO;
import com.fast.po.member.FastMemberRecentLogPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 查询观看记录日志(datalake)
 */
@Mapper
@JingFenDataLake
public interface DataLakeRecentLogMapper {


    /**
     * 查询分页
     *
     * @param query
     * @return
     */
    List<FastMemberRecentLogPO> queryRecentLogPage(FastMemberRecentLogPO query);

    /**
     * 单部剧跳出率查询
     */
    List<FastMemberRecentLogPO> queryDramaSkipGroupList(FastMemberRecentLogPO query);

    /**
     * 单个短剧查询观看人数
     *
     * @param query
     * @return
     */
    Integer queryWatchCountByDramaId(FastMemberRecentLogPO query);

    /**
     * 批量短剧查询观看人数
     *
     * @param query
     * @return
     */
    List<FastMemberRecentLogPO> queryWatchCountBatch(FastMemberRecentLogPO query);

    /**
     * 单部短剧根据剧集号分组
     *
     * @param query
     * @return
     */
    List<FastMemberRecentLogPO> queryWatchCountGroupBySeriesNum(FastMemberRecentLogPO query);

    Integer queryMemberRecentCount(FastMemberRecentLogPO query);

    List<FastMemberRecentLogPO> queryWatchRank(FastMemberRecentLogPO watchQuery);

    Set<Long> queryWatchMemberList(DramaAnalysisPO params);

    /**
     * 短剧详情-核心数据分析-观看人数
     *
     * @param query
     * @return
     */
    Integer queryWatchMemberCountByDramaId(FastMemberRecentLogPO query);

    /**
     * 短剧详情-核心数据分析-观看人数
     *
     * @param query
     * @return
     */
    Integer queryWatchMemberCountByDramaId2(FastMemberRecentLogPO query);

    /**
     * 剧集留存分析
     *
     * @param query
     * @return
     */
    List<FastMemberRecentLogPO> queryWatchCountGroupBySeriesNumV2(FastMemberRecentLogPO query);

    /**
     * 查询单部剧剧集明细
     *
     * @param seriesCount
     * @return
     */
    List<FastMemberRecentLogPO> queryDramaDetailSeriesGroupList(FastMemberRecentLogPO seriesCount);

    /**
     * 查询最近n小时内无观看的用户，发送推送消息
     *
     * @param query
     * @return
     */
    List<FastMemberRecentLogPO> queryPushMember(FastMemberRecentLogPO query);

    FastMemberRecentLogPO queryLastRecent(FastMemberRecentLogPO query);

    int queryCount(FastMemberRecentLogPO query);

    /**
     * 短剧完播人数分析
     */
    int queryWatchFinishMemberCountV2(DramaAnalysisPO po);

    List<FastDramaPO> queryWatchCountByDramaIds(FastMemberRecentLogPO queryRecent);
}
