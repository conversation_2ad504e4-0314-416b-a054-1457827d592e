package com.fast.mapper.datalake;

import com.fast.annotation.JingFenDataLake;
import com.fast.po.analysis.DramaAnalysisPO;
import com.fast.po.member.FastMemberOrderRechargePO;
import com.fast.po.promote.FastLinkPO;
import com.fast.po.promote.FastStatisLinkFreePO;
import com.fast.po.unlock.FastMemberUnlockStartLogPO;
import com.fast.vo.statistics.FreeDataIncomeAnalysisVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 免费广告观看记录日志(datalake)
 */
@JingFenDataLake
@Mapper
public interface DataLakeUnlockStartLogMapper {

    List<FastStatisLinkFreePO> statisLinkFreeGroupbyLinkId(FastStatisLinkFreePO query);

    List<FastStatisLinkFreePO> backUserGroupbyLinkId(FastStatisLinkFreePO query);

    Integer queryAddmemberCount(FastStatisLinkFreePO addMemeberStart);

    BigDecimal queryAddmemberIncomeTotal(FastStatisLinkFreePO addMemeberStart);

    FastStatisLinkFreePO queryD60(FastStatisLinkFreePO queryD60);

    List<Long> queryAddMemberIds(FastLinkPO queryAddMember);

    List<Long> queryAddMemberIdsByDate(FastLinkPO queryAddMember);

    FastStatisLinkFreePO queryAddmemberIncomeByDate(FastStatisLinkFreePO addMemeberStart);

    Integer updateById(FastMemberUnlockStartLogPO item);

    FastStatisLinkFreePO queryTodayIncome(FastStatisLinkFreePO query1);

    List<FastStatisLinkFreePO> queryD60Statis(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<FastStatisLinkFreePO> statisLinkAddMemberGroupbyLinkId(FastStatisLinkFreePO query);

    List<FastStatisLinkFreePO> queryTotalMemberIdsGroupByLinkId();

    FreeDataIncomeAnalysisVO queryFreeDataIncomeAnalysis(FastMemberUnlockStartLogPO freeDramaAnalysis);

    FastMemberOrderRechargePO queryRetailAddEcpm(DramaAnalysisPO param);

    Integer queryEcpmNum(DramaAnalysisPO query);

    List<DramaAnalysisPO> queryAdMemberGroupByHour(DramaAnalysisPO params);

    List<DramaAnalysisPO> queryAdMemberGroupByDate(DramaAnalysisPO params);

    List<FastStatisLinkFreePO> queryUnlockSuccessListGroupbyLinkId(FastStatisLinkFreePO query);

}
