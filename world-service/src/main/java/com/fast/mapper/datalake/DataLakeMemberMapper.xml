<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.datalake.DataLakeMemberMapper">

	<select id="queryRegisterMemberCountList" parameterType="DramaAnalysisPO" resultType="Integer">
		select count(*)
		from fast_member t
		left join fast_member_link ml on ml.member_id=t.id
		left join fast_link l on l.id=ml.first_link_id
		<where>
			<if test="dramaId != null">
				and l.`drama_id` = #{dramaId}
			</if>
			<if test="linkId != null">
				and ml.`last_link_id` = #{linkId}
			</if>
			<if test="linkIds != null and linkIds.length()>0">
				and ml.`last_link_id` in (${linkIds})
			</if>
			<if test="advUserIds != null and advUserIds.length()>0">
				and l.adv_user_id in (${advUserIds})
			</if>
			<if test="retailIds != null and retailIds.length()>0">
				and t.`retail_id` in (${retailIds})
			</if>
			<if test="miniIds != null and miniIds.length()>0">
				and t.`mini_id` in (${miniIds})
			</if>
			<if test="removeMountLinkFlag != null and removeMountLinkFlag == 1">
				and l.`link_type` != 3
			</if>
			<if test="contentType != null">
				and t.`content_type` = #{contentType}
			</if>
			<if test="createTimeS != null">
				and t.`create_time` &gt;= #{createTimeS}
			</if>
			<if test="createTimeE != null">
				and t.`create_time` &lt;= #{createTimeE}
			</if>
		</where>
	</select>

</mapper>
