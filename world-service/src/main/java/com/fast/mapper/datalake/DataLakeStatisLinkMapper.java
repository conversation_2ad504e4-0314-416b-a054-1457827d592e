package com.fast.mapper.datalake;

import com.fast.annotation.JingFenDataLake;
import com.fast.vo.promote.FastLinkQueryVO;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;

/**
 * 渠道报表(datalake)
 */
@JingFenDataLake
@Mapper
public interface DataLakeStatisLinkMapper {

    /**
     * 按条件查询-短剧的消耗金额
     *
     * @param fastLinkPO
     * @return
     */
    BigDecimal queryListDramaCostSum(FastLinkQueryVO fastLinkPO);

    /**
     * 查询新增人数
     *
     * @param fastLinkPO
     * @return
     */
    Integer queryLinkAddMemberNum(FastLinkQueryVO fastLinkPO);
}
