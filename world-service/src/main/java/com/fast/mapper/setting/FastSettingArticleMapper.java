/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastSettingArticlePO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastSettingArticleMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastSettingArticlePO
     * @return
     */
    FastSettingArticlePO queryById(FastSettingArticlePO fastSettingArticlePO);

    /**
     * 通过条件查询单个对象
     *
     * @param fastSettingArticlePO
     * @return
     */
    FastSettingArticlePO queryOne(FastSettingArticlePO fastSettingArticlePO);

    /**
     * 查询全部
     *
     * @param fastSettingArticlePO
     * @return
     */
    List<FastSettingArticlePO> queryList(FastSettingArticlePO fastSettingArticlePO);

    /**
     * 查询总数
     *
     * @param fastSettingArticlePO
     * @return
     */
    int queryCount(FastSettingArticlePO fastSettingArticlePO);

    /**
     * 可选新增
     *
     * @param fastSettingArticlePO
     * @return
     */
    int insertSelective(FastSettingArticlePO fastSettingArticlePO);

    /**
     * 更新
     *
     * @param fastSettingArticlePO
     * @return
     */
    int updateById(FastSettingArticlePO fastSettingArticlePO);

}
