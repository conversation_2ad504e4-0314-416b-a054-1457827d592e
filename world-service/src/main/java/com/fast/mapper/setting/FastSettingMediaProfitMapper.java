/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastSettingMediaProfitPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastSettingMediaProfitMapper {

    // 通过id查询单个对象
    FastSettingMediaProfitPO queryById(FastSettingMediaProfitPO entity);

    // 通过id查询单个对象
    FastSettingMediaProfitPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastSettingMediaProfitPO queryOne(FastSettingMediaProfitPO entity);

    // 查询全部
    List<FastSettingMediaProfitPO> queryList(FastSettingMediaProfitPO entity);

    // 查询总数
    int queryCount(FastSettingMediaProfitPO entity);

    // 可选新增
    int insertSelective(FastSettingMediaProfitPO entity);

    // 批量新增
    int insertBatch(List<FastSettingMediaProfitPO> list);

    // 更新
    int updateById(FastSettingMediaProfitPO entity);

    int updateInsertData(FastSettingMediaProfitPO entity);

}
