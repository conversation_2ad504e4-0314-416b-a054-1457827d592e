/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastMemberOrderSettingPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMemberOrderSettingMapper {

    // 通过id查询单个对象
    FastMemberOrderSettingPO queryById(FastMemberOrderSettingPO fastMemberOrderSettingPO);

    // 通过id查询单个对象
    FastMemberOrderSettingPO queryById(@Param("id") Integer id);

    // 通过type查询单个对象
    FastMemberOrderSettingPO queryByType(@Param("type") int type);

    // 通过条件查询单个对象
    FastMemberOrderSettingPO queryOne(FastMemberOrderSettingPO fastMemberOrderSettingPO);

    // 查询全部
    List<FastMemberOrderSettingPO> queryList(FastMemberOrderSettingPO fastMemberOrderSettingPO);

    // 查询总数
    int queryCount(FastMemberOrderSettingPO fastMemberOrderSettingPO);

    // 可选新增
    int insertSelective(FastMemberOrderSettingPO fastMemberOrderSettingPO);

}
