<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastSettingSystemMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastSettingSystem_columns">
		select t.`id`,t.`code`,t.`content`,t.`remark`,t.`create_time`,t.`update_time`,t.`creator_id`,t.`updator_id`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastSettingSystemPO">
		<include refid="FastSettingSystem_columns" />
	    from fast_setting_system t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastSettingSystemPO" resultType="FastSettingSystemPO">
		<include refid="FastSettingSystem_columns" />
	    from fast_setting_system t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastSettingSystemPO" resultType="FastSettingSystemPO">
		<include refid="FastSettingSystem_columns" />
	    from fast_setting_system t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastSettingSystemPO" resultType="int">
		select count(*)
	    from fast_setting_system t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="code != null">
			and t.`code` = #{code}
		</if>
		<if test="content != null">
			and t.`content` = #{content}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingSystemPO">
        insert into fast_setting_system
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="code != null">code,</if>
	        <if test="content != null">content,</if>
	        <if test="remark != null">remark,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="code != null">#{code},</if>
	        <if test="content != null">#{content},</if>
	        <if test="remark != null">#{remark},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingSystemPO">
        insert into fast_setting_system (
         code, content, `remark`, create_time, update_time, creator_id, updator_id
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.code}, #{item.content}, #{item.remark}, #{item.createTime}, #{item.updateTime}, #{item.creatorId}, #{item.updatorId}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastSettingSystemPO">
        update fast_setting_system
        <set>
         	<if test="code != null" >
               code = #{code},
            </if>
         	<if test="content != null" >
               content = #{content},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
