/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastSettingMediaSettlementPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastSettingMediaSettlementMapper {

    // 通过id查询单个对象
    FastSettingMediaSettlementPO queryById(FastSettingMediaSettlementPO entity);

    // 通过id查询单个对象
    FastSettingMediaSettlementPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastSettingMediaSettlementPO queryOne(FastSettingMediaSettlementPO entity);

    // 查询全部
    List<FastSettingMediaSettlementPO> queryList(FastSettingMediaSettlementPO entity);

    // 查询总数
    int queryCount(FastSettingMediaSettlementPO entity);

    // 可选新增
    int insertSelective(FastSettingMediaSettlementPO entity);

    // 批量新增
    int insertBatch(List<FastSettingMediaSettlementPO> list);

    // 更新
    int updateById(FastSettingMediaSettlementPO entity);

}
