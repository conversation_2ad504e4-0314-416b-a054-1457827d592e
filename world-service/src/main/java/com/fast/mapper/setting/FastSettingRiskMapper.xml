<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastSettingRiskMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastSettingRisk_columns">
		select 
		t.`id`,
		t.`mini_id`,
		t.`type`,
		t.`recharge_flag`,
		t.`recharge_time`,
		t.`recharge_total_flag`,
		t.`recharge_one_flag`,
		t.`total_base_count`,
		t.`total_recharge_count`,
		t.`total_select`,
		t.`free_all_count`,
		t.`free_series_count`,
		t.`free_series_ratio`,
		t.`one_select`,
		t.`creator_id`,
		t.`updator_id`,
		t.`create_time`,
		t.`update_time`,
		t.im_active_flag,
		t.im_his_flag,
		t.im_recharge_down_flag,
		t.im_recharge_down,
		t.im_recharge_times_flag,
		t.im_recharge_times,
		t.im_recent_flag,
		t.im_recent_time_all,
		t.im_recent_count,
		t.im_recent_time_one,
		t.im_recent_select
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastSettingRiskPO">
		<include refid="FastSettingRisk_columns" />
	    from fast_setting_risk t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastSettingRiskPO" resultType="FastSettingRiskPO">
		<include refid="FastSettingRisk_columns" />
	    from fast_setting_risk t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastSettingRiskPO" resultType="FastSettingRiskPO">
		<include refid="FastSettingRisk_columns" />
	    from fast_setting_risk t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastSettingRiskPO" resultType="int">
		select count(*)
	    from fast_setting_risk t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="type != null">
			and t.`type` = #{type}
		</if>
		<if test="rechargeFlag != null">
			and t.`recharge_flag` = #{rechargeFlag}
		</if>
		<if test="rechargeTime != null">
			and t.`recharge_time` = #{rechargeTime}
		</if>
		<if test="rechargeTotalFlag != null">
			and t.`recharge_total_flag` = #{rechargeTotalFlag}
		</if>
		<if test="rechargeOneFlag != null">
			and t.`recharge_one_flag` = #{rechargeOneFlag}
		</if>
		<if test="totalBaseCount != null">
			and t.`total_base_count` = #{totalBaseCount}
		</if>
		<if test="totalRechargeCount != null">
			and t.`total_recharge_count` = #{totalRechargeCount}
		</if>
		<if test="totalSelect != null">
			and t.`total_select` = #{totalSelect}
		</if>
		<if test="freeAllCount != null">
			and t.`free_all_count` = #{freeAllCount}
		</if>
		<if test="freeSeriesCount != null">
			and t.`free_series_count` = #{freeSeriesCount}
		</if>
		<if test="freeSeriesRatio != null">
			and t.`free_series_ratio` = #{freeSeriesRatio}
		</if>
		<if test="oneSelect != null">
			and t.`one_select` = #{oneSelect}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null"> and t.`update_time` = #{updateTime} </if>
		
		<if test="imActiveFlag != null">and t.im_active_flag = #{imActiveFlag}</if>
        <if test="imHisFlag != null">and t.im_his_flag = #{imHisFlag}</if>
        <if test="imRechargeDownFlag != null">and t.im_recharge_down_flag = #{imRechargeDownFlag}</if>
        <if test="imRechargeDown != null">and t.im_recharge_down = #{imRechargeDown}</if>
        <if test="imRechargeTimesFlag != null">and t.im_recharge_times_flag = #{imRechargeTimesFlag}</if>
        <if test="imRechargeTimes != null">and t.im_recharge_times = #{imRechargeTimes}</if>
        <if test="imRecentFlag != null">and t.im_recent_flag = #{imRecentFlag}</if>
        <if test="imRecentTimeAll != null">and t.im_recent_time_all = #{imRecentTimeAll}</if>
        <if test="imRecentCount != null">and t.im_recent_count = #{imRecentCount}</if>
        <if test="imRecentTimeOne != null">and t.im_recent_time_one = #{imRecentTimeOne}</if>
        <if test="imRecentSelect != null">and t.im_recent_select = #{imRecentSelect}</if>
		
		
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingRiskPO">
        insert into fast_setting_risk
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="miniId != null">mini_id,</if>
	        <if test="type != null">`type`,</if>
	        <if test="rechargeFlag != null">recharge_flag,</if>
	        <if test="rechargeTime != null">recharge_time,</if>
	        <if test="rechargeTotalFlag != null">recharge_total_flag,</if>
	        <if test="rechargeOneFlag != null">recharge_one_flag,</if>
	        <if test="totalBaseCount != null">total_base_count,</if>
	        <if test="totalRechargeCount != null">total_recharge_count,</if>
	        <if test="totalSelect != null">total_select,</if>
	        <if test="freeAllCount != null">free_all_count,</if>
	        <if test="freeSeriesCount != null">free_series_count,</if>
	        <if test="freeSeriesRatio != null">free_series_ratio,</if>
	        <if test="oneSelect != null">one_select,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time,</if>
	        
	        <if test="imActiveFlag != null">im_active_flag,</if>
	        <if test="imHisFlag != null">im_his_flag,</if>
	        <if test="imRechargeDownFlag != null">im_recharge_down_flag,</if>
	        <if test="imRechargeDown != null">im_recharge_down,</if>
	        <if test="imRechargeTimesFlag != null">im_recharge_times_flag,</if>
	        <if test="imRechargeTimes != null">im_recharge_times,</if>
	        <if test="imRecentFlag != null">im_recent_flag,</if>
	        <if test="imRecentTimeAll != null">im_recent_time_all,</if>
	        <if test="imRecentCount != null">im_recent_count,</if>
	        <if test="imRecentTimeOne != null">im_recent_time_one,</if>
	        <if test="imRecentSelect != null">im_recent_select</if>
	        
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="miniId != null">#{miniId},</if>
	        <if test="type != null">#{type},</if>
	        <if test="rechargeFlag != null">#{rechargeFlag},</if>
	        <if test="rechargeTime != null">#{rechargeTime},</if>
	        <if test="rechargeTotalFlag != null">#{rechargeTotalFlag},</if>
	        <if test="rechargeOneFlag != null">#{rechargeOneFlag},</if>
	        <if test="totalBaseCount != null">#{totalBaseCount},</if>
	        <if test="totalRechargeCount != null">#{totalRechargeCount},</if>
	        <if test="totalSelect != null">#{totalSelect},</if>
	        <if test="freeAllCount != null">#{freeAllCount},</if>
	        <if test="freeSeriesCount != null">#{freeSeriesCount},</if>
	        <if test="freeSeriesRatio != null">#{freeSeriesRatio},</if>
	        <if test="oneSelect != null">#{oneSelect},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime},</if>
	        
	        <if test="imActiveFlag != null">#{imActiveFlag},</if>
	        <if test="imHisFlag != null">#{imHisFlag},</if>
	        <if test="imRechargeDownFlag != null">#{imRechargeDownFlag},</if>
	        <if test="imRechargeDown != null">#{imRechargeDown},</if>
	        <if test="imRechargeTimesFlag != null">#{imRechargeTimesFlag},</if>
	        <if test="imRechargeTimes != null">#{imRechargeTimes},</if>
	        <if test="imRecentFlag != null">#{imRecentFlag},</if>
	        <if test="imRecentTimeAll != null">#{imRecentTimeAll},</if>
	        <if test="imRecentCount != null">#{imRecentCount},</if>
	        <if test="imRecentTimeOne != null">#{imRecentTimeOne},</if>
	        <if test="imRecentSelect != null">#{imRecentSelect}</if>
	        
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingRiskPO">
        insert into fast_setting_risk (
         mini_id, `type`, recharge_flag, recharge_time, recharge_total_flag, recharge_one_flag, total_base_count, total_recharge_count, total_select, free_all_count, free_series_count, free_series_ratio, one_select, creator_id, updator_id, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.miniId}, #{item.type}, #{item.rechargeFlag}, #{item.rechargeTime}, #{item.rechargeTotalFlag}, #{item.rechargeOneFlag}, #{item.totalBaseCount}, #{item.totalRechargeCount}, #{item.totalSelect}, #{item.freeAllCount}, #{item.freeSeriesCount}, #{item.freeSeriesRatio}, #{item.oneSelect}, #{item.creatorId}, #{item.updatorId}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastSettingRiskPO">
        update fast_setting_risk
        <set>
         	<if test="miniId != null" >
               mini_id = #{miniId},
            </if>
         	<if test="type != null" >
               `type` = #{type},
            </if>
         	<if test="rechargeFlag != null" >
               recharge_flag = #{rechargeFlag},
            </if>
         	<if test="rechargeTime != null" >
               recharge_time = #{rechargeTime},
            </if>
         	<if test="rechargeTotalFlag != null" >
               recharge_total_flag = #{rechargeTotalFlag},
            </if>
         	<if test="rechargeOneFlag != null" >
               recharge_one_flag = #{rechargeOneFlag},
            </if>
         	<if test="totalBaseCount != null" >
               total_base_count = #{totalBaseCount},
            </if>
         	<if test="totalRechargeCount != null" >
               total_recharge_count = #{totalRechargeCount},
            </if>
         	<if test="totalSelect != null" >
               total_select = #{totalSelect},
            </if>
         	<if test="freeAllCount != null" >
               free_all_count = #{freeAllCount},
            </if>
         	<if test="freeSeriesCount != null" >
               free_series_count = #{freeSeriesCount},
            </if>
         	<if test="freeSeriesRatio != null" >
               free_series_ratio = #{freeSeriesRatio},
            </if>
         	<if test="oneSelect != null" >
               one_select = #{oneSelect},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" > update_time = #{updateTime}, </if>
         	
         	<if test="imActiveFlag != null">im_active_flag = #{imActiveFlag},</if>
	        <if test="imHisFlag != null">im_his_flag = #{imHisFlag},</if>
	        <if test="imRechargeDownFlag != null">im_recharge_down_flag = #{imRechargeDownFlag},</if>
	        <if test="imRechargeDown != null">im_recharge_down = #{imRechargeDown},</if>
	        <if test="imRechargeTimesFlag != null">im_recharge_times_flag = #{imRechargeTimesFlag},</if>
	        <if test="imRechargeTimes != null">im_recharge_times = #{imRechargeTimes},</if>
	        <if test="imRecentFlag != null">im_recent_flag = #{imRecentFlag},</if>
	        <if test="imRecentTimeAll != null">im_recent_time_all = #{imRecentTimeAll},</if>
	        <if test="imRecentCount != null">im_recent_count = #{imRecentCount},</if>
	        <if test="imRecentTimeOne != null">im_recent_time_one = #{imRecentTimeOne},</if>
	        <if test="imRecentSelect != null">im_recent_select = #{imRecentSelect}</if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
