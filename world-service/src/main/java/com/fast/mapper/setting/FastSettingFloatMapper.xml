<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastSettingFloatMapper">
    <!-- 用于select查询公用抽取的列 -->
    <sql id="FastSettingFloat_columns">
        select t.`id`, t.`state`, t.`retail_id`, t.`official_id`, t.`float_name`, t.`float_location`, t.`float_text`,
               t.`jump_type`, t.`jump_content`, t.`jump_article_id`, t.`creator_id`, t.`updator_id`, t.`create_time`,
               t.`update_time`
    </sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" parameterType="FastSettingFloatPO" resultType="FastSettingFloatPO">
        <include refid="FastSettingFloat_columns"/>
            ,a.article_head_img
            ,a.article_title
            ,a.article_tag
            ,a.article_content
            ,a.article_code
            ,a.article_remark
            ,a.title_bottom
        from fast_setting_float t
        left join fast_setting_article a on a.id = t.jump_article_id
        <where>
            t.`id` = #{id}
        </where>
    </select>

    <!--通过条件查询单个对象  -->
    <select id="queryOne" parameterType="FastSettingFloatPO" resultType="FastSettingFloatPO">
        <include refid="FastSettingFloat_columns"/>
            ,a.article_head_img
            ,a.article_title
            ,a.article_tag
            ,a.article_content
            ,a.article_code
            ,a.article_remark
            ,a.title_bottom
        from fast_setting_float t
        left join fast_setting_article a on a.id = t.jump_article_id
        <where>
            <include refid="whereSQL"/>
        </where>
        limit 1
    </select>

    <!--分页查询集合  -->
    <select id="queryList" parameterType="FastSettingFloatPO" resultType="FastSettingFloatPO">
        <include refid="FastSettingFloat_columns"/>
            ,a.article_head_img
            ,a.article_title
            ,a.article_tag
            ,a.article_content
            ,a.article_code
            ,a.article_remark
            ,a.title_bottom
        from fast_setting_float t
        left join fast_setting_article a on a.id = t.jump_article_id
        <where>
            <include refid="whereSQL"/>
        </where>
        order by t.`id` desc
    </select>

    <!--查询总数  -->
    <select id="queryCount" parameterType="FastSettingFloatPO" resultType="int">
        select count(*)
        from fast_setting_float t
        <where>
            <include refid="whereSQL"/>
        </where>
    </select>

    <!-- 公共条件  -->
    <sql id="whereSQL">
        <if test="id != null">
            and t.`id` = #{id}
        </if>
        <if test="delFlag != null">
            and t.`del_flag` = #{delFlag}
        </if>
        <if test="state != null">
            and t.`state` = #{state}
        </if>
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="officialId != null">
            and t.`official_id` = #{officialId}
        </if>
        <if test="floatName != null">
            and t.`float_name` = #{floatName}
        </if>
        <if test="floatLocation != null">
            and t.`float_location` = #{floatLocation}
        </if>
        <if test="floatText != null">
            and t.`float_text` = #{floatText}
        </if>
        <if test="jumpType != null">
            and t.`jump_type` = #{jumpType}
        </if>
        <if test="jumpContent != null">
            and t.`jump_content` = #{jumpContent}
        </if>
        <if test="jumpArticleId != null">
            and t.`jump_article_id` = #{jumpArticleId}
        </if>
        <if test="creatorId != null">
            and t.`creator_id` = #{creatorId}
        </if>
        <if test="updatorId != null">
            and t.`updator_id` = #{updatorId}
        </if>
        <if test="createTime != null">
            and t.`create_time` = #{createTime}
        </if>
        <if test="updateTime != null">
            and t.`update_time` = #{updateTime}
        </if>
    </sql>

    <!-- 可选新增 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingFloatPO">
        insert into fast_setting_float
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="state != null">
                state,
            </if>
            <if test="retailId != null">
                retail_id,
            </if>
            <if test="officialId != null">
                official_id,
            </if>
            <if test="floatName != null">
                float_name,
            </if>
            <if test="floatLocation != null">
                float_location,
            </if>
            <if test="floatText != null">
                float_text,
            </if>
            <if test="jumpType != null">
                jump_type,
            </if>
            <if test="jumpContent != null">
                jump_content,
            </if>
            <if test="jumpArticleId != null">
                jump_article_id,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="updatorId != null">
                updator_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="state != null">
                #{state},
            </if>
            <if test="retailId != null">
                #{retailId},
            </if>
            <if test="officialId != null">
                #{officialId},
            </if>
            <if test="floatName != null">
                #{floatName},
            </if>
            <if test="floatLocation != null">
                #{floatLocation},
            </if>
            <if test="floatText != null">
                #{floatText},
            </if>
            <if test="jumpType != null">
                #{jumpType},
            </if>
            <if test="jumpContent != null">
                #{jumpContent},
            </if>
            <if test="jumpArticleId != null">
                #{jumpArticleId},
            </if>
            <if test="creatorId != null">
                #{creatorId},
            </if>
            <if test="updatorId != null">
                #{updatorId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="FastSettingFloatPO">
        update fast_setting_float
        <set>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="state != null">
                `state` = #{state},
            </if>
            <if test="floatName != null">
                float_name = #{floatName},
            </if>
            <if test="floatLocation != null">
                float_location = #{floatLocation},
            </if>
            <if test="floatText != null">
                float_text = #{floatText},
            </if>
            <if test="jumpType != null">
                jump_type = #{jumpType},
            </if>
            <if test="jumpContent != null">
                jump_content = #{jumpContent},
            </if>
            <if test="jumpArticleId != null">
                jump_article_id = #{jumpArticleId},
            </if>
            <if test="updatorId != null">
                updator_id = #{updatorId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
