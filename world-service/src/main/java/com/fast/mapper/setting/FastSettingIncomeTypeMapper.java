/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastSettingIncomeTypePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastSettingIncomeTypeMapper {

    // 通过id查询单个对象
    FastSettingIncomeTypePO queryById(FastSettingIncomeTypePO entity);

    // 通过id查询单个对象
    FastSettingIncomeTypePO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastSettingIncomeTypePO queryOne(FastSettingIncomeTypePO entity);

    // 查询全部
    List<FastSettingIncomeTypePO> queryList(FastSettingIncomeTypePO entity);

    // 查询总数
    int queryCount(FastSettingIncomeTypePO entity);

    // 可选新增
    int insertSelective(FastSettingIncomeTypePO entity);

    // 批量新增
    int insertBatch(List<FastSettingIncomeTypePO> list);

    // 更新
    int updateById(FastSettingIncomeTypePO entity);

    int deleteById(FastSettingIncomeTypePO entity);

    List<FastSettingIncomeTypePO> queryThirdIncomeTypeList();

    List<FastSettingIncomeTypePO> queryThirdIncomeTypeListV5();

    List<Integer> queryThirdIncomeTypeIdsV5();
}
