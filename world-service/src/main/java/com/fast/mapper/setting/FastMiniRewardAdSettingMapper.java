/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastMiniRewardAdSettingPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMiniRewardAdSettingMapper {

    // 通过id查询单个对象
    FastMiniRewardAdSettingPO queryById(FastMiniRewardAdSettingPO fastMiniRewardAdSettingPO);

    // 通过id查询单个对象
    FastMiniRewardAdSettingPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastMiniRewardAdSettingPO queryOne(FastMiniRewardAdSettingPO fastMiniRewardAdSettingPO);

    // 查询全部
    List<FastMiniRewardAdSettingPO> queryList(FastMiniRewardAdSettingPO fastMiniRewardAdSettingPO);

    // 查询总数
    int queryCount(FastMiniRewardAdSettingPO fastMiniRewardAdSettingPO);

    // 可选新增
    int insertSelective(FastMiniRewardAdSettingPO fastMiniRewardAdSettingPO);

    // 批量新增
    int insertBatch(List<FastMiniRewardAdSettingPO> list);

    // 更新
    int updateById(FastMiniRewardAdSettingPO fastMiniRewardAdSettingPO);

}
