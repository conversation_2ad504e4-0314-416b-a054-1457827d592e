<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastSettingMediaProfitMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastSettingMediaProfit_columns">
		select t.`id`,t.`media_type`,t.`company_ratio`,t.`staff_ratio`,t.`effect_month`,t.`state`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastSettingMediaProfitPO">
		<include refid="FastSettingMediaProfit_columns" />
	    from fast_setting_media_profit t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastSettingMediaProfitPO" resultType="FastSettingMediaProfitPO">
		<include refid="FastSettingMediaProfit_columns" />
	    from fast_setting_media_profit t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastSettingMediaProfitPO" resultType="FastSettingMediaProfitPO">
		<include refid="FastSettingMediaProfit_columns" />
	    from fast_setting_media_profit t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastSettingMediaProfitPO" resultType="int">
		select count(*)
	    from fast_setting_media_profit t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="mediaType != null">
			and t.`media_type` = #{mediaType}
		</if>
		<if test="companyRatio != null">
			and t.`company_ratio` = #{companyRatio}
		</if>
		<if test="staffRatio != null">
			and t.`staff_ratio` = #{staffRatio}
		</if>
		<if test="effectMonth != null">
			and t.`effect_month` = #{effectMonth}
		</if>
		<if test="state != null">
			and t.`state` = #{state}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingMediaProfitPO">
        insert into fast_setting_media_profit
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="mediaType != null">media_type,</if>
	        <if test="companyRatio != null">company_ratio,</if>
	        <if test="staffRatio != null">staff_ratio,</if>
	        <if test="effectMonth != null">effect_month,</if>
	        <if test="state != null">`state`,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="mediaType != null">#{mediaType},</if>
	        <if test="companyRatio != null">#{companyRatio},</if>
	        <if test="staffRatio != null">#{staffRatio},</if>
	        <if test="effectMonth != null">#{effectMonth},</if>
	        <if test="state != null">#{state},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingMediaProfitPO">
        insert into fast_setting_media_profit (
         media_type, company_ratio, staff_ratio, effect_month, creator_id, updator_id, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.mediaType}, #{item.companyRatio}, #{item.staffRatio}, #{item.effectMonth}, #{item.creatorId}, #{item.updatorId}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastSettingMediaProfitPO">
        update fast_setting_media_profit
        <set>
         	<if test="mediaType != null" >
               media_type = #{mediaType},
            </if>
         	<if test="companyRatio != null" >
               company_ratio = #{companyRatio},
            </if>
         	<if test="staffRatio != null" >
               staff_ratio = #{staffRatio},
            </if>
         	<if test="effectMonth != null" >
               effect_month = #{effectMonth},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

	<update id="updateInsertData">

	</update>
</mapper>
