/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastSettingMediaBackPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 媒体返点设置
 *
 * <AUTHOR>
 */
@Mapper
public interface FastSettingMediaBackMapper {

    // 通过id查询单个对象
    FastSettingMediaBackPO queryById(FastSettingMediaBackPO fastSettingMediaBackPO);

    // 通过id查询单个对象
    FastSettingMediaBackPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastSettingMediaBackPO queryOne(FastSettingMediaBackPO fastSettingMediaBackPO);

    // 查询全部
    List<FastSettingMediaBackPO> queryList(FastSettingMediaBackPO fastSettingMediaBackPO);

    List<FastSettingMediaBackPO> queryListUpdate(FastSettingMediaBackPO moneyDivideSettingPO);

    // 查询总数
    int queryCount(FastSettingMediaBackPO fastSettingMediaBackPO);

    // 可选新增
    int insertSelective(FastSettingMediaBackPO fastSettingMediaBackPO);

    // 更新
    int updateById(FastSettingMediaBackPO fastSettingMediaBackPO);

    /**
     * 查询返点规则
     *
     * @param beginOfDay
     * @return
     */
    List<FastSettingMediaBackPO> queryEffectGroupByMediaType(@Param("effectDate") Date beginOfDay);
}
