<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastSettingArticleMapper">
    <!-- 用于select查询公用抽取的列 -->
    <sql id="FastSettingArticle_columns">
        select t.`id`, t.`retail_id`, t.`official_id`, t.`article_head_img`, t.`article_title`, t.`article_tag`, t.`article_content`,
               t.`article_code`, t.`article_remark`, t.`title_bottom`, t.`creator_id`, t.`updator_id`, t.`create_time`,
               t.`update_time`
    </sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" parameterType="FastSettingArticlePO" resultType="FastSettingArticlePO">
        <include refid="FastSettingArticle_columns"/>
        from fast_setting_article t
        <where>
            t.`id` = #{id}
        </where>
    </select>

    <!--通过条件查询单个对象  -->
    <select id="queryOne" parameterType="FastSettingArticlePO" resultType="FastSettingArticlePO">
        <include refid="FastSettingArticle_columns"/>
        from fast_setting_article t
        <where>
            <include refid="whereSQL"/>
        </where>
        limit 1
    </select>

    <!--分页查询集合  -->
    <select id="queryList" parameterType="FastSettingArticlePO" resultType="FastSettingArticlePO">
        <include refid="FastSettingArticle_columns"/>
        from fast_setting_article t
        <where>
            <include refid="whereSQL"/>
        </where>
        order by t.`id` desc
    </select>

    <!--查询总数  -->
    <select id="queryCount" parameterType="FastSettingArticlePO" resultType="int">
        select count(*)
        from fast_setting_article t
        <where>
            <include refid="whereSQL"/>
        </where>
    </select>

    <!-- 公共条件  -->
    <sql id="whereSQL">
        <if test="id != null">
            and t.`id` = #{id}
        </if>
        <if test="delFlag != null">
            and t.`del_flag` = #{delFlag}
        </if>
        <if test="retailId != null">
            and t.`retail_id` = #{retailId}
        </if>
        <if test="officialId != null">
            and t.`official_id` = #{officialId}
        </if>
        <if test="articleHeadImg != null">
            and t.`article_head_img` = #{articleHeadImg}
        </if>
        <if test="articleTitle != null">
            and t.`article_title` = #{articleTitle}
        </if>
        <if test="articleTag != null">
            and t.`article_tag` = #{articleTag}
        </if>
        <if test="articleContent != null">
            and t.`article_content` = #{articleContent}
        </if>
        <if test="articleCode != null">
            and t.`article_code` = #{articleCode}
        </if>
        <if test="articleRemark != null">
            and t.`article_remark` = #{articleRemark}
        </if>
        <if test="titleBottom != null">
            and t.`title_bottom` = #{titleBottom}
        </if>
        <if test="creatorId != null">
            and t.`creator_id` = #{creatorId}
        </if>
        <if test="updatorId != null">
            and t.`updator_id` = #{updatorId}
        </if>
        <if test="createTime != null">
            and t.`create_time` = #{createTime}
        </if>
        <if test="updateTime != null">
            and t.`update_time` = #{updateTime}
        </if>
    </sql>

    <!-- 可选新增 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingArticlePO">
        insert into fast_setting_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="retailId != null">
                retail_id,
            </if>
            <if test="officialId != null">
                official_id,
            </if>
            <if test="articleHeadImg != null">
                article_head_img,
            </if>
            <if test="articleTitle != null">
                article_title,
            </if>
            <if test="articleTag != null">
                article_tag,
            </if>
            <if test="articleContent != null">
                article_content,
            </if>
            <if test="articleCode != null">
                article_code,
            </if>
            <if test="articleRemark != null">
                article_remark,
            </if>
            <if test="titleBottom != null">
                title_bottom,
            </if>
            <if test="creatorId != null">
                creator_id,
            </if>
            <if test="updatorId != null">
                updator_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="retailId != null">
                #{retailId},
            </if>
            <if test="officialId != null">
                #{officialId},
            </if>
            <if test="articleHeadImg != null">
                #{articleHeadImg},
            </if>
            <if test="articleTitle != null">
                #{articleTitle},
            </if>
            <if test="articleTag != null">
                #{articleTag},
            </if>
            <if test="articleContent != null">
                #{articleContent},
            </if>
            <if test="articleCode != null">
                #{articleCode},
            </if>
            <if test="articleRemark != null">
                #{articleRemark},
            </if>
            <if test="titleBottom != null">
                #{titleBottom},
            </if>
            <if test="creatorId != null">
                #{creatorId},
            </if>
            <if test="updatorId != null">
                #{updatorId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <!-- 更新 -->
    <update id="updateById" parameterType="FastSettingArticlePO">
        update fast_setting_article
        <set>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="articleHeadImg != null">
                article_head_img = #{articleHeadImg},
            </if>
            <if test="articleTitle != null">
                article_title = #{articleTitle},
            </if>
            <if test="articleTag != null">
                article_tag = #{articleTag},
            </if>
            <if test="articleContent != null">
                article_content = #{articleContent},
            </if>
            <if test="articleCode != null">
                article_code = #{articleCode},
            </if>
            <if test="articleRemark != null">
                article_remark = #{articleRemark},
            </if>
            <if test="titleBottom != null">
                title_bottom = #{titleBottom},
            </if>
            <if test="updatorId != null">
                updator_id = #{updatorId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>
