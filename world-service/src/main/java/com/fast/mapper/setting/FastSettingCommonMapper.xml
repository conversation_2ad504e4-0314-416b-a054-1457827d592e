<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastSettingCommonMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastSettingCommon_columns">
		select t.`id`,t.`app_type`,t.`cost_pop_flag`,t.`recharge_check_flag`,t.`create_time`,t.`creator_id`,t.`update_time`,t.`updator_id`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastSettingCommonPO">
		<include refid="FastSettingCommon_columns" />
	    from fast_setting_common t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastSettingCommonPO" resultType="FastSettingCommonPO">
		<include refid="FastSettingCommon_columns" />
	    from fast_setting_common t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastSettingCommonPO" resultType="FastSettingCommonPO">
		<include refid="FastSettingCommon_columns" />
	    from fast_setting_common t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastSettingCommonPO" resultType="int">
		select count(*)
	    from fast_setting_common t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="appType != null">
			and t.`app_type` = #{appType}
		</if>
		<if test="costPopFlag != null">
			and t.`cost_pop_flag` = #{costPopFlag}
		</if>
		<if test="rechargeCheckFlag != null">
			and t.`recharge_check_flag` = #{rechargeCheckFlag}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingCommonPO">
        insert into fast_setting_common
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="appType != null">app_type,</if>
	        <if test="costPopFlag != null">cost_pop_flag,</if>
	        <if test="rechargeCheckFlag != null">recharge_check_flag,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updateTime != null">update_time,</if>
	        <if test="updatorId != null">updator_id</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="appType != null">#{appType},</if>
	        <if test="costPopFlag != null">#{costPopFlag},</if>
	        <if test="rechargeCheckFlag != null">#{rechargeCheckFlag},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updateTime != null">#{updateTime},</if>
	        <if test="updatorId != null">#{updatorId}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingCommonPO">
        insert into fast_setting_common (
         app_type, cost_pop_flag, recharge_check_flag, create_time, creator_id, update_time, updator_id
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.appType}, #{item.costPopFlag}, #{item.rechargeCheckFlag}, #{item.createTime}, #{item.creatorId}, #{item.updateTime}, #{item.updatorId}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastSettingCommonPO">
        update fast_setting_common
        <set>
         	<if test="appType != null" >
               app_type = #{appType},
            </if>
         	<if test="costPopFlag != null" >
               cost_pop_flag = #{costPopFlag},
            </if>
         	<if test="rechargeCheckFlag != null" >
               recharge_check_flag = #{rechargeCheckFlag},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
