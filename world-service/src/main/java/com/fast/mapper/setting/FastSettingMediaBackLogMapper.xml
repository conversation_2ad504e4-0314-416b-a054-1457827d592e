<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastSettingMediaBackLogMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastSettingMediaBackLog_columns">
		select t.`id`,t.`pid`,t.`del_flag`,t.`back_ratio`,t.`effect_date`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastSettingMediaBackLogPO">
		<include refid="FastSettingMediaBackLog_columns" />
	    from fast_setting_media_back_log t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastSettingMediaBackLogPO" resultType="FastSettingMediaBackLogPO">
		<include refid="FastSettingMediaBackLog_columns" />
	    from fast_setting_media_back_log t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryLastEffectOne" parameterType="FastSettingMediaBackLogPO" resultType="FastSettingMediaBackLogPO">
		<include refid="FastSettingMediaBackLog_columns" />
		from fast_setting_media_back_log t
		<where>
			<include refid="whereSQL" />
		</where>
		order by t.`effect_date` desc
		limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastSettingMediaBackLogPO" resultType="FastSettingMediaBackLogPO">
		<include refid="FastSettingMediaBackLog_columns" />
			,(select u.user_name from fast_user u where u.id = t.creator_id) creatorName
			,b.media_type mediaType
	    from fast_setting_media_back_log t
		left join fast_setting_media_back b on b.id = t.pid
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<select id="queryHisList" parameterType="FastSettingMediaBackLogPO" resultType="FastSettingMediaBackLogPO">
		<include refid="FastSettingMediaBackLog_columns" />
		,(select u.user_name from fast_user u where u.id = t.creator_id) creatorName
		,b.media_type mediaType
		from fast_setting_media_back_log t
		left join fast_setting_media_back b on b.id = t.pid
		<where>
			<include refid="whereSQL" />
		</where>
		order by t.`effect_date` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastSettingMediaBackLogPO" resultType="int">
		select count(*)
	    from fast_setting_media_back_log t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="pid != null">
			and t.`pid` = #{pid}
		</if>
		<if test="delFlag != null">
			and t.`del_flag` = #{delFlag}
		</if>
		<if test="backRatio != null">
			and t.`back_ratio` = #{backRatio}
		</if>
		<if test="effectDate != null">
			and t.`effect_date` = #{effectDate, jdbcType=DATE}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingMediaBackLogPO">
        insert into fast_setting_media_back_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="pid != null">pid,</if>
	        <if test="backRatio != null">back_ratio,</if>
	        <if test="effectDate != null">effect_date,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="pid != null">#{pid},</if>
	        <if test="backRatio != null">#{backRatio},</if>
	        <if test="effectDate != null">#{effectDate, jdbcType=DATE},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastSettingMediaBackLogPO">
        update fast_setting_media_back_log
        <set>
			<if test="delFlag != null" >
				del_flag = #{delFlag},
			</if>
         	<if test="backRatio != null" >
               back_ratio = #{backRatio},
            </if>
         	<if test="effectDate != null" >
               effect_date = #{effectDate, jdbcType=DATE},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where id = #{id}
	</update>

</mapper>
