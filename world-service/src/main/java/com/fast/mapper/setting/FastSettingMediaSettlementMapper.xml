<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastSettingMediaSettlementMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastSettingMediaSettlement_columns">
		select t.`id`,t.`media_type`,t.`virtual_ratio`,t.`normal_ratio`,t.`effect_month`,t.`state`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastSettingMediaSettlementPO">
		<include refid="FastSettingMediaSettlement_columns" />
	    from fast_setting_media_settlement t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastSettingMediaSettlementPO" resultType="FastSettingMediaSettlementPO">
		<include refid="FastSettingMediaSettlement_columns" />
	    from fast_setting_media_settlement t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastSettingMediaSettlementPO" resultType="FastSettingMediaSettlementPO">
		<include refid="FastSettingMediaSettlement_columns" />
	    from fast_setting_media_settlement t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastSettingMediaSettlementPO" resultType="int">
		select count(*)
	    from fast_setting_media_settlement t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="mediaType != null">
			and t.`media_type` = #{mediaType}
		</if>
		<if test="virtualRatio != null">
			and t.`virtual_ratio` = #{virtualRatio}
		</if>
		<if test="normalRatio != null">
			and t.`normal_ratio` = #{normalRatio}
		</if>
		<if test="effectMonth != null">
			and t.`effect_month` = #{effectMonth}
		</if>
		<if test="state != null">
			and t.`state` = #{state}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingMediaSettlementPO">
        insert into fast_setting_media_settlement
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="mediaType != null">media_type,</if>
	        <if test="virtualRatio != null">virtual_ratio,</if>
	        <if test="normalRatio != null">normal_ratio,</if>
	        <if test="effectMonth != null">effect_month,</if>
	        <if test="state != null">`state`,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="mediaType != null">#{mediaType},</if>
	        <if test="virtualRatio != null">#{virtualRatio},</if>
	        <if test="normalRatio != null">#{normalRatio},</if>
	        <if test="effectMonth != null">#{effectMonth},</if>
	        <if test="state != null">#{state},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingMediaSettlementPO">
        insert into fast_setting_media_settlement (
         media_type, virtual_ratio, normal_ratio, effect_month, `state`, creator_id, updator_id, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.mediaType}, #{item.virtualRatio}, #{item.normalRatio}, #{item.effectMonth}, #{item.state}, #{item.creatorId}, #{item.updatorId}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastSettingMediaSettlementPO">
        update fast_setting_media_settlement
        <set>
         	<if test="mediaType != null" >
               media_type = #{mediaType},
            </if>
         	<if test="virtualRatio != null" >
               virtual_ratio = #{virtualRatio},
            </if>
         	<if test="normalRatio != null" >
               normal_ratio = #{normalRatio},
            </if>
         	<if test="effectMonth != null" >
               effect_month = #{effectMonth},
            </if>
         	<if test="state != null" >
				`state` = #{state},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
