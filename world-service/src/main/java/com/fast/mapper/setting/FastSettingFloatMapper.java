/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastSettingFloatPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastSettingFloatMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastSettingFloatPO
     * @return
     */
    FastSettingFloatPO queryById(FastSettingFloatPO fastSettingFloatPO);

    /**
     * 通过条件查询单个对象
     *
     * @param fastSettingFloatPO
     * @return
     */
    FastSettingFloatPO queryOne(FastSettingFloatPO fastSettingFloatPO);

    /**
     * 查询全部
     *
     * @param fastSettingFloatPO
     * @return
     */
    List<FastSettingFloatPO> queryList(FastSettingFloatPO fastSettingFloatPO);

    /**
     * 查询总数
     *
     * @param fastSettingFloatPO
     * @return
     */
    int queryCount(FastSettingFloatPO fastSettingFloatPO);

    /**
     * 可选新增
     *
     * @param fastSettingFloatPO
     * @return
     */
    int insertSelective(FastSettingFloatPO fastSettingFloatPO);

    /**
     * 更新
     *
     * @param fastSettingFloatPO
     * @return
     */
    int updateById(FastSettingFloatPO fastSettingFloatPO);

}
