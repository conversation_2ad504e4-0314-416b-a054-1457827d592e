<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastSettingMediaBackMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastSettingMediaBack_columns">
		select t.`id`,t.`media_type`,t.`back_ratio`,t.`effect_date`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastSettingMediaBackPO">
		<include refid="FastSettingMediaBack_columns" />
	    from fast_setting_media_back t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastSettingMediaBackPO" resultType="FastSettingMediaBackPO">
		<include refid="FastSettingMediaBack_columns" />
	    from fast_setting_media_back t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastSettingMediaBackPO" resultType="FastSettingMediaBackPO">
		<include refid="FastSettingMediaBack_columns" />
			,(select u.user_name from fast_user u where u.id = t.creator_id) creatorName
			,(select max(l.effect_date) from fast_setting_media_back_log l where l.pid = t.id and l.del_flag=0) effectDateMax
	    from fast_setting_media_back t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--分页查询集合  -->
	<select id="queryListUpdate" parameterType="FastSettingMediaBackPO" resultType="FastSettingMediaBackPO">
		select  pid AS id,
				effect_date AS effectDate,
				back_ratio AS backRatio
		from fast_setting_media_back_log
		where id in (SELECT SUBSTRING_INDEX(GROUP_CONCAT(id order by id desc),',',1) FROM `fast_setting_media_back_log` where effect_date &lt;= DATE_FORMAT(now(),'%Y-%m-%d') group by pid )
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastSettingMediaBackPO" resultType="int">
		select count(*)
	    from fast_setting_media_back t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="mediaType != null">
			and t.`media_type` = #{mediaType}
		</if>
		<if test="backRatio != null">
			and t.`back_ratio` = #{backRatio}
		</if>
		<if test="effectDate != null">
			and t.`effect_date` = #{effectDate, jdbcType=DATE}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingMediaBackPO">
        insert into fast_setting_media_back
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="mediaType != null">media_type,</if>
	        <if test="backRatio != null">back_ratio,</if>
	        <if test="effectDate != null">effect_date,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="mediaType != null">#{mediaType},</if>
	        <if test="backRatio != null">#{backRatio},</if>
	        <if test="effectDate != null">#{effectDate, jdbcType=DATE},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastSettingMediaBackPO">
        update fast_setting_media_back
        <set>
         	<if test="backRatio != null" >
               back_ratio = #{backRatio},
            </if>
         	<if test="effectDate != null" >
               effect_date = #{effectDate, jdbcType=DATE},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}
	</update>

	<select id="queryEffectGroupByMediaType" resultType="FastSettingMediaBackPO" >
		select pid as media_type,
		       back_ratio
		from fast_setting_media_back_log
		where id in (SELECT SUBSTRING_INDEX(GROUP_CONCAT(id order by id desc),',',1) FROM `fast_setting_media_back_log` where effect_date &lt;= #{effectDate}  group by pid )
		union all
		(SELECT
			media_type,
			0.00 AS back_ratio
		FROM
			fast_setting_media_back t1
		WHERE NOT EXISTS ( SELECT * FROM fast_setting_media_back_log WHERE effect_date &lt;= #{effectDate} AND t1.media_type = pid ))
	</select>

</mapper>
