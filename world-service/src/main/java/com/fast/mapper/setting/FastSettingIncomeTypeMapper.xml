<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastSettingIncomeTypeMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastSettingIncomeType_columns">
		select t.`id`,t.`title`,t.`type`,t.`time_type`,t.`remark`,t.`create_time`,t.`update_time`,t.`cp_devide_flag`,t.`cp_income_from`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastSettingIncomeTypePO">
		<include refid="FastSettingIncomeType_columns" />
	    from fast_setting_income_type t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastSettingIncomeTypePO" resultType="FastSettingIncomeTypePO">
		<include refid="FastSettingIncomeType_columns" />
	    from fast_setting_income_type t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastSettingIncomeTypePO" resultType="FastSettingIncomeTypePO">
		<include refid="FastSettingIncomeType_columns" />
	    from fast_setting_income_type t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastSettingIncomeTypePO" resultType="int">
		select count(*)
	    from fast_setting_income_type t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="neqId != null">
			and t.`id` != #{neqId}
		</if>
		<if test="title != null">
			and t.`title` = #{title}
		</if>
		<if test="type != null">
			and t.`type` = #{type}
		</if>
		<if test="timeType != null">
			and t.`time_type` = #{timeType}
		</if>
		<if test="cpIncomeFrom != null">
			and t.`cp_income_from` = #{cpIncomeFrom}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>


	<delete id="deleteById" parameterType="java.lang.Integer">
		delete from fast_setting_income_type where id = #{id}
	</delete>

	<select id="queryThirdIncomeTypeList" parameterType="FastSettingIncomeTypePO" resultType="FastSettingIncomeTypePO">
		<include refid="FastSettingIncomeType_columns" />
		from fast_setting_income_type t
		where t.cp_devide_flag = 1 AND t.id not in (1,2,3,4)
    </select>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingIncomeTypePO">
		insert into fast_setting_income_type
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="title != null">title,</if>
			<if test="type != null">`type`,</if>
			<if test="timeType != null">time_type,</if>
			<if test="cpIncomeFrom != null">cp_income_from,</if>
			<if test="remark != null">remark,</if>
			<if test="createTime != null">create_time,</if>
			<if test="updateTime != null">update_time,</if>
			<if test="cpDevideFlag != null">cp_devide_flag</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="title != null">#{title},</if>
			<if test="type != null">#{type},</if>
			<if test="timeType != null">#{timeType},</if>
			<if test="cpIncomeFrom != null">#{cpIncomeFrom},</if>
			<if test="remark != null">#{remark},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="cpDevideFlag != null">#{cpDevideFlag}</if>
		</trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastSettingIncomeTypePO">
		insert into fast_setting_income_type (
		title, `type`, time_type, cp_income_from, `remark`, create_time, update_time, cp_devide_flag
		) values
		<foreach collection="list" item="item" separator=",">
			(
			#{item.title}, #{item.type}, #{item.timeType}, #{item.cpIncomeFrom}, #{item.remark}, #{item.createTime}, #{item.updateTime}, #{item.cpDevideFlag}
			)
		</foreach>
	</insert>

	<!-- 更新 -->
	<update id="updateById" parameterType="FastSettingIncomeTypePO">
		update fast_setting_income_type
		<set>
			<if test="title != null" >
				title = #{title},
			</if>
			<if test="type != null" >
				`type` = #{type},
			</if>
			<if test="timeType != null" >
				time_type = #{timeType},
			</if>
			<if test="cpIncomeFrom != null" >
				cp_income_from = #{cpIncomeFrom},
			</if>
			<if test="remark != null" >
				remark = #{remark},
			</if>
			<if test="createTime != null" >
				create_time = #{createTime},
			</if>
			<if test="updateTime != null" >
				update_time = #{updateTime},
			</if>
			<if test="cpDevideFlag != null" >
				cp_devide_flag = #{cpDevideFlag},
			</if>
		</set>
		where
		id = #{id}
	</update>

	<select id="queryThirdIncomeTypeListV5" resultType="com.fast.po.setting.FastSettingIncomeTypePO">
		<include refid="FastSettingIncomeType_columns" />
		from fast_setting_income_type t
		where t.cp_devide_flag = 1
		<!--6三方分发-->
		AND t.cp_income_from = 6
		and t.time_type = 3
	</select>

	<select id="queryThirdIncomeTypeIdsV5" resultType="Integer">
		select t.id
		from fast_setting_income_type t
		where t.cp_devide_flag = 1
		<!--6三方分发-->
		AND t.cp_income_from = 6
		and t.time_type = 3
	</select>
</mapper>
