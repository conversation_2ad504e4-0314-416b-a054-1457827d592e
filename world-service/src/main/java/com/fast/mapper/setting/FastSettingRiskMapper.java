/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastSettingRiskPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastSettingRiskMapper {

    // 通过id查询单个对象
    FastSettingRiskPO queryById(FastSettingRiskPO fastSettingRiskPO);

    // 通过id查询单个对象
    FastSettingRiskPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastSettingRiskPO queryOne(FastSettingRiskPO fastSettingRiskPO);

    // 查询全部
    List<FastSettingRiskPO> queryList(FastSettingRiskPO fastSettingRiskPO);

    // 查询总数
    int queryCount(FastSettingRiskPO fastSettingRiskPO);

    // 可选新增
    int insertSelective(FastSettingRiskPO fastSettingRiskPO);

    // 批量新增
    int insertBatch(List<FastSettingRiskPO> list);

    // 更新
    int updateById(FastSettingRiskPO fastSettingRiskPO);

}
