/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastSettingSystemPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastSettingSystemMapper {

    // 通过id查询单个对象
    FastSettingSystemPO queryById(FastSettingSystemPO fastSettingSystemPO);

    // 通过id查询单个对象
    FastSettingSystemPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastSettingSystemPO queryOne(FastSettingSystemPO fastSettingSystemPO);

    // 查询全部
    List<FastSettingSystemPO> queryList(FastSettingSystemPO fastSettingSystemPO);

    // 查询总数
    int queryCount(FastSettingSystemPO fastSettingSystemPO);

    // 可选新增
    int insertSelective(FastSettingSystemPO fastSettingSystemPO);

    // 批量新增
    int insertBatch(List<FastSettingSystemPO> list);

    // 更新
    int updateById(FastSettingSystemPO fastSettingSystemPO);

}
