<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastMemberOrderSettingMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMemberOrderSetting_columns">
		select t.`id`,t.`state`,t.`type`,t.`fee_rate`,t.`profit_rate`,t.`start_time`,t.`end_time`,t.`create_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastMemberOrderSettingPO">
		<include refid="FastMemberOrderSetting_columns" />
	    from fast_member_order_setting t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

    <!-- 通过id查询单个对象 -->
    <select id="queryByType" resultType="FastMemberOrderSettingPO">
		<include refid="FastMemberOrderSetting_columns" />
	    from fast_member_order_setting t
        <where>
	        t.`type` = #{type} and `state`=1
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMemberOrderSettingPO" resultType="FastMemberOrderSettingPO">
		<include refid="FastMemberOrderSetting_columns" />
	    from fast_member_order_setting t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMemberOrderSettingPO" resultType="FastMemberOrderSettingPO">
		<include refid="FastMemberOrderSetting_columns" />
	    from fast_member_order_setting t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMemberOrderSettingPO" resultType="int">
		select count(*)
	    from fast_member_order_setting t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="type != null">
			and t.`type` = #{type}
		</if>
		<if test="state != null">
			and t.`state` = #{state}
		</if>
		<if test="feeRate != null">
			and t.`fee_rate` = #{feeRate}
		</if>
		<if test="profitRate != null">
			and t.`profit_rate` = #{profitRate}
		</if>
		<if test="startTime != null">
			and t.`start_time` = #{startTime}
		</if>
		<if test="endTime != null">
			and t.`end_time` = #{endTime}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMemberOrderSettingPO">
        insert into fast_member_order_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="state != null">`state`,</if>
	        <if test="type != null">`type`,</if>
	        <if test="feeRate != null">fee_rate,</if>
	        <if test="profitRate != null">profit_rate,</if>
	        <if test="startTime != null">start_time,</if>
	        <if test="endTime != null">end_time,</if>
	        <if test="createTime != null">create_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="state != null">#{state},</if>
	        <if test="type != null">#{type},</if>
	        <if test="feeRate != null">#{feeRate},</if>
	        <if test="profitRate != null">#{profitRate},</if>
	        <if test="startTime != null">#{startTime},</if>
	        <if test="endTime != null">#{endTime},</if>
	        <if test="createTime != null">#{createTime}</if>
        </trim>
	</insert>

</mapper>
