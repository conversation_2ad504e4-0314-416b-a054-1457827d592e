/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastSettingCommonPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastSettingCommonMapper {

    // 通过id查询单个对象
    FastSettingCommonPO queryById(FastSettingCommonPO fastSettingCommonPO);

    // 通过id查询单个对象
    FastSettingCommonPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastSettingCommonPO queryOne(FastSettingCommonPO fastSettingCommonPO);

    // 查询全部
    List<FastSettingCommonPO> queryList(FastSettingCommonPO fastSettingCommonPO);

    // 查询总数
    int queryCount(FastSettingCommonPO fastSettingCommonPO);

    // 可选新增
    int insertSelective(FastSettingCommonPO fastSettingCommonPO);

    // 批量新增
    int insertBatch(List<FastSettingCommonPO> list);

    // 更新
    int updateById(FastSettingCommonPO fastSettingCommonPO);

}
