/*
 * Powered By fast.up
 */
package com.fast.mapper.setting;

import com.fast.po.setting.FastSettingMediaBackLogPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 媒体返点设置
 *
 * <AUTHOR>
 */
@Mapper
public interface FastSettingMediaBackLogMapper {

    // 通过id查询单个对象
    FastSettingMediaBackLogPO queryById(FastSettingMediaBackLogPO fastSettingMediaBackLogPO);

    // 通过id查询单个对象
    FastSettingMediaBackLogPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastSettingMediaBackLogPO queryOne(FastSettingMediaBackLogPO fastSettingMediaBackLogPO);

    FastSettingMediaBackLogPO queryLastEffectOne(FastSettingMediaBackLogPO moneyDivideSettingPO);

    // 查询全部
    List<FastSettingMediaBackLogPO> queryList(FastSettingMediaBackLogPO fastSettingMediaBackLogPO);

    // 查询全部
    List<FastSettingMediaBackLogPO> queryHisList(FastSettingMediaBackLogPO fastSettingMediaBackLogPO);

    // 查询总数
    int queryCount(FastSettingMediaBackLogPO fastSettingMediaBackLogPO);

    // 可选新增
    int insertSelective(FastSettingMediaBackLogPO fastSettingMediaBackLogPO);

    // 更新
    int updateById(FastSettingMediaBackLogPO fastSettingMediaBackLogPO);

}
