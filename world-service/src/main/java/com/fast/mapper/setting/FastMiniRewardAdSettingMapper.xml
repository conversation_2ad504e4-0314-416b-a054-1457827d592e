<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.setting.FastMiniRewardAdSettingMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMiniRewardAdSetting_columns">
		select t.`id`,t.`mini_id`,t.`enter_type`,t.`android_allow_flag`,t.`ios_allow_flag`,t.`android_register_days`,t.`android_not_charge_days`,t.`android_sequeue`,t.`android_per_unlock_amount`,t.`android_unlock_max`,t.`ios_register_days`,t.`ios_not_charge_days`,t.`ios_sequeue`,t.`ios_per_unlock_amount`,t.`ios_unlock_max`,t.`creator_id`,t.`updator_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastMiniRewardAdSettingPO">
		<include refid="FastMiniRewardAdSetting_columns" />
	    from fast_mini_reward_ad_setting t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMiniRewardAdSettingPO" resultType="FastMiniRewardAdSettingPO">
		<include refid="FastMiniRewardAdSetting_columns" />
	    from fast_mini_reward_ad_setting t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMiniRewardAdSettingPO" resultType="FastMiniRewardAdSettingPO">
		<include refid="FastMiniRewardAdSetting_columns" />
	    from fast_mini_reward_ad_setting t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMiniRewardAdSettingPO" resultType="int">
		select count(*)
	    from fast_mini_reward_ad_setting t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="enterType != null">
			and t.`enter_type` = #{enterType}
		</if>
		<if test="androidAllowFlag != null">
			and t.`android_allow_flag` = #{androidAllowFlag}
		</if>
		<if test="iosAllowFlag != null">
			and t.`ios_allow_flag` = #{iosAllowFlag}
		</if>
		<if test="androidRegisterDays != null">
			and t.`android_register_days` = #{androidRegisterDays}
		</if>
		<if test="androidNotChargeDays != null">
			and t.`android_not_charge_days` = #{androidNotChargeDays}
		</if>
		<if test="androidSequeue != null">
			and t.`android_sequeue` = #{androidSequeue}
		</if>
		<if test="androidPerUnlockAmount != null">
			and t.`android_per_unlock_amount` = #{androidPerUnlockAmount}
		</if>
		<if test="androidUnlockMax != null">
			and t.`android_unlock_max` = #{androidUnlockMax}
		</if>
		<if test="iosRegisterDays != null">
			and t.`ios_register_days` = #{iosRegisterDays}
		</if>
		<if test="iosNotChargeDays != null">
			and t.`ios_not_charge_days` = #{iosNotChargeDays}
		</if>
		<if test="iosSequeue != null">
			and t.`ios_sequeue` = #{iosSequeue}
		</if>
		<if test="iosPerUnlockAmount != null">
			and t.`ios_per_unlock_amount` = #{iosPerUnlockAmount}
		</if>
		<if test="iosUnlockMax != null">
			and t.`ios_unlock_max` = #{iosUnlockMax}
		</if>
		<if test="creatorId != null">
			and t.`creator_id` = #{creatorId}
		</if>
		<if test="updatorId != null">
			and t.`updator_id` = #{updatorId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniRewardAdSettingPO">
        insert into fast_mini_reward_ad_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="miniId != null">mini_id,</if>
	        <if test="enterType != null">enter_type,</if>
	        <if test="androidAllowFlag != null">android_allow_flag,</if>
	        <if test="iosAllowFlag != null">ios_allow_flag,</if>
	        <if test="androidRegisterDays != null">android_register_days,</if>
	        <if test="androidNotChargeDays != null">android_not_charge_days,</if>
	        <if test="androidSequeue != null">android_sequeue,</if>
	        <if test="androidPerUnlockAmount != null">android_per_unlock_amount,</if>
	        <if test="androidUnlockMax != null">android_unlock_max,</if>
	        <if test="iosRegisterDays != null">ios_register_days,</if>
	        <if test="iosNotChargeDays != null">ios_not_charge_days,</if>
	        <if test="iosSequeue != null">ios_sequeue,</if>
	        <if test="iosPerUnlockAmount != null">ios_per_unlock_amount,</if>
	        <if test="iosUnlockMax != null">ios_unlock_max,</if>
	        <if test="creatorId != null">creator_id,</if>
	        <if test="updatorId != null">updator_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="miniId != null">#{miniId},</if>
	        <if test="enterType != null">#{enterType},</if>
	        <if test="androidAllowFlag != null">#{androidAllowFlag},</if>
	        <if test="iosAllowFlag != null">#{iosAllowFlag},</if>
	        <if test="androidRegisterDays != null">#{androidRegisterDays},</if>
	        <if test="androidNotChargeDays != null">#{androidNotChargeDays},</if>
	        <if test="androidSequeue != null">#{androidSequeue},</if>
	        <if test="androidPerUnlockAmount != null">#{androidPerUnlockAmount},</if>
	        <if test="androidUnlockMax != null">#{androidUnlockMax},</if>
	        <if test="iosRegisterDays != null">#{iosRegisterDays},</if>
	        <if test="iosNotChargeDays != null">#{iosNotChargeDays},</if>
	        <if test="iosSequeue != null">#{iosSequeue},</if>
	        <if test="iosPerUnlockAmount != null">#{iosPerUnlockAmount},</if>
	        <if test="iosUnlockMax != null">#{iosUnlockMax},</if>
	        <if test="creatorId != null">#{creatorId},</if>
	        <if test="updatorId != null">#{updatorId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniRewardAdSettingPO">
        insert into fast_mini_reward_ad_setting (
         mini_id, enter_type, android_allow_flag, ios_allow_flag, android_register_days, android_not_charge_days, android_sequeue, android_per_unlock_amount, android_unlock_max, ios_register_days, ios_not_charge_days, ios_sequeue, ios_per_unlock_amount, ios_unlock_max, creator_id, updator_id, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.miniId}, #{item.enterType}, #{item.androidAllowFlag}, #{item.iosAllowFlag}, #{item.androidRegisterDays}, #{item.androidNotChargeDays}, #{item.androidSequeue}, #{item.androidPerUnlockAmount}, #{item.androidUnlockMax}, #{item.iosRegisterDays}, #{item.iosNotChargeDays}, #{item.iosSequeue}, #{item.iosPerUnlockAmount}, #{item.iosUnlockMax}, #{item.creatorId}, #{item.updatorId}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastMiniRewardAdSettingPO">
        update fast_mini_reward_ad_setting
        <set>
         	<if test="miniId != null" >
               mini_id = #{miniId},
            </if>
         	<if test="enterType != null" >
               enter_type = #{enterType},
            </if>
         	<if test="androidAllowFlag != null" >
               android_allow_flag = #{androidAllowFlag},
            </if>
         	<if test="iosAllowFlag != null" >
               ios_allow_flag = #{iosAllowFlag},
            </if>
         	<if test="androidRegisterDays != null" >
               android_register_days = #{androidRegisterDays},
            </if>
         	<if test="androidNotChargeDays != null" >
               android_not_charge_days = #{androidNotChargeDays},
            </if>
         	<if test="androidSequeue != null" >
               android_sequeue = #{androidSequeue},
            </if>
         	<if test="androidPerUnlockAmount != null" >
               android_per_unlock_amount = #{androidPerUnlockAmount},
            </if>
         	<if test="androidUnlockMax != null" >
               android_unlock_max = #{androidUnlockMax},
            </if>
         	<if test="iosRegisterDays != null" >
               ios_register_days = #{iosRegisterDays},
            </if>
         	<if test="iosNotChargeDays != null" >
               ios_not_charge_days = #{iosNotChargeDays},
            </if>
         	<if test="iosSequeue != null" >
               ios_sequeue = #{iosSequeue},
            </if>
         	<if test="iosPerUnlockAmount != null" >
               ios_per_unlock_amount = #{iosPerUnlockAmount},
            </if>
         	<if test="iosUnlockMax != null" >
               ios_unlock_max = #{iosUnlockMax},
            </if>
         	<if test="creatorId != null" >
               creator_id = #{creatorId},
            </if>
         	<if test="updatorId != null" >
               updator_id = #{updatorId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
