/*
 * Powered By fast.up
 */
package com.fast.mapper.subscribe;

import com.fast.po.subscribe.FastMemberSubscribePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMemberSubscribeMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastMemberSubscribePO
     * @return
     */
    FastMemberSubscribePO queryById(FastMemberSubscribePO fastMemberSubscribePO);

    /**
     * 通过id查询单个对象
     *
     * @param id
     * @return
     */
    FastMemberSubscribePO queryById(@Param("id") Integer id);

    /**
     * 通过条件查询单个对象
     *
     * @param fastMemberSubscribePO
     * @return
     */
    FastMemberSubscribePO queryOne(FastMemberSubscribePO fastMemberSubscribePO);

    /**
     * 查询全部
     *
     * @param fastMemberSubscribePO
     * @return
     */
    List<FastMemberSubscribePO> queryList(FastMemberSubscribePO fastMemberSubscribePO);

    List<FastMemberSubscribePO> queryJobList(FastMemberSubscribePO fastMemberSubscribePO);

    /**
     * 查询总数
     *
     * @param fastMemberSubscribePO
     * @return
     */
    int queryCount(FastMemberSubscribePO fastMemberSubscribePO);

    /**
     * 可选新增
     *
     * @param fastMemberSubscribePO
     * @return
     */
    int insertSelective(FastMemberSubscribePO fastMemberSubscribePO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastMemberSubscribePO> list);

    /**
     * 更新
     *
     * @param fastMemberSubscribePO
     * @return
     */
    int updateById(FastMemberSubscribePO fastMemberSubscribePO);

}
