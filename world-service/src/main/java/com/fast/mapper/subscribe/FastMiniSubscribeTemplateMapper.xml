<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.subscribe.FastMiniSubscribeTemplateMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMiniSubscribeTemplate_columns">
		select t.`id`,t.`type`,t.`mini_id`,t.`wechat_template_id`,t.`remark`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastMiniSubscribeTemplatePO">
		<include refid="FastMiniSubscribeTemplate_columns" />
	    from fast_mini_subscribe_template t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMiniSubscribeTemplatePO" resultType="FastMiniSubscribeTemplatePO">
		<include refid="FastMiniSubscribeTemplate_columns" />
	    from fast_mini_subscribe_template t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMiniSubscribeTemplatePO" resultType="FastMiniSubscribeTemplatePO">
		<include refid="FastMiniSubscribeTemplate_columns" />
	    from fast_mini_subscribe_template t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMiniSubscribeTemplatePO" resultType="int">
		select count(*)
	    from fast_mini_subscribe_template t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="type != null">
			and t.`type` = #{type}
		</if>
		<if test="miniId != null">
			and t.`mini_id` = #{miniId}
		</if>
		<if test="wechatTemplateId != null">
			and t.`wechat_template_id` = #{wechatTemplateId}
		</if>
		<if test="remark != null">
			and t.`remark` = #{remark}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniSubscribeTemplatePO">
        insert into fast_mini_subscribe_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="type != null">`type`,</if>
	        <if test="miniId != null">mini_id,</if>
	        <if test="wechatTemplateId != null">wechat_template_id,</if>
	        <if test="remark != null">remark</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="type != null">#{type},</if>
	        <if test="miniId != null">#{miniId},</if>
	        <if test="wechatTemplateId != null">#{wechatTemplateId},</if>
	        <if test="remark != null">#{remark}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastMiniSubscribeTemplatePO">
        insert into fast_mini_subscribe_template (
         type, mini_id, wechat_template_id, remark
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.type}, #{item.miniId}, #{item.wechatTemplateId}, #{item.remark}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastMiniSubscribeTemplatePO">
        update fast_mini_subscribe_template
        <set>
         	<if test="type != null" >
               `type` = #{type},
            </if>
         	<if test="miniId != null" >
               mini_id = #{miniId},
            </if>
         	<if test="wechatTemplateId != null" >
               wechat_template_id = #{wechatTemplateId},
            </if>
         	<if test="remark != null" >
               remark = #{remark},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

	<select id="queryListByMiniId" resultType="com.fast.po.subscribe.FastMiniSubscribeTemplatePO">
		select t.`id`,t.`type`,t.`mini_id`,t.`wechat_template_id`,t.`remark`
	    from fast_mini_subscribe_template t
		<where>
	        t.`mini_id` = #{miniId}
	    </where>
    </select>

	<update id="updateByMiniId">
		update fast_mini_subscribe_template
		set wechat_template_id = #{wechatTemplateId}
		where mini_id = #{miniId}
		and type = #{type}
	</update>
</mapper>
