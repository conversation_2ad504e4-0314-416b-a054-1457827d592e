<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.subscribe.FastMemberSubscribeMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastMemberSubscribe_columns">
		select t.`id`,t.`state`,t.`member_id`,t.openid,t.`template_id`,t.`drama_id`,t.`create_time`,t.`update_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastMemberSubscribePO">
		<include refid="FastMemberSubscribe_columns" />
	    from fast_member_subscribe t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastMemberSubscribePO" resultType="FastMemberSubscribePO">
		<include refid="FastMemberSubscribe_columns" />
	    from fast_member_subscribe t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastMemberSubscribePO" resultType="FastMemberSubscribePO">
		<include refid="FastMemberSubscribe_columns" />
	    from fast_member_subscribe t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>
	
	<select id="queryJobList" parameterType="FastMemberSubscribePO" resultType="FastMemberSubscribePO">
		<include refid="FastMemberSubscribe_columns" />
		,m.type memberType
	    from fast_member_subscribe t
	    left join fast_member_all m on m.id = t.member_id
        <where>
        	<include refid="whereSQL" />
        </where>
		limit ${start},${limit}
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastMemberSubscribePO" resultType="int">
		select count(*)
	    from fast_member_subscribe t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="state != null">
			and t.`state` = #{state}
		</if>
		<if test="retailId != null">
			and t.`retail_id` = #{retailId}
		</if>
		<if test="memberId != null">
			and t.`member_id` = #{memberId}
		</if>
		<if test="templateId != null">
			and t.`template_id` = #{templateId}
		</if>
		<if test="dramaId != null">
			and t.`drama_id` = #{dramaId}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
		<if test="updateTime != null">
			and t.`update_time` = #{updateTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastMemberSubscribePO">
        insert into fast_member_subscribe
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="state != null">`state`,</if>
	        <if test="retailId != null">retail_id,</if>
	        <if test="memberId != null">member_id,</if>
	        <if test="openid != null">openid,</if>
	        <if test="templateId != null">template_id,</if>
	        <if test="dramaId != null">drama_id,</if>
	        <if test="createTime != null">create_time,</if>
	        <if test="updateTime != null">update_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="state != null">#{state},</if>
	        <if test="retailId != null">#{retailId},</if>
	        <if test="memberId != null">#{memberId},</if>
	        <if test="openid != null">#{openid},</if>
	        <if test="templateId != null">#{templateId},</if>
	        <if test="dramaId != null">#{dramaId},</if>
	        <if test="createTime != null">#{createTime},</if>
	        <if test="updateTime != null">#{updateTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastMemberSubscribePO">
        insert into fast_member_subscribe (
         state, member_id, template_id, drama_id, create_time, update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.state}, #{item.memberId}, #{item.templateId}, #{item.dramaId}, #{item.createTime}, #{item.updateTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastMemberSubscribePO">
        update fast_member_subscribe
        <set>
         	<if test="state != null" >
               `state` = #{state},
            </if>
         	<if test="memberId != null" >
               member_id = #{memberId},
            </if>
         	<if test="templateId != null" >
               template_id = #{templateId},
            </if>
         	<if test="dramaId != null" >
               drama_id = #{dramaId},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
         	<if test="updateTime != null" >
               update_time = #{updateTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
