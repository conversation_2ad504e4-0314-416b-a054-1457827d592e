/*
 * Powered By fast.up
 */
package com.fast.mapper.subscribe;

import com.fast.po.subscribe.FastMiniSubscribeTemplatePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastMiniSubscribeTemplateMapper {

    /**
     * 通过id查询单个对象
     *
     * @param fastMiniSubscribeTemplatePO
     * @return
     */
    FastMiniSubscribeTemplatePO queryById(FastMiniSubscribeTemplatePO fastMiniSubscribeTemplatePO);

    /**
     * 通过id查询单个对象
     *
     * @param id
     * @return
     */
    FastMiniSubscribeTemplatePO queryById(@Param("id") Integer id);

    /**
     * 通过条件查询单个对象
     *
     * @param fastMiniSubscribeTemplatePO
     * @return
     */
    FastMiniSubscribeTemplatePO queryOne(FastMiniSubscribeTemplatePO fastMiniSubscribeTemplatePO);

    /**
     * 查询全部
     *
     * @param fastMiniSubscribeTemplatePO
     * @return
     */
    List<FastMiniSubscribeTemplatePO> queryList(FastMiniSubscribeTemplatePO fastMiniSubscribeTemplatePO);

    /**
     * 查询总数
     *
     * @param fastMiniSubscribeTemplatePO
     * @return
     */
    int queryCount(FastMiniSubscribeTemplatePO fastMiniSubscribeTemplatePO);

    /**
     * 可选新增
     *
     * @param fastMiniSubscribeTemplatePO
     * @return
     */
    int insertSelective(FastMiniSubscribeTemplatePO fastMiniSubscribeTemplatePO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int insertBatch(List<FastMiniSubscribeTemplatePO> list);

    /**
     * 更新
     *
     * @param fastMiniSubscribeTemplatePO
     * @return
     */
    int updateById(FastMiniSubscribeTemplatePO fastMiniSubscribeTemplatePO);

    /**
     * 通过miniId查询订阅模板
     *
     * @param miniId 小程序id
     */
    List<FastMiniSubscribeTemplatePO> queryListByMiniId(@Param("miniId") Integer miniId);

    /**
     * 通过miniId更新订阅模板
     *
     * @param params 更新参数
     */
    int updateByMiniId(FastMiniSubscribeTemplatePO params);
}
