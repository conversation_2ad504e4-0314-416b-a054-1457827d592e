<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.tablestore.FastTablestoreBackMapper">

	<!-- 用于select查询公用抽取的列 -->
	<sql id="FastTablestoreBack_columns">
		select t.`id`,t.`click_id`,t.`back_link`,t.`create_time`
	</sql>

    <!-- 通过id查询单个对象 -->
    <select id="queryById" resultType="FastTablestoreBackPO">
		<include refid="FastTablestoreBack_columns" />
	    from fast_tablestore_back t
        <where>
	        t.`id` = #{id}
	    </where>
	</select>

	<!--通过条件查询单个对象  -->
	<select id="queryOne" parameterType="FastTablestoreBackPO" resultType="FastTablestoreBackPO">
		<include refid="FastTablestoreBack_columns" />
	    from fast_tablestore_back t
        <where>
        	<include refid="whereSQL" />
        </where>
        limit 1
	</select>

	<!--分页查询集合  -->
	<select id="queryList" parameterType="FastTablestoreBackPO" resultType="FastTablestoreBackPO">
		<include refid="FastTablestoreBack_columns" />
	    from fast_tablestore_back t
        <where>
        	<include refid="whereSQL" />
        </where>
		order by t.`id` desc
	</select>

	<!--查询总数  -->
	<select id="queryCount" parameterType="FastTablestoreBackPO" resultType="int">
		select count(*)
	    from fast_tablestore_back t
        <where>
        	<include refid="whereSQL" />
        </where>
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
		<if test="id != null">
			and t.`id` = #{id}
		</if>
		<if test="clickId != null">
			and t.`click_id` = #{clickId}
		</if>
		<if test="backLink != null">
			and t.`back_link` = #{backLink}
		</if>
		<if test="createTime != null">
			and t.`create_time` = #{createTime}
		</if>
	</sql>

	<!-- 可选新增 -->
	<insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="FastTablestoreBackPO">
        insert into fast_tablestore_back
        <trim prefix="(" suffix=")" suffixOverrides=",">
	        <if test="clickId != null">click_id,</if>
	        <if test="backLink != null">back_link,</if>
	        <if test="createTime != null">create_time</if>
        </trim>
    	<trim prefix="values (" suffix=")" suffixOverrides=",">
	        <if test="clickId != null">#{clickId},</if>
	        <if test="backLink != null">#{backLink},</if>
	        <if test="createTime != null">#{createTime}</if>
        </trim>
	</insert>

	<!-- 批量新增 -->
	<insert id="insertBatch" useGeneratedKeys="true" keyProperty="id" parameterType="FastTablestoreBackPO">
        insert into fast_tablestore_back (
         click_id, back_link, create_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
         #{item.clickId}, #{item.backLink}, #{item.createTime}
        )
        </foreach>
	</insert>

    <!-- 更新 -->
	<update id="updateById" parameterType="FastTablestoreBackPO">
        update fast_tablestore_back
        <set>
         	<if test="clickId != null" >
               click_id = #{clickId},
            </if>
         	<if test="backLink != null" >
               back_link = #{backLink},
            </if>
         	<if test="createTime != null" >
               create_time = #{createTime},
            </if>
	    </set>
        where
        	id = #{id}  
	</update>

</mapper>
