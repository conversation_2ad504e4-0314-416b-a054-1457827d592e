/*
 * Powered By fast.up
 */
package com.fast.mapper.tablestore;

import com.fast.po.tablestore.FastTablestoreBackPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FastTablestoreBackMapper {

    // 通过id查询单个对象
    FastTablestoreBackPO queryById(FastTablestoreBackPO entity);

    // 通过id查询单个对象
    FastTablestoreBackPO queryById(@Param("id") Integer id);

    // 通过条件查询单个对象
    FastTablestoreBackPO queryOne(FastTablestoreBackPO entity);

    // 查询全部
    List<FastTablestoreBackPO> queryList(FastTablestoreBackPO entity);

    // 查询总数
    int queryCount(FastTablestoreBackPO entity);

    // 可选新增
    int insertSelective(FastTablestoreBackPO entity);

    // 批量新增
    int insertBatch(List<FastTablestoreBackPO> list);

    // 更新
    int updateById(FastTablestoreBackPO entity);

}
