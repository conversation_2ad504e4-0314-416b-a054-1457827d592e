<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fast.mapper.monitor.MonitorMapper">

	<!--分页查询集合  -->
	<select id="queryPubMemberNumGroupByMini" resultType="com.fast.vo.monitor.MonitorDataVO">
		select count(*) memberNum, a.mini_id miniId
		from (
			select m.mini_id from fast_member m
	        <where>
	        	<if test="miniIds != null and miniIds.length() > 0">
		            and m.`mini_id` in (${miniIds})
		        </if>
		        <if test="retailIds != null and retailIds.length() > 0">
		            and m.`retail_id` in (${retailIds})
		        </if>
				<if test="createTimeS != null">
					and m.`create_time` &gt;= #{createTimeS}
				</if>
				<if test="createTimeE != null">
					and m.`create_time` &lt;= #{createTimeE}
				</if>
				<if test="contentType != null">
					and m.`content_type` = #{contentType}
				</if>
	        </where>
		) a
		group by a.mini_id
	</select>

	<!--分页查询集合  -->
	<select id="queryMiniMemberNumGroupByMini" resultType="com.fast.vo.monitor.MonitorDataVO">
		select count(distinct a.openid) memberMiniNum, a.mini_id miniId
	    from
	    (
	    	select
	    		m.openid openid,
	    		m.mini_id mini_id
	    	from fast_member m
	        <where>
	        	<if test="miniIds != null and miniIds.length() > 0">
		            and m.`mini_id` in (${miniIds})
		        </if>
		        <if test="retailIds != null and retailIds.length() > 0">
		            and m.`retail_id` in (${retailIds})
		        </if>
				<if test="createTimeS != null">
					and m.`create_time` &gt;= #{createTimeS}
				</if>
				<if test="createTimeE != null">
					and m.`create_time` &lt;= #{createTimeE}
				</if>
				<if test="contentType != null">
					and m.`content_type` = #{contentType}
				</if>
	        </where>
	    ) a
		group by a.mini_id
	</select>

	<!--分页查询集合  -->
	<select id="queryMemberOrderNumGroupByMini" resultType="com.fast.vo.monitor.MonitorDataVO">
		select count(*) orderNum, t.mini_id miniId
	    from fast_member_order_recharge t
        <where>
        	<include refid="whereSQL" />
			and t.`state` = 1
        </where>
		group by t.mini_id
	</select>

	<!--分页查询集合  -->
	<select id="queryMemberNewOrderNumGroupByMini" resultType="com.fast.vo.monitor.MonitorDataVO">
		select count(distinct t.member_id) newOrderNum, t.mini_id miniId
	    from fast_member_order_recharge t
		left join fast_member_all m on m.id = t.member_id
        <where>
        	<if test="miniIds != null and miniIds.length() > 0">
	            and t.`mini_id` in (${miniIds})
	        </if>
	        <if test="retailIds != null and retailIds.length() > 0">
	            and t.`retail_id` in (${retailIds})
	        </if>
			<if test="contentType != null">
				and t.`content_type` = #{contentType}
			</if>

			<if test="createTimeS != null">
				and t.`create_time` &gt;= #{createTimeS}
			</if>
			<if test="createTimeE != null">
				and t.`create_time` &lt;= #{createTimeE}
			</if>
			and t.`state` = 1
			<if test="createTimeS != null">
				and m.`create_time` &gt;= #{createTimeS}
			</if>
			<if test="createTimeE != null">
				and m.`create_time` &lt;= #{createTimeE}
			</if>
        </where>
		group by t.mini_id
	</select>

	<!--分页查询集合  -->
	<select id="queryMemberOrderMoneyGroupByMini" resultType="com.fast.vo.monitor.MonitorDataVO">
		select sum(t.money_recharge) orderMoney, t.mini_id miniId
	    from fast_member_order_recharge t
        <where>
        	<include refid="whereSQL" />
			and t.`state` = 1
        </where>
		group by t.mini_id
	</select>

	<!--分页查询集合  -->
	<select id="queryMemberOrderMoneyProfitGroupByMini" resultType="com.fast.vo.monitor.MonitorDataVO">
		select sum(t.money_profit) orderMoney, t.mini_id miniId
	    from fast_member_order_recharge t
        <where>
        	<include refid="whereSQL" />
			and t.`state` = 1
        </where>
		group by t.mini_id
	</select>

	<!--分页查询集合  -->
	<select id="queryMiniUrlMakeCountGroupByMini" resultType="com.fast.vo.monitor.MonitorDataVO">
		select sum(t.url_make_count) urlMakeCount, t.mini_id miniId
	    from fast_mini_url_scheme t
		left join fast_mini mi on mi.id = t.mini_id
        <where>
			<if test="miniIds != null and miniIds.length() > 0">
				and t.`mini_id` in (${miniIds})
			</if>
			<if test="contentType != null">
				and mi.`content_type` = #{contentType}
			</if>

			<if test="countDateS != null">
				and t.`count_date` &gt;= #{countDateS}
			</if>
			<if test="countDateE != null">
				and t.`count_date` &lt;= #{countDateE}
			</if>
        </where>
		group by t.mini_id
	</select>

	<!-- 公共条件  -->
	<sql id="whereSQL">
        <if test="miniIds != null and miniIds.length() > 0">
            and t.`mini_id` in (${miniIds})
        </if>
        <if test="retailIds != null and retailIds.length() > 0">
            and t.`retail_id` in (${retailIds})
        </if>
		<if test="contentType != null">
			and t.`content_type` = #{contentType}
		</if>

		<if test="createTimeS != null">
			and t.`create_time` &gt;= #{createTimeS}
		</if>
		<if test="createTimeE != null">
			and t.`create_time` &lt;= #{createTimeE}
		</if>
	</sql>

</mapper>
