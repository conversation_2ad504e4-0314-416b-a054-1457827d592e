package com.fast.constant;

/**
 * 常见符号
 *
 * <AUTHOR>
 */
public class StaticSymbol {

    public static final String EMPTY = "";
    public static final String SPACE = " ";
    public static final String BANG = "!";
    public static final String QUESTION_MARK = "?";
    public static final String COMMA = ",";
    public static final String POINT = ".";
    public static final String COLON = ":";
    public static final String SEMICOLON = ";";
    public static final String QUOTE = "'";
    public static final String SINGLE_QUOTE = "'";
    public static final String DOUBLE_QUOTE = "\"";
    public static final String STAR = "*";
    public static final String PLUS = "+";
    public static final String DASH = "-";
    public static final String EQUAL = "=";
    public static final String SLASH = "/";
    public static final String BACK_SLASH = "\\";
    public static final String PIPE = "|";
    public static final String UNDERLINE = "_";
    public static final String DOLOR = "$";
    public static final String AT = "@";
    public static final String CROSS_HATCH = "#";
    public static final String CROSS_HATCH_2 = "##";
    public static final String PERCENT = "%";
    public static final String AND = "&";
    public static final String CIRCUMFLEX = "^";
    public static final String TILDE = "~";
    public static final String LEFT_BRACE = "{";
    public static final String RIGHT_BRACE = "}";
    public static final String LEFT_BRACKET = "[";
    public static final String RIGHT_BRACKET = "]";
    public static final String LEFT_ANGLE_BRACKET = "<";
    public static final String RIGHT_ANGLE_BRACKET = ">";
    public static final String LEFT_PARENTHESES = "(";
    public static final String RIGHT_PARENTHESES = ")";
    public static final String LINE_CHANGE_SYMBOL = "\n";
    public static final String ENTER_SYMBOL = "\r";

}
