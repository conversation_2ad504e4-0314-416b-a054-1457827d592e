package com.fast.constant;

/**
 * 优付错误码，成功码
 */
public class YouFuErrorConstant {
    // 成功返回
    public static String SUCCESS = "0000";
    public static String SUCCESS_MSG = "成功";
    // 产生未知异常
    public static String ERROR_6000 = "6000";
    public static String ERROR_MSG_6000 = "系统内部错误";

    public static String ERROR_6001 = "6001";
    public static String ERROR_MSG_6001 = "参数错误";
    //    【参数错误】客户端前置请求参数为空【reqJson为空】
//    【参数错误】请求数据为空
//    【参数错误】商户号为空
//    【参数错误】版本信息为空
//    【参数错误】签名为空
//    【参数错误】客户端前置请求获取实现类错误
//    【参数错误】接口编码为空
    public static String ERROR_6002 = "6002";
    public static String ERROR_MSG_6002 = "无效交易金额";

    public static String ERROR_6003 = "6003";
    public static String ERROR_MSG_6003 = "企业信息不存在";

    public static String ERROR_6004 = "6004";
    public static String ERROR_MSG_6004 = "企业信息不存在或状态未开通";

    public static String ERROR_6005 = "6005";
    public static String ERROR_MSG_6005 = "企业商户秘钥为空";

    public static String ERROR_6006 = "6006";
    public static String ERROR_MSG_6006 = "请求数据验签失败";

    public static String ERROR_6007 = "6007";
    public static String ERROR_MSG_6007 = "请求数据解密失败";

    public static String ERROR_6008 = "6008";
    public static String ERROR_MSG_6008 = "商户在黑名单不允许交易";

    public static String ERROR_6009 = "6009";
    public static String ERROR_MSG_6009 = "无商户风控信息";

    public static String ERROR_6010 = "6010";
    public static String ERROR_MSG_6010 = "无商户账户信息或账户状态无效";

    public static String ERROR_6011 = "6011";
    public static String ERROR_MSG_6011 = "商户请求地址未配置白名单";

    public static String ERROR_6012 = "6012";
    public static String ERROR_MSG_6012 = "商户批次号重复,该批次已付款";

    public static String ERROR_6013 = "6013";
    public static String ERROR_MSG_6013 = "付款金额超限";

    public static String ERROR_6014 = "6014";
    public static String ERROR_MSG_6014 = "信息入库失败";

    public static String ERROR_6015 = "6015";
    public static String ERROR_MSG_6015 = "计算商户手续费出错或商户手续费率不存在";

    public static String ERROR_6016 = "6016";
    public static String ERROR_MSG_6016 = "该用户信息已经做过签约";

    public static String ERROR_6017 = "6017";
    public static String ERROR_MSG_6017 = "商户付款方式未配置";

    public static String ERROR_6018 = "6018";
    public static String ERROR_MSG_6018 = "商户未开通该权限";

    public static String ERROR_6019 = "6019";
    public static String ERROR_MSG_6019 = "账户余额不足,该批次结算失败";

    public static String ERROR_6020 = "6020";
    public static String ERROR_MSG_6020 = "未查询到订单";

    public static String ERROR_6021 = "6021";
    public static String ERROR_MSG_6021 = "商户未签约此落地服务公司";

    public static String ERROR_6022 = "6022";
    public static String ERROR_MSG_6022 = "签约信息鉴权失败";

    public static String ERROR_6023 = "6023";
    public static String ERROR_MSG_6023 = "对账文件不存在";

    public static String ERROR_6024 = "6024";
    public static String ERROR_MSG_6024 = "姓名不能为空";

    public static String ERROR_6025 = "6025";
    public static String ERROR_MSG_6025 = "身份证号不能为空";

    public static String ERROR_6026 = "6026";
    public static String ERROR_MSG_6026 = "落地服务公司ID不能为空";

    public static String ERROR_6027 = "6027";
    public static String ERROR_MSG_6027 = "用户未在该落地服务公司签约";

    public static String ERROR_6028 = "6028";
    public static String ERROR_MSG_6028 = "不存在的落地服务公司";

    public static String ERROR_6029 = "6029";
    public static String ERROR_MSG_6029 = "该落地服务公司不可用";

    public static String ERROR_6030 = "6030";
    public static String ERROR_MSG_6030 = "商户ID不能为空";

    public static String ERROR_6031 = "6031";
    public static String ERROR_MSG_6031 = "商户批次号不能为空";

    public static String ERROR_6032 = "6032";
    public static String ERROR_MSG_6032 = "该商户批次号不存在";

    public static String ERROR_6033 = "6033";
    public static String ERROR_MSG_6033 = "商户订单号或者订单流水号不存在";

    public static String ERROR_6034 = "6034";
    public static String ERROR_MSG_6034 = "付款总笔数和明细不一致";

    public static String ERROR_6035 = "6035";
    public static String ERROR_MSG_6035 = "付款总金额和明细不一致";

    public static String ERROR_6036 = "6036";
    public static String ERROR_MSG_6036 = "批量付款只能选择一个税源地地";

    public static String ERROR_6037 = "6037";
    public static String ERROR_MSG_6037 = "该用户签约中";


    public static String ERROR_6038 = "6038";
    public static String ERROR_MSG_6038 = "该商户不支持API接口签约";

    public static String ERROR_6039 = "6039";
    public static String ERROR_MSG_6039 = "落地服务公司需要上传身份证正反面图片";

    public static String ERROR_6040 = "6040";
    public static String ERROR_MSG_6040 = "落地服务公司需要上传项目编码";

    public static String ERROR_6041 = "6041";
    public static String ERROR_MSG_6041 = "不存在该项目";

    public static String ERROR_6042 = "6042";
    public static String ERROR_MSG_6042 = "请求频繁请稍后再试";

    public static String ERROR_6043 = "6043";
    public static String ERROR_MSG_6043 = "三要素认证失败";

    public static String ERROR_6044 = "6044";
    public static String ERROR_MSG_6044 = "该商户未签约此落地服务公司";

    public static String ERROR_6045 = "6045";
    public static String ERROR_MSG_6045 = "未查询到该商户可开票类目信息";

    public static String ERROR_6046 = "6046";
    public static String ERROR_MSG_6046 = "未查询到该商户在该税源地开票信息";


}
