package com.fast.constant;

import com.fast.utils.StrUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 常量
 *
 * <AUTHOR>
 */
@Component
public class StaticYml extends StaticSymbol {

    /**
     * 动态注入
     */
    public static String FAST_PW;// 前后端通信，加密密码
    public static String FAST_UP_MINI_URL;// 小程序接口域名

    /**
     * 小程序三方平台
     */
    public static String WECHAT_OPEN_APP_ID;
    public static String WECHAT_OPEN_APP_SECRET;
    public static String WECHAT_OPEN_ENCODINGAESKEY;
    public static String WECHAT_OPEN_TOKEN;
    public static String WECHAT_OPEN_REDIRECT_MINI_URL;// 小程序开放授权回调url
    public static String WECHAT_OPEN_REDIRECT_ACCOUNT_URL;// 公众号开放授权回调url

    /**
     * 抖音开放平台
     */
    public static String TIKTOK_OPEN_APP_ID;
    public static String TIKTOK_OPEN_APP_SECRET;
    public static String TIKTOK_OPEN_ENCODINGAESKEY;
    public static String TIKTOK_OPEN_TOKEN;
    public static String TIKTOK_OPEN_REDIRECT_MINI_URL;// 小程序开放授权回调url
    public static String TIKTOK_PAY_TOKEN;
    public static String TIKTOK_PAY_SALT;
    public static String TIKTOK_REFUND_CALLBACK;// 退款回调

    /**
     * 快手三方平台
     */
    public static String KUAISHOU_SAAS_APP_ID;
    public static String KUAISHOU_SAAS_APP_SECRET;
    public static String KUAISHOU_SAAS_PUSH_TOKEN;// 推送消息解密token
    public static String KUAISHOU_SAAS_PUSH_SECRET;// 推送消息解密secret
    public static String KUAISHOU_SAAS_REDIRECT_MINI_URL;// 小程序开放授权回调url


    /**
     * 支付宝三方平台
     */
    public static String ALI_SAAS_APP_ID;
    public static String ALI_SAAS_APP_SECRET;
    public static String ALI_SAAS_PUSH_TOKEN;// 推送消息解密token
    public static String ALI_SAAS_PUSH_SECRET;// 推送消息解密secret
    public static String ALI_SAAS_REDIRECT_MINI_URL;// 小程序开放授权回调url
    public static String ALI_SAAS_PRIVATE_KEY;// 支付宝小程序私钥
    public static String ALI_SAAS_PUBLIC_KEY;// 支付宝小程序支付宝应用公钥
    public static String ALI_SAAS_TEMPLATE_APP_ID; // 支付宝模版小程序appId

    public static String ALI_TRADE_CREATE = "alipay.trade.create";

    public static String YXH_SECRET_TT; // 悦享会，抖音秘钥
    public static String YXH_SECRET_KS; // 悦享会，快手秘钥

    // 小程序推广链接
    public static String PROMOTE_MINI_SHORT_LINK_DOMAIN;//短剧推广-推广链接-小程序短连接域名
    public static String PROMOTE_MINI_DOMAIN_OUT_URL;//短剧推广-推广链接-对外推广url
    public static String PROMOTE_MINI_DOMAIN_OUT_BAIDU_URL;//短剧推广-推广链接-对外推广url
    public static String PROMOTE_MINI_DOMAIN_MONITOR_URL;//短剧推广-推广链接-对外监测url
    public static String PROMOTE_MINI_DOMAIN_MONITOR_ADQ_3V_URL;//短剧推广-推广链接-adq3.0-对外监测url
    public static String PROMOTE_MINI_DOMAIN_MONITOR_ADQ_QUICK_3V_URL;//短剧推广-推广链接-adq3.0-对外监测url
    public static String PROMOTE_MINI_DOMAIN_MONITOR_WEIBO_URL;//新浪微博-短剧推广-推广链接-对外监测url
    public static String PROMOTE_MINI_DOMAIN_MONITOR_BILIBILI_URL;//新浪微博-短剧推广-推广链接-对外监测url
    public static String PROMOTE_MINI_DOMAIN_MONITOR_BAIDU_URL;//百度-短剧推广-推广链接-对外监测url
    public static String PROMOTE_MINI_DOMAIN_MONITOR_BAIDU_QUICK_URL;//百度-短剧推广-推广链接-对外监测url-快应用
    public static String PROMOTE_MINI_DOMAIN_MONITOR_KUAISHOU_URL;// 快手-短剧推广-推广链接-对外监测url
    public static String PROMOTE_MINI_DOMAIN_MONITOR_URL_V2;//短剧推广-推广链接-对外监测url,2.0版本
    public static String PROMOTE_MINI_PAGE_URL;//短剧推广-推广链接-小程序页面推广链接
    public static String PROMOTE_MINI_PAGE_OPERATION_URL;//短剧推广-推广链接-小程序页面推广链接
    // 快应用
    public static String PROMOTE_QUICK_PAGE_URL;//短剧推广-推广链接-快应用页面推广链接
    // 抖音推广链接
    public static String PROMOTE_TIKTOK_MINI_PAGE_URL;//抖音小程序-短剧推广-推广链接-小程序页面推广链接
    public static String PROMOTE_TIKTOK_MINI_PAGE_PATH;//抖音小程序-短剧推广-推广链接-小程序页面
    public static String TIKTOK_AUDIT_APP_ID;// 抖音审核小程序id
    public static String TIKTOK_AUDIT_APP_SECRET;// 抖音审核小程序密文
    // H5推广链接
    public static String PROMOTE_H5_PAGE_URL;//H5站-短剧推广-推广链接-小程序页面推广链接（直达页面的地址）
    public static String PROMOTE_H5_GROUND_DRAMA_NET;// H5站，自研落地页地址
    public static String PROMOTE_H5_GROUND_CARTOON_NET;// H5站，自研落地页地址
    public static String PROMOTE_H5_GROUND_NOVEL_NET;// H5站，自研落地页地址
    public static String PROMOTE_H5_GROUND_URL;// H5站，自研落地页兜底域名
    // 商户平台的appkey
    public static String WECHAT_API_KEY;
    // 短剧服务商-商户号
    public static String WECHAT_MCH_ID;
    // 商户平台-子商户号
    public static String WECHAT_SUB_APPID;

    // 百度媒体回传token
    public static String BACK_BAIDU_TOKEN;

    /**
     * 短剧公众号-(短剧机构端-pay)
     */
    public static String WECHAT_PAY_APP_ID;
    public static String WECHAT_PAY_APP_SECRET;
    public static String WECHAT_PAY_APP_TOKEN;

    /**
     * h5站
     */
    public static String H5WAP_HTTP;
    public static String START_MONITOR;
    public static String H5_TEMP_CODE_TIME;

    /**
     * 汇付
     */
    public static String AGENT_ID;
    public static String BAGENT_ID;
    public static String OP_TELLER_ID;
    // 微信支付接口安全证书地址
    public static String WECHAT_PAY_CRET_P12;

    /**
     * 阿里云短信
     */
    public static String SMS_ACCESS_KEY_ID;// 短信
    public static String SMS_ACCESS_KEY_SECRET;

    public static String AUDIT_APP_ID; // 腾讯送审appId
    public static String AUDIT_TIKTOK_APP_ID; // 抖音送审appId（第一版）

    public static String TIKTOKPRIVATEKEY;
    public static String TIKTOKPRIVATEKEYVESION;

    // 腾讯QQ内容审核
    public static String QQ_CONTENT_CLIENT_ID;// 应用id
    public static String QQ_CONTENT_CLIENT_SECRET;// 应用秘钥
    // 快手结算回调地址
    public static String KUAISHOU_SETTLE_CALLBACK;// 快手结算回调地址
    public static String KUAISHOU_REFUND_CALLBACK;// 快手退款回调地址

    public static String EXPORT_INTERNAL_URL;
    
    public static String TT_COUPON_CALLBACK;// 抖音发券回调地址 ;

    public static String KUAISHOU_DRAMA_AUDIT_CALLBACK;// 抖音发券回调地址 ;
    
    public static Integer DEPLOY_PLATFORM;// 系统部署平台:1=阿里云;2=连山云;3=咪咕

    public static Set<String> OSS_REPLACE_URLS;
    public static Set<String> MIDDLE_GROUND_WHITE_LIST;// 中台秘钥

    public static String QUICK_LOGIN_SMS_TEMPLATE;// 快手登录模板
    
    // 短信账号
    public static String DHST_ACCOUNT; // 大汉三通账号
    public static String DHST_PASSWORD; // 大汉三通密码
    public static Integer COERCE_UPDATE_PASSWORD; // 强制改密码

    public static String ALI_CAPTCHA_PREFIX; // 身份标识符
    public static String ALI_CAPTCHA_SCENE_ID; // 场景值
    // public static Integer
    public static String ADMIN_SMS_DEFAULT_CODE; // 后台短信验证码默认值
    
    public static Integer TIKTOK_AUTO_CLOSE_DISTRIBUTE;// 关闭抖音分发开关
    
    public static String DEVICE_NUM;// 设备编号

    public static String OCEAN_ENGINE_OPEN_APP_ID; // 星图开放平台appId
    public static String OCEAN_ENGINE_OPEN_APP_SECRET; // 星图开放平台appSecret
    
    public static Integer LIULINGYI_PLATFORM;// 是否六零一平台
    
    /**
     * 投放平台内部调用地址
     */
    public static String LAUNCH_INTERNAL_URL;

    /**
     * 请求地址
     */

    public static String BOSSKG_YOU_FU_URL;


    /**
     * facebook pix
     */
    public static String FB_PIXEL_ID;
    public static String FB_TOKEN;


    @Value("${fast.liulingyi_platform:0}")
    public void setLiulingyiPlatform(Integer liulingyiPlatform) {
        LIULINGYI_PLATFORM = liulingyiPlatform;
    }
    
    @Value("${device_num:0}")
    public void setDeviceNum(String deviceNum) {
    	DEVICE_NUM = deviceNum;
    }
    
    @Value("${fast.tiktok_auto_close_distribute:0}")
    public void setTiktokAutoCloseDistribute(Integer tiktokAutoCloseDistribute) {
    	TIKTOK_AUTO_CLOSE_DISTRIBUTE = tiktokAutoCloseDistribute;
    }
    
    @Value("${fast.dhst_account}")
    public void setDhstAccount(String dhstAccount) {
    	DHST_ACCOUNT = dhstAccount;
    }
    @Value("${fast.dhst_password}")
    public void setDhstPassword(String dhstPassword) {
    	DHST_PASSWORD = dhstPassword;
    }
    @Value("${fast.deploy_platform}")
    public void setDeployPlatform(Integer deployPlatform) {
    	DEPLOY_PLATFORM = deployPlatform;
    }
    
    @Value("${fast.quick_login_sms_template}")
    public void setQuickLoginSmsTemplate(String quickLoginSmsTemplate) {
    	QUICK_LOGIN_SMS_TEMPLATE = quickLoginSmsTemplate;
    }
    
    @Value("${fast.tt_coupon_callback}")
    public void setTtCouponCallback(String ttCouponCallback) {
    	TT_COUPON_CALLBACK = ttCouponCallback;
    }

    @Value("${fast.kuaishou_drama_audit_notify_url}")
    public void setKuaishouDramaAuditCallback(String kuaishouAuditUrl) {
        KUAISHOU_DRAMA_AUDIT_CALLBACK = kuaishouAuditUrl;
    }
    
    @Value("${fast.kuaishou_refund_callback}")
    public void setKuaishouRefundCallback(String kuaishouRefundCallback) {
    	KUAISHOU_REFUND_CALLBACK = kuaishouRefundCallback;
    }
    
    @Value("${fast.kuaishou_settle_callback}")
    public void setKuaishouSettleCallback(String kuaishouSettleCallback) {
    	KUAISHOU_SETTLE_CALLBACK = kuaishouSettleCallback;
    }
    
    @Value("${fast.launch_internal_url}")
    public void setLaunchInternalUrl(String launchProjectUrl) {
    	LAUNCH_INTERNAL_URL = launchProjectUrl;
    }

    @Value("${fast.export_internal_url}")
    public void setExportInternalUrl(String exportInternalUrl) {
        EXPORT_INTERNAL_URL = exportInternalUrl;
    }

    @Value("${fast.qq_content_client_id}")
    public void setQQContentClientId(String qqContentClientId) {
    	QQ_CONTENT_CLIENT_ID = qqContentClientId;
    }
    @Value("${fast.qq_content_client_secret}")
    public void setQQContentClientSecret(String qqContentClientSecret) {
    	QQ_CONTENT_CLIENT_SECRET = qqContentClientSecret;
    }
    @Value("${fast.tiktok_audit_app_id}")
    public void setTiktokAuditAppId(String tiktokAuditAppId) {
    	TIKTOK_AUDIT_APP_ID = tiktokAuditAppId;
    }
    @Value("${fast.tiktok_audit_app_secret}")
    public void setTiktokAuditAppSecret(String tiktokAuditAppSecret) {
    	TIKTOK_AUDIT_APP_SECRET = tiktokAuditAppSecret;
    }
    @Value("${fast.tiktok_refund_callback}")
    public void setTiktokRefundCallback(String tiktokRefundCallback) {
    	TIKTOK_REFUND_CALLBACK = tiktokRefundCallback;
    }
    @Value("${fast.yxh_secret_tt}")
    public void setYxhSecretTt(String yxhSecretTt) {
    	YXH_SECRET_TT = yxhSecretTt;
    }
    @Value("${fast.yxh_secret_ks}")
    public void setYxhSecretKs(String yxhSecretKs) {
    	YXH_SECRET_KS = yxhSecretKs;
    }
    @Value("${fast.kuaishou_component_app_id}")
    public void setKuaishouComponentAppId(String kuaishouComponentAppId) {
    	KUAISHOU_SAAS_APP_ID = kuaishouComponentAppId;
    }
    @Value("${fast.kuaishou_component_app_secret}")
    public void setKuaishouComponentAppSecret(String kuaishouComponentAppSecret) {
    	KUAISHOU_SAAS_APP_SECRET = kuaishouComponentAppSecret;
    }
    @Value("${fast.kuaishou_component_push_token}")
    public void setKuaishouComponentPushToken(String kuaishouComponentPushToken) {
    	KUAISHOU_SAAS_PUSH_TOKEN = kuaishouComponentPushToken;
    }
    @Value("${fast.kuaishou_component_push_secret}")
    public void setKuaishouComponentPushSecret(String kuaishouComponentPushSecret) {
    	KUAISHOU_SAAS_PUSH_SECRET = kuaishouComponentPushSecret;
    }

    @Value("${fast.audit_tiktok_app_id}")
    public void setAuditTiktokAppId(String auditTiktokAppId) {
    	AUDIT_TIKTOK_APP_ID = auditTiktokAppId;
    }
    @Value("${fast.audit_app_id}")
    public void setAuditAppId(String auditAppId) {
    	AUDIT_APP_ID = auditAppId;
    }
    @Value("${fast.back_baidu_token}")
    public void setBackBaiduToken(String backBaiduToken) {
    	BACK_BAIDU_TOKEN = backBaiduToken;
    }
    @Value("${fast.h5_temp_code_time}")
    public void setH5TempCodeTime(String h5TempCodeTime) {
    	H5_TEMP_CODE_TIME = h5TempCodeTime;
    }
    @Value("${fast.promote_h5_page_url}")
    public void setPromoteH5PageUrl(String promoteH5PageUrl) {
    	PROMOTE_H5_PAGE_URL = promoteH5PageUrl;
    }

    @Value("${fast.promote_h5_ground_drama_net}")
    public void setPromoteH5GroundDramaNet(String promoteH5GroundDramaNet) {
    	PROMOTE_H5_GROUND_DRAMA_NET = promoteH5GroundDramaNet;
    }
    @Value("${fast.promote_h5_ground_cartoon_net}")
    public void setPromoteH5GroundCartoonNet(String promoteH5GroundCartoonNet) {
    	PROMOTE_H5_GROUND_CARTOON_NET = promoteH5GroundCartoonNet;
    }
    @Value("${fast.promote_h5_ground_novel_net}")
    public void setPromoteH5GroundNovelNet(String promoteH5GroundNovelNet) {
    	PROMOTE_H5_GROUND_NOVEL_NET = promoteH5GroundNovelNet;
    }
    @Value("${fast.promote_h5_ground_url}")
    public void setPromoteH5GroundUrl(String promoteH5GroundUrl) {
    	PROMOTE_H5_GROUND_URL = promoteH5GroundUrl;
    }

    @Value("${fast.h5wap_http}")
    public void setH5wapHttp(String h5wapHttp) {
        H5WAP_HTTP = h5wapHttp;
    }
    @Value("${fast.start_monitor}")
    public void setStartMonitor(String startMonitor) {
    	START_MONITOR = startMonitor;
    }
    @Value("${fast.sms_access_key_id}")
    public void setSmsAccessKeyId(String smsAccessKeyId) {
    	SMS_ACCESS_KEY_ID = smsAccessKeyId;
    }

    @Value("${fast.sms_access_key_secret}")
    public void setSmsAccessKeySecret(String smsAccessKeySecret) {
        SMS_ACCESS_KEY_SECRET = smsAccessKeySecret;
    }

    @Value("${fast.wechat_pay_app_id}")
    public void setWechatPayAppId(String wechatPayAppId) {
        WECHAT_PAY_APP_ID = wechatPayAppId;
    }

    @Value("${fast.wechat_pay_app_secret}")
    public void setWechatPayAppSecret(String wechatPayAppSecret) {
        WECHAT_PAY_APP_SECRET = wechatPayAppSecret;
    }

    @Value("${fast.wechat_pay_app_token}")
    public void setWechatPayAppToken(String wechatPayAppToken) {
        WECHAT_PAY_APP_TOKEN = wechatPayAppToken;
    }

    @Value("${fast.wechat_api_key}")
    public void setWechatApiKey(String wechatApiKey) {
        WECHAT_API_KEY = wechatApiKey;
    }

    @Value("${fast.wechat_mch_id}")
    public void setWechatMchId(String wechatMchId) {
        WECHAT_MCH_ID = wechatMchId;
    }

    @Value("${fast.wechat_sub_appid}")
    public void setWechatSubAppid(String wechatSubAppid) {
        WECHAT_SUB_APPID = wechatSubAppid;
    }

    @Value("${fast.pw}")
    public void setFastPw(String fastPw) {
        FAST_PW = fastPw;
    }

    @Value("${fast.wechat_open_app_id}")
    public void setWechatOpenAppId(String wechatOpenAppId) {
        WECHAT_OPEN_APP_ID = wechatOpenAppId;
    }

    @Value("${fast.wechat_open_app_secret}")
    public void setWechatOpenAppSecret(String wechatOpenAppSecret) {
        WECHAT_OPEN_APP_SECRET = wechatOpenAppSecret;
    }

    @Value("${fast.wechat_open_encodingaeskey}")
    public void setWechatOpenEncodingaeskey(String wechatOpenEncodingaeskey) {
        WECHAT_OPEN_ENCODINGAESKEY = wechatOpenEncodingaeskey;
    }

    @Value("${fast.wechat_open_token}")
    public void setWechatOpenToken(String wechatOpenToken) {
        WECHAT_OPEN_TOKEN = wechatOpenToken;
    }

    @Value("${fast.wechat_open_redirect_account_url}")
    public void setWechatOpenRedirectAccountUrl(String wechatOpenRedirectAccountUrl) {
        WECHAT_OPEN_REDIRECT_ACCOUNT_URL = wechatOpenRedirectAccountUrl;
    }

    @Value("${fast.wechat_open_redirect_mini_url}")
    public void setWechatOpenRedirectMiniUrl(String wechatOpenRedirectMiniUrl) {
        WECHAT_OPEN_REDIRECT_MINI_URL = wechatOpenRedirectMiniUrl;
    }

    @Value("${fast.promote_mini_short_link_domain}")
    public void setPromoteMiniShortLinkDomain(String promoteMiniShortLinkDomain) {
        PROMOTE_MINI_SHORT_LINK_DOMAIN = promoteMiniShortLinkDomain;
    }

    @Value("${fast.promote_mini_domain_out_url}")
    public void setPromoteMiniDomainOutUrl(String promoteMiniDomainOutUrl) {
        PROMOTE_MINI_DOMAIN_OUT_URL = promoteMiniDomainOutUrl;
    }

    @Value("${fast.promote_mini_domain_out_baidu_url}")
    public void setPromoteMiniDomainOutBaiduUrl(String promoteMiniDomainOutBaiduUrl) {
    	PROMOTE_MINI_DOMAIN_OUT_BAIDU_URL = promoteMiniDomainOutBaiduUrl;
    }

    @Value("${fast.promote_mini_domain_monitor_url}")
    public void setPromoteMiniDomainMonitorUrl(String promoteMiniDomainMonitorUrl) {
        PROMOTE_MINI_DOMAIN_MONITOR_URL = promoteMiniDomainMonitorUrl;
    }
    
    @Value("${fast.promote_mini_domain_monitor_adq_3v_url:}")
    public void setPromoteMiniDomainMonitorAdq3VUrl(String promoteMiniDomainMonitorAdq3VUrl) {
    	PROMOTE_MINI_DOMAIN_MONITOR_ADQ_3V_URL = promoteMiniDomainMonitorAdq3VUrl;
    }
    
    @Value("${fast.promote_mini_domain_monitor_adq_quick_3v_url:}")
    public void setPromoteMiniDomainMonitorAdqQuick3VUrl(String promoteMiniDomainMonitorAdqQuick3VUrl) {
    	PROMOTE_MINI_DOMAIN_MONITOR_ADQ_QUICK_3V_URL = promoteMiniDomainMonitorAdqQuick3VUrl;
    }
    
    @Value("${fast.promote_mini_domain_monitor_weibo_url}")
    public void setPromoteMiniDomainMonitorWeiboUrl(String promoteMiniDomainMonitorWeiboUrl) {
    	PROMOTE_MINI_DOMAIN_MONITOR_WEIBO_URL = promoteMiniDomainMonitorWeiboUrl;
    }
    
    @Value("${fast.promote_mini_domain_monitor_bilibili_url:}")
    public void setPromoteMiniDomainMonitorBilibiliUrl(String promoteMiniDomainMonitorBilibiliUrl) {
    	PROMOTE_MINI_DOMAIN_MONITOR_BILIBILI_URL = promoteMiniDomainMonitorBilibiliUrl;
    }

    @Value("${fast.promote_mini_domain_monitor_baidu_url:}")
    public void setPromoteMiniDomainMonitorBaiduUrl(String promoteMiniDomainMonitorBaiduUrl) {
    	PROMOTE_MINI_DOMAIN_MONITOR_BAIDU_URL = promoteMiniDomainMonitorBaiduUrl;
    }
    
    @Value("${fast.promote_mini_domain_monitor_baidu_quick_url:}")
    public void setPromoteMiniDomainMonitorBaiduQuickUrl(String promoteMiniDomainMonitorBaiduQuickUrl) {
    	PROMOTE_MINI_DOMAIN_MONITOR_BAIDU_QUICK_URL = promoteMiniDomainMonitorBaiduQuickUrl;
    }

    @Value("${fast.promote_mini_domain_monitor_kuaishou_url}")
    public void setPromoteMiniDomainMonitorKuaishouUrl(String promoteMiniDomainMonitorKuaishouUrl) {
    	PROMOTE_MINI_DOMAIN_MONITOR_KUAISHOU_URL = promoteMiniDomainMonitorKuaishouUrl;
    }

    @Value("${fast.promote_mini_domain_monitor_url_v2}")
    public void setPromoteMiniDomainMonitorUrlV2(String promoteMiniDomainMonitorUrlV2) {
        PROMOTE_MINI_DOMAIN_MONITOR_URL_V2 = promoteMiniDomainMonitorUrlV2;
    }

    @Value("${fast.promote_mini_page_url}")
    public void setPromoteMiniPageUrl(String promoteMiniPageUrl) {
        PROMOTE_MINI_PAGE_URL = promoteMiniPageUrl;
    }
    
    @Value("${fast.promote_quick_page_url:}")
    public void setPromoteQuickPageUrl(String promoteQuickPageUrl) {
    	PROMOTE_QUICK_PAGE_URL = promoteQuickPageUrl;
    }

    @Value("${fast.promote_mini_page_operation_url}")
    public void setPromoteMiniPageOperationUrl(String promoteMiniPageOperationUrl) {
    	PROMOTE_MINI_PAGE_OPERATION_URL = promoteMiniPageOperationUrl;
    }

    @Value("${fast.promote_tiktok_mini_page_url}")
    public void setPromoteTiktokMiniPageUrl(String promoteTiktokMiniPageUrl) {
    	PROMOTE_TIKTOK_MINI_PAGE_URL = promoteTiktokMiniPageUrl;
    }
    @Value("${fast.promote_tiktok_mini_page_path}")
    public void setPromoteTiktokMiniPagePath(String promoteTiktokMiniPagePath) {
    	PROMOTE_TIKTOK_MINI_PAGE_PATH = promoteTiktokMiniPagePath;
    }

    @Value("${fast.fast_up_mini_url}")
    public void setFastUpMiniUrl(String fastUpMiniUrl) {
        FAST_UP_MINI_URL = fastUpMiniUrl;
    }

    @Value("${fast.agent_id}")
    public void setAgentId(String agentId) {
        AGENT_ID = agentId;
    }

    @Value("${fast.bagent_id}")
    public void setBagentId(String bagentId) {
        BAGENT_ID = bagentId;
    }

    @Value("${fast.op_teller_id}")
    public void setOpTellerId(String opTellerId) {
        OP_TELLER_ID = opTellerId;
    }

    @Value("${fast.wechatPayCretP12}")
    public void setWechatPayCretP12(String wechatPayCretP12) {
        WECHAT_PAY_CRET_P12 = wechatPayCretP12;
    }

    @Value("${fast.tiktok_open_app_id}")
    public void setTiktokOpenAppId(String tiktokOpenAppId) {
        TIKTOK_OPEN_APP_ID = tiktokOpenAppId;
    }

    @Value("${fast.tiktok_open_app_secret}")
    public void setTiktokOpenAppSecret(String tiktokOpenAppSecret) {
        TIKTOK_OPEN_APP_SECRET = tiktokOpenAppSecret;
    }

    @Value("${fast.tiktok_open_encodingaeskey}")
    public void setTiktokOpenEncodingaeskey(String tiktokOpenEncodingaeskey) {
        TIKTOK_OPEN_ENCODINGAESKEY = tiktokOpenEncodingaeskey;
    }

    @Value("${fast.tiktok_open_token}")
    public void setTiktokOpenToken(String tiktokOpenToken) {
        TIKTOK_OPEN_TOKEN = tiktokOpenToken;
    }

    @Value("${fast.tiktok_open_redirect_mini_url}")
    public void setTiktokOpenRedirectMiniUrl(String tiktokOpenRedirectMiniUrl) {
        TIKTOK_OPEN_REDIRECT_MINI_URL = tiktokOpenRedirectMiniUrl;
    }

    @Value("${fast.kuaishou_saas_redirect_mini_url}")
    public void setKuaishouSaasRedirectMiniUrl(String kuaishouSaasRedirectMiniUrl) {
    	KUAISHOU_SAAS_REDIRECT_MINI_URL = kuaishouSaasRedirectMiniUrl;
    }

    @Value("${fast.tiktok_pay_salt}")
    public void setTiktokPaySalt(String tiktokPaySalt) {
        TIKTOK_PAY_SALT = tiktokPaySalt;
    }

    @Value("${fast.tiktok_pay_token}")
    public void setTiktokPayToken(String tiktokPayToken) {
        TIKTOK_PAY_TOKEN = tiktokPayToken;
    }


    @Value("${fast.tiktok_privateKey}")
    public void setTiktokPrivateKey(String tiktokPrivateKey) {
        TIKTOKPRIVATEKEY = tiktokPrivateKey;
    }

    @Value("${fast.tiktok_privatekeyVesion}")
    public void setTiktokPrivatekeyVersion(String tiktokPrivatekeyVesion) {
        TIKTOKPRIVATEKEYVESION = tiktokPrivatekeyVesion;
    }

    @Value("#{'${oss.replaceUrls:}'.split(',')}")
    public void setTiktokPrivatekeyVersion(Set<String> ossUrls) {
        OSS_REPLACE_URLS = ossUrls.stream().filter(StrUtil::isNotBlank).collect(Collectors.toSet());
    }

    @Value("#{'${fast.middle_ground_while_list:}'.split(',')}")
    public void setMiddleGroundEncryptKey(Set<String> middleGroundEncryptKey) {
        MIDDLE_GROUND_WHITE_LIST = middleGroundEncryptKey;
    }

    @Value("${fast.coerce_update_password:0}")
    public void setTiktokPrivatekeyVersion(Integer coerceUpdatePassword) {
        COERCE_UPDATE_PASSWORD = coerceUpdatePassword;
    }

    @Value("${fast.ali_captcha_prefix:}")
    public void setAliCaptchaPrefix(String aliCaptchaPrefix){
        ALI_CAPTCHA_PREFIX = aliCaptchaPrefix;
    }

    @Value("${fast.ali_captcha_scene_id:}")
    public void setAliCaptchaSceneId(String aliCaptchaSceneId){
        ALI_CAPTCHA_SCENE_ID = aliCaptchaSceneId;
    }

    @Value("${fast.admin_sms_default_code:}")
    public void setAdminSmsDefaultCode(String adminSmsDefaultCode){
        ADMIN_SMS_DEFAULT_CODE = adminSmsDefaultCode;
    }


    @Value("${fast.ali_saas_app_id:}")
    public void setAliSaasAppId(String aliSaasAppId) {
        ALI_SAAS_APP_ID = aliSaasAppId;
    }

    @Value("${fast.ali_saas_redirect_mini_url:}")
    public void setAliSaasRedirectMiniUrl(String aliSaasRedirectMiniUrl) {
        ALI_SAAS_REDIRECT_MINI_URL = aliSaasRedirectMiniUrl;
    }

    @Value("${fast.ali_saas_private_key:}")
    public void setAliSaasPrivateKey(String aliSaasRedirectMiniUrl) {
        ALI_SAAS_PRIVATE_KEY = aliSaasRedirectMiniUrl;
    }

    @Value("${fast.ali_saas_public_key:}")
    public void setAliSaasPublicKey(String aliSaasRedirectMiniUrl) {
        ALI_SAAS_PUBLIC_KEY = aliSaasRedirectMiniUrl;
    }

    @Value("${fast.ali_saas_template_app_id:}")
    public void setAliSaasTemplateAppId(String aliSaasTemplateAppId) {
        ALI_SAAS_TEMPLATE_APP_ID = aliSaasTemplateAppId;
    }

    @Value("${fast.ocean_engine_open_app_id:}")
    public void setOceanEngineOpenAppId(String oceanEngineOpenAppId) {
    	OCEAN_ENGINE_OPEN_APP_ID = oceanEngineOpenAppId;
    }

    @Value("${fast.ocean_engine_open_app_secret:}")
    public void setOceanEngineOpenAppSecret(String oceanEngineOpenAppSecret) {
    	OCEAN_ENGINE_OPEN_APP_SECRET = oceanEngineOpenAppSecret;
    }

    @Value("${fast.BOSSKG_YOU_FU_URL:https://api.serviceshare.com/clientapi/clientBusiness/common}")
    public void setYouFuUrl(String youFuUrl) {
        BOSSKG_YOU_FU_URL = youFuUrl;
    }


    /**
     * facebook pixel像素配置
     * @param fbPixelId
     */
    @Value("${fast.fb_pixel_id:0}")
    public void setFbPixelId(String fbPixelId) {
        FB_PIXEL_ID = fbPixelId;
    }

    @Value("${fast.fb_token:token}")
    public void setFbToken(String fbToken) {
        FB_TOKEN = fbToken;
    }
}
