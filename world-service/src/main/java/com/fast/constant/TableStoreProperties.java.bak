package com.fast.constant;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Created on 2020/10/09
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ConfigurationProperties(prefix = "tablestore")
public class TableStoreProperties {
    
    private String ak; // 阿里云访问AK
    private String sk; // 阿里云访问SK
    private String endpoint; // 阿里云表格存储服务域名地址
    private String instance; // 阿里云表格存储实例名
    private int connectionTimeout; // 建立连接的超时时间,单位为毫秒。
    private int socketTimeout; // socket超时时间,单位为毫秒。
    
//    private String fasBackToutiaoTable; // 监测回传头条表名-阿里云tablestore
//    private String fasBackToutiaoTableIndex; // 监测回传头条表-多元索引名-阿里云tablestore
//    private String fasBackToutiaoTablePk; // 监测回传头条表-主键名-阿里云tablestore
    
//    private String fasBackToutiaoLinkTable; // 投放链接回传头条表名-阿里云tablestore
//    private String fasBackToutiaoLinkTableIndex; // 投放链接回传头条表-多元索引名-阿里云tablestore
//    private String fasBackToutiaoLinkTablePk; // 投放链接回传头条表-主键名-阿里云tablestore
    
//    private String fasMemberLinkHisTable; // 投放链接历史表名-阿里云tablestore
//    private String fasMemberLinkHisTableIndex; // 投放链接历史表-多元索引名-阿里云tablestore
//    private String fasMemberLinkHisTablePk; // 投放链接历史表-主键名-阿里云tablestore
    // 百度
//    private String fasBackBaiduTable; // 监测回传头条表名-阿里云tablestore
//    private String fasBackBaiduTableIndex; // 监测回传头条表-多元索引名-阿里云tablestore
//    private String fasBackBaiduTablePk; // 监测回传头条表-主键名-阿里云tablestore
    // adqv3
//    private String fasBackAdqV3Table; // 监测回传头条表名-阿里云tablestore
//    private String fasBackAdqV3TableIndex; // 监测回传头条表-多元索引名-阿里云tablestore
//    private String fasBackAdqV3TablePk; // 监测回传头条表-主键名-阿里云tablestore
    

}
