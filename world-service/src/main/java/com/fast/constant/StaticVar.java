package com.fast.constant;

import com.fast.utils.DateUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.google.common.collect.Lists;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 常量
 *
 * <AUTHOR>
 */
public class StaticVar extends StaticYml {

    //-------------------------------------------------------------------------------
    // TODO 为了测试
    public static final List<Integer> TEST_DRAMA = Lists.newArrayList(
            13486, 13482, 13479, 13449, 13430,
            13429, 13427, 13404, 13386, 13383,
            13382, 13381, 13380, 13363, 13362
    );

    public static boolean isTestDrama(Integer dramaId) {
        return TEST_DRAMA.contains(dramaId);
    }
    //-------------------------------------------------------------------------------
    
    /**
     * 纯静态
     */
    public static final String DEFAULT_PASSWORD = "88888888";
    public static final int WX_VIR_ENV = 0;// 微信虚拟支付环境:0-正式;1=沙箱
    public static final int DATA_2K = 2000;
    public static final int DATA_1W = 10_000;
    public static final int ONLINE_TIME = 3;
    public static final int CHARGE_RATE = 100;// 一元兑换比列
    public static final String ACCESS_TOKEN_PRE = "fast_token:";// token有效期为2天(秒)
    public static final String ACCESS_OPENAPI_TOKEN_PRE = "fast_openapi_token:";// token有效期为5天(秒)
    public static final int ACCESS_TOKEN_EXP = 2 * 24 * 3600;// token有效期为2天(秒)
    public static final int ACCESS_APP_TOKEN_EXP = 7 * 24 * 3600;// token有效期为7天(秒)
    public static final int ACCESS_OPENAPI_TOKEN_EXP = 6 * 3600;// openapi token有效期为6小时(秒)
    // public static final int ACCESS_TOKEN_REFRESH = 48 * 3600 * 1000;// (毫秒)旧token时间超过48小时刷新重置token时间为4天
    public static final String SESSION_LOGIN_USER = "session_login_user";// session_key
    public static final String PROFILES = System.getProperty("spring.profiles.active");
    public static final String HOST_IP;
    public static final String MIDDLE_GROUND_TOKEN = "fast_middle_ground_token:";// token有效期为2天(秒)
    public static final String ALI_PAY_CALLBACK_PATH = "/upay/wap/asyncNotice/nologin/aliPay/callback/";
    public static final String ALI_PAY_CALLBACK_SAAS_PATH = "/upay/wap/asyncNotice/nologin/aliPay/callbackSaas";
    public static final String ALI_PAY_APP_CALLBACK_PATH = "/upay/wap/asyncNotice/nologin/aliPay/app/callback/";
    // public static final String ALI_PAY_APP_CALLBACK_SAAS_PATH = "/upay/wap/asyncNotice/nologin/aliPay/app/callbackSaas";
    public static final String HUA_WEI_TOKEN_URL = "https://oauth-login.cloud.huawei.com/oauth2/v3/token";
    public static final String HUA_WEI_ORDER_TOKEN_VERIFY_URL = "https://orders-drcn.iap.cloud.huawei.com.cn/applications/purchases/tokens/verify";
    public static final int ADMIN_PASSWORD_UPDATE_DAT = 90;
    public static final String ADMIN_PASSWORD_REG = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).+$";
    public static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$";
    public static final int DAY1_SECOND = 24 * 3600;
    public static final String ALI_OPEN_AUTH_ACCESS_TOKEN = "ali_open_auth_access_token:";
    public static final List<String> WE_CHAT_DESKTOP_SCENE = Arrays.asList("1023","1194");
    public static final String WE_CHAT_MEMBER_DESKTOP_FLAG = "we_chat_member_desktop_flag:";
    public static final String OCEAN_ENGINE_OPEN_OAUTH_ACCESS_TOKEN = "ocean_engine_open_oauth_access_token:"; // 基于OAUTH2认证方式所需的access_token
    public static final String OCEAN_ENGINE_OPEN_OAUTH_REFRESH_TOKEN = "ocean_engine_open_oauth_refresh_token:"; // 基于OAUTH2认证方式所需的refresh_token
    public static final String OCEAN_ENGINE_OPEN_AUTHORIZER_ACCESS_TOKEN_URL = "https://ad.oceanengine.com/open_api/oauth2/access_token/";
    public static final String OCEAN_ENGINE_OPEN_GET_USER_INFO_URL = "https://api.oceanengine.com/open_api/2/user/info/";
    public static final String OCEAN_ENGINE_OPEN_AUTHORIZER_REFRESH_TOKEN_URL = "https://api.oceanengine.com/open_api/oauth2/refresh_token/";
    public static final String OCEAN_ENGINE_OPEN_GET_ADVERTISER_URL = "https://ad.oceanengine.com/open_api/oauth2/advertiser/get/";
    public static final String FINDER_PHONE_CODE = "finder_phone_code:";
    public static final String FINDER_ROLE_NAME = "视频号达人";
    public static final String CRAWLER_SYNC_KEY = "crawler_sync_key";


    public static int getAdminTokenExp(long current) {
        // token过期时间固定为登录时间的第二天4点
        Date date1 = DateUtil.addDays(DateUtil.beginOfDay(new Date(current)), 1);
        return (int) ((DateUtil.addHours(date1, 4).getTime() - DateUtil.getNowDate().getTime()) / 1000);
    }

    static {
        try {
            HOST_IP = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 系统类型：0=自用;1=羽;2=天;3=永
     */
    public static final int SYSTEM_USE_TYPE_DRAMA;
    // 是否开启CP
    public static final int SYSTEM_OPEN_CP;

    static {
        String val = RedisUtil.get("system_use_type_drama");
        if (StrUtil.isNotEmpty(val)) {
            SYSTEM_USE_TYPE_DRAMA = Integer.parseInt(val);
        } else {
            SYSTEM_USE_TYPE_DRAMA = 0;
        }

        val = RedisUtil.get("system_use_cp");
        if (StrUtil.isNotEmpty(val)) {
            SYSTEM_OPEN_CP = Integer.parseInt(val);
        } else {
            SYSTEM_OPEN_CP = 1;
        }
    }

    /**
     * redis缓存key前缀
     */
    public static final String TAG_INFO_ID = "tag_info_id:";// 系统标签
    public static final String CORNER_INFO_ID = "corner_info_id:";// 系统角标
    public static final String WX_AUDIT_DRAMA_ID = "wx_audit_drama_id:";// 微信短剧审核剧id
    public static final String WX_FINDER_LINK_ID = "wx_finder_link_id:";// 微信视频号关联渠道信息
    public static final String WX_FINDER_INFO_ID = "wx_finder_info_id:";// 微信视频号信息
    public static final String MINI_MEMBER_VERSION_ID = "mini_member_version_id:";// 用户在小程序的配置版本
    public static final String VIDEO_INFO_ID = "video_info_id:";// 视频
    public static final String ORDER_FEE_SETTING = "order_fee_setting:";// 订单费率设置
    public static final String AD_GROUND_ID = "ad_ground_id:";// 广告落地页id
    public static final String MINI_INFO_ID = "mini_info_id:";// 小程序
    public static final String OFFICIAL_INFO_ID = "official_info_id:";// 公众号
    public static final String MINI_INFO_APPID = "mini_info_appid:";// 小程序
    public static final String TIKTOK_MINI_INFO_APPID = "tiktok_mini_info_appid:";// 小程序
    public static final String DRAMA_INFO_ID = "drama_info_id:";// 系统短剧
    public static final String RETAIL_DRAMA_LIST = "retail_drama_list:";// 分销商授权短剧
    public static final String RETAIL_INFO_ID = "retail_info_id:";// 分销商
    public static final String SETTING_FLOAT_LIST = "setting_float_list:";// 浮窗配置-根据公众号id
    public static final String DRAMA_SERIES = "drama_series:";// 系统短剧
    public static final String DRAMA_SERIES_NUM = "drama_series_num:";// 系统短剧剧集
    public static final String DRAMA_SERIES_ID = "drama_series_id:";// 系统短剧剧集
    public static final String FEE_RULE_ID = "fee_rule_id:";// 计费规则
    public static final String FEE_LINKID_RULE_ID = "fee_linkid_rule_id:";// 渠道计费规则
    public static final String LINK_INFO_ID = "link_info_id:";// 推广链接
    public static final String LINK_EXT_INFO_ID = "link_ext_info_id:";// 推广链接
    public static final String MODEL_DETAIL_ID = "model_detail_id:";// 充值模板详情
    public static final String MODEL_DETAIL_PO_ID = "model_detail_po_id:";// 充值模板详情
    public static final String MINI_CONTENT_SETTING = "mini_content_setting:";// 小程序配置
    public static final String MINI_CONTENT_SETTING_SIMPLE = "mini_content_setting_simple:";// 小程序配置-简单快速
    public static final String MINI_CONTENT_SETTING_VER = "mini_content_setting_ver:";// 小程序配置版本号
    public static final String MINI_CONTENT_SETTING_ONLY = "mini_content_setting_only:";// 小程序配置
    public static final String MINI_SETTING_DRAMA_IDS = "mini_setting_drama_ids:";// 小程序配置短剧id
    public static final String MEMBER_ACCOUNT_CHANGE = "member_account_change:";// 用户账户变动
    public static final String MEMBER_ACCOUNT_ID = "member_account_id:";// 用户账户
    public static final String MEMBER_ADDICTION = "member_addiction:";// 用户追剧
    public static final String MEMBER_SUBSCRIBE = "member_subscribe:";// 用户订阅
    public static final String MEMBER_RECENT_SERIESNUM = "member_recent_seriesnum:";// 观看记录
    public static final String MEMBER_RECENT = "member_recent:";// 用户观看
    public static final String MEMBER_INFO_ID = "member_info_id:";// 用户信息
    public static final String MEMBER_RECHARGE = "member_recharge:";// 用户链接充值记录
    public static final String MEMBER_SERIES_UNLOCK = "member_series_unlock:";// 用户已解锁剧集
    public static final String MEMBER_LAST_LOGIN = "member_last_login:";// 用户最后登录id By openid
    public static final String PAY_CHANNEL_MINI_ID = "pay_channel_mini_id:";// 小程序支付通道
    public static final String ACTIVITY_CLICK_LIST = "activity_click_list";// 促销活动点击队列
    public static final String ACTIVITY_INFO_ID = "activity_info_id:";// 促销活动
    public static final String DRAMA_INTRODUCTION = "drama_introduction:";// 剧介绍列表
    public static final String DRAMA_HAS_UNLOCK = "drama_has_unlock:";// 短剧解锁成功的锁, 只能使用一次
    public static final String OFFICIAL_GHID = "official_ghid:";// 公众号获取appid
    public static final String PUSH_KEFU = "push_kefu:";// 客服消息发送
    public static final String PUSH_KEFU_MEMBER = "push_kefu_member:";// 客服消息发送
    public static final String PUSH_DELAY_MEMBER = "push_delay_member:";// 延迟消息发送
    public static final String DRAMA_SUB = "drama_sub:";// 订阅消息剧集
    public static final String MINI_RELOAD = "mini_reload:";// 小程序重载
    public static final String OPENAPI_TIMES = "openapi_times:";// 开放接口调用次数
    public static final String MONITOR_MINI = "monitor_mini:";// 监控报警小程序
    public static final String MONITOR_MINI_LIST = "monitor_mini_list:";// 监控报警次数
    public static final String MONITOR_MINI_STATUS = "monitor_mini_status:";// 监控报警状态
    public static final String MONITOR_MINI_SEND = "monitor_mini_send:";// 监控报警状态
    public static final String MONITOR_MINI_SWITCH = "monitor_mini_switch:";// 监控报警状态
    public static final String H5_DOMAIN_MONITOR = "h5_domain_monitor:";// H5域名监控
    public static final String IM_WARN_SEND_COUNT = "im_warn_send_count:";// im报警发送次数

    public static final String CP_NOTICE_READ_STATUS = "cp_notice_read_status:";

    public static final String H5_UID = "h5_uid:"; //
    public static final String H5_TEMP_CODE = "h5_temp_code:"; //
    public static final String H5_FOLLOW_OPENID = "h5_follow_openid:";
    public static final String H5_FOLLOW_TIME_OPENID = "h5_follow_time_openid:";

    public static final String FLOCK_MEMBER = "flock_member:";
    public static final String Back_8_DAY = "back8day";

    public static final String STATIS_COST = "statis_cost:";
    public static final String LOG_DATE = "20231205";
    public static final String ECPM_TT_ID = "ecpm_tt_id:";
    public static final String HUA_WEI_AT_TOKEN = "hua_wei_at_token:";
    public static final String SIGN_WARN_STATE = "sign_warn_state:";

    /**
     * redis 锁key
     */
    public static final String A_ADD_RECENT_LOG_LOCK = "a_add_recent_log_lock:";// 观看剧集日志剧集锁-异步
    public static final String ADD_RECENT_LOG_LOCK = "add_recent_log_lock:";// 观看剧集日志剧集锁
    public static final String RECHARGE_ADD_LOCK = "recharge_add_lock:";// 充值下单锁
    public static final String UNLOCK_DRAMA_LOCK = "unlock_drama_lock:";// 解锁剧集
    public static final String ADD_COIN_CHANGE_LOCK = "add_coin_change_lock:";// 用户调币
    public static final String QUERY_FREE_SECONDS_LIST_LOCK = "query_free_seconds_list_lock";// 查询剧的总时长
    public static final String PRE_PAY_LOCK = "prepay_Web_lock:";
    public static final String PAY_TEMPLATE_ANALYSIS_LOCK = "pay_template_analysis_list_lock:";
    public static final String PAY_RATE_ANALYSIS_LIST_LOCK = "pay_rate_analysis_list_lock:";
    public static final String ORDER_TYPE_ANALYSIS_LIST_LOCK = "order_type_analysis_list_lock:";
    public static final String DRAMA_DETAIL_PAY_ANALYSIS_LIST_LOCK = "drama_detail_pay_analysis_list_lock:";
    public static final String DRAMA_DETAIL_CORE_DATA_ANALYSIS_LIST_LOCK = "drama_detail_core_data_analysis_list_lock:";
    public static final String BACK_SUM_ANALYSIS_LIST_LOCK = "back_sum_analysis_list_lock:";
    public static final String BACK_FAIL_REASON_ANALYSIS_LIST_LOCK = "back_fail_reason_analysis_list_lock:";
    public static final String RETAIL_RECHARGE_CONSUME_LOCK = "retail_recharge_consume_lock:";
    public static final String RETAIL_CORE_ANALYSIS_LOCK = "retail_core_analysis_lock:";
    public static final String ADD_M_ONLINE_LOCK = "add_m_online_lock:";
    public static final String WX_VIRTUAL_SUCCESS_LOCK = "wx_virtual_success_lock:";
    public static final String ONLINE_MEMBER_KEY = "online_member_set";
    public static final String GET_MEMBER_TASK_INFO_LOCK = "get_member_task_info_lock:";
    // public static final String DRAMA_DETAIL_ANALIYS_VERSION_2_LOCK = "drama_detail_analiys_version_2_lock:";

    public static final String CP_STATEMENT_RERUN_LOCK = "cp_statement_rerun_lock:";

    public static final String TIKTOK_IOS_PRE_PAY_LOCK = "tiktok_ios_pre_pay_lock:";
    public static final String LOCK_INSERT_WATCH_AD_END = "lock_in_wa_ad_en:";
    public static final String LOCK_INSERT_WATCH_AD_START = "lock_in_wa_ad_st:";
    public static final String LOCK_CLOSE_WATCH_AD = "lock_close_wa_ad:";
    public static final String INSERT_WATCH_AD_START = "in_wa_ad_st:";
    public static final String LOCK_STATIS_LINK_DATE = "lock_sld:";// 链接统计锁
    // public static final String ALI_PAY_PRE_PAY_LOCK = "ali_pay_pre_pay_lock:";
    public static final String TT_CODE_LOCK = "tt_code_lock:";
    public static final String TT_CODE_RES = "tt_code_res:";

    public static final String LOCK_REUPLOAD_KUAISHOU_DRAMA = "lock_reupload_kuaishou_drama:";// 快手短剧上传

    public static final String LOCK_TT_GIVE_COUPON = "lock_tt_give_coupon:";

    public static final String CONTINUE_WATCH = "【继续观看】";

    public static final String DREAM_ANALYSIS_DATA_REGISTER_HISTORY = "dream_analysis_data_register_history:";
    public static final String DREAM_ANALYSIS_DATA_REGISTER_YESTERDAY = "dream_analysis_data_register_yesterday:";

    public static final String DREAM_ANALYSIS_DATA_WATCH_HISTORY = "dream_analysis_data_watch_history:";
    public static final String DREAM_ANALYSIS_DATA_WATCH_YESTERDAY = "dream_analysis_data_watch_yesterday:";

    public static final String DREAM_ANALYSIS_DATA_CONSUME_HISTORY = "dream_analysis_data_consume_history:";
    public static final String DREAM_ANALYSIS_DATA_CONSUME_YESTERDAY = "dream_analysis_data_consume_yesterday:";
    public static final String DREAM_ANALYSIS_DATA_RECHARGE_HISTORY = "dream_analysis_data_recharge_history:";
    public static final String DREAM_ANALYSIS_DATA_RECHARGE_YESTERDAY = "dream_analysis_data_recharge_yesterday:";
    public static final String DREAM_ANALYSIS_DATA_COST_HISTORY = "dream_analysis_data_cost_history:";
    public static final String DREAM_ANALYSIS_DATA_COST_YESTERDAY = "dream_analysis_data_cost_yesterday:";
    public static final String TIKTOK_AIGC_AUTH_LOCK = "tiktok_aigc_auth_lock";
    public static final String TIKTOK_AIGC_AUTH_QPS_LIMIT = "tiktok_aigc_auth_qps_limit:";


    /**
     * 导出缓存相关
     */
    public static int EXPORT_NUMBER = 120; // 导出次数限制
    public static final String EXPORT_RATE = "export_rate:";// 导出频次限制标记
    public static final String EXPORT_MEMBER_LIST = "export_member_list:";// 用户导出
    public static final String EXPORT_OFFICIAL_ANNUAL_LIST = "export_official_annual_list:";// 公众号年审
    public static final String EXPORT_DRAMA_DAY_LIST = "export_drama_day_list:";// 用户导出
    public static final String EXPORT_TENCENT_DRAMA_LIST = "export_tencent_drama_list:";// 用户导出
    public static final String EXPORT_TIKTOK_DRAMA_LIST = "export_tiktok_drama_list:";// 用户导出
    public static final String EXPORT_LINK_LIST = "export_link_list:";// 链接导出
    public static final String EXPORT_OFFICIAL_LIST = "export_official_list:";// 链接导出
    public static final String EXPORT_RECHARGE_LIST = "export_recharge_list:";// 充值订单导出
    public static final String EXPORT_RECHARGE_ORDER_LIST = "export_recharge_order_list:";// 财务订单导出
    public static final String EXPORT_ACTIVITY_DATA_LIST = "export_activity_data_list:";// 活动数据导出
    public static final String EXPORT_ACTIVITY_DATA_ALL = "export_activity_data_all:";// 活动数据导出
    public static final String EXPORT_CONSUME_LIST = "export_consume_list:";// 消费记录导出
    public static final String EXPORT_COIN_CHANGE_LIST = "export_coin_change_list:";// 调币记录导出
    public static final String EXPORT_DRAMA_DAY_DETAIL = "EXPORT_DRAMA_DAY_DETAIL:";// 短剧单日明细表
    public static final String EXPORT_DRAMA_DETAIL_SERIES = "export_drama_detail_series:";// 剧集数据明细
    public static final String EXPORT_LAUNCH_RETAIL_DETAIL = "export_launch_retail_detail:";// 投放分销商明细表
    public static final String EXPORT_LINK_ROI = "export_link_roi:";// 渠道ROI梯度表
    public static final String EXPORT_FREE_LINK_ROI = "export_link_roi:";// 免费渠道ROI梯度表
    public static final String EXPORT_ORDER_RETAIL_DETAIL = "export_order_retail_detail:";// 订单统计导出
    public static final String EXPORT_DRAMA_DETAIL = "EXPORT_DRAMA_DETAIL:";// 短剧明细表
    public static final String EXPORT_REFUND_LIST = "EXPORT_REFUND_LIST:";// 退款列表
    public static final String EXPORT_SETTLEMENT_LOG_LIST = "export_settlement_log_list:";// 申请结算记录
    public static final String EXPORT_RECENT_LOG_LIST = "export_recent_log_list:";// 观看记录导出
    public static final String EXPORT_PROJECT_FINISHED_LIST = "export_project_finished_list:";// 观看记录导出
    public static final String EXPORT_ORDER_ANALYSIS_LIST = "export_order_analysis_list:";// 订单分析导出
    public static final String EXPORT_UNLOCK_START_LOG_LIST = "export_unlock_start_log_list:";// 广告接收明细导出
    public static final String EXPORT_ECPM_DATA_TT_LIST = "export_ecpm_data_tt_list:";// ecpm明细导出
    public static final String EXPORT_ORDER_CONTRACT_LIST = "export_order_contract_list:";// 签约记录导出

    public static final String EXPORT_CP_SCHEDULED_PROJECT = "export_cp_project_scheduled_list:";// cp项目-制作进度列表导出
    public static final String EXPORT_CP_ONLINE_PROJECT = "export_cp_project_online_list:";// cp项目-已上线投放列表导出

    public static final String EXPORT_CP_STATIC_GROUPBYPROJECT = "export_cp_static_groupByProject:";// cp项目-按项目汇总
    public static final String EXPORT_CP_STATIC_GROUPBYCP = "export_cp_static_groupByCP:";// cp项目-按团队汇总
    public static final String EXPORT_CP_STATIC_GROUPBYORG = "export_cp_static_groupByOrg:";// cp项目-按组织汇总
    public static final String EXPORT_CP_STATIC_GROUPBYPROJECT_DETAIL = "export_cp_static_groupByProject_detail:";// cp项目-按项目汇总
    public static final String EXPORT_CP_INDEX = "export_cp_index:";// cp项目-按项目汇总

    public static final String EXPORT_DRAMA_UNLOCK_CONSUME = "export_drama_unlock_consume:";// 短剧k币导出
    public static final String EXPORT_LINK_HIS_LIST = "export_link_his_list:";// 短剧k币导出
    public static final String EXPORT_FREE_LINK_LIST = "export_free_link_list:";// 免费短剧渠道链接列表导出
    public static final String EXPORT_FREE_DAY_ROI_LIST = "export_free_day_roi_list:";// 免费短剧渠道链接列表导出
    public static final String EXPORT_FAST_NATIVE_PLAYLET_LIST = "export_fast_native_playlet_list:";// 端原生短剧导出
    public static final String EXPORT_FAST_KUAISHOU_NATIVE_DRAMA = "export_fast_kuaishou_native_drama:";// 端原生短剧导出
    public static final String EXPORT_THIRD_ALIPAY_BILL_EXPORT = "export_third_alipay_bill_export:";// 支付宝分发结算单导出

    public static final String BACK_RESEND = "back_resend:";
    public static final String OPERATION_RATE = "operation_rate:";// 操作频次限制标记
    public static final String TABLE_UNPACK_DRAMA_ID_KEY = "table_unpack_drama_id";

    public static final String DRAMA_AD_REWARD_UNLOCK = "drama_ad_reward:";// 看广告解锁短剧

    /**
     * 账号变更相关缓存key前缀
     */
    public static final String STUDE_ACCOUNT_CHANGED = "stu_act_ch:";// 用户账号更新
    public static final String STAFF_ACCOUNT_CHANGED = "stf_act_ch:";// 员工账号更新
    public static final String RETAIL_ACCOUNT_CHANGED = "sto_act_ch:";// 分店信息更新
    public static final String BUSIN_ACCOUNT_CHANGED = "bus_act_ch:";// 总部信息更新
    public static final String CHARGE_SET = "fast_charge_set";// 兑换设置
    public static final String ROLE_LIST = "role_list:";// 角色列表
    public static final String MEMBER_FOLLOW = "member_follow:";// 用户关注状态


    public static final String EMPTY_FLAG = "empty..";// redis数据被标记为空
    public static final String IMPORT_TEMP_PATH = "/soft/temp/import/";
    public static final String EXPORT_TEMP_PATH = "/soft/temp/export/";
    public static final String UTF8 = "UTF-8";

    public static final int MED_INT = ********;// mediumint
    public static final int SMALL_INT = 65535;// smallint
    public static final int MILLION = 1_000_000;// 百万
    public static final int TEN_THOUSAND = 10_000;// 一万
    public static final long IMPORT_SIZE = 1024 * 1024;// 1MB
    public static final int YES = 1;
    public static final int NO = 0;
    public static final int ZERO = 0;
    public static final int HUNDRED = 100;
    public static final String STR_000 = "0.00";
    public static final String STR_0 = "0";
    public static final String WECHAT_OPEN_AUTH_ACCESS_TOKEN = "wechat_open_auth_access_token:";
    public static final String KUAISHOU_MINI_AUTH_ACCESS_TOKEN = "kuaishou_mini_auth_access_token:";

    public static final String YES_STR = "yes";
    public static final String NO_STR = "no";

    // 短剧推广-推广链接
    public static final int WECHAT_MINI_URL_SCHEME_EXP = 7 * 24 * 3600;// 小程序url scheme有效期为7天(秒),与h5客户端一致
    public static final String WECHAT_MINI_URL_SCHEME = "wechat_mini_url_scheme:%s:%s";// 小程序url scheme  redis key
    public static final String PROMOTE_MINI_SHORT_LINK = "short_link:";// 小程序短连接redis key
    public static final int PROMOTE_MINI_SHORT_LINK_EXP = 365 * 24 * 3600;// 小程序短连接redis缓存有效期为365天(秒)
    public static final String PROMOTE_MINI_MONITOR_LINK_STATE = "promote_mini_monitor_link_state";// 小程序url scheme  redis key


    public static final String WECHAT_OPEN_WEBVIEW_DOMAIN = "https://www.qq.com;https://m.qq.com";

    public static final String SMS_SIGN_NAME = "广州六零一";// 短信签名

    public static final String OFFICIAL_MINI_APPID = "official_mini_appid:";
    public static final String WE_CHAT_IAA_BACK_EXPIRED = "we_chat_iaa_back_expired:";
    public static final String WE_CHAT_IAA_BACK_TIME = "we_chat_iaa_back_time:";
    /**
     * 抖音开发平台相关
     */
    // 抖音票据
    public static final String TIKTOK_OPEN_VERIFY_TICKET = "tiktok_open_verify_ticket:";
    // 抖音授权令牌
    public static final String TIKTOK_OPEN_COMPONENT_ACCESS_TOKEN = "tiktok_open_component_access_token:";
    // 抖音开放平台授权token
    public static final String TIKTOK_OPEN_AUTH_ACCESS_TOKEN = "tiktok_open_auth_access_token:";
    // 抖音开发平台授权令牌token的URL
    public static final String TIKTOK_OPEN_COMPNENT_ACCESS_TOKEN_URL = "https://open.microapp.bytedance.com/openapi/v1/auth/tp/token";
    // 抖音开发平台预授权码的URL，不预设分账
    public static final String TIKTOK_OPEN_PRE_NO_AUTHCODE_URL = "https://open.microapp.bytedance.com/openapi/v1/create/tp/pre_auth_code";
    // 抖音开发平台预授权码的URL，预设分账
    public static final String TIKTOK_OPEN_PRE_YES_AUTHCODE_URL = "https://open.microapp.bytedance.com/openapi/v2/auth/pre_auth_code";
    // 抖音开放平台链接授权地址
    public static final String TIKTOK_OPEN_AUTH_LINK_URL = "https://open.microapp.bytedance.com/mappconsole/tp/authorization";
    // 抖音开放平台根据授权码获取授权信息
    public static final String TIKTOK_OPEN_AUTH_INFO = "https://open.microapp.bytedance.com/openapi/v1/oauth/token";
    // 抖音开放平台-找回授权码
    public static final String TIKTOK_OPEN_RETRIEVE_AUTH_CODE = "https://open.microapp.bytedance.com/openapi/v1/auth/retrieve";
    // 抖音开放平台-刷新授权小程序接口调用凭据
    public static final String TIKTOK_OPEN_REFRESH_AUTH_CODE = "https://open.microapp.bytedance.com/openapi/v1/oauth/token";
    // 抖音开放平台根据code获取openid
    public static final String TIKTOK_OPEN_GET_OPENID = "https://open.microapp.bytedance.com/openapi/v1/microapp/code2session";
    // 抖音开放平台-公众号订阅消息(接口预期下线时间为2024.07.01，接口文档下线后V1接口停止维护)
//    public static final String TIKTOK_SUBSCRIBE_MESSAGE_SERVICE = "https://developer.toutiao.com/api/apps/subscribe_notification/developer/v1/notify";
    // 抖音开放平台-公众号订阅消息
    public static final String TIKTOK_SUBSCRIBE_MESSAGE_SERVICE_V2 = "https://open.douyin.com/api/notification/v2/subscription/notify_user/";
    // 抖音开放平台-获取代码草稿列表
    public static final String TIKTOK_OPEN_TEMPLATE_DRAFT_URL = "https://open.microapp.bytedance.com/openapi/v1/tp/template/get_draft_list";
    // 抖音开放平台-获取代码模板列表
    public static final String TIKTOK_OPEN_TEMPLATE_URL = "https://open.microapp.bytedance.com/openapi/v1/tp/template/get_tpl_list";
    // 抖音开放平台-获取代码模板列表
    public static final String TIKTOK_OPEN_ADD_TO_TEMPLATE_URL = "https://open.microapp.bytedance.com/openapi/v1/tp/template/add_tpl";
    // 抖音开放平台-申请手机号
    public static final String TIKTOK_OPEN_APPLY_PHONE_URL = "https://open.microapp.bytedance.com/openapi/v1/microapp/operation/phone_number_application";
    // 抖音开放平台-删除代码模板
    public static final String TIKTOK_OPEN_DELETE_TEMPLATE_URL = "https://open.microapp.bytedance.com/openapi/v1/tp/template/del_tpl";
    // 抖音开放平台-代码上传
    public static final String TIKTOK_OPEN_UPLOAD_CODE_URL = "https://open.microapp.bytedance.com/openapi/v1/microapp/package/upload";
    // 抖音开放平台-提交审核
    public static final String TIKTOK_OPEN_SUBMIT_AUDIT_URL = "https://open.microapp.bytedance.com/openapi/v2/microapp/package/audit";
    // 抖音开放平台-可选审核宿主端列表
    public static final String TIKTOK_OPEN_AUDIT_HOST_URL = "https://open.microapp.bytedance.com/openapi/v1/microapp/package/audit_hosts";
    // 抖音开放平台-提交审核
    public static final String TIKTOK_OPEN_GET_AUDIT_URL = "https://open.microapp.bytedance.com/openapi/v1/microapp/package/versions";
    // 抖音开放平台-查询审核
    public static final String TIKTOK_OPEN_RELEASE_URL = "https://open.microapp.bytedance.com/openapi/v1/microapp/package/release";
    // 抖音开放平台-代码回退
    public static final String TIKTOK_OPEN_ROLLBACK = "https://open.microapp.bytedance.com/openapi/v1/microapp/package/rollback";
    // 抖音开放平台-获取体验码
    public static final String TIKTOK_OPEN_QR_CODE_URL = "https://open.microapp.bytedance.com/openapi/v1/microapp/app/qrcode";
    // 抖音开放平台-获取正式二维码
    public static final String TIKTOK_OPEN_QR_CODE_PROD_URL = "https://developer.toutiao.com/api/apps/qrcode";
    // 抖音开放平台-设置域名
    public static final String TIKTOK_OPEN_MODIFY_DOMAIN_URL = "https://open.microapp.bytedance.com/openapi/v1/microapp/app/modify_server_domain";
    // 抖音开放平台-设置webview域名
    public static final String TIKTOK_OPEN_MODIFY_WEBVIEW_DOMAIN_URL = "https://open.microapp.bytedance.com/openapi/v1/microapp/app/modify_webview_domain";
    // 抖音开放平台-支付预下单
    public static final String TIKTOK_ECPAY_CREATE_ORDER_URL = "https://developer.toutiao.com/api/apps/ecpay/v1/create_order";
    // 抖音开放平台-支付订单查询（担保支付）
    public static final String TIKTOK_ECPAY_QUERY_ORDER_URL = "https://developer.toutiao.com/api/apps/ecpay/v1/query_order";
    // 抖音开放平台-支付订单查询(通用支付)
    public static final String TIKTOK_ECPAY_COMMON_QUERY_ORDER_URL = "https://open.douyin.com/api/trade_basic/v1/developer/order_query";
    // 抖音开放平台-支付订单查询(vip包月代扣支付)
    public static final String TIKTOK_ECPAY_CONTRACT_QUERY_PAY_URL = "https://open.douyin.com/api/trade_auth/v1/developer/query_sign_pay/";
    public static final String TIKTOK_ECPAY_CONTRACT_QUERY_ORDER_URL = "https://open.douyin.com/api/trade_auth/v1/developer/query_sign_order/";
    // 抖音获取scheme
    public static final String TIKTOK_OPEN_GET_SCHEME_URL = "https://developer.toutiao.com/api/apps/v1/url/generate_schema";
    public static final String TIKTOK_OPEN_GET_LINK_URL = "https://developer.toutiao.com/api/apps/url_link/generate";
    // 抖音小程序基本信息
    public static final String TIKTOK_OPEN_MINI_BASE_INFO = "https://open.microapp.bytedance.com/openapi/v1/microapp/app/info";
    // 抖小代扣地址
    public static final String TIKTOK_CONTRACT_ORDER_PAY_URL = "https://open.douyin.com/api/trade_auth/v1/developer/create_sign_pay/";

    // 抖音-群主授权
    public static final String TIKTOK_FLOCK_AUTH = "https://open.douyin.com/oauth/access_token/";
    // 抖音-发送私信接口
    public static final String TIKTOK_SEND_MESSAGE_URL = "https://open.douyin.com/im/send/customer/service/msg/";
    // 平台接受私信webhook接口
    public static final String IM_CALLBACK_URL = StaticYml.FAST_UP_MINI_URL + "tiktokIM/nologin/im/callback";
    // 平台IOS抖音小程序预支付接口
    public static final String IOS_TT_PRE_URL = StaticYml.FAST_UP_MINI_URL + "tiktokPay/iosPayPre";
    // 抖音订单中心同步订单
    public static final String TIKTOK_ORDER_SYN_URL = "https://developer.toutiao.com/api/apps/order/v2/push";
    // 抖音订单分账
    public static final String TIKTOK_ORDER_SETTLE_URL = "https://developer.toutiao.com/api/apps/ecpay/v1/settle";
    // 抖音订单分账
    public static final String TIKTOK_ORDER_SETTLE_COMMON_URL = "https://open.douyin.com/api/trade_basic/v1/developer/settle_create";
    // 抖音订单分账查询
    public static final String TIKTOK_ORDER_SETTLE_COMMON_QUERY_URL = "https://open.douyin.com/api/trade_basic/v1/developer/settle_query";

    // 抖音订单退款
    public static final String TIKTOK_ORDER_REFUND_URL = "https://developer.toutiao.com/api/apps/ecpay/v1/create_refund";

    //---------  抖音内容审核
    public static final String TIKTOK_SET_CALLBACK_URL = "https://open.douyin.com/api/industry/v1/solution/set_impl";
    // 抖音审核授权token
    public static final String TIKTOK_AUDIT_AUTH_ACCESS_TOKEN = "tiktok_audit_auth_access_token:";
    // 查询激活状态
    public static final String TIKTOK_OPENID_ACTIVE_QUERY = "https://open.douyin.com/api/playlet/v1/active_user/query/";
    // 抖音aigc授权
    public static final String TIKTOK_AIGC_AUTH_URL = "https://open.douyin.com/api/resource_library/util/v1/batch_auth_contents/";

    /**
     * 微信开放平台相关
     */
    // 总部公众号appid
    public static final String WECHAT_OPEN_APPID_ACCOUNT = "wechat_open_appid_account:";
    // 总部小程序appid
    public static final String WECHAT_OPEN_APPID_MINI = "wechat_open_appid_mini:";
    // 微信开放平台票据
    public static final String WECHAT_OPEN_VERIFY_TICKET = "wechat_open_verify_ticket:";
    // 微信开放平台令牌
    public static final String WECHAT_OPEN_ACCESS_TOKEN = "wechat_open_access_token:";
    // 微信开放平台预授权码
    public static final String WECHAT_OPEN_PRE_AUTHCODE = "wechat_open_pre_authcode:";
    // 微信开放平台预授权码
    public static final String WECHAT_OPEN_AUTH_ACCESS_TKEN = "wechat_open_auth_access_token:";
    // 微信开发平台授权令牌token的URL
    public static final String WECHAT_OPEN_COMPNENT_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/component/api_component_token";
    // 微信开发平台预授权码的URL
    public static final String WECHAT_OPEN_PRE_AUTHCODE_URL = "https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token=%s";
    // 微信开放平台扫码授权页面地址
    public static final String WECHAT_OPEN_AUTH_QRCODE_URL = "https://mp.weixin.qq.com/cgi-bin/componentloginpage";
    // 微信开放平台链接授权地址
    public static final String WECHAT_OPEN_AUTH_LINK_URL = "https://mp.weixin.qq.com/safe/bindcomponent";
    // 微信开放平台根据code获取openid
    public static final String WECHAT_OPEN_GET_OPENID = "https://api.weixin.qq.com/sns/component/jscode2session";
    // 微信开放平台根据授权码获取授权信息
    public static final String WECHAT_OPEN_AUTH_INFO = "https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token=%s";

    // 微信开放平台-公众号获取菜单
    public static final String WECHAT_OFFICIAL_MENU = "https://api.weixin.qq.com/cgi-bin/get_current_selfmenu_info?access_token=%s";
    // 微信开放平台-公众号创建菜单
    public static final String WECHAT_OFFICIAL_MENU_INSERT = "https://api.weixin.qq.com/cgi-bin/menu/create?access_token=%s";
    // 微信开放平台-公众号删除菜单
    public static final String WECHAT_OFFICIAL_MENU_DELETE = "https://api.weixin.qq.com/cgi-bin/menu/delete?access_token=%s";
    // 微信开放平台-公众号上传媒体文件
    public static final String WECHAT_OFFICIAL_MEDIA_UPLOAD = "https://api.weixin.qq.com/cgi-bin/material/add_material?access_token=%s";
    public static final String WECHAT_OFFICIAL_MEDIA_TEMP_UPLOAD = "https://api.weixin.qq.com/cgi-bin/media/upload?access_token=%s";
    // public static final String WECHAT_OFFICIAL_MEDIA_UPLOAD = "https://api.weixin.qq.com/cgi-bin/media/uploadimg?access_token=%s";
    // 微信开放平台-公众号客服消息
    public static final String WECHAT_OFFICIAL_MESSAGE_SERVICE = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=%s";
    // 微信开放平台-公众号订阅消息
    public static final String WECHAT_SUBSCRIBE_MESSAGE_SERVICE = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s";


    // 微信开放平台刷新用户令牌
    public static final String WECHAT_OPEN_AUTHORIZER_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=%s";
    // 微信开放平台-获取机构小程序基本信息
    public static final String WECHAT_OPEN_BIND_USER_BASE_URL = "https://api.weixin.qq.com/cgi-bin/account/getaccountbasicinfo?access_token=%s";
    // 微信开放平台-获取用户手机号基本信息
    public static final String WECHAT_OPEN_USER_PHONE_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s";
    // 微信开放平台-获取机构小程序基本信息
    public static final String WECHAT_OPEN_BIND_USER_TT_URL = "https://api.weixin.qq.com/wxa/getwxacode?access_token=%s";
    // 微信开放平台-获取机构公众号基本信息(机构自己注册)
    public static final String WECHAT_OPEN_BIND_USER_BASE_HAS_URL = "https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?component_access_token=%s";
    // 微信开放平台-获取机构小程序基本信息
    public static final String WECHAT_OPEN_OFFICIAL_BASE_HAS_URL = "https://api.weixin.qq.com/cgi-bin/account/getaccountbasicinfo?access_token=%s";
    // 微信开放平台-获取代码草稿列表
    public static final String WECHAT_OPEN_TEMPLATE_DRAFT_URL = "https://api.weixin.qq.com/wxa/gettemplatedraftlist?access_token=%s";
    // 微信开放平台-获取代码模板列表
    public static final String WECHAT_OPEN_TEMPLATE_URL = "https://api.weixin.qq.com/wxa/gettemplatelist?access_token=%s";
    // 微信开放平台-获取代码模板列表
    public static final String WECHAT_OPEN_ADD_TO_TEMPLATE_URL = "https://api.weixin.qq.com/wxa/addtotemplate?access_token=%s";
    // 微信开放平台-删除代码模板
    public static final String WECHAT_OPEN_DELETE_TEMPLATE_URL = "https://api.weixin.qq.com/wxa/deletetemplate?access_token=%s";
    // 微信开放平台-代码上传
    public static final String WECHAT_OPEN_UPLOAD_CODE_URL = "https://api.weixin.qq.com/wxa/commit?access_token=%s";
    // 微信开放平台-获取体验码
    public static final String WECHAT_OPEN_QR_CODE_URL = "https://api.weixin.qq.com/wxa/get_qrcode?access_token=%s";
    // 微信开放平台-获取正式二维码
    public static final String WECHAT_OPEN_QR_CODE_PROD_URL = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token=%s";
    // 微信开放平台-获取正式小程序码（有限100000）
    public static final String WECHAT_OPEN_QR_CODE_MINI_PROD_URL = "https://api.weixin.qq.com/wxa/getwxacode?access_token=%s";
    // 微信开放平台-获取正式小程序码（无限）
    public static final String WECHAT_OPEN_QR_CODE_MINI_UNLIMIT_PROD_URL = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s";
    // 微信开放平台-设置域名
    public static final String WECHAT_OPEN_MODIFY_DOMAIN_URL = "https://api.weixin.qq.com/wxa/modify_domain?access_token=%s";
    // 微信开放平台-设置域名
    public static final String WECHAT_OPEN_MODIFY_WEBVIEW_DOMAIN_URL = "https://api.weixin.qq.com/wxa/setwebviewdomain?access_token=%s";
    // 微信开放平台-获取绑定体验用户列表
    public static final String WECHAT_OPEN_BIND_USER_LIST_URL = "https://api.weixin.qq.com/wxa/memberauth?access_token=%s";
    // 微信开放平台-添加体验用户
    public static final String WECHAT_OPEN_BIND_TESTER_URL = "https://api.weixin.qq.com/wxa/bind_tester?access_token=%s";
    // 微信开放平台-提交审核
    public static final String WECHAT_OPEN_SUBMIT_AUDIT_URL = "https://api.weixin.qq.com/wxa/submit_audit?access_token=%s";
    // 微信开放平台-提交审核撤回
    public static final String WECHAT_OPEN_CANCEL_AUDIT_URL = "https://api.weixin.qq.com/wxa/undocodeaudit?access_token=%s";
    // 微信开放平台-提交审核撤回
    public static final String WECHAT_OPEN_UNDO_SUBMIT_AUDIT_URL = "https://api.weixin.qq.com/wxa/undocodeaudit?access_token=%s";
    // 微信开放平台-查询审核
    public static final String WECHAT_OPEN_GET_AUDIT_URL = "https://api.weixin.qq.com/wxa/get_auditstatus?access_token=%s";
    // 微信开放平台-查询审核
    public static final String WECHAT_OPEN_RELEASE_URL = "https://api.weixin.qq.com/wxa/release?access_token=%s";
    // 微信开放平台-版本回退
    public static final String WECHAT_OPEN_REVERT_CODE = "https://api.weixin.qq.com/wxa/revertcoderelease?access_token=%s";
    // 微信开放平台-查询剩余次数
    public static final String WECHAT_OPEN_GET_QUOTA_URL = "https://api.weixin.qq.com/cgi-bin/openapi/quota/get?access_token=%s";
    // 微信开放平台-查询剩余次数清零
    public static final String WECHAT_OPEN_CLEAR_QUOTA_URL = "https://api.weixin.qq.com/cgi-bin/clear_quota?access_token=%s";
    // 微信开放平台-查询版本信息
    public static final String WECHAT_OPEN_RELEASE_VERSION_URL = "https://api.weixin.qq.com/wxa/getversioninfo?access_token=%s";
    // 微信开放平台-获取日数据
    public static final String WECHAT_OPEN_ANALYSE_DAILY_VISIT_URL = "https://api.weixin.qq.com/datacube/getweanalysisappiddailyvisittrend?access_token=%s";
    // 微信开放平台-获取日分享数据
    public static final String WECHAT_OPEN_ANALYSE_DAILY_SHARE_URL = "https://api.weixin.qq.com/datacube/getweanalysisappiddailysummarytrend?access_token=%s";
    // 微信开放平台-插件管理
    public static final String WECHAT_OPEN_PLUGIN_URL = "https://api.weixin.qq.com/wxa/plugin?access_token=%s";
    // 微信开放平台-发送统一消息
    public static final String WECHAT_OPEN_UNION_MESSAGE_URL = "https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?access_token=%s";
    // 微信开放平台-设置隐私协议
    public static final String WECHAT_OPEN_SET_PRIVACY_URL = "https://api.weixin.qq.com/cgi-bin/component/setprivacysetting?access_token=%s";
    // 微信开放平台-查询隐私协议
    public static final String WECHAT_OPEN_GET_PRIVACY_URL = "https://api.weixin.qq.com/cgi-bin/component/getprivacysetting?access_token=%s";
    // 微信小程序-获取 URL Scheme-URL
    public static final String WECHAT_MINI_GET_SCHEME_URL = "https://api.weixin.qq.com/wxa/generatescheme?access_token=%s";
    // 微信小程序-获取 AccessToken-URL
    public static final String WECHAT_MINI_GET_ACESSTOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=%s&appid=%s&secret=%s";
    // 公众号openid
    public static final String WECHAT_OFFICIAL_OPENID_URL = "https://api.weixin.qq.com/sns/oauth2/component/access_token?code=%s&grant_type=%s&component_appid=%s&appid=%s&component_access_token=%s";
    // 微信开放平台-创建微信数据源
    public static final String WECHAT_OPEN_CREATE_MP_SOURCE_URL = "https://api.weixin.qq.com/marketing/user_action_sets/add?version=v1.0&access_token=%s";
    // 微信开放平台-上传用户行为数据-微信数据源
    public static final String WECHAT_OPEN_ADD_USER_ACTION_MP_SOURCE_URL = "https://api.weixin.qq.com/marketing/user_actions/add?version=v1.0&access_token=%s";
	// adq3.0url前缀
	public static final String ADQ_V3_URL = "https://api.e.qq.com/v3.0";


    // 快手平台，小程序接口，获取openid
    public static final String KUAISHOU_MINI_GET_OPENID = "https://open.kuaishou.com/oauth2/mp/code2session";
    // 快手平台，小程序接口，获取接口调用凭证
    public static final String KUAISHOU_MINI_ACCESS_TOKEN = "https://open.kuaishou.com/oauth2/access_token";
    // 快手平台，小程序接口，预下单接口
    public static final String KUAISHOU_MINI_CREATE_ORDER = "https://open.kuaishou.com/openapi/mp/developer/epay/create_order_with_channel?app_id=%s&access_token=%s";
    // 快手平台，小程序接口，订单查询接口
    public static final String KUAISHOU_MINI_QUERY_ORDER = "https://open.kuaishou.com/openapi/mp/developer/epay/query_order?app_id=%s&access_token=%s";
    // 快手平台，小程序接口，取消支付接口
    public static final String KUAISHOU_MINI_CANCEL_CHANNEL = "https://open.kuaishou.com/openapi/mp/developer/epay/cancel_channel?app_id=%s&access_token=%s";


    // 快手三方-启动推送票据
    public static final String KUAISHOU_START_PUSH_TICKET = "https://open.kuaishou.com/mp/tp/oauth/start_push_ticket";
    // 快手三方-票据
    public static final String KUAISHOU_SAAS_TICKET = "kuaishou_saas_ticket:";
    // 快手三方-平台授权token
    public static final String KUAISHOU_SAAS_AUTH_ACCESS_TOKEN = "kuaishou_saas_auth_access_token:";
    // 快手三方-授权令牌
    public static final String KUAISHOU_SAAS_COMPONENT_ACCESS_TOKEN = "kuaishou_saas_component_access_token:";
    // 快手三方-平台授权令牌token的URL
    public static final String KUAISHOU_SAAS_COMPNENT_ACCESS_TOKEN_URL = "https://open.kuaishou.com/mp/tp/oauth/access_token";
    // 快手三方-平台预授权码的URL，不预设分账
    public static final String KUAISHOU_SAAS_PRE_NO_AUTHCODE_URL = "https://open.kuaishou.com/mp/tp/oauth/pre_auth_code";
    // 快手三方-平台链接授权地址
    public static final String KUAISHOU_SAAS_AUTH_LINK_URL = "https://mp-saas.kuaishou.com/oauth/authorization";
    // 快手三方-平台根据授权码获取授权信息
    public static final String KUAISHOU_SAAS_AUTH_INFO = "https://open.kuaishou.com/mp/tp/oauth/authorizer_access_token";
    // 快手三方-平台找回授权码
    public static final String KUAISHOU_SAAS_FINDBACK_AUTH_CODE = "https://open.kuaishou.com/mp/tp/oauth/re_authorization_code";
    // 快手三方-平台刷新授权码
    public static final String KUAISHOU_SAAS_REFRESH_AUTH_CODE = "https://open.kuaishou.com/mp/tp/oauth/authorizer_refresh_token";
    // 快手三方-平台根据code获取openid
    public static final String KUAISHOU_SAAS_GET_OPENID = "https://open.kuaishou.com/openapi/mp/tp/auth/code2session";
    // 快手三方-代码上传
    public static final String KUAISHOU_SAAS_UPLOAD_CODE_URL = "https://open.kuaishou.com/openapi/mp/auth/package/commit?authorizer_access_token=%s&component_app_id=%s";
    // 快手三方-获取体验码
    public static final String KUAISHOU_SAAS_QR_CODE_URL = "https://open.kuaishou.com/openapi/mp/auth/package/get_exp_qrcode?authorizer_access_token=%s&component_app_id=%s";
    // 快手三方-设置服务域名
    public static final String KUAISHOU_SAAS_MODIFY_DOMAIN_URL = "https://open.kuaishou.com/openapi/mp/auth/app/server_domain?authorizer_access_token=%s&component_app_id=%s";
    // 快手三方-设置业务域名
    public static final String KUAISHOU_SAAS_MODIFY_WEBVIEW_DOMAIN_URL = "https://open.kuaishou.com/openapi/mp/auth/app/webview_domain?authorizer_access_token=%s&component_app_id=%s";
    // 快手三方-提交审核
    public static final String KUAISHOU_SAAS_SUBMIT_AUDIT_URL = "https://open.kuaishou.com/openapi/mp/auth/package/submit_audit?authorizer_access_token=%s&component_app_id=%s";
    // 快手三方-提交审核撤回
    public static final String KUAISHOU_SAAS_UNDO_SUBMIT_AUDIT_URL = "https://open.kuaishou.com/openapi/mp/auth/package/review_recall?authorizer_access_token=%s&component_app_id=%s";
    // 快手三方-查询审核
    public static final String KUAISHOU_SAAS_GET_AUDIT_URL = "https://open.kuaishou.com/openapi/mp/auth/package/get_audit_status?authorizer_access_token=%s&component_app_id=%s";
    // 快手三方-查询审核
    public static final String KUAISHOU_SAAS_RELEASE_URL = "https://open.kuaishou.com/openapi/mp/auth/package/release?authorizer_access_token=%s&component_app_id=%s";
    // 快手三方-查询版本信息
    public static final String KUAISHOU_SAAS_RELEASE_VERSION_URL = "https://open.kuaishou.com/openapi/mp/auth/package/version?authorizer_access_token=%s&component_app_id=%s";
    // 快手三方-版本回退
    public static final String KUAISHOU_SAAS_REVERT_CODE = "https://open.kuaishou.com/openapi/mp/auth/package/revert?authorizer_access_token=%s&component_app_id=%s";
    // 快手小程序-上传图片
    public static final String KUAISHOU_SAAS_UPLOAD_IMGG = "https://open.kuaishou.com/openapi/mp/developer/file/img/uploadWithUrl";
    // 快手小程序-订单同步
    public static final String KUAISHOU_MINI_SYN_ORDER = "https://open.kuaishou.com/openapi/mp/developer/order/v1/report";
    // 快手小程序-订单结算
    public static final String KUAISHOU_MINI_SETTLE_ORDER = "https://open.kuaishou.com/openapi/mp/developer/epay/settle";
    // 快手小程序-订单结算查询
    public static final String KUAISHOU_MINI_SETTLE_ORDER_QUERY = "https://open.kuaishou.com/openapi/mp/developer/epay/query_settle";
    // 快手小程序-订单退款
    public static final String KUAISHOU_MINI_REFUND_ORDER = "https://open.kuaishou.com/openapi/mp/developer/epay/apply_refund";

    // 支付宝小程序
    // 支付宝开放平台链接授权地址
    public static final String ALI_OPEN_AUTH_LINK_URL = "https://b.alipay.com/page/message/tasksDetail";
    public static final String ALI_OPEN_MINI_APP_TYPE = "TINYAPP";


    // 开放平台-腾讯短剧审核
    // 拉取上传地址
    public static final String TENCENT_AUDIT_PULL_UPLOAD = "https://api.weixin.qq.com/wxa/sec/vod/pullupload?access_token=%s";
    // 任务状态
    public static final String TENCENT_AUDIT_TASK_SYN_URL = "https://api.weixin.qq.com/wxa/sec/vod/gettask?access_token=%s";
    // 媒资列表
    public static final String TENCENT_AUDIT_TASK_MEDIA_LIST_URL = "https://api.weixin.qq.com/wxa/sec/vod/listmedia?access_token=%s";
    // 媒资详情
    public static final String TENCENT_AUDIT_TASK_MEDIA_SYN_URL = "https://api.weixin.qq.com/wxa/sec/vod/getmedia?access_token=%s";
    // 剧目详情
    public static final String TENCENT_AUDIT_DRAMA_SYN_URL = "https://api.weixin.qq.com/wxa/sec/vod/getdrama?access_token=%s";
    // 媒资链接
    public static final String TENCENT_AUDIT_MEDIA_URL = "https://api.weixin.qq.com/wxa/sec/vod/getmedialink?access_token=%s";
    // 剧目审核
    public static final String TENCENT_AUDIT_DRAMA_URL = "https://api.weixin.qq.com/wxa/sec/vod/auditdrama?access_token=%s";
    // 修改剧目信息
    public static final String TENCENT_MODIFY_DRAMA_URL = "https://api.weixin.qq.com/wxa/sec/vod/modifydramabasicinfo?access_token=%s";
    // 修改剧目信息查询审核状态
    public static final String TENCENT_QUERY_MODIFY_DRAMA_URL = "https://api.weixin.qq.com/wxa/sec/vod/getdramalatestauditinfo?access_token=%s";
    // 小程序授权
    public static final String TENCENT_AUDIT_AUTH_MINI = "https://api.weixin.qq.com/wxa/sec/vod/authorizeapp?access_token=%s";
    // cdn用量查询
    public static final String TENCENT_AUDIT_CDN_URL = "https://api.weixin.qq.com/wxa/sec/vod/getcdnusagedata?access_token=%s";

    // 开放平台-抖音短剧审核
    // 视频上传
    public static final String TIKTOK_AUDIT_VIDEO_UPLOAD = "https://open.douyin.com/api/playlet/v1/video/upload/";
    public static final String TIKTOK_AUDIT_VIDEO_UPLOAD_V2 = "https://open.douyin.com/api/playlet/v2/resource/upload/";
    // 新增剧审接口
    public static final String TIKTOK_AUDIT_PRODUCT_ADD_URL = "https://open.douyin.com/api/product/v1/product_add/";
    public static final String TIKTOK_AUDIT_PRODUCT_ADD_URL_V2 = "https://open.douyin.com/api/playlet/v2/video/create/";
    public static final String TIKTOK_AUDIT_PRODUCT_EDIT_URL = "https://open.douyin.com/api/product/v1/product_modify/";
    // 选集部分修改接口
    public static final String TIKTOK_AUDIT_SERIES_PART_URL = "https://open.douyin.com/api/product/v1/fulfillment_modify/";
    // 修改剧
    public static final String TIKTOK_AUDIT_SERIES_PART_URL_V2 = "https://open.douyin.com/api/playlet/v2/video/edit/";
    // 抖音提审-模板
    public static final String TIKTOK_AUDIT_TEMPLATE = "https://open.douyin.com/api/product/v1/product_template_query/";
    public static final String TIKTOK_AUDIT_SUBMIT_V2 = "https://open.douyin.com/api/playlet/v2/video/review/";
    // 图片上传
    public static final String TIKTOK_AUDIT_UPLOAD_IMAGE = "https://open.douyin.com/api/playlet/v1/pic/upload/";
    public static final String TIKTOK_AUDIT_UPLOAD_IMAGE_V2 = "https://open.douyin.com/api/playlet/v2/resource/upload/";
    // 查询短剧审核详情
    public static final String TIKTOK_AUDIT_PRODUCT_DETAIL = "https://open.douyin.com/api/product/v1/productdraft_list/";
    public static final String TIKTOK_AUDIT_ALBUM_DETAIL_V2 = "https://open.douyin.com/api/playlet/v2/album/fetch/";
    // 视频上传状态查询
    public static final String TIKTOK_AUDIT_VIDEO_STATUS = "https://open.douyin.com/api/playlet/v1/video/get_upload_status/";
    public static final String TIKTOK_AUDIT_VIDEO_STATUS_V2 = "https://open.douyin.com/api/playlet/v2/video/query/";
    // 关联小程序
    public static final String TIKTOK_AUDIT_RELATE_MINI_V2 = "https://open.douyin.com/api/playlet/v2/auth/authorize/";
    // 获取播放链接
    public static final String TIKTOK_CDN_PLAY_URL_V2 = "https://open.douyin.com/api/playlet/v2/video/play_info/";
    // 短剧上线
    public static final String TIKTOK_AUDIT_ONLINE_URL_V2 = "https://open.douyin.com/api/playlet/v2/album/online/";
    // 短剧上线
    public static final String TIKTOK_AUDIT_BIND_URL_V2 = "https://open.douyin.com/api/playlet/v2/album/bind/";
    // 复审
    public static final String TIKTOK_AUDIT_BIND_REVIEW_URL_V2 = "https://open.douyin.com/api/resource_library/v1/post_review/query/";
    // 分发开关
    public static final String TIKTOK_AUDIT_DIS_OPERATE_URL_V1 = "https://open.douyin.com/api/resource_library/v1/distribution/operate/";
    // 分发查询
    public static final String TIKTOK_AUDIT_DIS_OPERATE_QUERY_URL_V1 = "https://open.douyin.com/api/resource_library/v1/distribution/query/";


    public static final String TENCENT_MEDIA_URL = "tencent_media_url:";
    public static final String TENCENT_AUDIT_SYN_FLAG = "tencent_audit_syn_flag";
    public static final String TENCENT_AUDIT_MEDIA_SYN_FLAG = "tencent_audit_media_syn_flag";
    public static final String TENCENT_AUDIT_DRAMA_SYN_FLAG = "tencent_audit_drama_syn_flag";
    public static final String TENCENT_AUDIT_TASK_SYN = "tencent_audit_task_syn:";
    public static final String TENCENT_AUDIT_TASK_MEDIA_SYN = "tencent_audit_task_media_syn:";
    public static final String TENCENT_AUDIT_DRAMA_SYN = "tencent_audit_drama_syn:";


    // 微信公众号授权
    public static final String WX_OAUTH_URL = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=%s&component_appid=%s&state=%s#wechat_redirect";
    // 获取js调用凭证接口(GET)
    public static final String WECHAT_GET_JSAPI_TICKET_URL = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi";

    // 微信支付统一接口(POST)
    public static final String WECHAT_UNIFIED_ORDER_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";
    // 微信支付刷卡接口(POST)
    public static final String WECHAT_MICROPAY_URL = "https://api.mch.weixin.qq.com/pay/micropay";
    // 微信支付刷卡查询接口(POST)
    public static final String WECHAT_ORDER_QUERY_URL = "https://api.mch.weixin.qq.com/pay/orderquery";
    // 微信退款接口(POST)
    public static final String WECHAT_REFUND_URL = "https://api.mch.weixin.qq.com/secapi/pay/refund";
    // 订单查询接口(POST)
    public static final String WECHAT_CHECK_ORDER_URL = "https://api.mch.weixin.qq.com/pay/orderquery";
    // 关闭订单接口(POST)
    public static final String WECHAT_CLOSE_ORDER_URL = "https://api.mch.weixin.qq.com/pay/closeorder";
    // 退款查询接口(POST)
    public static final String WECHAT_REFUND_QUERY_URL = "https://api.mch.weixin.qq.com/pay/refundquery";
    // 对账单接口(POST)
    public static final String WECHAT_DOWNLOAD_BILL_URL = "https://api.mch.weixin.qq.com/pay/downloadbill";
    // 短链接转换接口(POST)
    public static final String WECHAT_SHORT_URL = "https://api.mch.weixin.qq.com/tools/shorturl";
    // 接口调用上报接口(POST)
    public static final String WECHAT_REPORT_URL = "https://api.mch.weixin.qq.com/payitil/report";
    // 微信虚拟支付查询订单
    public static final String WX_VIR_QUERY_ORDER = "https://api.weixin.qq.com/xpay/query_order?access_token=%s&pay_sig=%s";
    // 微信虚拟支付下载账单
    public static final String WX_VIR_DOWNLOAD_BILL = "https://api.weixin.qq.com/xpay/download_bill?access_token=%s&pay_sig=%s";
    // 微信虚拟支付通知已经发货完成
    public static final String WX_VIR_NOTIFY_PROVIDE_GOODS = "https://api.weixin.qq.com/xpay/notify_provide_goods?access_token=%s&pay_sig=%s";


    public static final int NOTIFY_MAX_COUNT = 5;// 最大重试次数


    // 2999-12-31  1099=2999-1900
    public static final Date MAX_DATE = new Date(1099, Calendar.DECEMBER, 31);
    // 2000-01-01  100=2000-1900
    public static final Date MIN_DATE = new Date(100, Calendar.JANUARY, 1);
    public static final Date DATE_17 = DateUtil.format07("2023-11-17 00:00:00");


    public static final String STATIS_LINK_IDS = "statis_link_ids";
    public static final String STATIS_LINK_DATE = "statis_link_date";
    public static final String STATIS_LINK_QUEUE_JOB = "statis_link_queue_job:";
    public static final String ACTION_LOG_CACHE = "action_log_cache";

    public static final String STATIS_LINK_CREATE = "statis_link_create:";

    public static final String MD5 = "MD5";
    public static final String CNY = "CNY";
    public static final String JSAPI = "JSAPI";
    public static final String WX = "wx";
    public static final String W1 = "W1";// 微信公众号
    public static final String W2 = "W2";// 微信小程序
    public static final String TT = "T1";// 抖音小程序
    public static final String KH = "K1";// 快手小程序
    public static final String A1 = "A1";// 支付宝

    public static final int PAY_H5 = 0;// H5
    public static final int PAY_WX_MINI = 1;// 微信小程序
    public static final int PAY_TT_MINI = 3;// 抖音小程序
    public static final int PAY_KH_MINI = 4;// 快手小程序
    public static final int PAY_WX_VIRTUAL = 5;// 微信虚拟
    public static final int PAY_ALI_MINI = 7;// 支付宝小程序
    public static final int PAY_OPPO_MINI = 9;// oppo快应用支付
    public static final int PAY_APP_MINI = 10;// App应用支付

    public static final String S = "S";
    public static final String F = "F";
    public static final String A = "A";
    public static final String W = "W";
    public static final String U = "U";

    public static final String RECV_ORD_ID_ = "RECV_ORD_ID_";

    public static final String C000 = "000";
    public static final String C1000 = "1000";
    public static final String C2030 = "2030";

    // 汇付版本号
    public static final String API_VERSION2 = "2.0.0.1";
    public static final String API_VERSION3_2 = "3.0.0.2";
    public static final String API_VERSION3_1 = "3.0.0.1";
    // 支付回调通知
    public static final String PAY_CALLBACK = "u_pay_call_back:";
    public static final String HF_MER_NOTICE_URL = FAST_UP_MINI_URL + "upay/wap/asyncNotice/uPayCallBack";
    public static final String WX_NOTIFY_PAY_URL = FAST_UP_MINI_URL + "upay/wap/asyncNotice/wxPayCallBack";
    public static final String TT_NOTIFY_PAY_URL = FAST_UP_MINI_URL + "upay/wap/asyncNotice/ttPayCallBack";
    public static final String TT_NOTIFY_COMMON_PAY_URL = FAST_UP_MINI_URL + "upay/wap/asyncNotice/ttPayCallBackCommon";
    public static final String KH_NOTIFY_PAY_URL = FAST_UP_MINI_URL + "upay/wap/asyncNotice/khPayCallBack";
    public static final String WX_NOTIFY_REF_URL = FAST_UP_MINI_URL + "upay/wap/asyncNotice/wxRefCallBack";

    public static final String MEM_PFX_FILE_PASSWORD = "ste123";

    public static String PAY_CHANNEL_ID_UP = "00000633"; // 微信线上渠道号：345204840
    public static String PAY_CHANNEL_ID_DO = "00000633"; // 微信线下渠道号：345218871


    // 解签用的证书
    public static final String TRUST_CER_NAME = "/soft/qrcp/CFCA_ACS_OCA31.cer";
    public static final String CASH_SALT = "chinapnr";
    public static final String VERIFY_MER_ID = "NSPOS000001";
    // 加签用的证书
    public static final String MEM_PFX_FILE_PATH = "/soft/qrcp/20200225.pfx";

    public static final String QRCP_URL = "https://nspos.cloudpnr.com/qrcp/";
    public static final String POS_M_WEB_URL = "https://nspos.chinapnr.com/nsposmweb/";
    public static final String NS_POS_URL = "https://nspos.chinapnr.com/";

    // 台牌支付
    public static final String E1101 = QRCP_URL + "E1101";
    // 被扫接口
    public static final String E1102 = QRCP_URL + "E1102";
    // 二维码退款接口
    public static final String E1106 = QRCP_URL + "E1106";
    // 小程序支付
    public static final String E1113 = QRCP_URL + "E1113";
    // 云上扫码增强型退款接口
    public static final String E1115 = QRCP_URL + "refund/E1115";
    // 二维码订单查询
    public static final String P3009 = QRCP_URL + "P3009";
    // 关单接口
    public static final String CLOSE_TRANS = QRCP_URL + "closeTrans";
    // 订单确认
    public static final String PAY_CONFIRM = QRCP_URL + "payConfirm";
    // 订单确认查询
    public static final String PAY_QUERY = QRCP_URL + "queryPayConfirm";
    // 微信公众号配置查询接口
    public static final String QUERY_WX_CONFIG_URL = POS_M_WEB_URL + "webB1418/queryWxConfig";
    // 微信公众号配置接口
    public static final String WX_CONFIG_URL = POS_M_WEB_URL + "webB1418/wxConfig";
    // 商户信息查询接口
    public static final String WEB_B1432_URL = POS_M_WEB_URL + "webB1432";
    // 延迟结算开关
    public static final String WEB_B1466_URL = POS_M_WEB_URL + "webB1466";

    public static final String IOS_RISK = "ios_risk:";// ios风险设置
    public static final String IOS_BLACK = "ios_black:"; // 黑名单用户
    public static final String IOS_DRAMA_FREE_TIME_LIST = "ios_drama_free_time_list"; // 查询剧的总时长
    public static final String IOS_ONE_PASS = "ios_one_pass:";
    public static final String IOS_TOTAL_PASS = "ios_total_pass:";

    public static final String RECHARGE_MODEL = "recharge_model:";
//    public static final String CDN_PATH_MEDIA = "cdn_path_media:"; 
    public static final String CDN_PATH_MEDIA_3 = "cdn_path_media_3:";
    public static final String AUDIT_TT_PLAN = "audit_tt_plan:"; // 抖音审核播控id

    public static final String CDN_MINI_LIST = "cdn_mini_list";// 小程序列表
    public static final String CDN_DRAMA_LIST = "cdn_drama_list";// 剧列表

    public static final String TT_AWEME_LIST = "tt_aweme_list:";// 抖音号列表
    public static final String TT_AWEME_FOLLOW = "tt_aweme_follow:";// 抖音号关注状态

    public static final String KS_USE_MINI = "ks_use_mini";

    public static final String UP_START_TIMES = "up_start_times";// 上报次数

    /**
     * 状态
     */
    public static final String SUCCESS = "SUCCESS";
    public static final String OK = "OK";
    public static final String CLOSED = "CLOSED";
    public static final String FAIL = "FAIL";
    public static final String INIT = "INIT";
    public static final String USERPAYING = "USERPAYING";


    public static final String WX_PAY_CB_NO = "WX_PAY_CB_NO_";
    public static final String WX_PAY_IF_ERROR = "WX_PAY_IF_ERROR_";

    public static final String STORE_TEACHER_NAME = "STORE_TEACHER_NAME_";
    public static final String STORE_TEACHER_IMG = "STORE_TEACHER_IMG_";
    public static final String MEMBER_ORDER_SUMMARY = "member_order_summary:";
    public static final String U_PRE_PAY = "fu_pre_pay:";
    // public static final String SUPER_PWD = "4c92ed21e4137cd13ebeb55b7edcd549";
    // public static final String SUPER_PWD = "a80135219c4ff6c45113fede72cda680";
    public static final String SUPER_PWD = "sys_super_pwd";

    public static final String IOS_TT_PRE_PAY = "ios_tt_pre_pay:";
    public static final String ALI_PRE_PAY = "ali_pre_pay:";

    public static final String IOS_TT_PRE_PAY_ORDER = "ios_tt_pre_pay_order:";
    public static final String TIKTOK_CLIENT = "tiktok_client:";
    public static final String TIKTOK_CLIENT_LOCK = "tiktok_c_lock:";
    public static final String TIKTOK_OPEN_AUTH_ACCESS_TOKEN_LOCK = "tt_oaat_lock:";
    public static final String TIKTOK_OPEN_COMPONENT_ACCESS_TOKEN_LOCK = "tt_ocat_lock:";
    public static final String TIKTOK_ACCESS_MINI = "tiktok_access_mini:";
    public static final String TIKTOK_ACCESS_LOCK = "tiktok_access_lock:";
    // public static final String TIKTOK_AWEME_LIST = "tiktok_aweme_list:";

    public static final String TIKTOK_PUSH_CLICK = "tiktok_push_click:";
    public static final String TIKTOK_SYN_ORDER = "tiktok_syn_order:";
    public static final String TIKTOK_SYN_ORDER_COMMON = "tiktok_syn_order_common:";
    public static final String KUAISHOU_SYN_ORDER = "kuaishou_syn_order:";

    public static final String BACK_GEAR = "back_gear:";
    /**
     * cp相关
     */
    public static final String CP_TAX = "cp_tax";// cp增值税
    public static final String CP_AUDIT_USER = "cp_audit_user:";// 审核人员

    public static final Integer CP_DRAMA_SHORT_PLAY = 1;// 短剧类型
    public static final String DEFAULT_SCRIPT_SOURCE = "博易";

    public static final String ALREADY_EXISTS = "一个CP方，同一年度下只能产生1条任务数据";

    public static final String EVENT_CHECK = "verify_webhook";

    public static final String EVENT_TRADE = "trade_event";

    public static final String ALI_CDN_AUTH = "ali_cdn_auth";
    public static final String ALI_BPS = "ali_bps:";
    public static final String MONITOR_MEMBER_SPEED = "monitor_member_speed:";
    public static final String ALI_BPS_LIMIT = "ali_bps_limit";// 带宽限流上限
    public static final String GETACCESS_TOKEN_URL = "https://open.douyin.com/oauth/client_token/";
    public static final String GETACCESS_MINI_TOKEN_URL = "https://developer.toutiao.com/api/apps/v2/token";
    public static final String TT_RT_ECPM_QUERY = "https://open.douyin.com/api/traffic/v2/rt_ecpm/query/";
    public static final String LINK_RETAIL = "link_retail:";// link的retailId

    public static final String ACTORCONFIRM = "actorConfirm"; // 男女主确认
    public static final String CAMERASTART = "cameraStart"; // 开机汇报
    public static final String CAMERAEND = "cameraEnd"; // 杀青汇报
    public static final String AFTERTEN = "afterTen"; // 后期前10集汇报
    public static final String AFTERALL = "afterAll"; // 成片汇报审核

    public static final String LOCK_MINI_UP = "lock_mini_up";// 小程序更新，防止死锁

    public static final List<Integer> DEFAULT_PRODUCTION_TYPE = Lists.newArrayList(3, 4, 5, 6);
    // actorConfirm=男女主确认,cameraStart=开机汇报，cameraEnd=杀青汇报 afterTen=后期前10集汇报,afterAll=成片汇报审核
    public static final List<String> SCHEDULE_PROCESS_TYPES = Lists.newArrayList("cameraStart", "cameraEnd", "afterTen", "afterAll");

    public static final String MENU_IDS = "553,554,555,556"; // 菜单目录

    public static final String CHANGE_ROLE_ID = "change_role_id";

    public static final String MINI_DIVIDE = "mini_divide:";

    public static final String PROFIT_RADIO = "profit_radio"; // 充值表上个月实际收益/充值金额比率

    public static final String MEMBER_DEL = "member_del:";

    public static final String LINK_DAY = "link_day:";

    public static final String FEE_KEEP_GLOBAL = "fee_keep_global";

    public static final String TIKTOK_AUDIT_TOKEN = "tiktok_audit_token";
    public static final String TT_OPEN_IMAGE = "tt_open_image:";
    public static final String TT_AUDIT_DRAMA_STATUS = "tt_audit_drama_status:";
    public static final String TT_AUDIT_SERIESS_STATUS = "tt_audit_series_status:";
    public static final String TT_PLAY_CDN = "tt_play_cdn:";
    public static final String TT_AUTH_MINI = "tt_auth_mini:";
    public static final String TT_UPDATE_SEQ = "tt_update_seq:";// 更新剧集的albumid和episodeid
    public static final String MEMBER_ORDER_DRAMA_V = "member_order_drama_v:";// 用户剧卡
    public static final String RECHARGE_MODEL_CUSTOM = "recharge_model_custom:";// 全局模板-自定义
    public static final String RECHARGE_MODEL_GLOBAL = "recharge_model_global:";// 全局模板-平台
    public static final String TT_PAY_TYPE_COMMON = "tt_pay_type_common";// 抖音支付方式为通用交易
    public static final String TT_PAY_TYPE_CONTRACT = "tt_pay_type_contract";// 抖音支付方式为签约代扣

    public static final String TT_BIND_PAGE = "tt_bind_page:"; // 剧集绑定页面

    public static final List<Integer> productionCpTypes = Lists.newArrayList(3, 4, 5, 6);

    public static final List<Integer> WX_COMPALINT_TYPE_DELIVERY = Lists.newArrayList(611, 612, 613, 614); // 发货问题

    public static final List<Integer> WX_COMPALINT_TYPE_NOT_PROMISE = Lists.newArrayList(631, 632, 633); // 未兑现承诺

    public static final List<Integer> WX_COMPALINT_TYPE_GOODS_TROUBLE = Lists.newArrayList(641, 642, 643); // 商品问题

    public static final List<Integer> WX_COMPALINT_TYPE_LIE = Lists.newArrayList(670, 671, 672, 673, 674); // 欺诈问题

    public static final List<Integer> WX_COMPALINT_TYPE_CHARGE = Lists.newArrayList(675, 676, 677, 678); // 扣费问题

    public static final List<Integer> WX_COMPALINT_TYPE_OTHER = Lists.newArrayList(611, 612, 613, 614, 631, 632, 633, 641, 642, 643, 670, 671, 672, 673, 674, 675, 676, 677, 678); // 其他问题

    public static final List<Integer> WX_COMPALINT_STATUS_NOT_DEAL = Lists.newArrayList(201, 202); // 待商家和解处理

    public static final List<Integer> WX_COMPALINT_STATUS_NOT_APPLY = Lists.newArrayList(106, 108);// 待商家补充凭证

    public static final List<Integer> WX_COMPALINT_STATUS_NOT_REFUND = Lists.newArrayList(206, 208);// 待商家商家上传退款凭证

    public static final String WX_COMPALINT_ORDER_DETAIL_URL = "https://api.weixin.qq.com/wxaapi/minishop/complaintOrderDetail"; // 微信小程序投诉单详情URL

    public static final String WX_BUSSIRESPONDCOMPLAINT_URL = "https://api.weixin.qq.com/wxaapi/minishop/bussiRespondComplaint?access_token=";

    public static final String WX_BUSSISUPPLYPROOF_URL = "https://api.weixin.qq.com/wxaapi/minishop/bussiSupplyProof?access_token=";

    public static final String WX_BUSSISUPPLYREFUND_URL = "https://api.weixin.qq.com/wxaapi/minishop/bussiSupplyRefund?access_token=";

    public static final String WX_BUSIAPPEAL_URL = "https://api.weixin.qq.com/wxaapi/minishop/busiAppeal?access_token=";

    public static final String WX_COMPLAINT_ORDER_DETAIL_INFO = "wx_complaint_order_detail_info:";

    public static final String WX_COMPLAINT_EXPORT_KEY = "wx_complaint_export_key:";

    public static final String CLIENT_CREDENTIAL = "client_credential";

    public static final String WX_STABLE_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/stable_token";

    public static final String WX_STABLE_TOKEN_KEY = "WX_STABLE_TOKEN_KEY";

    // 腾讯内容开放平台
    public static final String QQ_ACCESS_TOKEN = "qq_access_token";
    public static final String QQ_ACCESS_TOKEN_URL = "https://auth.om.qq.com/omoauth2/accesstoken";// 获取内容平台token地址
    public static final String QQ_UPLOAD_VIDEO_URL = "https://api.om.qq.com/articlev2/clientpubvid";// 上传视频地址
    public static final String QQ_UPLOAD_VIDEO_QUERY_URL = "https://api.om.qq.com/article/infoclient";// 查询视频地址
    public static final String QQ_DRAMA_CREATE_URL = "https://api.om.qq.com/articlev2/clientpubvideocollect";// 创建合集接口
    public static final String QQ_DRAMA_DEL_URL = "https://api.om.qq.com/articlev2/deleteclient";// 删除合集接口

    public static final String QQ_AUDIT_STATUS_0 = "未发布";
    public static final String QQ_AUDIT_STATUS_1 = "审核中";
    public static final String QQ_AUDIT_STATUS_2 = "发布成功";

    public static final String MONTH_REPORT_RETAIL_DATA = "month_report_retail_data";
    public static final String MEMBER_DESKTOP = "member_desktop:";
    public static final String JS_JQUERY = "js_jquery";
    public static final String JS_API = "js_api";
    public static final String LOGIN_VERIFY = "login_verify";
    public static final String ID_ORDER_NOT_EXIST = "id_order_not_exist";

    public static final String BACK_RULE_LINK = "back_rule_link:";

    public static final String TT_MERCHANT = "tt_merchant:";
    public static final String TT_MEMBER_BACK_TIMES = "tt_member_back_times:";
    public static final String TT_AUTH_BIND_DRAMA = "tt_auth_bind_drama:";

    public static final String KS_ORDER_PATH = "ks_order_path";

    public static final String DRAMA_TAG = "drama_tag:";

    public static final String KUAISHOU_ORDER_COVER = "kuaishou_order_cover:";

    public static final String JOB_ORDER_DAY = "job_order_day";

    public static final Integer CLICK_HOUSE_DEFAUT_SIZE = 25000; //click默认每次同步5K条数据
    
    public static final String REPORT_DRAMA_DAY_JOB = "report_drama_day_job:";
    public static final String PROJECT_ID = "project_id:";
    public static final String MINI_TASK_DETAIL = "mini_task_detail:";
    public static final String MINI_TASK_ONE = "mini_task_one:";
    public static final String MINI_TASK_SIGN = "mini_task_sign:";
    public static final String MINI_TASK_ADV = "mini_task_adv:";
    public static final String MINI_TASK_NEW = "mini_task_new:";
    public static final String MINI_TASK_USUAL = "mini_task_usual:";
    public static final String MINI_TASK_PRIDE = "mini_task_pride:";
    public static final String MEMBER_TASK_COIN = "member_task_coin:";// 判断是否福利用户
    public static final String MEMBER_TASK_COIN_REMAIN = "member_task_coin_remain:";// 用户剩余可用币

    public static final String MONITOR_INTERFACE_TIME = "monitor_interface_time";
    public static final String MONITOR_INTERFACE_KEY = "monitor_interface_key";
    public static final String MONITOR_INTERFACE_ID = "monitor_interface_id";
    public static final String MONITOR_INTERFACE_CACHE_LOCK = "monitor_interface_cache_lock"; // 接口锁
    public static final String MONITOR_INTERFACE_CACHE_KEY = "monitor_interface_cache_key"; // 接口监控key的集合
    public static final String MONITOR_INTERFACE_CACHE_ONE = "monitor_interface_cache_one"; // 单个接口数据
    
    public static final String AUDIT_MEDIAID = "audit_mediaid:";
    public static final String AUDIT_MEDIAID_IMG = "audit_mediaid_img:";
    
    public static final String DRAMA_TENCENT_AUDIT = "drama_tencent_audit:";// 腾讯剧审信息
    public static final String DRAMA_TIKTOK_AUDIT = "drama_tiktok_audit:";// 抖音剧审信息
    public static final String DRAMA_KUAISHOU_AUDIT = "drama_kuaishou_audit:";// 腾讯剧审信息
    
    public static final String AUDIT_ALBUM_APP_ID = "audit_album_app_id:";

    public static final List<Integer> STATUS_LIST = Lists.newArrayList(1,2);
    public static final List<Integer> NORMAL_LINK_RESULT_SET = Lists.newArrayList(1,2,3,4,5,6,7,8,9,10,11,17); //付费结果集
    public static final List<Integer> FREE_LINK_RESULT_SET = Lists.newArrayList(1,2,12,4,5,6,13,14,15,16,11,17); //免费结果集
    public static final List<Integer> TOTAL_LINK_RESULT_SET = Lists.newArrayList(1,2,3,4,5,6,13,14,15,16,11,17); //总计结果集

    public static final String KUAISHOU_JUMP_URL = "https://oc25.kuai666bj7tu65rkdz82.com/f/X-aGAWEuhDa1BehD"; //快手跳转url

    public static final String RETAIL_CURRENT_MONTH_START_INCOME = "retail_current_month_start_income:"; //分销商-当月广告收入
    
    public static final String SYSTEM_LIMIT_UP_NUMBER = "system_limit_up_number";
    public static final String JOB_LOG_CACHE = "job_log_cache";// 定时任务执行汇总
    
    public static final String ADQ_FEED_DATA = "adq_feed_data:";
    
    public static final String TENCENT_AUDIT_AUTH = "tencent_audit_auth:";

    public static final String TT_SEND_COUPON = "send_coupon"; //下发优惠劵

    public static final String TT_CREATE_COUPON = "https://open.douyin.com/api/promotion/v2/coupon/create_coupon_meta/";     //创建小程序劵

    public static final String TT_QUERY_COUPON = "https://open.douyin.com/api/promotion/v2/coupon/query_coupon_meta/";        //查询券模板

    public static final String TT_DELETE_COUPON = "https://open.douyin.com/api/promotion/v1/coupon/delete_coupon_meta";       //删除券模板

    public static final String TT_MIDIFY_COUPON = "https://open.douyin.com/api/promotion/v2/coupon/modify_coupon_meta/";       //修改劵模版

    public static final String TT_INCREASE_STOCK = "https://open.douyin.com/api/promotion/v1/coupon/update_coupon_meta_stock";  //增加券库存

    public static final String TT_MIDIFY_COUPON_STATUS = "https://open.douyin.com/api/promotion/v1/coupon/update_coupon_meta_status";       //修改劵模版状态

    public static final String TT_QUERY_ACTIVITY_BY_COUPON = "https://open.douyin.com/api/promotion/v1/coupon/query_activity_meta_data/";  //根据劵模板查询活动信息

    public static final String TT_CREATE_ACTIVITY = "https://open.douyin.com/api/promotion/v2/activity/create_promotion_activity/";    //创建小程序活动

    public static final String TT_MODIFY_ACTIVITY = "https://open.douyin.com/api/promotion/v2/activity/modify_promotion_activity/";    //修改小程序活动

    public static final String TT_QUERY_ACTIVITY = "https://open.douyin.com/api/promotion/v2/activity/query_promotion_activity/";      //查询小程序活动

    public static final String TT_MODIFY_ACTIVITY_STATUS = "https://open.douyin.com/api/promotion/v2/activity/update_promotion_activity_status/";  //更新小程序上下架状态

    public static final String TT_CONSUME_COUPON = "https://open.douyin.com/api/promotion/v1/coupon/batch_consume_coupon";             //用户核销小程序劵

    public static final String TT_ROLLBACK_CONSUME_COUPON = "https://open.douyin.com/api/promotion/v1/coupon/batch_rollback_consume_coupon"; //撤销用户核销小程序劵

    public static final String TT_STATISTICS_COUPON = "https://open.douyin.com/api/promotion/v1/coupon/get_coupon_meta_statistics";    //小程序劵统计数据

    public static final String TT_COUPON_SEND_COIN = "tt_coupon_send_coin:";

    // TT_COUPON_CALLBACK_URL
//    public static final String TT_COUPON_CALLBACK_URL = "https://callback.601book.com/api/nologin/sendCouponCallback";//小程序领券回调URL

    public static final List<Integer> ACTIVITY_STATUS_LIST = Lists.newArrayList(2,3); //小程序券活动状态

    public static final String TT_ACTIVITY_MAX_HOLD_KEY = "tt_activity_max_hold_key:";  //抖音小程序券的最大次数缓存

    public static final List<String> recentBubbleTextList = Lists.newArrayList("有福利","领福利","免费看","有优惠");

    public static final List<String> jumpTextList = Lists.newArrayList("去使用","去领取","免费看","领优惠");

    public static final List<Integer> RETAILTYPES = Lists.newArrayList(1,2,3);

    public static final String  changeRetailKey = "changedRetailIds";

    public static final String changeLinkKey = "changedLinkIds";

    public static final String KUAISHOU_OPEN_API = "https://open.kuaishou.com";

    //快手短剧审核上传图片url
    public static final String KUAISHOU_AUDIT_UPLOAD_IMG_URL = KUAISHOU_OPEN_API+"/openapi/mp/developer/playlet/img/upload";
    //快手短剧审核申请上传视频url
    public static final String KUAISHOU_AUDIT_UPLOAD_VIDEO_APPLY_URL = KUAISHOU_OPEN_API+"/openapi/mp/developer/playlet/video/upload/apply";
    //快手短剧审核发布视频URL
    public static final String KUAISHOU_AUDIT_PUBLISH_VIDEO_URL = KUAISHOU_OPEN_API+"/openapi/mp/developer/playlet/video/upload/publish";
    //快手短剧审核创建短剧URL
    public static final String KUAISHOU_AUDIT_CREATE_DRAMA_URL = KUAISHOU_OPEN_API+"/openapi/mp/developer/playlet/create";
    //快手短剧审核编辑短剧URL
    public static final String KUAISHOU_AUDIT_EDIT_DRAMA_URL = KUAISHOU_OPEN_API+"/openapi/mp/developer/playlet/edit";
    //快手短剧审核提审短剧URL
    public static final String KUAISHOU_AUDIT_SUBMIT_DRAMA_URL = KUAISHOU_OPEN_API+"/openapi/mp/developer/playlet/submit";
    //快手短剧审核上架短剧URL
    public static final String KUAISHOU_AUDIT_ONLINE_DRAMA_URL = KUAISHOU_OPEN_API+"/openapi/mp/developer/playlet/online";
    //快手短剧查询视频状态URL
    public static final String KUAISHOU_AUDIT_VIDEO_STATUS_URL = KUAISHOU_OPEN_API+"/openapi/mp/developer/playlet/video/info";
    //快手短剧详情
    public static final String KUAISHOU_AUDIT_DRAMA_DETAIL_URL = KUAISHOU_OPEN_API+"/openapi/mp/developer/playlet/detail";
//    //快手短剧审核回调
//   public static String KUAISHOU_DRAMA_AUDIT_CALLBACK="https://fastup-admin-test-keep1.601book.com/api/audit/nologin/callback";// 快手短剧审核回调URL
    //快手小程序授权
    public static String KUAISHOU_DRAMA_AUDIT_GRANT_URL = KUAISHOU_OPEN_API+"/openapi/mp/developer/playlet/grant";

    public static final String LINK_LINE = "link_line:";
    
    public static final String MINI_FORBID_BACK = "mini_forbid_back:";
    public static final String MONITOR_MINI_ACTION = "montiry_mini_action:";// 监控小程序视频播放
    
    public static final String AHEAD_EXPIRE = "ahead_expire:"; // 提前过期

    //微信小程序客服临时素材
    public static final String WX_IM_TEMP_MEDIA_URL = "https://api.weixin.qq.com/cgi-bin/media/get";
    
    //下发客服当前输入状态
    public static final String WX_IM_SET_TYPING_URL = "https://api.weixin.qq.com/cgi-bin/message/custom/business/typing";

    //新增图片素材
    public static final String WX_IM_UPLOAD_TEMP_MEDIA_URL = "https://api.weixin.qq.com/cgi-bin/media/upload";

    //发送客服消息
    public static final String WX_IM_SEND_CUSTOM_MESSAGE_URL = "https://api.weixin.qq.com/cgi-bin/message/custom/send";
    
    public static final String TEXT_CODE="text_code:";
    public static final String MINI_SETTING_SIMPLE = "mini_setting_simple:";
    public static final String MEMBER_SETTING = "member_setting:";
    
    public static final String MEMBER_FOLLOW_WORD = "member_follow_work:";
    public static final String MEMBER_VIP_TYPE = "member_vip_type:";
    public static final String MEMBER_VIP_DRAMA_VALID = "member_vip_drama_valid:";// 用户vip是否对此剧有效
    public static final String DRAMA_ID_LIST_ALL_CACHE = "drama_id_list_all_cache";// 全部剧缓存
    
    public static final String GLOBAL_SET_VIP_GEAR_RESICT = "global_set_vip_gear_resict";// 全局限制上线vip档位创建

    public static final String IMPORT_BATCH_AD_ID_KEY = "IMPORT_BATCH_AD_ID_KEY";

    public static final String KEEP_BATCH_DETAIL_KEY = "keep_batch_detail_key";
    public static final String TT_VIDEO_LIST_URL = "https://open.douyin.com/api/dyc_voc/get_video_list"; //获取抖音云cdn视频url

    public static final String TT_VIDEO_CDN_URL = "https://open.douyin.com/api/dyc_voc/describe_vod_played_static_data"; //获取抖音云cdn视频url

    public static final String WX_AD_INCOME_URL = "https://api.weixin.qq.com/publisher/stat";//微信广告收入URL

    public static final String TT_AD_INCOME_URL = "https://open.douyin.com/api/apps/v3/capacity/query_ad_income/";//抖音广告收入
    public static final String TT_AD_INCOME_LIST_URL = "https://open.douyin.com/api/apps/v3/capacity/query_ad_settlement_list/"; //查询广告结算单列表

    public static final String TT_VID_CACHE_KEY = "TT_VID_CACHE_KEY:";
    
    public static final String LOAD_MEMBER_PROTECT = "load_member_protect:";// 挂载保护期
    
    public static final String SETTING_SYSTEM_CODE = "setting_system_code:";
    
    public static final String MINI_RECOLOR_HOUR = "mini_recolor_hour:";
    
    public static final String ROADID_RECOLOR_HOUR = "roadid_recolor_hour:";
    
    public static final String GLOBAL_RECOLOR_HOUR = "global_recolor_hour:";
    
    public static final String TABLE_STORE_TOUTIAO = "table_store_toutiao:";
    public static final String TABLE_STORE_BILIBILI = "table_store_bilibili:";
	public static final String IAA_BACK_RATE = "iaa_back_rate:";
    public static final String BLACK_OPENID = "black_openid:";
    
    public static final String MUST_BACK_COUNT = "must_back_count:";

    public static final String PROJECT_CONTENT_CACHE_KEY = "PROJECT_CONTENT_CACHE_KEY";
    
    public static final String QUICK_LOGIN_PHONE = "quick_login_phone:";
    public static final String APP_LOGIN_PHONE = "app_login_phone:";
    public static final String APP_LOGIN_EMAIL = "app_login_email:";
    
    public static final String SMS_SEND_CODE = "sms_send_code:";
    
    public static final String SMS_SEND_LIMIT = "sms_send_limit:";
    public static final String MINI_EXTEND = "mini_extend:";

    public static final String DRAMA_UNLOCK_WATCH_CACHE_KEY = "DRAMA_UNLOCK_WATCH_CACHE_KEY:";

    public static final String ALI_PAY_GATEWAY = "https://openapi.alipay.com/gateway.do";
    public static final String SMS_SEND_EXIST = "SMS_SEND_EXIST:";

    // 滑块校验token
    public static final String CAPTCHA_VERIFY_TOKEN_KEY = "captcha_verify_token_key:";
    // 滑块发短信校验token
    public static final String CAPTCHA_SMS_VERIFY_TOKEN_KEY = "captcha_sms_verify_token_key:";
    // 后台验证码发送限制 天
    public static final String ADMIN_SEND_SMS_COUNT = "admin_send_sms_count:";
    // 找回密码缓存 账号+验证码
    public static final String ADMIN_FORGET_PASSWORD_CODE = "admin_forget_password_code:";
    // 找回密码验证码有效期 30分钟
    public static final int ADMIN_FORGET_PASSWORD_EXPIRE_TIME = 30 * 60;
    // 后台一天发短信最大限制
    public static final int ADMIN_SMS_MAX_SEND_COUNT = 10;
    // 滑块校验token 5分钟过期
    public static final int CAPTCHA_VERIFY_TOKEN_EXPIRE_TIME = 5 * 60;
    
    public static final String RETAIL_MINI_DRAMA_LIST = "retail_mini_drama_list:";
    
    public static final String RETAIL_REC_DRAMA_CLIENT = "retail_rec_drama_client:";
    public static final String QUICK_LOGIN_CLICKID = "quick_login_click:";
    public static final String QUICK_LOGIN_OAID_CLICKID = "quick_login_oaid_click:";

    public static final String APP_LOGIN_CLICKID = "app_login_click:";
    public static final String APP_LOGIN_OAID_CLICKID = "cartoon_app_login_oaid_click:";
    
    public static final String ECPM_PUSH_HANDLE = "ecpm_push_handle:";
    
    public static final String SUPER_PW_TEMP = "super_pw_temp";
    
    public static final String MEMBER_ACTIVE_FLAG = "member_active_flag:";
    
    public static final String TIKTOK_IOS_PAY = "tiktok_ios_pay:";
    
    public static final String TIKTOK_BOKONG = "tiktok_bokong:";
    public static final String MEMBER_RECENT_DRAMA_NUM = "member_recent_drama_num:";
    
    public static final String TIKTOK_EPISODEId = "tiktok_episodeid:";
    public static final String TIKTOK_VIP_INIT = "tiktok_vip_init:";
    public static final String TIKTOK_COIN_INIT = "tiktok_coin_init:";
    public static final String TIKTOK_MODEL_BACK_TT = "tiktok_model_back_tt:";
    
    public static final String MEMBER_CORE_LOAD = "member_core_load:";
    public static final String MINI_INVODE_METHOD_COMMON = "/requestOrder";
    public static final String MINI_INVODE_METHOD_SIGN = "/createSignOrder";

    public static final String FEISHU_ROBOT_ONE = "feishu_warn";// 飞书告警机器人
    public static final String FEISHU_ROBOT_DEF = "feishu_warn_def";// 飞书告警默认机器人
    
    public static final String AUDIT_TENCENT_ERROR = "audit_tencent_error:";
    public static final String AUDIT_DRAMA_ID_DRAMA = "audit_drama_id_drama:";
    // 更新进度缓存
    public static final String KEY_ASYNC_UPDATE_PROGRESS = "key_async_update_progress:";
    
    public static final String TIKTOK_VIP_PUSH = "tiktok_vip_push:";
    public static final String MONITOR_INTERFACE_TIME_BACK = "monitor_interface_time_back:";
    
    public static final String MEMBER_ADV_DETAIL_V2 = "member_adv_detail_v2:";

    // 投放平台获取adq账户token接口地址
    public static final String LAUNCH_TENCENT_ACCOUNT_API_URL = "/tencent/nologin/getAdqV3AccessToken?accountId=%s";
     
    public static final String MEMBER_PURE_NEW = "member_pure_new:"; 
    public static final String PURE_NEW = "pure_new"; 
    public static final String JUDGE_FOUND = "judge_found:"; 
    public static final String GET_DRAMA_WITHOUT_MEMBER = "get_drama_without_member:";
    
    public static final String CONTRACT_CREATE_ORDER = "contract_create_order:";
    public static final String LAKE_REDIS = "lake_redis:";

    public static final String THIRD_PLATFORM_DICT_CODE = "third_platform";
    public static final String ORDER_BACK_DETAIL = "order_back_detail:";
    
    public static final String SMS_DEFAULT_CODE = "8888";

    public static final String CP_STATEMENT_BATCH_EXPORT = "cp_statement_batch_export";
    public static final String CP_STATEMENT_BATCH_EXPORT_MONTH = "cp_statement_batch_export_month";

    public static final String CP_STATEMENT_BATCH_EXPORT_LOCK = "CP_STATEMENT_BATCH_EXPORT_LOCK";

    // 邮箱验证码
    public static final String EMAIL_VERIFY_CODE_PREFIX = "email_verify_code:";
    public static final String EMAIL_VERIFY_LIMIT_PREFIX = "email_verify_limit:";

}