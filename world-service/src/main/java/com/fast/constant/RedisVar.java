package com.fast.constant;

import com.fast.utils.StrUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 常量
 *
 * <AUTHOR>
 */
@Component
public class RedisVar {

    public static String REDIS_HOST;
    public static String REDIS_LAUNCH_HOST;
    public static String REDIS_PWD;
    public static int REDIS_PORT;
    public static int REDIS_DB;
    public static int REDIS_TOKEN_DB; // redis的token
    public static int REDIS_MIN_IDLE; // redis最小空闲连接
    public static int REDIS_MAX_IDLE; // redis最大空闲连接
    public static int REDIS_MAX_ACTIVE; // redis最大连接数
    public static final int TIMEOUT = 30_000;// (毫秒)

    @Value("${spring.redis.host}")
    public void setRedisHost(String redisHost) {
        REDIS_HOST = redisHost;
    }

    @Value("${spring.redis.host-lock}")
    public void setRedisLockHost(String redisLockHost) {
        REDIS_LAUNCH_HOST = redisLockHost;
    }

    @Value("${spring.redis.password}")
    public void setRedisPwd(String redisPwd) {
        if (StrUtil.isBlank(redisPwd)) redisPwd = null;
        REDIS_PWD = redisPwd;
    }

    @Value("${spring.redis.port}")
    public void setRedisPort(int redisPort) {
        REDIS_PORT = redisPort;
    }

    @Value("${spring.redis.database}")
    public void setRedisDb(int redisDb) {
        REDIS_DB = redisDb;
    }

    @Value("${spring.redis.token-db}")
    public void setRedisTokenDb(int redisTokenDb) {
        REDIS_TOKEN_DB = redisTokenDb;
    }

    @Value("${spring.redis.jedis.pool.min-idle}")
    public void setRedisMinIdle(int redisMinIdle) {
        REDIS_MIN_IDLE = redisMinIdle;
    }

    @Value("${spring.redis.jedis.pool.max-idle}")
    public void setRedisMaxIdle(int redisMaxIdle) {
        REDIS_MAX_IDLE = redisMaxIdle;
    }

    @Value("${spring.redis.jedis.pool.max-active}")
    public void setRedisMaxActive(int redisMaxActive) {
        REDIS_MAX_ACTIVE = redisMaxActive;
    }

}
