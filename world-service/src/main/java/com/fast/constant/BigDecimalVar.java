package com.fast.constant;

import java.math.BigDecimal;

/**
 * 常量
 *
 * <AUTHOR>
 */
public class BigDecimalVar {

    public static final BigDecimal BD_100 = new BigDecimal(100);
    public static final BigDecimal BD_1000 = new BigDecimal(1000);
    public static final BigDecimal BD_10W = new BigDecimal(100_000);
    public static final BigDecimal BD_0_0065 = new BigDecimal("0.0065");
    public static final BigDecimal BD_0_01 = new BigDecimal("0.01");
    public static final BigDecimal BD_9999_99 = new BigDecimal("9999.99");
    public static final BigDecimal BD_999_99 = new BigDecimal("999.99");
    public static final BigDecimal BD_0_1 = new BigDecimal("0.1");
    public static final BigDecimal BD_0_99 = new BigDecimal("0.99");
    public static final BigDecimal BD_0_98 = new BigDecimal("0.98");
    public static final BigDecimal BD_0_85 = new BigDecimal("0.85");
    public static final BigDecimal BD_0_8 = new BigDecimal("0.8");
    public static final BigDecimal BD_20 = new BigDecimal("20");
    public static final BigDecimal BD_0_6 = new BigDecimal("0.6");
    public static final BigDecimal BD_2 = new BigDecimal("2");
    public static final BigDecimal BD_10 = new BigDecimal("10");
    public static final BigDecimal BD_0_735 = new BigDecimal("0.735");
    public static final BigDecimal BD_0_7875 = new BigDecimal("0.7875");

}
