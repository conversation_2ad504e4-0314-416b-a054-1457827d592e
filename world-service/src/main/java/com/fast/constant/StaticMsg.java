package com.fast.constant;

public class StaticMsg {

    public static final String GET_OUT = "please get out here";
    public static final String DATA_ERROR = "数据更新失败";
    public static final String DATA_NULL = "空数据";
    public static final String MSG_ERROR_10 = "登录信息错误，请重试";
    public static final String MSG_ERROR_11 = "第三方授权失败";
    public static final String MSG_ERROR_12 = "验证码输入错误";
    public static final String MSG_ERROR_13 = "注册账户存在，请重新注册";
    public static final String MSG_ERROR_14 = "登录信息过期，请重新登录";
    public static final String MSG_ERROR_15 = "用户名不能为空";
    public static final String MSG_ERROR_16 = "密码不能为空";
    public static final String MSG_ERROR_17 = "用户名或密码错误";
    public static final String MSG_ERROR_18 = "您的用户没有启用，请联系管理员";
    public static final String MSG_ERROR_19 = "用户没有购买对应课程或者可用次数不足";
    public static final String MSG_ERROR_20 = "您还不是本店用户，请先联系商家领卡成为用户后才可以购买";
    public static final String MSG_ERROR_21 = "请求参数不完整，请联系工作人员";
    public static final String MSG_ERROR_22 = "用户信息不正确,非法操作";
    public static final String MSG_ERROR_23 = "已消费过的不能再取消";
    public static final String MSG_ERROR_25 = "您取消预约的课程已经被取消啦";
    public static final String MSG_ERROR_26 = "预约失败，预约人数貌似满啦";
    public static final String MSG_ERROR_27 = "当前课程不能取消预约，如有问题请联系商家";
    public static final String MSG_ERROR_28 = "还未到开始预约时间哦";
    public static final String MSG_ERROR_29 = "您今日在当前模块的下载次数已达上限";
    public static final String MSG_ERROR_30 = "您所导出的数据为空!";
    public static final String MSG_ERROR_31 = "不能重复预约!";
    public static final String MSG_ERROR_32 = "没有预约该课程，不能取消!";
    public static final String MSG_ERROR_33 = "当前课程已开课不能请假，请联系老师";
    public static final String MSG_ERROR_34 = "课节信息已变更，请刷新后重试！";
    public static final String MSG_ERROR_35 = "用户资产已失效，不能取消";
    public static final String MSG_ERROR_36 = "已过预约取消截止时间";

    public static final String MSG_ERROR_41 = "您预约的课程有时间上的冲突";
    public static final String MSG_ERROR_44 = "没有购买对应课程";
    public static final String MSG_ERROR_45 = "可用次数不足";
    public static final String MSG_ERROR_46 = "已请假过的课程不能再取消";
    public static final String MSG_ERROR_48 = "已签到过的课程不能再取消";
    public static final String MSG_ERROR_47 = "当前预约的课程时间冲突，无法预约。";

    public static final String MSG_ERROR_56 = "下发随机密码失败，请稍后再试";
    public static final String MSG_ERROR_57 = "验证码已发，请不要重试";
    public static final String MSG_ERROR_59 = "鉴权失败，请确认版本后再试";
    public static final String MSG_ERROR_88 = "系统忙，请稍后再试";
    public static final String MSG_ERROR_89 = "应用服务器不可用，请稍后再试";
    public static final String MSG_ERROR_90 = "IMEI非法";
    public static final String MSG_ERROR_99 = "重试次数超过限制，请稍后再试";

    public static final String MSG_ERROR_100 = "新增失败";
    public static final String MSG_ERROR_101 = "删除失败";
    public static final String MSG_ERROR_102 = "修改失败";
    public static final String MSG_ERROR_103 = "请求token不合法";
    public static final String MSG_ERROR_104 = "暂无权限访问";
    public static final String MSG_ERROR_105 = "签名不合法";
    public static final String MSG_ERROR_106 = "每页显示数不合法";
    public static final String MSG_ERROR_107 = "刷脸签到时间间隔不能小于20秒";
    public static final String MSG_ERROR_108 = "刷脸签到时间间隔不能大于7200秒";

    public final static String MSG_ERROR_110 = "订单号orderBatchId不能为空";
    public final static String MSG_ERROR_111 = "支付码authCode不能为空";
    public final static String MSG_ERROR_112 = "请输入正确付款码";
    public final static String MSG_ERROR_113 = "支付码authCode必须是纯数字";
    public final static String MSG_ERROR_114 = "该用户已经被禁用或者不存在";
    public final static String MSG_ERROR_115 = "已达最大预约次数，本次无法预约";
    public static final String MSG_ERROR_116 = "您当前所使用的下载次数已达上限";

    public static final String MSG_PAY_ERROR_21 = "请求参数不完整，请联系工作人员";
    public final static String MSG_PAY_ERROR_101 = "机构ID不能为空";
    public final static String MSG_PAY_ERROR_102 = "员工ID不能为空";
    public final static String MSG_PAY_ERROR_103 = "商户号不能为空";
    public final static String MSG_PAY_ERROR_104 = "订单号不能为空";
    public final static String MSG_PAY_ERROR_105 = "支付码不能为空";
    public final static String MSG_PAY_ERROR_106 = "请输入正确的付款码";
    public final static String MSG_PAY_ERROR_107 = "支付码必须是纯数字";
    public final static String MSG_PAY_ERROR_108 = "支付金额不能为空";
    public final static String MSG_PAY_ERROR_109 = "支付金额不合法";
    public final static String MSG_PAY_ERROR_110 = "备注长度不能大于20字符";
    public final static String MSG_PAY_ERROR_111 = "二维码信息不存在";
    public final static String MSG_PAY_ERROR_112 = "二维码id不能为空";
    public final static String MSG_PAY_ERROR_113 = "支付方式不合法";
    public final static String MSG_PAY_ERROR_114 = "支付宝buyerId不能为空";
    public final static String MSG_PAY_ERROR_115 = "微信openId不能为空";
    public final static String MSG_PAY_ERROR_116 = "订单号不能为空";

    public final static String MSG_PAY_ERROR_117 = "订单Id不能为空";
    public final static String MSG_PAY_ERROR_118 = "订单无效";
    public final static String MSG_PAY_ERROR_119 = "订单已支付";
    public final static String MSG_PAY_ERROR_120 = "订单已取消";
    public final static String MSG_PAY_ERROR_121 = "订单已作废";
    public final static String MSG_PAY_ERROR_145 = "订单已确认";
    public final static String MSG_PAY_ERROR_146 = "订单无需确认";
    public final static String MSG_PAY_ERROR_147 = "订单支付失败";
    public final static String MSG_PAY_ERROR_148 = "订单处理中";
    public final static String MSG_PAY_ERROR_122 = "订单无需支付";
    public final static String MSG_PAY_ERROR_123 = "订单支付失败";
    public final static String MSG_PAY_ERROR_124 = "该机构支付账户异常";

    public final static String MSG_PAY_ERROR_125 = "该学员已经被禁用或者不存在";
    public final static String MSG_PAY_ERROR_126 = "当前订单号短时间内被重复支付,请稍后再试";
    public final static String MSG_PAY_ERROR_127 = "支付码不能重复使用,请变更支付码后再试";
    public final static String MSG_PAY_ERROR_128 = "支付订单中有已退款的订单,不能支付";
    public final static String MSG_PAY_ERROR_129 = "获取openId失败，请联系管理员";

    public final static String MSG_PAY_ERROR_130 = "支付通道错误，请联系管理员";
    public final static String MSG_PAY_ERROR_131 = "写入数据失败";
    public final static String MSG_PAY_ERROR_132 = "验证签名失败";
    public final static String MSG_PAY_ERROR_133 = "被扫接口下单失败";
    public final static String MSG_PAY_ERROR_134 = "交易异步通知失败";
    public final static String MSG_PAY_ERROR_135 = "回调支付结果给商家失败";
    public final static String MSG_PAY_ERROR_136 = "交易查询失败";
    public final static String MSG_PAY_ERROR_137 = "更新购买产品状态失败";
    public final static String MSG_PAY_ERROR_139 = "微信支付查询失败";
    public final static String MSG_PAY_ERROR_141 = "sleep error";
    public final static String MSG_PAY_ERROR_138 = "uPay callback canceled, cause notifyUrl is null, data: {}";
    public final static String MSG_PAY_ERROR_140 = "uPay notifyUrl fail,notifyUrl:{}; resultMap: {}";
    public final static String MSG_PAY_ERROR_142 = "uPay notifyUrl fail, result: {}";

    public final static String MSG_PAY_ERROR_143 = "不允许关闭一分钟以内的订单";
    public final static String MSG_PAY_ERROR_144 = "账户余额不足，请选择其他退款方式";
    public final static String MSG_PAY_ERROR_149 = "非汇付订单，无需确认";
    public final static String MSG_PAY_ERROR_150 = "小程序支付时appId不能为空";

    public final static String MSG_PAY_ERROR_160 = "抖音支付查询失败";

    public final static String MSG_PAY_ERROR_161 = "只支持抖音小程序支付";
    public final static String MSG_PAY_ERROR_162 = "支付平台参数必传";
    public final static String MSG_PAY_ERROR_163 = "快手支付查询失败";

    public static final String MSG_NO_DATA = "没有数据";
    public static final String SYSTEM_ERROR = "系统错误";
    public static final String SYSTEM_EXCEPTION = "系统异常";
    public static final String CONN_TIME_OUT = "请求超时，请稍后再试";
    public static final String READ_TIME_OUT = "读取数据超时";
    public static final String REQUEST_FAIL = "请求失败";
    public static final String PIC_SIZE_TOO_LARGE = "单张图片大小不能超过1MB";
    public static final String PIC_FORMAT_FAIL = "图片格式不支持";
    public static final String PARAM_NOT_COMPLETE = "请求参数不完整";
    public static final String DONE_SUCCESS = "处理成功";
    public static final String PAGE_TIME_OUT = "页面已过期，请刷新重试！";
    public static final String SYSTEM_IS_BUSY = "系统繁忙,请稍后再试";
    public static final String FORBIDDEN_ERROR = "非法操作";
    public static final String ANNOUNCE_DEL = "当前通知已被删除";
    public static final String DEL_SUCCESS = "删除成功";
    public static final String IP_NOT_ALLOW = "ip不合法,拒绝访问";
    public static final String PARAM_NOT_ALLOW = "参数不合法";
    public static final String OPER_NOT_ALLOW = "非法操作";


    public static final String SELECT_DATE_VALID = "所选日期间隔大于90天";
    public static final String SELECT_MONTH_VALID = "所选月份间隔大于12月";
    public static final String IN_EXPORT = "正在导出，请稍后前往下载中心下载";
    public static final String NOTHING_EXPORT = "暂无数据可供导出";

    /** 通用名词 **/
    public static final String MSG_0001 = "短剧用户管理营销系统";
    public static final String MSG_XK = "消课";


    public static final String NO_COMPLETA = "经查询该用户还有剩余的资产未退款，请再次操作退款结业";
    public static final String REFUND_BUT_NO_COMPLETA = "所选择的资产已退款，经查询该用户还有剩余的资产未退款，请再次操作退款结业";
    public static final String REFUND_AND_COMPLETA = "该用户已成功结业，转为历史用户";
    public static final String REFUND_BUT_COMPLETA_FAILED = "该用户所有资产已退款，但结业失败，请再次操作结业";
    public static final String CYCLE_HAS_NOASSETSTUNOCOMPLATION = "该班级里还存在剩余天数和剩余次数为0的用户未转为历史用户，请确认后再次进行操作";
    public static final String COMPLETA_FAILED = "结业失败，请重试";

    public static final String CYCLE_COMPLATION_FAILED = "班级结业失败，请重试";
    public static final String CYCLE_COURSE_REMOVE_FAILED = "未签到的班级课节移除失败，请重试";
    public static final String CYCLE_COMPLATION_SUCCESS_AND_STU  = "班级已结业成功，并将已选的用户转为历史用户";
    public static final String CYCLE_COMPLATION_SUCCESS  = "班级已结业成功";



    /**
     * CRM相关通知
     */
    public static final String CRM_MSG_CREATE_STU = "创建用户";
    public static final String CRM_MSG_DISTRIBUTION_FOLLOWER = "分配跟进人为#followerNames#";
    public static final String CRM_MSG_UNFORMAL_TO_FORMAL = "意向用户转为在读用户";
    public static final String CRM_MSG_UNFORMAL_TO_FORMAL_BUY = "意向用户购买#package#，转为在读用户";
    public static final String CRM_MSG_UNFORMAL_TO_FORMAL_IMPORT = "意向用户导入#package#，转为在读用户";
    public static final String CRM_MSG_UNFORMAL_TO_FORMAL_BUY_WX = "意向用户微信商城购买#package#，转为在读用户";

    public static final String CRM_MSG_FORMAL_COMPLETION = "在读用户结业";
    public static final String CRM_MSG_HIS_TO_FORMAL = "历史用户转为在读用户";
    public static final String CRM_MSG_HIS_TO_FORMAL_BUY = "历史用户购买#package#，转为在读用户";
    public static final String CRM_MSG_HIS_TO_FORMAL_IMPORT = "历史用户导入#package#，转为在读用户";
    public static final String CRM_MSG_HIS_TO_FORMAL_BUY_WX = "历史用户微信商城购买#package#，转为在读用户";

    public static final String CRM_MSG_PASS_ARRANGE_LISTEN = "通过安排预约试听";
    public static final String CRM_MSG_COURSEIN_ARRANGE_LISTEN = "课节内试听预约";
    public static final String CRM_MSG_STUDENT_ARRANGE = "用户自主预约";


    //public static final String CRM_MSG_TITLEKEY_UNFORMAL_ARRANGE = "意向用户预约试听课";
    //public static final String CRM_MSG_TITLEKEY_HIS_ARRANGE = "历史用户预约试听课";
    public static final String CRM_MSG_TITLEKEY_ARRANGE = "预约试听课";
    //public static final String CRM_MSG_TITLEKEY_UNFORMAL_CANCEL_ARRANGE = "意向用户取消预约试听课";
    //public static final String CRM_MSG_TITLEKEY_HIS_CANCEL_ARRANGE = "历史用户取消预约试听课";
    public static final String CRM_MSG_TITLEKEY_CANCEL_ARRANGE = "取消预约试听课";
    public static final String CRM_MSG_TITLEKEY_LISTEN_SIGN = "试听课签到";

    public static final String CYCLE_ALREADY_COMPLETION = "班级已结业,无法进行相关操作";

}
