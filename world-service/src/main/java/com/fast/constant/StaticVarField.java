package com.fast.constant;

/**
 * 支付参数常量
 *
 * <AUTHOR>
 */
public class StaticVarField {

    public static final String F_APPID = "appid";
    public static final String F_APP_ID_TT = "app_id";
    public static final String F_MINI_ID = "mini_id";
    public static final String F_MCH_ID = "mch_id";
    public static final String F_SUB_APPID = "sub_appid";
    public static final String F_SUB_MCH_ID = "sub_mch_id";
    public static final String F_DEVICE_INFO = "device_info";
    public static final String F_NONCE_STR = "nonce_str";
    public static final String F_BODY = "body";
    public static final String F_SUBJECT = "subject";
    public static final String F_DETAIL = "detail";
    public static final String F_TYPE = "type";
    public static final String F_EXPIRE_TIME = "expire_time";
    public static final String F_ATTACH = "attach";
    public static final String F_OPEN_ID = "open_id";
    public static final String F_OUT_TRADE_NO = "out_trade_no";
    public static final String F_OUT_ORDER_NO = "out_order_no";
    public static final String F_OUT_REFUND_NO = "out_refund_no";
    public static final String F_FEE_TYPE = "fee_type";
    public static final String F_TOTAL_FEE = "total_fee";
    public static final String F_TOTAL_AMOUNT = "total_amount";
    public static final String F_SPBILL_CREATE_IP = "spbill_create_ip";
    public static final String F_TIME_START = "time_start";
    public static final String F_TIME_EXPIRE = "time_expire";
    public static final String F_VALID_TIME = "valid_time";
    public static final String F_THIRD_PARTY_ID = "thirdparty_id";
    public static final String F_GOODS_TAG = "goods_tag";
    public static final String F_NOTIFY_URL = "notify_url";
    public static final String F_PROVIDER = "provider";
    public static final String F_PROVIDER_CT = "provider_channel_type";
    public static final String F_TRADE_TYPE = "trade_type";
    public static final String F_LIMIT_PAY = "limit_pay";
    public static final String F_OPENID = "openid";
    public static final String F_SUB_OPENID = "sub_openid";
    public static final String F_RECEIPT = "receipt";
    public static final String F_SCENE_INFO = "scene_info";
    public static final String F_SIGN = "sign";
    public static final String F_MSG_PAGE = "msg_page";
    public static final String F_MSG_SIGNATURE = "msg_signature";
    public static final String F_SIGN_TYPE = "sign_type";
    public static final String F_REFUND_FEE = "refund_fee";
    public static final String F_OP_USER_ID = "op_user_id";
    public static final String F_REFUND_FEE_TYPE = "refund_fee_type";
    public static final String F_REFUND_DESC = "refund_desc";
    public static final String F_REFUND_ACCOUNT = "refund_account";
    public static final String F_OFFSET = "offset";
    public static final String F_RESULT = "my_result";
    public static final String F_STORE_UID = "store_uid";

    public static final String F_RETURN_CODE = "return_code";
    public static final String F_RETURN_MSG = "return_msg";
    public static final String F_RESULT_CODE = "result_code";
    public static final String F_TRADE_STATE = "trade_state";
    public static final String F_REFUND_STATUS = "refund_status";
    public static final String F_REFUND_STATUS_0 = "refund_status_0";
    public static final String F_SUCCESS_TIME = "success_time";
    public static final String F_ERR_CODE = "err_code";
    public static final String F_ERR_CODE_DES = "err_code_des";
    public static final String F_MESSAGE = "message";
    public static final String F_TRANSACTION_ID = "transaction_id";
    public static final String F_REFUND_ID = "refund_id";
    public static final String F_TIME_END = "time_end";
    public static final String F_COUPON_FEE = "coupon_fee";
    public static final String F_APP_ID = "appId";
    public static final String F_TIME_STAMP = "timeStamp";
    public static final String F_NONCE_STR2 = "nonceStr";
    public static final String F_PACKAGE = "package";
    public static final String F_PREPAY_ID = "prepay_id";
    public static final String F_PREPAY_ID2 = "prepay_id=";
    public static final String F_SIGN_TYPE2 = "signType";
    public static final String F_PAY_SIGN = "paySign";
    public static final String F_OUT_TRADE_NO2 = "outTradeNo";
    public static final String F_TRANSACTION_ID2 = "transactionId";
    public static final String F_TOTAL_FEE2 = "totalFee";
    public static final String F_REQ_INFO = "req_info";


    public static final String FH_APP_ID = "appId";
    public static final String FH_CLIENT_IP = "clientIp";
    public static final String FH_TERM_ORD_ID = "termOrdId";
    public static final String FH_MEMBER_ID = "memberId";
    public static final String FH_BUSI_CODE = "busiCode";
    public static final String FH_BUSI_STAT = "busiStat";
    public static final String FH_ORD_AMT = "ordAmt";
    public static final String FH_ORD_TYPE = "ordType";
    public static final String FH_AUTH_CODE = "authCode";
    public static final String FH_GOODS_DESC = "goodsDesc";
    public static final String FH_PAY_CHANNEL_TYPE = "payChannelType";
    public static final String FH_BUYER_ID = "buyerId";
    public static final String FH_OPEN_ID = "openId";
    public static final String FH_AGENT_ID = "agentId";
    public static final String FH_BAGENT_ID = "bagentId";
    public static final String FH_OP_TELLER_ID = "opTellerId";
    public static final String FH_TELLER_ID = "tellerId";
    public static final String FH_API_VERSION = "apiVersion";
    public static final String FH_IS_SETTLED = "isSettled";
    public static final String FH_MER_PRIV = "merPriv";
    public static final String FH_IS_DELAY_ACCT = "isDelayAcct";
    public static final String FH_TIME_EXPIRE = "timeExpire";
    public static final String FH_RESP_CODE = "respCode";
    public static final String FH_RESP_DESC = "respDesc";
    public static final String FH_CHECK_VALUE = "checkValue";
    public static final String FH_JSON_DATA = "jsonData";
    public static final String FH_MERCH_NAME = "merchName";
    public static final String FH_WECHAT_PUB_NUM_AUTH = "wechatPubNumAuth";
    public static final String FH_WECHAT_PUB_NUM_APPID = "wechatPubNumAppid";
    public static final String FH_REQ_SERIAL_NUM = "reqSerialNum";
    public static final String FH_FEE_RATE = "feeRate";
    public static final String FH_PAY_INFO = "payInfo";
    public static final String FH_ORD_ID = "ordId";
    public static final String FH_ORDER_ID = "orderId";
    public static final String FH_ORG_TERM_ORD_ID = "orgTermOrdId";
    public static final String FH_MER_NOTICE_URL = "merNoticeUrl";
    public static final String FH_TRANS_DATE = "transDate";
    public static final String FH_TRANS_TIME = "transTime";
    public static final String FH_SETTLE_START_DATE = "settleStartDate";
    public static final String FH_SETTLE_END_DATE = "settleEndDate";
    public static final String FH_RECORD_LIST = "recordList";
    public static final String FH_TOTAL_RECORD = "totalRecord";

    public static final String FH_PAY_CHANNEL_ID = "payChannelId";
    public static final String FH_PAY_SCENE = "payScene";

    public static final String IOS_PAY_SCENE = "pay_scene";
    public static final String IOS_PAY_SCENE_VALUE = "LinkPay";
    public static final String IOS_PAY_SCENE_IM = "IM";

    public static final String T_ENCRYPTION_ID = "encryptionId";
    public static final String T_BATCH_CODE = "batchCode";
    public static final String T_BATCH_Id = "batchId";
    public static final String T_ID = "id";
    public static final String T_SID = "sid";

    public static final String TOTAL_AMOUNT = "totalAmount";
//    public static final String SKU_ID = "skuId";
//    public static final String PRICE = "price";
    public static final String QUANTITY = "quantity";
    public static final String TITLE = "title";
    public static final String IMAGELIST = "imageList";
    public static final String TYPE = "type";
    public static final String TAGGROUPID = "tagGroupId";
    public static final String SKULIST = "skuList";
    public static final String OUTORDERNO = "outOrderNo";
    public static final String ORDERENTRYSCHEMA = "orderEntrySchema";
    public static final String PAYNOTIFYURL = "payNotifyUrl";
    public static final String CURRENCY = "currency";
    public static final String DIAMOND = "DIAMOND";
    public static final String CNY = "CNY";
}
