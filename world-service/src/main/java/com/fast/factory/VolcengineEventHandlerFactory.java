package com.fast.factory;

import com.fast.handler.VolcengineEventHandler;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 火山引擎事件处理器工厂类
 * Created by Song on 2025/08/02.
 */
@Component
public class VolcengineEventHandlerFactory {

    @Getter
    @Autowired
    private List<VolcengineEventHandler> volcengineEventHandlerList;

    @Getter
    private final Map<String, VolcengineEventHandler> volcengineEventHandlerMap = new HashMap<>();

    @PostConstruct
    public void init() {
        volcengineEventHandlerList.forEach(eventHandler -> volcengineEventHandlerMap.put(eventHandler.eventType(), eventHandler));
    }

    public VolcengineEventHandler getVolcengineEventHandler(String eventType) {
        VolcengineEventHandler eventHandler = volcengineEventHandlerMap.get(eventType);
        if (eventHandler == null) {
            throw new IllegalArgumentException("No handler found for event type: " + eventType);
        }
        return eventHandler;
    }

}
