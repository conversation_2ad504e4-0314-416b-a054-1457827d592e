package com.fast.framework.exception;

import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.api.ApiResultEnum;
import com.fast.service.feishu.FeiShuService;
import com.fast.vo.ResultVO;
import org.apache.catalina.connector.ClientAbortException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

/**
 * 全局异常捕获
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private FeiShuService feiShuService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 参数校验异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResultVO<Object> constraintViolationExceptionHandler(ConstraintViolationException ex) {
        StringBuilder sb = new StringBuilder();
        for (ConstraintViolation<?> constraintViolation : ex.getConstraintViolations()) {
            sb.append(StaticVar.SEMICOLON).append(constraintViolation.getMessage());
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(0);
        }
        log.error("Error:", ex);
        log.error("参数校验错误:{}", sb);
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        return ResultVO.error(sb.toString());
    }

    private static final String EXCLUDE_ERR = "java.lang.NumberFormatException: For input string: \"undefined\"";

    /**
     * 参数校验异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResultVO<Object> constraintViolationExceptionHandler(BindException ex) {
        StringBuilder sb = new StringBuilder();
        List<ObjectError> allErrors = ex.getBindingResult().getAllErrors();
        allErrors.forEach(error -> sb.append(StaticVar.SEMICOLON).append(error.getDefaultMessage()));
        if (sb.length() > 0) {
            sb.deleteCharAt(0);
        }
        if (sb.indexOf(EXCLUDE_ERR) > -1) {
            log.error("参数校验错误2:{}", sb);
        } else {
            log.error("参数校验错误2:{}", sb, ex);
        }
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        return ResultVO.error(sb.toString());
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public ResultVO<?> handleException(NoHandlerFoundException ex) {
        log.error("Error:", ex);
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        return ResultVO.error(StaticStr.PLEASE_GET_OUT_HERE);
    }

    /**
     * 拦截未知的空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public ResultVO<?> handleNullPointerException(NullPointerException ex) {
        String requestURI = request.getRequestURI();
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        log.error("请求地址'{}',发生空指针异常.", requestURI, ex);
        return ResultVO.error(ApiResultEnum.ERROR_NULL);
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResultVO<?> handleRuntimeException(RuntimeException ex) {
        String requestURI = request.getRequestURI();
        System.out.println("--"+ex.getMessage());
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        log.error("请求地址'{}',发生未知异常.", requestURI, ex);
        return ResultVO.error(ApiResultEnum.ERROR_RUNTIME);
    }

    @ExceptionHandler(ClassCastException.class)
    public ResultVO<?> handleException(ClassCastException ex) {
        log.error("Error:", ex);
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        return ResultVO.error(ApiResultEnum.ERROR_CLASS_CAST);
    }

    @ExceptionHandler(ClientAbortException.class)
    public ResultVO<?> handleException(ClientAbortException ex) {
        log.error(ex.getMessage());
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        return ResultVO.error(ApiResultEnum.ERROR_IO);
    }

    @ExceptionHandler(IOException.class)
    public ResultVO<?> handleException(IOException ex) {
        log.error("Error:", ex);
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        return ResultVO.error(ApiResultEnum.ERROR_IO);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResultVO<?> handleException(HttpRequestMethodNotSupportedException ex) {
        log.error("Error:", ex);
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        return ResultVO.error(ApiResultEnum.ERROR_METHOD_NOT_SUPPORT);
    }

    @ExceptionHandler(MyException.class)
    public ResultVO<?> handleMyException(MyException ex) {
        log.error("Error:", ex);
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        return ResultVO.error(ex.getMessage());
    }

    /**
     * 数据库异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler({BadSqlGrammarException.class, DataIntegrityViolationException.class, UncategorizedSQLException.class, SQLException.class})
    public ResultVO<?> handleException(Exception ex) {
        log.error(ex.getMessage());
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        return ResultVO.error("SQLException..");
    }
    
    @ExceptionHandler({BusinessException.class})
    public ResultVO<?> handleBusinessException(BusinessException ex) {
        log.error(ex.getMessage());
        return ResultVO.error(ex.getCode(), ex.getMsg());
    }

    @ExceptionHandler({MaxUploadSizeExceededException.class})
    public ResultVO<?> handleBusinessException(MaxUploadSizeExceededException ex) {
        log.error(ex.getMessage());
        return ResultVO.error("Maximum upload size exceeded...");
    }

    /**
     * 其它异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(Exception.class)
    public ResultVO<?> exceptionHandler(Exception ex) {
        // 日志输出
        log.error("Error:", ex);
        feiShuService.sendErrorLogMsgAsync(request.getRequestURI(), ex);
        // 不向外抛出异常具体信息
        return ResultVO.error(StaticStr.SYS_ERROR);
    }
}
