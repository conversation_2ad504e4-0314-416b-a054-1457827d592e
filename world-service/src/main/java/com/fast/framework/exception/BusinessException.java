package com.fast.framework.exception;

import lombok.Getter;

@Getter
public class BusinessException extends RuntimeException {

    private final int code;
    private String msg;

    public BusinessException(int code, String msg) {
        super(msg);
        this.code = code;
    }

    public BusinessException(String msg){
        super(msg);
        this.code = 500;
        this.msg = msg;
    }
}
