package com.fast.framework.exception;

import org.slf4j.Logger;

public class MyException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public MyException(Exception e) {
        super(e);
    }

    public MyException(String msg) {
        super(msg);
    }

    /**
     * 打印异常信息到日志文件
     *
     * @param logger
     * @param e      void
     */
    public static void print(Logger logger, Exception e) {
        if (e.getMessage() != null && e.getMessage().contains("Broken pipe")) {
            printMessage(logger, e);
        } else {
            logger.error("System Error Exception: ", e);
        }
    }

    /**
     * 打印异常信息到日志文件
     *
     * @param logger
     * @param e      void
     */
    public static void printMessage(Logger logger, Exception e) {
        logger.error("System Error Message: {}", e.getMessage());
    }

}
