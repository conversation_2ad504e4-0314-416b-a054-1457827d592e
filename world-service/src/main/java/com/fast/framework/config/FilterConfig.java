package com.fast.framework.config;

import com.fast.framework.filter.RepeatableFilter;
import com.fast.framework.filter.UndefinedStringFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Filter配置
 *
 * <AUTHOR>
 */
@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean<RepeatableFilter> someFilterRegistration() {
        FilterRegistrationBean<RepeatableFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new RepeatableFilter());
        registration.addUrlPatterns("/*");
        registration.setName("repeatableFilter");
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
        return registration;
    }

    @Bean
    public FilterRegistrationBean<UndefinedStringFilter> undefinedStringFilter() {
        FilterRegistrationBean<UndefinedStringFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new UndefinedStringFilter());
        registration.addUrlPatterns("/*");
        registration.setName("undefinedStringFilter");
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
        return registration;
    }

}
