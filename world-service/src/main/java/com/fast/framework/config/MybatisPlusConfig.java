package com.fast.framework.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.fast.base.BaseClass;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.thread.ContentTypeContext;
import com.fast.utils.thread.DramaIdContext;
import com.fast.utils.thread.MemberIdContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Mybatis Plus 配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableTransactionManagement
public class MybatisPlusConfig {
	
	
	
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 动态表名(分表)
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor());
        // 阻断插件
        interceptor.addInnerInterceptor(blockAttackInnerInterceptor());
        return interceptor;
    }

    /**
     * 如果是对全表的删除或更新操作，就会终止该操作 <a href="https://baomidou.com/guide/interceptor-block-attack.html">...</a>
     */
    public BlockAttackInnerInterceptor blockAttackInnerInterceptor() {
        return new BlockAttackInnerInterceptor();
    }

    /**
     * 动态表名(分表)
     */
    public DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor() {
        DynamicTableNameInnerInterceptor interceptor = new DynamicTableNameInnerInterceptor();
        interceptor.setTableNameHandler((sql, tableName) -> {
        	String keySql = Md5Util.getMD5(sql);
        	
            switch (tableName) {
                case "fast_member_order_consume_@": {
                    Long memberId = MemberIdContext.getMemberId();
                    return tableName.replace("@", String.valueOf(memberId % 100));
                }
//                case "fast_member_order_recharge_@": {
//	                Long memberId = MemberIdContext.getMemberId();
//                    return tableName.replace("@", String.valueOf(memberId % 50));
//                }
                case "fast_member_order_recharge": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 3: {
                            return "fast_member_order_recharge_novel";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member_recent_log": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 1: {
                            Integer dramaId = DramaIdContext.getDramaId();
                            if (dramaId != null) {
                                return tableName + "_" + dramaId;
                            }
                            return tableName;
                        }
                        case 2: {
                            return "fast_member_cartoon_recent_log";
                        }
                        case 3: {
                            return "fast_member_recent_log_novel";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member_recent": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 2: {
                            return "fast_member_cartoon_recent";
                        }
                        case 3: {
                            return "fast_member_novel_recent";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member_recent_day": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 2: {
                            return "fast_member_cartoon_recent_day";
                        }
                        case 3: {
                            return "fast_member_novel_recent_day";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member_recent_drama_finish": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 2: {
                            return "fast_member_cartoon_recent_drama_finish";
                        }
                        case 3: {
                            return "fast_member_novel_recent_drama_finish";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member_recent_series_day": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 1: {
                            Integer dramaId = DramaIdContext.getDramaId();
                            if (dramaId != null) {
                                return tableName + "_" + dramaId;
                            }
                            return tableName;
                        }
                        case 2: {
                            return "fast_member_cartoon_recent_series_day";
                        }
                        case 3: {
                            return "fast_member_novel_recent_series_day";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member_recent_series_num": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 2: {
                            return "fast_member_cartoon_recent_series_num";
                        }
                        case 3: {
                            return "fast_member_novel_recent_series_num";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member_order_consume": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 2: {
                            return "fast_member_cartoon_consume";
                        }
                        case 3: {
                            return "fast_member_novel_consume";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member_drama_first_watch": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 2: {
                            return "fast_member_cartoon_first_watch";
                        }
                        case 3: {
                            return "fast_member_novel_first_watch";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 3: {
                            return "fast_member_novel";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member_link": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 3: {
                            return "fast_member_link_novel";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_report_member_recent_day": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 3: {
                            return "fast_report_member_recent_day_novel";
                        }
                        default:
                            return tableName;
                    }
                }
                case "fast_member_link_his": {
                    int contentType = BaseClass.defaultIfNull(ContentTypeContext.getContentType(), 1);
                    switch (contentType) {
                        case 3: {
                            return "fast_member_link_his_novel";
                        }
                        default:
                            return tableName;
                    }
                }
                default:
                    return tableName;
            }
            
        });
        return interceptor;
    }

}
