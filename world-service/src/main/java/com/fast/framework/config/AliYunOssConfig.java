package com.fast.framework.config;

import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * 阿里云Oss 配置
 *
 * <AUTHOR>
 * @date 2021-10-02
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "oss.ali")
@ConditionalOnProperty(name = "fast.deploy_platform", havingValue = "1")
public class AliYunOssConfig {

    private String scheme;
    private String host;
    private String accessKey;
    private String secretKey;
    private String bucketName;
    private String endpoint;
    private String endpoint2;
    private String callbackUrl;

    public String getHost() {
        if (host == null || host.isEmpty()) {
            host = scheme + "://" + bucketName + "." + endpoint + "/";
        }
        return host;
    }

    @Bean
    public OSSClient ossClient() {
        return new OSSClient(endpoint,
                new DefaultCredentialProvider(accessKey, secretKey),
                new ClientConfiguration());
    }

    @Bean
    public OSSClient ossClientInternal() {
        return new OSSClient(endpoint2,
                new DefaultCredentialProvider(accessKey, secretKey),
                new ClientConfiguration());
    }

    public String getHostInternal() {
        return scheme + "://" + bucketName + "." + endpoint2 + "/";
    }
}
