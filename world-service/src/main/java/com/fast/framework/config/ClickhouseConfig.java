package com.fast.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "spring.datasource.dynamic.datasource.clickhouse")
public class ClickhouseConfig {

    private String driverClassName;

    private String url;

    private String username;

    private String password;

    private String dbname;
}
