package com.fast.framework.config;

import org.apache.catalina.connector.Connector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Spring Boot2.X Tomcat容器优雅停机
 *
 * <AUTHOR>
 * @date 2022-09-02
 */
@Configuration
public class ShutdownConfig {

    /**
     * 用于接受shutdown事件
     *
     * @return
     */
    @Bean
    public SmartShutdown gracefulShutdown() {
        return new SmartShutdown();
    }

    @Bean
    public ServletWebServerFactory servletContainer() {
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory();
        tomcat.addConnectorCustomizers(gracefulShutdown());
        return tomcat;
    }

    private static class SmartShutdown implements TomcatConnectorCustomizer, ApplicationListener<ContextClosedEvent> {
        private static final Logger log = LoggerFactory.getLogger(SmartShutdown.class);
        private volatile Connector connector;
        private static final int WAIT_TIME = 90;

        @Override
        public void customize(Connector connector) {
            this.connector = connector;
        }

        @Override
        public void onApplicationEvent(ContextClosedEvent event) {
            this.connector.pause();
            Executor executor = this.connector.getProtocolHandler().getExecutor();
            if (executor instanceof ThreadPoolExecutor) {
                ThreadPoolExecutor pool = (ThreadPoolExecutor) executor;
                try {
                    pool.shutdown();
                    if (!pool.awaitTermination(WAIT_TIME, TimeUnit.SECONDS)) {
                        log.info("Tomcat 进程在" + WAIT_TIME + "秒内无法结束，尝试强制结束");
                        pool.shutdownNow();
                    }
                    log.info("shutdown server success");
                } catch (InterruptedException ex) {
                    pool.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
}
