package com.fast.framework.config;

import com.google.crypto.tink.apps.rewardedads.RewardedAdsVerifier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.security.GeneralSecurityException;

/**
 * Created by <PERSON> on 2025/06/11.
 */
@Configuration
@Slf4j
public class AdMobSsvConfig {

    @Bean
    public RewardedAdsVerifier rewardedAdsVerifier() throws GeneralSecurityException {
        RewardedAdsVerifier rewardedAdsVerifier = new RewardedAdsVerifier.Builder()
                .fetchVerifyingPublicKeysWith(RewardedAdsVerifier.KEYS_DOWNLOADER_INSTANCE_PROD)
                .build();
        log.info("初始化RewardedAdsVerifier成功");
        return rewardedAdsVerifier;
    }

}
