package com.fast.framework.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "oss.lsy")
@ConditionalOnProperty(name = "fast.deploy_platform", havingValue = "2")
public class LsyTosConfig {

    private String endpoint;
    private String region;
    private String accessKey;
    private String secretKey;
    private String bucketName;
    private String callbackUrl;
    private String callbackHost;

    private String host;

    public String getHost() {
        if (host == null || host.isEmpty()) {
            host = "https://" + bucketName + "." + endpoint + "/";
        }
        return host;
    }

    // 内容生产平台
    private Cont cont;

    @Getter
    @Setter
    public static class Cont {
        private String bucketName;
        private String endpoint;
        private String region;

        private String host;

        public String getHost() {
            if (host == null || host.isEmpty()) {
                host = "https://" + bucketName + "." + endpoint + "/";
            }
            return host;
        }
    }

}
