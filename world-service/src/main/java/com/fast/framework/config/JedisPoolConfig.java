package com.fast.framework.config;

import com.fast.constant.RedisVar;
import com.fast.utils.redis.RedisLauUtil;
import com.fast.utils.redis.RedisUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;

import java.time.Duration;

/**
 * JedisPool 配置
 *
 * <AUTHOR>
 * @date 2022-08-22
 */
@Configuration
public class JedisPoolConfig {

    @Bean
    public JedisPool jedisPool(RedisVar redisVar) {
        final redis.clients.jedis.JedisPoolConfig config = new redis.clients.jedis.JedisPoolConfig();
        config.setMinIdle(RedisVar.REDIS_MIN_IDLE);// 最小空闲连接数
        config.setMaxIdle(RedisVar.REDIS_MAX_IDLE);// 最大空闲连接数
        config.setMaxTotal(RedisVar.REDIS_MAX_ACTIVE);// 最大连接数
        config.setMaxWait(Duration.ofMillis(RedisVar.TIMEOUT));// 最大等待时间(毫秒)
        config.setTestWhileIdle(true);// 定期对线程池中空闲的连接进行validateObject校验
        config.setJmxEnabled(false);

        JedisPool jedisPool = new JedisPool(config, RedisVar.REDIS_HOST, RedisVar.REDIS_PORT, RedisVar.TIMEOUT, RedisVar.REDIS_PWD);
        RedisUtil.setJedisPool(jedisPool);

        JedisPool jedisLockPool = new JedisPool(config, RedisVar.REDIS_LAUNCH_HOST, RedisVar.REDIS_PORT, RedisVar.TIMEOUT, RedisVar.REDIS_PWD);
        RedisLauUtil.setJedisPool(jedisLockPool);

        return jedisPool;
    }
}
