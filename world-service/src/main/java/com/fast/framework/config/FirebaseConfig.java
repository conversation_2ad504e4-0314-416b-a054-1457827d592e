package com.fast.framework.config;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * Created by Song on 2025/06/06.
 */
@Configuration
@Slf4j
public class FirebaseConfig {

    @Value("${fast.google_pay_project}")
    private String projectName;

    @Value("${fast.google_pay_account_keypath1}")
    private String serviceAccountKeyPath1;

    @PostConstruct
    public void initialize() throws IOException {
        // log.info("projectName: {}", projectName);
        // log.info("serviceAccountKeyPath: {}", serviceAccountKeyPath);

        FileInputStream serviceAccount = new FileInputStream(serviceAccountKeyPath1);
        FirebaseOptions options = FirebaseOptions.builder()
                .setProjectId(projectName)
                .setCredentials(GoogleCredentials.fromStream(serviceAccount))
                .build();
        FirebaseApp.initializeApp(options);
        log.info("初始化FireBase成功");
    }

}