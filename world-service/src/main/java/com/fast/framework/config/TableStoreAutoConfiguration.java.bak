package com.fast.framework.config;

import com.alicloud.openservices.tablestore.ClientConfiguration;
import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.TimeseriesClient;
import com.alicloud.openservices.tablestore.TunnelClient;
import com.alicloud.openservices.tablestore.model.AlwaysRetryStrategy;
import com.fast.constant.TableStoreProperties;
import com.fast.utils.tablestore.TableStoreUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.autoconfigure.tablestore.service.TableStoreService;
//import org.springframework.boot.autoconfigure.tablestore.service.impl.TableStoreServiceImpl;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * Created on 2020/10/09
 *
 * <AUTHOR>
 */
@Configuration
//@ConditionalOnClass({TableStoreService.class, SyncClient.class, TunnelClient.class})
@EnableConfigurationProperties({TableStoreProperties.class})
public class TableStoreAutoConfiguration {

    @Resource
    private TableStoreProperties tableStoreProperties;

//    @Bean
//    @ConditionalOnMissingBean(name = {"tableStoreService"})
//    public TableStoreService tableStoreService(SyncClient syncClient) {
//        return new TableStoreServiceImpl(syncClient);
//    }

    @Bean(destroyMethod = "shutdown")
    @ConditionalOnMissingBean(name = {"syncClient"})
    public SyncClient syncClient() {
// ClientConfiguration提供了很多配置项，以下只列举部分。
        ClientConfiguration clientConfiguration = new ClientConfiguration();
// 设置建立连接的超时时间。单位为毫秒。
        clientConfiguration.setConnectionTimeoutInMillisecond(tableStoreProperties.getConnectionTimeout());
// 设置socket超时时间。单位为毫秒。
        clientConfiguration.setSocketTimeoutInMillisecond(tableStoreProperties.getSocketTimeout());
// 设置重试策略。如果不设置，则采用默认的重试策略。
        clientConfiguration.setRetryStrategy(new AlwaysRetryStrategy());
        SyncClient client = new SyncClient(tableStoreProperties.getEndpoint(),
                tableStoreProperties.getAk(),
                tableStoreProperties.getSk(),
                tableStoreProperties.getInstance(), clientConfiguration);
        TableStoreUtil.setSyncClient(client);
        return client;
    }


    @Bean(destroyMethod = "shutdown")
    @ConditionalOnMissingBean(name = {"timeseriesClient"})
    public TimeseriesClient timeseriesClient() {
// ClientConfiguration提供了很多配置项，以下只列举部分。
        ClientConfiguration clientConfiguration = new ClientConfiguration();
// 设置建立连接的超时时间。单位为毫秒。
        clientConfiguration.setConnectionTimeoutInMillisecond(tableStoreProperties.getConnectionTimeout());
// 设置socket超时时间。单位为毫秒。
        clientConfiguration.setSocketTimeoutInMillisecond(tableStoreProperties.getSocketTimeout());
// 设置重试策略。如果不设置，则采用默认的重试策略。
        clientConfiguration.setRetryStrategy(new AlwaysRetryStrategy());
        TimeseriesClient client = new TimeseriesClient(tableStoreProperties.getEndpoint(),
                tableStoreProperties.getAk(),
                tableStoreProperties.getSk(),
                tableStoreProperties.getInstance(), clientConfiguration);
        return client;
    }


    @Bean(destroyMethod = "shutdown")
    @ConditionalOnMissingBean(name = {"tunnelClient"})
    public TunnelClient tunnelClient() {
        return new TunnelClient(tableStoreProperties.getEndpoint(),
                tableStoreProperties.getAk(),
                tableStoreProperties.getSk(),
                tableStoreProperties.getInstance());
    }
}
