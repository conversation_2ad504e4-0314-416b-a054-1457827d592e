package com.fast.framework.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 阿里云Cdn 配置
 *
 * <AUTHOR>
 * @date 2022-09-19
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "cdn.ali")
public class AliYunCdnConfig {
    public String url;
    public String auth;// 授权秘钥
    public String accessKey;// 阿里云账号
    public String secretKey;// 阿里云账号

}
