package com.fast.framework.config;

import com.fast.constant.RedisVar;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * redisson配置，默认用single模式，需要集群或哨兵的需要改造一下配置
 */
@Configuration
@Slf4j
public class RedissonConfig {

    @Bean
    public RedissonClient redissonClient(RedisVar redisVar) {
        // log.info("redis配置：host={}, port={}, db={}, pwd={}", RedisVar.REDIS_HOST, RedisVar.REDIS_PORT, RedisVar.REDIS_DB, RedisVar.REDIS_PWD);
        Config config = new Config();

        SingleServerConfig singleServerConfig = config.useSingleServer();
        singleServerConfig.setAddress("redis://" + RedisVar.REDIS_HOST + ":" + RedisVar.REDIS_PORT);
        if (RedisVar.REDIS_PWD != null) {
            singleServerConfig.setPassword(RedisVar.REDIS_PWD);
        }
        singleServerConfig.setDatabase(RedisVar.REDIS_DB);

        return Redisson.create(config);
    }

}
