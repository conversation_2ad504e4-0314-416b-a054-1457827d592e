package com.fast.framework.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.http.HttpClient;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * 视频处理配置
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "fast.videoprocess")
@Slf4j
public class VideoProcessConfig {

    private GhostCut ghostCut;

    private Coze coze;

    @Bean
    public HttpClient httpClient() {
        return HttpClient.newBuilder()
                .version(HttpClient.Version.HTTP_2)
                .connectTimeout(Duration.ofSeconds(30))
                .executor(virtualThreadExecutor())
                .build();
    }

    @Bean(destroyMethod = "shutdown")
    public ExecutorService virtualThreadExecutor() {
        ThreadFactory virtualThreadFactory = Thread.ofVirtual()
                .name("VideoProcess-VT-", 0)
                .uncaughtExceptionHandler((thread, ex) -> log.error("虚拟线程 {} 异常: {}", thread.getName(), ExceptionUtils.getStackTrace(ex)))
                .factory();
        return Executors.newThreadPerTaskExecutor(virtualThreadFactory);
    }

    @Getter
    @Setter
    public static class GhostCut {
        private String apiDomain;
        private String appKey;
        private String appSecret;
        private String uid;
        // private String cdnPrefix;
        private Notify notify;

        @Getter
        @Setter
        public static class Notify {
            private String domain;
            private String subtitleExtract;
            private String subtitleRemove;
            private String videoCompress;
        }
    }

    @Getter
    @Setter
    public static class Coze {
        private String apiUrl;
        private String apiToken;
        private String zh2enWorkflowId;
        private String en2anyWorkflowId;
    }

    public String buildVideoCompressCallback(Integer batchTaskId) {
        GhostCut.Notify notify = ghostCut.getNotify();
        return String.format(notify.getDomain() + notify.getVideoCompress(), batchTaskId);
    }

    public String buildSubtitleRemoveCallback(Integer batchTaskId) {
        GhostCut.Notify notify = ghostCut.getNotify();
        return String.format(notify.getDomain() + notify.getSubtitleRemove(), batchTaskId);
    }

    public String buildSubtitleExtractCallback(Integer batchTaskId) {
        GhostCut.Notify notify = ghostCut.getNotify();
        return String.format(notify.getDomain() + notify.getSubtitleExtract(), batchTaskId);
    }

    // https://gc100.cdn.izhaoli.cn/ve_work/af54e20cafa04d93/492675037/492675037__work__export_0.mp4
    // 鬼手没有返回视频url，但是根据它的后台管理中可以分析出来
    // public String buildCdnVideoUrl(String dataId) {
    //     return ghostCut.getCdnPrefix() + "/" + dataId + "/" + dataId + "__work__export_0.mp4";
    // }

}
