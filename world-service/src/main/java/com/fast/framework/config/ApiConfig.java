package com.fast.framework.config;

import com.fast.annotation.ApiName;
import com.fast.annotation.ApiParamsIn;
import com.fast.annotation.ApiParamsOut;
import com.fast.constant.StaticVar;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ApiDocVO;
import com.github.pagehelper.util.StringUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.JarURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

@Component
public class ApiConfig {

    public static String getHtml() {
        StringBuilder html = new StringBuilder();
        String jquery = RedisUtil.get(StaticVar.JS_JQUERY);
        String apidoc = RedisUtil.get(StaticVar.JS_API);
        html.append("<html>");
        html.append("<head>");
        html.append("<title>短剧项目api文档2</title>");
        html.append("<script type='text/javascript'>");
        html.append(jquery);
        html.append(apidoc);
        html.append("</script>");

        html.append("<style type='text/css'>");
        html.append(".rx-sc{display:flex;flex-flow: row nowrap;justify-content: flex-start;align-items:center;}");
        html.append(".pop{cursor: pointer;}");
        html.append(".pop:hover{background-color: #eee;}");
        html.append(".pl30{padding-left: 30px;}");
        html.append(".color-888{color:#888888;}");
        html.append(".color-333{color:#333333;}");
        html.append("</style>");
        html.append("</head>");
        html.append("<body>");
        html.append("<div id='top' style='width:100vw;height:50px;position:absolute;top:0px;border-bottom:1px solid #666;'>");
        html.append("</div>");
        html.append("<div class='rx-sc' style='margin-top:50px;'>");
        html.append("<div class='menu' id='menu' style='height:100vh;width:300px;border-right: 1px solid #a00;overflow-y:scroll;'></div>");
        html.append("<div id='detail'></div></div>");
        html.append("</body>");
        html.append("</html>");
        return html.toString();
    }

    public List<ApiDocVO> getAllMethod() {
        Set<Class> allClass = getClasses("com.fast.controller");
        List<ApiDocVO> docList = new ArrayList<>();
        if (allClass.size() < 1) {
            System.out.println("为空返回");
            return null;
        }
        int index = 1;// 接口编号
        for (Class c : allClass) {
            // 获取基础路径
            StringBuilder basePath = new StringBuilder();
            RequestMapping requestMapping = (RequestMapping) c.getAnnotation(RequestMapping.class);
            if (requestMapping != null) {
                String[] basePathArray = requestMapping.value();
                for (String bp : basePathArray) {
                    if (basePath.length() > 0) {
                        basePath.append(",");
                    }
                    basePath.append(bp);
                }
            }
            // 获取方法
            Method[] ss = c.getMethods();
            for (Method method : ss) {
                RequestMapping rm = method.getAnnotation(RequestMapping.class);
                PostMapping pm = method.getAnnotation(PostMapping.class);
                GetMapping gm = method.getAnnotation(GetMapping.class);
                // 方法路径
                String[] basePathArray = null;
                if (rm != null) {
                    basePathArray = rm.value();
                } else if (pm != null) {
                    basePathArray = pm.value();
                } else if (gm != null) {
                    basePathArray = gm.value();
                }
                if (basePathArray != null) {
                    ApiDocVO doc = new ApiDocVO();
                    ApiName apiName = method.getAnnotation(ApiName.class);
                    String methodName = "";
                    StringBuilder folderBuffer = new StringBuilder();
                    if (apiName != null) {
                        methodName = apiName.value();
                        String[] folders = apiName.folder();
                        if (folders != null) {
                            for (String folder : folders) {
                                if (folderBuffer.length() > 0) {
                                    folderBuffer.append(",");
                                }
                                folderBuffer.append(folder);
                            }
                        }
                    }
                    StringBuilder methodPath = new StringBuilder();
                    for (String bp : basePathArray) {
                        if (methodPath.length() > 0) {
                            methodPath.append(",");
                        }
                        methodPath.append(bp);
                    }
                    // 入参
                    StringBuilder paramsIn = new StringBuilder();
                    ApiParamsIn apiParams = method.getAnnotation(ApiParamsIn.class);
                    if (apiParams != null && apiParams.value() != null) {
                        for (String params : apiParams.value()) {
                            if (paramsIn.length() > 0) {
                                paramsIn.append("##");
                            }
                            paramsIn.append(params);
                        }
                    }
                    // 出参
                    StringBuilder paramsOut = new StringBuilder();
                    ApiParamsOut apiParamsOut = method.getAnnotation(ApiParamsOut.class);
                    if (apiParamsOut != null && apiParamsOut.value() != null) {
                        for (String params : apiParamsOut.value()) {
                            if (paramsOut.length() > 0) {
                                paramsOut.append("##");
                            }
                            paramsOut.append(params);
                        }
                    }
                    doc.setPath((basePath + "/" + methodPath).replace("//", "/"));
                    doc.setMethodName(methodName);
                    doc.setFolder(folderBuffer.toString());
                    doc.setParamsIn(paramsIn.toString());
                    doc.setParamsOut(paramsOut.toString());
                    doc.setIndex(index);
                    if (StringUtil.isNotEmpty(methodName) && paramsIn.length() > 0) {
                        docList.add(doc);
                        index++;
                    }
                }
            }
        }

        if (!docList.isEmpty()) {
            docList.sort(Comparator.comparing(ApiDocVO::getFolder).thenComparing(ApiDocVO::getMethodName));
        }

        return docList;
    }

    public static List<Class> getClassByPackage(String packageName) {
        System.out.println("----获取包路径：" + packageName);
        try {
            List<Class> allList = new ArrayList<>();
            Enumeration<URL> resources = ClassUtils.class.getClassLoader().getResources(packageName.replaceAll("\\.", "/"));
            while (resources.hasMoreElements()) {
                System.out.println("有资源");
                URL url = resources.nextElement();
                System.out.println(url.getFile());
                String[] file = new File(url.getFile()).list();
                if (file != null && file.length > 0) {
                    Class[] classList = new Class[file.length];
                    System.out.println("----file：" + file.length);
                    for (String s : file) {
                        try {
                            allList.add(Class.forName(packageName + "." + s.replaceAll("\\.class", "")));
                        } catch (Exception e) {
                            System.out.println("----allList：文件找不到");
                            // 文件为找到不处理
                            List<Class> subList = getClassByPackage(packageName + "." + s.replaceAll("\\.class", ""));
                            if (subList != null && subList.size() > 0) {
                                allList.addAll(subList);
                            }
                        }
                    }
                }
            }
            return allList;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 从包package中获取所有的Class
     *
     * @param pack
     * @return
     */
    public static Set<Class> getClasses(String pack) {

        Set<Class> classes = new LinkedHashSet<Class>();// 第一个class类的集合
        boolean recursive = true;// 是否循环迭代
        String packageName = pack;// 获取包的名字 并进行替换
        String packageDirName = packageName.replace('.', '/');

        Enumeration<URL> dirs;// 定义一个枚举的集合 并进行循环来处理这个目录下的things
        try {
            dirs = Thread.currentThread().getContextClassLoader().getResources(packageDirName);
            // 循环迭代下去
            while (dirs.hasMoreElements()) {
                // 获取下一个元素
                URL url = dirs.nextElement();
                // 得到协议的名称
                String protocol = url.getProtocol();
                // 如果是以文件的形式保存在服务器上
                if ("file".equals(protocol)) {
                    // System.err.println("file类型的扫描");
                    // 获取包的物理路径
                    String filePath = URLDecoder.decode(url.getFile(), "UTF-8");
                    // 以文件的方式扫描整个包下的文件 并添加到集合中
                    findAndAddClassesInPackageByFile(packageName, filePath, recursive, classes);
                } else if ("jar".equals(protocol)) {
                    // 如果是jar包文件
                    // 定义一个JarFile
                    // System.err.println("jar类型的扫描");
                    JarFile jar;
                    try {
                        // 获取jar
                        jar = ((JarURLConnection) url.openConnection()).getJarFile();
                        // 从此jar包 得到一个枚举类
                        Enumeration<JarEntry> entries = jar.entries();
                        // 同样的进行循环迭代
                        while (entries.hasMoreElements()) {
                            // 获取jar里的一个实体 可以是目录 和一些jar包里的其他文件 如META-INF等文件
                            JarEntry entry = entries.nextElement();
                            String name = entry.getName();
                            // 如果是以/开头的
                            if (name.charAt(0) == '/') {
                                // 获取后面的字符串
                                name = name.substring(1);
                            }
                            // 如果前半部分和定义的包名相同
                            if (name.startsWith(packageDirName)) {
                                int idx = name.lastIndexOf('/');
                                // 如果以"/"结尾 是一个包
                                if (idx != -1) {
                                    // 获取包名 把"/"替换成"."
                                    packageName = name.substring(0, idx).replace('/', '.');
                                }
                                // 如果可以迭代下去 并且是一个包
                                if ((idx != -1) || recursive) {
                                    // 如果是一个.class文件 而且不是目录
                                    if (name.endsWith(".class") && !entry.isDirectory()) {
                                        // 去掉后面的".class" 获取真正的类名
                                        String className = name.substring(packageName.length() + 1, name.length() - 6);
                                        try {
                                            // 添加到classes
                                            classes.add(Class.forName(packageName + '.' + className));
                                        } catch (ClassNotFoundException e) {
                                            // log.error("添加用户自定义视图类错误 找不到此类的.class文件");
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            }
                        }
                    } catch (IOException e) {
                        // log.error("在扫描用户定义视图时从jar包获取文件出错");
                        e.printStackTrace();
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return classes;
    }

    /**
     * 以文件的形式来获取包下的所有Class
     *
     * @param packageName
     * @param packagePath
     * @param recursive
     * @param classes
     */
    public static void findAndAddClassesInPackageByFile(String packageName,
                                                        String packagePath, final boolean recursive, Set<Class> classes) {
        // 获取此包的目录 建立一个File
        File dir = new File(packagePath);
        // 如果不存在或者 也不是目录就直接返回
        if (!dir.exists() || !dir.isDirectory()) {
            // log.warn("用户定义包名 " + packageName + " 下没有任何文件");
            return;
        }
        // 如果存在 就获取包下的所有文件 包括目录
        File[] dirfiles = dir.listFiles(new FileFilter() {

            // 自定义过滤规则 如果可以循环(包含子目录) 或则是以.class结尾的文件(编译好的java类文件)
            public boolean accept(File file) {
                return (recursive && file.isDirectory()) || (file.getName().endsWith(".class"));
            }
        });
        // 循环所有文件
        for (File file : dirfiles) {
            // 如果是目录 则继续扫描
            if (file.isDirectory()) {
                findAndAddClassesInPackageByFile(packageName + "." + file.getName(), file.getAbsolutePath(), recursive,
                        classes);
            } else {
                // 如果是java类文件 去掉后面的.class 只留下类名
                String className = file.getName().substring(0, file.getName().length() - 6);
                try {
                    // 添加到集合中去
                    classes.add(Thread.currentThread().getContextClassLoader().loadClass(packageName + '.' + className));
                } catch (ClassNotFoundException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
