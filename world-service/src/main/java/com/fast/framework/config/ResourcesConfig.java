package com.fast.framework.config;

import com.fast.framework.interceptor.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 通用配置
 *
 * <AUTHOR>
 */
@Configuration
public class ResourcesConfig implements WebMvcConfigurer {

    public static Integer interceptorType = 1;// 拦截器类型1：默认拦截器，2：openapi自定义拦截器,3:fast-api自定义拦截器,4:小程序专用拦截器

    @Autowired
    private CommonApiInterceptor commonApiInterceptor;
    @Autowired
    private MiniApiInterceptor miniApiInterceptor;
    @Autowired
    private OpenApiInterceptor openApiInterceptor;
    @Autowired
    private FastApiInterceptor fastApiInterceptor;
    @Autowired
    private IpWhiteInterfaceInterceptor ipWhiteInterfaceInterceptor;

    private static final Logger logger = LoggerFactory.getLogger(ResourcesConfig.class);

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
    }

    /**
     * 自定义拦截规则
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        logger.info("interceptorType:" + interceptorType);
        if (interceptorType == 2) {
            logger.info("加载-openapi-专用拦截器");
            registry.addInterceptor(openApiInterceptor).addPathPatterns("/**");
        } else if (interceptorType == 3) {
            logger.info("加载-fast-api-专用拦截器");
            registry.addInterceptor(fastApiInterceptor).addPathPatterns("/**");
            registry.addInterceptor(ipWhiteInterfaceInterceptor).addPathPatterns("/**");
        } else if (interceptorType == 4) {
            logger.info("加载-mini-api-专用拦截器");
            registry.addInterceptor(miniApiInterceptor).addPathPatterns("/**");
        } else {
            logger.info("加载默认通用拦截器");
            registry.addInterceptor(commonApiInterceptor).addPathPatterns("/**");
        }
    }

    /**
     * 跨域配置
     */
    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        // 设置访问源地址
        config.addAllowedOriginPattern("*");
        // 设置访问源请求头
        config.addAllowedHeader("*");
        // 设置访问源请求方法
        config.addAllowedMethod("*");
        // 对接口配置跨域设置
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}
