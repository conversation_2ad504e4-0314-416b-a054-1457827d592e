package com.fast.framework.config;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpRequestInitializer;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.GeneralSecurityException;
import java.util.Collections;

/**
 * Created by <PERSON> on 2025/06/06.
 */
@Configuration
@Slf4j
public class GooglePlayConfig {

    @Value("${fast.google_pay_project}")
    private String projectName;

    @Value("${fast.google_pay_account_keypath}")
    private String serviceAccountKeyPath;

    @Bean
    public AndroidPublisher androidPublisher() throws IOException, GeneralSecurityException {
        // log.info("projectName: {}", projectName);
        // log.info("serviceAccountKeyPath: {}", serviceAccountKeyPath);

        GoogleCredentials credentials = GoogleCredentials
                .fromStream(Files.newInputStream(Paths.get(serviceAccountKeyPath)))
                .createScoped(Collections.singleton(AndroidPublisherScopes.ANDROIDPUBLISHER));

        NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
        GsonFactory gsonFactory = GsonFactory.getDefaultInstance();
        HttpRequestInitializer requestInitializer = new HttpCredentialsAdapter(credentials);

        AndroidPublisher androidPublisher = new AndroidPublisher.Builder(httpTransport, gsonFactory, requestInitializer)
                .setApplicationName(projectName)
                .build();
        log.info("初始化AndroidPublisher成功");

        return androidPublisher;
    }

}
