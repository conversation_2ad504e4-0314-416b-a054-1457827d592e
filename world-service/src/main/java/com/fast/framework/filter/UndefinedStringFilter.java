package com.fast.framework.filter;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 用过滤器过滤全站非法字符
 *
 * <AUTHOR>
 */
public class UndefinedStringFilter implements Filter {

    @Override
    public void destroy() {
    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) resp;

        // new 出一个增强后的HttpServletRequest
        chain.doFilter(new UndefinedStringRequest(request), response);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }
}
