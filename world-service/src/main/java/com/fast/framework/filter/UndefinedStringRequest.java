package com.fast.framework.filter;

import com.fast.constant.StaticVar;
import com.fast.utils.StrUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.util.List;

public class UndefinedStringRequest extends HttpServletRequestWrapper {

    // 定义非法字符集合
    private static final List<String> STR = List.of("undefined");

    public UndefinedStringRequest(HttpServletRequest request) {
        super(request);
    }

    @Override
    public String getHeader(String name) {
        String value = super.getHeader(name);
        if (StrUtil.isEmpty(value)) {
            return null;
        }
        return filter(value);// 返回代替后的数据
    }

    @Override
    public String getParameter(String name) {
        String value = super.getParameter(name);
        if (StrUtil.isEmpty(value)) {
            return null;
        }
        return filter(value);// 返回代替后的数据
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] parameters = super.getParameterValues(name);
        if (parameters == null || parameters.length == 0) {
            return null;
        }
        for (int i = 0; i < parameters.length; i++) {
            parameters[i] = filter(parameters[i]);
        }
        return parameters;
    }

    private static String filter(String s) {
        for (String str : STR) {
            if (s != null && s.contains(str)) {
                s = s.replaceAll(str, StaticVar.EMPTY);
            }
        }
        return s;
    }
}
