package com.fast.framework.filter;

import com.fast.utils.StrUtil;
import org.slf4j.MDC;
import org.springframework.http.MediaType;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.UUID;

/**
 * Repeatable 过滤器
 *
 * <AUTHOR>
 */
public class RepeatableFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        String traceId = MDC.get("traceId");
        if (traceId == null) {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            traceId = uuid.substring(0, 16) + "." + uuid.substring(16);
        }
        MDC.put("traceId", traceId);
        
        ServletRequest requestWrapper = null;
        if (request instanceof HttpServletRequest) {
            String contentType = request.getContentType();
            if (contentType != null && StrUtil.startsWithIgnoreCase(contentType, MediaType.APPLICATION_JSON_VALUE)) {
                requestWrapper = new RepeatedlyRequest((HttpServletRequest) request, response);
            }
        }
        if (null == requestWrapper) {
            chain.doFilter(request, response);
        } else {
            chain.doFilter(requestWrapper, response);
        }

        MDC.remove("traceId");
    }

    @Override
    public void destroy() {
    }
}
