package com.fast.framework.manager;

import com.fast.framework.thread.mdc.MdcThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.Executor;

/**
 * 视频处理虚拟线程管理器
 */
@Component
@Slf4j
public class VThreadManager {

    @Autowired
    private Executor virtualThreadExecutor;

    public void executeTask(Runnable runnable) {
        virtualThreadExecutor.execute(runnable);
    }

    public void executeTaskWithMDC(Runnable runnable) {
        Map<String, String> mdcContext = MdcThreadUtil.getCurrentMDCContext();
        virtualThreadExecutor.execute(() -> MdcThreadUtil.executeWithMDC(mdcContext, runnable));
    }

}
