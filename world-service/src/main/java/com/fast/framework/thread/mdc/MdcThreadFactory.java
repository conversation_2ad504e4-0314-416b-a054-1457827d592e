package com.fast.framework.thread.mdc;

import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * 带有MDC的线程工厂
 * Created by SongFei on 2025/04/10.
 */
public class MdcThreadFactory implements ThreadFactory {

    private final ThreadFactory defaultFactory = Executors.defaultThreadFactory();

    @Override
    public Thread newThread(Runnable r) {
        return defaultFactory.newThread(wrap(r));
    }

    private Runnable wrap(Runnable runnable) {
        return MdcThreadUtil.wrap(runnable);
    }

}