package com.fast.framework.thread.mdc;

import com.fast.utils.StrUtil;
import org.slf4j.MDC;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * 处理MDC相关的线程工具类，手动包装 Runnable/Callable 任务实现上下文传递
 * Created by SongFei on 2025/04/10.
 */
public class MdcThreadUtil {

    public static Runnable wrap(Runnable runnable) {
        // 在父线程提交任务时捕获上下文快照
        Map<String, String> contextMap = getCurrentMDCContext();
        return () -> {
            // 子线程执行前注入上下文
            if (contextMap == null) {
                MDC.clear();
            } else {
                MDC.setContextMap(buildNewContextMap(contextMap));
            }
            // 执行原始任务
            try {
                runnable.run();
            } finally {
                MDC.clear();
            }
        };
    }

    public static <T> Callable<T> wrap(Callable<T> callable) {
        // 在父线程提交任务时捕获上下文快照
        Map<String, String> contextMap = getCurrentMDCContext();
        return () -> {
            // 子线程执行前注入上下文
            if (contextMap == null) {
                MDC.clear();
            } else {
                MDC.setContextMap(buildNewContextMap(contextMap));
            }
            // 执行原始任务
            try {
                return callable.call();
            } finally {
                MDC.clear();
            }
        };
    }

    /**
     * 在当前线程中执行任务时传递指定的MDC上下文
     *
     * @param contextMap MDC上下文映射
     * @param supplier   要执行的任务
     * @param <T>        返回值类型
     * @return 任务执行结果
     */
    public static <T> T executeWithMDC(Map<String, String> contextMap, Supplier<T> supplier) {
        if (contextMap == null || contextMap.isEmpty()) {
            return supplier.get();
        }
        // 设置传入的MDC上下文
        MDC.setContextMap(buildNewContextMap(contextMap));
        return supplier.get();
    }

    /**
     * 在当前线程中执行任务时传递指定的MDC上下文（无返回值版本）
     *
     * @param mdcContext MDC上下文映射
     * @param runnable   要执行的任务
     */
    public static void executeWithMDC(Map<String, String> mdcContext, Runnable runnable) {
        executeWithMDC(mdcContext, () -> {
            runnable.run();
            return null;
        });
    }

    /**
     * 获取当前线程的MDC上下文副本
     *
     * @return MDC上下文映射，如果没有则返回null
     */
    public static Map<String, String> getCurrentMDCContext() {
        return MDC.getCopyOfContextMap();
    }

    private static Map<String, String> buildNewContextMap(Map<String, String> contextMap) {
        if (contextMap == null) {
            return null;
        }
        String traceId = contextMap.get("traceId");
        if (traceId != null) {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            String[] split = StrUtil.split(traceId, ".");
            String newTraceId = split[0] + "." + uuid.substring(0, 16);
            contextMap.put("traceId", newTraceId);
        }
        return contextMap;
    }

}
