package com.fast.framework.thread;

import com.fast.framework.thread.mdc.MdcTaskDecorator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 导出 线程池配置
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@EnableAsync
@Configuration
public class ExportThreadExecutor {

    public static final String NAME = "exportThreadPoolExecutor";
    private static final int corePoolSize = 2; // 核心线程池大小
    private static final int maxPoolSize = 2; // 最大可创建的线程数
    private static final int queueCapacity = 20; // 队列最大长度
    private static final int keepAliveSeconds = 500; // 线程空闲时间

    @Bean(NAME)
    public Executor exportThreadPoolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(maxPoolSize);
        executor.setCorePoolSize(corePoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("exportThreadExecutor-");
        // CALLER_RUNS：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 注入上下文装饰器
        executor.setTaskDecorator(new MdcTaskDecorator());
        executor.initialize();
        return executor;
    }

}
