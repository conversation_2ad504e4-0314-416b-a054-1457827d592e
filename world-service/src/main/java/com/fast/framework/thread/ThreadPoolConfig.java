package com.fast.framework.thread;

import com.fast.framework.thread.mdc.MdcTaskDecorator;
import com.fast.framework.thread.mdc.MdcThreadFactory;
import com.fast.utils.Threads;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 *
 * <AUTHOR>
 **/
@Slf4j
@EnableAsync
@Configuration
public class ThreadPoolConfig implements AsyncConfigurer {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final int corePoolSize = 150; // 核心线程池大小
    private static final int maxPoolSize = 300; // 最大可创建的线程数
    private static final int queueCapacity = 19999; // 队列最大长度
    private static final int keepAliveSeconds = 900; // 线程空闲时间

    @Bean(name = "threadPoolTaskExecutor")
    public Executor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(maxPoolSize);
        executor.setCorePoolSize(corePoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("threadPool-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());// 由调用线程（提交任务的线程）处理
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 注入上下文装饰器
        executor.setTaskDecorator(new MdcTaskDecorator());
        executor.initialize();
        return executor;
    }

    @Override
    public Executor getAsyncExecutor() {
        return threadPoolTaskExecutor();
    }

    /**
     * 异常处理
     *
     * @return
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> {
            List<String> paramsStr = new ArrayList<>();
            for (Object param : params) {
                try {
                    String s = objectMapper.writeValueAsString(param);
                    paramsStr.add(s);
                } catch (JsonProcessingException e) {
                    log.error("执行异步任务解析参数错误", e);
                }
            }
            log.error("执行异步任务出错 {}，params: {}", method, paramsStr, ex);
        };
    }

    /**
     * 执行周期性或定时任务
     */
    @Bean(name = "scheduledExecutorService")
    protected ScheduledExecutorService scheduledExecutorService() {
        return new ScheduledThreadPoolExecutor(100
                , new BasicThreadFactory.Builder().wrappedFactory(new MdcThreadFactory()).namingPattern("schedulePool-%d").daemon(true).build()
                , new ThreadPoolExecutor.CallerRunsPolicy()) {
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                Threads.printException(r, t);
            }
        };
    }
}
