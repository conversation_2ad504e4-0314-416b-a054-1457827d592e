package com.fast.framework.thread.mdc;

import java.util.concurrent.*;

/**
 * 带有MDC的定时任务线程池，支持 scheduleXXX 方法的上下文传递
 * Created by SongFei on 2025/04/10.
 */
public class MdcScheduledThreadPoolExecutor extends ScheduledThreadPoolExecutor {

    public MdcScheduledThreadPoolExecutor(int corePoolSize, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, threadFactory, handler);
    }

    @Override
    public ScheduledFuture<?> schedule(Runnable command, long delay, TimeUnit unit) {
        return super.schedule(MdcThreadUtil.wrap(command), delay, unit);
    }

    @Override
    public <V> ScheduledFuture<V> schedule(Callable<V> callable, long delay, TimeUnit unit) {
        return super.schedule(MdcThreadUtil.wrap(callable), delay, unit);
    }

    @Override
    public ScheduledFuture<?> scheduleAtFixedRate(Runnable command, long initialDelay, long period, TimeUnit unit) {
        return super.scheduleAtFixedRate(MdcThreadUtil.wrap(command), initialDelay, period, unit);
    }

    @Override
    public ScheduledFuture<?> scheduleWithFixedDelay(Runnable command, long initialDelay, long delay, TimeUnit unit) {
        return super.scheduleWithFixedDelay(MdcThreadUtil.wrap(command), initialDelay, delay, unit);
    }

}
