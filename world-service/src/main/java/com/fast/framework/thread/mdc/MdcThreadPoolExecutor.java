package com.fast.framework.thread.mdc;

import java.util.concurrent.*;

/**
 * 带有MDC的线程池，自动包装所有提交的任务
 * Created by SongFei on 2025/04/10.
 */
public class MdcThreadPoolExecutor extends ThreadPoolExecutor {

    public MdcThreadPoolExecutor(int corePoolSize,
                                 int maximumPoolSize,
                                 long keepAliveTime,
                                 TimeUnit unit,
                                 BlockingQueue<Runnable> workQueue,
                                 ThreadFactory threadFactory,
                                 RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }

    @Override
    public void execute(Runnable command) {
        super.execute(MdcThreadUtil.wrap(command));
    }

    @Override
    public Future<?> submit(Runnable task) {
        return super.submit(MdcThreadUtil.wrap(task));
    }

    @Override
    public <T> Future<T> submit(Runnable task, T result) {
        return super.submit(MdcThreadUtil.wrap(task), result);
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(MdcThreadUtil.wrap(task));
    }

}