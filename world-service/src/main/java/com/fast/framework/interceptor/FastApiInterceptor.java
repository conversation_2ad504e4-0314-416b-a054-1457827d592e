package com.fast.framework.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.fast.annotation.Authority;
import com.fast.annotation.LogVisit;
import com.fast.constant.RedisVar;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.SysTypeEnum;
import com.fast.mapper.log.FastLogVisitMapper;
import com.fast.po.log.FastLogVisitPO;
import com.fast.utils.*;
import com.fast.utils.encryption.Md5Util;
import com.fast.utils.redis.RedisLauUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.thread.*;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

/**
 * 自定义拦截器
 */
@Component
public class FastApiInterceptor implements HandlerInterceptor {
    @Autowired
    private FastLogVisitMapper fastLogVisitMapper;
	
    private static final Logger log = LoggerFactory.getLogger(FastApiInterceptor.class);
    private static final String[] whiteUrls = {"/login", "/nologin/", "/apidoc/", "/wap/", "/wxAuth/"};

    /**
     * 在业务处理器处理请求之前被调用 如果返回false 从当前的拦截器往回执行所有拦截器的afterCompletion(),再退出拦截器链
     * <p>
     * 如果返回true 执行下一个拦截器,直到所有的拦截器都执行完毕 再执行被拦截的Controller 然后进入拦截器链,
     * 从最后一个拦截器往回执行所有的postHandle() 接着再从最后一个拦截器往回执行所有的afterCompletion()
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // 请求开始释放线程变量,防止数据没有被释放,导致线程变量被污染
            MemberIdContext.clear();
            DramaIdContext.clear();
            ContentTypeContext.clear();
            FastUserContext.clear();
            SysTypeContext.clear();

            SysTypeContext.setSystemType(SysTypeEnum.BACKEND.getCode());
            
            String url = request.getRequestURL().toString().toLowerCase();
            // 判断access_token
            String accessToken = ComUtil.getAccessToken(request);
            // get入参
            Map<String, String> paramsMap = ComUtil.readRequestBodyToMap(request);

            String ip = LinkUtil.getRemoteAddr(request);
            StringBuilder noRepReqBuf = new StringBuilder();
            noRepReqBuf.append(ip);
            noRepReqBuf.append(accessToken);
            noRepReqBuf.append(request.getRequestURL());
            noRepReqBuf.append(JSONObject.toJSON(paramsMap));
            // logger.info(noRepReqBuf.toString());
            if (StaticVar.SYSTEM_USE_TYPE_DRAMA == 0 && StaticVar.LIULINGYI_PLATFORM == 1) {
                String keyReq = Md5Util.getMD5(noRepReqBuf.toString());
                long incr = RedisLauUtil.incr(keyReq, 500L);// 相同请求放缓存
                if (incr > 1) {
                    log.error("请求频率过高，ip:{};url:{};access_token:{};params:{}", ip, request.getRequestURL(), accessToken, paramsMap);
                    // 报系统错误，访问频率过快
                    ResultVO<?> vo = new ResultVO<>();
                    vo.setCode(500);
                    vo.setMessage("相同请求，1秒内勿重复发起，稍等下...");
                    ComUtil.sendJsonMessage(response, vo);
                    return false;
                }
            }

            log.info("REQUEST_URL: {}; access_token: {}; PARAMETER: {}", request.getRequestURL(), accessToken, paramsMap.get("body"));
            boolean logoutFlag = false;
            HttpSession session = request.getSession();
            SessionVO sessionVO = (SessionVO) session.getAttribute(StaticVar.SESSION_LOGIN_USER);
            // log.info("sessionVo:{},url:{}", sessionVO, request.getRequestURL());
            if (sessionVO == null && StrUtil.hasText(accessToken)) {
                String key = StaticVar.ACCESS_TOKEN_PRE + accessToken;
                String redisValue = RedisUtil.get(RedisVar.REDIS_TOKEN_DB, key);
                if (StrUtil.hasText(redisValue)) {
                    sessionVO = JsonUtil.toJavaObject(redisValue, SessionVO.class);
                    if (sessionVO != null) {
                        long now = System.currentTimeMillis();
                        if(sessionVO.getLoginH5Flag() != null && sessionVO.getLoginH5Flag() == 1){
                        	// 达人H5不处理4点过期
                        }else{
	                        // 判断是否需要刷新token创建时间
	                        if (sessionVO.getAccessTokenCreateTime() == null) {
	                            sessionVO.setAccessTokenCreateTime(now);
	                            RedisUtil.set(RedisVar.REDIS_TOKEN_DB, key, JsonUtil.toString(sessionVO), StaticVar.getAdminTokenExp(now));
	                        } else {
	                        		// 兼容之前线上数据 设置token过期时间为登录时间的第二天
		                            if (StaticVar.getAdminTokenExp(sessionVO.getAccessTokenCreateTime()) < 0) {
		                                // token过期
                                        ComUtil.sendJsonMessage(response, ResultVO.error(StaticCode.LOGIN_EXPIRE, StaticStr.USER_LOGIN_HAS_EXPIRED));
		                                log.info("登录信息已过期,请重新登录");
		                                return false;
		                            } else {
		                                	RedisUtil.set(RedisVar.REDIS_TOKEN_DB, key, JsonUtil.toString(sessionVO), StaticVar.getAdminTokenExp(sessionVO.getAccessTokenCreateTime()));
		                            }
	                        }
                        }
                        request.getSession().setAttribute(StaticVar.SESSION_LOGIN_USER, sessionVO);
                        if (sessionVO.getAccessTokenCreateTime() != null) {
                            // 1.处理员工账号的修改
                            String userKey = StaticVar.STAFF_ACCOUNT_CHANGED + sessionVO.getUserId();
                            redisValue = RedisUtil.get(RedisVar.REDIS_TOKEN_DB, userKey);
                            if (StrUtil.hasText(redisValue)) {
                                long changeTime = Long.parseLong(redisValue);
                                if (changeTime >= sessionVO.getAccessTokenCreateTime()) {
                                    // 用户登录信息过期
                                    ResultVO<?> vo = new ResultVO<>();
                                    vo.setCode(StaticCode.LOGIN_EXPIRE);
                                    vo.setMessage("账号信息已变更,请重新登录");
                                    ComUtil.sendJsonMessage(response, vo);
                                    log.info("账号信息已变更,请重新登录");
                                    RedisUtil.del(RedisVar.REDIS_TOKEN_DB, key);
                                    return false;
                                }
                            }
                            // 2.处理分销商信息的修改
                            userKey = StaticVar.RETAIL_ACCOUNT_CHANGED + sessionVO.getRetailId();
                            redisValue = RedisUtil.get(RedisVar.REDIS_TOKEN_DB, userKey);
                            if (StrUtil.hasText(redisValue)) {
                                long changeTime = Long.parseLong(redisValue);
                                if (changeTime >= sessionVO.getAccessTokenCreateTime()) {
                                    // 信息过期
                                    ResultVO<?> vo = new ResultVO<>();
                                    vo.setCode(StaticCode.LOGIN_EXPIRE);
                                    vo.setMessage("分销商信息已变更,请重新登录");
                                    ComUtil.sendJsonMessage(response, vo);
                                    log.error("分销商信息已变更,请重新登录");
                                    RedisUtil.del(RedisVar.REDIS_TOKEN_DB, key);
                                    return false;
                                }
                            }
                        }
                        // 角色或者权限变更的用户，强制下线
                        String logoutUserId = RedisUtil.get(StaticVar.CHANGE_ROLE_ID + ":" + sessionVO.getUserId());
                        if (StrUtil.isNotEmpty(logoutUserId)) {
                            logoutFlag = true;
                        }
                    }
                }
            }

            // 如果存在sessionVO=>处理业务数据
            if (sessionVO != null) {
                if (sessionVO.getMemberId() != null) {
                    MemberIdContext.setMemberId(sessionVO.getMemberId());
                    if (StrUtil.notBlank(sessionVO.getOpenid())) {
                        // 业务域名防封监控-删除埋点数据
                        RedisUtil.del(StaticVar.H5_DOMAIN_MONITOR + sessionVO.getOpenid());
                    }
                }
                ContentTypeContext.setContentType(sessionVO.getContentType());
                FastUserContext.setUser(sessionVO);

                if (sessionVO.getRetailId() == null) {
                    sessionVO.setRetailId(0);
                    String key = StaticVar.ACCESS_TOKEN_PRE + accessToken;
                    RedisUtil.set(RedisVar.REDIS_TOKEN_DB, key, JsonUtil.toString(sessionVO), StaticVar.getAdminTokenExp(sessionVO.getAccessTokenCreateTime()));
                }
                // log.info("REQUEST_USER:retailId:{}; miniId:{}; officialId:{}; userId:{}; memberId:{}",
                //         sessionVO.getRetailId(), sessionVO.getMiniId(), sessionVO.getOfficialId(), sessionVO.getUserId(), sessionVO.getMemberId());
            }
            // 添加访问日志，保存访问记录
            if (handler instanceof HandlerMethod) {
                Method method = ((HandlerMethod) handler).getMethod();
                LogVisit logVisit = method.getAnnotation(LogVisit.class);
                if (logVisit != null && StrUtil.isNotEmpty(logVisit.name())) {
                    FastLogVisitPO lvPO = new FastLogVisitPO();
                    if (sessionVO != null && sessionVO.getUserId() != null) {
                        lvPO.setActorId(sessionVO.getUserId());
                    }
                    lvPO.setCreateTime(DateUtil.getNowDate());
                    lvPO.setMethod(request.getRequestURL().toString());
                    lvPO.setIp(ip);
                    lvPO.setName(logVisit.name());
                    String params = JsonUtil.toString(paramsMap);
                    if (StrUtil.isNotEmpty(params) && params.length() > 1900) {
                        params = params.substring(0, 1900);
                    }
                    lvPO.setParams(params);
                    if (sessionVO != null) {
                        lvPO.setActorName(sessionVO.getUserName());
                    }
                    fastLogVisitMapper.insertSelective(lvPO);
                }
            }
            
            // 过滤放行一些不需要登录的页面
            for (String key : whiteUrls) {
                if (url.contains(key)) {
                    log.info("nologin:key={}", key);
                    return true;
                }
            }
            // 终极判断
            if (sessionVO == null) {
                ComUtil.sendJsonMessage(response, ResultVO.error(StaticCode.LOGIN_EXPIRE, StaticStr.USER_LOGIN_HAS_EXPIRED));
                log.info("登录信息已过期,请重新登录");
                return false;
            }
            // H5达人token持续加热，最长30天
            if (sessionVO.getLoginH5Flag() != null && sessionVO.getLoginH5Flag() == 1) {
                Long lastHotTime = sessionVO.getLoginLastHotTime();
                Long nowTimeLong = DateUtil.getNowTimeStamp();
                if (lastHotTime == null) {
                    sessionVO.setLoginLastHotTime(nowTimeLong);
                }
                // 1个小时前加热或者无加热时间的情况，进行加热
                if (nowTimeLong - sessionVO.getLoginLastHotTime() > 1000 * 60 * 60 || lastHotTime == null) {
                    RedisUtil.set(RedisVar.REDIS_TOKEN_DB, StaticVar.ACCESS_TOKEN_PRE + sessionVO.getAccessToken(), JSONObject.toJSONString(sessionVO), 60 * 60 * 48);
                }
            }
            // 账号未登录或者角色权限变更用户强制下线
            if (logoutFlag) {
                ComUtil.sendJsonMessage(response, ResultVO.error(StaticCode.LOGIN_EXPIRE, StaticStr.USER_LOGIN_HAS_EXPIRED));
                log.info("用户角色/权限变更强制下线");
                RedisUtil.del(StaticVar.CHANGE_ROLE_ID + ":" + sessionVO.getUserId());
                return false;
            }

            // 权限判断
            if (handler instanceof HandlerMethod) {
                Method method = ((HandlerMethod) handler).getMethod();
                Authority authority = method.getAnnotation(Authority.class);
                if (authority != null && StrUtil.hasText(authority.authIds())) {
                    String allMenuId = sessionVO.getAllMenuId();
                    if (StringUtil.isNotEmpty(allMenuId)) {
                        List<String> allMenuIds = CollUtil.parseStr2List(allMenuId);
                        String[] menuIds = authority.authIds().split(",");
                        for (String menuId : menuIds) {
                            if (allMenuIds.contains(menuId)) {
                                return true;
                            }
                        }
                    }
                    ComUtil.sendJsonMessage(response, ResultVO.error(StaticCode.NOT_AUTH, StaticStr.NO_PERMISSION));
                    log.error("{}:无权操作", method.getName());
                    return false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return true;
    }

    /**
     * 前提:preHandle返回true
     * <p>
     * Controller方法处理完之后，DispatcherServlet进行视图的渲染之前
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
    }

    /**
     * 前提:preHandle返回true
     * <p>
     * DispatcherServlet进行视图的渲染之后
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        try {
            MemberIdContext.clear();
            DramaIdContext.clear();
            ContentTypeContext.clear();
            FastUserContext.clear();
            if (request.getSession() != null) {
                request.getSession().invalidate();
            }
        } catch (Exception ignored) {
        }
    }

    public FastApiInterceptor() {
    }
}
