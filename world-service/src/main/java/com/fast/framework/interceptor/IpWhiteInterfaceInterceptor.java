package com.fast.framework.interceptor;

import com.fast.constant.StaticVar;
import com.fast.utils.IPUtil;
import com.fast.utils.thread.MemberIdContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class IpWhiteInterfaceInterceptor implements HandlerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(IpWhiteInterfaceInterceptor.class);
    private static final String[] whiteUrls = {"/middleground"};

    /**
     * 在业务处理器处理请求之前被调用 如果返回false 从当前的拦截器往回执行所有拦截器的afterCompletion(),再退出拦截器链
     * <p>
     * 如果返回true 执行下一个拦截器,直到所有的拦截器都执行完毕 再执行被拦截的Controller 然后进入拦截器链,
     * 从最后一个拦截器往回执行所有的postHandle() 接着再从最后一个拦截器往回执行所有的afterCompletion()
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {

            String url = request.getRequestURL().toString().toLowerCase();
            for (String key : whiteUrls) {
                if (!url.contains(key)) {
                    return true;
                }
            }
            String hostIp = IPUtil.getIpAddr(request);
            logger.info("中台接口hostIp:{}", hostIp);
            return StaticVar.MIDDLE_GROUND_WHITE_LIST.contains(hostIp);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }
        return true;
    }

    /**
     * 前提:preHandle返回true
     * Controller方法处理完之后，DispatcherServlet进行视图的渲染之前
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
    }

    /**
     * 前提:preHandle返回true
     * <p>
     * DispatcherServlet进行视图的渲染之后
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        try {
            MemberIdContext.clear();
            if (request.getSession() != null) {
                request.getSession().invalidate();
            }
        } catch (Exception ignored) {
        }
    }

}
