package com.fast.framework.interceptor;

import com.alibaba.nacos.common.utils.ConcurrentHashSet;
import com.fast.annotation.Authority;
import com.fast.constant.RedisVar;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.enums.LanguageEnum;
import com.fast.enums.SysTypeEnum;
import com.fast.po.setting.FastSettingSystemPO;
import com.fast.service.common.FastOnlineService;
import com.fast.service.monitor.FastMonitorInterfaceService;
import com.fast.service.setting.FastSettingSystemService;
import com.fast.utils.*;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.thread.*;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 自定义拦截器-mini专用
 */
@Component
public class MiniApiInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(MiniApiInterceptor.class);
    private static final String[] whiteUrls = {"/login", "/nologin/", "/apidoc/", "/wap/", "/wxAuth/"};
    private static final Set<String> deviceSet = new ConcurrentHashSet<>();
    private static Long lastUpdateDeviceSetTime = 0L;

    @Autowired
    private FastOnlineService onlineService;
    @Autowired
    private FastMonitorInterfaceService fastMonitorInterfaceService;
    @Autowired
    private FastSettingSystemService fastSettingSystemService;

    /**
     * 在业务处理器处理请求之前被调用 如果返回false 从当前的拦截器往回执行所有拦截器的afterCompletion(),再退出拦截器链
     * <p>
     * 如果返回true 执行下一个拦截器,直到所有的拦截器都执行完毕 再执行被拦截的Controller 然后进入拦截器链,
     * 从最后一个拦截器往回执行所有的postHandle() 接着再从最后一个拦截器往回执行所有的afterCompletion()
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // 请求开始释放线程变量,防止数据没有被释放,导致线程变量被污染
            MemberIdContext.clear();
            DramaIdContext.clear();
            ContentTypeContext.clear();
            LanguageContext.clear();
            SysTypeContext.clear();

            // i18n
            String lang = request.getHeader("lang");
            if (StrUtil.isBlank(lang)) {
                lang = LanguageEnum.ENGLISH.getCode();
            }
            LanguageContext.setLanguageType(lang);
            SysTypeContext.setSystemType(SysTypeEnum.APP.getCode());

            String url = request.getRequestURL().toString().toLowerCase();
            // 判断access_token
            String accessToken = ComUtil.getAccessToken(request);

            Map<String, String> paramsMap = ComUtil.readRequestBodyToMap(request);
            logger.info("REQUEST_URL: {}; access_token: {}; lang: {}; PARAMETER: {}", request.getRequestURL(), accessToken, lang, paramsMap.get("body"));

            HttpSession session = request.getSession();
            SessionVO sessionVO = (SessionVO) session.getAttribute(StaticVar.SESSION_LOGIN_USER);
            if (sessionVO == null && StrUtil.hasText(accessToken)) {
                String key = StaticVar.ACCESS_TOKEN_PRE + accessToken;
                String redisValue = RedisUtil.get(RedisVar.REDIS_TOKEN_DB, key);
                if (StrUtil.hasText(redisValue)) {
                    sessionVO = JsonUtil.toJavaObject(redisValue, SessionVO.class);
                    if (sessionVO != null) {
                        long now = System.currentTimeMillis();
                        // 判断是否需要刷新token
                        if (sessionVO.getAccessTokenCreateTime() == null || (now - sessionVO.getAccessTokenCreateTime()) / 1000 > StaticVar.ACCESS_APP_TOKEN_EXP / 2) {
                            sessionVO.setAccessTokenCreateTime(now);
                            RedisUtil.set(RedisVar.REDIS_TOKEN_DB, key, JsonUtil.toString(sessionVO), StaticVar.ACCESS_APP_TOKEN_EXP);
                        }
                        request.getSession().setAttribute(StaticVar.SESSION_LOGIN_USER, sessionVO);
                    }
                }
            }
            // 判断接口访问时长
            if (getDeviceSet().contains(StaticVar.DEVICE_NUM)) {
                try {
                    fastMonitorInterfaceService.addInterfaceCount(request);
                } catch (Exception ignored) {
                }
            }
            // 如果存在sessionVO=>处理业务数据
            if (sessionVO != null) {
                if (sessionVO.getMemberId() != null) {
                    MemberIdContext.setMemberId(sessionVO.getMemberId());
                    if (StrUtil.notEmpty(sessionVO.getOpenid())) {
                        // 业务域名防封监控-删除埋点数据
                        RedisUtil.del(StaticVar.H5_DOMAIN_MONITOR + sessionVO.getOpenid());
                    }
                    onlineService.addOnline(sessionVO);
                }
                ContentTypeContext.setContentType(sessionVO.getContentType());

                if (sessionVO.getRetailId() == null) {
                    sessionVO.setRetailId(0);
                    String key = StaticVar.ACCESS_TOKEN_PRE + accessToken;
                    RedisUtil.set(RedisVar.REDIS_TOKEN_DB, key, JsonUtil.toString(sessionVO), StaticVar.ACCESS_APP_TOKEN_EXP);
                }
                // logger.info("REQUEST_USER:retailId:{}; miniId:{}; officialId:{}; userId:{}; memberId:{}",
                //         sessionVO.getRetailId(), sessionVO.getMiniId(), sessionVO.getOfficialId(), sessionVO.getUserId(), sessionVO.getMemberId());
            }

            // 过滤放行一些不需要登录的页面
            for (String key : whiteUrls) {
                if (url.contains(key)) {
                    // logger.info("nologin:key={}", key);
                    return true;
                }
            }

            // 终极判断
            if (sessionVO == null) {
                ComUtil.sendJsonMessage(response, ResultVO.error(StaticCode.LOGIN_EXPIRE, StaticStr.USER_LOGIN_HAS_EXPIRED));
                logger.info("登录信息已过期,请重新登录");
                return false;
            }

            // 权限判断
            if (handler instanceof HandlerMethod) {
                Method method = ((HandlerMethod) handler).getMethod();
                Authority authority = method.getAnnotation(Authority.class);
                if (authority != null && StrUtil.hasText(authority.authIds())) {
                    String allMenuId = sessionVO.getAllMenuId();
                    if (StringUtil.isNotEmpty(allMenuId)) {
                        List<String> allMenuIds = CollUtil.parseStr2List(allMenuId);
                        String[] menuIds = authority.authIds().split(",");
                        for (String menuId : menuIds) {
                            if (allMenuIds.contains(menuId)) {
                                return true;
                            }
                        }
                    }
                    ComUtil.sendJsonMessage(response, ResultVO.error(StaticCode.NOT_AUTH, StaticStr.NO_PERMISSION));
                    logger.error("{}:无权操作", method.getName());
                    return false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }

        return true;
    }

    private Set<String> getDeviceSet() {
        // 每隔60秒，同步一次数据库
        if (DateUtil.getTimeNowUnix() - lastUpdateDeviceSetTime > 60) {
            deviceSet.clear();
            lastUpdateDeviceSetTime = DateUtil.getTimeNowUnix();
            FastSettingSystemPO ssPO = fastSettingSystemService.queryByCode("mini_interface_device");
            if (ssPO != null) {
                String[] deviceArray = ssPO.getContent().split(",");
                Collections.addAll(deviceSet, deviceArray);
            }
        }
        return deviceSet;
    }

    /**
     * 前提:preHandle返回true
     * <p>
     * Controller方法处理完之后，DispatcherServlet进行视图的渲染之前
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
    }

    /**
     * 前提:preHandle返回true
     * <p>
     * DispatcherServlet进行视图的渲染之后
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        try {
            Long memberId = MemberIdContext.getMemberId();
            MemberIdContext.clear();
            DramaIdContext.clear();
            ContentTypeContext.clear();
            LanguageContext.clear();
            SysTypeContext.clear();
            
            if (request.getSession() != null) {
                request.getSession().invalidate();
            }
            if (getDeviceSet().contains(StaticVar.DEVICE_NUM)) {
                Object startTimeObj = request.getAttribute(StaticVar.MONITOR_INTERFACE_TIME);
                if (startTimeObj != null) {
                    long startTime = (Long) startTimeObj;
                    Object mapKey = request.getAttribute(StaticVar.MONITOR_INTERFACE_KEY);
                    if (mapKey != null) {
                        fastMonitorInterfaceService.updateInterfaceCount(memberId, startTime, mapKey.toString(), request.getRequestURI());
                    }
                }
            }
        } catch (Exception ignored) {
        }
    }

    public MiniApiInterceptor() {
    }
}
