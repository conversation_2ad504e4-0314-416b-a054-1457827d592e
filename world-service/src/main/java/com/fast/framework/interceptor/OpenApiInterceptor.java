package com.fast.framework.interceptor;

import com.alibaba.fastjson.JSON;
import com.fast.annotation.ApiDesc;
import com.fast.constant.RedisVar;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.utils.ComUtil;
import com.fast.utils.DateUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.fast.vo.openapi.InvokeTimesVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;

/**
 * 自定义拦截器
 */
@Component
public class OpenApiInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(OpenApiInterceptor.class);
    private static final String[] whiteUrls = {"/nologin/", "/login"};

    /**
     * 在业务处理器处理请求之前被调用 如果返回false 从当前的拦截器往回执行所有拦截器的afterCompletion(),再退出拦截器链
     * 如果返回true 执行下一个拦截器,直到所有的拦截器都执行完毕 再执行被拦截的Controller 然后进入拦截器链,
     * 从最后一个拦截器往回执行所有的postHandle() 接着再从最后一个拦截器往回执行所有的afterCompletion()
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            String url = request.getRequestURL().toString().toLowerCase();
            // 过滤放行一些不需要登录的页面
            for (String key : whiteUrls) {
                if (url.contains(key)) {
                    logger.info("nologin:key={};url:{}", key, request.getRequestURL());
                    return true;
                }
            }
            // 判断access_token
            String accessToken = ComUtil.getAccessToken(request);
            logger.info("REQUEST_URL: {}; access_token: {}; PARAMETER: {}", request.getRequestURL(), accessToken, ComUtil.readRequestBodyToMap(request));
            HttpSession session = request.getSession();
            SessionVO sessionVO = (SessionVO) session.getAttribute(StaticVar.SESSION_LOGIN_USER);
            if (sessionVO == null && StrUtil.hasText(accessToken)) {
                String key = StaticVar.ACCESS_OPENAPI_TOKEN_PRE + accessToken;
                String redisValue = RedisUtil.get(RedisVar.REDIS_TOKEN_DB, key);
                if (StrUtil.hasText(redisValue)) {
                    sessionVO = JSON.parseObject(redisValue, SessionVO.class);
                    if (sessionVO != null) {
                        long now = System.currentTimeMillis();
                        // 判断是否需要刷新token
                        if (sessionVO.getAccessTokenCreateTime() == null || (now - sessionVO.getAccessTokenCreateTime()) / 1000 > StaticVar.ACCESS_TOKEN_EXP / 2) {
                            sessionVO.setAccessTokenCreateTime(now);
                            RedisUtil.set(RedisVar.REDIS_TOKEN_DB, key, JsonUtil.toString(sessionVO), StaticVar.ACCESS_TOKEN_EXP);
                        }
                        request.getSession().setAttribute(StaticVar.SESSION_LOGIN_USER, sessionVO);
                    }
                }
            }
            if (sessionVO == null) {
                ComUtil.sendJsonMessage(response, ResultVO.error(StaticCode.LOGIN_EXPIRE, StaticStr.USER_LOGIN_HAS_EXPIRED));
                return false;
            }
            // 限制调用频次
            if (handler instanceof HandlerMethod) {
                Method method = ((HandlerMethod) handler).getMethod();
                ApiDesc apiDesc = method.getAnnotation(ApiDesc.class);
                if (apiDesc != null && StrUtil.hasText(apiDesc.name()) && StrUtil.hasText(apiDesc.times())) {
                    String timesKey = StaticVar.OPENAPI_TIMES + apiDesc.name() + "_" + sessionVO.getRetailId();
                    int times = Integer.parseInt(apiDesc.times());
                    InvokeTimesVO timeVO = RedisUtil.getObject(timesKey, InvokeTimesVO.class, 100);
                    long timeNowUnix = DateUtil.getTimeNowUnix();
                    int leftTimesCount = times * 3;// 5秒次数
                    if (timeVO == null) {
                        timeVO = new InvokeTimesVO();
                        timeVO.setTimes(leftTimesCount - 1);
                        timeVO.setToTime(timeNowUnix + 10);
                        RedisUtil.setObject(timesKey, timeVO, 60 * 60);
                    } else {
                        if (timeNowUnix > timeVO.getToTime()) {
                            // 需要重置剩余次数
                            timeVO.setTimes(leftTimesCount - 1);
                            timeVO.setToTime(timeNowUnix + 10);
                            RedisUtil.setObject(timesKey, timeVO, 60 * 60);
                        } else {
                            Integer leftCount = timeVO.getTimes();
                            if (leftCount < 1) {
                                ComUtil.sendJsonMessage(response, ResultVO.error(StaticCode.LOGIN_EXPIRE, StaticStr.REQUEST_FREQUENCY_TOO_HIGH));
                                return false;
                            }
                            leftCount = leftCount - 1;
                            timeVO.setTimes(leftCount);
                            RedisUtil.setObject(timesKey, timeVO, 60 * 60);
                        }
                    }
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }
        return false;
    }

    /**
     * 前提:preHandle返回true
     * Controller方法处理完之后，DispatcherServlet进行视图的渲染之前
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        // TODO:
    }

    /**
     * 前提:preHandle返回true
     * DispatcherServlet进行视图的渲染之后
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // TODO:
    }
}
