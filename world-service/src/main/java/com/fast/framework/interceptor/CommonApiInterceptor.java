package com.fast.framework.interceptor;

import com.fast.annotation.Authority;
import com.fast.constant.RedisVar;
import com.fast.constant.StaticCode;
import com.fast.constant.StaticStr;
import com.fast.constant.StaticVar;
import com.fast.service.common.FastOnlineService;
import com.fast.utils.CollUtil;
import com.fast.utils.ComUtil;
import com.fast.utils.JsonUtil;
import com.fast.utils.StrUtil;
import com.fast.utils.redis.RedisUtil;
import com.fast.utils.thread.ContentTypeContext;
import com.fast.utils.thread.DramaIdContext;
import com.fast.utils.thread.MemberIdContext;
import com.fast.vo.ResultVO;
import com.fast.vo.SessionVO;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.util.List;

/**
 * 自定义拦截器-通用，最开始的那个
 */
@Component
public class CommonApiInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(CommonApiInterceptor.class);
    private static final String[] whiteUrls = {"/login", "/nologin/", "/apidoc/", "/wap/", "/wxAuth/"};

    @Autowired
    private FastOnlineService onlineService;

    /**
     * 在业务处理器处理请求之前被调用 如果返回false 从当前的拦截器往回执行所有拦截器的afterCompletion(),再退出拦截器链
     * <p>
     * 如果返回true 执行下一个拦截器,直到所有的拦截器都执行完毕 再执行被拦截的Controller 然后进入拦截器链,
     * 从最后一个拦截器往回执行所有的postHandle() 接着再从最后一个拦截器往回执行所有的afterCompletion()
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // 请求开始释放线程变量,防止数据没有被释放,导致线程变量被污染
            MemberIdContext.clear();
            DramaIdContext.clear();
            ContentTypeContext.clear();

            String url = request.getRequestURL().toString().toLowerCase();
            // 判断access_token
            String accessToken = ComUtil.getAccessToken(request);
            logger.info("REQUEST_URL: {}; access_token: {}; PARAMETER: {}", request.getRequestURL(), accessToken, ComUtil.readRequestBodyToMap(request));

            HttpSession session = request.getSession();
            SessionVO sessionVO = (SessionVO) session.getAttribute(StaticVar.SESSION_LOGIN_USER);
            if (sessionVO == null && StrUtil.hasText(accessToken)) {
                String key = StaticVar.ACCESS_TOKEN_PRE + accessToken;
                String redisValue = RedisUtil.get(RedisVar.REDIS_TOKEN_DB, key);
                if (StrUtil.hasText(redisValue)) {
                    sessionVO = JsonUtil.toJavaObject(redisValue, SessionVO.class);
                    if (sessionVO != null) {
                        long now = System.currentTimeMillis();
                        // 判断是否需要刷新token
                        if (sessionVO.getAccessTokenCreateTime() == null || (now - sessionVO.getAccessTokenCreateTime()) / 1000 > StaticVar.ACCESS_TOKEN_EXP / 2) {
                            sessionVO.setAccessTokenCreateTime(now);
                            RedisUtil.set(RedisVar.REDIS_TOKEN_DB, key, JsonUtil.toString(sessionVO), StaticVar.ACCESS_TOKEN_EXP);
                        }
                        request.getSession().setAttribute(StaticVar.SESSION_LOGIN_USER, sessionVO);

                        if (sessionVO.getAccessTokenCreateTime() != null) {
                            // 1.处理员工账号的修改
                            String userKey = StaticVar.STAFF_ACCOUNT_CHANGED + sessionVO.getUserId();
                            redisValue = RedisUtil.get(RedisVar.REDIS_TOKEN_DB, userKey);
                            if (StrUtil.hasText(redisValue)) {
                                long changeTime = Long.parseLong(redisValue);
                                if (changeTime >= sessionVO.getAccessTokenCreateTime()) {
                                    // 用户登录信息过期
                                    ResultVO<?> vo = new ResultVO<>();
                                    vo.setCode(StaticCode.LOGIN_EXPIRE);
                                    vo.setMessage("账号信息已变更,请重新登录");
                                    ComUtil.sendJsonMessage(response, vo);
                                    logger.info("账号信息已变更,请重新登录");
                                    RedisUtil.del(RedisVar.REDIS_TOKEN_DB, key);
                                    return false;
                                }
                            }
                            // 2.处理分销商信息的修改
                            userKey = StaticVar.RETAIL_ACCOUNT_CHANGED + sessionVO.getRetailId();
                            redisValue = RedisUtil.get(RedisVar.REDIS_TOKEN_DB, userKey);
                            if (StrUtil.hasText(redisValue)) {
                                long changeTime = Long.parseLong(redisValue);
                                if (changeTime >= sessionVO.getAccessTokenCreateTime()) {
                                    // 信息过期
                                    ResultVO<?> vo = new ResultVO<>();
                                    vo.setCode(StaticCode.LOGIN_EXPIRE);
                                    vo.setMessage("分销商信息已变更,请重新登录");
                                    ComUtil.sendJsonMessage(response, vo);
                                    logger.error("分销商信息已变更,请重新登录");
                                    RedisUtil.del(RedisVar.REDIS_TOKEN_DB, key);
                                    return false;
                                }
                            }
                        }
                    }
                }
            }

            // 如果存在sessionVO=>处理业务数据
            if (sessionVO != null) {
                if (sessionVO.getMemberId() != null) {
                    MemberIdContext.setMemberId(sessionVO.getMemberId());
                    if (StrUtil.notBlank(sessionVO.getOpenid())) {
                        // 业务域名防封监控-删除埋点数据
                        RedisUtil.del(StaticVar.H5_DOMAIN_MONITOR + sessionVO.getOpenid());
                    }
                    onlineService.addOnline(sessionVO);
                }
                ContentTypeContext.setContentType(sessionVO.getContentType());

                if (sessionVO.getRetailId() == null) {
                    sessionVO.setRetailId(0);
                    String key = StaticVar.ACCESS_TOKEN_PRE + accessToken;
                    RedisUtil.set(RedisVar.REDIS_TOKEN_DB, key, JsonUtil.toString(sessionVO), StaticVar.ACCESS_TOKEN_EXP);
                }
                // logger.info("REQUEST_USER:retailId:{}; miniId:{}; officialId:{}; userId:{}; memberId:{}",
                //         sessionVO.getRetailId(), sessionVO.getMiniId(), sessionVO.getOfficialId(), sessionVO.getUserId(), sessionVO.getMemberId());
            }

            // 过滤放行一些不需要登录的页面
            for (String key : whiteUrls) {
                if (url.contains(key)) {
                    logger.info("nologin:key={}", key);
                    return true;
                }
            }

            // 终极判断
            if (sessionVO == null) {
                ComUtil.sendJsonMessage(response, ResultVO.error(StaticCode.LOGIN_EXPIRE, StaticStr.USER_LOGIN_HAS_EXPIRED));
                logger.info("登录信息已过期,请重新登录");
                return false;
            }

            // 权限判断
            if (handler instanceof HandlerMethod) {
                Method method = ((HandlerMethod) handler).getMethod();
                Authority authority = method.getAnnotation(Authority.class);
                if (authority != null && StrUtil.hasText(authority.authIds())) {
                    String allMenuId = sessionVO.getAllMenuId();
                    if (StringUtil.isNotEmpty(allMenuId)) {
                        List<String> allMenuIds = CollUtil.parseStr2List(allMenuId);
                        String[] menuIds = authority.authIds().split(",");
                        for (String menuId : menuIds) {
                            if (allMenuIds.contains(menuId)) {
                                return true;
                            }
                        }
                    }
                    ComUtil.sendJsonMessage(response, ResultVO.error(StaticCode.NOT_AUTH, StaticStr.NO_PERMISSION));
                    logger.error("{}:无权操作", method.getName());
                    return false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage());
        }
        return true;
    }

    /**
     * 前提:preHandle返回true
     * <p>
     * Controller方法处理完之后，DispatcherServlet进行视图的渲染之前
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
    }

    /**
     * 前提:preHandle返回true
     * <p>
     * DispatcherServlet进行视图的渲染之后
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        try {
            MemberIdContext.clear();
            DramaIdContext.clear();
            ContentTypeContext.clear();
            if (request.getSession() != null) {
                request.getSession().invalidate();
            }
        } catch (Exception ignored) {
        }
    }

    public CommonApiInterceptor() {
    }
}
