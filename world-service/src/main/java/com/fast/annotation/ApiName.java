package com.fast.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于识别在进行controller调用的时候，标注该方法调用是否需要权限控制，需要什么样的权限的注解类。<br>
 * 该注解类一般会包括两个属性，一个是需要的权限，一个是对应的action。<br>
 * 
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(value ={ElementType.METHOD,ElementType.TYPE})
public @interface ApiName {

	public String value();
	public String[] folder();// 归属目录

}