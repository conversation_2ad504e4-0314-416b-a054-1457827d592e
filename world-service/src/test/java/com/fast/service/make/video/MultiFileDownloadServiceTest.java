package com.fast.service.make.video;

import com.fast.service.make.video.util.MultiFileDownloader;
import com.fast.service.make.video.util.MultiFileDownloader.DownloadTask;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.nio.file.Path;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 多文件下载服务测试类
 */
@SpringBootTest
public class MultiFileDownloadServiceTest {

    private MultiFileDownloadService multiFileDownloadService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        multiFileDownloadService = new MultiFileDownloadService();
    }

    @Test
    void testDownloadFilesAndZip_WithValidUrls() {
        // 准备测试数据
        List<String> urls = Arrays.asList(
            "https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/caption/SC/1754368236507838770.srt"
            // 注意：在实际测试中，建议使用稳定的测试URL
        );

        String zipFileName = "test_download.zip";
        String saveDir = tempDir.toString();

        // 执行测试
        String result = multiFileDownloadService.downloadFilesAndZip(urls, zipFileName, saveDir);

        // 验证结果
        if (result != null) {
            assertTrue(multiFileDownloadService.isZipFileExists(result));
            assertTrue(multiFileDownloadService.getZipFileSize(result) > 0);
        }
        // 注意：由于网络依赖，这个测试可能会失败，建议使用Mock
    }

    @Test
    void testDownloadFilesAndZip_WithEmptyUrls() {
        // 准备测试数据
        List<String> urls = new ArrayList<>();
        String zipFileName = "empty_test.zip";
        String saveDir = tempDir.toString();

        // 执行测试
        String result = multiFileDownloadService.downloadFilesAndZip(urls, zipFileName, saveDir);

        // 验证结果
        assertNull(result);
    }

    @Test
    void testDownloadFilesAndZip_WithNullUrls() {
        // 执行测试
        String result = multiFileDownloadService.downloadFilesAndZip(null, "test.zip", tempDir.toString());

        // 验证结果
        assertNull(result);
    }

    @Test
    void testDownloadFilesAndZip_WithUrlFileNameMap() {
        // 准备测试数据
        Map<String, String> urlFileNameMap = new HashMap<>();
        urlFileNameMap.put("https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/caption/SC/1754368236507838770.srt", "subtitle.srt");

        String zipFileName = "test_with_names.zip";
        String saveDir = tempDir.toString();

        // 执行测试
        String result = multiFileDownloadService.downloadFilesAndZip(urlFileNameMap, zipFileName, saveDir);

        // 验证结果（由于网络依赖，可能为null）
        if (result != null) {
            assertTrue(multiFileDownloadService.isZipFileExists(result));
        }
    }

    @Test
    void testDownloadVideoPackage() {
        // 准备测试数据
        String videoUrl = "https://example.com/video.mp4";
        String subtitleUrl = "https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/caption/SC/1754368236507838770.srt";
        String thumbnailUrl = "https://example.com/thumb.jpg";
        String zipFileName = "video_package.zip";
        String saveDir = tempDir.toString();

        // 执行测试
        String result = multiFileDownloadService.downloadVideoPackage(
            videoUrl, subtitleUrl, thumbnailUrl, zipFileName, saveDir
        );

        // 验证结果（由于网络依赖和URL可能无效，结果可能为null）
        // 这里主要测试方法不会抛出异常
        assertDoesNotThrow(() -> {
            multiFileDownloadService.downloadVideoPackage(
                videoUrl, subtitleUrl, thumbnailUrl, zipFileName, saveDir
            );
        });
    }

    @Test
    void testDownloadSubtitlePackage() {
        // 准备测试数据
        List<String> subtitleUrls = Arrays.asList(
            "https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/caption/SC/1754368236507838770.srt"
        );
        List<String> languages = Arrays.asList("zh");
        String zipFileName = "subtitles.zip";
        String saveDir = tempDir.toString();

        // 执行测试
        String result = multiFileDownloadService.downloadSubtitlePackage(
            subtitleUrls, languages, zipFileName, saveDir
        );

        // 验证结果
        if (result != null) {
            assertTrue(multiFileDownloadService.isZipFileExists(result));
        }
    }

    @Test
    void testIsZipFileExists() {
        // 测试不存在的文件
        assertFalse(multiFileDownloadService.isZipFileExists("nonexistent.zip"));
        assertFalse(multiFileDownloadService.isZipFileExists(null));
        assertFalse(multiFileDownloadService.isZipFileExists(""));

        // 创建一个临时文件进行测试
        try {
            File tempFile = new File(tempDir.toFile(), "test.zip");
            tempFile.createNewFile();
            
            assertTrue(multiFileDownloadService.isZipFileExists(tempFile.getAbsolutePath()));
            
            // 清理
            tempFile.delete();
        } catch (Exception e) {
            fail("创建临时文件失败: " + e.getMessage());
        }
    }

    @Test
    void testGetZipFileSize() {
        // 测试不存在的文件
        assertEquals(-1, multiFileDownloadService.getZipFileSize("nonexistent.zip"));
        assertEquals(-1, multiFileDownloadService.getZipFileSize(null));

        // 创建一个临时文件进行测试
        try {
            File tempFile = new File(tempDir.toFile(), "test.zip");
            tempFile.createNewFile();
            
            long size = multiFileDownloadService.getZipFileSize(tempFile.getAbsolutePath());
            assertEquals(0, size); // 空文件大小为0
            
            // 清理
            tempFile.delete();
        } catch (Exception e) {
            fail("创建临时文件失败: " + e.getMessage());
        }
    }

    @Test
    void testDeleteZipFile() {
        // 测试删除不存在的文件
        assertFalse(multiFileDownloadService.deleteZipFile("nonexistent.zip"));
        assertFalse(multiFileDownloadService.deleteZipFile(null));

        // 创建一个临时文件进行测试
        try {
            File tempFile = new File(tempDir.toFile(), "test_delete.zip");
            tempFile.createNewFile();
            
            assertTrue(tempFile.exists());
            assertTrue(multiFileDownloadService.deleteZipFile(tempFile.getAbsolutePath()));
            assertFalse(tempFile.exists());
            
        } catch (Exception e) {
            fail("创建临时文件失败: " + e.getMessage());
        }
    }

    @Test
    void testDownloadFilesAndZipAdvanced() {
        // 准备测试数据
        List<DownloadTask> tasks = new ArrayList<>();
        tasks.add(new DownloadTask("https://world-shanghai-test.tos-cn-shanghai.volces.com/0/2025-08/caption/SC/1754368236507838770.srt", "test.srt"));

        String zipFileName = "advanced_test.zip";
        String saveDir = tempDir.toString();
        String tempDirPath = tempDir.toString() + File.separator + "temp";
        int maxThreads = 2;

        // 执行测试
        String result = multiFileDownloadService.downloadFilesAndZipAdvanced(
            tasks, zipFileName, saveDir, tempDirPath, maxThreads
        );

        // 验证结果
        if (result != null) {
            assertTrue(multiFileDownloadService.isZipFileExists(result));
        }
    }

    @Test
    void testCreateDownloadTasks() {
        // 测试通过URL列表创建任务
        List<String> urls = Arrays.asList(
            "https://example.com/file1.txt",
            "https://example.com/file2.jpg"
        );

        List<DownloadTask> tasks = MultiFileDownloader.createDownloadTasks(urls);
        
        assertNotNull(tasks);
        assertEquals(2, tasks.size());
        assertEquals("https://example.com/file1.txt", tasks.get(0).getUrl());
        assertEquals("file1.txt", tasks.get(0).getFileName());

        // 测试通过URL和文件名映射创建任务
        Map<String, String> urlFileMap = new HashMap<>();
        urlFileMap.put("https://example.com/file1.txt", "document.txt");
        urlFileMap.put("https://example.com/file2.jpg", "image.jpg");

        List<DownloadTask> tasks2 = MultiFileDownloader.createDownloadTasks(urlFileMap);
        
        assertNotNull(tasks2);
        assertEquals(2, tasks2.size());
    }

    @Test
    void testDownloadTaskProperties() {
        // 测试DownloadTask的属性设置和获取
        DownloadTask task = new DownloadTask("https://example.com/test.txt", "test.txt");
        
        assertEquals("https://example.com/test.txt", task.getUrl());
        assertEquals("test.txt", task.getFileName());
        assertFalse(task.isSuccess());
        assertNull(task.getLocalPath());
        assertNull(task.getErrorMessage());

        // 设置属性
        task.setLocalPath("/tmp/test.txt");
        task.setSuccess(true);
        task.setErrorMessage("test error");

        assertEquals("/tmp/test.txt", task.getLocalPath());
        assertTrue(task.isSuccess());
        assertEquals("test error", task.getErrorMessage());
    }
}
